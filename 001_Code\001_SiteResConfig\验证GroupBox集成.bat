@echo off
chcp 65001 >nul
echo ========================================
echo  传感器界面GroupBox集成验证
echo ========================================
echo.

echo 🔍 验证GroupBox控件集成...
echo.

REM 检查新增的控件
echo [1/5] 检查新增控件...
findstr /C:"edsIdEdit" SiteResConfig\ui\SensorDialog.ui >nul
if errorlevel 1 (
    echo ❌ 未找到EDS ID控件
) else (
    echo ✅ EDS ID控件已添加到GroupBox
)

findstr /C:"dimensionCombo" SiteResConfig\ui\SensorDialog.ui >nul
if errorlevel 1 (
    echo ❌ 未找到Dimension控件
) else (
    echo ✅ Dimension控件已添加到GroupBox
)

echo.

REM 检查GroupBox内的控件
echo [2/5] 检查GroupBox内控件...
findstr /C:"typeComboInGroup" SiteResConfig\ui\SensorDialog.ui >nul
if errorlevel 1 (
    echo ❌ 未找到GroupBox内的Type控件
) else (
    echo ✅ Type控件已移入GroupBox
)

findstr /C:"serialEditInGroup" SiteResConfig\ui\SensorDialog.ui >nul
if errorlevel 1 (
    echo ❌ 未找到GroupBox内的Serial Number控件
) else (
    echo ✅ Serial Number控件已移入GroupBox
)

echo.

REM 检查重复控件是否已删除
echo [3/5] 检查重复控件删除...
findstr /C:"name=\"typeLayout\"" SiteResConfig\ui\SensorDialog.ui >nul
if errorlevel 1 (
    echo ✅ 原typeLayout已删除，避免重复
) else (
    echo ❌ 原typeLayout仍存在，可能重复
)

findstr /C:"name=\"serialLayout\"" SiteResConfig\ui\SensorDialog.ui >nul
if errorlevel 1 (
    echo ✅ 原serialLayout已删除，避免重复
) else (
    echo ❌ 原serialLayout仍存在，可能重复
)

echo.

REM 检查数据结构更新
echo [4/5] 检查数据结构更新...
findstr /C:"QString edsId" SiteResConfig\include\SensorDialog.h >nul
if errorlevel 1 (
    echo ❌ 数据结构中未找到edsId字段
) else (
    echo ✅ edsId字段已添加到数据结构
)

findstr /C:"QString dimension" SiteResConfig\include\SensorDialog.h >nul
if errorlevel 1 (
    echo ❌ 数据结构中未找到dimension字段
) else (
    echo ✅ dimension字段已添加到数据结构
)

echo.

REM 检查功能实现
echo [5/5] 检查功能实现...
findstr /C:"initializeDimensionOptions" SiteResConfig\src\SensorDialog.cpp >nul
if errorlevel 1 (
    echo ❌ 未找到尺寸选项初始化函数
) else (
    echo ✅ 尺寸选项初始化函数已实现
)

findstr /C:"typeComboInGroup" SiteResConfig\src\SensorDialog.cpp >nul
if errorlevel 1 (
    echo ❌ 源代码中未使用GroupBox内的控件
) else (
    echo ✅ 源代码已更新使用GroupBox内的控件
)

echo.
echo ========================================
echo  ✅ GroupBox集成验证完成！
echo ========================================
echo.
echo 📋 集成摘要:
echo   • Sensor: 保持原有，作为主选择器
echo   • Type: 移入GroupBox，使用typeComboInGroup
echo   • Serial Number: 移入GroupBox，使用serialEditInGroup
echo   • EDS ID: 新增到GroupBox，使用edsIdEdit
echo   • Dimension: 新增到GroupBox，使用dimensionCombo
echo.
echo 🎯 避免重复:
echo   • 删除了原来的typeLayout和serialLayout
echo   • 使用新的控件ID避免冲突
echo   • 保持了功能的完整性
echo.
echo 🔧 功能特性:
echo   • 统一的GroupBox界面风格
echo   • 智能的传感器配置
echo   • 丰富的尺寸选项
echo   • 完整的数据获取
echo.
echo 🚀 测试建议:
echo   1. 编译项目验证UI文件正确性
echo   2. 测试传感器选择的智能配置
echo   3. 验证EDS ID和尺寸输入功能
echo   4. 检查数据获取的完整性
echo.
echo 📖 详细信息: 传感器界面GroupBox集成完成报告.md
echo.
pause
