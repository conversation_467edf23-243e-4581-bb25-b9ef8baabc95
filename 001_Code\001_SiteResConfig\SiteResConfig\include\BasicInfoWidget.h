#ifndef BASICINFOWIDGET_H
#define BASICINFOWIDGET_H

#include <QWidget>
#include <QTableWidget>
#include <QLabel>
#include <QGroupBox>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QMap>
#include <QList>
#include <QDateTime>
#include <QString>
#include <QVariant>
#include <QColor>   // 🆕 新增：支持颜色设置
#include <QFont>    // 🆕 新增：支持字体设置

// 前向声明
QT_BEGIN_NAMESPACE
class QTableWidget;
class QLabel;
class QGroupBox;
class QTreeWidgetItem;  // 🆕 新增：QTreeWidgetItem前向声明
namespace Ui {
    class BasicInfoWidget;
}
QT_END_NAMESPACE

// 包含QTreeWidgetItem头文件以支持完整类型
#include <QTreeWidgetItem>
#include <QTreeWidget>

// 节点状态枚举
enum class NodeStatus {
    Unknown,        // 未知状态
    Online,         // 在线
    Offline,        // 离线
    Warning,        // 警告
    Error,          // 错误
    Maintenance     // 维护
};

// 子节点信息结构
struct SubNodeInfo {
    // 基本信息
    QString name;               // 子节点名称
    QString type;               // 子节点类型
    QString deviceName;         // 关联设备名称
    QString deviceId;           // 设备ID
    bool isConnected;           // 关联状态
    
    // 设备属性
    QMap<QString, QVariant> properties;
    
    // 构造函数
    SubNodeInfo() : isConnected(false) {}
    
    // 设置设备属性
    void setProperty(const QString& key, const QVariant& value) {
        properties[key] = value;
    }
    
    // 获取设备属性
    QVariant getProperty(const QString& key, const QVariant& defaultValue = QVariant()) const {
        return properties.value(key, defaultValue);
    }
    
    // 检查是否有特定属性
    bool hasProperty(const QString& key) const {
        return properties.contains(key);
    }
};

// 节点信息结构
struct NodeInfo {
    // 基本信息
    QString nodeName;           // 节点名称
    QString nodeType;           // 节点类型
    NodeStatus status;          // 节点状态
    QString nodeId;             // 节点ID
    QDateTime createTime;       // 创建时间
    QDateTime updateTime;       // 更新时间
    
    // 基本信息属性（基于现有代码的列头）
    QMap<QString, QVariant> basicProperties;
    
    // 子节点信息
    QList<SubNodeInfo> subNodes;
    
    // 构造函数
    NodeInfo() : status(NodeStatus::Unknown) {}
    
    // 设置基本信息属性
    void setBasicProperty(const QString& key, const QVariant& value) {
        basicProperties[key] = value;
    }
    
    // 获取基本信息属性
    QVariant getBasicProperty(const QString& key, const QVariant& defaultValue = QVariant()) const {
        return basicProperties.value(key, defaultValue);
    }
    
    // 添加子节点
    void addSubNode(const SubNodeInfo& subNode) {
        subNodes.append(subNode);
    }
};

// 基本信息控件类
class BasicInfoWidget : public QWidget {
    Q_OBJECT
    
public:
    explicit BasicInfoWidget(QWidget *parent = nullptr);
    ~BasicInfoWidget();
    
    // 公共接口
    void setNodeInfo(const NodeInfo& nodeInfo);
    void clearInfo();
    
    // 🆕 修改：更安全的getCurrentNodeInfo方法
    NodeInfo getCurrentNodeInfo() const { 
        // 返回当前节点信息的副本，避免外部修改
        return m_currentNodeInfo; 
    }
    
    // 🆕 新增：检查当前节点信息是否有效
    bool hasValidNodeInfo() const {
        return !m_currentNodeInfo.nodeName.isEmpty() || !m_currentNodeInfo.nodeType.isEmpty();
    }
    
    // 获取基本信息表格（供外部访问）
    QTableWidget* getBasicInfoTable() const { return m_basicInfoTable; }
    
    // 🆕 新增：设置控制通道根节点信息
    void setControlChannelRootInfo(const QString& rootName, 
                                  const QList<QTreeWidgetItem*>& childChannels);
    
    // 🆕 新增：创建控制通道根节点的NodeInfo
    static NodeInfo createControlChannelRootNodeInfo(const QString& rootName,
                                                   const QList<QTreeWidgetItem*>& childChannels);
    
    // 🆕 新增：添加控制通道行
    void addControlChannelRow(int row, const SubNodeInfo& channel);
    
    // 🆕 新增：添加控制子节点行
    void addControlSubNodeRow(int row, const QString& channelName, const SubNodeInfo& subNode);
    
    // 🆕 新增：设置表格选中行
    void setSelectedRow(int row);
    
signals:
    // 可以添加需要的信号
    
private slots:
    // 私有槽函数
    
private:
    // 界面初始化
    void initUI();
    void setupConnections();
    void applyStyles();
    
    // 创建界面组件
    void createBasicInfoTable();
    
    // 数据更新
    void updateSummaryInfo(const NodeInfo& nodeInfo);
    void updateBasicInfoTable(const NodeInfo& nodeInfo);
    
    // 辅助方法
    QString getStatusText(NodeStatus status);
    void adjustTableColumns();
    
    // 🆕 新增：获取子节点类型的辅助方法
    QString getSubNodeType(const QString& subNodeName);
    
    // 🆕 新增：获取子节点类型的静态方法（用于静态方法中）
    static QString getSubNodeTypeStatic(const QString& subNodeName);
    
    // 🆕 新增：获取极性文本的辅助方法
    QString getPolarityText(int polarity) const;
    
    // 🆕 新增：极性文本转换为数字值的静态辅助方法
    static int getPolarityValueStatic(const QString& polarityText);
    
    // 🆕 新增：极性文本转换为数字值的辅助方法
    int getPolarityValue(const QString& polarityText) const;
    
    // 事件处理
    void resizeEvent(QResizeEvent* event) override;
    
private:
    // UI组件
    Ui::BasicInfoWidget* ui;            // UI界面
    QTableWidget* m_basicInfoTable;     // 基本信息表格
    
    // 数据
    NodeInfo m_currentNodeInfo;
    
    // 常量
    static const QStringList BASIC_INFO_HEADERS;
};

#endif // BASICINFOWIDGET_H 