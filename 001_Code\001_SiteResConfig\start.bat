@echo off
echo Starting SiteResConfig Application...
echo.

set APP_PATH=build-SiteResConfig_Simple-Desktop_Qt_5_14_2_MinGW_32_bit-Debug\debug\SiteResConfig.exe

echo Checking for executable at: %APP_PATH%

if exist "%APP_PATH%" (
    echo Found executable!
    echo Starting application...
    echo.
    echo Debug Info:
    echo - Watch console for debug messages
    echo - Look for "=== dragMoveEvent START ===" when dragging
    echo - Test drag from hardware tree to test config tree
    echo.
    
    "%APP_PATH%"
    
    echo.
    echo Application closed.
    
) else (
    echo ERROR: Executable not found!
    echo Expected location: %APP_PATH%
    echo.
    echo Current directory: %CD%
    echo.
    echo Please check:
    echo 1. Are you in the correct directory?
    echo 2. Has the project been compiled?
    echo 3. Is the build configuration correct?
)

echo.
pause
