@echo off
echo ========================================
echo Simple Compile Test for Qt 5.14 Fix
echo ========================================
echo.

echo [INFO] Setting Qt environment...
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo [INFO] Entering project directory...
cd /d "%~dp0\SiteResConfig"

echo [INFO] Cleaning previous build...
if exist "Makefile" del Makefile
if exist "*.o" del *.o
mingw32-make clean > nul 2>&1

echo [INFO] Generating Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo [ERROR] qmake failed!
    pause
    exit /b 1
)

echo [INFO] Compiling (Qt 5.14 compatibility fix)...
mingw32-make -j4

if errorlevel 1 (
    echo.
    echo [ERROR] Compilation failed!
    echo [INFO] Main fixes applied:
    echo   - Removed deprecated setCodecForCStrings for Qt 5.14
    echo   - Simplified console encoding setup
    echo   - Improved UI refresh mechanism
    echo.
    echo [INFO] Please check error messages above
    pause
    exit /b 1
)

echo.
echo [SUCCESS] Compilation successful!
echo.
echo [INFO] Fix summary:
echo   1. Fixed Qt 5.14 compatibility issues
echo   2. Simplified console encoding setup
echo   3. Improved UI refresh to avoid freezing
echo   4. Enhanced exception handling
echo.
echo [INFO] Starting application...

start "" "debug\SiteResConfig.exe"

echo.
echo [INFO] Application started
echo [INFO] Please test project import with:
echo        File: C:\Users\<USER>\Desktop\20250818152156_shiyangongcheng.xlsx
echo.
echo [TEST POINTS]
echo   - Check if console output is normal
echo   - Verify progress dialog appears during import
echo   - Confirm import process doesn't freeze
echo   - Check import completion dialog
echo.
pause
