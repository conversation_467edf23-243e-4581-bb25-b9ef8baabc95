@echo off
echo ========================================
echo  节点信息解析修复测试
echo ========================================

echo 修复内容：
echo 1. 简化解析逻辑，避免复杂的正则表达式
echo 2. 使用字符串分割方法，更可靠
echo 3. 增强调试输出，便于问题排查
echo 4. 支持多种格式的自动识别
echo.

echo 解析规则：
echo 规则1 - 冒号分隔的键值对：
echo   输入: "类型: 单出杆"
echo   输出: 名称="类型", 参数1="单出杆"
echo.
echo 规则2 - 冒号分隔的数值+单位：
echo   输入: "Frequency: 528.00 Hz"
echo   输出: 名称="Frequency", 参数1="528.00", 参数2="Hz"
echo.
echo 规则3 - 冒号分隔的数值+单位：
echo   输入: "Balance: 0.000 V"
echo   输出: 名称="Balance", 参数1="0.000", 参数2="V"
echo.
echo 规则4 - 直接数值+单位（无冒号）：
echo   输入: "528.00 Hz"
echo   输出: 名称=原名称, 参数1="528.00", 参数2="Hz"
echo.

echo 技术改进：
echo ✅ 使用QString::split()替代正则表达式
echo ✅ 使用toDouble()检测数值有效性
echo ✅ 支持多词单位（如"mm/s"）
echo ✅ 简化的调试输出格式
echo ✅ 向后兼容原有格式
echo.

echo 解析流程：
echo 1. 检查是否包含冒号":"
echo 2. 如果有冒号，按冒号分割为键值对
echo 3. 检查值部分是否为"数值 单位"格式
echo 4. 如果是数值，分别提取数值和单位
echo 5. 如果无冒号，检查是否为直接的"数值 单位"
echo 6. 都不匹配则保持原格式
echo.

echo 调试输出示例：
echo - "解析节点: CH1 源文本: Frequency: 528.00 Hz"
echo - "发现冒号分隔 - 键: Frequency 值: 528.00 Hz"
echo - "解析为数值+单位: 参数1=528.00 参数2=Hz"
echo - "保存CSV: Frequency → 528.00|Hz|"
echo.

echo 测试步骤：
echo 1. 重新编译项目
echo 2. 创建包含以下格式的节点：
echo    - "类型: 单出杆"
echo    - "Frequency: 528.00 Hz"  
echo    - "Balance: 0.000 V"
echo 3. 保存工程配置为CSV文件
echo 4. 查看控制台调试输出
echo 5. 检查CSV文件中的列分布
echo.

echo 预期结果：
echo - 控制台显示清晰的解析过程
echo - CSV文件正确分列存储信息
echo - "类型: 单出杆" → 名称列="类型", 参数1列="单出杆"
echo - "Frequency: 528.00 Hz" → 名称列="Frequency", 参数1列="528.00", 参数2列="Hz"
echo - "Balance: 0.000 V" → 名称列="Balance", 参数1列="0.000", 参数2列="V"
echo.

pause
