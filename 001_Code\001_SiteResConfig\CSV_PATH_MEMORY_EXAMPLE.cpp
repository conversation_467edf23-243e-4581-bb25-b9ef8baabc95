/**
 * @file CSV_PATH_MEMORY_EXAMPLE.cpp
 * @brief CSV路径记忆功能使用示例
 * @details 演示如何使用CSV路径记忆功能
 * <AUTHOR> Assistant
 * @date 2025-08-11
 * @version 1.0.0
 */

#include "MainWindow_Qt_Simple.h"
#include <QtCore/QDebug>

/**
 * @brief CSV路径记忆功能使用示例
 * @details 这些示例展示了如何使用CSV路径记忆功能
 */

// 示例1：基础路径记忆功能
void ExampleBasicPathMemory(CMyMainWindow* mainWindow) {
    qDebug() << "=== 示例1：基础路径记忆功能 ===";
    
    // 获取当前记忆的路径
    QString lastPath = mainWindow->GetLastUsedCSVPath();
    qDebug() << "当前记忆路径:" << lastPath;
    
    // 获取智能路径（优先使用记忆路径）
    QString smartPath = mainWindow->GetSmartCSVPath();
    qDebug() << "智能路径:" << smartPath;
    
    // 获取默认路径（用于对比）
    QString defaultPath = mainWindow->GetDefaultCSVPath();
    qDebug() << "默认路径:" << defaultPath;
}

// 示例2：保存和加载路径设置
void ExampleSaveLoadSettings(CMyMainWindow* mainWindow) {
    qDebug() << "=== 示例2：保存和加载路径设置 ===";
    
    // 获取配置文件路径
    QString configFile = mainWindow->GetCSVPathConfigFile();
    qDebug() << "配置文件路径:" << configFile;
    
    // 手动保存一个路径
    QString testPath = "D:\\MyExperiments\\CSV_Data";
    mainWindow->SaveLastUsedCSVPath(testPath);
    qDebug() << "保存测试路径:" << testPath;
    
    // 重新加载设置
    bool loadSuccess = mainWindow->LoadCSVPathSettings();
    qDebug() << "加载设置结果:" << (loadSuccess ? "成功" : "失败");
    
    // 验证加载的路径
    QString loadedPath = mainWindow->GetLastUsedCSVPath();
    qDebug() << "加载的路径:" << loadedPath;
}

// 示例3：自动路径记忆（通过文件操作）
void ExampleAutoPathMemory(CMyMainWindow* mainWindow) {
    qDebug() << "=== 示例3：自动路径记忆 ===";
    
    // 创建测试数据
    QVector<QStringList> testData;
    testData.append(QStringList() << "时间" << "数值" << "状态");
    testData.append(QStringList() << "0.0" << "100.5" << "正常");
    testData.append(QStringList() << "0.1" << "200.3" << "正常");
    
    // 保存到不同路径，观察路径记忆变化
    QString originalPath = mainWindow->GetLastUsedCSVPath();
    qDebug() << "原始记忆路径:" << originalPath;
    
    // 导出到默认路径
    bool success1 = mainWindow->ExportDataToCSV(testData, "test1.csv");
    if (success1) {
        QString newPath1 = mainWindow->GetLastUsedCSVPath();
        qDebug() << "导出后路径1:" << newPath1;
    }
    
    // 导出到子目录
    bool success2 = mainWindow->ExportDataToCSV(testData, "test2.csv", "测试目录");
    if (success2) {
        QString newPath2 = mainWindow->GetLastUsedCSVPath();
        qDebug() << "导出后路径2:" << newPath2;
    }
}

// 示例4：智能路径选择
void ExampleSmartPathSelection(CMyMainWindow* mainWindow) {
    qDebug() << "=== 示例4：智能路径选择 ===";
    
    // 测试不同情况下的智能路径选择
    
    // 情况1：有有效的记忆路径
    QString validPath = "D:\\ValidPath";
    QDir().mkpath(validPath); // 确保路径存在
    mainWindow->SaveLastUsedCSVPath(validPath);
    
    QString smart1 = mainWindow->GetSmartCSVPath();
    qDebug() << "有效记忆路径时的智能路径:" << smart1;
    
    // 情况2：记忆路径无效
    QString invalidPath = "Z:\\NonExistentPath";
    mainWindow->SaveLastUsedCSVPath(invalidPath);
    
    QString smart2 = mainWindow->GetSmartCSVPath();
    qDebug() << "无效记忆路径时的智能路径:" << smart2;
    
    // 情况3：没有记忆路径
    mainWindow->SaveLastUsedCSVPath(""); // 清空记忆
    
    QString smart3 = mainWindow->GetSmartCSVPath();
    qDebug() << "无记忆路径时的智能路径:" << smart3;
}

// 示例5：路径记忆的实际应用场景
void ExampleRealWorldUsage(CMyMainWindow* mainWindow) {
    qDebug() << "=== 示例5：实际应用场景 ===";
    
    // 场景：用户首次使用
    qDebug() << "--- 首次使用场景 ---";
    QString firstUsePath = mainWindow->GetSmartCSVPath();
    qDebug() << "首次使用路径:" << firstUsePath;
    
    // 用户保存项目到自定义位置
    QString customPath = "D:\\MyProjects\\Experiment_2025";
    QDir().mkpath(customPath);
    
    QString savedPath;
    bool saveSuccess = mainWindow->QuickSaveProjectToCSV(&savedPath, "实验项目", true);
    if (saveSuccess) {
        qDebug() << "项目保存到:" << savedPath;
        
        // 路径自动记忆
        QString rememberedPath = mainWindow->GetLastUsedCSVPath();
        qDebug() << "自动记忆路径:" << rememberedPath;
    }
    
    // 场景：用户下次使用
    qDebug() << "--- 下次使用场景 ---";
    QString nextUsePath = mainWindow->GetSmartCSVPath();
    qDebug() << "下次使用路径:" << nextUsePath;
    
    // 验证路径是否被正确记忆
    if (nextUsePath.contains("MyProjects")) {
        qDebug() << "✓ 路径记忆功能正常工作";
    } else {
        qDebug() << "✗ 路径记忆功能可能有问题";
    }
}

// 示例6：配置文件操作
void ExampleConfigFileOperations(CMyMainWindow* mainWindow) {
    qDebug() << "=== 示例6：配置文件操作 ===";
    
    QString configFile = mainWindow->GetCSVPathConfigFile();
    qDebug() << "配置文件位置:" << configFile;
    
    // 检查配置文件是否存在
    if (QFile::exists(configFile)) {
        qDebug() << "配置文件存在";
        
        // 读取配置文件内容（用于调试）
        QSettings settings(configFile, QSettings::IniFormat);
        settings.setIniCodec("UTF-8");
        
        QString savedPath = settings.value("CSV/LastUsedPath", "").toString();
        QString updateTime = settings.value("CSV/LastUpdateTime", "").toString();
        QString version = settings.value("CSV/ConfigVersion", "").toString();
        
        qDebug() << "配置内容:";
        qDebug() << "  保存路径:" << savedPath;
        qDebug() << "  更新时间:" << updateTime;
        qDebug() << "  配置版本:" << version;
    } else {
        qDebug() << "配置文件不存在";
    }
    
    // 强制保存当前设置
    bool saveResult = mainWindow->SaveCSVPathSettings();
    qDebug() << "保存配置结果:" << (saveResult ? "成功" : "失败");
}

// 示例7：路径记忆的边界情况测试
void ExampleEdgeCases(CMyMainWindow* mainWindow) {
    qDebug() << "=== 示例7：边界情况测试 ===";
    
    // 测试空路径
    mainWindow->SaveLastUsedCSVPath("");
    QString emptyResult = mainWindow->GetLastUsedCSVPath();
    qDebug() << "空路径测试结果:" << emptyResult;
    
    // 测试特殊字符路径
    QString specialPath = "D:\\测试路径\\特殊@#$字符";
    QDir().mkpath(specialPath);
    mainWindow->SaveLastUsedCSVPath(specialPath);
    QString specialResult = mainWindow->GetLastUsedCSVPath();
    qDebug() << "特殊字符路径测试:" << specialResult;
    
    // 测试长路径
    QString longPath = "D:\\VeryLongPathNameForTestingPurposes\\AnotherLongDirectoryName\\YetAnotherDirectory";
    QDir().mkpath(longPath);
    mainWindow->SaveLastUsedCSVPath(longPath);
    QString longResult = mainWindow->GetLastUsedCSVPath();
    qDebug() << "长路径测试:" << longResult;
    
    // 测试网络路径（如果适用）
    QString networkPath = "\\\\NetworkServer\\SharedFolder";
    mainWindow->SaveLastUsedCSVPath(networkPath);
    QString networkResult = mainWindow->GetLastUsedCSVPath();
    qDebug() << "网络路径测试:" << networkResult;
}

// 示例8：完整的工作流程演示
void ExampleCompleteWorkflow(CMyMainWindow* mainWindow) {
    qDebug() << "=== 示例8：完整工作流程演示 ===";
    
    // 1. 程序启动，加载路径设置
    qDebug() << "1. 程序启动";
    bool loadSuccess = mainWindow->LoadCSVPathSettings();
    QString initialPath = mainWindow->GetSmartCSVPath();
    qDebug() << "   加载设置:" << (loadSuccess ? "成功" : "失败");
    qDebug() << "   初始路径:" << initialPath;
    
    // 2. 用户进行CSV操作
    qDebug() << "2. 用户操作";
    QVector<QStringList> data;
    data.append(QStringList() << "实验" << "结果" << "备注");
    data.append(QStringList() << "测试1" << "成功" << "正常");
    
    QString exportPath = mainWindow->GenerateCSVFilePath("workflow_test.csv", "工作流程测试");
    bool exportSuccess = mainWindow->ExportDataToCSV(data, "workflow_test.csv", "工作流程测试");
    qDebug() << "   导出操作:" << (exportSuccess ? "成功" : "失败");
    qDebug() << "   导出路径:" << exportPath;
    
    // 3. 路径自动记忆
    qDebug() << "3. 路径记忆";
    QString rememberedPath = mainWindow->GetLastUsedCSVPath();
    qDebug() << "   记忆路径:" << rememberedPath;
    
    // 4. 下次操作使用记忆路径
    qDebug() << "4. 下次操作";
    QString nextPath = mainWindow->GetSmartCSVPath();
    qDebug() << "   下次路径:" << nextPath;
    
    // 5. 验证路径一致性
    qDebug() << "5. 验证结果";
    if (rememberedPath == nextPath) {
        qDebug() << "   ✓ 路径记忆功能正常";
    } else {
        qDebug() << "   ✗ 路径记忆功能异常";
    }
}

/**
 * @brief 运行所有CSV路径记忆示例
 * @param mainWindow 主窗口指针
 */
void RunAllCSVPathMemoryExamples(CMyMainWindow* mainWindow) {
    if (!mainWindow) {
        qDebug() << "错误：主窗口指针为空";
        return;
    }
    
    qDebug() << "========================================";
    qDebug() << "       CSV路径记忆功能演示";
    qDebug() << "========================================";
    
    try {
        ExampleBasicPathMemory(mainWindow);
        qDebug() << "";
        
        ExampleSaveLoadSettings(mainWindow);
        qDebug() << "";
        
        ExampleAutoPathMemory(mainWindow);
        qDebug() << "";
        
        ExampleSmartPathSelection(mainWindow);
        qDebug() << "";
        
        ExampleRealWorldUsage(mainWindow);
        qDebug() << "";
        
        ExampleConfigFileOperations(mainWindow);
        qDebug() << "";
        
        ExampleEdgeCases(mainWindow);
        qDebug() << "";
        
        ExampleCompleteWorkflow(mainWindow);
        
    } catch (const std::exception& e) {
        qDebug() << "示例运行出错:" << e.what();
    }
    
    qDebug() << "";
    qDebug() << "========================================";
    qDebug() << "       所有示例运行完成";
    qDebug() << "========================================";
}
