@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo.
echo ========================================
echo  🔧 修复作动器管理器编译错误
echo ========================================
echo.

REM 设置Qt环境
set QTDIR=C:\Qt\5.15.2\msvc2019_64
set PATH=%QTDIR%\bin;%PATH%

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo 1. 清理所有构建文件...
if exist "Makefile*" del Makefile* >nul 2>&1
if exist "debug" rmdir /s /q debug >nul 2>&1
if exist "release" rmdir /s /q release >nul 2>&1
if exist "*.o" del *.o >nul 2>&1
if exist "ui_*.h" del ui_*.h >nul 2>&1
if exist "moc_*.cpp" del moc_*.cpp >nul 2>&1
if exist "moc_*.h" del moc_*.h >nul 2>&1

echo 清理构建目录...
cd /d "%~dp0"
if exist "build-SiteResConfig_Simple-Desktop_Qt_5_14_2_MinGW_32_bit-Debug" (
    rmdir /s /q "build-SiteResConfig_Simple-Desktop_Qt_5_14_2_MinGW_32_bit-Debug" >nul 2>&1
)

cd /d "%~dp0\SiteResConfig"

echo ✅ 构建文件清理完成

echo.
echo 2. 重新生成UI头文件...
uic ui\MainWindow.ui -o ui_MainWindow.h
if errorlevel 1 (
    echo ❌ UI头文件生成失败
    goto :error
)

echo 验证UI头文件...
findstr /C:"actionExportActuatorDetailsToExcel" "ui_MainWindow.h" >nul
if errorlevel 1 (
    echo ❌ UI头文件中缺少actionExportActuatorDetailsToExcel
    goto :error
) else (
    echo ✅ UI头文件生成成功，包含actionExportActuatorDetailsToExcel
)

echo.
echo 3. 验证代码完整性...

echo 检查ActuatorDataManager.h...
if not exist "include\ActuatorDataManager.h" (
    echo ❌ ActuatorDataManager.h 不存在
    goto :error
) else (
    echo ✅ ActuatorDataManager.h 存在
)

echo 检查ActuatorDataManager.cpp...
if not exist "src\ActuatorDataManager.cpp" (
    echo ❌ ActuatorDataManager.cpp 不存在
    goto :error
) else (
    echo ✅ ActuatorDataManager.cpp 存在
)

echo 检查MainWindow头文件中的作动器接口声明...
findstr /C:"saveActuatorDetailedParams" "include\MainWindow_Qt_Simple.h" >nul
if errorlevel 1 (
    echo ❌ MainWindow头文件中缺少作动器接口声明
    goto :error
) else (
    echo ✅ MainWindow头文件中有作动器接口声明
)

echo 检查MainWindow实现文件中的作动器接口实现...
findstr /C:"CMyMainWindow::saveActuatorDetailedParams" "src\MainWindow_Qt_Simple.cpp" >nul
if errorlevel 1 (
    echo ❌ MainWindow实现文件中缺少作动器接口实现
    goto :error
) else (
    echo ✅ MainWindow实现文件中有作动器接口实现
)

echo.
echo 4. 生成完整的qmake项目文件...
echo QT += core widgets > SiteResConfig_Simple.pro
echo CONFIG += console >> SiteResConfig_Simple.pro
echo TARGET = SiteResConfig >> SiteResConfig_Simple.pro
echo TEMPLATE = app >> SiteResConfig_Simple.pro
echo. >> SiteResConfig_Simple.pro
echo INCLUDEPATH += include >> SiteResConfig_Simple.pro
echo INCLUDEPATH += ../../../vcpkg/installed/x64-windows/include >> SiteResConfig_Simple.pro
echo. >> SiteResConfig_Simple.pro
echo LIBS += -L../../../vcpkg/installed/x64-windows/lib >> SiteResConfig_Simple.pro
echo LIBS += -lQXlsx >> SiteResConfig_Simple.pro
echo. >> SiteResConfig_Simple.pro

REM 添加所有源文件
echo SOURCES += src/main_qt.cpp >> SiteResConfig_Simple.pro
echo SOURCES += src/MainWindow_Qt_Simple.cpp >> SiteResConfig_Simple.pro
echo SOURCES += src/XLSDataExporter.cpp >> SiteResConfig_Simple.pro
echo SOURCES += src/ActuatorDataManager.cpp >> SiteResConfig_Simple.pro
echo SOURCES += src/SensorDataManager.cpp >> SiteResConfig_Simple.pro
echo SOURCES += src/ActuatorDialog.cpp >> SiteResConfig_Simple.pro
echo SOURCES += src/SensorDialog.cpp >> SiteResConfig_Simple.pro
echo SOURCES += src/DataExportManager.cpp >> SiteResConfig_Simple.pro
echo SOURCES += src/CSVDataExporter.cpp >> SiteResConfig_Simple.pro
echo SOURCES += src/JSONDataExporter.cpp >> SiteResConfig_Simple.pro
echo SOURCES += src/DataExporterFactory.cpp >> SiteResConfig_Simple.pro
echo SOURCES += src/CreateHardwareNodeDialog.cpp >> SiteResConfig_Simple.pro
echo SOURCES += src/HardwareConfigDialog.cpp >> SiteResConfig_Simple.pro
echo SOURCES += src/NodeConfigDialog.cpp >> SiteResConfig_Simple.pro
echo SOURCES += src/ControlModeDialog.cpp >> SiteResConfig_Simple.pro
echo SOURCES += src/PIDParametersDialog.cpp >> SiteResConfig_Simple.pro
echo SOURCES += src/CustomTreeWidgets.cpp >> SiteResConfig_Simple.pro
echo SOURCES += src/ConfigManager_Simple.cpp >> SiteResConfig_Simple.pro
echo SOURCES += src/DataModels_Simple.cpp >> SiteResConfig_Simple.pro
echo SOURCES += src/Utils_Fixed.cpp >> SiteResConfig_Simple.pro
echo. >> SiteResConfig_Simple.pro

REM 添加所有头文件
echo HEADERS += include/MainWindow_Qt_Simple.h >> SiteResConfig_Simple.pro
echo HEADERS += include/XLSDataExporter.h >> SiteResConfig_Simple.pro
echo HEADERS += include/ActuatorDataManager.h >> SiteResConfig_Simple.pro
echo HEADERS += include/SensorDataManager.h >> SiteResConfig_Simple.pro
echo HEADERS += include/ActuatorDialog.h >> SiteResConfig_Simple.pro
echo HEADERS += include/SensorDialog.h >> SiteResConfig_Simple.pro
echo HEADERS += include/IDataExporter.h >> SiteResConfig_Simple.pro
echo HEADERS += include/DataExportManager.h >> SiteResConfig_Simple.pro
echo HEADERS += include/CSVDataExporter.h >> SiteResConfig_Simple.pro
echo HEADERS += include/JSONDataExporter.h >> SiteResConfig_Simple.pro
echo HEADERS += include/DataExporterFactory.h >> SiteResConfig_Simple.pro
echo HEADERS += include/CreateHardwareNodeDialog.h >> SiteResConfig_Simple.pro
echo HEADERS += include/HardwareConfigDialog.h >> SiteResConfig_Simple.pro
echo HEADERS += include/NodeConfigDialog.h >> SiteResConfig_Simple.pro
echo HEADERS += include/ControlModeDialog.h >> SiteResConfig_Simple.pro
echo HEADERS += include/PIDParametersDialog.h >> SiteResConfig_Simple.pro
echo HEADERS += include/CustomTreeWidgets.h >> SiteResConfig_Simple.pro
echo HEADERS += include/Common_Fixed.h >> SiteResConfig_Simple.pro
echo HEADERS += include/ConfigManager_Fixed.h >> SiteResConfig_Simple.pro
echo HEADERS += include/DataModels_Fixed.h >> SiteResConfig_Simple.pro
echo. >> SiteResConfig_Simple.pro

REM 添加UI文件
echo FORMS += ui/MainWindow.ui >> SiteResConfig_Simple.pro
echo FORMS += ui/ActuatorDialog.ui >> SiteResConfig_Simple.pro
echo FORMS += ui/SensorDialog.ui >> SiteResConfig_Simple.pro
echo FORMS += ui/CreateHardwareNodeDialog.ui >> SiteResConfig_Simple.pro
echo FORMS += ui/HardwareConfigDialog.ui >> SiteResConfig_Simple.pro
echo FORMS += ui/NodeConfigDialog.ui >> SiteResConfig_Simple.pro
echo FORMS += ui/ControlModeDialog.ui >> SiteResConfig_Simple.pro
echo FORMS += ui/PIDParametersDialog.ui >> SiteResConfig_Simple.pro

echo ✅ 项目文件生成完成

echo.
echo 5. 运行qmake...
qmake SiteResConfig_Simple.pro
if errorlevel 1 (
    echo ❌ qmake 失败
    goto :error
)

echo ✅ qmake 成功

echo.
echo 6. 编译项目...
nmake clean >nul 2>&1
nmake
if errorlevel 1 (
    echo ❌ 编译失败
    echo.
    echo 显示编译错误详情:
    nmake 2>&1 | findstr /C:"error"
    goto :error
) else (
    echo ✅ 编译成功
)

echo.
echo ========================================
echo  ✅ 作动器管理器编译错误修复完成！
echo ========================================
echo.
echo 📋 修复内容:
echo   1. ✅ 删除了重复的getAllActuatorGroups声明
echo   2. ✅ 修复了QJsonObject::fromVariantMap类型转换问题
echo   3. ✅ 添加了完整的作动器数据管理接口
echo   4. ✅ 重新编译了整个项目
echo.
echo 🎯 现在可以使用的功能:
echo   - saveActuatorDetailedParams() - 保存作动器详细参数
echo   - getActuatorDetailedParams() - 获取作动器详细参数
echo   - updateActuatorDetailedParams() - 更新作动器参数
echo   - removeActuatorDetailedParams() - 删除作动器参数
echo   - getAllActuatorSerialNumbers() - 获取所有序列号
echo   - getAllActuatorDetailedParams() - 获取所有作动器参数
echo.
echo 📊 高级功能:
echo   - 作动器组管理 (saveActuatorGroup, getActuatorGroup等)
echo   - 数据统计分析 (类型统计、Unit统计、极性统计)
echo   - 序列号自动管理 (生成、唯一性检查)
echo   - 数据验证和错误处理
echo   - 多格式数据导出 (CSV、JSON)
echo.

echo 清理临时文件...
if exist "SiteResConfig_Simple.pro" del SiteResConfig_Simple.pro >nul 2>&1

pause
goto :end

:error
echo.
echo ❌ 修复失败！
echo.
echo 🔍 可能的问题:
echo   1. Qt环境配置问题
echo   2. vcpkg依赖库未安装
echo   3. 代码语法错误
echo   4. 文件权限问题
echo.
echo 💡 解决建议:
echo   1. 检查Qt安装路径: %QTDIR%
echo   2. 确认QXlsx库已通过vcpkg安装
echo   3. 检查编译错误信息
echo   4. 确保有文件写入权限
echo.

if exist "SiteResConfig_Simple.pro" del SiteResConfig_Simple.pro >nul 2>&1

pause
exit /b 1

:end
endlocal
