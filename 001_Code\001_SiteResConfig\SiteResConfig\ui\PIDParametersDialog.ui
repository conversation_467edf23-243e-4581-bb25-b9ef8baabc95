<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>PIDParametersDialog</class>
 <widget class="QDialog" name="PIDParametersDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>350</width>
    <height>200</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>PID参数设置</string>
  </property>
  <property name="modal">
   <bool>true</bool>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QHBoxLayout" name="kpLayout">
     <item>
      <widget class="QLabel" name="kpLabel">
       <property name="text">
        <string>比例系数(P):</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="kpLineEdit">
       <property name="text">
        <string>1.0</string>
       </property>
       <property name="placeholderText">
        <string>请输入比例系数</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="kiLayout">
     <item>
      <widget class="QLabel" name="kiLabel">
       <property name="text">
        <string>积分系数(I):</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="kiLineEdit">
       <property name="text">
        <string>0.1</string>
       </property>
       <property name="placeholderText">
        <string>请输入积分系数</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="kdLayout">
     <item>
      <widget class="QLabel" name="kdLabel">
       <property name="text">
        <string>微分系数(D):</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="kdLineEdit">
       <property name="text">
        <string>0.01</string>
       </property>
       <property name="placeholderText">
        <string>请输入微分系数</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <layout class="QHBoxLayout" name="buttonLayout">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="okButton">
       <property name="text">
        <string>设置</string>
       </property>
       <property name="default">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="cancelButton">
       <property name="text">
        <string>取消</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>cancelButton</sender>
   <signal>clicked()</signal>
   <receiver>PIDParametersDialog</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>300</x>
     <y>170</y>
    </hint>
    <hint type="destinationlabel">
     <x>175</x>
     <y>100</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
