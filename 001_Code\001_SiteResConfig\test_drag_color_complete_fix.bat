@echo off
echo ========================================
echo  拖拽颜色完全恢复修复测试
echo ========================================

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o
if exist "ui_*.h" del ui_*.h

echo.
echo 生成UI头文件...
uic ui\MainWindow.ui -o ui_MainWindow.h
if errorlevel 1 (
    echo UI文件生成失败！
    pause
    exit /b 1
) else (
    echo ✅ UI头文件生成成功: ui_MainWindow.h
)

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目...
mingw32-make clean
mingw32-make
if errorlevel 1 (
    echo 编译失败！
    pause
    exit /b 1
)

echo.
echo ========================================
echo  编译完成！
echo ========================================

if exist "SiteResConfig.exe" (
    echo 可执行文件: SiteResConfig.exe
    echo.
    echo ✅ 拖拽颜色完全恢复修复已实现！
    echo.
    echo 🎨 修复内容:
    echo ├─ 修复颜色恢复逻辑: 使用保存的原始颜色而非透明背景
    echo ├─ 增强恢复机制: 多级延迟恢复确保万无一失
    echo ├─ 添加dragEndEvent: 拖拽结束时强制恢复颜色
    echo ├─ 主窗口协调: 通过主窗口强制恢复所有树控件颜色
    echo └─ 全面覆盖: 所有拖拽事件都有颜色恢复保障
    echo.
    echo 🔧 技术改进:
    echo ├─ restoreTargetItemColor(): 使用原始颜色恢复
    echo ├─ 多重延迟恢复: 50ms, 150ms, 300ms 三级保障
    echo ├─ dragEndEvent(): 拖拽结束时的最终保障
    echo └─ ForceRestoreAllTreeColors(): 主窗口全局恢复
    echo.
    echo 🎯 测试建议:
    echo 1. 拖拽硬件节点到测试配置树
    echo 2. 观察拖拽过程中的颜色变化
    echo 3. 验证拖拽完成后颜色完全恢复
    echo 4. 测试拖拽取消时的颜色恢复
    echo 5. 验证长时间使用后无颜色残留
    echo.
) else (
    echo ❌ 编译失败，未找到可执行文件！
)

pause
