@echo off
echo Building SiteResConfig with drag color fix...

REM Set Qt environment
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo Checking build environment...
qmake --version
if errorlevel 1 (
    echo ERROR: Qt environment not found!
    pause
    exit /b 1
)

REM Change to project directory
cd /d "%~dp0\SiteResConfig"

echo.
echo Cleaning build files...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o
if exist "ui_*.h" del ui_*.h

echo.
echo Generating UI headers...
uic ui\MainWindow.ui -o ui_MainWindow.h
if errorlevel 1 (
    echo UI generation failed!
    pause
    exit /b 1
) else (
    echo UI header generated successfully: ui_MainWindow.h
)

echo.
echo Generating Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake failed!
    pause
    exit /b 1
)

echo.
echo Building project...
mingw32-make clean
mingw32-make
if errorlevel 1 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo ========================================
echo  Build Complete!
echo ========================================

if exist "SiteResConfig.exe" (
    echo Executable: SiteResConfig.exe
    echo.
    echo Drag color fix has been implemented!
    echo.
    echo Test the following:
    echo 1. Drag hardware nodes to test config tree
    echo 2. Observe color changes during drag
    echo 3. Verify colors restore after drag complete
    echo 4. Test drag cancellation color restore
    echo 5. Verify no color residue after long use
    echo.
) else (
    echo Build failed - executable not found!
)

pause
