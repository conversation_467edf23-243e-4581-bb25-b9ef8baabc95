/**
 * @file EventManager.cpp
 * @brief 事件管理模块实现 - 完整版
 * @details 提供完整的事件管理接口，包括项目事件、设备事件等
 * <AUTHOR> Assistant
 * @date 2025-01-27
 * @version 3.4.0
 */

#include "EventManager.h"
#include <QDebug>
#include <QTimer>

EventManager::EventManager(QObject* parent)
    : QObject(parent) {
    qDebug() << "EventManager: 事件管理器初始化";
}

EventManager::~EventManager() {
    qDebug() << "EventManager: 事件管理器已销毁";
}

void EventManager::sendEvent(const QString& eventType, const QVariant& data) {
    qDebug() << "EventManager: 发送事件" << eventType << "数据:" << data;
    
    if (eventListeners_.contains(eventType)) {
        auto callback = eventListeners_[eventType];
        try {
            callback(data);
        } catch (const std::exception& e) {
            qWarning() << "EventManager: 事件回调错误:" << e.what();
            emit eventError(eventType, QString("回调执行失败: %1").arg(e.what()));
        }
    }
    
    emit eventProcessed(eventType, data);
}

void EventManager::addEventListener(const QString& eventType, std::function<void(const QVariant&)> callback) {
    eventListeners_[eventType] = callback;
    qDebug() << "EventManager: 添加事件监听器" << eventType;
    emit listenerAdded(eventType);
}

void EventManager::removeEventListener(const QString& eventType) {
    if (eventListeners_.remove(eventType) > 0) {
        qDebug() << "EventManager: 移除事件监听器" << eventType;
        emit listenerRemoved(eventType);
    }
}

// 🆕 新增：项目相关事件
void EventManager::onProjectCreated(const QString& projectPath) {
    qDebug() << "EventManager: 项目创建事件" << projectPath;
    sendEvent("project.created", projectPath);
}

void EventManager::onProjectOpened(const QString& projectPath) {
    qDebug() << "EventManager: 项目打开事件" << projectPath;
    sendEvent("project.opened", projectPath);
}

void EventManager::onProjectSaved(const QString& projectPath) {
    qDebug() << "EventManager: 项目保存事件" << projectPath;
    sendEvent("project.saved", projectPath);
}

void EventManager::onProjectClosed() {
    qDebug() << "EventManager: 项目关闭事件";
    sendEvent("project.closed", QVariant());
}

// 🆕 新增：设备相关事件
void EventManager::onDeviceAdded(const QString& deviceType, const QString& deviceId) {
    qDebug() << "EventManager: 设备添加事件" << deviceType << deviceId;
    QVariantMap data;
    data["type"] = deviceType;
    data["id"] = deviceId;
    sendEvent("device.added", data);
}

void EventManager::onDeviceRemoved(const QString& deviceType, const QString& deviceId) {
    qDebug() << "EventManager: 设备移除事件" << deviceType << deviceId;
    QVariantMap data;
    data["type"] = deviceType;
    data["id"] = deviceId;
    sendEvent("device.removed", data);
}

void EventManager::onDeviceConfigChanged(const QString& deviceType, const QString& deviceId) {
    qDebug() << "EventManager: 设备配置变更事件" << deviceType << deviceId;
    QVariantMap data;
    data["type"] = deviceType;
    data["id"] = deviceId;
    sendEvent("device.config.changed", data);
}

// 🆕 新增：对话框相关事件
void EventManager::onDialogOpened(const QString& dialogType) {
    qDebug() << "EventManager: 对话框打开事件" << dialogType;
    sendEvent("dialog.opened", dialogType);
}

void EventManager::onDialogClosed(const QString& dialogType, bool accepted) {
    qDebug() << "EventManager: 对话框关闭事件" << dialogType << "接受:" << accepted;
    QVariantMap data;
    data["type"] = dialogType;
    data["accepted"] = accepted;
    sendEvent("dialog.closed", data);
}

// 🆕 新增：数据相关事件
void EventManager::onDataImported(const QString& filePath, const QString& dataType) {
    qDebug() << "EventManager: 数据导入事件" << filePath << dataType;
    QVariantMap data;
    data["filePath"] = filePath;
    data["dataType"] = dataType;
    sendEvent("data.imported", data);
}

void EventManager::onDataExported(const QString& filePath, const QString& dataType) {
    qDebug() << "EventManager: 数据导出事件" << filePath << dataType;
    QVariantMap data;
    data["filePath"] = filePath;
    data["dataType"] = dataType;
    sendEvent("data.exported", data);
}

// 🆕 新增：UI相关事件
void EventManager::onUIStateChanged(const QString& component, const QString& state) {
    qDebug() << "EventManager: UI状态变更事件" << component << state;
    QVariantMap data;
    data["component"] = component;
    data["state"] = state;
    sendEvent("ui.state.changed", data);
}

// 🆕 新增：批量事件处理
void EventManager::sendBatchEvents(const QList<QPair<QString, QVariant>>& events) {
    qDebug() << "EventManager: 发送批量事件，数量:" << events.size();
    
    for (const auto& event : events) {
        // 使用QTimer::singleShot确保事件按顺序处理
        QTimer::singleShot(0, [this, event]() {
            sendEvent(event.first, event.second);
        });
    }
    
    emit batchEventsProcessed(events.size());
}

// 🆕 新增：获取事件统计
EventManager::EventStats EventManager::getEventStats() const {
    EventStats stats;
    stats.totalListeners = eventListeners_.size();
    stats.activeEventTypes = eventListeners_.keys();
    return stats;
}

// 🆕 新增：清理所有监听器
void EventManager::clearAllListeners() {
    int count = eventListeners_.size();
    eventListeners_.clear();
    qDebug() << "EventManager: 清理了" << count << "个事件监听器";
    emit allListenersCleared();
} 