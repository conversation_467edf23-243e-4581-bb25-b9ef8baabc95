# 作动器相关类和文件重命名整体方案

## 🎯 **重命名规则**

将所有作动器相关的类、文件等名称修改为 `现在名称_1_2` 格式。

## 📋 **需要重命名的文件清单**

### 1. 头文件 (.h)
```
include/ActuatorDataManager.h          → include/ActuatorDataManager_1_2.h
include/ActuatorDialog.h               → include/ActuatorDialog_1_2.h
include/ActuatorViewModel1_2.h         → include/ActuatorViewModel1_2_1_2.h
```

### 2. 源文件 (.cpp)
```
src/ActuatorDataManager.cpp            → src/ActuatorDataManager_1_2.cpp
src/ActuatorDialog.cpp                 → src/ActuatorDialog_1_2.cpp
src/ActuatorViewModel1_2.cpp           → src/ActuatorViewModel1_2_1_2.cpp
```

### 3. UI文件 (.ui)
```
ui/ActuatorDialog.ui                   → ui/ActuatorDialog_1_2.ui
```

## 📝 **需要重命名的类名**

### 1. 核心类
```cpp
// 数据管理类
ActuatorDataManager                    → ActuatorDataManager_1_2

// 视图模型类
ActuatorViewModel1_2                   → ActuatorViewModel1_2_1_2

// 对话框类
ActuatorDialog                         → ActuatorDialog_1_2
```

### 2. 结构体和枚举
```cpp
// 在DataModels_Fixed.h中的结构体
namespace UI {
    struct ActuatorParams              → struct ActuatorParams_1_2
    struct ActuatorGroup               → struct ActuatorGroup_1_2
}
```

## 🔧 **需要修改的文件内容**

### 第一阶段：重命名文件

#### A. 重命名头文件
1. `ActuatorDataManager.h` → `ActuatorDataManager_1_2.h`
2. `ActuatorDialog.h` → `ActuatorDialog_1_2.h`
3. `ActuatorViewModel1_2.h` → `ActuatorViewModel1_2_1_2.h`

#### B. 重命名源文件
1. `ActuatorDataManager.cpp` → `ActuatorDataManager_1_2.cpp`
2. `ActuatorDialog.cpp` → `ActuatorDialog_1_2.cpp`
3. `ActuatorViewModel1_2.cpp` → `ActuatorViewModel1_2_1_2.cpp`

#### C. 重命名UI文件
1. `ActuatorDialog.ui` → `ActuatorDialog_1_2.ui`

### 第二阶段：修改类名和包含文件

#### A. 修改头文件中的类名
```cpp
// ActuatorDataManager_1_2.h
class ActuatorDataManager_1_2 {
    // 类内容保持不变
};

// ActuatorDialog_1_2.h
class ActuatorDialog_1_2 : public QDialog {
    // 类内容保持不变
};

// ActuatorViewModel1_2_1_2.h
class ActuatorViewModel1_2_1_2 : public QObject {
    // 类内容保持不变
};
```

#### B. 修改结构体名称
```cpp
// DataModels_Fixed.h
namespace UI {
    struct ActuatorParams_1_2 {
        // 字段保持不变
    };
    
    struct ActuatorGroup_1_2 {
        QList<ActuatorParams_1_2> actuators;  // 更新引用
        // 其他字段保持不变
    };
}
```

### 第三阶段：更新所有引用

#### A. 更新包含文件
```cpp
// 在所有使用这些类的文件中更新
#include "ActuatorDataManager.h"       → #include "ActuatorDataManager_1_2.h"
#include "ActuatorDialog.h"            → #include "ActuatorDialog_1_2.h"
#include "ActuatorViewModel1_2.h"      → #include "ActuatorViewModel1_2_1_2.h"
```

#### B. 更新类型引用
```cpp
// 在所有使用这些类型的地方更新
ActuatorDataManager                    → ActuatorDataManager_1_2
ActuatorDialog                         → ActuatorDialog_1_2
ActuatorViewModel1_2                   → ActuatorViewModel1_2_1_2
UI::ActuatorParams                     → UI::ActuatorParams_1_2
UI::ActuatorGroup                      → UI::ActuatorGroup_1_2
```

#### C. 更新变量名和成员变量
```cpp
// MainWindow_Qt_Simple.h 和 .cpp
std::unique_ptr<ActuatorViewModel1_2> actuatorViewModel1_2_;
→ std::unique_ptr<ActuatorViewModel1_2_1_2> actuatorViewModel1_2_1_2_;

// 所有使用该变量的地方
actuatorViewModel1_2_                  → actuatorViewModel1_2_1_2_
```

## 📂 **影响的主要文件列表**

### 1. 直接重命名的文件
- `include/ActuatorDataManager.h`
- `include/ActuatorDialog.h`
- `include/ActuatorViewModel1_2.h`
- `src/ActuatorDataManager.cpp`
- `src/ActuatorDialog.cpp`
- `src/ActuatorViewModel1_2.cpp`
- `ui/ActuatorDialog.ui`

### 2. 需要更新引用的文件
- `include/MainWindow_Qt_Simple.h`
- `src/MainWindow_Qt_Simple.cpp`
- `include/DataModels_Fixed.h`
- `include/XLSDataExporter.h`
- `src/XLSDataExporter.cpp`
- `include/JSONDataExporter.h`
- `src/JSONDataExporter.cpp`
- `include/CSVDataExporter.h`
- `src/CSVDataExporter.cpp`
- `include/DataExportManager.h`
- `src/DataExportManager.cpp`
- `SiteResConfig_Simple.pro` (项目文件)

### 3. 可能需要更新的构建文件
- `SiteResConfig_Simple.pro`
- `CMakeLists.txt` (如果存在)
- `Makefile` 相关文件

## 🔄 **实施步骤**

### 步骤1：备份项目
```bash
# 创建完整项目备份
cp -r SiteResConfig SiteResConfig_backup_before_rename
```

### 步骤2：重命名文件
1. 重命名所有头文件
2. 重命名所有源文件
3. 重命名UI文件

### 步骤3：修改类名
1. 在头文件中修改类声明
2. 在源文件中修改类定义
3. 修改构造函数和析构函数名称

### 步骤4：更新所有引用
1. 更新包含文件路径
2. 更新类型声明和使用
3. 更新变量名和成员变量名

### 步骤5：更新项目文件
1. 更新.pro文件中的源文件列表
2. 更新CMakeLists.txt（如果存在）

### 步骤6：编译验证
1. 清理构建目录
2. 重新编译项目
3. 修复任何编译错误
4. 运行测试验证功能

## ⚠️ **注意事项**

### 1. 保持功能一致性
- 重命名过程中不修改任何业务逻辑
- 只修改名称，不修改功能实现
- 保持所有接口和方法签名不变

### 2. 依赖关系处理
- 确保所有依赖这些类的代码都得到更新
- 特别注意模板参数和类型别名
- 检查是否有字符串形式的类名引用

### 3. 构建系统更新
- 更新项目文件中的源文件列表
- 更新任何自动生成的文件引用
- 确保资源文件路径正确

### 4. 测试验证
- 编译成功后进行功能测试
- 验证所有作动器相关功能正常
- 检查数据导入导出功能
- 验证UI界面显示正确

## 🎯 **预期结果**

重命名完成后，项目将具有：

1. **一致的命名规范** - 所有作动器相关类都使用`_1_2`后缀
2. **清晰的版本标识** - 便于区分不同版本的实现
3. **完整的功能保持** - 所有现有功能保持不变
4. **良好的可维护性** - 便于后续版本管理和升级

## 📋 **验证清单**

- [ ] 所有文件成功重命名
- [ ] 所有类名成功更新
- [ ] 所有包含文件路径正确
- [ ] 项目文件更新完成
- [ ] 编译无错误
- [ ] 功能测试通过
- [ ] UI界面正常显示
- [ ] 数据导入导出正常

这个重命名方案确保了系统性和完整性，避免遗漏任何相关文件或引用。
