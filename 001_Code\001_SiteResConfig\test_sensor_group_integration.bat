@echo off
echo ========================================
echo  测试传感器组管理功能完整集成
echo ========================================

REM 设置Qt环境变量
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目（测试传感器组管理功能完整集成）...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo 请检查上面的错误信息。
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！传感器组管理功能已完整集成
    echo ========================================
    
    echo.
    echo ✅ 完整集成的功能:
    echo - SensorDataManager传感器组管理后端
    echo - UI层传感器组创建集成
    echo - 传感器添加到组的完整流程
    echo - 传感器组ID自动分配和管理
    echo - 传感器ID自动分配和管理
    echo - 组内传感器序列号唯一性检查
    echo.
    echo 🔧 集成实现详解:
    echo 1. CreateSensorGroup() - 创建传感器组并保存到SensorDataManager
    echo 2. OnCreateSensor() - 创建传感器并添加到传感器组
    echo 3. createOrUpdateSensorGroup() - 管理传感器组中的传感器
    echo 4. extractSensorGroupIdFromItem() - 从UI项目获取组ID
    echo 5. saveSensorDetailedParams() - 保存传感器到SensorDataManager
    echo.
    echo 🎯 完整的数据流:
    echo 1. 用户创建传感器组 → UI + SensorDataManager保存
    echo 2. 用户在组中添加传感器 → 传感器保存 + 组更新
    echo 3. 传感器ID自动分配 → 确保唯一性
    echo 4. 组内序列号检查 → 防止重复
    echo 5. 数据同步 → UI树 + SensorDataManager一致
    echo.
    echo 🆕 新增的集成方法:
    echo - createOrUpdateSensorGroup() - 传感器组管理
    echo - extractSensorGroupIdFromItem() - 组ID提取
    echo - 修改CreateSensorGroup() - 集成SensorDataManager
    echo - 修改OnCreateSensor() - 完整的传感器创建流程
    echo.
    echo 🎮 测试步骤:
    echo 1. 启动软件
    echo 2. 新建项目
    echo 3. 创建传感器组（如"载荷_传感器组"）
    echo 4. 在组中添加传感器
    echo 5. 验证传感器ID自动分配
    echo 6. 验证组内序列号唯一性检查
    echo 7. 验证传感器组数据管理
    echo.
    
    if exist "SiteResConfig.exe" (
        echo 启动程序验证完整集成...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 启动程序验证完整集成...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 启动程序验证完整集成...
        start release\SiteResConfig.exe
    )
)

echo.
echo 📖 详细测试指南:
echo.
echo 🎮 传感器组完整功能测试:
echo 1. 启动软件后，新建一个项目
echo 2. 在硬件树中右键"传感器"节点
echo 3. 选择"新建" → "传感器组"
echo 4. 选择"载荷"类型，创建"载荷_传感器组"
echo 5. 观察日志：应显示"创建传感器组成功: 载荷_传感器组 (ID: 1)"
echo 6. 在"载荷_传感器组"上右键，选择"新建传感器"
echo 7. 选择传感器类型，填写参数，保存
echo 8. 观察日志：应显示传感器创建和组更新成功
echo 9. 再次添加传感器，验证ID递增分配
echo 10. 尝试添加重复序列号，验证唯一性检查
echo.
echo 🎮 数据一致性验证:
echo 1. 创建多个传感器组
echo 2. 在每个组中添加传感器
echo 3. 验证每个传感器都有唯一的ID
echo 4. 验证组内传感器序列号唯一
echo 5. 验证不同组间可以有相同序列号
echo.
echo 🎮 错误处理测试:
echo 1. 尝试创建重复名称的传感器组 → 应该被拒绝
echo 2. 在组内添加重复序列号的传感器 → 应该被拒绝
echo 3. 观察错误信息是否清晰明确
echo.
echo ✅ 预期结果:
echo - 传感器组创建成功并分配组ID
echo - 传感器创建成功并分配传感器ID
echo - 传感器正确添加到对应的传感器组
echo - 组内序列号唯一性检查正常工作
echo - UI树显示和SensorDataManager数据一致
echo - 日志记录完整的操作过程
echo.
echo 🚨 如果测试失败:
echo - 检查编译错误信息
echo - 查看日志中的详细错误
echo - 确认SensorDataManager初始化正常
echo - 验证UI集成代码正确
echo.
pause
