#ifndef JSONDATAEXPORTER_H
#define JSONDATAEXPORTER_H

#include "IDataExporter.h"
#include "XLSDataExporter_1_2.h"
#include <QJsonDocument>
#include <QJsonArray>
#include <QJsonObject>
#include <QDateTime>
#include <QFile>
#include <QTreeWidget>

class SensorDataManager_1_2;
class CtrlChanDataManager;  // 🆕 新增：控制通道数据管理器前向声明

/**
 * @brief JSON数据导出器
 * @details 负责将硬件树和传感器数据导出为JSON格式
 */
class JSONDataExporter_1_2 : public IDataExporter {
public:
    explicit JSONDataExporter_1_2(SensorDataManager_1_2* sensorManager = nullptr);
    virtual ~JSONDataExporter_1_2() = default;
    
    // 实现接口方法
    bool exportCompleteProject(const QString& filePath) override;
    bool importToHardwareTree(const QString& filePath, QTreeWidget* treeWidget) override;
    QString getSupportedExtension() const override;
    QString getFormatDescription() const override;
    QString getLastError() const override;

    /**
     * @brief 设置XLS导出器（用于复用数据收集逻辑）
     * @param xlsExporter XLS导出器指针
     */
    void setXLSExporter(XLSDataExporter_1_2* xlsExporter);
    
    // 🆕 新增：设置控制通道数据管理器
    /**
     * @brief 设置控制通道数据管理器
     * @param ctrlChanManager 控制通道数据管理器指针
     */
    void setCtrlChanDataManager(CtrlChanDataManager* ctrlChanManager);
    
    // JSON特有方法
    /**
     * @brief 设置JSON格式化选项
     * @param compact 是否使用紧凑格式
     */
    void setCompactFormat(bool compact);
    
    /**
     * @brief 设置缩进大小
     * @param indentSize 缩进空格数
     */
    void setIndentSize(int indentSize);

    // 🆕 新增：servo_control格式导出方法
    /**
     * @brief 导出单个作动器为servo_control格式
     * @param actuator 作动器参数
     * @param filePath 导出文件路径
     * @return 导出是否成功
     */
    bool exportActuatorAsServoControl(const UI::ActuatorParams_1_2& actuator, const QString& filePath);

    /**
     * @brief 将作动器转换为servo_control JSON对象
     * @param actuator 作动器参数
     * @return servo_control JSON对象
     */
    QJsonObject convertActuatorToServoControlJson(const UI::ActuatorParams_1_2& actuator);

    /**
     * @brief 从servo_control格式导入作动器
     * @param filePath JSON文件路径
     * @return 作动器参数
     */
    UI::ActuatorParams_1_2 importActuatorFromServoControl(const QString& filePath);

    // 🚫 已注释：冗余的完整项目导出功能，已被exportChannelConfig()替代
    /**
     * @brief 导出项目为JSON格式（增强版）
     * @param filePath 导出文件路径
     * @return 导出是否成功
     */
    // bool exportToJSON_1_2(const QString& filePath);

    /**
     * @brief 导出通道配置为channel_config.json格式
     * @param filePath 导出文件路径
     * @return 导出是否成功
     */
    bool exportChannelConfig(const QString& filePath);

private:
    SensorDataManager_1_2* sensorDataManager_;
    XLSDataExporter_1_2* xlsExporter_;  // 复用XLS导出器的数据收集功能
    CtrlChanDataManager* ctrlChanDataManager_;  // 🆕 新增：控制通道数据管理器
    QString lastError_;
    bool compactFormat_;
    int indentSize_;

    /**
     * @brief 项目数据结构
     */
    struct ProjectData {
        QTreeWidget* hardwareTree;
        QList<UI::SensorGroup_1_2> sensorGroups;
        QList<UI::ActuatorGroup_1_2> actuatorGroups;
        QList<UI::ControlChannelGroup> controlChannelGroups;
        QList<UI::NodeConfigParams> hardwareNodes;
        QString projectName;
        QString exportTime;
        QString version;
    };
    
    /**
     * @brief 导出项目数据为JSON
     * @param projectData 项目数据
     * @param filePath 文件路径
     * @return 导出是否成功
     */
    bool exportToJSON(const ProjectData& projectData, const QString& filePath);
    
    /**
     * @brief 导出项目数据为JSON（增强版）
     * @param projectData 项目数据
     * @param filePath 文件路径
     * @return 导出是否成功
     */
    bool exportToJSON_1_2(const ProjectData& projectData, const QString& filePath);

    /**
     * @brief 收集完整项目数据
     * @param treeWidget 硬件树控件
     * @return 项目数据结构
     */
    ProjectData collectProjectData();

    // JSON生成辅助方法（🚫 移除硬件树相关方法）
    QJsonArray createSensorGroupsJson(const QList<UI::SensorGroup_1_2>& sensorGroups);
    QJsonArray createActuatorGroupsJson(const QList<UI::ActuatorGroup_1_2>& actuatorGroups);
    QJsonArray createControlChannelGroupsJson(const QList<UI::ControlChannelGroup>& channelGroups);
    QJsonArray createHardwareNodesJson(const QList<UI::NodeConfigParams>& nodeConfigs);
    
    // 🆕 新增：创建通道配置数据（从控制通道数据管理器获取实际数据）
    /**
     * @brief 创建通道配置数据（从控制通道数据管理器获取实际数据）
     * @return 通道配置JSON数组
     */
    QJsonArray createChannelConfigDataFromMemory();
    
    /**
     * @brief 获取节点类型
     * @param item 树节点
     * @return 节点类型字符串
     */
    QString getItemType(QTreeWidgetItem* item) const;
    
    /**
     * @brief 从工具提示中提取参数
     * @param tooltip 工具提示文本
     * @param paramName 参数名称
     * @return 参数值
     */
    QString extractParameterFromTooltip(const QString& tooltip, const QString& paramName) const;
    
    /**
     * @brief 清除错误信息
     */
    void clearError();
    
    /**
     * @brief 设置错误信息
     * @param error 错误信息
     */
    void setError(const QString& error);
    
    /**
     * @brief 写入JSON文档到文件
     * @param document JSON文档
     * @param filePath 文件路径
     * @return 是否成功
     */
    bool writeJsonToFile(const QJsonDocument& document, const QString& filePath);
};

#endif // JSONDATAEXPORTER_H