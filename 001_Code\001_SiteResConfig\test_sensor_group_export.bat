@echo off
echo ========================================
echo  测试传感器组详细配置导出功能
echo ========================================

REM 设置Qt环境变量
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目（测试传感器组详细配置导出）...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo 请检查上面的错误信息。
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！传感器组详细配置导出功能已实现
    echo ========================================
    
    echo.
    echo ✅ 实现的功能:
    echo - 传感器组详细配置导出（参考作动器格式）
    echo - 添加"组序号"和"传感器组名称"列
    echo - 35列完整格式（组信息2列 + 传感器信息33列）
    echo - 与作动器导出格式保持一致
    echo.
    echo 🔧 实现详情:
    echo 1. 新增XLSDataExporter::exportSensorGroupDetails()方法
    echo 2. 新增addSensorGroupDetailToExcel()辅助方法
    echo 3. 修改传感器导出调用为传感器组方式
    echo 4. 新增getAllSensorGroups()方法
    echo 5. 实现传感器组数据提取逻辑
    echo.
    echo 📊 传感器组详细配置35列格式:
    echo 组信息（2列）:
    echo - 组序号, 传感器组名称
    echo.
    echo 传感器信息（33列）:
    echo - 传感器序号, 传感器序列号, 传感器类型, EDS标识, 尺寸
    echo - 型号, 量程, 精度, 单位, 灵敏度, 校准启用, 校准日期
    echo - 校准执行人, 单位类型, 单位值, 输入范围, 满量程最大值
    echo - 满量程最大值单位, 满量程最小值, 满量程最小值单位
    echo - 极性, 前置放大增益, 后置放大增益, 总增益, Delta K增益
    echo - 比例因子, 启用激励, 激励电压, 激励平衡, 激励频率
    echo - 相位, 编码器分辨率
    echo.
    echo 🎯 与作动器对比:
    echo - 作动器: 17列（组信息2列 + 作动器信息15列）
    echo - 传感器: 35列（组信息2列 + 传感器信息33列）
    echo - 相同的组织结构和导出风格
    echo - 统一的样式和格式
    echo.
    echo 🔄 参考作动器实现:
    echo - 表头格式: 组序号, 组名称, 设备序号, 设备序列号...
    echo - 组格式: 第一行显示组名称，后续行为空
    echo - 样式: 组行使用浅蓝色背景，粗体
    echo - 数据: 每个传感器占一行，包含完整信息
    echo.
    
    if exist "SiteResConfig.exe" (
        echo 启动程序验证功能...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 启动程序验证功能...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 启动程序验证功能...
        start release\SiteResConfig.exe
    )
)

echo.
echo 📖 详细测试指南:
echo.
echo 🎮 传感器组详细配置导出测试:
echo 1. 启动软件后，新建一个项目
echo 2. 创建传感器组（如"载荷_传感器组"）
echo 3. 在传感器组中添加多个传感器
echo 4. 配置每个传感器的详细参数
echo 5. 选择菜单: 数据导出 → 导出传感器详细信息到Excel
echo 6. 保存Excel文件并打开验证
echo.
echo 🎮 Excel格式验证:
echo 1. 检查表头是否为35列完整格式
echo 2. 验证第1列为"组序号"，第2列为"传感器组名称"
echo 3. 验证第3列为"传感器序号"，第4列为"传感器序列号"
echo 4. 检查组名称只在每组第一行显示
echo 5. 验证组行使用浅蓝色背景，粗体格式
echo 6. 确认所有传感器字段都正确导出
echo.
echo 🎮 多组测试:
echo 1. 创建多个传感器组（载荷、位置、压力等）
echo 2. 每个组添加不同数量的传感器
echo 3. 导出并验证所有组都正确显示
echo 4. 检查组序号是否正确分配
echo 5. 验证组间分隔清晰
echo.
echo ✅ 预期结果:
echo - 传感器详细配置导出为35列完整格式
echo - 包含组序号和传感器组名称列
echo - 与作动器导出格式风格一致
echo - 所有传感器字段都完整导出
echo - Excel格式专业，易于阅读
echo.
echo 🚨 如果测试失败:
echo - 检查编译错误信息
echo - 验证getAllSensorGroups方法正确
echo - 确认传感器组数据提取正确
echo - 检查Excel文件格式和列数
echo.
pause
