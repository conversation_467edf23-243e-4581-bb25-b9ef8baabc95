@echo off
chcp 65001 >nul
echo ========================================
echo  作动器组存储格式代码修改测试
echo ========================================
echo.

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile* >nul 2>&1
if exist "debug" rmdir /s /q debug >nul 2>&1
if exist "release" rmdir /s /q release >nul 2>&1
if exist "*.o" del *.o >nul 2>&1
if exist "ui_*.h" del ui_*.h >nul 2>&1

echo.
echo 检查作动器组相关代码修改...
echo.

echo 1. 检查ActuatorParams结构体修改...
findstr /C:"unitType" "include\ActuatorDialog.h" >nul
if errorlevel 1 (
    echo ❌ ActuatorParams结构体未添加unitType字段
    goto :error
) else (
    echo ✅ ActuatorParams结构体已添加unitType字段
)

findstr /C:"unitName" "include\ActuatorDialog.h" >nul
if errorlevel 1 (
    echo ❌ ActuatorParams结构体未添加unitName字段
    goto :error
) else (
    echo ✅ ActuatorParams结构体已添加unitName字段
)

findstr /C:"ActuatorGroup" "include\ActuatorDialog.h" >nul
if errorlevel 1 (
    echo ❌ ActuatorGroup结构体未定义
    goto :error
) else (
    echo ✅ ActuatorGroup结构体已定义
)

echo.
echo 2. 检查getUnitName方法...
findstr /C:"getUnitName" "include\ActuatorDialog.h" >nul
if errorlevel 1 (
    echo ❌ getUnitName方法未声明
    goto :error
) else (
    echo ✅ getUnitName方法已声明
)

findstr /C:"getUnitName" "src\ActuatorDialog.cpp" >nul
if errorlevel 1 (
    echo ❌ getUnitName方法未实现
    goto :error
) else (
    echo ✅ getUnitName方法已实现
)

echo.
echo 3. 检查XLSDataExporter修改...
findstr /C:"exportActuatorGroups" "include\XLSDataExporter.h" >nul
if errorlevel 1 (
    echo ❌ exportActuatorGroups方法未声明
    goto :error
) else (
    echo ✅ exportActuatorGroups方法已声明
)

findstr /C:"writeActuatorGroupHeader" "include\XLSDataExporter.h" >nul
if errorlevel 1 (
    echo ❌ writeActuatorGroupHeader方法未声明
    goto :error
) else (
    echo ✅ writeActuatorGroupHeader方法已声明
)

findstr /C:"exportActuatorGroups" "src\XLSDataExporter.cpp" >nul
if errorlevel 1 (
    echo ❌ exportActuatorGroups方法未实现
    goto :error
) else (
    echo ✅ exportActuatorGroups方法已实现
)

echo.
echo 4. 检查包含文件...
findstr /C:"#include \"ActuatorDialog.h\"" "src\XLSDataExporter.cpp" >nul
if errorlevel 1 (
    echo ❌ XLSDataExporter未包含ActuatorDialog.h
    goto :error
) else (
    echo ✅ XLSDataExporter已包含ActuatorDialog.h
)

echo.
echo 生成UI头文件...
uic ui\ActuatorDialog.ui -o ui_ActuatorDialog.h >nul 2>&1
if errorlevel 1 (
    echo ❌ ActuatorDialog UI文件生成失败
    goto :error
) else (
    echo ✅ ActuatorDialog UI头文件生成成功
)

uic ui\MainWindow.ui -o ui_MainWindow.h >nul 2>&1
if errorlevel 1 (
    echo ❌ MainWindow UI文件生成失败
    goto :error
) else (
    echo ✅ MainWindow UI头文件生成成功
)

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++ >nul 2>&1
if errorlevel 1 (
    echo ❌ qmake失败
    goto :error
) else (
    echo ✅ Makefile生成成功
)

echo.
echo 开始编译测试...
echo 这可能需要几分钟时间，请耐心等待...
echo.

mingw32-make clean >nul 2>&1
mingw32-make -j4 2>compile_errors.txt
if errorlevel 1 (
    echo ❌ 编译失败！
    echo.
    echo 编译错误信息：
    type compile_errors.txt
    echo.
    echo 请检查上述错误信息并进行修复。
    pause
    exit /b 1
) else (
    echo ✅ 编译成功！
    
    if exist compile_errors.txt del compile_errors.txt >nul 2>&1
    
    echo.
    echo ========================================
    echo  🎉 作动器组存储格式代码修改成功！
    echo ========================================
    
    if exist "SiteResConfig.exe" (
        echo.
        echo 📊 编译结果:
        echo ├─ 可执行文件: SiteResConfig.exe
        echo ├─ 文件大小: 
        for %%F in (SiteResConfig.exe) do echo │  └─ %%~zF 字节
        echo └─ 修改时间: 
        for %%F in (SiteResConfig.exe) do echo    └─ %%~tF
        echo.
        echo 🎯 作动器组存储格式修改内容:
        echo.
        echo 📋 数据结构修改:
        echo ├─ ✅ ActuatorParams结构体更新
        echo │  ├─ 添加unitType字段 (Unit类型)
        echo │  ├─ 添加unitName字段 (Unit名称)
        echo │  └─ 添加notes字段 (备注信息)
        echo ├─ ✅ 新增ActuatorGroup结构体
        echo │  ├─ groupId (组序号)
        echo │  ├─ groupName (作动器组名称)
        echo │  ├─ actuators (作动器列表)
        echo │  ├─ groupType (组类型)
        echo │  ├─ createTime (创建时间)
        echo │  └─ groupNotes (组备注)
        echo └─ ✅ getUnitName辅助方法
        echo.
        echo 📤 XLSX导出功能:
        echo ├─ ✅ exportActuatorGroups方法
        echo ├─ ✅ writeActuatorGroupHeader方法
        echo ├─ ✅ writeActuatorGroupData方法
        echo ├─ ✅ applyActuatorGroupStyles方法
        echo └─ ✅ createActuatorGroupSummarySheet方法
        echo.
        echo 📊 存储格式特点:
        echo ├─ ✅ 17列完整布局
        echo │  ├─ 组序号、作动器组名称
        echo │  ├─ 作动器序号、作动器序列号
        echo │  ├─ Unit类型、Unit名称 (双列存储)
        echo │  ├─ 完整的截面数据参数
        echo │  ├─ 完整的伺服控制器参数
        echo │  └─ 备注信息
        echo ├─ ✅ 分组显示优化
        echo │  ├─ 组名称只在第一行显示
        echo │  ├─ 组内作动器独立编号
        echo │  └─ 专业的分组样式
        echo ├─ ✅ 多工作表支持
        echo │  ├─ 主表: 作动器组配置表
        echo │  └─ 汇总表: 作动器组汇总
        echo └─ ✅ 完整的数据验证
        echo.
        echo 🔧 Unit字段增强:
        echo ├─ ✅ Unit类型: m/mm/cm/inch
        echo ├─ ✅ Unit名称: 米/毫米/厘米/英寸
        echo ├─ ✅ 类型名称匹配验证
        echo └─ ✅ 中文友好显示
        echo.
        echo 🎨 样式设计:
        echo ├─ ✅ 专业表头样式 (深蓝色背景)
        echo ├─ ✅ 分组行样式 (浅蓝色背景)
        echo ├─ ✅ 数据行交替颜色
        echo ├─ ✅ 优化的列宽设置
        echo └─ ✅ 完整的边框样式
        echo.
        
        set /p choice="是否启动程序测试作动器组功能？(Y/N): "
        if /i "%choice%"=="Y" (
            echo 启动程序...
            start SiteResConfig.exe
        )
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
        set /p choice="是否启动程序测试？(Y/N): "
        if /i "%choice%"=="Y" start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
        set /p choice="是否启动程序测试？(Y/N): "
        if /i "%choice%"=="Y" start release\SiteResConfig.exe
    ) else (
        echo ⚠️ 警告: 找不到可执行文件
    )
)

echo.
echo 📖 作动器组存储格式说明:
echo.
echo 🎯 核心改进:
echo - 支持作动器组层级结构管理
echo - Unit字段双列存储 (类型+名称)
echo - 17列完整的XLSX存储格式
echo - 专业的分组显示和样式设计
echo.
echo 🔧 技术特点:
echo - 完整的数据结构定义
echo - 专业的XLSX导出功能
echo - 多工作表支持 (主表+汇总表)
echo - 完整的数据验证机制
echo.
echo 测试完成！
pause
exit /b 0

:error
echo.
echo ❌ 作动器组存储格式代码修改测试失败！
echo.
echo 可能的问题：
echo 1. 数据结构修改不完整
echo 2. 方法声明或实现缺失
echo 3. 包含文件错误
echo 4. 编译环境问题
echo.
echo 请检查错误信息并重新修复。
pause
exit /b 1
