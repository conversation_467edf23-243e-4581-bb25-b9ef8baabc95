@echo off
echo ========================================
echo  增强实验工程管理功能测试
echo ========================================

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o
if exist "ui_*.h" del ui_*.h

echo.
echo 生成UI头文件...
uic ui\MainWindow.ui -o ui_MainWindow.h
if errorlevel 1 (
    echo UI文件生成失败！
    pause
    exit /b 1
) else (
    echo ✅ UI头文件生成成功: ui_MainWindow.h
)

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo.
    echo 请检查以下可能的问题：
    echo 1. HasInterfaceData函数实现
    echo 2. ClearInterfaceData函数实现
    echo 3. PromptSaveIfNeeded函数实现
    echo 4. projectPath字段使用
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！增强实验工程管理功能已实现
    echo ========================================
    
    if exist "SiteResConfig.exe" (
        echo 可执行文件: SiteResConfig.exe
        echo.
        echo ✅ 增强实验工程管理功能已实现！
        echo.
        echo 🗂️ 新建实验工程增强功能:
        echo ├─ 选择保存文件夹: 用户可选择工程保存位置
        echo ├─ 默认工程名称: 自动生成时间戳格式名称
        echo ├─ 数据检查机制: 检查界面是否有未保存数据
        echo ├─ 保存提示功能: 有数据时提示用户保存
        echo └─ 清空界面功能: 创建新工程时清空旧数据
        echo.
        echo 🔍 界面数据检查功能:
        echo ├─ HasInterfaceData(): 检查界面是否有数据
        echo │  ├─ 检查硬件树: 作动器组、传感器组、硬件节点
        echo │  ├─ 检查试验配置: 指令节点下的子项
        echo │  └─ 返回结果: true=有数据, false=无数据
        echo ├─ ClearInterfaceData(): 清空界面所有数据
        echo │  ├─ 清空硬件树: 删除所有用户创建的组和节点
        echo │  ├─ 清空试验配置: 删除所有子项和关联信息
        echo │  └─ 清空日志: 重置日志显示区域
        echo └─ SetDefaultEmptyInterface(): 设置默认空界面
        echo    ├─ 重新初始化树形控件
        echo    ├─ 重置窗口标题
        echo    └─ 记录操作日志
        echo.
        echo 💾 保存提示功能:
        echo ├─ PromptSaveIfNeeded(): 智能保存提示
        echo │  ├─ 检查数据: 调用HasInterfaceData()
        echo │  ├─ 无数据: 直接返回true，允许继续
        echo │  └─ 有数据: 弹出三选一对话框
        echo ├─ 对话框选项:
        echo │  ├─ "是": 保存当前工程后继续
        echo │  ├─ "否": 不保存直接继续
        echo │  └─ "取消": 返回当前工程
        echo └─ 返回值: true=继续操作, false=取消操作
        echo.
        echo 🏠 软件默认状态:
        echo ├─ 启动状态: 没有工程信息
        echo ├─ 窗口标题: "SiteResConfig"（不显示工程名）
        echo ├─ 界面状态: 空的树形控件结构
        echo ├─ currentProject_: nullptr（无当前工程）
        echo └─ 用户操作: 需要手动创建新工程
        echo.
        echo 📁 文件夹选择功能:
        echo ├─ 创建工程时: 用户选择保存文件夹
        echo ├─ 默认位置: 用户文档目录
        echo ├─ 工程文件: 自动生成完整路径
        echo ├─ 路径保存: 存储在currentProject_->projectPath
        echo └─ 后续保存: 直接使用已保存的路径
        echo.
        echo 📋 测试步骤:
        echo.
        echo 🎯 软件默认状态测试:
        echo 1. 启动程序
        echo 2. 验证窗口标题为"SiteResConfig"
        echo 3. 验证硬件树和试验配置树为空结构
        echo 4. 验证没有工程信息显示
        echo.
        echo 🎯 新建工程流程测试:
        echo 1. 点击"文件" → "新建工程"
        echo 2. 选择保存文件夹（如：D:\TestProjects）
        echo 3. 验证工程名称自动生成（如：20250807143025_实验工程）
        echo 4. 验证界面清空并重置为默认状态
        echo 5. 验证窗口标题更新为工程名称
        echo.
        echo 🎯 数据检查和保存提示测试:
        echo 1. 在界面中创建一些硬件设备
        echo    - 右键"作动器" → 新建 → 作动器组
        echo    - 右键"传感器" → 新建 → 传感器组
        echo 2. 点击"文件" → "新建工程"
        echo 3. 验证弹出保存提示对话框
        echo 4. 测试三个选项的不同行为:
        echo    - "是": 保存当前工程后创建新工程
        echo    - "否": 不保存直接创建新工程
        echo    - "取消": 返回当前工程
        echo.
        echo 🎯 保存功能增强测试:
        echo 1. 创建新工程并选择保存文件夹
        echo 2. 添加一些配置数据
        echo 3. 点击"文件" → "保存工程"
        echo 4. 验证直接保存到预设路径（无需再次选择）
        echo 5. 修改数据后再次保存，验证覆盖保存
        echo.
        echo 🔍 验证要点:
        echo ├─ 软件启动时无工程信息
        echo ├─ 新建工程时可选择保存位置
        echo ├─ 工程名称自动生成时间戳格式
        echo ├─ 有数据时提示保存
        echo ├─ 界面数据正确清空和重置
        echo ├─ 保存路径正确记录和使用
        echo └─ 窗口标题正确更新
        echo.
        echo 💡 设计特点:
        echo ├─ 智能检查: 自动检测界面数据状态
        echo ├─ 友好提示: 清晰的保存选择对话框
        echo ├─ 路径记忆: 保存路径自动记录
        echo ├─ 默认空白: 软件启动时无工程信息
        echo └─ 完整清理: 新建工程时彻底清空旧数据
        echo.
        echo 启动程序测试增强实验工程管理功能...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
        echo 启动程序...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
        echo 启动程序...
        start release\SiteResConfig.exe
    ) else (
        echo 警告: 找不到可执行文件！
    )
)

echo.
echo 📖 增强实验工程管理功能详细测试指南:
echo.
echo 🎯 软件默认状态验证:
echo 1. 启动程序后检查:
echo    - 窗口标题应该是"SiteResConfig"
echo    - 硬件树只有基本结构（作动器、传感器、硬件节点资源）
echo    - 试验配置树只有基本结构（实验、指令、DI、DO、控制通道）
echo    - 没有任何用户创建的组或设备
echo    - 状态栏显示系统就绪信息
echo.
echo 🎯 新建工程完整流程:
echo 1. 点击菜单"文件" → "新建工程"
echo 2. 选择保存文件夹对话框:
echo    - 默认打开用户文档目录
echo    - 选择或创建工程保存文件夹
echo    - 点击"选择文件夹"确认
echo 3. 工程创建成功:
echo    - 自动生成工程名称（时间戳格式）
echo    - 界面重置为默认空白状态
echo    - 窗口标题更新为工程名称
echo    - 显示创建成功消息和保存路径
echo.
echo 🎯 数据检查和保存提示:
echo 1. 创建一些测试数据:
echo    - 右键"作动器" → 新建 → 作动器组 → 选择"50kN_作动器组"
echo    - 右键"传感器" → 新建 → 传感器组 → 选择"载荷"
echo    - 右键"硬件节点资源" → 新建硬件节点
echo 2. 尝试新建工程:
echo    - 点击"文件" → "新建工程"
echo    - 应该弹出保存提示对话框
echo 3. 测试保存选项:
echo    - 点击"是": 先保存当前工程，然后创建新工程
echo    - 点击"否": 不保存，直接创建新工程
echo    - 点击"取消": 返回当前工程，不创建新工程
echo.
echo 🎯 保存功能验证:
echo 1. 新建工程并选择保存文件夹
echo 2. 添加一些配置（作动器组、传感器组等）
echo 3. 点击"文件" → "保存工程"
echo 4. 验证直接保存到预设路径
echo 5. 修改配置后再次保存，验证覆盖更新
echo.
echo 🔍 关键验证点:
echo ✓ 软件启动时窗口标题为"SiteResConfig"
echo ✓ 启动时界面为空白状态（无用户数据）
echo ✓ 新建工程时可以选择保存文件夹
echo ✓ 工程名称自动生成时间戳格式
echo ✓ 有数据时正确提示保存选择
echo ✓ 界面数据正确清空和重置
echo ✓ 保存路径正确记录和重用
echo ✓ 窗口标题正确更新显示工程名
echo.
pause
