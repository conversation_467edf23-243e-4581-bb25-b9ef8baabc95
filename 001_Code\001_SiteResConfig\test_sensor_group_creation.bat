@echo off
echo ========================================
echo  传感器组和传感器创建功能测试
echo ========================================

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o
if exist "ui_*.h" del ui_*.h

echo.
echo 生成UI头文件...
uic ui\MainWindow.ui -o ui_MainWindow.h
if errorlevel 1 (
    echo UI文件生成失败！
    pause
    exit /b 1
) else (
    echo ✅ UI头文件生成成功: ui_MainWindow.h
)

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo.
    echo 新增功能：
    echo ✅ 按传感器类型创建传感器组
    echo ✅ 传感器组右键菜单
    echo ✅ 传感器参数输入对话框
    echo ✅ 智能型号选择
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！传感器组和传感器创建功能已实现
    echo ========================================
    
    if exist "SiteResConfig.exe" (
        echo 可执行文件: SiteResConfig.exe
        echo.
        echo ✅ 传感器组和传感器创建功能已实现！
        echo.
        echo 📋 传感器组类型选项:
        echo ├─ 载荷 (创建"载荷_传感器组")
        echo ├─ 位置 (创建"位置_传感器组")
        echo ├─ 压力 (创建"压力_传感器组")
        echo ├─ 温度 (创建"温度_传感器组")
        echo ├─ 振动 (创建"振动_传感器组")
        echo ├─ 应变 (创建"应变_传感器组")
        echo ├─ 角度 (创建"角度_传感器组")
        echo └─ 自定义... (创建"自定义类型_传感器组")
        echo.
        echo 🌳 创建后的树形结构示例:
        echo 任务1
        echo ├─ 作动器
        echo ├─ 传感器
        echo │  ├─ 载荷_传感器组 (类型: "传感器组")
        echo │  │  ├─ 传感器_000001 [载荷传感器] (类型: "传感器设备")
        echo │  │  └─ 传感器_000002 [载荷传感器] (类型: "传感器设备")
        echo │  ├─ 位置_传感器组 (类型: "传感器组")
        echo │  │  └─ 传感器_000001 [位置传感器] (类型: "传感器设备")
        echo │  └─ 液压_传感器组 (类型: "传感器组") - 自定义
        echo │     └─ 传感器_000001 [液压传感器] (类型: "传感器设备")
        echo └─ 硬件节点资源
        echo.
        echo 🎯 传感器参数输入界面:
        echo ┌─────────────────────────────────────────────────────┐
        echo │                新建传感器                            │
        echo ├─────────────────────────────────────────────────────┤
        echo │ 传感器组（选择要添加传感器的传感器组名称）：          │
        echo │           载荷_传感器组\传感器_000001               │
        echo ├─────────────────────────────────────────────────────┤
        echo │ 序列号: [传感器_000001                    ]         │
        echo │ 类型:   [载荷传感器                      ] (只读)   │
        echo │ 型号:   [LCF-100kN ▼]                              │
        echo │ 量程:   [±100kN                          ]         │
        echo │ 精度:   [±0.1%FS                         ]         │
        echo │                                                     │
        echo │                    [确定] [取消]                    │
        echo └─────────────────────────────────────────────────────┘
        echo.
        echo 🔧 智能型号选择功能:
        echo.
        echo 📊 载荷传感器型号:
        echo ├─ LCF-500N (500牛顿载荷传感器)
        echo ├─ LCF-1kN (1千牛载荷传感器)
        echo ├─ LCF-5kN (5千牛载荷传感器)
        echo ├─ LCF-10kN (10千牛载荷传感器)
        echo ├─ LCF-50kN (50千牛载荷传感器)
        echo └─ LCF-100kN (100千牛载荷传感器)
        echo.
        echo 📏 位置传感器型号:
        echo ├─ LVDT-10mm (10毫米位移传感器)
        echo ├─ LVDT-25mm (25毫米位移传感器)
        echo ├─ LVDT-50mm (50毫米位移传感器)
        echo ├─ LVDT-100mm (100毫米位移传感器)
        echo └─ LVDT-200mm (200毫米位移传感器)
        echo.
        echo 🔧 其他类型传感器型号:
        echo ├─ 标准型号 (标准规格传感器)
        echo ├─ 高精度型号 (高精度传感器)
        echo └─ 工业级型号 (工业级传感器)
        echo.
        echo 💡 传感器参数说明:
        echo.
        echo 🏷️ 序列号: 自动生成，格式为"传感器_000001"
        echo 🏷️ 类型: 根据传感器组自动设置，如"载荷传感器"
        echo 🏷️ 型号: 根据传感器类型智能推荐相应型号
        echo 🏷️ 量程: 传感器的测量范围，如"±100kN"、"0-200mm"
        echo 🏷️ 精度: 传感器的测量精度，如"±0.1%FS"、"±0.01mm"
        echo.
        echo 🎯 操作流程:
        echo.
        echo 1️⃣ 创建传感器组:
        echo   - 右键"传感器"节点
        echo   - 选择"新建" → "传感器组"
        echo   - 选择传感器类型，如"载荷"
        echo   - 验证"载荷"组创建成功
        echo.
        echo 2️⃣ 创建传感器设备:
        echo   - 右键"载荷"传感器组
        echo   - 选择"新建" → "传感器"
        echo   - 填写传感器参数:
        echo     * 序列号: 传感器_000001 (自动生成)
        echo     * 类型: 载荷传感器 (自动设置)
        echo     * 型号: LCF-100kN (智能推荐)
        echo     * 量程: ±100kN
        echo     * 精度: ±0.1%FS
        echo   - 确定创建
        echo.
        echo 3️⃣ 重复创建:
        echo   - 可以在同一组中创建多个传感器
        echo   - 可以创建不同类型的传感器组
        echo   - 每个组的编号独立计算
        echo.
        echo 启动程序测试传感器组和传感器创建功能...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
        echo 启动程序...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
        echo 启动程序...
        start release\SiteResConfig.exe
    ) else (
        echo 警告: 找不到可执行文件！
    )
)

echo.
echo 📖 传感器组和传感器创建测试指南:
echo.
echo 🎯 完整测试流程:
echo.
echo 1️⃣ 测试载荷传感器组:
echo   - 右键"传感器" → "新建" → "传感器组"
echo   - 选择"载荷"
echo   - 验证"载荷"组创建成功
echo   - 右键"载荷"组 → "新建" → "传感器"
echo   - 验证型号选项为载荷传感器型号
echo   - 创建载荷传感器设备
echo.
echo 2️⃣ 测试位置传感器组:
echo   - 创建"位置"传感器组
echo   - 验证型号选项为位移传感器型号
echo   - 创建位置传感器设备
echo.
echo 3️⃣ 测试其他传感器组:
echo   - 测试压力、温度、振动、应变、角度传感器组
echo   - 验证每种类型的型号选项正确
echo   - 验证传感器类型自动设置
echo.
echo 4️⃣ 验证信息显示:
echo   - 验证工具提示显示完整参数
echo   - 验证日志记录传感器创建信息
echo   - 验证树形结构正确显示
echo.
echo 🔍 验证要点:
echo - 传感器组类型选择正确
echo - 传感器组右键菜单正常
echo - 传感器参数输入对话框正常
echo - 智能型号选择功能正常
echo - 传感器类型自动设置正确
echo - 自动编号功能正常
echo - 工具提示和日志信息完整
echo.
pause
