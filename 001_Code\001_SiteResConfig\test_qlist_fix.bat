@echo off
echo.
echo ========================================
echo QList Template Fix Test
echo ========================================
echo.

echo Fixed QList template instantiation error:
echo - Added #include ^<QtCore/QList^> to ActuatorStructs1_1.h
echo - Ensured proper struct definition order
echo.

if exist "SiteResConfig\SiteResConfig_Simple.pro" (
    echo Found project file, testing compilation...
    echo.
    
    cd SiteResConfig
    
    echo Cleaning old files...
    if exist "Makefile" del /Q "Makefile" 2>nul
    if exist "debug" rmdir /S /Q "debug" 2>nul
    if exist "release" rmdir /S /Q "release" 2>nul
    if exist "ui_*.h" del /Q "ui_*.h" 2>nul
    
    echo.
    echo Running qmake...
    qmake SiteResConfig_Simple.pro -spec win32-g++ "CONFIG+=debug" 2>&1
    
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo qmake successful!
        echo.
        echo Starting compilation...
        mingw32-make debug 2>&1
        
        if %ERRORLEVEL% EQU 0 (
            echo.
            echo *** COMPILATION SUCCESSFUL! ***
            echo.
            echo QList template error has been fixed!
            echo All ActuatorDialog1_1 files compiled successfully.
            echo.
            
            if exist "debug\SiteResConfig.exe" (
                echo Executable created successfully!
                echo Ready for testing ActuatorDialog1_1 functionality.
            )
        ) else (
            echo.
            echo *** COMPILATION FAILED ***
            echo Please check the error messages above.
        )
    ) else (
        echo.
        echo *** QMAKE FAILED ***
        echo Please check Qt environment configuration.
    )
    
    cd ..
) else (
    echo ERROR: Project file not found
)

echo.
echo ========================================
echo QList Fix Test Completed
echo ========================================
echo.
pause
