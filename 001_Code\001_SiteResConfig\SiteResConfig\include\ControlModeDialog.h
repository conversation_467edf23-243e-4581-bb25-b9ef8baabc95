#pragma once

/**
 * @file ControlModeDialog.h
 * @brief 控制模式设置对话框类定义
 * @details 使用Qt Designer设计的控制模式选择对话框
 * <AUTHOR> Assistant
 * @date 2025-08-06
 * @version 1.0.0
 */

#include <QtWidgets/QDialog>
#include <QtCore/QString>

QT_BEGIN_NAMESPACE
QT_END_NAMESPACE

namespace Ui {
class ControlModeDialog;
}

namespace UI {

/**
 * @brief 控制模式参数结构体
 * @details 存储控制模式的所有参数
 */
struct ControlModeParams {
    QString controlMode;    // 控制模式
    
    ControlModeParams() 
        : controlMode("力控制") {}
};

/**
 * @brief 控制模式设置对话框类
 * @details 使用.ui文件设计的标准Qt对话框
 */
class ControlModeDialog : public QDialog {
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口
     */
    explicit ControlModeDialog(QWidget* parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    virtual ~ControlModeDialog();

    /**
     * @brief 获取控制模式参数
     * @return 控制模式参数结构体
     */
    ControlModeParams getControlModeParams() const;

private slots:
    /**
     * @brief 确定按钮点击前的验证
     */
    void onAcceptClicked();

private:
    Ui::ControlModeDialog* ui;

    /**
     * @brief 初始化界面
     */
    void initializeUI();

    /**
     * @brief 连接信号槽
     */
    void connectSignals();
};

} // namespace UI
