<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ControlModeDialog</class>
 <widget class="QDialog" name="ControlModeDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>300</width>
    <height>150</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>控制模式设置</string>
  </property>
  <property name="modal">
   <bool>true</bool>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QHBoxLayout" name="modeLayout">
     <item>
      <widget class="QLabel" name="modeLabel">
       <property name="text">
        <string>控制模式:</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="modeComboBox">
       <property name="minimumSize">
        <size>
         <width>150</width>
         <height>0</height>
        </size>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <layout class="QHBoxLayout" name="buttonLayout">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="okButton">
       <property name="text">
        <string>确定</string>
       </property>
       <property name="default">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="cancelButton">
       <property name="text">
        <string>取消</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>cancelButton</sender>
   <signal>clicked()</signal>
   <receiver>ControlModeDialog</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>250</x>
     <y>120</y>
    </hint>
    <hint type="destinationlabel">
     <x>150</x>
     <y>75</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
