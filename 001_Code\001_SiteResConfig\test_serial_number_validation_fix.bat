@echo off
echo ========================================
echo  测试序列号验证问题修复
echo ========================================

REM 设置Qt环境变量
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目（测试序列号验证修复）...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo 请检查上面的错误信息。
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！序列号验证问题已修复
    echo ========================================
    
    echo.
    echo ✅ 修复内容:
    echo - 修复了序列号验证过于严格的问题
    echo - 允许中文字符在序列号中使用
    echo - 支持"作动器_000001"格式的自动编号
    echo - 解决了"无效的序列号"错误
    echo.
    echo 🔧 修复原理:
    echo 1. 原问题: 正则表达式 ^[A-Za-z0-9_-]+$ 不允许中文字符
    echo 2. 自动编号: "作动器_000001" 包含中文字符
    echo 3. 修复方案: 使用字符检查代替正则表达式
    echo 4. 允许: 中文字符、字母、数字、下划线、连字符、空格
    echo.
    echo 🎯 测试步骤:
    echo 1. 启动软件
    echo 2. 新建项目
    echo 3. 创建作动器组
    echo 4. 在组中添加作动器（使用默认序列号）
    echo 5. 验证不再出现"无效的序列号"错误
    echo.
    
    if exist "SiteResConfig.exe" (
        echo 启动程序验证修复...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 启动程序验证修复...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 启动程序验证修复...
        start release\SiteResConfig.exe
    )
)

echo.
echo 📖 验证指南:
echo.
echo 🎮 详细测试步骤:
echo 1. 启动软件后，新建一个项目
echo 2. 在硬件树中右键"作动器"节点
echo 3. 选择"新建" → "作动器组"
echo 4. 创建一个作动器组（如"测试组"）
echo 5. 在作动器组上右键，选择"新建作动器"
echo 6. 观察序列号默认值（应该是"作动器_000001"）
echo 7. 直接点击保存，不修改任何参数
echo 8. 观察是否还会出现"无效的序列号"错误
echo.
echo ✅ 预期结果:
echo - 不再出现"无效的序列号"错误
echo - 作动器保存成功
echo - 作动器组保存成功
echo - 日志显示正常的操作记录
echo.
echo 🧪 支持的序列号格式:
echo - 中文: "作动器_000001", "传感器_001"
echo - 英文: "ACT001", "SENSOR_01"
echo - 混合: "作动器ACT_001", "Sensor传感器_01"
echo - 数字: "123456", "001"
echo - 符号: "ACT-001", "SENSOR_001"
echo.
echo 🚨 不支持的字符:
echo - 控制字符（如换行符、制表符）
echo - 特殊符号（如@#$%^&*()+=[]{}|;:'"<>?/）
echo.
pause
