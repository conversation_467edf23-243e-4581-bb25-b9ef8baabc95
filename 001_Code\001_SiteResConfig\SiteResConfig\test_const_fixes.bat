@echo off
echo ========================================
echo  Const修饰符编译错误修复验证
echo ========================================

echo.
echo 🔧 已修复的编译错误:
echo.
echo 1. ActuatorDataManager::clearError() const 声明不一致
echo    - 头文件: void clearError() const;
echo    - 实现文件: void clearError() const; ✅ 已修复
echo.
echo 2. SensorDataManager::clearError() const 声明不一致  
echo    - 头文件: void clearError() const;
echo    - 实现文件: void clearError() const; ✅ 已修复
echo.
echo 3. SensorDataManager::setError() const 声明不一致
echo    - 头文件: void setError(const QString& error) const;
echo    - 实现文件: void setError(const QString& error) const; ✅ 已修复
echo.
echo 4. SensorDataManager中const_cast使用
echo    - validateSensorParams中移除const_cast ✅ 已修复
echo    - getSensor中移除const_cast ✅ 已修复
echo    - getAllSensorSerialNumbers中移除const_cast ✅ 已修复
echo.
echo 5. MainWindow::AddLogEntry() const 声明不一致
echo    - 头文件: void AddLogEntry(...) const;
echo    - 实现文件: void AddLogEntry(...) const; ✅ 已修复
echo    - 移除validateSensorData中的const_cast ✅ 已修复
echo.

echo 🔍 修复原理:
echo.
echo 1. mutable成员变量:
echo    - ActuatorDataManager::lastError_ 声明为 mutable
echo    - SensorDataManager::lastError_ 声明为 mutable
echo    - 允许在const方法中修改错误状态
echo.
echo 2. const方法一致性:
echo    - 头文件和实现文件的const声明必须完全一致
echo    - clearError()和setError()都声明为const
echo    - 因为它们只修改mutable成员变量
echo.
echo 3. 避免const_cast:
echo    - const_cast是危险的，应该避免使用
echo    - 通过正确的const设计避免const_cast
echo    - 日志记录方法也可以声明为const
echo.

echo 开始编译验证...
echo.

if not exist "Makefile" (
    echo 生成Makefile...
    qmake SiteResConfig_Simple.pro -spec win32-g++
    if errorlevel 1 (
        echo qmake失败！
        pause
        exit /b 1
    )
)

echo.
echo 清理之前的构建...
mingw32-make clean

echo.
echo 开始编译...
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  ❌ 编译失败！
    echo ========================================
    echo.
    echo 请检查以下可能的问题：
    echo 1. const方法声明和定义是否一致
    echo 2. mutable成员变量是否正确声明
    echo 3. 是否还有其他const_cast使用
    echo 4. 错误处理方法的const声明
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  ✅ 编译成功！所有const修饰符错误已修复
    echo ========================================
    
    if exist "SiteResConfig.exe" (
        echo 可执行文件: SiteResConfig.exe
        echo.
        echo ✅ Const修饰符修复完成！
        echo.
        echo 🔧 修复总结:
        echo ├─ ActuatorDataManager: clearError()方法const声明一致
        echo ├─ SensorDataManager: clearError()和setError()方法const声明一致
        echo ├─ SensorDataManager: lastError_成员变量声明为mutable
        echo ├─ MainWindow: AddLogEntry()方法声明为const
        echo └─ 移除所有const_cast使用，使用正确的const设计
        echo.
        echo 🎯 技术要点:
        echo ├─ mutable关键字: 允许const方法修改特定成员
        echo ├─ const一致性: 头文件和实现文件声明必须一致
        echo ├─ 错误处理设计: 错误状态不影响对象的逻辑const性
        echo └─ 避免const_cast: 通过正确设计避免类型转换
        echo.
        echo 启动程序验证功能...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
        echo 启动程序...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
        echo 启动程序...
        start release\SiteResConfig.exe
    ) else (
        echo 警告: 找不到可执行文件！
    )
)

echo.
echo ========================================
echo  修复验证完成
echo ========================================
pause
