#include <QApplication>
#include <QMainWindow>
#include <QVBoxLayout>
#include <QPushButton>
#include <QDebug>
#include "BasicInfoWidget.h"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // 创建主窗口
    QMainWindow mainWindow;
    mainWindow.setWindowTitle("BasicInfoWidget 测试程序");
    mainWindow.resize(800, 600);
    
    // 创建中央部件
    QWidget* centralWidget = new QWidget(&mainWindow);
    mainWindow.setCentralWidget(centralWidget);
    
    // 创建布局
    QVBoxLayout* layout = new QVBoxLayout(centralWidget);
    
    // 创建BasicInfoWidget
    BasicInfoWidget* basicInfoWidget = new BasicInfoWidget(centralWidget);
    layout->addWidget(basicInfoWidget);
    
    // 创建测试按钮
    QPushButton* testButton1 = new QPushButton("设置测试数据1", centralWidget);
    QPushButton* testButton2 = new QPushButton("设置测试数据2", centralWidget);
    QPushButton* clearButton = new QPushButton("清空数据", centralWidget);
    
    layout->addWidget(testButton1);
    layout->addWidget(testButton2);
    layout->addWidget(clearButton);
    
    // 连接信号和槽
    QObject::connect(testButton1, &QPushButton::clicked, [basicInfoWidget]() {
        // 创建测试节点信息
        NodeInfo testNode;
        testNode.nodeName = "测试通道1";
        testNode.nodeType = "控制通道";
        testNode.status = NodeStatus::Online;
        testNode.nodeId = "CH1";
        
        // 设置基本信息属性
        testNode.setBasicProperty("通道名称", "测试通道1");
        testNode.setBasicProperty("硬件关联选择", "硬件组1");
        testNode.setBasicProperty("载荷1传感器选择", "LS001");
        testNode.setBasicProperty("载荷2传感器选择", "LS002");
        testNode.setBasicProperty("位置传感器选择", "PS001");
        testNode.setBasicProperty("控制作动器选择", "SA001");
        testNode.setBasicProperty("下位机ID", "1");
        testNode.setBasicProperty("站点ID", "1");
        testNode.setBasicProperty("使能状态", true);
        testNode.setBasicProperty("控制作动器极性", "正极性");
        testNode.setBasicProperty("载荷1传感器极性", "正极性");
        testNode.setBasicProperty("载荷2传感器极性", "负极性");
        testNode.setBasicProperty("位置传感器极性", "正极性");
        
        basicInfoWidget->setNodeInfo(testNode);
        qDebug() << "已设置测试数据1";
    });
    
    QObject::connect(testButton2, &QPushButton::clicked, [basicInfoWidget]() {
        // 创建另一个测试节点信息
        NodeInfo testNode;
        testNode.nodeName = "测试通道2";
        testNode.nodeType = "控制通道";
        testNode.status = NodeStatus::Warning;
        testNode.nodeId = "CH2";
        
        // 设置基本信息属性
        testNode.setBasicProperty("通道名称", "测试通道2");
        testNode.setBasicProperty("硬件关联选择", "硬件组2");
        testNode.setBasicProperty("载荷1传感器选择", "LS003");
        testNode.setBasicProperty("载荷2传感器选择", "LS004");
        testNode.setBasicProperty("位置传感器选择", "PS002");
        testNode.setBasicProperty("控制作动器选择", "SA002");
        testNode.setBasicProperty("下位机ID", "2");
        testNode.setBasicProperty("站点ID", "2");
        testNode.setBasicProperty("使能状态", false);
        testNode.setBasicProperty("控制作动器极性", "负极性");
        testNode.setBasicProperty("载荷1传感器极性", "负极性");
        testNode.setBasicProperty("载荷2传感器极性", "正极性");
        testNode.setBasicProperty("位置传感器极性", "负极性");
        
        basicInfoWidget->setNodeInfo(testNode);
        qDebug() << "已设置测试数据2";
    });
    
    QObject::connect(clearButton, &QPushButton::clicked, [basicInfoWidget]() {
        basicInfoWidget->clearInfo();
        qDebug() << "已清空数据";
    });
    
    // 显示窗口
    mainWindow.show();
    
    return app.exec();
} 