@echo off
echo ========================================
echo  Rebuilding with Debug Code
echo ========================================

echo Checking if we need to rebuild...

REM Check if CustomTreeWidgets.cpp is newer than the object file
if exist "SiteResConfig\src\CustomTreeWidgets.cpp" (
    if exist "build-SiteResConfig_Simple-Desktop_Qt_5_14_2_MinGW_32_bit-Debug\debug\CustomTreeWidgets.o" (
        echo Both source and object files exist.
        echo We need to rebuild to include debug code.
    ) else (
        echo Object file missing, need to build.
    )
) else (
    echo Source file not found!
    pause
    exit /b 1
)

echo.
echo Attempting to rebuild using existing build directory...

cd build-SiteResConfig_Simple-Desktop_Qt_5_14_2_MinGW_32_bit-Debug

if exist "Makefile" (
    echo Found Makefile, attempting to rebuild...
    echo.
    
    REM Try to use mingw32-make if available
    where mingw32-make >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo Using mingw32-make...
        mingw32-make
    ) else (
        echo mingw32-make not found in PATH
        echo.
        echo Please rebuild the project in Qt Creator:
        echo 1. Open the project in Qt Creator
        echo 2. Build -> Rebuild Project
        echo 3. Run the debug script again
    )
) else (
    echo No Makefile found.
    echo Please rebuild the project in Qt Creator.
)

cd ..

echo.
echo Rebuild attempt completed.
echo Now try running: run_debug.bat

pause
