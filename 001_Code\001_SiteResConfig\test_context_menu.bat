@echo off
echo ========================================
echo  硬件树右键菜单功能测试
echo ========================================

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o
if exist "ui_*.h" del ui_*.h

echo.
echo 生成UI头文件...
uic ui\MainWindow.ui -o ui_MainWindow.h
if errorlevel 1 (
    echo UI文件生成失败！
    pause
    exit /b 1
) else (
    echo ✅ UI头文件生成成功: ui_MainWindow.h
)

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo.
    echo 添加的功能：
    echo ✅ 硬件树右键菜单支持
    echo ✅ 作动器组创建功能
    echo ✅ 传感器组创建功能
    echo ✅ 硬件节点组创建功能
    echo ✅ 按量程快速创建作动器组
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！右键菜单功能已添加
    echo ========================================
    
    if exist "SiteResConfig.exe" (
        echo 可执行文件: SiteResConfig.exe
        echo.
        echo ✅ 硬件树右键菜单功能已实现！
        echo.
        echo 🖱️ 右键菜单功能:
        echo.
        echo 📁 作动器节点右键菜单:
        echo ├─ 新建
        echo │  └─ 作动器组
        echo └─ 快速创建作动器组
        echo    ├─ 50kN_作动器
        echo    ├─ 100kN_作动器
        echo    ├─ 200kN_作动器
        echo    └─ 500kN_作动器
        echo.
        echo 📁 传感器节点右键菜单:
        echo └─ 新建
        echo    └─ 传感器组
        echo.
        echo 📁 硬件节点资源右键菜单:
        echo └─ 新建
        echo    └─ 硬件节点组
        echo.
        echo 🎯 使用方法:
        echo 1. 启动程序
        echo 2. 在硬件资源树中找到"作动器"节点
        echo 3. 右键点击"作动器"节点
        echo 4. 选择"新建" → "作动器组"或快速创建选项
        echo 5. 输入组名称或选择预定义的量程组
        echo 6. 组将自动创建并显示在树中
        echo.
        echo 🎨 功能特色:
        echo - 支持自定义组名称
        echo - 预定义常用量程组（50kN, 100kN, 200kN, 500kN）
        echo - 自动添加示例设备到量程组
        echo - 组节点带有文件夹图标
        echo - 操作日志记录
        echo - 工具提示显示详细信息
        echo.
        echo 📋 快速创建作动器组的优势:
        echo - 50kN作动器组: 适用于小型试验
        echo - 100kN作动器组: 适用于中型试验
        echo - 200kN作动器组: 适用于大型试验
        echo - 500kN作动器组: 适用于重载试验
        echo.
        echo 🔧 扩展功能:
        echo - 可以继续在组下添加具体的作动器设备
        echo - 支持拖拽重新组织设备
        echo - 支持组的重命名和删除
        echo - 支持设备在组间移动
        echo.
        echo 启动程序测试右键菜单功能...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
        echo 启动程序...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
        echo 启动程序...
        start release\SiteResConfig.exe
    ) else (
        echo 警告: 找不到可执行文件！
    )
)

echo.
echo 📖 右键菜单测试指南:
echo.
echo 🎯 测试步骤:
echo 1. 程序启动后，查看硬件资源树
echo 2. 找到"任务1"下的"作动器"节点
echo 3. 右键点击"作动器"节点
echo 4. 验证菜单显示：
echo    - "新建"子菜单包含"作动器组"
echo    - "快速创建作动器组"子菜单
echo 5. 测试"新建" → "作动器组"下拉框选择
echo 6. 测试快速创建50kN_作动器、100kN_作动器等组
echo 7. 验证组创建后的树结构
echo 8. 检查日志中的操作记录
echo.
echo 🔍 验证要点:
echo - 右键菜单正确显示
echo - 输入对话框正常工作
echo - 组节点正确创建
echo - 示例设备自动添加（快速创建）
echo - 日志记录操作信息
echo - 工具提示显示设备信息
echo.
pause
