@echo off
echo ========================================
echo  CSV to JSON Converter
echo ========================================

set CSV_FILE=C:\Users\<USER>\Desktop\20250812095644_实验工程.csv
set JSON_FILE=C:\Users\<USER>\Desktop\20250812095644_实验工程.json

echo Checking if CSV file exists...
if exist "%CSV_FILE%" (
    echo Found CSV file: %CSV_FILE%
) else (
    echo CSV file not found: %CSV_FILE%
    echo.
    echo Searching for CSV files on desktop...
    dir "C:\Users\<USER>\Desktop\*.csv"
    echo.
    pause
    exit /b 1
)

echo.
echo Starting conversion...
echo Input file: %CSV_FILE%
echo Output file: %JSON_FILE%
echo.

REM Run the converter
csv_to_json_converter.exe "%CSV_FILE%" "%JSON_FILE%"

if %errorlevel%==0 (
    echo.
    echo Conversion completed successfully!
    echo JSON file saved to: %JSON_FILE%
    
    if exist "%JSON_FILE%" (
        echo JSON file created successfully
        for %%I in ("%JSON_FILE%") do (
            echo File size: %%~zI bytes
        )
    ) else (
        echo JSON file creation failed
    )
) else (
    echo.
    echo Conversion failed with error code: %errorlevel%
)

echo.
echo Process completed.
pause
