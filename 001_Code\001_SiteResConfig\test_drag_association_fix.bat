@echo off
echo ========================================
echo 测试拖拽关联信息动态生成修复
echo ========================================

cd SiteResConfig

echo.
echo [1/3] 清理旧的编译文件...
if exist Makefile (
    mingw32-make clean >nul 2>&1
)

echo.
echo [2/3] 重新生成Makefile...
qmake SiteResConfig_Simple.pro

echo.
echo [3/3] 编译项目...
mingw32-make -j4

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ 编译成功！
    echo.
    echo 🎯 修复内容：
    echo - 修改了拖拽数据格式，包含父节点信息
    echo - 新格式：通道名^|类型^|父节点名
    echo - 例如："CH1^|硬件节点通道^|LD-B1"
    echo.
    echo 📋 测试场景：
    echo 1. 拖拽 LD-B1 的 CH1 → 应显示 "LD-B1 - CH1"
    echo 2. 拖拽 LD-B1 的 CH2 → 应显示 "LD-B1 - CH2"
    echo 3. 拖拽 LD-B2 的 CH1 → 应显示 "LD-B2 - CH1"
    echo 4. 拖拽 LD-B2 的 CH2 → 应显示 "LD-B2 - CH2"
    echo.
    echo 🚀 启动应用进行测试...
    start "" "debug\SiteResConfig.exe"
) else (
    echo.
    echo ❌ 编译失败！请检查错误信息。
)

pause
