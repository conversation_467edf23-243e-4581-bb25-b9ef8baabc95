/**
 * @file CtrlChanDataManager.cpp
 * @brief 控制通道数据管理器实现
 * @details 管理控制通道组和控制通道参数的存储、检索和操作
 * <AUTHOR> Agent
 * @date 2024-01-15
 * @version 1.0.0
 */

#include "CtrlChanDataManager.h"
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QFile>
#include <QTextStream>
#include <QStandardPaths>
#include <QDir>
#include <algorithm>

CtrlChanDataManager::CtrlChanDataManager(QObject* parent)
    : QObject(parent)
    , nextGroupId_(1) {
    qDebug() << "CtrlChanDataManager 初始化完成";
}

CtrlChanDataManager::~CtrlChanDataManager() {
    QMutexLocker locker(&dataMutex_);
    groupStorage_.clear();
    qDebug() << "CtrlChanDataManager 析构完成";
}

// ============================================================================
// 控制通道组管理方法
// ============================================================================

bool CtrlChanDataManager::createControlChannelGroup(const UI::ControlChannelGroup& group) {
    QMutexLocker locker(&dataMutex_);
    
    if (!validateControlChannelGroup(group)) {
        qDebug() << "控制通道组数据验证失败";
        return false;
    }
    
    // 检查组ID是否已存在
    if (groupStorage_.contains(group.groupId)) {
        qDebug() << QString("控制通道组ID %1 已存在").arg(group.groupId);
        return false;
    }
    
    // 创建组的副本并设置创建时间
    UI::ControlChannelGroup newGroup = group;
    if (newGroup.createTime.empty()) {
        newGroup.createTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss").toStdString();
    }
    
    // 如果组ID为0或负数，自动分配新ID
    if (newGroup.groupId <= 0) {
        newGroup.groupId = generateNextGroupId();
    } else {
        // 更新下一个组ID
        if (newGroup.groupId >= nextGroupId_) {
            nextGroupId_ = newGroup.groupId + 1;
        }
    }
    
    groupStorage_[newGroup.groupId] = newGroup;
    
    qDebug() << QString("创建控制通道组成功: ID=%1, 名称=%2, 通道数=%3")
                .arg(newGroup.groupId)
                .arg(QString::fromStdString(newGroup.groupName))
                .arg(newGroup.channels.size());
    
    emit controlChannelGroupCreated(newGroup.groupId);
    return true;
}

bool CtrlChanDataManager::updateControlChannelGroup(const UI::ControlChannelGroup& group) {
    QMutexLocker locker(&dataMutex_);
    
    if (!validateControlChannelGroup(group)) {
        qDebug() << "控制通道组数据验证失败";
        return false;
    }
    
    if (!groupStorage_.contains(group.groupId)) {
        qDebug() << QString("控制通道组ID %1 不存在").arg(group.groupId);
        return false;
    }
    
    groupStorage_[group.groupId] = group;
    
    qDebug() << QString("更新控制通道组成功: ID=%1, 名称=%2")
                .arg(group.groupId)
                .arg(QString::fromStdString(group.groupName));
    
    emit controlChannelGroupUpdated(group.groupId);
    return true;
}

bool CtrlChanDataManager::deleteControlChannelGroup(int groupId) {
    QMutexLocker locker(&dataMutex_);
    
    if (!groupStorage_.contains(groupId)) {
        qDebug() << QString("控制通道组ID %1 不存在").arg(groupId);
        return false;
    }
    
    QString groupName = QString::fromStdString(groupStorage_[groupId].groupName);
    int channelCount = groupStorage_[groupId].channels.size();
    
    groupStorage_.remove(groupId);
    
    qDebug() << QString("删除控制通道组成功: ID=%1, 名称=%2, 包含通道数=%3")
                .arg(groupId).arg(groupName).arg(channelCount);
    
    emit controlChannelGroupDeleted(groupId);
    return true;
}

QList<UI::ControlChannelGroup> CtrlChanDataManager::getAllControlChannelGroups() const {
    QMutexLocker locker(&dataMutex_);
    
    QList<UI::ControlChannelGroup> groups;
    for (auto it = groupStorage_.begin(); it != groupStorage_.end(); ++it) {
        groups.append(it.value());
    }
    
    // 按创建时间排序
    sortGroupsByCreateTime(groups);
    
    qDebug() << QString("获取所有控制通道组: 共 %1 个组").arg(groups.size());
    return groups;
}

UI::ControlChannelGroup CtrlChanDataManager::getControlChannelGroup(int groupId) const {
    QMutexLocker locker(&dataMutex_);
    
    if (groupStorage_.contains(groupId)) {
        return groupStorage_[groupId];
    }
    
    qDebug() << QString("控制通道组ID %1 不存在，返回空组").arg(groupId);
    return UI::ControlChannelGroup(); // 返回默认构造的空组
}

bool CtrlChanDataManager::hasControlChannelGroup(int groupId) const {
    QMutexLocker locker(&dataMutex_);
    return groupStorage_.contains(groupId);
}

// ============================================================================
// 控制通道管理方法
// ============================================================================

bool CtrlChanDataManager::addChannelToGroup(int groupId, const UI::ControlChannelParams& channel) {
    QMutexLocker locker(&dataMutex_);
    
    if (!validateControlChannelParams(channel)) {
        qDebug() << "控制通道参数验证失败";
        return false;
    }
    
    if (!groupStorage_.contains(groupId)) {
        qDebug() << QString("控制通道组ID %1 不存在").arg(groupId);
        return false;
    }
    
    // 检查通道ID是否已存在于该组中
    auto& group = groupStorage_[groupId];
    for (const auto& existingChannel : group.channels) {
        if (existingChannel.channelId == channel.channelId) {
            qDebug() << QString("通道ID %1 已存在于组 %2 中")
                        .arg(QString::fromStdString(channel.channelId)).arg(groupId);
            return false;
        }
    }
    
    group.channels.push_back(channel);
    
    qDebug() << QString("添加控制通道成功: 组ID=%1, 通道ID=%2, 通道名=%3")
                .arg(groupId)
                .arg(QString::fromStdString(channel.channelId))
                .arg(QString::fromStdString(channel.channelName));
    
    emit controlChannelAdded(groupId, QString::fromStdString(channel.channelId));
    return true;
}

bool CtrlChanDataManager::updateChannelInGroup(int groupId, const UI::ControlChannelParams& channel) {
    QMutexLocker locker(&dataMutex_);
    
    if (!validateControlChannelParams(channel)) {
        qDebug() << "控制通道参数验证失败";
        return false;
    }
    
    if (!groupStorage_.contains(groupId)) {
        qDebug() << QString("控制通道组ID %1 不存在").arg(groupId);
        return false;
    }
    
    auto& group = groupStorage_[groupId];
    for (auto& existingChannel : group.channels) {
        if (existingChannel.channelId == channel.channelId) {
            existingChannel = channel;
            
            qDebug() << QString("更新控制通道成功: 组ID=%1, 通道ID=%2")
                        .arg(groupId)
                        .arg(QString::fromStdString(channel.channelId));
            
            emit controlChannelUpdated(groupId, QString::fromStdString(channel.channelId));
            return true;
        }
    }
    
    qDebug() << QString("通道ID %1 在组 %2 中不存在")
                .arg(QString::fromStdString(channel.channelId)).arg(groupId);
    return false;
}

bool CtrlChanDataManager::removeChannelFromGroup(int groupId, const std::string& channelId) {
    QMutexLocker locker(&dataMutex_);
    
    if (!groupStorage_.contains(groupId)) {
        qDebug() << QString("控制通道组ID %1 不存在").arg(groupId);
        return false;
    }
    
    auto& group = groupStorage_[groupId];
    auto it = std::find_if(group.channels.begin(), group.channels.end(),
                          [&channelId](const UI::ControlChannelParams& channel) {
                              return channel.channelId == channelId;
                          });
    
    if (it != group.channels.end()) {
        group.channels.erase(it);
        
        qDebug() << QString("移除控制通道成功: 组ID=%1, 通道ID=%2")
                    .arg(groupId)
                    .arg(QString::fromStdString(channelId));
        
        emit controlChannelRemoved(groupId, QString::fromStdString(channelId));
        return true;
    }
    
    qDebug() << QString("通道ID %1 在组 %2 中不存在")
                .arg(QString::fromStdString(channelId)).arg(groupId);
    return false;
}

std::vector<UI::ControlChannelParams> CtrlChanDataManager::getChannelsInGroup(int groupId) const {
    QMutexLocker locker(&dataMutex_);
    
    if (groupStorage_.contains(groupId)) {
        return groupStorage_[groupId].channels;
    }
    
    qDebug() << QString("控制通道组ID %1 不存在，返回空列表").arg(groupId);
    return std::vector<UI::ControlChannelParams>();
}

// ============================================================================
// 数据统计和查询方法
// ============================================================================

int CtrlChanDataManager::getGroupCount() const {
    QMutexLocker locker(&dataMutex_);
    return groupStorage_.size();
}

int CtrlChanDataManager::getTotalChannelCount() const {
    QMutexLocker locker(&dataMutex_);
    
    int totalCount = 0;
    for (auto it = groupStorage_.begin(); it != groupStorage_.end(); ++it) {
        totalCount += it.value().channels.size();
    }
    return totalCount;
}

int CtrlChanDataManager::getChannelCountInGroup(int groupId) const {
    QMutexLocker locker(&dataMutex_);
    
    if (groupStorage_.contains(groupId)) {
        return groupStorage_[groupId].channels.size();
    }
    return 0;
}

void CtrlChanDataManager::clearAllData() {
    QMutexLocker locker(&dataMutex_);

    int groupCount = groupStorage_.size();

    // 🆕 修复死锁：直接计算通道数量，避免调用getTotalChannelCount()
    int channelCount = 0;
    for (auto it = groupStorage_.begin(); it != groupStorage_.end(); ++it) {
        channelCount += it.value().channels.size();
    }

    groupStorage_.clear();
    nextGroupId_ = 1;

    qDebug() << QString("清空所有控制通道数据: 清除了 %1 个组，%2 个通道")
                .arg(groupCount).arg(channelCount);
}

// ============================================================================
// 数据导入导出方法
// ============================================================================

bool CtrlChanDataManager::exportToCSV(const QString& filePath) const {
    QMutexLocker locker(&dataMutex_);

    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        qDebug() << QString("无法创建CSV文件: %1").arg(filePath);
        return false;
    }

    QTextStream out(&file);
    out.setCodec("UTF-8");
    out.setGenerateByteOrderMark(true);

    // 写入CSV头部
    out << "# 控制通道配置文件\n";
    out << "# 导出时间," << QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss") << "\n";
    out << "\n";

    // 写入表头
    out << "组ID,组名称,组类型,创建时间,组备注,通道ID,通道名称,硬件关联,载荷1传感器,载荷2传感器,位置传感器,控制作动器,备注\n";

    // 写入数据
    for (auto it = groupStorage_.begin(); it != groupStorage_.end(); ++it) {
        const UI::ControlChannelGroup& group = it.value();

        if (group.channels.empty()) {
            // 如果组内没有通道，只写入组信息
            out << QString("%1,%2,%3,%4,%5,,,,,,,,\n")
                   .arg(group.groupId)
                   .arg(QString::fromStdString(group.groupName))
                   .arg(QString::fromStdString(group.groupType))
                   .arg(QString::fromStdString(group.createTime))
                   .arg(QString::fromStdString(group.groupNotes));
        } else {
            // 写入组内每个通道
            for (const auto& channel : group.channels) {
                out << QString("%1,%2,%3,%4,%5,%6,%7,%8,%9,%10,%11,%12,%13\n")
                       .arg(group.groupId)
                       .arg(QString::fromStdString(group.groupName))
                       .arg(QString::fromStdString(group.groupType))
                       .arg(QString::fromStdString(group.createTime))
                       .arg(QString::fromStdString(group.groupNotes))
                       .arg(QString::fromStdString(channel.channelId))
                       .arg(QString::fromStdString(channel.channelName))
                       .arg(QString::fromStdString(channel.hardwareAssociation))
                       .arg(QString::fromStdString(channel.load1Sensor))
                       .arg(QString::fromStdString(channel.load2Sensor))
                       .arg(QString::fromStdString(channel.positionSensor))
                       .arg(QString::fromStdString(channel.controlActuator))
                       .arg(QString::fromStdString(channel.notes));
            }
        }
    }

    file.close();
    qDebug() << QString("控制通道数据导出到CSV成功: %1").arg(filePath);
    return true;
}

// 🚫 已注释：独立JSON导出功能已废弃
//bool CtrlChanDataManager::exportToJSON(const QString& filePath) const {
//    QMutexLocker locker(&dataMutex_);
//
//    QJsonObject rootObj;
//    rootObj["exportTime"] = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
//    rootObj["version"] = "1.0.0";
//    rootObj["description"] = "控制通道配置数据";
//
//    QJsonArray groupsArray;
//    for (auto it = groupStorage_.begin(); it != groupStorage_.end(); ++it) {
//        const UI::ControlChannelGroup& group = it.value();
//
//        QJsonObject groupObj;
//        groupObj["groupId"] = group.groupId;
//        groupObj["groupName"] = QString::fromStdString(group.groupName);
//        groupObj["groupType"] = QString::fromStdString(group.groupType);
//        groupObj["createTime"] = QString::fromStdString(group.createTime);
//        groupObj["groupNotes"] = QString::fromStdString(group.groupNotes);
//
//        QJsonArray channelsArray;
//        for (const auto& channel : group.channels) {
//            QJsonObject channelObj;
//            channelObj["channelId"] = QString::fromStdString(channel.channelId);
//            channelObj["channelName"] = QString::fromStdString(channel.channelName);
//            channelObj["hardwareAssociation"] = QString::fromStdString(channel.hardwareAssociation);
//            channelObj["load1Sensor"] = QString::fromStdString(channel.load1Sensor);
//            channelObj["load2Sensor"] = QString::fromStdString(channel.load2Sensor);
//            channelObj["positionSensor"] = QString::fromStdString(channel.positionSensor);
//            channelObj["controlActuator"] = QString::fromStdString(channel.controlActuator);
//            channelObj["notes"] = QString::fromStdString(channel.notes);
//
//            channelsArray.append(channelObj);
//        }
//        groupObj["channels"] = channelsArray;
//
//        groupsArray.append(groupObj);
//    }
//    rootObj["controlChannelGroups"] = groupsArray;
//
//    QJsonDocument doc(rootObj);
//
//    QFile file(filePath);
//    if (!file.open(QIODevice::WriteOnly)) {
//        qDebug() << QString("无法创建JSON文件: %1").arg(filePath);
//        return false;
//    }
//
//    file.write(doc.toJson());
//    file.close();
//
//    qDebug() << QString("控制通道数据导出到JSON成功: %1").arg(filePath);
//    return true;
//}

bool CtrlChanDataManager::importFromJSON(const QString& filePath) {
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        qDebug() << QString("无法打开JSON文件: %1").arg(filePath);
        return false;
    }

    QByteArray data = file.readAll();
    file.close();

    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(data, &error);
    if (error.error != QJsonParseError::NoError) {
        qDebug() << QString("JSON解析失败: %1").arg(error.errorString());
        return false;
    }

    QJsonObject rootObj = doc.object();
    QJsonArray groupsArray = rootObj["controlChannelGroups"].toArray();

    QMutexLocker locker(&dataMutex_);

    // 清空现有数据
    groupStorage_.clear();
    nextGroupId_ = 1;

    // 导入数据
    for (const QJsonValue& groupValue : groupsArray) {
        QJsonObject groupObj = groupValue.toObject();

        UI::ControlChannelGroup group;
        group.groupId = groupObj["groupId"].toInt();
        group.groupName = groupObj["groupName"].toString().toStdString();
        group.groupType = groupObj["groupType"].toString().toStdString();
        group.createTime = groupObj["createTime"].toString().toStdString();
        group.groupNotes = groupObj["groupNotes"].toString().toStdString();

        QJsonArray channelsArray = groupObj["channels"].toArray();
        for (const QJsonValue& channelValue : channelsArray) {
            QJsonObject channelObj = channelValue.toObject();

            UI::ControlChannelParams channel;
            channel.channelId = channelObj["channelId"].toString().toStdString();
            channel.channelName = channelObj["channelName"].toString().toStdString();
            channel.hardwareAssociation = channelObj["hardwareAssociation"].toString().toStdString();
            channel.load1Sensor = channelObj["load1Sensor"].toString().toStdString();
            channel.load2Sensor = channelObj["load2Sensor"].toString().toStdString();
            channel.positionSensor = channelObj["positionSensor"].toString().toStdString();
            channel.controlActuator = channelObj["controlActuator"].toString().toStdString();
            channel.notes = channelObj["notes"].toString().toStdString();

            group.channels.push_back(channel);
        }

        groupStorage_[group.groupId] = group;

        // 更新下一个组ID
        if (group.groupId >= nextGroupId_) {
            nextGroupId_ = group.groupId + 1;
        }
    }

    qDebug() << QString("从JSON导入控制通道数据成功: %1 个组，%2 个通道")
                .arg(groupStorage_.size()).arg(getTotalChannelCount());
    return true;
}

// ============================================================================
// 私有辅助方法
// ============================================================================

int CtrlChanDataManager::generateNextGroupId() {
    return nextGroupId_++;
}

void CtrlChanDataManager::sortGroupsByCreateTime(QList<UI::ControlChannelGroup>& groups) const {
    std::sort(groups.begin(), groups.end(),
              [](const UI::ControlChannelGroup& a, const UI::ControlChannelGroup& b) {
                  return a.createTime < b.createTime;
              });
}

bool CtrlChanDataManager::validateControlChannelGroup(const UI::ControlChannelGroup& group) const {
    // 检查组名称是否为空
    if (group.groupName.empty()) {
        qDebug() << "控制通道组名称不能为空";
        return false;
    }

    // 检查组类型是否为空
    if (group.groupType.empty()) {
        qDebug() << "控制通道组类型不能为空";
        return false;
    }

    // 验证组内每个通道
    for (const auto& channel : group.channels) {
        if (!validateControlChannelParams(channel)) {
            return false;
        }
    }

    return true;
}

bool CtrlChanDataManager::validateControlChannelParams(const UI::ControlChannelParams& channel) const {
    // 检查通道ID是否为空
    if (channel.channelId.empty()) {
        qDebug() << "控制通道ID不能为空";
        return false;
    }

    // 🔧 修复：允许通道名称为空，在这种情况下会使用默认的channelId作为显示名称
    // 通道名称可以为空，系统会自动使用channelId作为默认显示名称

    return true;
}
