@echo off
echo Simple Rebuild - TreeLineStyle Fix
echo ==================================

REM Kill existing process
taskkill /f /im SiteResConfig.exe 2>nul

REM Go to build directory
cd build-SiteResConfig_Simple-Desktop_Qt_5_14_2_MinGW_32_bit-Debug

REM Try to rebuild just the changed files
echo Rebuilding TreeLineStyle...
if exist "debug\treelinestyle.o" (
    del "debug\treelinestyle.o"
    echo Deleted old object file
)

if exist "debug\MainWindow_Qt_Simple.o" (
    del "debug\MainWindow_Qt_Simple.o"
    echo Deleted old MainWindow object file
)

REM Try to rebuild
echo Attempting incremental build...
mingw32-make 2>nul
if %ERRORLEVEL% EQU 0 (
    echo Build successful!
    echo Starting application...
    cd debug
    start SiteResConfig.exe
    cd ..
) else (
    echo Incremental build failed, trying full rebuild...
    mingw32-make clean 2>nul
    mingw32-make 2>nul
    if %ERRORLEVEL% EQU 0 (
        echo Full rebuild successful!
        echo Starting application...
        cd debug
        start SiteResConfig.exe
        cd ..
    ) else (
        echo Build failed completely!
        echo Please check Qt environment and dependencies.
    )
)

cd ..
echo Done.
pause
