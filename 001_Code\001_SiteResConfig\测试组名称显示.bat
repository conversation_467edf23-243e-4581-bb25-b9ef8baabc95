@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🔍 测试传感器组名称显示功能
echo ========================================
echo.

echo 📋 测试目标:
echo 验证传感器组名称只在每组第一行显示，其他行为空
echo.

echo 🔧 修复内容:
echo.
echo 📁 XLSDataExporter.cpp:
echo   addSensorGroupDetailToExcel() 方法:
echo   - 修改了组名称写入逻辑
echo   - 使用明确的 if-else 语句
echo   - 添加了调试信息输出
echo.
echo 修复前:
echo   worksheet-^>write(row, 2, (i == 0) ? group.groupName : QString(), currentFormat);
echo.
echo 修复后:
echo   if (i == 0) {
echo       worksheet-^>write(row, 2, group.groupName, currentFormat);
echo   } else {
echo       worksheet-^>write(row, 2, QString(""), currentFormat);
echo   }
echo.

echo 📊 预期Excel格式:
echo.
echo 组序号 ^| 传感器组名称    ^| 传感器序列号 ^| 传感器类型 ^| ...
echo ------|---------------|------------|----------|----
echo 1     ^| 载荷_传感器组   ^| SEN001     ^| 载荷传感器 ^| ...
echo 1     ^|               ^| SEN002     ^| 载荷传感器 ^| ...  ← 空白
echo 1     ^|               ^| SEN003     ^| 载荷传感器 ^| ...  ← 空白
echo 2     ^| 位置_传感器组   ^| SEN004     ^| 位置传感器 ^| ...
echo 2     ^|               ^| SEN005     ^| 位置传感器 ^| ...  ← 空白
echo 3     ^| 温度_传感器组   ^| SEN006     ^| 温度传感器 ^| ...
echo.

echo 🚀 测试步骤:
echo.
echo 1. 重新编译应用程序
echo    - 确保修复的代码生效
echo.
echo 2. 启动应用程序
echo    - 程序会自动创建测试数据
echo    - 检查日志: "已创建测试传感器组数据，用于验证组名称显示功能"
echo.
echo 3. 导出传感器详细配置
echo    - 点击: 数据导出 -^> 导出传感器详细信息到Excel
echo    - 选择保存位置
echo.
echo 4. 检查调试输出
echo    - 在控制台查看调试信息:
echo      "组ID: 1, 传感器索引: 0, 组名称: 载荷_传感器组, 显示组名称: 是"
echo      "组ID: 1, 传感器索引: 1, 组名称: 载荷_传感器组, 显示组名称: 否"
echo      "组ID: 1, 传感器索引: 2, 组名称: 载荷_传感器组, 显示组名称: 否"
echo.
echo 5. 验证Excel文件
echo    - 打开生成的Excel文件
echo    - 检查"传感器组名称"列
echo    - 确认只有每组第一行有组名称
echo.

echo ✅ 预期结果:
echo.
echo 1. 调试输出正确:
echo    - 每组第一个传感器: "显示组名称: 是"
echo    - 其他传感器: "显示组名称: 否"
echo.
echo 2. Excel文件正确:
echo    - 载荷_传感器组: 只在SEN001行显示，SEN002和SEN003行为空
echo    - 位置_传感器组: 只在SEN004行显示，SEN005行为空
echo    - 温度_传感器组: 只在SEN006行显示
echo.
echo 3. 视觉效果:
echo    - 组结构清晰可见
echo    - 每组传感器明确分组
echo    - 组名称不重复显示
echo.

echo 🔍 验证要点:
echo.
echo 1. 组名称显示规则:
echo    - 每组第一行: 显示完整组名称
echo    - 同组其他行: 单元格为空（不是空字符串显示）
echo.
echo 2. 格式一致性:
echo    - 第一行使用组格式（浅蓝色背景，粗体）
echo    - 其他行使用普通数据格式
echo.
echo 3. 数据完整性:
echo    - 组序号在同组所有行都显示
echo    - 传感器信息完整准确
echo    - 表头33列正确
echo.

echo ⚠️ 如果仍有问题:
echo.
echo 可能原因1: QString() 在Excel中显示为文本
echo 解决方案: 使用空字符串 QString("")
echo.
echo 可能原因2: Excel格式问题
echo 解决方案: 检查单元格格式设置
echo.
echo 可能原因3: 逻辑判断问题
echo 解决方案: 检查调试输出确认逻辑正确
echo.

echo 💡 调试技巧:
echo - 查看控制台调试输出
echo - 检查Excel单元格内容（不是显示）
echo - 验证组内传感器数量和索引
echo.

echo 🎯 测试完成后:
echo 如果功能正常，可以注释掉测试数据创建代码
echo 如果仍有问题，请检查调试输出并进一步修复
echo.

pause
