/**
 * @file ConfigManager.h
 * @brief 配置管理模块 - 基于现有Config::ConfigManager
 * @details 封装现有的Config::ConfigManager，提供统一的配置管理接口
 * <AUTHOR> Assistant
 * @date 2025-01-27
 * @version 3.4.0
 */

#ifndef CONFIGMANAGER_H
#define CONFIGMANAGER_H

#include <QObject>
#include <QString>
#include <QStringList>
#include <QVariant>
#include <QVariantMap>

// 前向声明
namespace Config {
    class ConfigManager;
}

/**
 * @brief 配置管理器类 - 基于现有Config::ConfigManager
 * @details 封装现有的Config::ConfigManager，提供统一的配置管理接口
 */
class ConfigManager : public QObject {
    Q_OBJECT
    
public:
    explicit ConfigManager(QObject* parent = nullptr);
    ~ConfigManager();
    
    // 设置现有配置管理器 (不创建新的)
    void setExistingConfigManager(Config::ConfigManager* configManager);
    
    // 基本配置管理
    QVariant getValue(const QString& key, const QVariant& defaultValue = QVariant());
    void setValue(const QString& key, const QVariant& value);
    
    // 最近项目管理
    void addRecentProject(const QString& projectName, const QString& projectPath);
    QStringList getRecentProjects() const;
    
    // 配置保存和加载
    bool loadConfig(const QString& configPath = QString());
    bool saveConfig(const QString& configPath = QString());
    
signals:
    void configChanged(const QString& key, const QVariant& value);
    
private:
    // 引用现有配置管理器 (不创建新的)
    Config::ConfigManager* existingConfigManager_;
    QVariantMap config_;
    QStringList recentProjects_;
};

#endif // CONFIGMANAGER_H 