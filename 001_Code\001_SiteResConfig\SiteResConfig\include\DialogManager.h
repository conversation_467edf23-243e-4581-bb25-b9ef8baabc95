#ifndef DIALOGMANAGER_H
#define DIALOGMANAGER_H

#include <QObject>
#include <QDialog>
#include <QMap>
#include <QString>
#include <memory>

// 引用现有对话框类
class SensorDialog_1_2;
class ActuatorDialog_1_2;

// 包含完整的数据结构体定义
#include "SensorDialog_1_2.h"
#include "ActuatorDialog_1_2.h"

/**
 * @brief 对话框管理器 - 基于现有对话框组件
 * 
 * DialogManager基于现有的SensorDialog_1_2和ActuatorDialog_1_2提供统一的对话框管理接口。
 * 
 * 设计特点：
 * - 100%基于现有对话框 (SensorDialog_1_2, ActuatorDialog_1_2)
 * - 使用现有数据结构体 (UI::SensorParams_1_2, UI::ActuatorParams_1_2)
 * - 提供对话框状态管理
 * - 不创建任何新的UI组件
 */
class DialogManager : public QObject
{
    Q_OBJECT

public:
    explicit DialogManager(QObject* parent = nullptr);
    ~DialogManager();

    // 使用现有SensorDialog_1_2
    bool showSensorDialog(const QString& serialNumber = QString());
    bool showSensorDialog(const UI::SensorParams_1_2& params);
    UI::SensorParams_1_2 getSensorParams() const;

    // 使用现有ActuatorDialog_1_2
    bool showActuatorDialog(const QString& serialNumber = QString());
    bool showActuatorDialog(const UI::ActuatorParams_1_2& params);
    UI::ActuatorParams_1_2 getActuatorParams() const;

    // 对话框状态管理
    bool isDialogOpen(const QString& dialogType) const;
    void closeAllDialogs();

signals:
    void dialogAccepted(const QString& dialogType, const QString& identifier);
    void dialogRejected(const QString& dialogType, const QString& identifier);

private slots:
    void onDialogAccepted();
    void onDialogRejected();

private:
    QMap<QString, QDialog*> openDialogs_;
    UI::SensorParams_1_2 lastSensorParams_;
    UI::ActuatorParams_1_2 lastActuatorParams_;

    void registerDialog(const QString& type, QDialog* dialog);
    void unregisterDialog(const QString& type);
};

#endif // DIALOGMANAGER_H 