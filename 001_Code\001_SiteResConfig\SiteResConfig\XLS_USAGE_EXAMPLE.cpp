/**
 * @file XLS_USAGE_EXAMPLE.cpp
 * @brief XLS导出功能使用示例
 * @details 演示如何在MainWindow中集成和使用XLS导出功能
 * <AUTHOR> Assistant
 * @date 2025-08-13
 * @version 1.0.0
 */

// 在MainWindow_Qt_Simple.h中添加的头文件包含
/*
#include "XLSDataExporter.h"
#include "DataExporterFactory.h"
*/

// 在MainWindow_Qt_Simple.h中添加的成员变量
/*
private:
    std::unique_ptr<XLSDataExporter> xlsExporter_;
*/

// 在MainWindow_Qt_Simple.cpp构造函数中的初始化代码
/*
MainWindow::MainWindow(QWidget *parent) : QMainWindow(parent), ui(new Ui::MainWindow) {
    ui->setupUi(this);
    
    // 初始化XLS导出器
    xlsExporter_ = std::make_unique<XLSDataExporter>(sensorDataManager_);
    xlsExporter_->setIncludeHeader(true);
    xlsExporter_->setAutoFitColumns(true);
    xlsExporter_->setUseTableStyle(true);
    xlsExporter_->setWorksheetName(u8"硬件配置");
    
    // 其他初始化代码...
}
*/

// 示例方法1：导出硬件树到Excel
/*
void MainWindow::exportHardwareTreeToExcel() {
    QString fileName = QFileDialog::getSaveFileName(
        this,
        u8"导出硬件配置到Excel",
        QString(u8"硬件配置_%1.xlsx").arg(QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss")),
        u8"Excel文件 (*.xlsx);;所有文件 (*.*)"
    );
    
    if (fileName.isEmpty()) {
        return;
    }
    
    if (xlsExporter_->exportHardwareTree(ui->hardwareTreeWidget, fileName)) {
        QMessageBox::information(this, u8"导出成功", 
            QString(u8"硬件配置已成功导出到:\n%1").arg(fileName));
    } else {
        QMessageBox::critical(this, u8"导出失败", 
            QString(u8"导出失败:\n%1").arg(xlsExporter_->getLastError()));
    }
}
*/

// 示例方法2：导出完整项目到Excel
/*
void MainWindow::exportCompleteProjectToExcel() {
    QString fileName = QFileDialog::getSaveFileName(
        this,
        u8"导出完整项目到Excel",
        QString(u8"完整项目_%1.xlsx").arg(QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss")),
        u8"Excel文件 (*.xlsx);;所有文件 (*.*)"
    );
    
    if (fileName.isEmpty()) {
        return;
    }
    
    if (xlsExporter_->exportCompleteProject(ui->hardwareTreeWidget, fileName)) {
        QMessageBox::information(this, u8"导出成功", 
            QString(u8"完整项目已成功导出到:\n%1").arg(fileName));
    } else {
        QMessageBox::critical(this, u8"导出失败", 
            QString(u8"导出失败:\n%1").arg(xlsExporter_->getLastError()));
    }
}
*/

// 示例方法3：从Excel导入硬件配置
/*
void MainWindow::importHardwareConfigFromExcel() {
    QString fileName = QFileDialog::getOpenFileName(
        this,
        u8"从Excel导入硬件配置",
        "",
        u8"Excel文件 (*.xlsx *.xls);;所有文件 (*.*)"
    );
    
    if (fileName.isEmpty()) {
        return;
    }
    
    // 检查是否有未保存的数据
    if (HasInterfaceData()) {
        int ret = QMessageBox::question(this, u8"确认导入", 
            u8"当前界面有未保存的数据，导入将清空现有数据。\n是否继续？",
            QMessageBox::Yes | QMessageBox::No, QMessageBox::No);
        
        if (ret != QMessageBox::Yes) {
            return;
        }
    }
    
    if (xlsExporter_->importToHardwareTree(fileName, ui->hardwareTreeWidget)) {
        QMessageBox::information(this, u8"导入成功", 
            QString(u8"硬件配置已成功从Excel文件导入:\n%1").arg(fileName));
        
        // 更新界面状态
        UpdateTreeDisplay();
        LogMessage(QString(u8"从Excel文件导入硬件配置: %1").arg(QFileInfo(fileName).fileName()));
    } else {
        QMessageBox::critical(this, u8"导入失败", 
            QString(u8"导入失败:\n%1").arg(xlsExporter_->getLastError()));
    }
}
*/

// 示例方法4：使用工厂模式创建XLS导出器
/*
void MainWindow::exportUsingFactory() {
    QString fileName = QFileDialog::getSaveFileName(
        this,
        u8"选择导出格式",
        QString(u8"项目数据_%1").arg(QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss")),
        DataExporterFactory::getFileFilter()
    );
    
    if (fileName.isEmpty()) {
        return;
    }
    
    // 使用工厂创建对应格式的导出器
    auto exporter = DataExporterFactory::createExporterByFilePath(fileName, sensorDataManager_);
    if (!exporter) {
        QMessageBox::critical(this, u8"错误", u8"不支持的文件格式");
        return;
    }
    
    if (exporter->exportCompleteProject(ui->hardwareTreeWidget, fileName)) {
        QMessageBox::information(this, u8"导出成功", 
            QString(u8"项目数据已成功导出到:\n%1").arg(fileName));
    } else {
        QMessageBox::critical(this, u8"导出失败", 
            QString(u8"导出失败:\n%1").arg(exporter->getLastError()));
    }
}
*/

// 示例方法5：批量导出多种格式
/*
void MainWindow::exportToMultipleFormats() {
    QString baseDir = QFileDialog::getExistingDirectory(
        this,
        u8"选择导出目录",
        ""
    );
    
    if (baseDir.isEmpty()) {
        return;
    }
    
    QString timestamp = QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss");
    QString baseName = QString(u8"项目数据_%1").arg(timestamp);
    
    // 导出到CSV
    QString csvPath = QDir(baseDir).filePath(baseName + ".csv");
    auto csvExporter = DataExporterFactory::createExporter(DataExporterFactory::ExportFormat::CSV, sensorDataManager_);
    bool csvSuccess = csvExporter->exportCompleteProject(ui->hardwareTreeWidget, csvPath);
    
    // 导出到JSON
    QString jsonPath = QDir(baseDir).filePath(baseName + ".json");
    auto jsonExporter = DataExporterFactory::createExporter(DataExporterFactory::ExportFormat::JSON, sensorDataManager_);
    bool jsonSuccess = jsonExporter->exportCompleteProject(ui->hardwareTreeWidget, jsonPath);
    
    // 导出到Excel
    QString xlsPath = QDir(baseDir).filePath(baseName + ".xlsx");
    auto xlsExporter = DataExporterFactory::createExporter(DataExporterFactory::ExportFormat::XLS, sensorDataManager_);
    bool xlsSuccess = xlsExporter->exportCompleteProject(ui->hardwareTreeWidget, xlsPath);
    
    // 显示结果
    QStringList results;
    if (csvSuccess) results << QString(u8"✅ CSV: %1").arg(csvPath);
    else results << QString(u8"❌ CSV导出失败: %1").arg(csvExporter->getLastError());
    
    if (jsonSuccess) results << QString(u8"✅ JSON: %1").arg(jsonPath);
    else results << QString(u8"❌ JSON导出失败: %1").arg(jsonExporter->getLastError());
    
    if (xlsSuccess) results << QString(u8"✅ Excel: %1").arg(xlsPath);
    else results << QString(u8"❌ Excel导出失败: %1").arg(xlsExporter->getLastError());
    
    QMessageBox::information(this, u8"批量导出结果", results.join("\n"));
}
*/
