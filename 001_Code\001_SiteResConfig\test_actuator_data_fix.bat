@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🔧 作动器数据完整性修复验证程序
echo ========================================
echo.
echo 📋 修复内容:
echo   1. ✅ 修复OnCreateActuator中缺少saveActuatorDetailedParams调用
echo   2. ✅ 修复getAllActuatorGroups优先从ActuatorDataManager获取完整参数
echo   3. ✅ 添加调试日志以跟踪数据获取过程
echo   4. ✅ 确保作动器界面数据完整保存到XLSX文件
echo.
echo 🎯 修复目标:
echo   - 解决作动器界面值没有全部存进XLSX文件的问题
echo   - 确保所有17列作动器参数都正确导出
echo   - 提供完整的数据流跟踪和调试信息
echo.
echo 🔍 修复原理:
echo.
echo 1. 数据保存修复:
echo    - OnCreateActuator现在会调用saveActuatorDetailedParams
echo    - 作动器创建时自动保存到ActuatorDataManager
echo    - 数据持久化到项目的actuatorDetailedParams中
echo.
echo 2. 数据获取修复:
echo    - getAllActuatorGroups优先从ActuatorDataManager获取
echo    - 如果数据管理器中有完整参数，使用完整参数
echo    - 如果没有，则从tooltip解析（向后兼容）
echo.
echo 3. 调试信息增强:
echo    - 添加数据获取来源的日志记录
echo    - 统计获取的作动器组和作动器数量
echo    - 便于问题诊断和验证修复效果
echo.

REM 检查项目目录
if not exist "SiteResConfig" (
    echo ❌ 错误: 找不到SiteResConfig项目目录
    echo 请确保在正确的项目根目录下运行此脚本
    pause
    exit /b 1
)

echo 进入项目目录...
cd SiteResConfig

echo.
echo 🔧 开始编译验证...
echo.

REM 清理旧的构建文件
if exist "Makefile*" (
    echo 清理旧的构建文件...
    del Makefile* >nul 2>&1
)

REM 生成Makefile
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo ❌ qmake失败！
    pause
    exit /b 1
)

REM 编译项目
echo.
echo 开始编译...
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ❌ 编译失败！
    echo.
    echo 可能的原因:
    echo   1. 代码语法错误
    echo   2. 缺少依赖库
    echo   3. 编译器配置问题
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ 编译成功！
echo.

REM 检查可执行文件
if exist "debug\SiteResConfig.exe" (
    echo 🎯 测试说明:
    echo.
    echo 1. 启动程序后，创建一个作动器组
    echo 2. 在组中创建一个作动器，填写完整参数
    echo 3. 保存项目为XLSX格式
    echo 4. 检查XLSX文件中的"作动器详细配置"工作表
    echo 5. 验证所有17列数据是否完整
    echo.
    echo 🔍 关键验证点:
    echo   - Unit类型和Unit值是否正确
    echo   - 行程、位移、拉伸面积、压缩面积是否有值
    echo   - 极性、Deliver、频率、输出倍数、平衡值是否正确
    echo   - 序列号、类型、备注信息是否完整
    echo.
    echo 📊 日志监控:
    echo   - 查看日志中"从ActuatorDataManager获取作动器详细参数"信息
    echo   - 查看"从硬件树获取作动器组"的统计信息
    echo   - 确认数据来源和数量正确
    echo.
    echo 启动程序进行测试...
    start debug\SiteResConfig.exe
) else (
    echo ❌ 找不到可执行文件！
    echo 编译可能未完全成功
)

echo.
echo ========================================
echo ✅ 作动器数据完整性修复验证完成！
echo ========================================
echo.
echo 📋 修复总结:
echo   1. ✅ 作动器创建时数据保存修复
echo   2. ✅ 作动器数据获取优先级修复  
echo   3. ✅ 调试日志和统计信息增强
echo   4. ✅ XLSX导出数据完整性保证
echo.
echo 🎯 预期效果:
echo   - 作动器界面的所有参数都会保存到XLSX文件
echo   - 17列作动器详细信息完整导出
echo   - 数据来源可追踪，便于问题诊断
echo.
echo 🚀 使用建议:
echo   1. 创建作动器时填写完整参数
echo   2. 保存项目前检查日志信息
echo   3. 导出XLSX后验证数据完整性
echo   4. 如有问题，查看日志中的调试信息
echo.

pause
