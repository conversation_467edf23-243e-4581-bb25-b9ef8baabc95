#include "BasicInfoWidget.h"
#include "ui_BasicInfoWidget.h"
#include <QHeaderView>
#include <QTableWidgetItem>
#include <QFrame>
#include <QSpacerItem>
#include <QApplication>
#include <QFont>
#include <QColor>
#include <QSize>
#include <QDebug> // Added for qDebug
#include <QTimer>

// 定义基本信息表格列头
const QStringList BasicInfoWidget::BASIC_INFO_HEADERS = {
    "通道名称", "硬件关联选择", "载荷1传感器选择", "载荷2传感器选择", 
    "位置传感器选择", "控制作动器选择", "下位机ID", "站点ID", "使能状态",
    "控制作动器极性", "载荷1传感器极性", "载荷2传感器极性", "位置传感器极性"
};

BasicInfoWidget::BasicInfoWidget(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::BasicInfoWidget)
    , m_basicInfoTable(nullptr)
{
    qDebug() << "🔧 [BasicInfoWidget] 开始创建BasicInfoWidget";
    
    try {
        // 设置UI
        ui->setupUi(this);
        qDebug() << "✅ [BasicInfoWidget] UI设置完成";
        
        // 初始化UI
        initUI();
        qDebug() << "✅ [BasicInfoWidget] UI初始化完成";
        
        // 设置连接
        setupConnections();
        qDebug() << "✅ [BasicInfoWidget] 连接设置完成";
        
        // 应用样式
        applyStyles();
        qDebug() << "✅ [BasicInfoWidget] 样式应用完成";
        
        qDebug() << "🎉 [BasicInfoWidget] 构造函数完成";
        
    } catch (const std::exception& e) {
        qWarning() << "❌ [BasicInfoWidget] 构造函数中发生异常:" << e.what();
    } catch (...) {
        qWarning() << "❌ [BasicInfoWidget] 构造函数中发生未知异常";
    }
}

BasicInfoWidget::~BasicInfoWidget()
{
    delete ui;
}

void BasicInfoWidget::initUI()
{
    qDebug() << "🔧 [BasicInfoWidget] 开始初始化UI界面";
    
    try {
        // 🆕 修复：确保UI对象有效
        if (!ui) {
            qWarning() << "❌ [BasicInfoWidget] UI对象为空，无法初始化";
            return;
        }
        
        // 从UI文件加载控件
        m_basicInfoTable = ui->basicInfoTable;
        qDebug() << "   📊 基本信息表格控件:" << (m_basicInfoTable ? "已找到" : "未找到");
        
        // 🆕 新增：验证表格控件是否有效
        if (!m_basicInfoTable) {
            qWarning() << "❌ [BasicInfoWidget] 基本信息表格控件为空，尝试查找替代方案";
            
            // 尝试通过对象名称查找
            QList<QTableWidget*> tables = this->findChildren<QTableWidget*>();
            if (!tables.isEmpty()) {
                m_basicInfoTable = tables.first();
                qDebug() << "✅ [BasicInfoWidget] 通过findChildren找到表格控件";
            } else {
                qWarning() << "❌ [BasicInfoWidget] 无法找到任何表格控件";
                return;
            }
        }
        
        // 初始化基本信息表格
        qDebug() << "🔄 [BasicInfoWidget] 准备创建基本信息表格";
        createBasicInfoTable();
        qDebug() << "✅ [BasicInfoWidget] 基本信息表格创建完成";
        
        // 设置初始状态 - 修复：传入有意义的默认信息而不是空NodeInfo
        qDebug() << "🔄 [BasicInfoWidget] 准备设置默认状态";
        NodeInfo defaultInfo;
        defaultInfo.nodeName = "未选择节点";
        defaultInfo.nodeType = "请选择树形控件中的节点";
        defaultInfo.status = NodeStatus::Unknown;
        
        // 🆕 新增：验证UI控件是否有效
        if (ui && ui->lblSummaryInfo) {
            updateSummaryInfo(defaultInfo);
            qDebug() << "✅ [BasicInfoWidget] 默认状态设置完成";
        } else {
            qWarning() << "⚠️ [BasicInfoWidget] 汇总信息标签控件无效，跳过默认状态设置";
        }
        
        qDebug() << "🎉 [BasicInfoWidget] UI界面初始化完成";
        
    } catch (const std::exception& e) {
        qWarning() << "❌ [BasicInfoWidget] 初始化UI界面时发生异常:" << e.what();
    } catch (...) {
        qWarning() << "❌ [BasicInfoWidget] 初始化UI界面时发生未知异常";
    }
}

void BasicInfoWidget::setupConnections()
{
    // 连接信号和槽
    // 这里可以添加控件之间的信号连接
}

void BasicInfoWidget::applyStyles()
{
    // 应用样式表
    setStyleSheet("QGroupBox { font-weight: bold; border: 2px solid #bdc3c7; border-radius: 5px; margin-top: 1ex; }"
                  "QGroupBox::title { subcontrol-origin: margin; left: 10px; padding: 0 5px 0 5px; }");
}



void BasicInfoWidget::createBasicInfoTable()
{
    qDebug() << "🔧 [BasicInfoWidget] 开始创建基本信息表格";
    
    try {
        if (!m_basicInfoTable) {
            qWarning() << "❌ [BasicInfoWidget] 错误：基本信息表格控件为空！";
            return;
        }
        
        qDebug() << "   📊 表格控件状态:" << (m_basicInfoTable ? "有效" : "无效");
        qDebug() << "   📏 列头数量:" << BASIC_INFO_HEADERS.size();
        
        // 🆕 新增：验证表格控件是否仍然有效
        if (!m_basicInfoTable->parent()) {
            qWarning() << "❌ [BasicInfoWidget] 表格控件无父窗口，可能已被销毁";
            return;
        }
        
        // 设置表格属性
        m_basicInfoTable->setColumnCount(BASIC_INFO_HEADERS.size());
        m_basicInfoTable->setRowCount(1);
        m_basicInfoTable->setHorizontalHeaderLabels(BASIC_INFO_HEADERS);
        qDebug() << "   ✅ 表格基本属性设置完成";
        
        // 🆕 新增：验证表格属性设置是否成功
        if (m_basicInfoTable->columnCount() != BASIC_INFO_HEADERS.size()) {
            qWarning() << "⚠️ [BasicInfoWidget] 列数设置失败！期望:" << BASIC_INFO_HEADERS.size() << "实际:" << m_basicInfoTable->columnCount();
        }
        
        if (m_basicInfoTable->rowCount() != 1) {
            qWarning() << "⚠️ [BasicInfoWidget] 行数设置失败！期望:1 实际:" << m_basicInfoTable->rowCount();
        }
        
        // 垂直表头标签将在updateBasicInfoTable中动态设置
        
        // 设置表格样式
        qDebug() << "🎨 [BasicInfoWidget] 开始设置表格样式";
        
        m_basicInfoTable->setAlternatingRowColors(true);
        
        // 🆕 新增：验证样式设置是否成功
        if (!m_basicInfoTable->alternatingRowColors()) {
            qWarning() << "⚠️ [BasicInfoWidget] 交替行颜色设置失败";
        }
        
        // 设置表格选择模式
        m_basicInfoTable->setSelectionBehavior(QAbstractItemView::SelectRows);
        m_basicInfoTable->setSelectionMode(QAbstractItemView::SingleSelection);
        
        // 🆕 新增：验证选择模式设置是否成功
        if (m_basicInfoTable->selectionBehavior() != QAbstractItemView::SelectRows) {
            qWarning() << "⚠️ [BasicInfoWidget] 选择行为设置失败";
        }
        
        if (m_basicInfoTable->selectionMode() != QAbstractItemView::SingleSelection) {
            qWarning() << "⚠️ [BasicInfoWidget] 选择模式设置失败";
        }
        
        // 设置表格大小策略
        m_basicInfoTable->setSizeAdjustPolicy(QAbstractScrollArea::AdjustToContents);
        
        // 调整列宽
        m_basicInfoTable->resizeColumnsToContents();
        
        qDebug() << "✅ [BasicInfoWidget] 基本信息表格创建完成";
        qDebug() << "   📊 最终表格状态:";
        qDebug() << "     - 列数:" << m_basicInfoTable->columnCount();
        qDebug() << "     - 行数:" << m_basicInfoTable->rowCount();
        qDebug() << "     - 交替行颜色:" << (m_basicInfoTable->alternatingRowColors() ? "启用" : "禁用");
        qDebug() << "     - 选择行为:" << (m_basicInfoTable->selectionBehavior() == QAbstractItemView::SelectRows ? "行选择" : "其他");
        qDebug() << "     - 选择模式:" << (m_basicInfoTable->selectionMode() == QAbstractItemView::SingleSelection ? "单选" : "其他");
        
    } catch (const std::exception& e) {
        qWarning() << "❌ [BasicInfoWidget] 创建基本信息表格时发生异常:" << e.what();
    } catch (...) {
        qWarning() << "❌ [BasicInfoWidget] 创建基本信息表格时发生未知异常";
    }
}

void BasicInfoWidget::setNodeInfo(const NodeInfo& nodeInfo)
{
    qDebug() << "🔍 [BasicInfoWidget] 开始设置节点信息";
    
    try {
        // 🆕 新增：验证NodeInfo对象的有效性
        if (nodeInfo.nodeName.isEmpty() && nodeInfo.nodeType.isEmpty()) {
            qDebug() << "ℹ️ [BasicInfoWidget] 节点信息为空，跳过设置";
            return;
        }
        
        qDebug() << "   📋 节点名称:" << nodeInfo.nodeName;
        qDebug() << "   🏷️  节点类型:" << nodeInfo.nodeType;
        qDebug() << "   📊 节点状态:" << (nodeInfo.status == NodeStatus::Online ? "在线" : 
                                          nodeInfo.status == NodeStatus::Offline ? "离线" : 
                                          nodeInfo.status == NodeStatus::Error ? "错误" : "未知");
        qDebug() << "   🔗 子节点数量:" << nodeInfo.subNodes.size();
        
        // 🆕 新增：安全的子节点信息打印
        try {
            for (int i = 0; i < nodeInfo.subNodes.size(); ++i) {
                const SubNodeInfo& subNode = nodeInfo.subNodes[i];
                qDebug() << "   📍 子节点" << (i+1) << ":" << subNode.name;
                qDebug() << "      🏷️  类型:" << subNode.type;
                qDebug() << "      🔧 设备:" << subNode.deviceName;
                qDebug() << "      🆔 设备ID:" << subNode.deviceId;
                qDebug() << "      🔌 连接状态:" << (subNode.isConnected ? "已连接" : "未连接");
                
                // 🆕 新增：安全的属性信息打印
                try {
                    const QMap<QString, QVariant>& properties = subNode.properties;
                    if (!properties.isEmpty()) {
                        qDebug() << "      📋 属性数量:" << properties.size();
                        for (auto it = properties.begin(); it != properties.end(); ++it) {
                            qDebug() << "         *" << it.key() << ":" << it.value().toString();
                        }
                    }
                } catch (const std::exception& e) {
                    qWarning() << "❌ [BasicInfoWidget] 打印子节点属性时发生异常:" << e.what();
                } catch (...) {
                    qWarning() << "❌ [BasicInfoWidget] 打印子节点属性时发生未知异常";
                }
            }
        } catch (const std::exception& e) {
            qWarning() << "❌ [BasicInfoWidget] 打印子节点信息时发生异常:" << e.what();
        } catch (...) {
            qWarning() << "❌ [BasicInfoWidget] 打印子节点信息时发生未知异常";
        }
        
        // 🆕 新增：安全的成员变量赋值
        try {
            m_currentNodeInfo = nodeInfo;
            qDebug() << "✅ [BasicInfoWidget] 节点信息已保存到成员变量";
        } catch (const std::exception& e) {
            qWarning() << "❌ [BasicInfoWidget] 保存节点信息到成员变量时发生异常:" << e.what();
            return;
        } catch (...) {
            qWarning() << "❌ [BasicInfoWidget] 保存节点信息到成员变量时发生未知异常";
            return;
        }
        
        qDebug() << "🔄 [BasicInfoWidget] 准备更新汇总信息";
        try {
            updateSummaryInfo(nodeInfo);
            qDebug() << "✅ [BasicInfoWidget] 汇总信息更新完成";
        } catch (const std::exception& e) {
            qWarning() << "❌ [BasicInfoWidget] 更新汇总信息时发生异常:" << e.what();
        } catch (...) {
            qWarning() << "❌ [BasicInfoWidget] 更新汇总信息时发生未知异常";
        }
        
        qDebug() << "🔄 [BasicInfoWidget] 准备更新基本信息表格";
        try {
            updateBasicInfoTable(nodeInfo);
            qDebug() << "✅ [BasicInfoWidget] 基本信息表格更新完成";
        } catch (const std::exception& e) {
            qWarning() << "❌ [BasicInfoWidget] 更新基本信息表格时发生异常:" << e.what();
        } catch (...) {
            qWarning() << "❌ [BasicInfoWidget] 更新基本信息表格时发生未知异常";
        }
        
        qDebug() << "🎉 [BasicInfoWidget] 节点信息设置完成";
        
    } catch (const std::exception& e) {
        qWarning() << "❌ [BasicInfoWidget] 设置节点信息时发生异常:" << e.what();
    } catch (...) {
        qWarning() << "❌ [BasicInfoWidget] 设置节点信息时发生未知异常";
    }
}

void BasicInfoWidget::clearInfo()
{
    qDebug() << "🧹 [BasicInfoWidget] 开始清空节点信息";
    
    try {
        m_currentNodeInfo = NodeInfo();
        
        // 清空汇总信息 - 修复：使用有意义的默认信息
        NodeInfo defaultInfo;
        defaultInfo.nodeName = "未选择节点";
        defaultInfo.nodeType = "请选择树形控件中的节点";
        defaultInfo.status = NodeStatus::Unknown;
        
        qDebug() << "🔄 [BasicInfoWidget] 准备更新默认汇总信息";
        updateSummaryInfo(defaultInfo);
        qDebug() << "✅ [BasicInfoWidget] 默认汇总信息更新完成";
        
        // 清空表格
        if (m_basicInfoTable) {
            qDebug() << "🔄 [BasicInfoWidget] 准备清空基本信息表格";
            m_basicInfoTable->clearContents();
            m_basicInfoTable->setRowCount(1);
            qDebug() << "✅ [BasicInfoWidget] 基本信息表格清空完成";
        } else {
            qDebug() << "❌ [BasicInfoWidget] 错误：基本信息表格为空！";
        }
        
        qDebug() << "🎉 [BasicInfoWidget] 节点信息清空完成";
        
    } catch (const std::exception& e) {
        qWarning() << "❌ [BasicInfoWidget] 清空节点信息时发生异常:" << e.what();
    } catch (...) {
        qWarning() << "❌ [BasicInfoWidget] 清空节点信息时发生未知异常";
    }
}



void BasicInfoWidget::updateSummaryInfo(const NodeInfo& nodeInfo)
{
    qDebug() << "🔄 [BasicInfoWidget] 开始更新汇总信息";
    qDebug() << "   📋 节点名称:" << nodeInfo.nodeName;
    qDebug() << "   🏷️  节点类型:" << nodeInfo.nodeType;
    qDebug() << "   📊 节点状态:" << (nodeInfo.status == NodeStatus::Online ? "在线" : 
                                      nodeInfo.status == NodeStatus::Offline ? "离线" : 
                                      nodeInfo.status == NodeStatus::Error ? "错误" : "未知");
    qDebug() << "   🔗 子节点数量:" << nodeInfo.subNodes.size();
    
    // 🆕 新增：多层空指针检查
    if (!ui) {
        qWarning() << "❌ [BasicInfoWidget] UI对象为空，无法更新汇总信息";
        return;
    }
    
    if (!ui->lblSummaryInfo) {
        qWarning() << "❌ [BasicInfoWidget] 汇总信息标签控件为空，无法更新汇总信息";
        return;
    }
    
    // 修复：增加空值防护检查
    if (nodeInfo.nodeName.isEmpty() && nodeInfo.nodeType.isEmpty()) {
        qDebug() << "⚠️  [BasicInfoWidget] 检测到空节点信息，使用默认提示";
        try {
            ui->lblSummaryInfo->setText("请选择树形控件中的节点以查看详细信息");
            qDebug() << "✅ [BasicInfoWidget] 默认提示已设置";
        } catch (const std::exception& e) {
            qWarning() << "❌ [BasicInfoWidget] 设置默认提示时发生异常:" << e.what();
        } catch (...) {
            qWarning() << "❌ [BasicInfoWidget] 设置默认提示时发生未知异常";
        }
        return;
    }
    
    try {
        if (nodeInfo.nodeType == "控制通道组") {
            qDebug() << "🎯 [BasicInfoWidget] 检测到控制通道组节点";
            // 控制通道根节点：显示汇总信息
            int totalChannels = 0;
            int totalSubNodes = 0;
            
            // 🆕 新增：详细统计信息
            qDebug() << "📊 [BasicInfoWidget] 开始统计控制通道组信息...";
            
            // 统计通道数量和子节点数量
            for (const SubNodeInfo& subNode : nodeInfo.subNodes) {
                if (subNode.type == "控制通道") {
                    totalChannels++;
                    qDebug() << "🔌 [BasicInfoWidget] 找到控制通道:" << subNode.name;
                    qDebug() << "   📋 设备名称:" << subNode.deviceName;
                    qDebug() << "   🆔 设备ID:" << subNode.deviceId;
                    qDebug() << "   🔌 连接状态:" << (subNode.isConnected ? "已连接" : "未连接");
                    
                    // 打印通道的属性
                    const QMap<QString, QVariant>& properties = subNode.properties;
                    if (!properties.isEmpty()) {
                        qDebug() << "   📋 属性数量:" << properties.size();
                        for (auto it = properties.begin(); it != properties.end(); ++it) {
                            qDebug() << "      *" << it.key() << ":" << it.value().toString();
                        }
                    }
                } else {
                    totalSubNodes++;
                    qDebug() << "📍 [BasicInfoWidget] 找到子节点:" << subNode.name << "类型:" << subNode.type;
                    qDebug() << "   📋 设备名称:" << subNode.deviceName;
                    qDebug() << "   🆔 设备ID:" << subNode.deviceId;
                    qDebug() << "   🔌 连接状态:" << (subNode.isConnected ? "已连接" : "未连接");
                    
                    // 打印子节点的属性
                    const QMap<QString, QVariant>& properties = subNode.properties;
                    if (!properties.isEmpty()) {
                        qDebug() << "  - 属性数量:" << properties.size();
                        for (auto it = properties.begin(); it != properties.end(); ++it) {
                            qDebug() << "    *" << it.key() << ":" << it.value().toString();
                        }
                    }
                }
            }
            
            qDebug() << "BasicInfoWidget::updateSummaryInfo: 统计完成:";
            qDebug() << "  - 控制通道数量:" << totalChannels;
            qDebug() << "  - 子节点总数:" << totalSubNodes;
            
            QString summaryText = QString(
                "🎛️ 控制通道组 - 汇总信息\n"
                "控制通道数量: %1个"
            ).arg(totalChannels);
            
            qDebug() << "BasicInfoWidget::updateSummaryInfo: 设置汇总文本:" << summaryText;
            ui->lblSummaryInfo->setText(summaryText);
            
            // 🆕 新增：验证标签设置是否成功
            if (ui->lblSummaryInfo->text() == summaryText) {
                qDebug() << "✅ BasicInfoWidget::updateSummaryInfo: 汇总标签设置成功";
            } else {
                qDebug() << "❌ BasicInfoWidget::updateSummaryInfo: 汇总标签设置失败";
                qDebug() << "  期望:" << summaryText;
                qDebug() << "  实际:" << ui->lblSummaryInfo->text();
            }
            
        } else {
            qDebug() << "📋 BasicInfoWidget::updateSummaryInfo: 其他类型节点:" << nodeInfo.nodeType;
            // 其他类型节点：显示基本信息
            QString summaryText = QString(
                "📊 %1 - 基本信息\n"
                "子节点数量: %2个"
            ).arg(nodeInfo.nodeType).arg(nodeInfo.subNodes.size());
            
            qDebug() << "BasicInfoWidget::updateSummaryInfo: 设置基本信息汇总文本:" << summaryText;
            ui->lblSummaryInfo->setText(summaryText);
            
            // 🆕 新增：验证标签设置是否成功
            if (ui->lblSummaryInfo->text() == summaryText) {
                qDebug() << "✅ BasicInfoWidget::updateSummaryInfo: 基本信息标签设置成功";
            } else {
                qDebug() << "❌ BasicInfoWidget::updateSummaryInfo: 基本信息标签设置失败";
                qDebug() << "  期望:" << summaryText;
                qDebug() << "  实际:" << ui->lblSummaryInfo->text();
            }
        }
    } catch (const std::exception& e) {
        qWarning() << "❌ [BasicInfoWidget] 更新汇总信息时发生异常:" << e.what();
    } catch (...) {
        qWarning() << "❌ [BasicInfoWidget] 更新汇总信息时发生未知异常";
    }
    
    qDebug() << "✅ [BasicInfoWidget] 汇总信息更新完成";
}

void BasicInfoWidget::updateBasicInfoTable(const NodeInfo& nodeInfo)
{
    qDebug() << "🔄 [BasicInfoWidget] 开始更新基本信息表格";
    qDebug() << "   📋 节点名称:" << nodeInfo.nodeName;
    qDebug() << "   🏷️  节点类型:" << nodeInfo.nodeType;
    qDebug() << "   📊 节点状态:" << (nodeInfo.status == NodeStatus::Online ? "在线" : 
                                      nodeInfo.status == NodeStatus::Offline ? "离线" : 
                                      nodeInfo.status == NodeStatus::Error ? "错误" : "未知");
    qDebug() << "   🔗 子节点数量:" << nodeInfo.subNodes.size();
    
    try {
        // 🆕 新增：详细打印子节点信息
        for (int i = 0; i < nodeInfo.subNodes.size(); ++i) {
            const SubNodeInfo& subNode = nodeInfo.subNodes[i];
            qDebug() << "   📍 子节点[" << (i+1) << "]:";
            qDebug() << "      📋 名称:" << subNode.name;
            qDebug() << "      🏷️  类型:" << subNode.type;
            qDebug() << "      🔧 设备名称:" << subNode.deviceName;
            qDebug() << "      🆔 设备ID:" << subNode.deviceId;
            qDebug() << "      🔌 连接状态:" << (subNode.isConnected ? "已连接" : "未连接");
            
            // 打印子节点的属性
            const QMap<QString, QVariant>& properties = subNode.properties;
            if (!properties.isEmpty()) {
                qDebug() << "      📋 属性数量:" << properties.size();
                for (auto it = properties.begin(); it != properties.end(); ++it) {
                    qDebug() << "         *" << it.key() << ":" << it.value().toString();
                }
            }
        }
        
        if (!m_basicInfoTable) {
            qDebug() << "❌ [BasicInfoWidget] 错误：基本信息表格为空！";
            return;
        }
        
        // 🆕 新增：打印表格当前状态
        qDebug() << "📊 [BasicInfoWidget] 表格当前状态:";
        qDebug() << "   📏 行数:" << m_basicInfoTable->rowCount();
        qDebug() << "   📏 列数:" << m_basicInfoTable->columnCount();
        qDebug() << "   🔗 表格指针:" << m_basicInfoTable;
        
        // 清空表格内容
        m_basicInfoTable->clearContents();
        qDebug() << "✅ [BasicInfoWidget] 表格内容已清空";
        
        // 如果是控制通道根节点，使用13列横向布局显示
        if (nodeInfo.nodeType == "控制通道组") {
            qDebug() << "🎯 [BasicInfoWidget] 检测到控制通道组节点，使用特殊布局";
            // 设置行数：每个子节点1行
            int totalRows = nodeInfo.subNodes.size();
            m_basicInfoTable->setRowCount(totalRows);
            qDebug() << "📏 [BasicInfoWidget] 设置表格行数:" << totalRows;
            
            // 🆕 新增：验证行数设置是否成功
            if (m_basicInfoTable->rowCount() != totalRows) {
                qDebug() << "⚠️  [BasicInfoWidget] 行数设置失败！期望:" << totalRows << "实际:" << m_basicInfoTable->rowCount();
            } else {
                qDebug() << "✅ [BasicInfoWidget] 行数设置成功:" << m_basicInfoTable->rowCount();
            }
            
            // 设置垂直表头标签
            QStringList verticalLabels;
            for (int i = 0; i < totalRows; ++i) {
                const SubNodeInfo& subNode = nodeInfo.subNodes[i];
                if (subNode.type == "控制通道") {
                    verticalLabels << subNode.name; // 直接使用通道名称（CH1、CH2）
                    qDebug() << "🏷️  [BasicInfoWidget] 行" << (i+1) << "标签:" << subNode.name << "(控制通道)";
                } else {
                    verticalLabels << QString("子节点%1").arg(i + 1);
                    qDebug() << "🏷️  [BasicInfoWidget] 行" << (i+1) << "标签:子节点" << (i + 1) << "(" << subNode.type << ")";
                }
            }
            m_basicInfoTable->setVerticalHeaderLabels(verticalLabels);
            qDebug() << "✅ [BasicInfoWidget] 垂直表头标签已设置:" << verticalLabels;
            
            // 🆕 新增：验证垂直表头设置是否成功
            QStringList actualLabels;
            for (int i = 0; i < m_basicInfoTable->rowCount(); ++i) {
                QTableWidgetItem* headerItem = m_basicInfoTable->verticalHeaderItem(i);
                if (headerItem) {
                    actualLabels << headerItem->text();
                } else {
                    actualLabels << QString("行%1").arg(i + 1);
                }
            }
            qDebug() << "BasicInfoWidget::updateBasicInfoTable: 实际垂直表头标签:" << actualLabels;
            
            // 填充表格数据：每行代表一个控制通道
            for (int row = 0; row < totalRows; ++row) {
                const SubNodeInfo& subNode = nodeInfo.subNodes[row];
                qDebug() << "BasicInfoWidget::updateBasicInfoTable: 处理行" << (row+1) << ":" << subNode.name;
                
                // 🆕 新增：调试信息 - 显示子节点的所有属性
                qDebug() << "BasicInfoWidget::updateBasicInfoTable: 子节点" << subNode.name << "属性:";
                qDebug() << "  - 载荷1传感器选择:" << subNode.getProperty("载荷1传感器选择").toString();
                qDebug() << "  - 载荷2传感器选择:" << subNode.getProperty("载荷2传感器选择").toString();
                qDebug() << "  - 位置传感器选择:" << subNode.getProperty("位置传感器选择").toString();
                qDebug() << "  - 控制作动器选择:" << subNode.getProperty("控制作动器选择").toString();
                qDebug() << "  - 硬件关联选择:" << subNode.getProperty("硬件关联选择").toString();
                qDebug() << "  - 下位机ID:" << subNode.getProperty("下位机ID").toString();
                qDebug() << "  - 站点ID:" << subNode.getProperty("站点ID").toString();
                qDebug() << "  - 使能状态:" << subNode.getProperty("使能状态").toString();
                
                int col = 0;
                
                // 通道名称
                QTableWidgetItem* nameItem = new QTableWidgetItem(subNode.name);
                m_basicInfoTable->setItem(row, col++, nameItem);
                
                // 硬件关联选择
                QTableWidgetItem* hardwareItem = new QTableWidgetItem(
                    subNode.getProperty("硬件关联选择", "未关联").toString());
                m_basicInfoTable->setItem(row, col++, hardwareItem);
                
                // 载荷1传感器选择
                QTableWidgetItem* load1Item = new QTableWidgetItem(
                    subNode.getProperty("载荷1传感器选择", "未选择").toString());
                m_basicInfoTable->setItem(row, col++, load1Item);
                
                // 载荷2传感器选择
                QTableWidgetItem* load2Item = new QTableWidgetItem(
                    subNode.getProperty("载荷2传感器选择", "未选择").toString());
                m_basicInfoTable->setItem(row, col++, load2Item);
                
                // 位置传感器选择
                QTableWidgetItem* positionItem = new QTableWidgetItem(
                    subNode.getProperty("位置传感器选择", "未选择").toString());
                m_basicInfoTable->setItem(row, col++, positionItem);
                
                // 控制作动器选择
                QTableWidgetItem* actuatorItem = new QTableWidgetItem(
                    subNode.getProperty("控制作动器选择", "未选择").toString());
                m_basicInfoTable->setItem(row, col++, actuatorItem);
                
                // 下位机ID
                QTableWidgetItem* lowerMachineItem = new QTableWidgetItem(
                    subNode.getProperty("下位机ID", "未知").toString());
                m_basicInfoTable->setItem(row, col++, lowerMachineItem);
                
                // 站点ID
                QTableWidgetItem* siteItem = new QTableWidgetItem(
                    subNode.getProperty("站点ID", "未知").toString());
                m_basicInfoTable->setItem(row, col++, siteItem);
                
                // 使能状态
                QTableWidgetItem* enableItem = new QTableWidgetItem(
                    subNode.getProperty("使能状态", false).toBool() ? "启用" : "禁用");
                m_basicInfoTable->setItem(row, col++, enableItem);
                
                // 控制作动器极性
                QTableWidgetItem* actuatorPolarityItem = new QTableWidgetItem(
                    getPolarityText(subNode.getProperty("控制作动器极性", 0).toInt()));
                m_basicInfoTable->setItem(row, col++, actuatorPolarityItem);
                
                // 载荷1传感器极性
                QTableWidgetItem* load1PolarityItem = new QTableWidgetItem(
                    getPolarityText(subNode.getProperty("载荷1传感器极性", 0).toInt()));
                m_basicInfoTable->setItem(row, col++, load1PolarityItem);
                
                // 载荷2传感器极性
                QTableWidgetItem* load2PolarityItem = new QTableWidgetItem(
                    getPolarityText(subNode.getProperty("载荷2传感器极性", 0).toInt()));
                m_basicInfoTable->setItem(row, col++, load2PolarityItem);
                
                // 位置传感器极性
                QTableWidgetItem* positionPolarityItem = new QTableWidgetItem(
                    getPolarityText(subNode.getProperty("位置传感器极性", 0).toInt()));
                m_basicInfoTable->setItem(row, col++, positionPolarityItem);
                
                qDebug() << "BasicInfoWidget::updateBasicInfoTable: 行" << (row+1) << "数据填充完成";
            }
            
            qDebug() << "=== BasicInfoWidget::updateBasicInfoTable: 控制通道组布局处理完成 ===";
            
        } else {
            qDebug() << "BasicInfoWidget::updateBasicInfoTable: 使用标准布局处理节点类型:" << nodeInfo.nodeType;
            
            // 标准布局：单行显示，每列一个属性
            m_basicInfoTable->setRowCount(1);
            
            int col = 0;
            
            // 通道名称
            QTableWidgetItem* nameItem = new QTableWidgetItem(nodeInfo.nodeName);
            m_basicInfoTable->setItem(0, col++, nameItem);
            
            // 硬件关联选择
            QTableWidgetItem* hardwareItem = new QTableWidgetItem(
                nodeInfo.getBasicProperty("硬件关联选择", "未关联").toString());
            m_basicInfoTable->setItem(0, col++, hardwareItem);
            
            // 载荷1传感器选择
            QTableWidgetItem* load1Item = new QTableWidgetItem(
                nodeInfo.getBasicProperty("载荷1传感器选择", "未选择").toString());
            m_basicInfoTable->setItem(0, col++, load1Item);
            
            // 载荷2传感器选择
            QTableWidgetItem* load2Item = new QTableWidgetItem(
                nodeInfo.getBasicProperty("载荷2传感器选择", "未选择").toString());
            m_basicInfoTable->setItem(0, col++, load2Item);
            
            // 位置传感器选择
            QTableWidgetItem* positionItem = new QTableWidgetItem(
                nodeInfo.getBasicProperty("位置传感器选择", "未选择").toString());
            m_basicInfoTable->setItem(0, col++, positionItem);
            
            // 控制作动器选择
            QTableWidgetItem* actuatorItem = new QTableWidgetItem(
                nodeInfo.getBasicProperty("控制作动器选择", "未选择").toString());
            m_basicInfoTable->setItem(0, col++, actuatorItem);
            
            // 下位机ID
            QTableWidgetItem* lowerMachineItem = new QTableWidgetItem(
                nodeInfo.getBasicProperty("下位机ID", "未知").toString());
            m_basicInfoTable->setItem(0, col++, lowerMachineItem);
            
            // 站点ID
            QTableWidgetItem* siteItem = new QTableWidgetItem(
                nodeInfo.getBasicProperty("站点ID", "未知").toString());
            m_basicInfoTable->setItem(0, col++, siteItem);
            
            // 使能状态
            QTableWidgetItem* enableItem = new QTableWidgetItem(
                nodeInfo.getBasicProperty("使能状态", false).toBool() ? "启用" : "禁用");
            m_basicInfoTable->setItem(0, col++, enableItem);
            
            // 控制作动器极性
            QTableWidgetItem* actuatorPolarityItem = new QTableWidgetItem(
                getPolarityText(nodeInfo.getBasicProperty("控制作动器极性", 0).toInt()));
            m_basicInfoTable->setItem(0, col++, actuatorPolarityItem);
            
            // 载荷1传感器极性
            QTableWidgetItem* load1PolarityItem = new QTableWidgetItem(
                getPolarityText(nodeInfo.getBasicProperty("载荷1传感器极性", 0).toInt()));
            m_basicInfoTable->setItem(0, col++, load1PolarityItem);
            
            // 载荷2传感器极性
            QTableWidgetItem* load2PolarityItem = new QTableWidgetItem(
                getPolarityText(nodeInfo.getBasicProperty("载荷2传感器极性", 0).toInt()));
            m_basicInfoTable->setItem(0, col++, load2PolarityItem);
            
            // 位置传感器极性
            QTableWidgetItem* positionPolarityItem = new QTableWidgetItem(
                getPolarityText(nodeInfo.getBasicProperty("位置传感器极性", 0).toInt()));
            m_basicInfoTable->setItem(0, col++, positionPolarityItem);
            
            qDebug() << "=== BasicInfoWidget::updateBasicInfoTable: 标准布局处理完成 ===";
        }
        
        // 🆕 新增：检查是否需要设置选中行
        if (nodeInfo.basicProperties.contains("selectedRow")) {
            int selectedRow = nodeInfo.getBasicProperty("selectedRow").toInt();
            if (selectedRow >= 0) {
                qDebug() << "🎯 BasicInfoWidget::updateBasicInfoTable: 检测到选中行设置:" << selectedRow;
                // 延迟设置选中行，确保表格完全更新后再设置
                QTimer::singleShot(100, [this, selectedRow]() {
                    this->setSelectedRow(selectedRow);
                });
            }
        }
        
        qDebug() << "=== BasicInfoWidget::updateBasicInfoTable: 标准布局处理完成 ===";
        
    } catch (const std::exception& e) {
        qWarning() << "❌ [BasicInfoWidget] 更新基本信息表格时发生异常:" << e.what();
    } catch (...) {
        qWarning() << "❌ [BasicInfoWidget] 更新基本信息表格时发生未知异常";
    }
}



QString BasicInfoWidget::getStatusText(NodeStatus status)
{
    switch (status) {
        case NodeStatus::Online: return "在线";
        case NodeStatus::Offline: return "离线";
        case NodeStatus::Warning: return "警告";
        case NodeStatus::Error: return "错误";
        case NodeStatus::Maintenance: return "维护";
        default: return "未知";
    }
}





void BasicInfoWidget::adjustTableColumns()
{
    if (!m_basicInfoTable) return;
    
    // 调整列宽以适应内容
    m_basicInfoTable->resizeColumnsToContents();
    
    // 确保最后一列能够拉伸填充剩余空间
    m_basicInfoTable->horizontalHeader()->setStretchLastSection(true);
}

void BasicInfoWidget::resizeEvent(QResizeEvent* event)
{
    QWidget::resizeEvent(event);
    
    // 在窗口大小改变时调整表格列宽
    adjustTableColumns();
}

// 🆕 新增：设置控制通道根节点信息
void BasicInfoWidget::setControlChannelRootInfo(const QString& rootName, 
                                               const QList<QTreeWidgetItem*>& childChannels)
{
    // 创建控制通道根节点的NodeInfo
    NodeInfo rootNodeInfo = createControlChannelRootNodeInfo(rootName, childChannels);
    
    // 设置节点信息
    setNodeInfo(rootNodeInfo);
}

// 🆕 新增：创建控制通道根节点的NodeInfo
NodeInfo BasicInfoWidget::createControlChannelRootNodeInfo(const QString& rootName,
                                                         const QList<QTreeWidgetItem*>& childChannels)
{
    qDebug() << "=== BasicInfoWidget::createControlChannelRootNodeInfo: 开始创建控制通道根节点信息 ===";
    qDebug() << "BasicInfoWidget::createControlChannelRootNodeInfo: 根节点名称:" << rootName;
    qDebug() << "BasicInfoWidget::createControlChannelRootNodeInfo: 子通道数量:" << childChannels.size();
    
    // 创建根节点信息
    NodeInfo rootInfo;
    rootInfo.nodeName = rootName;
    rootInfo.nodeType = "控制通道组";
    rootInfo.status = NodeStatus::Online;
    
    qDebug() << "BasicInfoWidget::createControlChannelRootNodeInfo: 根节点基本信息已设置:";
    qDebug() << "  - 节点名称:" << rootInfo.nodeName;
    qDebug() << "  - 节点类型:" << rootInfo.nodeType;
    qDebug() << "  - 节点ID:" << rootInfo.nodeId;
    qDebug() << "  - 状态:" << (rootInfo.status == NodeStatus::Online ? "在线" : "离线");
    
    // 🆕 新增：设置根节点属性
    rootInfo.setBasicProperty("系统类型", "控制通道组");
    rootInfo.setBasicProperty("通道数量", childChannels.size());
    rootInfo.setBasicProperty("创建时间", QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"));
    rootInfo.setBasicProperty("版本", "1.0.0");
    
    qDebug() << "BasicInfoWidget::createControlChannelRootNodeInfo: 根节点属性已设置";
    
    // 遍历所有子通道，创建SubNodeInfo
    for (int i = 0; i < childChannels.size(); ++i) {
        QTreeWidgetItem* channel = childChannels[i];
        qDebug() << "BasicInfoWidget::createControlChannelRootNodeInfo: 处理子通道" << i << ":" << channel->text(0);
        
        // 🆕 新增：打印子通道的详细信息
        qDebug() << "  - 子通道详细信息:";
        qDebug() << "    * 列0(名称):" << channel->text(0);
        qDebug() << "    * 列1(硬件关联):" << channel->text(1);
        qDebug() << "    * 列2(下位机ID):" << channel->text(2);
        qDebug() << "    * 列3(站点ID):" << channel->text(3);
        qDebug() << "    * 列4(使能状态):" << channel->text(4);
        qDebug() << "    * 列5(控制作动器极性):" << channel->text(5);
        qDebug() << "    * 列6(载荷1传感器极性):" << channel->text(6);
        qDebug() << "    * 列7(载荷2传感器极性):" << channel->text(7);
        qDebug() << "    * 列8(位置传感器极性):" << channel->text(8);
        qDebug() << "    * 子节点数量:" << channel->childCount();
        
        SubNodeInfo channelInfo;
        channelInfo.name = channel->text(0);
        channelInfo.type = "控制通道";
        channelInfo.deviceName = "LD-B1"; // 硬件关联
        channelInfo.deviceId = channel->text(0);
        channelInfo.isConnected = true;
        
        qDebug() << "BasicInfoWidget::createControlChannelRootNodeInfo: 通道" << channel->text(0) << "基本信息已设置";
        
        // 设置通道属性（基于现有13列结构）
        QString lowerId = channel->text(2).isEmpty() ? "1" : channel->text(2);
        QString siteId = channel->text(3).isEmpty() ? "1" : channel->text(3);
        QString enableStatus = channel->text(4).isEmpty() ? "✅" : channel->text(4);
        QString controlPolarity = channel->text(5).isEmpty() ? "正向" : channel->text(5);
        QString load1Polarity = channel->text(6).isEmpty() ? "正向" : channel->text(6);
        QString load2Polarity = channel->text(7).isEmpty() ? "正向" : channel->text(7);
        QString positionPolarity = channel->text(8).isEmpty() ? "正向" : channel->text(8);
        
        // 🔧 修复：使用实际的硬件关联信息，而不是硬编码
        QString hardwareAssociation = channel->text(1).isEmpty() ? "" : channel->text(1);
        
        channelInfo.setProperty("下位机ID", lowerId);
        channelInfo.setProperty("站点ID", siteId);
        channelInfo.setProperty("使能状态", enableStatus);
        channelInfo.setProperty("控制作动器极性", controlPolarity);
        channelInfo.setProperty("载荷1传感器极性", load1Polarity);
        channelInfo.setProperty("载荷2传感器极性", load2Polarity);
        channelInfo.setProperty("位置传感器极性", positionPolarity);
        channelInfo.setProperty("硬件关联选择", hardwareAssociation);
        
        qDebug() << "BasicInfoWidget::createControlChannelRootNodeInfo: 通道" << channel->text(0) << "属性已设置:";
        qDebug() << "  - 下位机ID:" << lowerId;
        qDebug() << "  - 站点ID:" << siteId;
        qDebug() << "  - 使能状态:" << enableStatus;
        qDebug() << "  - 控制作动器极性:" << controlPolarity;
        qDebug() << "  - 载荷1传感器极性:" << load1Polarity;
        qDebug() << "  - 载荷2传感器极性:" << load2Polarity;
        qDebug() << "  - 位置传感器极性:" << positionPolarity;
        qDebug() << "  - 硬件关联选择:" << hardwareAssociation;
        
        // 添加子节点信息
        int subNodeCount = channel->childCount();
        channelInfo.setProperty("子节点数量", subNodeCount);
        qDebug() << "BasicInfoWidget::createControlChannelRootNodeInfo: 通道" << channel->text(0) << "有" << subNodeCount << "个子节点";
        
        // 🔧 修复：处理子节点信息 - 正确的设备关联映射
        if (subNodeCount > 0) {
            qDebug() << "BasicInfoWidget::createControlChannelRootNodeInfo: 开始处理子节点...";
            for (int j = 0; j < subNodeCount; ++j) {
                QTreeWidgetItem* subNode = channel->child(j);
                QString subNodeName = subNode->text(0);
                QString subNodeType = getSubNodeTypeStatic(subNodeName);
                QString deviceName = subNode->text(1);  // 🔧 修复：从列1获取设备关联信息
                
                qDebug() << "  - 子节点[" << j << "]:" << subNodeName;
                qDebug() << "    * 类型:" << subNodeType;
                qDebug() << "    * 设备名称:" << deviceName;
                
                // 🔧 修复1：根据子节点类型正确设置设备关联
                QString deviceAssociation;
                if (subNodeType == "载荷1传感器") {
                    deviceAssociation = deviceName.isEmpty() ? "" : deviceName;
                    channelInfo.setProperty("载荷1传感器选择", deviceAssociation);
                    channelInfo.setProperty("载荷1传感器状态", deviceName.isEmpty() ? "❌ 未关联" : "✅ 已关联");
                    
                    // 🆕 新增：从子节点的text(5)列读取极性信息
                    QString polarityText = subNode->text(5);
                    if (!polarityText.isEmpty()) {
                        int polarityValue = getPolarityValueStatic(polarityText);
                        channelInfo.setProperty("载荷1传感器极性", polarityValue);
                        qDebug() << "    * 设置载荷1传感器极性:" << polarityText << " -> " << polarityValue;
                    }
                    
                    qDebug() << "    * 设置载荷1传感器选择:" << deviceAssociation;
                } else if (subNodeType == "载荷2传感器") {
                    deviceAssociation = deviceName.isEmpty() ? "" : deviceName;
                    channelInfo.setProperty("载荷2传感器选择", deviceAssociation);
                    channelInfo.setProperty("载荷2传感器状态", deviceName.isEmpty() ? "❌ 未关联" : "✅ 已关联");
                    
                    // 🆕 新增：从子节点的text(5)列读取极性信息
                    QString polarityText = subNode->text(5);
                    if (!polarityText.isEmpty()) {
                        int polarityValue = getPolarityValueStatic(polarityText);
                        channelInfo.setProperty("载荷2传感器极性", polarityValue);
                        qDebug() << "    * 设置载荷2传感器极性:" << polarityText << " -> " << polarityValue;
                    }
                    
                    qDebug() << "    * 设置载荷2传感器选择:" << deviceAssociation;
                } else if (subNodeType == "位置传感器") {
                    deviceAssociation = deviceName.isEmpty() ? "" : deviceName;
                    channelInfo.setProperty("位置传感器选择", deviceAssociation);
                    channelInfo.setProperty("位置传感器状态", deviceName.isEmpty() ? "❌ 未关联" : "✅ 已关联");
                    
                    // 🆕 新增：从子节点的text(5)列读取极性信息
                    QString polarityText = subNode->text(5);
                    if (!polarityText.isEmpty()) {
                        int polarityValue = getPolarityValueStatic(polarityText);
                        channelInfo.setProperty("位置传感器极性", polarityValue);
                        qDebug() << "    * 设置位置传感器极性:" << polarityText << " -> " << polarityValue;
                    }
                    
                    qDebug() << "    * 设置位置传感器选择:" << deviceAssociation;
                } else if (subNodeType == "控制作动器") {
                    deviceAssociation = deviceName.isEmpty() ? "" : deviceName;
                    channelInfo.setProperty("控制作动器选择", deviceAssociation);
                    channelInfo.setProperty("控制作动器状态", deviceName.isEmpty() ? "❌ 未关联" : "✅ 已关联");
                    
                    // 🆕 新增：从子节点的text(5)列读取极性信息
                    QString polarityText = subNode->text(5);
                    if (!polarityText.isEmpty()) {
                        int polarityValue = getPolarityValueStatic(polarityText);
                        channelInfo.setProperty("控制作动器极性", polarityValue);
                        qDebug() << "    * 设置控制作动器极性:" << polarityText << " -> " << polarityValue;
                    }
                    
                    qDebug() << "    * 设置控制作动器选择:" << deviceAssociation;
                } else {
                    // 未知类型，使用通用处理
                    deviceAssociation = deviceName.isEmpty() ? "" : deviceName;
                    channelInfo.setProperty(QString("%1选择").arg(subNodeType), deviceAssociation);
                    channelInfo.setProperty(QString("%1状态").arg(subNodeType), 
                                         deviceName.isEmpty() ? "❌ 未关联" : "✅ 已关联");
                    qDebug() << "    * 设置通用设备选择:" << deviceAssociation;
                }
                
                // 🔧 修复2：设置子节点的其他属性
                channelInfo.setProperty(QString("子节点_%1_名称").arg(j), subNodeName);
                channelInfo.setProperty(QString("子节点_%1_类型").arg(j), subNodeType);
                channelInfo.setProperty(QString("子节点_%1_设备名称").arg(j), deviceName);
                channelInfo.setProperty(QString("子节点_%1_关联状态").arg(j), 
                                     deviceName.isEmpty() ? "❌ 未关联" : "✅ 已关联");
                
                qDebug() << "    * 子节点信息创建完成:" << subNodeName;
                qDebug() << "      > 类型:" << subNodeType;
                qDebug() << "      > 设备名称:" << deviceName;
                qDebug() << "      > 设备关联:" << deviceAssociation;
                qDebug() << "      > 关联状态:" << (deviceName.isEmpty() ? "未关联" : "已关联");
            }
            qDebug() << "BasicInfoWidget::createControlChannelRootNodeInfo: 通道" << channel->text(0) << "的子节点处理完成";
        }
        
        // 🆕 修改：只添加通道信息，不添加子节点详细信息
        // 子节点信息仍然存储在通道属性中，但不作为独立的表格行显示
        qDebug() << "BasicInfoWidget::createControlChannelRootNodeInfo: 通道" << channel->text(0) << "有" << subNodeCount << "个子节点，但不作为独立行显示";
        
        // 将通道也添加到根节点
        rootInfo.addSubNode(channelInfo);
        qDebug() << "BasicInfoWidget::createControlChannelRootNodeInfo: 通道" << channel->text(0) << "已添加到根节点";
        
        // 🆕 新增：验证通道属性设置
        qDebug() << "BasicInfoWidget::createControlChannelRootNodeInfo: 通道" << channel->text(0) << "属性验证:";
        qDebug() << "  - 载荷1传感器选择:" << channelInfo.getProperty("载荷1传感器选择").toString();
        qDebug() << "  - 载荷2传感器选择:" << channelInfo.getProperty("载荷2传感器选择").toString();
        qDebug() << "  - 位置传感器选择:" << channelInfo.getProperty("位置传感器选择").toString();
        qDebug() << "  - 控制作动器选择:" << channelInfo.getProperty("控制作动器选择").toString();
        qDebug() << "  - 硬件关联选择:" << channelInfo.getProperty("硬件关联选择").toString();
        qDebug() << "  - 下位机ID:" << channelInfo.getProperty("下位机ID").toString();
        qDebug() << "  - 站点ID:" << channelInfo.getProperty("站点ID").toString();
        qDebug() << "  - 使能状态:" << channelInfo.getProperty("使能状态").toString();
    }
    
    qDebug() << "BasicInfoWidget::createControlChannelRootNodeInfo: 控制通道根节点信息创建完成";
    qDebug() << "  - 总通道数:" << rootInfo.subNodes.size();
    qDebug() << "  - 根节点类型:" << rootInfo.nodeType;
    qDebug() << "  - 根节点名称:" << rootInfo.nodeName;
    
    // 🆕 新增：验证最终结果
    qDebug() << "BasicInfoWidget::createControlChannelRootNodeInfo: 最终验证结果:";
    for (int i = 0; i < rootInfo.subNodes.size(); ++i) {
        const SubNodeInfo& subNode = rootInfo.subNodes[i];
        qDebug() << "  - 子节点[" << i << "]:" << subNode.name;
        qDebug() << "    * 类型:" << subNode.type;
        qDebug() << "    * 属性数量:" << subNode.properties.size();
    }
    
    qDebug() << "=== BasicInfoWidget::createControlChannelRootNodeInfo: 控制通道根节点信息创建完成 ===";
    return rootInfo;
}

// 🆕 新增：获取子节点类型的辅助方法
QString BasicInfoWidget::getSubNodeType(const QString& subNodeName)
{
    if (subNodeName.contains("载荷")) return "载荷传感器";
    if (subNodeName.contains("位置")) return "位置传感器";
    if (subNodeName.contains("控制")) return "控制作动器";
    return "未知类型";
}

// 🆕 新增：获取子节点类型的静态方法（用于静态方法中）
QString BasicInfoWidget::getSubNodeTypeStatic(const QString& subNodeName)
{
    // 🔧 修复：更精确的子节点类型识别
    if (subNodeName == "载荷1" || subNodeName.contains("载荷1", Qt::CaseInsensitive)) {
        return "载荷1传感器";
    } else if (subNodeName == "载荷2" || subNodeName.contains("载荷2", Qt::CaseInsensitive)) {
        return "载荷2传感器";
    } else if (subNodeName == "位置" || subNodeName.contains("位置", Qt::CaseInsensitive)) {
        return "位置传感器";
    } else if (subNodeName == "控制" || subNodeName.contains("控制", Qt::CaseInsensitive)) {
        return "控制作动器";
    } else if (subNodeName.contains("传感器", Qt::CaseInsensitive)) {
        return "传感器";
    } else if (subNodeName.contains("作动器", Qt::CaseInsensitive)) {
        return "作动器";
    } else if (subNodeName.contains("通道", Qt::CaseInsensitive)) {
        return "控制通道";
    } else {
        return "未知类型";
    }
}

// 🆕 新增：获取极性文本的辅助方法
QString BasicInfoWidget::getPolarityText(int polarity) const
{
    switch (polarity) {
        case 0:
            return "正极性";
        case 1:
            return "负极性";
        case -1:
            return "负极性";
        default:
            return QString("未知极性(%1)").arg(polarity);
    }
}

// 🆕 新增：极性文本转换为数字值的辅助方法
int BasicInfoWidget::getPolarityValue(const QString& polarityText) const
{
    if (polarityText.contains("正极性") || polarityText.contains("Positive") || polarityText == "1") {
        return 1;
    } else if (polarityText.contains("负极性") || polarityText.contains("Negative") || polarityText == "-1") {
        return -1;
    } else if (polarityText.contains("双极性") || polarityText.contains("Both") || polarityText == "9") {
        return 9;
    } else if (polarityText.contains("无极性") || polarityText.contains("Unknown") || polarityText == "0") {
        return 0;
    } else {
        return 1; // 默认正极性
    }
}

// 🆕 新增：极性文本转换为数字值的静态辅助方法
int BasicInfoWidget::getPolarityValueStatic(const QString& polarityText)
{
    if (polarityText.contains("正极性") || polarityText.contains("Positive") || polarityText == "1") {
        return 1;
    } else if (polarityText.contains("负极性") || polarityText.contains("Negative") || polarityText == "-1") {
        return -1;
    } else if (polarityText.contains("双极性") || polarityText.contains("Both") || polarityText == "9") {
        return 9;
    } else if (polarityText.contains("无极性") || polarityText.contains("Unknown") || polarityText == "0") {
        return 0;
    } else {
        return 1; // 默认正极性
    }
}

// 🆕 新增：添加控制通道行
void BasicInfoWidget::addControlChannelRow(int row, const SubNodeInfo& channel)
{
    qDebug() << "=== BasicInfoWidget::addControlChannelRow: 开始填充控制通道行" << row << " ===";
    qDebug() << "BasicInfoWidget::addControlChannelRow: 通道名称:" << channel.name;
    qDebug() << "BasicInfoWidget::addControlChannelRow: 通道类型:" << channel.type;
    qDebug() << "BasicInfoWidget::addControlChannelRow: 设备名称:" << channel.deviceName;
    qDebug() << "BasicInfoWidget::addControlChannelRow: 设备ID:" << channel.deviceId;
    qDebug() << "BasicInfoWidget::addControlChannelRow: 连接状态:" << channel.isConnected;
    
    // 🆕 新增：打印通道的所有属性
    const QMap<QString, QVariant>& properties = channel.properties;
    if (!properties.isEmpty()) {
        qDebug() << "BasicInfoWidget::addControlChannelRow: 通道属性数量:" << properties.size();
        for (auto it = properties.begin(); it != properties.end(); ++it) {
            qDebug() << "  -" << it.key() << ":" << it.value().toString();
        }
    }
    
    if (!m_basicInfoTable) {
        qDebug() << "❌ BasicInfoWidget::addControlChannelRow: 基本信息表格为空！";
        return;
    }
    
    // 🆕 新增：验证行索引是否有效
    if (row < 0 || row >= m_basicInfoTable->rowCount()) {
        qDebug() << "❌ BasicInfoWidget::addControlChannelRow: 行索引无效！行:" << row << "表格行数:" << m_basicInfoTable->rowCount();
        return;
    }
    
    qDebug() << "BasicInfoWidget::addControlChannelRow: 开始填充13列数据...";
    
    // 列0: 通道名称
    QTableWidgetItem* nameItem = new QTableWidgetItem(channel.name);
    nameItem->setTextAlignment(Qt::AlignCenter);
    nameItem->setBackground(QColor("#fff3e0"));
    nameItem->setFont(QFont("Microsoft YaHei UI", 9, QFont::Bold));
    m_basicInfoTable->setItem(row, 0, nameItem);
    qDebug() << "✅ BasicInfoWidget::addControlChannelRow: 列0(通道名称)已设置:" << channel.name;
    
    // 列1: 硬件关联选择
    QString hardwareAssociation = channel.getProperty("硬件关联选择").toString();
    QTableWidgetItem* deviceItem = new QTableWidgetItem(hardwareAssociation.isEmpty() ? "" : hardwareAssociation);
    deviceItem->setTextAlignment(Qt::AlignCenter);
    m_basicInfoTable->setItem(row, 1, deviceItem);
    qDebug() << "✅ BasicInfoWidget::addControlChannelRow: 列1(硬件关联选择)已设置:" << deviceItem->text();
    
    // 列2: 载荷1传感器选择
    QString load1Value = channel.getProperty("载荷1传感器选择").toString();
    QTableWidgetItem* load1Item = new QTableWidgetItem(load1Value.isEmpty() ? "" : load1Value);
    load1Item->setTextAlignment(Qt::AlignCenter);
    m_basicInfoTable->setItem(row, 2, load1Item);
    qDebug() << "✅ BasicInfoWidget::addControlChannelRow: 列2(载荷1传感器选择)已设置:" << load1Value;
    
    // 列3: 载荷2传感器选择
    QString load2Value = channel.getProperty("载荷2传感器选择").toString();
    QTableWidgetItem* load2Item = new QTableWidgetItem(load2Value.isEmpty() ? "" : load2Value);
    load2Item->setTextAlignment(Qt::AlignCenter);
    m_basicInfoTable->setItem(row, 3, load2Item);
    qDebug() << "✅ BasicInfoWidget::addControlChannelRow: 列3(载荷2传感器选择)已设置:" << load2Value;
    
    // 列4: 位置传感器选择
    QString positionValue = channel.getProperty("位置传感器选择").toString();
    QTableWidgetItem* positionItem = new QTableWidgetItem(positionValue.isEmpty() ? "" : positionValue);
    positionItem->setTextAlignment(Qt::AlignCenter);
    m_basicInfoTable->setItem(row, 4, positionItem);
    qDebug() << "✅ BasicInfoWidget::addControlChannelRow: 列4(位置传感器选择)已设置:" << positionValue;
    
    // 列5: 控制作动器选择
    QString actuatorValue = channel.getProperty("控制作动器选择").toString();
    QTableWidgetItem* actuatorItem = new QTableWidgetItem(actuatorValue.isEmpty() ? "" : actuatorValue);
    actuatorItem->setTextAlignment(Qt::AlignCenter);
    m_basicInfoTable->setItem(row, 5, actuatorItem);
    qDebug() << "✅ BasicInfoWidget::addControlChannelRow: 列5(控制作动器选择)已设置:" << actuatorValue;
    
    // 列6: 下位机ID
    QString lowerIdValue = channel.getProperty("下位机ID").toString();
    QTableWidgetItem* lowerIdItem = new QTableWidgetItem(lowerIdValue);
    lowerIdItem->setTextAlignment(Qt::AlignCenter);
    m_basicInfoTable->setItem(row, 6, lowerIdItem);
    qDebug() << "✅ BasicInfoWidget::addControlChannelRow: 列6(下位机ID)已设置:" << lowerIdValue;
    
    // 列7: 站点ID
    QString siteIdValue = channel.getProperty("站点ID").toString();
    QTableWidgetItem* siteIdItem = new QTableWidgetItem(siteIdValue);
    siteIdItem->setTextAlignment(Qt::AlignCenter);
    m_basicInfoTable->setItem(row, 7, siteIdItem);
    qDebug() << "✅ BasicInfoWidget::addControlChannelRow: 列7(站点ID)已设置:" << siteIdValue;
    
    // 列8: 使能状态
    QString enableValue = channel.getProperty("使能状态").toString();
    QTableWidgetItem* enableItem = new QTableWidgetItem(enableValue);
    enableItem->setTextAlignment(Qt::AlignCenter);
    m_basicInfoTable->setItem(row, 8, enableItem);
    qDebug() << "✅ BasicInfoWidget::addControlChannelRow: 列8(使能状态)已设置:" << enableValue;
    
    // 列9: 控制作动器极性
    QString polarityValue = channel.getProperty("控制作动器极性").toString();
    QTableWidgetItem* polarityItem = new QTableWidgetItem(polarityValue);
    polarityItem->setTextAlignment(Qt::AlignCenter);
    m_basicInfoTable->setItem(row, 9, polarityItem);
    qDebug() << "✅ BasicInfoWidget::addControlChannelRow: 列9(控制作动器极性)已设置:" << polarityValue;
    
    // 列10: 载荷1传感器极性
    QString load1PolarityValue = channel.getProperty("载荷1传感器极性").toString();
    QTableWidgetItem* load1PolarityItem = new QTableWidgetItem(load1PolarityValue);
    load1PolarityItem->setTextAlignment(Qt::AlignCenter);
    m_basicInfoTable->setItem(row, 10, load1PolarityItem);
    qDebug() << "✅ BasicInfoWidget::addControlChannelRow: 列10(载荷1传感器极性)已设置:" << load1PolarityValue;
    
    // 列11: 载荷2传感器极性
    QString load2PolarityValue = channel.getProperty("载荷2传感器极性").toString();
    QTableWidgetItem* load2PolarityItem = new QTableWidgetItem(load2PolarityValue);
    load2PolarityItem->setTextAlignment(Qt::AlignCenter);
    m_basicInfoTable->setItem(row, 11, load2PolarityItem);
    qDebug() << "✅ BasicInfoWidget::addControlChannelRow: 列11(载荷2传感器极性)已设置:" << load2PolarityValue;
    
    // 列12: 位置传感器极性
    QString positionPolarityValue = channel.getProperty("位置传感器极性").toString();
    QTableWidgetItem* positionPolarityItem = new QTableWidgetItem(positionPolarityValue);
    positionPolarityItem->setTextAlignment(Qt::AlignCenter);
    m_basicInfoTable->setItem(row, 12, positionPolarityItem);
    qDebug() << "✅ BasicInfoWidget::addControlChannelRow: 列12(位置传感器极性)已设置:" << positionPolarityValue;
    
    // 🆕 新增：验证所有列是否设置成功
    qDebug() << "BasicInfoWidget::addControlChannelRow: 验证行" << row << "的所有列设置...";
    for (int col = 0; col < 13; ++col) {
        QTableWidgetItem* item = m_basicInfoTable->item(row, col);
        if (item) {
            qDebug() << "  - 列" << col << "设置成功，内容:" << item->text();
        } else {
            qDebug() << "  - 列" << col << "设置失败，内容为空";
        }
    }
    
    qDebug() << "✅ BasicInfoWidget::addControlChannelRow: 控制通道行" << row << "填充完成";
    qDebug() << "=== BasicInfoWidget::addControlChannelRow: 控制通道行" << row << "处理完成 ===";
}

// 🆕 新增：添加控制子节点行
void BasicInfoWidget::addControlSubNodeRow(int row, const QString& channelName, const SubNodeInfo& subNode)
{
    qDebug() << "=== BasicInfoWidget::addControlSubNodeRow: 开始填充子节点行" << row << " ===";
    qDebug() << "BasicInfoWidget::addControlSubNodeRow: 子节点名称:" << subNode.name;
    qDebug() << "BasicInfoWidget::addControlSubNodeRow: 子节点类型:" << subNode.type;
    qDebug() << "BasicInfoWidget::addControlSubNodeRow: 通道名称:" << channelName;
    qDebug() << "BasicInfoWidget::addControlSubNodeRow: 设备名称:" << subNode.deviceName;
    qDebug() << "BasicInfoWidget::addControlSubNodeRow: 设备ID:" << subNode.deviceId;
    qDebug() << "BasicInfoWidget::addControlSubNodeRow: 连接状态:" << subNode.isConnected;
    
    // 🆕 新增：打印子节点的所有属性
    const QMap<QString, QVariant>& properties = subNode.properties;
    if (!properties.isEmpty()) {
        qDebug() << "BasicInfoWidget::addControlSubNodeRow: 子节点属性数量:" << properties.size();
        for (auto it = properties.begin(); it != properties.end(); ++it) {
            qDebug() << "  -" << it.key() << ":" << it.value().toString();
        }
    }
    
    if (!m_basicInfoTable) {
        qDebug() << "❌ BasicInfoWidget::addControlSubNodeRow: 基本信息表格为空！";
        return;
    }
    
    // 🆕 新增：验证行索引是否有效
    if (row < 0 || row >= m_basicInfoTable->rowCount()) {
        qDebug() << "❌ BasicInfoWidget::addControlSubNodeRow: 行索引无效！行:" << row << "表格行数:" << m_basicInfoTable->rowCount();
        return;
    }
    
    qDebug() << "BasicInfoWidget::addControlSubNodeRow: 开始填充13列数据...";
    
    // 列0: 通道名称 - 显示子节点名称（缩进）
    QString displayName = QString("  └─ %1").arg(subNode.name);
    QTableWidgetItem* nameItem = new QTableWidgetItem(displayName);
    nameItem->setTextAlignment(Qt::AlignLeft);
    nameItem->setBackground(QColor("#fafafa"));
    m_basicInfoTable->setItem(row, 0, nameItem);
    qDebug() << "✅ BasicInfoWidget::addControlSubNodeRow: 列0(通道名称)已设置:" << displayName;
    
    // 列1: 硬件关联选择 - 显示关联信息
    QString deviceDisplayName = subNode.deviceName.isEmpty() ? "未配置" : subNode.deviceName;
    QTableWidgetItem* deviceItem = new QTableWidgetItem(deviceDisplayName);
    deviceItem->setTextAlignment(Qt::AlignCenter);
    m_basicInfoTable->setItem(row, 1, deviceItem);
    qDebug() << "✅ BasicInfoWidget::addControlSubNodeRow: 列1(硬件关联选择)已设置:" << deviceDisplayName;
    
    // 列2: 载荷1传感器选择
    QString load1Value = subNode.getProperty("载荷1传感器选择").toString();
    QTableWidgetItem* load1Item = new QTableWidgetItem(load1Value);
    load1Item->setTextAlignment(Qt::AlignCenter);
    m_basicInfoTable->setItem(row, 2, load1Item);
    qDebug() << "✅ BasicInfoWidget::addControlSubNodeRow: 列2(载荷1传感器选择)已设置:" << load1Value;
    
    // 列3: 载荷2传感器选择
    QString load2Value = subNode.getProperty("载荷2传感器选择").toString();
    QTableWidgetItem* load2Item = new QTableWidgetItem(load2Value);
    load2Item->setTextAlignment(Qt::AlignCenter);
    m_basicInfoTable->setItem(row, 3, load2Item);
    qDebug() << "✅ BasicInfoWidget::addControlSubNodeRow: 列3(载荷2传感器选择)已设置:" << load2Value;
    
    // 列4: 位置传感器选择
    QString positionValue = subNode.getProperty("位置传感器选择").toString();
    QTableWidgetItem* positionItem = new QTableWidgetItem(positionValue);
    positionItem->setTextAlignment(Qt::AlignCenter);
    m_basicInfoTable->setItem(row, 4, positionItem);
    qDebug() << "✅ BasicInfoWidget::addControlSubNodeRow: 列4(位置传感器选择)已设置:" << positionValue;
    
    // 列5: 控制作动器选择
    QString actuatorValue = subNode.getProperty("控制作动器选择").toString();
    QTableWidgetItem* actuatorItem = new QTableWidgetItem(actuatorValue);
    actuatorItem->setTextAlignment(Qt::AlignCenter);
    m_basicInfoTable->setItem(row, 5, actuatorItem);
    qDebug() << "✅ BasicInfoWidget::addControlSubNodeRow: 列5(控制作动器选择)已设置:" << actuatorValue;
    
    // 列6: 下位机ID
    QString lowerIdValue = subNode.getProperty("下位机ID").toString();
    QTableWidgetItem* lowerIdItem = new QTableWidgetItem(lowerIdValue);
    lowerIdItem->setTextAlignment(Qt::AlignCenter);
    m_basicInfoTable->setItem(row, 6, lowerIdItem);
    qDebug() << "✅ BasicInfoWidget::addControlSubNodeRow: 列6(下位机ID)已设置:" << lowerIdValue;
    
    // 列7: 站点ID
    QString siteIdValue = subNode.getProperty("站点ID").toString();
    QTableWidgetItem* siteIdItem = new QTableWidgetItem(siteIdValue);
    siteIdItem->setTextAlignment(Qt::AlignCenter);
    m_basicInfoTable->setItem(row, 7, siteIdItem);
    qDebug() << "✅ BasicInfoWidget::addControlSubNodeRow: 列7(站点ID)已设置:" << siteIdValue;
    
    // 列8: 使能状态
    QString enableValue = subNode.getProperty("使能状态").toString();
    QTableWidgetItem* enableItem = new QTableWidgetItem(enableValue);
    enableItem->setTextAlignment(Qt::AlignCenter);
    m_basicInfoTable->setItem(row, 8, enableItem);
    qDebug() << "✅ BasicInfoWidget::addControlSubNodeRow: 列8(使能状态)已设置:" << enableValue;
    
    // 列9: 控制作动器极性
    QString polarityValue = subNode.getProperty("控制作动器极性").toString();
    QTableWidgetItem* polarityItem = new QTableWidgetItem(polarityValue);
    polarityItem->setTextAlignment(Qt::AlignCenter);
    m_basicInfoTable->setItem(row, 9, polarityItem);
    qDebug() << "✅ BasicInfoWidget::addControlSubNodeRow: 列9(控制作动器极性)已设置:" << polarityValue;
    
    // 列10: 载荷1传感器极性
    QString load1PolarityValue = subNode.getProperty("载荷1传感器极性").toString();
    QTableWidgetItem* load1PolarityItem = new QTableWidgetItem(load1PolarityValue);
    load1PolarityItem->setTextAlignment(Qt::AlignCenter);
    m_basicInfoTable->setItem(row, 10, load1PolarityItem);
    qDebug() << "✅ BasicInfoWidget::addControlSubNodeRow: 列10(载荷1传感器极性)已设置:" << load1PolarityValue;
    
    // 列11: 载荷2传感器极性
    QString load2PolarityValue = subNode.getProperty("载荷2传感器极性").toString();
    QTableWidgetItem* load2PolarityItem = new QTableWidgetItem(load2PolarityValue);
    load2PolarityItem->setTextAlignment(Qt::AlignCenter);
    m_basicInfoTable->setItem(row, 11, load2PolarityItem);
    qDebug() << "✅ BasicInfoWidget::addControlSubNodeRow: 列11(载荷2传感器极性)已设置:" << load2PolarityValue;
    
    // 列12: 位置传感器极性
    QString positionPolarityValue = subNode.getProperty("位置传感器极性").toString();
    QTableWidgetItem* positionPolarityItem = new QTableWidgetItem(positionPolarityValue);
    positionPolarityItem->setTextAlignment(Qt::AlignCenter);
    m_basicInfoTable->setItem(row, 12, positionPolarityItem);
    qDebug() << "✅ BasicInfoWidget::addControlSubNodeRow: 列12(位置传感器极性)已设置:" << positionPolarityValue;
    
    // 🆕 新增：验证所有列是否设置成功
    qDebug() << "BasicInfoWidget::addControlSubNodeRow: 验证行" << row << "的所有列设置...";
    for (int col = 0; col < 13; ++col) {
        QTableWidgetItem* item = m_basicInfoTable->item(row, col);
        if (item) {
            qDebug() << "  - 列" << col << "设置成功，内容:" << item->text();
        } else {
            qDebug() << "  - 列" << col << "设置失败，内容为空";
        }
    }
    
    qDebug() << "✅ BasicInfoWidget::addControlSubNodeRow: 子节点行" << row << "填充完成";
    qDebug() << "=== BasicInfoWidget::addControlSubNodeRow: 子节点行" << row << "处理完成 ===";
}

// 🆕 新增：设置表格选中行
void BasicInfoWidget::setSelectedRow(int row)
{
    if (!m_basicInfoTable) {
        qDebug() << "⚠️ BasicInfoWidget::setSelectedRow: 基本信息表格未初始化";
        return;
    }
    
    qDebug() << "🎯 BasicInfoWidget::setSelectedRow: 设置选中行:" << row;
    
    // 检查行索引是否有效
    if (row < 0 || row >= m_basicInfoTable->rowCount()) {
        qDebug() << "⚠️ BasicInfoWidget::setSelectedRow: 行索引无效:" << row << "，表格行数:" << m_basicInfoTable->rowCount();
        return;
    }
    
    // 清除之前的选择
    m_basicInfoTable->clearSelection();
    
    // 设置新的选中行
    m_basicInfoTable->selectRow(row);
    
    // 确保选中的行可见
    m_basicInfoTable->scrollToItem(m_basicInfoTable->item(row, 0));
    
    // 设置高亮样式
    for (int col = 0; col < m_basicInfoTable->columnCount(); ++col) {
        QTableWidgetItem* item = m_basicInfoTable->item(row, col);
        if (item) {
            // 设置选中行的背景色
            item->setBackground(QColor("#e8f4fd"));
            item->setSelected(true);
        }
    }
    
    qDebug() << "✅ BasicInfoWidget::setSelectedRow: 行" << row << "已选中并高亮";
} 
