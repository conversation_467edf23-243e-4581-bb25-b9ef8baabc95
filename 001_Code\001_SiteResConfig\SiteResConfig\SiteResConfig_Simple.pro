# SiteResConfig Simple Qt Project File
# 简化版Qt项目文件，避免编译错误

QT += core widgets

CONFIG += c++14

# 强制移除所有C++标准标志并重新设置
QMAKE_CXXFLAGS_RELEASE -= -std=gnu++1y
QMAKE_CXXFLAGS_DEBUG -= -std=gnu++1y
QMAKE_CXXFLAGS -= -std=gnu++1y
QMAKE_CXXFLAGS -= -std=c++14

# 明确设置C++11标准 (Qt 5.14.2兼容)
QMAKE_CXXFLAGS += -std=c++11

# 移除C++宏定义冲突
# DEFINES += __cplusplus=201402L

# 包含路径
INCLUDEPATH += include

# MOC处理
MOC_DIR = debug
OBJECTS_DIR = debug

# 编译优化设置
CONFIG += optimize_full

# 并行编译设置 (通过make参数控制，不是编译器标志)
# 在Qt Creator中设置Make参数: -j4

# Debug模式优化
#CONFIG(debug, debug|release) {
#    # Debug模式下减少调试信息以加快编译
#    QMAKE_CXXFLAGS_DEBUG -= -g
#    QMAKE_CXXFLAGS_DEBUG += -g1  # 减少调试信息级别
#}

# Debug模式
CONFIG(debug, debug|release) {
    # DEFINES += _DEBUG
}

# Release模式优化
CONFIG(release, debug|release) {
    # Release模式下启用更多优化
    QMAKE_CXXFLAGS_RELEASE += -O2
    DEFINES += QT_NO_DEBUG_OUTPUT
}

TARGET = SiteResConfig
TEMPLATE = app

# 包含目录
INCLUDEPATH += include

# 包含QtXlsxWriter库
include(src/QtXlsxWriter-master/src/xlsx/qtxlsx.pri)

# 源文件
SOURCES += \
    src/ActuatorViewModel_1_2.cpp \
    src/ControlChannelEditDialog.cpp \
    src/TreeInteractionHandler.cpp \
    src/main_qt.cpp \
    src/MainWindow_Qt_Simple.cpp \
    src/MainWindow_Association_Enhancement.cpp \
    src/CustomTreeWidgets.cpp \
    src/ActuatorDialog_1_2.cpp \
    src/SensorDialog_1_2.cpp \
    src/SensorDataManager_1_2.cpp \
    src/ActuatorDataManager_1_2.cpp \
    src/DataChangeListener.cpp \
    src/CtrlChanDataManager.cpp \
    src/HardwareNodeResDataManager.cpp \
    #src/DataSequenceManager.cpp \
    src/HardwareConfigDialog.cpp \
    src/PIDParametersDialog.cpp \
    src/ControlModeDialog.cpp \
    src/NodeConfigDialog.cpp \
    src/CreateHardwareNodeDialog.cpp \
    src/Utils_Fixed.cpp \
    src/DataModels_Simple.cpp \
    src/ConfigManager_Simple.cpp \
    src/JSONDataExporter_1_2.cpp \
    src/XLSDataExporter_1_2.cpp \
    src/SensorExcelExtensions_1_2.cpp \
    src/treelinestyle.cpp \
    src/DetailInfoPanel.cpp \
    src/BasicInfoWidget.cpp \
    src/LogManager.cpp \
    src/ConfigManager.cpp \
    src/EventManager.cpp \
    src/DeviceManager.cpp \
    src/ExportManager.cpp \
    src/ProjectManager.cpp \
    src/DialogManager.cpp \
    src/TreeManager.cpp \
    src/InfoPanelManager.cpp \
    src/UIManager.cpp \
    src/MainWindowHelper.cpp

# 头文件
HEADERS += \
    include/ActuatorViewModel_1_2.h \
    include/Common_Fixed.h \
    include/ControlChannelEditDialog.h \
    include/DataModels_Fixed.h \
    include/ConfigManager_Fixed.h \
    include/MainWindow_Qt_Simple.h \
    include/CustomTreeWidgets.h \
    include/ActuatorDialog_1_2.h \
    include/SensorDialog_1_2.h \
    include/SensorDataManager_1_2.h \
    include/ActuatorDataManager_1_2.h \
    include/DataChangeListener.h \
    include/CtrlChanDataManager.h \
    include/HardwareNodeResDataManager.h \
    #include/DataSequenceManager.h \
    include/HardwareConfigDialog.h \
    include/PIDParametersDialog.h \
    include/ControlModeDialog.h \
    include/NodeConfigDialog.h \
    include/CreateHardwareNodeDialog.h \
    include/IDataExporter.h \
    include/JSONDataExporter_1_2.h \
    include/TreeInteractionHandler.h \
    include/XLSDataExporter_1_2.h \
    include/SensorExcelExtensions_1_2.h \
    include/treelinestyle.h \
    include/DetailInfoPanel.h \
    include/BasicInfoWidget.h \
    include/LogManager.h \
    include/ConfigManager.h \
    include/EventManager.h \
    include/DeviceManager.h \
    include/ExportManager.h \
    include/ProjectManager.h \
    include/DialogManager.h \
    include/TreeManager.h \
    include/InfoPanelManager.h \
    include/UIManager.h \
    include/MainWindowHelper.h

# Windows特定配置
win32 {
    CONFIG += windows
}

# 输出信息
message("Building SiteResConfig...")
message("Qt version: $$[QT_VERSION]")

FORMS += \
    ui/MainWindow.ui \
    ui/ActuatorDialog_1_2.ui \
    ui/SensorDialog_1_2.ui \
    ui/HardwareConfigDialog.ui \
    ui/PIDParametersDialog.ui \
    ui/ControlModeDialog.ui \
    ui/NodeConfigDialog.ui \
    ui/CreateHardwareNodeDialog.ui \
    ui/ControlChannelEditDialog.ui \
    ui/BasicInfoWidget.ui

# 资源文件
RESOURCES += \
    resources.qrc
