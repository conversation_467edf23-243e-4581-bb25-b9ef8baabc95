@echo off
echo ========================================
echo  验证传感器精度字段修复
echo ========================================
echo.

echo 1. 检查 SensorParams 结构体中的 accuracy 字段...
findstr /n "QString accuracy" "SiteResConfig\include\SensorDialog.h"
if errorlevel 1 (
    echo ❌ 错误: SensorParams 结构体中未找到 accuracy 字段
    goto :error
) else (
    echo ✅ 成功: SensorParams 结构体中已添加 accuracy 字段
)
echo.

echo 2. 检查传感器对话框UI中的精度控件...
findstr /n "accuracyEdit" "SiteResConfig\ui\SensorDialog.ui"
if errorlevel 1 (
    echo ❌ 错误: UI文件中未找到 accuracyEdit 控件
    goto :error
) else (
    echo ✅ 成功: UI文件中已添加 accuracyEdit 控件
)
echo.

echo 3. 检查 getSensorParams 方法中的精度字段获取...
findstr /n "params.accuracy" "SiteResConfig\src\SensorDialog.cpp"
if errorlevel 1 (
    echo ❌ 错误: getSensorParams 方法中未找到 accuracy 字段获取
    goto :error
) else (
    echo ✅ 成功: getSensorParams 方法中已添加 accuracy 字段获取
)
echo.

echo 4. 检查CSV导出中的精度字段...
findstr /n "精度" "SiteResConfig\src\MainWindow_Qt_Simple.cpp"
if errorlevel 1 (
    echo ❌ 错误: CSV导出中未找到精度字段
    goto :error
) else (
    echo ✅ 成功: CSV导出中已添加精度字段
)
echo.

echo 5. 检查JSON导出中的精度字段...
findstr /n "accuracyObj" "SiteResConfig\src\MainWindow_Qt_Simple.cpp"
if errorlevel 1 (
    echo ❌ 错误: JSON导出中未找到精度字段
    goto :error
) else (
    echo ✅ 成功: JSON导出中已添加精度字段
)
echo.

echo ========================================
echo  🎉 所有检查通过！精度字段修复完成！
echo ========================================
echo.
echo 修复内容总结:
echo ✅ SensorParams 结构体已添加 accuracy 字段
echo ✅ 传感器对话框UI已添加精度输入控件
echo ✅ getSensorParams 方法已添加精度字段获取
echo ✅ CSV导出已添加精度字段输出
echo ✅ JSON导出已添加精度字段输出
echo.
echo 现在传感器详细信息已完整集成到硬件树结构中！
goto :end

:error
echo.
echo ========================================
echo  ❌ 检查失败！请检查修复是否正确
echo ========================================
pause
exit /b 1

:end
echo.
pause
