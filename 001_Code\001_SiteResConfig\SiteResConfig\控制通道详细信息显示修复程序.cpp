#include <QApplication>
#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QLabel>
#include <QTextEdit>
#include <QTableWidget>
#include <QHeaderView>
#include <QDebug>
#include <QTimer>
#include <QMessageBox>

// 模拟数据结构
struct SubNodeInfo {
    QString name;
    QString type;
    QString deviceName;
    QString deviceId;
    bool isConnected;
    QMap<QString, QVariant> properties;
    
    void setProperty(const QString& key, const QVariant& value) {
        properties[key] = value;
    }
    
    QVariant getProperty(const QString& key) const {
        return properties.value(key);
    }
};

struct NodeInfo {
    QString nodeName;
    QString nodeType;
    QString nodeId;
    QList<SubNodeInfo> subNodes;
    QMap<QString, QVariant> basicProperties;
    
    void addSubNode(const SubNodeInfo& subNode) {
        subNodes.append(subNode);
    }
    
    void setBasicProperty(const QString& key, const QVariant& value) {
        basicProperties[key] = value;
    }
    
    QVariant getBasicProperty(const QString& key) const {
        return basicProperties.value(key);
    }
};

// 修复后的控制通道根节点信息创建方法
class ControlChannelInfoFixer {
public:
    // 🔧 修复1：正确的子节点类型识别
    static QString getSubNodeType(const QString& subNodeName) {
        if (subNodeName.contains("载荷1") || subNodeName.contains("载荷传感器1")) {
            return "载荷1传感器";
        } else if (subNodeName.contains("载荷2") || subNodeName.contains("载荷传感器2")) {
            return "载荷2传感器";
        } else if (subNodeName.contains("位置") || subNodeName.contains("位置传感器")) {
            return "位置传感器";
        } else if (subNodeName.contains("控制") || subNodeName.contains("作动器")) {
            return "控制作动器";
        }
        return "未知类型";
    }
    
    // 🔧 修复2：正确的设备关联映射
    static QString getDeviceAssociation(const QString& subNodeType, const QString& channelName) {
        if (subNodeType == "载荷1传感器") {
            return QString("载荷传感器组 - %1_载荷1").arg(channelName);
        } else if (subNodeType == "载荷2传感器") {
            return QString("载荷传感器组 - %1_载荷2").arg(channelName);
        } else if (subNodeType == "位置传感器") {
            return QString("位置传感器组 - %1_位置").arg(channelName);
        } else if (subNodeType == "控制作动器") {
            return QString("作动器组 - %1_控制").arg(channelName);
        }
        return "未配置";
    }
    
    // 🔧 修复3：创建正确的控制通道根节点信息
    static NodeInfo createFixedControlChannelRootNodeInfo(const QString& rootName,
                                                        const QList<QString>& channelNames) {
        qDebug() << "=== 创建修复后的控制通道根节点信息 ===";
        qDebug() << "根节点名称:" << rootName;
        qDebug() << "子通道数量:" << channelNames.size();
        
        NodeInfo rootInfo;
        rootInfo.nodeName = rootName;
        rootInfo.nodeType = "控制通道组";
        
        // 设置根节点属性
        rootInfo.setBasicProperty("系统类型", "控制通道组");
        rootInfo.setBasicProperty("通道数量", channelNames.size());
        rootInfo.setBasicProperty("创建时间", QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"));
        rootInfo.setBasicProperty("版本", "1.0.0");
        
        // 为每个通道创建正确的信息
        for (int i = 0; i < channelNames.size(); ++i) {
            QString channelName = channelNames[i];
            qDebug() << "处理通道" << i << ":" << channelName;
            
            // 创建通道信息
            SubNodeInfo channelInfo;
            channelInfo.name = channelName;
            channelInfo.type = "控制通道";
            channelInfo.deviceName = QString("LD-B1 - %1").arg(channelName);
            channelInfo.deviceId = channelName;
            channelInfo.isConnected = true;
            
            // 设置通道属性
            channelInfo.setProperty("硬件关联选择", QString("LD-B1 - %1").arg(channelName));
            channelInfo.setProperty("下位机ID", "1");
            channelInfo.setProperty("站点ID", "1");
            channelInfo.setProperty("使能状态", "✅ 已启用");
            channelInfo.setProperty("控制作动器极性", "1 (正极性)");
            channelInfo.setProperty("载荷1传感器极性", "1 (正极性)");
            channelInfo.setProperty("载荷2传感器极性", "-1 (负极性)");
            channelInfo.setProperty("位置传感器极性", "9 (双极性)");
            
            // 🔧 修复4：为每个子节点创建正确的设备关联信息
            QStringList subNodeTypes = {"载荷1传感器", "载荷2传感器", "位置传感器", "控制作动器"};
            for (int j = 0; j < subNodeTypes.size(); ++j) {
                QString subNodeType = subNodeTypes[j];
                QString deviceAssociation = getDeviceAssociation(subNodeType, channelName);
                
                // 设置子节点属性到通道属性中
                channelInfo.setProperty(QString("%1选择").arg(subNodeType), deviceAssociation);
                channelInfo.setProperty(QString("%1状态").arg(subNodeType), 
                                     deviceAssociation != "未配置" ? "✅ 已关联" : "❌ 未关联");
                
                qDebug() << "  - 子节点" << j << ":" << subNodeType;
                qDebug() << "    * 设备关联:" << deviceAssociation;
                qDebug() << "    * 关联状态:" << (deviceAssociation != "未配置" ? "已关联" : "未关联");
            }
            
            // 将通道添加到根节点
            rootInfo.addSubNode(channelInfo);
            qDebug() << "通道" << channelName << "信息创建完成";
        }
        
        qDebug() << "=== 修复后的控制通道根节点信息创建完成 ===";
        qDebug() << "总通道数:" << rootInfo.subNodes.size();
        
        return rootInfo;
    }
    
    // 🔧 修复5：验证修复结果
    static void validateFixedInfo(const NodeInfo& nodeInfo) {
        qDebug() << "=== 验证修复后的控制通道信息 ===";
        
        for (int i = 0; i < nodeInfo.subNodes.size(); ++i) {
            const SubNodeInfo& channel = nodeInfo.subNodes[i];
            qDebug() << "通道" << i << ":" << channel.name;
            qDebug() << "  - 类型:" << channel.type;
            qDebug() << "  - 设备名称:" << channel.deviceName;
            qDebug() << "  - 连接状态:" << channel.isConnected;
            
            // 验证子节点属性
            QStringList subNodeTypes = {"载荷1传感器", "载荷2传感器", "位置传感器", "控制作动器"};
            for (const QString& subNodeType : subNodeTypes) {
                QString selection = channel.getProperty(QString("%1选择").arg(subNodeType)).toString();
                QString status = channel.getProperty(QString("%1状态").arg(subNodeType)).toString();
                qDebug() << "    *" << subNodeType << "选择:" << selection;
                qDebug() << "    *" << subNodeType << "状态:" << status;
            }
        }
        
        qDebug() << "=== 验证完成 ===";
    }
};

// 测试主窗口
class TestMainWindow : public QMainWindow {
    Q_OBJECT
    
public:
    TestMainWindow(QWidget *parent = nullptr) : QMainWindow(parent) {
        setWindowTitle("控制通道详细信息显示修复测试");
        setGeometry(100, 100, 1200, 800);
        
        initUI();
        setupTestData();
    }
    
private:
    QTableWidget* m_basicInfoTable;
    QTextEdit* m_logText;
    QPushButton* m_testFixButton;
    QPushButton* m_validateButton;
    QPushButton* m_clearButton;
    
    void initUI() {
        QWidget* centralWidget = new QWidget(this);
        setCentralWidget(centralWidget);
        
        QVBoxLayout* mainLayout = new QVBoxLayout(centralWidget);
        
        // 标题
        QLabel* titleLabel = new QLabel("🔧 控制通道详细信息显示修复测试");
        titleLabel->setStyleSheet("font-size: 18px; font-weight: bold; color: #1976d2; margin: 10px;");
        mainLayout->addWidget(titleLabel);
        
        // 按钮区域
        QHBoxLayout* buttonLayout = new QHBoxLayout();
        m_testFixButton = new QPushButton("🧪 测试修复后的信息创建");
        m_validateButton = new QPushButton("✅ 验证修复结果");
        m_clearButton = new QPushButton("🗑️ 清空表格");
        
        buttonLayout->addWidget(m_testFixButton);
        buttonLayout->addWidget(m_validateButton);
        buttonLayout->addWidget(m_clearButton);
        buttonLayout->addStretch();
        
        mainLayout->addLayout(buttonLayout);
        
        // 基本信息表格
        QLabel* tableLabel = new QLabel("📊 控制通道基本信息表格 (13列)");
        tableLabel->setStyleSheet("font-weight: bold; margin: 5px;");
        mainLayout->addWidget(tableLabel);
        
        m_basicInfoTable = new QTableWidget(this);
        m_basicInfoTable->setColumnCount(13);
        m_basicInfoTable->setRowCount(0);
        
        // 设置列标题
        QStringList headers = {
            "通道名称", "硬件关联选择", "载荷1传感器选择", "载荷2传感器选择", "位置传感器选择",
            "控制作动器选择", "下位机ID", "站点ID", "使能状态", "控制作动器极性",
            "载荷1传感器极性", "载荷2传感器极性", "位置传感器极性"
        };
        m_basicInfoTable->setHorizontalHeaderLabels(headers);
        
        // 设置表格样式
        m_basicInfoTable->setAlternatingRowColors(true);
        m_basicInfoTable->horizontalHeader()->setStretchLastSection(true);
        m_basicInfoTable->setSelectionBehavior(QAbstractItemView::SelectRows);
        
        mainLayout->addWidget(m_basicInfoTable);
        
        // 日志区域
        QLabel* logLabel = new QLabel("📝 修复过程日志");
        logLabel->setStyleSheet("font-weight: bold; margin: 5px;");
        mainLayout->addWidget(logLabel);
        
        m_logText = new QTextEdit(this);
        m_logText->setMaximumHeight(200);
        m_logText->setReadOnly(true);
        mainLayout->addWidget(m_logText);
        
        // 连接信号
        connect(m_testFixButton, &QPushButton::clicked, this, &TestMainWindow::testFixedInfoCreation);
        connect(m_validateButton, &QPushButton::clicked, this, &TestMainWindow::validateFixedInfo);
        connect(m_clearButton, &QPushButton::clicked, this, &TestMainWindow::clearTable);
    }
    
    void setupTestData() {
        addLog("🚀 控制通道详细信息显示修复测试程序已启动");
        addLog("📋 测试目标：修复控制通道节点的详细信息显示问题");
        addLog("🔧 主要修复内容：");
        addLog("  1. 子节点类型识别错误");
        addLog("  2. 数据映射不一致问题");
        addLog("  3. 硬件关联信息丢失");
        addLog("  4. 设备关联状态显示错误");
        addLog("");
    }
    
    void addLog(const QString& message) {
        QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
        m_logText->append(QString("[%1] %2").arg(timestamp).arg(message));
    }
    
private slots:
    void testFixedInfoCreation() {
        addLog("🧪 开始测试修复后的信息创建...");
        
        // 清空表格
        m_basicInfoTable->setRowCount(0);
        
        // 创建测试数据
        QStringList channelNames = {"CH1", "CH2"};
        
        // 使用修复后的方法创建信息
        NodeInfo fixedNodeInfo = ControlChannelInfoFixer::createFixedControlChannelRootNodeInfo(
            "控制通道", channelNames);
        
        // 填充表格
        fillTableWithFixedInfo(fixedNodeInfo);
        
        addLog("✅ 修复后的信息创建测试完成");
        addLog(QString("📊 表格已填充 %1 个通道的信息").arg(fixedNodeInfo.subNodes.size()));
    }
    
    void fillTableWithFixedInfo(const NodeInfo& nodeInfo) {
        int rowCount = nodeInfo.subNodes.size();
        m_basicInfoTable->setRowCount(rowCount);
        
        for (int row = 0; row < rowCount; ++row) {
            const SubNodeInfo& channel = nodeInfo.subNodes[row];
            
            // 列0: 通道名称
            QTableWidgetItem* nameItem = new QTableWidgetItem(channel.name);
            nameItem->setBackground(QColor("#e3f2fd"));
            nameItem->setFont(QFont("Microsoft YaHei UI", 9, QFont::Bold));
            m_basicInfoTable->setItem(row, 0, nameItem);
            
            // 列1: 硬件关联选择
            QTableWidgetItem* deviceItem = new QTableWidgetItem(channel.getProperty("硬件关联选择").toString());
            m_basicInfoTable->setItem(row, 1, deviceItem);
            
            // 列2-5: 传感器和作动器选择
            QStringList subNodeTypes = {"载荷1传感器", "载荷2传感器", "位置传感器", "控制作动器"};
            for (int col = 2; col <= 5; ++col) {
                QString subNodeType = subNodeTypes[col - 2];
                QString selection = channel.getProperty(QString("%1选择").arg(subNodeType)).toString();
                QTableWidgetItem* item = new QTableWidgetItem(selection);
                m_basicInfoTable->setItem(row, col, item);
            }
            
            // 列6-7: 下位机ID和站点ID
            m_basicInfoTable->setItem(row, 6, new QTableWidgetItem(channel.getProperty("下位机ID").toString()));
            m_basicInfoTable->setItem(row, 7, new QTableWidgetItem(channel.getProperty("站点ID").toString()));
            
            // 列8: 使能状态
            QTableWidgetItem* enableItem = new QTableWidgetItem(channel.getProperty("使能状态").toString());
            enableItem->setBackground(QColor("#c8e6c9"));
            m_basicInfoTable->setItem(row, 8, enableItem);
            
            // 列9-12: 极性设置
            QStringList polarityTypes = {"控制作动器极性", "载荷1传感器极性", "载荷2传感器极性", "位置传感器极性"};
            for (int col = 9; col <= 12; ++col) {
                QString polarityType = polarityTypes[col - 9];
                QString polarity = channel.getProperty(polarityType).toString();
                QTableWidgetItem* item = new QTableWidgetItem(polarity);
                m_basicInfoTable->setItem(row, col, item);
            }
        }
        
        // 调整列宽
        m_basicInfoTable->resizeColumnsToContents();
    }
    
    void validateFixedInfo() {
        addLog("✅ 开始验证修复结果...");
        
        int rowCount = m_basicInfoTable->rowCount();
        if (rowCount == 0) {
            addLog("⚠️ 表格为空，请先运行测试");
            return;
        }
        
        addLog(QString("🔍 验证 %1 行数据...").arg(rowCount));
        
        bool allValid = true;
        for (int row = 0; row < rowCount; ++row) {
            QString channelName = m_basicInfoTable->item(row, 0)->text();
            addLog(QString("  验证通道 %1:").arg(channelName));
            
            // 验证基本信息
            QString hardwareAssociation = m_basicInfoTable->item(row, 1)->text();
            if (hardwareAssociation.contains("LD-B1")) {
                addLog(QString("    ✅ 硬件关联: %1").arg(hardwareAssociation));
            } else {
                addLog(QString("    ❌ 硬件关联错误: %1").arg(hardwareAssociation));
                allValid = false;
            }
            
            // 验证传感器选择
            QString load1Sensor = m_basicInfoTable->item(row, 2)->text();
            QString load2Sensor = m_basicInfoTable->item(row, 3)->text();
            QString positionSensor = m_basicInfoTable->item(row, 4)->text();
            QString controlActuator = m_basicInfoTable->item(row, 5)->text();
            
            if (load1Sensor.contains("载荷传感器组")) {
                addLog(QString("    ✅ 载荷1传感器: %1").arg(load1Sensor));
            } else {
                addLog(QString("    ❌ 载荷1传感器错误: %1").arg(load1Sensor));
                allValid = false;
            }
            
            if (load2Sensor.contains("载荷传感器组")) {
                addLog(QString("    ✅ 载荷2传感器: %1").arg(load2Sensor));
            } else {
                addLog(QString("    ❌ 载荷2传感器错误: %1").arg(load2Sensor));
                allValid = false;
            }
            
            if (positionSensor.contains("位置传感器组")) {
                addLog(QString("    ✅ 位置传感器: %1").arg(positionSensor));
            } else {
                addLog(QString("    ❌ 位置传感器错误: %1").arg(positionSensor));
                allValid = false;
            }
            
            if (controlActuator.contains("作动器组")) {
                addLog(QString("    ✅ 控制作动器: %1").arg(controlActuator));
            } else {
                addLog(QString("    ❌ 控制作动器错误: %1").arg(controlActuator));
                allValid = false;
            }
        }
        
        if (allValid) {
            addLog("🎉 所有验证通过！修复成功！");
            QMessageBox::information(this, "验证结果", "🎉 所有验证通过！控制通道详细信息显示修复成功！");
        } else {
            addLog("⚠️ 部分验证失败，需要进一步检查");
            QMessageBox::warning(this, "验证结果", "⚠️ 部分验证失败，请检查修复代码");
        }
    }
    
    void clearTable() {
        m_basicInfoTable->setRowCount(0);
        addLog("🗑️ 表格已清空");
    }
};

int main(int argc, char *argv[]) {
    QApplication app(argc, argv);
    
    TestMainWindow window;
    window.show();
    
    return app.exec();
}

#include "控制通道详细信息显示修复程序.moc" 