#include "DataChangeListener.h"
#include "ActuatorViewModel_1_2.h"
#include <QDebug>
#include <QTimer>
#include <QWidget> // For QWidget
#include <QMap> // For QMap
#include <QVariant> // For QVariant
// qWarning is defined in QDebug header
// qCritical is defined in QDebug header
// NodeInfo is defined in BasicInfoWidget.h
#include <QApplication> // For QApplication::processEvents()
#include <QMessageBox>

DataChangeListener::DataChangeListener(QObject *parent)
    : QObject(parent)
    , detailInfoPanel_(nullptr)
    , sensorDataManager_(nullptr)
    , actuatorDataManager_(nullptr)
    , actuatorViewModel_(nullptr)
    , isConnected_(false)
{
    qDebug() << "🔧 [DataChangeListener] 构造函数开始";
    qDebug() << "✅ [DataChangeListener] 构造函数完成";
}

DataChangeListener::~DataChangeListener()
{
    qDebug() << "🧹 [DataChangeListener] 开始销毁数据变化监听器";
    
    try {
        // 断开所有信号连接
        disconnectAllSignals();
        
        // 清理详细信息面板引用
        if (detailInfoPanel_) {
            qDebug() << "🔄 [DataChangeListener] 清理详细信息面板引用";
            detailInfoPanel_ = nullptr;
        }
        
        // 等待所有延迟操作完成
        qDebug() << "🔄 [DataChangeListener] 等待延迟操作完成...";
        QApplication::processEvents();
        
        qDebug() << "✅ [DataChangeListener] 数据变化监听器销毁完成";
        
    } catch (const std::exception& e) {
        qWarning() << "❌ [DataChangeListener] 销毁时发生异常:" << e.what();
    } catch (...) {
        qWarning() << "❌ [DataChangeListener] 销毁时发生未知异常";
    }
}

void DataChangeListener::setDetailInfoPanel(DetailInfoPanel* panel)
{
    detailInfoPanel_ = panel;
    qDebug() << "🔧 [DataChangeListener] 详细信息面板已设置:" << (panel ? "有效" : "无效");
}

void DataChangeListener::connectSensorDataManager(SensorDataManager_1_2* manager)
{
    if (!manager) {
        qWarning() << "⚠️ [DataChangeListener] 传感器数据管理器为空，无法连接信号";
        return;
    }
    
    sensorDataManager_ = manager;
    
    // 🆕 修复：使用Qt5新语法，支持自动断开连接
    // 当manager对象被销毁时，连接会自动断开，防止访问已销毁对象
    connect(manager, &SensorDataManager_1_2::sensorDataChanged,
            this, &DataChangeListener::onSensorDataChanged, Qt::QueuedConnection);
    connect(manager, &SensorDataManager_1_2::sensorGroupDataChanged,
            this, &DataChangeListener::onSensorGroupDataChanged, Qt::QueuedConnection);
    connect(manager, &SensorDataManager_1_2::sensorAssociationChanged,
            this, &DataChangeListener::onSensorAssociationChanged, Qt::QueuedConnection);
    connect(manager, &SensorDataManager_1_2::errorOccurred,
            this, &DataChangeListener::onSensorError, Qt::QueuedConnection);
    
    // 🆕 新增：连接manager的destroyed信号，在对象销毁时自动清理
    connect(manager, &QObject::destroyed, this, [this](QObject* obj) {
        qDebug() << "🔄 [DataChangeListener] 传感器数据管理器已销毁，自动清理连接";
        if (obj == sensorDataManager_) {
            sensorDataManager_ = nullptr;
            qDebug() << "✅ [DataChangeListener] 传感器数据管理器引用已清理";
        }
    });
    
    qDebug() << "✅ [DataChangeListener] 传感器数据管理器信号连接完成（支持自动断开）";
}

void DataChangeListener::connectActuatorDataManager(ActuatorDataManager_1_2* manager)
{
    if (!manager) {
        qWarning() << "⚠️ [DataChangeListener] 作动器数据管理器为空，无法连接信号";
        return;
    }
    
    actuatorDataManager_ = manager;
    
    // 🆕 修复：使用Qt5新语法，支持自动断开连接
    // 当manager对象被销毁时，连接会自动断开，防止访问已销毁对象
    connect(manager, &ActuatorDataManager_1_2::actuatorDataChanged,
            this, &DataChangeListener::onActuatorDataChanged, Qt::QueuedConnection);
    connect(manager, &ActuatorDataManager_1_2::actuatorGroupDataChanged,
            this, &DataChangeListener::onActuatorGroupDataChanged, Qt::QueuedConnection);
    connect(manager, &ActuatorDataManager_1_2::actuatorAssociationChanged,
            this, &DataChangeListener::onActuatorAssociationChanged, Qt::QueuedConnection);
    connect(manager, &ActuatorDataManager_1_2::errorOccurred,
            this, &DataChangeListener::onActuatorError, Qt::QueuedConnection);
    
    // 🆕 新增：连接manager的destroyed信号，在对象销毁时自动清理
    connect(manager, &QObject::destroyed, this, [this](QObject* obj) {
        qDebug() << "🔄 [DataChangeListener] 作动器数据管理器已销毁，自动清理连接";
        if (obj == actuatorDataManager_) {
            actuatorDataManager_ = nullptr;
            qDebug() << "✅ [DataChangeListener] 作动器数据管理器引用已清理";
        }
    });
    
    qDebug() << "✅ [DataChangeListener] 作动器数据管理器信号连接完成（支持自动断开）";
}

void DataChangeListener::connectActuatorViewModel(ActuatorViewModel1_2* viewModel)
{
    if (!viewModel) {
        qWarning() << "⚠️ [DataChangeListener] 作动器视图模型为空，无法连接信号";
        return;
    }
    
    actuatorViewModel_ = viewModel; // 存储作动器视图模型
    
    // 🆕 修复：使用Qt5新语法，支持自动断开连接
    // 当viewModel对象被销毁时，连接会自动断开，防止访问已销毁对象
    connect(viewModel, &ActuatorViewModel1_2::actuatorDataChanged,
            this, &DataChangeListener::onActuatorDataChanged, Qt::QueuedConnection);
    connect(viewModel, &ActuatorViewModel1_2::actuatorGroupDataChanged,
            this, &DataChangeListener::onActuatorGroupDataChanged, Qt::QueuedConnection);
    connect(viewModel, &ActuatorViewModel1_2::errorOccurred,
            this, &DataChangeListener::onActuatorError, Qt::QueuedConnection);
    
    // 🆕 新增：连接viewModel的destroyed信号，在对象销毁时自动清理
    connect(viewModel, &QObject::destroyed, this, [this](QObject* obj) {
        qDebug() << "🔄 [DataChangeListener] 作动器视图模型已销毁，自动清理连接";
        if (obj == actuatorViewModel_) {
            actuatorViewModel_ = nullptr;
            qDebug() << "✅ [DataChangeListener] 作动器视图模型引用已清理";
        }
    });
    
    qDebug() << "✅ [DataChangeListener] 作动器视图模型信号连接完成（支持自动断开）";
}

void DataChangeListener::disconnectAll()
{
    if (sensorDataManager_) {
        disconnect(sensorDataManager_, nullptr, this, nullptr);
        sensorDataManager_ = nullptr;
    }
    
    if (actuatorDataManager_) {
        disconnect(actuatorDataManager_, nullptr, this, nullptr);
        actuatorDataManager_ = nullptr;
    }
    
    qDebug() << "🔧 [DataChangeListener] 所有信号连接已断开";
}

void DataChangeListener::onSensorDataChanged(const QString& serialNumber, const QString& operation)
{
    qDebug() << "📡 [DataChangeListener] 传感器数据变化:" << serialNumber << "操作:" << operation;
    
    try {
//        // 基本指针检查
//        if (!this) {
//            qWarning() << "❌ [DataChangeListener] this指针无效，跳过处理";
//            return;
//        }
        
        // 🆕 使用统一的检查函数
        if (!isDetailInfoPanelValid()) {
            qDebug() << "ℹ️ [DataChangeListener] 详细信息面板状态无效，跳过处理";
            return;
        }
        
        // 检查是否需要更新当前信息
        if (!shouldUpdateCurrentInfo(serialNumber, operation)) {
            qDebug() << "ℹ️ [DataChangeListener] 无需更新当前信息，跳过处理";
            return;
        }
        
        qDebug() << "🔄 [DataChangeListener] 准备延迟刷新详细信息面板...";
        
        // 延迟刷新详细信息面板
        QTimer::singleShot(100, [this, serialNumber, operation]() {
            qDebug() << "🔄 [DataChangeListener] 延迟刷新开始执行，序列号:" << serialNumber << "操作:" << operation;
            
//            // 检查对象是否仍然有效
//            if (!this) {
//                qWarning() << "❌ [DataChangeListener] 延迟刷新时this指针无效，跳过";
//                return;
//            }
            
            // 🆕 使用统一的Lambda检查函数
            if (!isDetailInfoPanelValidInLambda()) {
                qDebug() << "ℹ️ [DataChangeListener] 延迟刷新时详细信息面板状态已改变，跳过";
                return;
            }
            
            try {
                qDebug() << "🔄 [DataChangeListener] 开始调用refreshDetailInfoPanel...";
                
                // 调用刷新方法
                this->refreshDetailInfoPanel();
                qDebug() << "✅ [DataChangeListener] refreshDetailInfoPanel调用成功";
                
            } catch (const std::exception& e) {
                qWarning() << "❌ [DataChangeListener] 延迟刷新时发生异常:" << e.what();
            } catch (...) {
                qWarning() << "❌ [DataChangeListener] 延迟刷新时发生未知异常";
            }
        });
        
        qDebug() << "✅ [DataChangeListener] 传感器数据变化处理完成，延迟刷新已安排";
        
    } catch (const std::exception& e) {
        qWarning() << "❌ [DataChangeListener] 处理传感器数据变化时发生异常:" << e.what();
    } catch (...) {
        qWarning() << "❌ [DataChangeListener] 处理传感器数据变化时发生未知异常";
    }
}

void DataChangeListener::onSensorGroupDataChanged(int groupId, const QString& operation)
{
    qDebug() << "📡 [DataChangeListener] 传感器组数据变化: 组ID" << groupId << "操作:" << operation;
    
    try {
//        // 基本指针检查
//        if (!this) {
//            qWarning() << "❌ [DataChangeListener] this指针无效，跳过处理";
//            return;
//        }
        
        // 🆕 使用统一的检查函数
        if (!isDetailInfoPanelValid()) {
            qDebug() << "ℹ️ [DataChangeListener] 详细信息面板状态无效，跳过处理";
            return;
        }
        
        // 检查是否需要更新当前信息
        if (!shouldUpdateCurrentInfo(QString::number(groupId), operation)) {
            qDebug() << "ℹ️ [DataChangeListener] 无需更新当前信息，跳过处理";
            return;
        }
        
        qDebug() << "🔄 [DataChangeListener] 准备延迟刷新详细信息面板...";
        
        // 延迟刷新详细信息面板
        QTimer::singleShot(100, [this, groupId, operation]() {
            qDebug() << "🔄 [DataChangeListener] 延迟刷新开始执行，组ID:" << groupId << "操作:" << operation;
            
//            // 检查对象是否仍然有效
//            if (!this) {
//                qWarning() << "❌ [DataChangeListener] 延迟刷新时this指针无效，跳过";
//                return;
//            }
            
            // 🆕 使用统一的Lambda检查函数
            if (!isDetailInfoPanelValidInLambda()) {
                qDebug() << "ℹ️ [DataChangeListener] 延迟刷新时详细信息面板状态已改变，跳过";
                return;
            }
            
            try {
                qDebug() << "🔄 [DataChangeListener] 开始调用refreshDetailInfoPanel...";
                
                // 调用刷新方法
                this->refreshDetailInfoPanel();
                qDebug() << "✅ [DataChangeListener] refreshDetailInfoPanel调用成功";
                
            } catch (const std::exception& e) {
                qWarning() << "❌ [DataChangeListener] 延迟刷新时发生异常:" << e.what();
            } catch (...) {
                qWarning() << "❌ [DataChangeListener] 延迟刷新时发生未知异常";
            }
        });
        
        qDebug() << "✅ [DataChangeListener] 传感器组数据变化处理完成，延迟刷新已安排";
        
    } catch (const std::exception& e) {
        qWarning() << "❌ [DataChangeListener] 处理传感器组数据变化时发生异常:" << e.what();
    } catch (...) {
        qWarning() << "❌ [DataChangeListener] 处理传感器组数据变化时发生未知异常";
    }
}

void DataChangeListener::onSensorAssociationChanged(const QString& serialNumber, const QString& channelName)
{
    qDebug() << "📡 [DataChangeListener] 传感器关联变化:" << serialNumber << "通道:" << channelName;
    
    try {
//        // 基本指针检查
//        if (!this) {
//            qWarning() << "❌ [DataChangeListener] this指针无效，跳过处理";
//            return;
//        }
        
        // 🆕 使用统一的检查函数
        if (!isDetailInfoPanelValid()) {
            qDebug() << "ℹ️ [DataChangeListener] 详细信息面板状态无效，跳过处理";
            return;
        }
        
        // 检查是否需要更新当前信息
        if (!shouldUpdateCurrentInfo(serialNumber, "update")) {
            qDebug() << "ℹ️ [DataChangeListener] 无需更新当前信息，跳过处理";
            return;
        }
        
        qDebug() << "🔄 [DataChangeListener] 准备延迟刷新详细信息面板...";
        
        // 延迟刷新详细信息面板
        QTimer::singleShot(100, [this, serialNumber, channelName]() {
            qDebug() << "🔄 [DataChangeListener] 延迟刷新开始执行，序列号:" << serialNumber << "通道:" << channelName;
            
//            // 检查对象是否仍然有效
//            if (!this) {
//                qWarning() << "❌ [DataChangeListener] 延迟刷新时this指针无效，跳过";
//                return;
//            }
            
            // 🆕 使用统一的Lambda检查函数
            if (!isDetailInfoPanelValidInLambda()) {
                qDebug() << "ℹ️ [DataChangeListener] 延迟刷新时详细信息面板状态已改变，跳过";
                return;
            }
            
            try {
                qDebug() << "🔄 [DataChangeListener] 开始调用refreshDetailInfoPanel...";
                
                // 调用刷新方法
                this->refreshDetailInfoPanel();
                qDebug() << "✅ [DataChangeListener] refreshDetailInfoPanel调用成功";
                
            } catch (const std::exception& e) {
                qWarning() << "❌ [DataChangeListener] 延迟刷新时发生异常:" << e.what();
            } catch (...) {
                qWarning() << "❌ [DataChangeListener] 延迟刷新时发生未知异常";
            }
        });
        
        qDebug() << "✅ [DataChangeListener] 传感器关联变化处理完成，延迟刷新已安排";
        
    } catch (const std::exception& e) {
        qWarning() << "❌ [DataChangeListener] 处理传感器关联变化时发生异常:" << e.what();
    } catch (...) {
        qWarning() << "❌ [DataChangeListener] 处理传感器关联变化时发生未知异常";
    }
}

void DataChangeListener::onActuatorDataChanged(const QString& serialNumber, const QString& operation)
{
    qDebug() << "📡 [DataChangeListener] 作动器数据变化:" << serialNumber << "操作:" << operation;
    
    try {
//        // 基本指针检查
//        if (!this) {
//            qWarning() << "❌ [DataChangeListener] this指针无效，跳过处理";
//            return;
//        }
        
        // 🆕 使用统一的检查函数
        if (!isDetailInfoPanelValid()) {
            qDebug() << "ℹ️ [DataChangeListener] 详细信息面板状态无效，跳过处理";
            return;
        }
        
        // 检查是否需要更新当前信息
        if (!shouldUpdateCurrentInfo(serialNumber, operation)) {
            qDebug() << "ℹ️ [DataChangeListener] 无需更新当前信息，跳过处理";
            return;
        }
        
        qDebug() << "🔄 [DataChangeListener] 准备延迟刷新详细信息面板...";
        
        // 延迟刷新详细信息面板
        QTimer::singleShot(100, [this, serialNumber, operation]() {
            qDebug() << "🔄 [DataChangeListener] 延迟刷新开始执行，序列号:" << serialNumber << "操作:" << operation;
            
//            // 检查对象是否仍然有效
//            if (!this) {
//                qWarning() << "❌ [DataChangeListener] 延迟刷新时this指针无效，跳过";
//                return;
//            }
            
            // 🆕 使用统一的Lambda检查函数
            if (!isDetailInfoPanelValidInLambda()) {
                qDebug() << "ℹ️ [DataChangeListener] 延迟刷新时详细信息面板状态已改变，跳过";
                return;
            }
            
            try {
                qDebug() << "🔄 [DataChangeListener] 开始调用refreshDetailInfoPanel...";
                
                // 调用刷新方法
                this->refreshDetailInfoPanel();
                qDebug() << "✅ [DataChangeListener] refreshDetailInfoPanel调用成功";
                
            } catch (const std::exception& e) {
                qWarning() << "❌ [DataChangeListener] 延迟刷新时发生异常:" << e.what();
            } catch (...) {
                qWarning() << "❌ [DataChangeListener] 延迟刷新时发生未知异常";
            }
        });
        
        qDebug() << "✅ [DataChangeListener] 作动器数据变化处理完成，延迟刷新已安排";
        
    } catch (const std::exception& e) {
        qWarning() << "❌ [DataChangeListener] 处理作动器数据变化时发生异常:" << e.what();
    } catch (...) {
        qWarning() << "❌ [DataChangeListener] 处理作动器数据变化时发生未知异常";
    }
}

void DataChangeListener::onActuatorGroupDataChanged(int groupId, const QString& operation)
{
    qDebug() << "📡 [DataChangeListener] 作动器组数据变化: 组ID" << groupId << "操作:" << operation;

    try {
//        // 基本指针检查
//        if (!this) {
//            qWarning() << "❌ [DataChangeListener] this指针无效，跳过处理";
//            return;
//        }
        
        int iNum = 0;
        qDebug() << __func__ << __LINE__ << ++iNum;
        qDebug() << __func__ << __LINE__ << ++iNum;

        // 🆕 使用统一的检查函数
        if (!isDetailInfoPanelValid()) {
            qDebug() << "ℹ️ [DataChangeListener] 详细信息面板状态无效，跳过处理";
            return;
        }
        qDebug() << __func__ << __LINE__ << ++iNum;
        // 检查是否需要更新当前信息
        if (!shouldUpdateCurrentInfo(QString::number(groupId), operation)) {
            qDebug() << "ℹ️ [DataChangeListener] 无需更新当前信息，跳过处理";
            return;
        }
        qDebug() << __func__ << __LINE__ << ++iNum;
        qDebug() << "🔄 [DataChangeListener] 准备延迟刷新详细信息面板...";
        qDebug() << __func__ << __LINE__ << ++iNum;
        // 延迟刷新详细信息面板
        QTimer::singleShot(100, [this, groupId, operation]() {
            int iNum = 0;
            qDebug() << __func__ << __LINE__ << ++iNum;
            qDebug() << "🔄 [DataChangeListener] 延迟刷新开始执行，组ID:" << groupId << "操作:" << operation;
            
            qDebug() << __func__ << __LINE__ << ++iNum;
//            // 检查对象是否仍然有效
//            if (!this) {
//                qWarning() << "❌ [DataChangeListener] 延迟刷新时this指针无效，跳过";
//                return;
//            }
            
            // 🆕 使用统一的Lambda检查函数
            if (!isDetailInfoPanelValidInLambda()) {
                qDebug() << "ℹ️ [DataChangeListener] 延迟刷新时详细信息面板状态已改变，跳过";
                return;
            }
            
            try {
                qDebug() << "🔄 [DataChangeListener] 开始调用refreshDetailInfoPanel...";
                
                // 调用刷新方法
                this->refreshDetailInfoPanel();
                qDebug() << "✅ [DataChangeListener] refreshDetailInfoPanel调用成功";
                
            } catch (const std::exception& e) {
                qWarning() << "❌ [DataChangeListener] 延迟刷新时发生异常:" << e.what();
            } catch (...) {
                qWarning() << "❌ [DataChangeListener] 延迟刷新时发生未知异常";
            }
        });
        qDebug() << __func__ << __LINE__ << ++iNum;
        qDebug() << "✅ [DataChangeListener] 作动器组数据变化处理完成，延迟刷新已安排";
        qDebug() << __func__ << __LINE__ << ++iNum;
    } catch (const std::exception& e) {
        qWarning() << "❌ [DataChangeListener] 处理作动器组数据变化时发生异常:" << e.what();
    } catch (...) {
        qWarning() << "❌ [DataChangeListener] 处理作动器组数据变化时发生未知异常";
    }
}

void DataChangeListener::onActuatorAssociationChanged(const QString& serialNumber, const QString& channelName)
{
    qDebug() << "📡 [DataChangeListener] 作动器关联变化:" << serialNumber << "通道:" << channelName;
    
    try {
//        // 基本指针检查
//        if (!this) {
//            qWarning() << "❌ [DataChangeListener] this指针无效，跳过处理";
//            return;
//        }
        
        // 🆕 使用统一的检查函数
        if (!isDetailInfoPanelValid()) {
            qDebug() << "ℹ️ [DataChangeListener] 详细信息面板状态无效，跳过处理";
            return;
        }
        
        // 检查是否需要更新当前信息
        if (!shouldUpdateCurrentInfo(serialNumber, "update")) {
            qDebug() << "ℹ️ [DataChangeListener] 无需更新当前信息，跳过处理";
            return;
        }
        
        qDebug() << "🔄 [DataChangeListener] 准备延迟刷新详细信息面板...";
        
        // 延迟刷新详细信息面板
        QTimer::singleShot(100, [this, serialNumber, channelName]() {
            qDebug() << "🔄 [DataChangeListener] 延迟刷新开始执行，序列号:" << serialNumber << "通道:" << channelName;
            
//            // 检查对象是否仍然有效
//            if (!this) {
//                qWarning() << "❌ [DataChangeListener] 延迟刷新时this指针无效，跳过";
//                return;
//            }
            
            // 🆕 使用统一的Lambda检查函数
            if (!isDetailInfoPanelValidInLambda()) {
                qDebug() << "ℹ️ [DataChangeListener] 延迟刷新时详细信息面板状态已改变，跳过";
                return;
            }
            
            try {
                qDebug() << "🔄 [DataChangeListener] 开始调用refreshDetailInfoPanel...";
                
                // 调用刷新方法
                this->refreshDetailInfoPanel();
                qDebug() << "✅ [DataChangeListener] refreshDetailInfoPanel调用成功";
                
            } catch (const std::exception& e) {
                qWarning() << "❌ [DataChangeListener] 延迟刷新时发生异常:" << e.what();
            } catch (...) {
                qWarning() << "❌ [DataChangeListener] 延迟刷新时发生未知异常";
            }
        });
        
        qDebug() << "✅ [DataChangeListener] 作动器关联变化处理完成，延迟刷新已安排";
        
    } catch (const std::exception& e) {
        qWarning() << "❌ [DataChangeListener] 处理作动器关联变化时发生异常:" << e.what();
    } catch (...) {
        qWarning() << "❌ [DataChangeListener] 处理作动器关联变化时发生未知异常";
    }
}

void DataChangeListener::onSensorError(const QString& error)
{
    qWarning() << "❌ [DataChangeListener] 传感器数据管理器错误:" << error;
}

void DataChangeListener::onActuatorError(const QString& error)
{
    qWarning() << "❌ [DataChangeListener] 作动器数据管理器错误:" << error;
}

void DataChangeListener::refreshDetailInfoPanel()
{
    qDebug() << "🔄 [DataChangeListener] 开始刷新详细信息面板";
    
    try {
        // 🆕 使用统一的检查函数
        if (!isDetailInfoPanelValid()) {
            qDebug() << "ℹ️ [DataChangeListener] 详细信息面板状态无效，跳过刷新";
            return;
        }
        
        qDebug() << "🔄 [DataChangeListener] 准备获取当前节点信息...";
        
        // 获取当前节点信息并刷新显示
        NodeInfo currentNodeInfo;
        try {
            currentNodeInfo = detailInfoPanel_->getCurrentNodeInfo();
            qDebug() << "✅ [DataChangeListener] 成功获取当前节点信息";
        } catch (const std::exception& e) {
            qWarning() << "❌ [DataChangeListener] 获取当前节点信息时发生异常:" << e.what();
            return;
        } catch (...) {
            qWarning() << "❌ [DataChangeListener] 获取当前节点信息时发生未知异常";
            return;
        }
        
        // 验证NodeInfo对象的有效性
        if (currentNodeInfo.nodeName.isEmpty() && currentNodeInfo.nodeType.isEmpty()) {
            qDebug() << "ℹ️ [DataChangeListener] 当前无有效节点信息，跳过刷新";
            return;
        }
        
        qDebug() << "🔄 [DataChangeListener] 刷新节点信息:" << currentNodeInfo.nodeName;
        qDebug() << "   📋 节点类型:" << currentNodeInfo.nodeType;
        qDebug() << "   🔗 子节点数量:" << currentNodeInfo.subNodes.size();
        
        // 设置节点信息
        try {
            qDebug() << "🔄 [DataChangeListener] 开始调用setNodeInfo...";
            detailInfoPanel_->setNodeInfo(currentNodeInfo);
            qDebug() << "✅ [DataChangeListener] 节点信息刷新完成";
        } catch (const std::exception& e) {
            qWarning() << "❌ [DataChangeListener] 设置节点信息时发生异常:" << e.what();
        } catch (...) {
            qWarning() << "❌ [DataChangeListener] 设置节点信息时发生未知异常";
        }
        
    } catch (const std::exception& e) {
        qWarning() << "❌ [DataChangeListener] 刷新详细信息面板时发生异常:" << e.what();
    } catch (...) {
        qWarning() << "❌ [DataChangeListener] 刷新详细信息面板时发生未知异常";
    }
}

bool DataChangeListener::shouldUpdateCurrentInfo(const QString& serialNumber, const QString& operation)
{
    qDebug() << "🔍 [DataChangeListener] 检查是否需要更新当前信息:" << serialNumber << "操作:" << operation;
    
    // 🆕 使用统一的检查函数
    if (!isDetailInfoPanelValid()) {
        qDebug() << "ℹ️ [DataChangeListener] 详细信息面板状态无效，无需更新";
        return false;
    }
    
    try {
        
        // 获取当前显示的节点信息
        NodeInfo currentNodeInfo;
        try {
            currentNodeInfo = detailInfoPanel_->getCurrentNodeInfo();
        } catch (const std::exception& e) {
            qWarning() << "❌ [DataChangeListener] 获取当前节点信息时发生异常:" << e.what();
            return false;
        } catch (...) {
            qWarning() << "❌ [DataChangeListener] 获取当前节点信息时发生未知异常";
            return false;
        }
        
        // 验证NodeInfo对象的有效性
        if (currentNodeInfo.nodeName.isEmpty() && currentNodeInfo.nodeType.isEmpty()) {
            qDebug() << "ℹ️ [DataChangeListener] 当前无有效节点信息，无需更新";
            return false;
        }
        
        // 检查当前显示的信息是否包含变化的设备
        try {
            for (const auto& subNode : currentNodeInfo.subNodes) {
                if (subNode.deviceName == serialNumber || subNode.deviceId == serialNumber) {
                    qDebug() << "✅ [DataChangeListener] 当前显示信息包含变化的设备，需要更新";
                    return true;
                }
            }
        } catch (const std::exception& e) {
            qWarning() << "❌ [DataChangeListener] 检查子节点时发生异常:" << e.what();
            return false;
        } catch (...) {
            qWarning() << "❌ [DataChangeListener] 检查子节点时发生未知异常";
            return false;
        }
        
        // 检查基本信息属性是否包含变化的设备
        try {
            QMap<QString, QVariant> properties = currentNodeInfo.basicProperties;
            for (auto it = properties.begin(); it != properties.end(); ++it) {
                if (it.value().toString().contains(serialNumber)) {
                    qDebug() << "✅ [DataChangeListener] 当前显示信息包含变化的设备，需要更新";
                    return true;
                }
            }
        } catch (const std::exception& e) {
            qWarning() << "❌ [DataChangeListener] 检查基本属性时发生异常:" << e.what();
            return false;
        } catch (...) {
            qWarning() << "❌ [DataChangeListener] 检查基本属性时发生未知异常";
            return false;
        }
        
        qDebug() << "ℹ️ [DataChangeListener] 当前显示信息不包含变化的设备，无需更新";
        return false;
        
    } catch (const std::exception& e) {
        qWarning() << "❌ [DataChangeListener] 检查更新需求时发生异常:" << e.what();
        return false;
    } catch (...) {
        qWarning() << "❌ [DataChangeListener] 检查更新需求时发生未知异常";
        return false;
    }
}

void DataChangeListener::disconnectAllSignals()
{
    qDebug() << "🔌 [DataChangeListener] 开始断开所有信号连接";
    
    try {
        // 断开传感器数据管理器信号
        if (sensorDataManager_) {
            disconnect(sensorDataManager_, nullptr, this, nullptr);
            qDebug() << "✅ [DataChangeListener] 传感器数据管理器信号已断开";
            sensorDataManager_ = nullptr;
        }
        
        // 断开作动器数据管理器信号
        if (actuatorDataManager_) {
            disconnect(actuatorDataManager_, nullptr, this, nullptr);
            qDebug() << "✅ [DataChangeListener] 作动器数据管理器信号已断开";
            actuatorDataManager_ = nullptr;
        }
        
        // 断开作动器视图模型信号
        if (actuatorViewModel_) {
            disconnect(actuatorViewModel_, nullptr, this, nullptr);
            qDebug() << "✅ [DataChangeListener] 作动器视图模型信号已断开";
            actuatorViewModel_ = nullptr;
        }
        
        // 更新连接状态
        isConnected_ = false;
        qDebug() << "✅ [DataChangeListener] 所有信号连接已断开";
        
    } catch (const std::exception& e) {
        qWarning() << "❌ [DataChangeListener] 断开信号连接时发生异常:" << e.what();
    } catch (...) {
        qWarning() << "❌ [DataChangeListener] 断开信号连接时发生未知异常";
    }
} 

// 🆕 新增：统一的详细信息面板检查函数
bool DataChangeListener::isDetailInfoPanelValid()
{
    qDebug() << "DataChangeListener : 111111111111111111111";
    int iNum = 0;
    qDebug() << __func__ << __LINE__ << ++iNum << detailInfoPanel_;

    // 第一层检查：指针是否为空
    if (!detailInfoPanel_) {
        qDebug() << __func__ << __LINE__ << ++iNum << detailInfoPanel_ << "ℹ️ [DataChangeListener] 详细信息面板指针为空";
        qDebug() << "ℹ️ [DataChangeListener] 详细信息面板指针为空";
        return false;
    }
    
    qDebug() << __func__ << __LINE__ << ++iNum << detailInfoPanel_;

    // 第二层检查：面板是否可见
    if (!detailInfoPanel_->isVisible()) {
        qDebug() << "ℹ️ [DataChangeListener] 详细信息面板不可见";
        return false;
    }
    
    qDebug() << __func__ << __LINE__ << ++iNum;

    // 第三层检查：面板是否有父窗口
    if (!detailInfoPanel_->parent()) {
        qDebug() << "ℹ️ [DataChangeListener] 详细信息面板无父窗口";
        return false;
    }
    
    qDebug() << __func__ << __LINE__ << ++iNum;

    // 第四层检查：面板是否启用
    if (!detailInfoPanel_->isEnabled()) {
        qDebug() << "ℹ️ [DataChangeListener] 详细信息面板已禁用";
        return false;
    }
    
    qDebug() << __func__ << __LINE__ << ++iNum;

    return true;
}

// 🆕 新增：专门用于lambda表达式的检查函数
bool DataChangeListener::isDetailInfoPanelValidInLambda() const
{
    // 第一层检查：指针是否为空
    if (!detailInfoPanel_) {
        qDebug() << "ℹ️ [DataChangeListener] Lambda中详细信息面板指针为空";
        return false;
    }
    
    // 第二层检查：面板是否可见
    if (!detailInfoPanel_->isVisible()) {
        qDebug() << "ℹ️ [DataChangeListener] Lambda中详细信息面板不可见";
        return false;
    }
    
    // 第三层检查：面板是否有父窗口
    if (!detailInfoPanel_->parent()) {
        qDebug() << "ℹ️ [DataChangeListener] Lambda中详细信息面板无父窗口";
        return false;
    }
    
    return true;
} 
