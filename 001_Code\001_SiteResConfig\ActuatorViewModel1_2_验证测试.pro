QT += core widgets

CONFIG += c++14
CONFIG += console

TARGET = ActuatorViewModel1_2_验证测试
TEMPLATE = app

# 源文件
SOURCES += \
    ActuatorViewModel1_2_验证测试.cpp \
    SiteResConfig/src/ActuatorViewModel1_2.cpp \
    SiteResConfig/src/ActuatorDataManager.cpp \
    SiteResConfig/src/ActuatorDialog.cpp

# 头文件
HEADERS += \
    SiteResConfig/include/ActuatorViewModel1_2.h \
    SiteResConfig/include/ActuatorDataManager.h \
    SiteResConfig/include/ActuatorDialog.h

# 包含路径
INCLUDEPATH += \
    SiteResConfig/include

# 编译器标志
QMAKE_CXXFLAGS += -std=c++14

# Windows特定设置
win32 {
    QMAKE_CXXFLAGS += -finput-charset=UTF-8 -fexec-charset=UTF-8
    DEFINES += UNICODE _UNICODE
}

# 输出目录
DESTDIR = bin
OBJECTS_DIR = obj
MOC_DIR = moc
RCC_DIR = rcc
UI_DIR = ui
