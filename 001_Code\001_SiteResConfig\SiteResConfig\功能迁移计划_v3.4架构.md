# 🚀 **SiteResConfig v3.4架构功能迁移计划**

## 📋 **迁移目标**

将 `MainWindow_Qt_Simple.cpp` 中的 **11,549行代码** 简化到 **500行** (减少95%)

---

## 🎯 **功能分类与迁移映射**

### **📊 主界面当前功能分析**

根据代码分析，主界面包含以下主要功能类别：

| 功能类别 | 估计行数 | 目标管理器 | 优先级 |
|----------|----------|------------|--------|
| **项目管理功能** | ~2000行 | ProjectManager | 🔥 最高 |
| **设备管理功能** | ~2500行 | DeviceManager | 🔥 最高 |
| **树形控件操作** | ~2000行 | TreeManager | 🔥 最高 |
| **对话框管理** | ~1500行 | DialogManager | 🔥 最高 |
| **导入导出功能** | ~1500行 | ExportManager | 🔥 最高 |
| **信息面板更新** | ~1000行 | InfoPanelManager | 🔶 高 |
| **日志记录功能** | ~500行 | LogManager | 🔶 高 |
| **事件处理** | ~500行 | EventManager | 🔶 高 |
| **UI初始化/样式** | ~500行 | 保留在主界面 | 🔸 低 |

---

## 📝 **阶段1: 项目管理功能迁移 → ProjectManager**

### **🎯 迁移的方法列表**

```cpp
// 项目管理相关方法 (约2000行)
void OnNewProject()                    // 新建项目
void OnOpenProject()                   // 打开项目  
void OnSaveProject()                   // 保存项目
void OnSaveAsProject()                 // 另存为项目
bool SaveProjectToJSON(filePath)      // 保存为JSON
bool SaveProjectToJSON_1_2(filePath)  // 保存为JSON 1.2版本
bool SaveProjectToXLS(filePath)       // 保存为XLS
bool LoadProjectFromXLS(filePath)     // 从XLS加载
bool exportProjectData_1_2(filePath)  // 导出项目数据
void completeNewProjectSetup()        // 完成新项目设置
void OnProjectOpened(path, name)      // 项目打开处理
void OnProjectClosed()                // 项目关闭处理
bool PromptSaveIfNeeded()             // 提示保存
void initializeProjectState()         // 初始化项目状态
void updateOperationAreaState(bool)   // 更新操作区域状态
```

### **🔧 迁移实施方案**

1. **在ProjectManager中添加方法**
2. **主界面方法改为调用管理器**
3. **移除主界面中的实现代码**

---

## 📝 **阶段2: 设备管理功能迁移 → DeviceManager**

### **🎯 迁移的方法列表**

```cpp
// 设备管理相关方法 (约2500行)
void OnCreateActuatorGroup()                    // 创建作动器组
void OnCreateSensorGroup()                      // 创建传感器组
void CreateSensorGroup(groupName)               // 创建传感器组实现
void OnCreateActuator(groupItem)                // 创建作动器
void OnCreateSensor(groupItem)                  // 创建传感器
void CreateSensorDevice(groupItem, ...)        // 创建传感器设备
void CreateActuatorDeviceUI(serialNum, group)  // 创建作动器设备UI
void OnEditActuatorDevice(item)                 // 编辑作动器设备
void OnDeleteActuatorDevice(item)               // 删除作动器设备
void OnEditSensorDevice(item)                   // 编辑传感器设备
void OnDeleteSensorDevice(item)                 // 删除传感器设备
void OnCreateHardwareNode()                     // 创建硬件节点
void OnDeleteHardwareNode(item)                 // 删除硬件节点
void OnEditHardwareNode(item)                   // 编辑硬件节点
void CreateHardwareNodeInTree(params)           // 在树中创建硬件节点
bool SynchronizeAllDataManagers()               // 同步所有数据管理器
bool SynchronizeActuatorData()                  // 同步作动器数据
bool SynchronizeSensorData()                    // 同步传感器数据
bool SynchronizeHardwareNodeData()              // 同步硬件节点数据
```

---

## 📝 **阶段3: 树形控件管理功能迁移 → TreeManager**

### **🎯 迁移的方法列表**

```cpp
// 树形控件相关方法 (约2000行)
void InitializeHardwareTree()                   // 初始化硬件树
void InitializeTestConfigTree()                 // 初始化测试配置树
void OnHardwareTreeContextMenu(pos)             // 硬件树右键菜单
void OnTestConfigTreeContextMenu(pos)           // 测试配置树右键菜单
void OnTestConfigTreeItemDoubleClicked(item)    // 测试配置树双击
void OnTestConfigTreeItemClicked(item)          // 测试配置树单击
void HandleDragDropAssociation(...)             // 处理拖拽关联
void HandleDragDropAssociationWithParent(...)   // 处理带父项的拖拽关联
void EnableTestConfigTreeDragDrop()             // 启用测试配置树拖拽
void UpdateTreeDisplay()                        // 更新树显示
void RefreshHardwareTreeFromDataManagers()     // 从数据管理器刷新硬件树
void RefreshTestConfigTreeFromDataManagers()   // 从数据管理器刷新测试配置树
void UpdateTreeItemTooltipsRecursively(item)   // 递归更新树项工具提示
void UpdateAllTreeWidgetTooltips()             // 更新所有树控件工具提示
void ForceRestoreAllTreeColors()               // 强制恢复所有树颜色
bool canAcceptDrop(targetItem, sourceType)     // 检查是否可以接受拖拽
bool canDragItem(item)                          // 检查是否可以拖拽项
QString getItemType(item)                       // 获取项类型
```

---

## 📝 **阶段4: 对话框管理功能迁移 → DialogManager**

### **🎯 迁移的方法列表**

```cpp
// 对话框管理相关方法 (约1500行)
void OnCreateControlChannel(parentItem)         // 创建控制通道对话框
void OnDeleteControlChannel(item)               // 删除控制通道对话框
void OnEditControlChannelDetailed(item)         // 编辑控制通道详细对话框
void OnConfigureNodeLD_B1()                     // 配置节点LD_B1对话框
void OnConfigureNodeLD_B2()                     // 配置节点LD_B2对话框
void OnClearAssociation(item)                   // 清除关联对话框
void OnClearSingleAssociation(item)             // 清除单个关联对话框
void OnClearAllAssociation(item)                // 清除所有关联对话框
```

---

## 📝 **阶段5: 导入导出功能迁移 → ExportManager**

### **🎯 迁移的方法列表**

```cpp
// 导入导出相关方法 (约1500行)
void exportProjectAsChannelConfig()             // 导出项目为通道配置
bool integrateChannelConfigToProject_1_2()     // 集成通道配置到项目
QJsonObject createControlChannelGroupsJson_1_2() // 创建控制通道组JSON
void SynchronizeJSONExporterWithDataManagers() // 同步JSON导出器
void initializeXLSExporter()                   // 初始化XLS导出器
void initializeJSONExporter()                  // 初始化JSON导出器
void configureXLSExporterOptions(exporter)     // 配置XLS导出器选项
void handleExportResult(success, filePath, ...)// 处理导出结果
```

---

## 📝 **阶段6: 信息面板管理功能迁移 → InfoPanelManager**

### **🎯 迁移的方法列表**

```cpp
// 信息面板相关方法 (约1000行)
void OnShowDetailInfoDlgChanged(checked)       // 显示详细信息对话框变化
QString generateNodeDetailInfo(item)           // 生成节点详细信息
QString GenerateDetailedAssociationInfo(...)   // 生成详细关联信息
void UpdateNodeTooltipAfterDragDrop(...)       // 拖拽后更新节点工具提示
void UpdateSingleActuatorTooltip(serialNum, groupId) // 更新单个作动器工具提示
void UpdateSingleNodeTooltip(item)             // 更新单个节点工具提示
```

---

## 📝 **阶段7: 日志管理功能迁移 → LogManager**

### **🎯 迁移的方法列表**

```cpp
// 日志管理相关方法 (约500行)
void AddLogEntry(level, message)               // 添加日志条目
void LogMessage(level, message)                // 记录消息
void OnClearLog()                              // 清除日志
void OnSaveLog()                               // 保存日志
void AddControlActuatorDebugInfo(...)          // 添加控制作动器调试信息
```

---

## 📝 **阶段8: 事件处理功能迁移 → EventManager**

### **🎯 迁移的方法列表**

```cpp
// 事件处理相关方法 (约500行)
void ConnectSignals()                          // 连接信号
void ConnectUISignals()                        // 连接UI信号
void connectManagerSignals()                   // 连接管理器信号
void connectActuatorViewModelSignals()         // 连接作动器视图模型信号
void OnUpdateStatus()                          // 更新状态事件
```

---

## 🚀 **实施步骤**

### **第一步: 准备工作**
1. ✅ 备份当前主界面代码
2. ✅ 确认所有管理器已正确初始化
3. ✅ 确认依赖注入正常工作

### **第二步: 逐个管理器迁移**
1. **ProjectManager功能迁移** (最高优先级)
2. **DeviceManager功能迁移** (最高优先级)  
3. **TreeManager功能迁移** (最高优先级)
4. **DialogManager功能迁移** (最高优先级)
5. **ExportManager功能迁移** (最高优先级)
6. **InfoPanelManager功能迁移** (高优先级)
7. **LogManager功能迁移** (高优先级)
8. **EventManager功能迁移** (高优先级)

### **第三步: 主界面代码清理**
1. 移除已迁移的方法实现
2. 保留方法声明，改为调用管理器
3. 清理未使用的include和变量
4. 验证代码行数减少到500行左右

### **第四步: 测试验证**
1. 编译测试
2. 功能测试
3. 性能测试
4. 用户界面一致性测试

---

## 📊 **预期成果**

| 指标 | 当前状态 | 迁移后目标 | 改善程度 |
|------|----------|------------|----------|
| **主界面代码行数** | 11,549行 | 500行 | 减少95% |
| **功能模块化程度** | 10% | 95% | 提升85% |
| **代码可维护性** | 低 | 高 | 显著提升 |
| **功能扩展性** | 低 | 高 | 显著提升 |

---

## ⚠️ **注意事项**

1. **保持用户界面一致性** - 用户操作体验不变
2. **确保向下兼容** - 所有现有功能正常工作
3. **逐步迁移** - 每完成一个管理器就进行测试
4. **代码质量** - 遵循现代C++和Qt最佳实践
5. **性能考虑** - 确保迁移后性能不下降

---

**开始实施时间**: 2024-12-19  
**预计完成时间**: 2024-12-19 (当日完成)  
**责任人**: AI Assistant  
**状态**: 🚀 **准备开始实施** 