#ifndef SENSOREXCELEXTENSIONS_1_2_H
#define SENSOREXCELEXTENSIONS_1_2_H

/**
 * @file SensorExcelExtensions_1_2.h
 * @brief 传感器扩展Excel导入导出功能
 * @details 支持新传感器参数结构(zero_offset, params等)的Excel导入导出
 * <AUTHOR> Assistant
 * @date 2025-08-23
 * @version 1.2.0
 */

#include "SensorDialog_1_2.h"
#include <QString>
#include <QList>

namespace QXlsx {
    class Document;
}

/**
 * @brief 传感器Excel扩展功能类
 * @details 专门处理包含新参数的传感器数据Excel导入导出
 */
class SensorExcelExtensions_1_2 {
public:
    /**
     * @brief 导出传感器详细参数到Excel（包含组信息和新增字段）
     * @param sensorGroups 传感器组列表
     * @param filePath Excel文件路径
     * @return 导出是否成功
     */
    static bool exportEnhancedSensorDetails(const QList<UI::SensorGroup_1_2>& sensorGroups, const QString& filePath);
    
    /**
     * @brief 从Excel文件导入传感器详细参数（包含组信息和新增字段）
     * @param filePath Excel文件路径
     * @return 导入的传感器组列表
     */
    static QList<UI::SensorGroup_1_2> importEnhancedSensorDetails(const QString& filePath);
    
    /**
     * @brief 创建传感器详细配置工作表（包含组信息）
     * @param document Excel文档指针
     * @param sensorGroups 传感器组列表
     * @return 创建是否成功
     */
    static bool createSensorDetailWorksheet(QXlsx::Document* document, const QList<UI::SensorGroup_1_2>& sensorGroups);

    /**
     * @brief 从传感器详细配置工作表读取数据
     * @param document Excel文档指针
     * @param sensorGroups 传感器组列表（输出参数）
     * @return 读取是否成功
     */
    static bool readSensorDetailWorksheet(QXlsx::Document* document, QList<UI::SensorGroup_1_2>& sensorGroups);

    /**
     * @brief 创建空的传感器配置模板
     * @param filePath Excel文件路径
     * @return 创建是否成功
     */
    static bool createEmptyTemplate(const QString& filePath);

    /**
     * @brief 获取最后的错误信息
     * @return 错误信息字符串
     */
    static QString getLastError();

private:
    static QString lastError_;
    
    /**
     * @brief 设置错误信息
     * @param error 错误信息
     */
    static void setError(const QString& error);
    
    /**
     * @brief 清空错误信息
     */
    static void clearError();
};

#endif // SENSOREXCELEXTENSIONS_1_2_H