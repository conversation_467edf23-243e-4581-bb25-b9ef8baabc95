#include <QCoreApplication>
#include <QDebug>
#include <QDir>
#include <QFile>
#include <QTextStream>
#include <QJsonObject>
#include <QJsonDocument>
#include <QJsonArray>
#include <QDateTime>

// 模拟HtmlFrameworkLoader类
class MockHtmlFrameworkLoader {
public:
    static QString loadHtmlFramework(const QString& templateName);
    static QString loadFromQtResource(const QString& templateName);
    static QString loadFromFileSystem(const QString& templateName);
    static QString getResourcePath(const QString& relativePath);
    static QString generateFallbackHtml();
    
private:
    static QStringList getPossiblePaths(const QString& templateName);
    static QString loadTemplateFromFile(const QString& filePath);
};

QString MockHtmlFrameworkLoader::loadHtmlFramework(const QString& templateName)
{
    qDebug() << "=== 测试HTML模板加载:" << templateName << "===";
    
    // 1. 优先使用Qt资源系统
    QString htmlContent = loadFromQtResource(templateName);
    
    // 2. 如果失败，使用文件系统
    if (htmlContent.isEmpty()) {
        qDebug() << "Qt资源系统加载失败，尝试文件系统...";
        htmlContent = loadFromFileSystem(templateName);
    }
    
    // 3. 如果仍然失败，生成备用HTML
    if (htmlContent.isEmpty()) {
        qWarning() << "所有加载方式都失败，使用备用HTML";
        htmlContent = generateFallbackHtml();
    }
    
    qDebug() << "最终加载结果:" << (htmlContent.isEmpty() ? "失败" : "成功");
    qDebug() << "内容长度:" << htmlContent.length();
    
    return htmlContent;
}

QString MockHtmlFrameworkLoader::loadFromQtResource(const QString& templateName)
{
    QString resourcePath = QString(":/templates/%1").arg(templateName);
    qDebug() << "尝试从Qt资源系统加载:" << resourcePath;
    
    // 模拟Qt资源系统（实际项目中这里会真正访问资源）
    // 由于这是测试程序，我们直接返回空字符串表示失败
    qDebug() << "Qt资源系统加载失败（模拟）";
    return QString();
}

QString MockHtmlFrameworkLoader::loadFromFileSystem(const QString& templateName)
{
    QStringList possiblePaths = getPossiblePaths(templateName);
    
    for (const QString& filePath : possiblePaths) {
        qDebug() << "尝试文件路径:" << filePath;
        QString htmlContent = loadTemplateFromFile(filePath);
        if (!htmlContent.isEmpty()) {
            qDebug() << "文件系统加载成功:" << filePath;
            return htmlContent;
        }
    }
    
    qWarning() << "文件系统加载失败，尝试的路径:" << possiblePaths;
    return QString();
}

QStringList MockHtmlFrameworkLoader::getPossiblePaths(const QString& templateName)
{
    QStringList paths;
    
    // 基础路径
    paths.append(getResourcePath(QString("Res/%1").arg(templateName)));
    
    // 备用路径
    paths.append(getResourcePath(QString("../Res/%1").arg(templateName)));
    paths.append(getResourcePath(QString("../../Res/%1").arg(templateName)));
    paths.append(QDir::currentPath() + "/Res/" + templateName);
    paths.append(QDir::currentPath() + "/../Res/" + templateName);
    
    return paths;
}

QString MockHtmlFrameworkLoader::getResourcePath(const QString& relativePath)
{
    QStringList possiblePaths;
    
    // 1. 应用程序目录
    QString appDir = QCoreApplication::applicationDirPath();
    possiblePaths.append(QDir(appDir).absoluteFilePath(relativePath));
    
    // 2. 当前工作目录
    possiblePaths.append(QDir::currentPath() + "/" + relativePath);
    
    // 3. 项目源码目录（开发环境）
    QString projectDir = QDir::currentPath();
    if (projectDir.contains("SiteResConfig")) {
        int index = projectDir.indexOf("SiteResConfig");
        QString baseDir = projectDir.left(index + QString("SiteResConfig").length());
        possiblePaths.append(QDir(baseDir).absoluteFilePath(relativePath));
    }
    
    // 4. 上级目录
    possiblePaths.append(QDir::currentPath() + "/../" + relativePath);
    
    // 返回第一个存在的路径
    for (const QString& path : possiblePaths) {
        if (QFile::exists(path)) {
            qDebug() << "找到资源文件:" << path;
            return path;
        }
    }
    
    // 如果都不存在，返回第一个路径（用于调试）
    qWarning() << "资源文件不存在，尝试的路径:" << possiblePaths;
    return possiblePaths.first();
}

QString MockHtmlFrameworkLoader::loadTemplateFromFile(const QString& filePath)
{
    QFile file(filePath);
    if (file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        QTextStream stream(&file);
        stream.setCodec("UTF-8");
        QString content = stream.readAll();
        file.close();
        return content;
    }
    return QString();
}

QString MockHtmlFrameworkLoader::generateFallbackHtml()
{
    return QString(
        "<!DOCTYPE html>\n"
        "<html lang='zh-CN'>\n"
        "<head>\n"
        "    <meta charset='UTF-8'>\n"
        "    <title>设备详细信息</title>\n"
        "    <style>\n"
        "        body { \n"
        "            font-family: 'Microsoft YaHei UI', 'Segoe UI', sans-serif; \n"
        "            margin: 0; padding: 20px; \n"
        "            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n"
        "            min-height: 100vh;\n"
        "        }\n"
        "        .container { \n"
        "            max-width: 1200px; \n"
        "            margin: 0 auto; \n"
        "            background: white; \n"
        "            border-radius: 15px; \n"
        "            box-shadow: 0 20px 40px rgba(0,0,0,0.1); \n"
        "            overflow: hidden; \n"
        "        }\n"
        "        .header { \n"
        "            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); \n"
        "            color: white; \n"
        "            padding: 30px; \n"
        "            text-align: center; \n"
        "        }\n"
        "        .content { padding: 30px; }\n"
        "        .error-message { \n"
        "            text-align: center; \n"
        "            color: #e74c3c; \n"
        "            font-size: 1.2em; \n"
        "            padding: 40px; \n"
        "        }\n"
        "    </style>\n"
        "</head>\n"
        "<body>\n"
        "    <div class='container'>\n"
        "        <div class='header'>\n"
        "            <h1>📋 设备详细信息</h1>\n"
        "            <div class='subtitle'>HTML模板加载中...</div>\n"
        "        </div>\n"
        "        <div class='content'>\n"
        "            <div class='error-message'>\n"
        "                <h3>⚠️ 模板加载提示</h3>\n"
        "                <p>正在加载设备详细信息模板，请稍候...</p>\n"
        "                <p>如果长时间未显示，请检查资源文件配置</p>\n"
        "            </div>\n"
        "        </div>\n"
        "    </div>\n"
        "</body>\n"
        "</html>"
    );
}

// 测试函数
void testResourcePathResolution() {
    qDebug() << "\n=== 测试资源路径解析 ===";
    
    QStringList testPaths = {
        "Res/detail_panel.html",
        "../Res/detail_panel.html",
        "Res/style.css"
    };
    
    for (const QString& testPath : testPaths) {
        QString resolvedPath = MockHtmlFrameworkLoader::getResourcePath(testPath);
        qDebug() << "测试路径:" << testPath;
        qDebug() << "解析结果:" << resolvedPath;
        qDebug() << "文件存在:" << QFile::exists(resolvedPath);
        qDebug() << "---";
    }
}

void testTemplateLoading() {
    qDebug() << "\n=== 测试模板加载 ===";
    
    QStringList testTemplates = {
        "detail_panel.html",
        "nonexistent.html"
    };
    
    for (const QString& template : testTemplates) {
        qDebug() << "\n--- 测试模板:" << template << "---";
        QString result = MockHtmlFrameworkLoader::loadHtmlFramework(template);
        
        if (!result.isEmpty()) {
            qDebug() << "✅ 模板加载成功";
            qDebug() << "内容预览:" << result.left(100) << "...";
        } else {
            qDebug() << "❌ 模板加载失败";
        }
    }
}

void testConfigurationFile() {
    qDebug() << "\n=== 测试配置文件 ===";
    
    QString configPath = MockHtmlFrameworkLoader::getResourcePath("Res/resource_config.json");
    qDebug() << "配置文件路径:" << configPath;
    
    if (QFile::exists(configPath)) {
        QFile configFile(configPath);
        if (configFile.open(QIODevice::ReadOnly | QIODevice::Text)) {
            QTextStream stream(&configFile);
            stream.setCodec("UTF-8");
            QString configContent = stream.readAll();
            configFile.close();
            
            QJsonDocument doc = QJsonDocument::fromJson(configContent.toUtf8());
            if (doc.isObject()) {
                qDebug() << "✅ 配置文件加载成功";
                QJsonObject config = doc.object();
                
                if (config.contains("loading_strategy")) {
                    QJsonObject strategy = config["loading_strategy"].toObject();
                    qDebug() << "主要加载策略:" << strategy["primary"].toString();
                    qDebug() << "备用加载策略:" << strategy["fallback"].toString();
                    qDebug() << "缓存启用:" << strategy["cache_enabled"].toBool();
                }
            } else {
                qDebug() << "❌ 配置文件格式错误";
            }
        } else {
            qDebug() << "❌ 无法读取配置文件";
        }
    } else {
        qDebug() << "❌ 配置文件不存在";
    }
}

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "🚀 HTML模板加载修复测试程序";
    qDebug() << "当前工作目录:" << QDir::currentPath();
    qDebug() << "应用程序目录:" << QCoreApplication::applicationDirPath();
    
    // 运行测试
    testResourcePathResolution();
    testTemplateLoading();
    testConfigurationFile();
    
    qDebug() << "\n🎉 测试完成！";
    
    return 0;
} 