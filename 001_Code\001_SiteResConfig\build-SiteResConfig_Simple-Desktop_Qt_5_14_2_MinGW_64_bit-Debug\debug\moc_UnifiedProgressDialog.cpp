/****************************************************************************
** Meta object code from reading C++ file 'UnifiedProgressDialog.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../001_SiteResConfig_OldStruct/SiteResConfig/include/UnifiedProgressDialog.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'UnifiedProgressDialog.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_UnifiedProgressDialog_t {
    QByteArrayData data[16];
    char stringdata0[244];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_UnifiedProgressDialog_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_UnifiedProgressDialog_t qt_meta_stringdata_UnifiedProgressDialog = {
    {
QT_MOC_LITERAL(0, 0, 21), // "UnifiedProgressDialog"
QT_MOC_LITERAL(1, 22, 17), // "continueRequested"
QT_MOC_LITERAL(2, 40, 0), // ""
QT_MOC_LITERAL(3, 41, 14), // "pauseRequested"
QT_MOC_LITERAL(4, 56, 15), // "cancelRequested"
QT_MOC_LITERAL(5, 72, 16), // "operationAborted"
QT_MOC_LITERAL(6, 89, 17), // "onContinueClicked"
QT_MOC_LITERAL(7, 107, 14), // "onPauseClicked"
QT_MOC_LITERAL(8, 122, 15), // "onCancelClicked"
QT_MOC_LITERAL(9, 138, 17), // "updateElapsedTime"
QT_MOC_LITERAL(10, 156, 16), // "onDetailsToggled"
QT_MOC_LITERAL(11, 173, 12), // "setAutoClose"
QT_MOC_LITERAL(12, 186, 6), // "enable"
QT_MOC_LITERAL(13, 193, 7), // "seconds"
QT_MOC_LITERAL(14, 201, 19), // "startAutoCloseTimer"
QT_MOC_LITERAL(15, 221, 22) // "updateAutoCloseDisplay"

    },
    "UnifiedProgressDialog\0continueRequested\0"
    "\0pauseRequested\0cancelRequested\0"
    "operationAborted\0onContinueClicked\0"
    "onPauseClicked\0onCancelClicked\0"
    "updateElapsedTime\0onDetailsToggled\0"
    "setAutoClose\0enable\0seconds\0"
    "startAutoCloseTimer\0updateAutoCloseDisplay"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_UnifiedProgressDialog[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      13,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       4,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    0,   79,    2, 0x06 /* Public */,
       3,    0,   80,    2, 0x06 /* Public */,
       4,    0,   81,    2, 0x06 /* Public */,
       5,    0,   82,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       6,    0,   83,    2, 0x08 /* Private */,
       7,    0,   84,    2, 0x08 /* Private */,
       8,    0,   85,    2, 0x08 /* Private */,
       9,    0,   86,    2, 0x08 /* Private */,
      10,    0,   87,    2, 0x08 /* Private */,
      11,    2,   88,    2, 0x0a /* Public */,
      11,    1,   93,    2, 0x2a /* Public | MethodCloned */,
      14,    0,   96,    2, 0x0a /* Public */,
      15,    0,   97,    2, 0x0a /* Public */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Bool, QMetaType::Int,   12,   13,
    QMetaType::Void, QMetaType::Bool,   12,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void UnifiedProgressDialog::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<UnifiedProgressDialog *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->continueRequested(); break;
        case 1: _t->pauseRequested(); break;
        case 2: _t->cancelRequested(); break;
        case 3: _t->operationAborted(); break;
        case 4: _t->onContinueClicked(); break;
        case 5: _t->onPauseClicked(); break;
        case 6: _t->onCancelClicked(); break;
        case 7: _t->updateElapsedTime(); break;
        case 8: _t->onDetailsToggled(); break;
        case 9: _t->setAutoClose((*reinterpret_cast< bool(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 10: _t->setAutoClose((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 11: _t->startAutoCloseTimer(); break;
        case 12: _t->updateAutoCloseDisplay(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (UnifiedProgressDialog::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&UnifiedProgressDialog::continueRequested)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (UnifiedProgressDialog::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&UnifiedProgressDialog::pauseRequested)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (UnifiedProgressDialog::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&UnifiedProgressDialog::cancelRequested)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (UnifiedProgressDialog::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&UnifiedProgressDialog::operationAborted)) {
                *result = 3;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject UnifiedProgressDialog::staticMetaObject = { {
    QMetaObject::SuperData::link<QDialog::staticMetaObject>(),
    qt_meta_stringdata_UnifiedProgressDialog.data,
    qt_meta_data_UnifiedProgressDialog,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *UnifiedProgressDialog::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *UnifiedProgressDialog::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_UnifiedProgressDialog.stringdata0))
        return static_cast<void*>(this);
    return QDialog::qt_metacast(_clname);
}

int UnifiedProgressDialog::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QDialog::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 13)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 13;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 13)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 13;
    }
    return _id;
}

// SIGNAL 0
void UnifiedProgressDialog::continueRequested()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void UnifiedProgressDialog::pauseRequested()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void UnifiedProgressDialog::cancelRequested()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void UnifiedProgressDialog::operationAborted()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
