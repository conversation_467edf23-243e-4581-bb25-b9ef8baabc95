@echo off
echo Searching for Qt installation...
echo.

echo Checking common Qt installation paths:
echo.

if exist "C:\Qt\5.14.2\mingw73_32\bin\qmake.exe" (
    echo [FOUND] C:\Qt\5.14.2\mingw73_32\bin\qmake.exe
    echo QTDIR should be: C:\Qt\5.14.2\mingw73_32
    echo MINGW should be: C:\Qt\Tools\mingw730_32\bin
    echo.
)

if exist "C:\Qt\Qt5.14.2\5.14.2\mingw73_32\bin\qmake.exe" (
    echo [FOUND] C:\Qt\Qt5.14.2\5.14.2\mingw73_32\bin\qmake.exe
    echo QTDIR should be: C:\Qt\Qt5.14.2\5.14.2\mingw73_32
    echo MINGW should be: C:\Qt\Qt5.14.2\Tools\mingw730_32\bin
    echo.
)

if exist "D:\Qt\5.14.2\mingw73_32\bin\qmake.exe" (
    echo [FOUND] D:\Qt\5.14.2\mingw73_32\bin\qmake.exe
    echo QTDIR should be: D:\Qt\5.14.2\mingw73_32
    echo MINGW should be: D:\Qt\Tools\mingw730_32\bin
    echo.
)

if exist "C:\Qt\5.14.0\mingw73_32\bin\qmake.exe" (
    echo [FOUND] C:\Qt\5.14.0\mingw73_32\bin\qmake.exe
    echo QTDIR should be: C:\Qt\5.14.0\mingw73_32
    echo MINGW should be: C:\Qt\Tools\mingw730_32\bin
    echo.
)

if exist "C:\Qt\5.15.2\mingw81_32\bin\qmake.exe" (
    echo [FOUND] C:\Qt\5.15.2\mingw81_32\bin\qmake.exe
    echo QTDIR should be: C:\Qt\5.15.2\mingw81_32
    echo MINGW should be: C:\Qt\Tools\mingw810_32\bin
    echo.
)

echo.
echo If Qt is installed in a different location, please:
echo 1. Find your Qt installation directory
echo 2. Look for qmake.exe in the bin subfolder
echo 3. Update the paths in find_qt_and_compile.bat
echo.
echo Manual compilation commands:
echo   set QTDIR=your_qt_path
echo   set PATH=%%QTDIR%%\bin;your_mingw_path\bin;%%PATH%%
echo   cd SiteResConfig
echo   qmake SiteResConfig_Simple.pro -spec win32-g++
echo   mingw32-make -j4
echo.

pause
