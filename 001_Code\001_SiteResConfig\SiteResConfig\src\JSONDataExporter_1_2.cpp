#include "JSONDataExporter_1_2.h"
#include "SensorDataManager_1_2.h"
#include "ActuatorDataManager_1_2.h"  // 🆕 新增：作动器数据管理器
#include "CtrlChanDataManager.h"  // 🆕 新增：控制通道数据管理器
#include <QTreeWidget>
#include <QTreeWidgetItem>
#include <QJsonDocument>
#include <QJsonArray>
#include <QJsonObject>
#include <QFile>
#include <QFileInfo>
#include <QDir>
#include <QDateTime>

JSONDataExporter_1_2::JSONDataExporter_1_2(SensorDataManager_1_2* sensorManager)
    : sensorDataManager_(sensorManager)
    , xlsExporter_(nullptr)
    , ctrlChanDataManager_(nullptr)  // 🆕 初始化控制通道数据管理器指针
    , compactFormat_(false)
    , indentSize_(2) {
}

bool JSONDataExporter_1_2::exportCompleteProject(const QString& filePath) {
    clearError();

    if (!xlsExporter_) {
        setError(QString(u8"XLS导出器未设置，无法收集完整项目数据"));
        return false;
    }

    try {
        // 收集完整项目数据
        ProjectData projectData = collectProjectData();

        // 导出为JSON
        return exportToJSON(projectData, filePath);

    } catch (const std::exception& e) {
        setError(QString(u8"导出过程中发生异常: %1").arg(e.what()));
        return false;
    } catch (...) {
        setError(QString(u8"导出过程中发生未知异常"));
        return false;
    }
}

QString JSONDataExporter_1_2::getSupportedExtension() const {
    return "json";
}

QString JSONDataExporter_1_2::getFormatDescription() const {
    return "JavaScript Object Notation (*.json)";
}

QString JSONDataExporter_1_2::getLastError() const {
    return lastError_;
}

void JSONDataExporter_1_2::setCompactFormat(bool compact) {
    compactFormat_ = compact;
}

void JSONDataExporter_1_2::setIndentSize(int indentSize) {
    indentSize_ = qMax(0, indentSize);
}

void JSONDataExporter_1_2::clearError() {
    lastError_.clear();
}

void JSONDataExporter_1_2::setError(const QString& error) {
    lastError_ = error;
}

bool JSONDataExporter_1_2::writeJsonToFile(const QJsonDocument& document, const QString& filePath) {
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        setError(QString(u8"无法创建文件: %1").arg(file.errorString()));
        return false;
    }
    
    QByteArray jsonData;
    if (compactFormat_) {
        jsonData = document.toJson(QJsonDocument::Compact);
    } else {
        jsonData = document.toJson(QJsonDocument::Indented);
    }
    
    if (file.write(jsonData) == -1) {
        setError(QString(u8"写入文件失败: %1").arg(file.errorString()));
        return false;
    }
    
    return true;
}

QString JSONDataExporter_1_2::getItemType(QTreeWidgetItem* item) const {
    if (!item) return QString();

    QVariant typeData = item->data(0, Qt::UserRole);
    if (typeData.isValid()) {
        return typeData.toString();
    }

    // 如果没有设置UserRole，尝试从文本推断
    QString text = item->text(0);
    if (text.contains(QStringLiteral("传感器")) && text.contains(QStringLiteral("组"))) {
        return QStringLiteral("传感器组");
    } else if (text.contains(QStringLiteral("作动器")) && text.contains(QStringLiteral("组"))) {
        return QStringLiteral("作动器组");
    } else if (text.contains(QStringLiteral("硬件节点"))) {
        return QStringLiteral("硬件节点资源");
    }

    return QString();
}

QString JSONDataExporter_1_2::extractParameterFromTooltip(const QString& tooltip, const QString& paramName) const {
    if (tooltip.isEmpty() || paramName.isEmpty()) {
        return QString();
    }

    // 查找参数名称
    QString searchPattern = paramName + ": ";
    int startIndex = tooltip.indexOf(searchPattern);
    if (startIndex == -1) {
        return QString();
    }

    startIndex += searchPattern.length();

    // 查找参数值的结束位置（换行符或字符串结尾）
    int endIndex = tooltip.indexOf('\n', startIndex);
    if (endIndex == -1) {
        endIndex = tooltip.length();
    }

    return tooltip.mid(startIndex, endIndex - startIndex).trimmed();
}

// ============================================================================
// 🆕 新增：完整项目导出功能
// ============================================================================

void JSONDataExporter_1_2::setXLSExporter(XLSDataExporter_1_2* xlsExporter) {
    xlsExporter_ = xlsExporter;
}

// 🆕 新增：设置控制通道数据管理器
void JSONDataExporter_1_2::setCtrlChanDataManager(CtrlChanDataManager* ctrlChanManager) {
    ctrlChanDataManager_ = ctrlChanManager;
}

bool JSONDataExporter_1_2::importToHardwareTree(const QString& filePath, QTreeWidget* treeWidget) {
    // JSON导入功能暂未实现
    Q_UNUSED(filePath);
    Q_UNUSED(treeWidget);
    setError(QString(u8"JSON导入功能暂未实现"));
    return false;
}

JSONDataExporter_1_2::ProjectData JSONDataExporter_1_2::collectProjectData()
{
    ProjectData data;
    data.hardwareTree = nullptr; // 🚫 不导出硬件树
    data.projectName = "SiteResConfig项目";
    data.exportTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
    data.version = "1.0.0";

    // 🆕 修改：只从内存数据管理器获取数据，不从界面获取
    if (xlsExporter_) {
        // 获取传感器数据（从SensorDataManager）
        if (xlsExporter_->getSensorDataManager()) {
            data.sensorGroups = xlsExporter_->getSensorDataManager()->getAllSensorGroups();
        }

        // 获取作动器数据（从ActuatorDataManager）
        if (xlsExporter_->getActuatorDataManager()) {
            data.actuatorGroups = xlsExporter_->getActuatorDataManager()->getAllActuatorGroups();
        }

        // 获取控制通道数据（从CtrlChanDataManager）
        if (xlsExporter_->getCtrlChanDataManager()) {
            data.controlChannelGroups = xlsExporter_->getCtrlChanDataManager()->getAllControlChannelGroups();
        }

        // 获取硬件节点数据（从内存配置）
        data.hardwareNodes = xlsExporter_->getHardwareNodeConfigs();
    }

    return data;
}

bool JSONDataExporter_1_2::exportToJSON(const ProjectData& projectData, const QString& filePath) {
    clearError();

    try {
        // 创建导出的JSON对象
        QJsonObject rootObject;

        // 项目元数据（第一个字段）
        QJsonObject projectInfo;
        projectInfo["title"] = QString(u8"实验工程配置文件");
        projectInfo["description"] = QString(u8"完整项目配置");
        projectInfo["exportTime"] = projectData.exportTime;
        projectInfo["format"] = "JSON";
        projectInfo["version"] = projectData.version;
        rootObject["projectInfo"] = projectInfo;

        // 作动器详细配置（第二个字段）
        if (!projectData.actuatorGroups.isEmpty()) {
            QJsonObject actuatorSheet;
            actuatorSheet["title"] = QString(u8"作动器详细配置");
            actuatorSheet["description"] = QString(u8"包含作动器组及其作动器的完整配置信息");
            actuatorSheet["data"] = createActuatorGroupsJson(projectData.actuatorGroups);
            rootObject["actuatorDetails"] = actuatorSheet;
        }

        // 传感器详细配置（第三个字段）
        if (!projectData.sensorGroups.isEmpty()) {
            QJsonObject sensorSheet;
            sensorSheet["title"] = QString(u8"传感器详细配置");
            sensorSheet["description"] = QString(u8"包含传感器组及其传感器的完整配置信息");
            sensorSheet["data"] = createSensorGroupsJson(projectData.sensorGroups);
            rootObject["sensorDetails"] = sensorSheet;
        }

        // 硬件节点详细配置（第四个字段）
        if (!projectData.hardwareNodes.isEmpty()) {
            QJsonObject nodeSheet;
            nodeSheet["title"] = QString(u8"硬件节点详细配置");
            nodeSheet["description"] = QString(u8"包含硬件节点的网络配置信息");
            nodeSheet["data"] = createHardwareNodesJson(projectData.hardwareNodes);
            rootObject["hardwareNodeDetails"] = nodeSheet;
        }

        // 控制通道详细配置（第五个字段，最后添加）
        if (!projectData.controlChannelGroups.isEmpty()) {
            QJsonObject channelSheet;
            channelSheet["title"] = QString(u8"控制通道详细配置");
            channelSheet["description"] = QString(u8"包含控制通道的关联信息与配置参数");
            channelSheet["data"] = createControlChannelGroupsJson(projectData.controlChannelGroups);
            rootObject["controlChannelDetails"] = channelSheet;
        }

        // 保存到文件
        QJsonDocument doc(rootObject);
        return writeJsonToFile(doc, filePath);

    } catch (const std::exception& e) {
        setError(QString(u8"JSON导出异常: %1").arg(e.what()));
        return false;
    } catch (...) {
        setError(QString(u8"JSON导出发生未知异常"));
        return false;
    }
}

// 🚫 已注释：冗余的完整项目导出功能，已被exportChannelConfig()替代
//bool JSONDataExporter_1_2::exportToJSON_1_2(const QString& filePath) {
//    clearError();
//    
//    if (!xlsExporter_) {
//        setError(QString(u8"XLS导出器未设置，无法收集完整项目数据"));
//        return false;
//    }
//
//    try {
//        // 收集完整项目数据
//        ProjectData projectData = collectProjectData();
//
//        // 导出为JSON（增强版）
//        return exportToJSON_1_2(projectData, filePath);
//
//    } catch (const std::exception& e) {
//        setError(QString(u8"导出过程中发生异常: %1").arg(e.what()));
//        return false;
//    } catch (...) {
//        setError(QString(u8"导出过程中发生未知异常"));
//        return false;
//    }
//}

//bool JSONDataExporter_1_2::exportToJSON_1_2(const ProjectData& projectData, const QString& filePath) {
//    clearError();
//
//    try {
//        // 创建导出的JSON对象（增强版）
//        QJsonObject rootObject;
//
//        // 项目元数据（第一个字段）
//        QJsonObject projectInfo;
//        projectInfo["title"] = QString(u8"实验工程配置文件");
//        projectInfo["description"] = QString(u8"完整项目配置");
//        projectInfo["exportTime"] = projectData.exportTime;
//        projectInfo["format"] = "JSON";
//        projectInfo["version"] = projectData.version;
//        rootObject["projectInfo"] = projectInfo;
//
//        // 作动器详细配置（第二个字段）
//        if (!projectData.actuatorGroups.isEmpty()) {
//            QJsonObject actuatorSheet;
//            actuatorSheet["title"] = QString(u8"作动器详细配置");
//            actuatorSheet["description"] = QString(u8"包含作动器组及其作动器的完整配置信息");
//            actuatorSheet["data"] = createActuatorGroupsJson(projectData.actuatorGroups);
//            rootObject["actuatorDetails"] = actuatorSheet;
//        }
//
//        // 传感器详细配置（第三个字段）
//        if (!projectData.sensorGroups.isEmpty()) {
//            QJsonObject sensorSheet;
//            sensorSheet["title"] = QString(u8"传感器详细配置");
//            sensorSheet["description"] = QString(u8"包含传感器组及其传感器的完整配置信息");
//            sensorSheet["data"] = createSensorGroupsJson(projectData.sensorGroups);
//            rootObject["sensorDetails"] = sensorSheet;
//        }
//
//        // 硬件节点详细配置（第四个字段）
//        if (!projectData.hardwareNodes.isEmpty()) {
//            QJsonObject nodeSheet;
//            nodeSheet["title"] = QString(u8"硬件节点详细配置");
//            nodeSheet["description"] = QString(u8"包含硬件节点的网络配置信息");
//            nodeSheet["data"] = createHardwareNodesJson(projectData.hardwareNodes);
//            rootObject["hardwareNodeDetails"] = nodeSheet;
//        }
//
//        // 控制通道详细配置（第五个字段，最后添加）
//        if (!projectData.controlChannelGroups.isEmpty()) {
//            QJsonObject channelSheet;
//            channelSheet["title"] = QString(u8"控制通道详细配置");
//            channelSheet["description"] = QString(u8"包含控制通道的关联信息与配置参数");
//            channelSheet["data"] = createControlChannelGroupsJson(projectData.controlChannelGroups);
//            rootObject["controlChannelDetails"] = channelSheet;
//        }
//
//        // 保存到文件
//        QJsonDocument doc(rootObject);
//        return writeJsonToFile(doc, filePath);
//
//    } catch (const std::exception& e) {
//        setError(QString(u8"JSON导出异常: %1").arg(e.what()));
//        return false;
//    } catch (...) {
//        setError(QString(u8"JSON导出过程中发生未知异常"));
//        return false;
//    }
//}

bool JSONDataExporter_1_2::exportChannelConfig(const QString& filePath) {
    clearError();
    
    try {
        // 使用现有的方法从内存中创建通道配置数据
        QJsonArray channelsArray = createChannelConfigDataFromMemory();
        
        // 创建根对象
        QJsonObject rootObject;
        rootObject["channels"] = channelsArray;
        
        // 创建JSON文档
        QJsonDocument jsonDoc(rootObject);
        
        // 写入文件
        return writeJsonToFile(jsonDoc, filePath);
        
    } catch (const std::exception& e) {
        setError(QString(u8"导出通道配置过程中发生异常: %1").arg(e.what()));
        return false;
    } catch (...) {
        setError(QString(u8"导出通道配置过程中发生未知异常"));
        return false;
    }
}

QJsonArray JSONDataExporter_1_2::createSensorGroupsJson(const QList<UI::SensorGroup_1_2>& sensorGroups) {
    QJsonArray groupsArray;

    // 🆕 修改：保持组结构，体现组名的层次
    for (int groupIndex = 0; groupIndex < sensorGroups.size(); ++groupIndex) {
        const UI::SensorGroup_1_2& group = sensorGroups[groupIndex];
        int displayGroupId = groupIndex + 1; // 显示序号从1开始连续递增

        // 🆕 修改：使用新的传感器参数结构创建JSON对象
        QJsonArray sensorsArray;
        for (const auto& sensor : group.sensors) {
            QJsonObject sensorObject;

            // 基本信息（与Excel保持一致）
            sensorObject["sensorId"] = sensor.sensorId;
            sensorObject["serialNumber"] = sensor.params_sn;      // 使用params_sn保持一致
            sensorObject["sensorType"] = sensor.params_model;     // 使用params_model保持一致
            
            // 🆕 核心控制参数（基础配置2字段）
            sensorObject["zero_offset"] = sensor.zero_offset;
            sensorObject["enable"] = sensor.enable;
            
            // 🆕 物理参数对象（params嵌套结构12字段）
            QJsonObject paramsObj;
            paramsObj["model"] = sensor.params_model;
            paramsObj["sn"] = sensor.params_sn;
            paramsObj["k"] = sensor.params_k;
            paramsObj["b"] = sensor.params_b;
            paramsObj["precision"] = sensor.params_precision;
            paramsObj["polarity"] = sensor.params_polarity;
            paramsObj["meas_unit"] = sensor.meas_unit;
            paramsObj["meas_range_min"] = sensor.meas_range_min;
            paramsObj["meas_range_max"] = sensor.meas_range_max;
            paramsObj["output_signal_unit"] = sensor.output_signal_unit;
            paramsObj["output_signal_range_min"] = sensor.output_signal_range_min;
            paramsObj["output_signal_range_max"] = sensor.output_signal_range_max;
            
            sensorObject["params"] = paramsObj;

            sensorsArray.append(sensorObject);
        }

        // 🆕 修改：在sensors数组准备好后，按照用户要求的顺序创建groupObject
        QJsonObject groupObject;
        groupObject["groupId"] = group.groupId;                // 1. 原始组ID
        groupObject["groupName"] = group.groupName;            // 2. 传感器组名称
        groupObject["groupNumber"] = displayGroupId;           // 3. 组序号
        groupObject["groupType"] = group.groupType;            // 4. 组类型
        groupObject["createTime"] = group.createTime;          // 5. 创建时间
        groupObject["sensors"] = sensorsArray;                 // 6. 传感器数组（最后添加）

        groupsArray.append(groupObject);
    }

    return groupsArray;
}

QJsonArray JSONDataExporter_1_2::createActuatorGroupsJson(const QList<UI::ActuatorGroup_1_2>& actuatorGroups) {
    QJsonArray groupsArray;

    // 🆕 修改：保持组结构，体现组名的层次
    for (int groupIndex = 0; groupIndex < actuatorGroups.size(); ++groupIndex) {
        const UI::ActuatorGroup_1_2& group = actuatorGroups[groupIndex];
        int displayGroupId = groupIndex + 1; // 显示序号从1开始连续递增

        // 🆕 更新：使用servo_control格式创建作动器JSON对象
        QJsonArray actuatorsArray;
        for (const auto& actuator : group.actuators) {
            // 使用新的servo_control格式转换方法
            QJsonObject servoControlObject = convertActuatorToServoControlJson(actuator);

            // 将servo_control对象包装，保持组结构
            QJsonObject actuatorWrapper;
            actuatorWrapper["servo_control"] = servoControlObject;

            actuatorsArray.append(actuatorWrapper);
        }

        // 🆕 修改：在actuators数组准备好后，按照用户要求的顺序创建groupObject
        QJsonObject groupObject;
        groupObject["groupId"] = group.groupId;                // 1. 原始组ID
        groupObject["groupName"] = group.groupName;            // 2. 作动器组名称
        groupObject["groupNumber"] = displayGroupId;           // 3. 组序号
        groupObject["groupType"] = group.groupType;            // 4. 组类型
        groupObject["createTime"] = group.createTime;          // 5. 创建时间
        groupObject["actuators"] = actuatorsArray;             // 6. 作动器数组（最后添加）

        groupsArray.append(groupObject);
    }

    return groupsArray;
}

// 🆕 新增：servo_control格式导出方法
bool JSONDataExporter_1_2::exportActuatorAsServoControl(const UI::ActuatorParams_1_2& actuator, const QString& filePath) {
    clearError();

    try {
        // 创建根对象
        QJsonObject rootObj;
        rootObj["servo_control"] = convertActuatorToServoControlJson(actuator);

        // 创建JSON文档
        QJsonDocument doc(rootObj);

        // 写入文件
        QFile file(filePath);
        if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
            setError(QString("无法创建文件: %1").arg(filePath));
            return false;
        }

        // 使用缩进格式写入
        QByteArray jsonData = doc.toJson(QJsonDocument::Indented);
        qint64 bytesWritten = file.write(jsonData);
        file.close();

        if (bytesWritten == -1) {
            setError(QString("写入文件失败: %1").arg(filePath));
            return false;
        }

        return true;

    } catch (const std::exception& e) {
        setError(QString("导出过程中发生异常: %1").arg(e.what()));
        return false;
    }
}

QJsonObject JSONDataExporter_1_2::convertActuatorToServoControlJson(const UI::ActuatorParams_1_2& actuator) {
    QJsonObject servoControl;

    // 基本信息
    servoControl["name"] = actuator.name;
    servoControl["type"] = static_cast<int>(actuator.type);
    servoControl["zero_offset"] = actuator.zero_offset;

    // 硬件配置
    servoControl["lc_id"] = actuator.lc_id;
    servoControl["station_id"] = actuator.station_id;
    servoControl["board_id_ao"] = actuator.board_id_ao;
    servoControl["board_type_ao"] = actuator.board_type_ao;
    servoControl["port_id_ao"] = actuator.port_id_ao;
    servoControl["board_id_do"] = actuator.board_id_do;
    servoControl["board_type_do"] = actuator.board_type_do;
    servoControl["port_id_do"] = actuator.port_id_do;

    // 详细参数
    QJsonObject paramsObj;
    paramsObj["model"] = actuator.params.model;
    paramsObj["sn"] = actuator.params.sn;
    paramsObj["k"] = actuator.params.k;
    paramsObj["b"] = actuator.params.b;
    paramsObj["precision"] = actuator.params.precision;
    paramsObj["polarity"] = static_cast<int>(actuator.params.polarity);
    paramsObj["meas_unit"] = static_cast<int>(actuator.params.meas_unit);
    paramsObj["meas_range_min"] = actuator.params.meas_range_min;
    paramsObj["meas_range_max"] = actuator.params.meas_range_max;
    paramsObj["output_signal_unit"] = actuator.params.output_signal_unit;
    paramsObj["output_signal_range_min"] = actuator.params.output_signal_range_min;
    paramsObj["output_signal_range_max"] = actuator.params.output_signal_range_max;

    servoControl["params"] = paramsObj;

    return servoControl;
}

UI::ActuatorParams_1_2 JSONDataExporter_1_2::importActuatorFromServoControl(const QString& filePath) {
    clearError();
    UI::ActuatorParams_1_2 actuator;

    try {
        // 读取文件
        QFile file(filePath);
        if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
            setError(QString("无法打开文件: %1").arg(filePath));
            return actuator;
        }

        QByteArray jsonData = file.readAll();
        file.close();

        // 解析JSON
        QJsonParseError parseError;
        QJsonDocument doc = QJsonDocument::fromJson(jsonData, &parseError);

        if (parseError.error != QJsonParseError::NoError) {
            setError(QString("JSON解析错误: %1").arg(parseError.errorString()));
            return actuator;
        }

        if (!doc.isObject()) {
            setError("JSON根节点不是对象");
            return actuator;
        }

        QJsonObject rootObj = doc.object();

        // 验证格式
        if (!rootObj.contains("servo_control")) {
            setError("JSON中缺少servo_control节点");
            return actuator;
        }

        QJsonObject servoControlObj = rootObj["servo_control"].toObject();

        // 解析基本信息
        actuator.name = servoControlObj["name"].toString();
        actuator.type = static_cast<UI::ActuatorType_1_2>(servoControlObj["type"].toInt());
        actuator.zero_offset = servoControlObj["zero_offset"].toDouble();

        // 解析硬件配置
        actuator.lc_id = servoControlObj["lc_id"].toInt();
        actuator.station_id = servoControlObj["station_id"].toInt();
        actuator.board_id_ao = servoControlObj["board_id_ao"].toInt();
        actuator.board_type_ao = servoControlObj["board_type_ao"].toInt();
        actuator.port_id_ao = servoControlObj["port_id_ao"].toInt();
        actuator.board_id_do = servoControlObj["board_id_do"].toInt();
        actuator.board_type_do = servoControlObj["board_type_do"].toInt();
        actuator.port_id_do = servoControlObj["port_id_do"].toInt();

        // 解析详细参数
        if (servoControlObj.contains("params") && servoControlObj["params"].isObject()) {
            QJsonObject paramsObj = servoControlObj["params"].toObject();

            actuator.params.model = paramsObj["model"].toString();
            actuator.params.sn = paramsObj["sn"].toString();
            actuator.params.k = paramsObj["k"].toDouble();
            actuator.params.b = paramsObj["b"].toDouble();
            actuator.params.precision = paramsObj["precision"].toDouble();
            actuator.params.polarity = static_cast<UI::Polarity_1_2>(paramsObj["polarity"].toInt());
            actuator.params.meas_unit = static_cast<UI::MeasurementUnit_1_2>(paramsObj["meas_unit"].toInt());
            actuator.params.meas_range_min = paramsObj["meas_range_min"].toDouble();
            actuator.params.meas_range_max = paramsObj["meas_range_max"].toDouble();
            actuator.params.output_signal_unit = paramsObj["output_signal_unit"].toInt();
            actuator.params.output_signal_range_min = paramsObj["output_signal_range_min"].toDouble();
            actuator.params.output_signal_range_max = paramsObj["output_signal_range_max"].toDouble();
        }

        return actuator;

    } catch (const std::exception& e) {
        setError(QString("导入过程中发生异常: %1").arg(e.what()));
        return actuator;
    }
}

QJsonArray JSONDataExporter_1_2::createControlChannelGroupsJson(const QList<UI::ControlChannelGroup>& channelGroups) {
    QJsonArray channelsArray;

    // 🆕 修改：参考XLSX格式，扁平化控制通道数据（简化格式，不显示组信息）
    int channelIndex = 1; // 通道序号从1开始
    for (const UI::ControlChannelGroup& group : channelGroups) {
        for (const UI::ControlChannelParams& channel : group.channels) {
            QJsonObject channelObject;

            // 🆕 扩展：包含所有控制通道字段
            channelObject["channelNumber"] = channelIndex;                                               // 通道序号
            channelObject["channelId"] = QString::fromStdString(channel.channelId);                     // 通道ID
            channelObject["channelName"] = QString::fromStdString(channel.channelName);                 // 通道名称
            
            // 🆕 新增：扩展配置字段
            channelObject["lc_id"] = channel.lc_id;                                                      // 下位机ID
            channelObject["station_id"] = channel.station_id;                                            // 站点ID
            channelObject["enable"] = channel.enable;                                                    // 使能状态
            channelObject["control_mode"] = channel.control_mode;                                        // 控制模式
            
            // 关联配置
            channelObject["hardwareAssociation"] = QString::fromStdString(channel.hardwareAssociation); // 硬件关联
            channelObject["load1Sensor"] = QString::fromStdString(channel.load1Sensor);                 // 载荷1传感器
            channelObject["load2Sensor"] = QString::fromStdString(channel.load2Sensor);                 // 载荷2传感器
            channelObject["positionSensor"] = QString::fromStdString(channel.positionSensor);           // 位置传感器
            channelObject["controlActuator"] = QString::fromStdString(channel.controlActuator);         // 控制作动器
            
            // 🆕 新增：极性参数
            channelObject["servo_control_polarity"] = channel.servo_control_polarity;                   // 控制作动器极性
            channelObject["payload_sensor1_polarity"] = channel.payload_sensor1_polarity;               // 载荷1传感器极性
            channelObject["payload_sensor2_polarity"] = channel.payload_sensor2_polarity;               // 载荷2传感器极性
            channelObject["position_sensor_polarity"] = channel.position_sensor_polarity;               // 位置传感器极性
            
            channelObject["notes"] = QString::fromStdString(channel.notes);                             // 备注

            channelsArray.append(channelObject);
            channelIndex++;
        }
    }

    return channelsArray;
}

QJsonArray JSONDataExporter_1_2::createHardwareNodesJson(const QList<UI::NodeConfigParams>& nodeConfigs) {
    QJsonArray nodesArray;

    // 🆕 修改：参考XLSX格式，保持硬件节点的层次结构
    for (const auto& node : nodeConfigs) {
        QJsonObject nodeObject;

        // 🆕 参考XLSX的硬件节点详细配置格式
        nodeObject["nodeName"] = node.nodeName;                // 节点名称 (如 LD-B1, LD-B2)
        nodeObject["channelCount"] = node.channelCount;        // 通道数量

        QJsonArray channelsArray;
        for (const auto& channel : node.channels) {
            QJsonObject channelObject;
            channelObject["channelId"] = channel.channelId;     // 通道ID (CH1, CH2)
            channelObject["ipAddress"] = channel.ipAddress;     // IP地址
            channelObject["port"] = channel.port;               // 端口号
            channelObject["enabled"] = channel.enabled;         // 是否启用
            channelsArray.append(channelObject);
        }
        nodeObject["channels"] = channelsArray;
        nodesArray.append(nodeObject);
    }

    return nodesArray;
}

// 🆕 新增：创建通道配置数据（从控制通道数据管理器获取实际数据）
QJsonArray JSONDataExporter_1_2::createChannelConfigDataFromMemory() {
    QJsonArray channelsArray;

    // 如果没有设置控制通道数据管理器，返回空数组
    if (!ctrlChanDataManager_) {
        qDebug() << "控制通道数据管理器未设置，无法创建通道配置数据";
        return channelsArray;
    }

    // 从控制通道数据管理器获取所有控制通道组
    QList<UI::ControlChannelGroup> channelGroups = ctrlChanDataManager_->getAllControlChannelGroups();
    
    // 遍历所有控制通道组和通道
    for (const UI::ControlChannelGroup& group : channelGroups) {
        for (const UI::ControlChannelParams& channel : group.channels) {
            QString channelId = QString::fromStdString(channel.channelId);
            
            //// 只处理CH1和CH2通道
            //if (channelId == "CH1" || channelId == "CH2")
            {
                QJsonObject channelObj;
                
                // 🆕 修改：使用通道参数中的实际数据
                channelObj["lc_id"] = channel.lc_id;
                channelObj["station_id"] = channel.station_id;
                channelObj["id"] = (channelId == "CH1") ? 1 : 2;
                channelObj["name"] = QString::fromStdString(channel.channelName);
                channelObj["control_mode"] = channel.control_mode;
                channelObj["enable"] = channel.enable;
                
                // 🆕 修改：使用通道参数中的极性数据
                channelObj["servo_control_polarity"] = channel.servo_control_polarity;
                channelObj["payload_sensor1_polarity"] = channel.payload_sensor1_polarity;
                channelObj["payload_sensor2_polarity"] = channel.payload_sensor2_polarity;
                channelObj["position_sensor_polarity"] = channel.position_sensor_polarity;
                
                // 作动器配置（从关联的作动器获取数据）
                QJsonObject servoControl;
                QJsonObject servoParams;
                
                // 🆕 修改：先初始化默认值，然后尝试从关联作动器获取真实数据
                QString servoName = QString(u8"控制量");
                int servoType = 1;
                double zeroOffset = 0.0;
                int lcId = 1;
                int stationId = 1;
                int boardIdAo = 1;
                int boardTypeAo = 1;
                int portIdAo = (channelId == "CH1") ? 1 : 2;
                int boardIdDo = 1;
                int boardTypeDo = 1;
                int portIdDo = (channelId == "CH1") ? 1 : 2;
                
                // 从关联的作动器获取真实数据
                QString controlActuator = QString::fromStdString(channel.controlActuator);
                if (!controlActuator.isEmpty() && xlsExporter_ && xlsExporter_->getActuatorDataManager()) {
                    // 尝试从作动器数据管理器获取关联的作动器数据
                    ActuatorDataManager_1_2* actuatorManager = xlsExporter_->getActuatorDataManager();
                    QStringList actuatorSerialNumbers = actuatorManager->getAllActuatorSerialNumbers();
                    
                    // 提前获取所有作动器组，避免在循环中重复调用
                    QList<UI::ActuatorGroup_1_2> actuatorGroups = actuatorManager->getAllActuatorGroups();
                    
                    bool foundActuator = false;
                    for (const QString& sn : actuatorSerialNumbers) {
                        // 获取作动器所属的组ID
                        int groupId = actuatorManager->getActuatorGroupId(sn);
                        if (groupId == -1) {
                            continue; // 跳过未找到组的作动器
                        }
                        
                        UI::ActuatorParams_1_2 actuator = actuatorManager->getActuator(sn, groupId);
                        // 检查作动器是否与当前通道关联
                        // controlActuator格式为"作动器组名 - 作动器序列号"
                        // 需要查找该作动器属于哪个组
                        QString actuatorGroupName;
                        for (const UI::ActuatorGroup_1_2& actuatorGroup : actuatorGroups) {
                            for (const UI::ActuatorParams_1_2& actuatorInGroup : actuatorGroup.actuators) {
                                if (actuatorInGroup.params.sn == actuator.params.sn) {
                                    actuatorGroupName = actuatorGroup.groupName;
                                    break;
                                }
                            }
                            if (!actuatorGroupName.isEmpty()) {
                                break;
                            }
                        }
                        
                        // 如果找到了作动器的组名，则构建正确的标识符进行匹配
                        if (!actuatorGroupName.isEmpty()) {
                            QString actuatorIdentifier = QString("%1 - %2").arg(actuatorGroupName).arg(actuator.params.sn);
                            if (actuatorIdentifier == controlActuator) {
                                
                                // 🆕 修改：使用作动器的真实基本配置数据
                                servoName = actuator.name;
                                servoType = static_cast<int>(actuator.type);
                                zeroOffset = actuator.zero_offset;
                                lcId = actuator.lc_id;
                                stationId = actuator.station_id;
                                boardIdAo = actuator.board_id_ao;
                                boardTypeAo = actuator.board_type_ao;
                                portIdAo = actuator.port_id_ao;
                                boardIdDo = actuator.board_id_do;
                                boardTypeDo = actuator.board_type_do;
                                portIdDo = actuator.port_id_do;
                                
                                // 使用作动器的详细参数
                                servoParams["model"] = actuator.params.model;
                                servoParams["sn"] = actuator.params.sn;
                                servoParams["k"] = actuator.params.k;
                                servoParams["b"] = actuator.params.b;
                                servoParams["precision"] = actuator.params.precision;
                                servoParams["polarity"] = static_cast<int>(actuator.params.polarity);
                                servoParams["meas_unit"] = static_cast<int>(actuator.params.meas_unit);
                                servoParams["meas_range_min"] = actuator.params.meas_range_min;
                                servoParams["meas_range_max"] = actuator.params.meas_range_max;
                                servoParams["output_signal_unit"] = actuator.params.output_signal_unit;
                                servoParams["output_signal_range_min"] = actuator.params.output_signal_range_min;
                                servoParams["output_signal_range_max"] = actuator.params.output_signal_range_max;
                                foundActuator = true;
                                break;
                            }
                        }
                    }
                    
//                    if (!foundActuator)
//                    {
//                        // 如果未找到关联的作动器，使用默认值
//                        servoParams["model"] = (channelId == "CH1") ? "MD500" : "MD300";
//                        servoParams["sn"] = (channelId == "CH1") ? "123" : "456";
//                        servoParams["k"] = 1.0;
//                        servoParams["b"] = 0.0;
//                        servoParams["precision"] = 0.1;
//                        servoParams["polarity"] = 1;
//                        servoParams["meas_unit"] = 1;
//                        servoParams["meas_range_min"] = (channelId == "CH1") ? -100.0 : -50.0;
//                        servoParams["meas_range_max"] = (channelId == "CH1") ? 100.0 : 50.0;
//                        servoParams["output_signal_unit"] = 1;
//                        servoParams["output_signal_range_min"] = (channelId == "CH1") ? -100.0 : -50.0;
//                        servoParams["output_signal_range_max"] = (channelId == "CH1") ? 100.0 : 50.0;
//                    }
                }
                
                // 🆕 新增：如果没有找到关联的作动器或servoParams为空，设置默认参数
                if (servoParams.isEmpty()) {
                    servoParams["model"] = (channelId == "CH1") ? "MD500" : "MD300";
                    servoParams["sn"] = (channelId == "CH1") ? "123" : "456";
                    servoParams["k"] = 1.0;
                    servoParams["b"] = 0.0;
                    servoParams["precision"] = 0.1;
                    servoParams["polarity"] = 1;
                    servoParams["meas_unit"] = 1;
                    servoParams["meas_range_min"] = (channelId == "CH1") ? -100.0 : -50.0;
                    servoParams["meas_range_max"] = (channelId == "CH1") ? 100.0 : 50.0;
                    servoParams["output_signal_unit"] = 1;
                    servoParams["output_signal_range_min"] = (channelId == "CH1") ? -100.0 : -50.0;
                    servoParams["output_signal_range_max"] = (channelId == "CH1") ? 100.0 : 50.0;
                }
                
                // 🆕 新增：设置servoControl的基本配置数据（使用真实数据或默认值）
                servoControl["name"] = servoName;
                servoControl["type"] = servoType;
                servoControl["zero_offset"] = zeroOffset;
                servoControl["lc_id"] = lcId;
                servoControl["station_id"] = stationId;
                servoControl["board_id_ao"] = boardIdAo;
                servoControl["board_type_ao"] = boardTypeAo;
                servoControl["port_id_ao"] = portIdAo;
                servoControl["board_id_do"] = boardIdDo;
                servoControl["board_type_do"] = boardTypeDo;
                servoControl["port_id_do"] = portIdDo;
                servoControl["params"] = servoParams;
                channelObj["servo_control"] = servoControl;
                
                // 载荷传感器1配置（从关联的传感器获取数据）
                QJsonObject payload1;
                payload1["name"] = QString(u8"载荷传感器1");
                payload1["zero_offset"] = 0.0;
                payload1["enable"] = true;
                
                QJsonObject payload1Params;
                QString load1Sensor = QString::fromStdString(channel.load1Sensor);
                if (!load1Sensor.isEmpty() && sensorDataManager_)
                {
                    // 尝试从传感器数据管理器获取关联的传感器数据
                    QList<UI::SensorGroup_1_2> sensorGroups = sensorDataManager_->getAllSensorGroups();
                    
                    bool foundSensor = false;
                    for (const UI::SensorGroup_1_2& sensorGroup : sensorGroups) {
                        QStringList sensorSerialNumbers = sensorDataManager_->getAllSensorSerialNumbersInGroup(sensorGroup.groupId);
                        
                        for (const QString& sn : sensorSerialNumbers) {
                            UI::SensorParams_1_2 sensor = sensorDataManager_->getSensorInGroup(sensorGroup.groupId, sn);
                            // 检查传感器是否与当前通道关联
                            QString sensorIdentifier = QString("%1 - %2").arg(sensorGroup.groupName).arg(sensor.params_sn);
                            if (sensor.params_sn == load1Sensor || sensorIdentifier == load1Sensor) {
                                // 使用传感器的实际参数
                                payload1Params["model"] = sensor.params_model;
                                payload1Params["sn"] = sensor.params_sn;
                                payload1Params["k"] = sensor.params_k;
                                payload1Params["b"] = sensor.params_b;
                                payload1Params["precision"] = sensor.params_precision;
                                payload1Params["polarity"] = sensor.params_polarity;
                                payload1Params["meas_unit"] = sensor.meas_unit;
                                payload1Params["meas_range_min"] = sensor.meas_range_min;
                                payload1Params["meas_range_max"] = sensor.meas_range_max;
                                payload1Params["output_signal_unit"] = sensor.output_signal_unit;
                                payload1Params["output_signal_range_min"] = sensor.output_signal_range_min;
                                payload1Params["output_signal_range_max"] = sensor.output_signal_range_max;
                                foundSensor = true;
                                break;
                            }
                        }
                        
                        if (foundSensor) {
                            break;
                        }
                    }
                }
                
                // 将参数对象添加到载荷传感器1对象中
                payload1["params"] = payload1Params;
                
                // 将载荷传感器1添加到通道对象中
                channelObj["payload1"] = payload1;
                
                // 载荷传感器2配置（从关联的传感器获取数据）
                QJsonObject payload2;
                payload2["name"] = QString(u8"载荷传感器2");
                payload2["zero_offset"] = 0.0;
                payload2["enable"] = true;
                
                QJsonObject payload2Params;
                QString load2Sensor = QString::fromStdString(channel.load2Sensor);
                if (!load2Sensor.isEmpty() && sensorDataManager_) {
                    // 尝试从传感器数据管理器获取关联的传感器数据
                    QList<UI::SensorGroup_1_2> sensorGroups = sensorDataManager_->getAllSensorGroups();
                    
                    bool foundSensor = false;
                    for (const UI::SensorGroup_1_2& sensorGroup : sensorGroups) {
                        QStringList sensorSerialNumbers = sensorDataManager_->getAllSensorSerialNumbersInGroup(sensorGroup.groupId);
                        
                        for (const QString& sn : sensorSerialNumbers) {
                            UI::SensorParams_1_2 sensor = sensorDataManager_->getSensorInGroup(sensorGroup.groupId, sn);
                            // 检查传感器是否与当前通道关联
                            QString sensorIdentifier = QString("%1 - %2").arg(sensorGroup.groupName).arg(sensor.params_sn);
                            if (sensor.params_sn == load2Sensor || sensorIdentifier == load2Sensor) {
                                // 使用传感器的实际参数
                                payload2Params["model"] = sensor.params_model;
                                payload2Params["sn"] = sensor.params_sn;
                                payload2Params["k"] = sensor.params_k;
                                payload2Params["b"] = sensor.params_b;
                                payload2Params["precision"] = sensor.params_precision;
                                payload2Params["polarity"] = sensor.params_polarity;
                                payload2Params["meas_unit"] = sensor.meas_unit;
                                payload2Params["meas_range_min"] = sensor.meas_range_min;
                                payload2Params["meas_range_max"] = sensor.meas_range_max;
                                payload2Params["output_signal_unit"] = sensor.output_signal_unit;
                                payload2Params["output_signal_range_min"] = sensor.output_signal_range_min;
                                payload2Params["output_signal_range_max"] = sensor.output_signal_range_max;
                                foundSensor = true;
                                break;
                            }
                        }
                        
                        if (foundSensor) {
                            break;
                        }
                    }
                }
                
                // 将参数对象添加到载荷传感器2对象中
                payload2["params"] = payload2Params;
                
                // 将载荷传感器2添加到通道对象中
                channelObj["payload2"] = payload2;
                
                // 位置传感器配置（从关联的传感器获取数据）
                QJsonObject position;
                position["name"] = QString(u8"位置传感器");
                position["zero_offset"] = 0.0;
                position["enable"] = true;
                
                QJsonObject positionParams;
                QString positionSensor = QString::fromStdString(channel.positionSensor);
                if (!positionSensor.isEmpty() && sensorDataManager_) {
                    // 尝试从传感器数据管理器获取关联的传感器数据
                    QList<UI::SensorGroup_1_2> sensorGroups = sensorDataManager_->getAllSensorGroups();
                    
                    bool foundSensor = false;
                    for (const UI::SensorGroup_1_2& sensorGroup : sensorGroups) {
                        QStringList sensorSerialNumbers = sensorDataManager_->getAllSensorSerialNumbersInGroup(sensorGroup.groupId);
                        
                        for (const QString& sn : sensorSerialNumbers) {
                            UI::SensorParams_1_2 sensor = sensorDataManager_->getSensorInGroup(sensorGroup.groupId, sn);
                            // 检查传感器是否与当前通道关联
                            QString sensorIdentifier = QString("%1 - %2").arg(sensorGroup.groupName).arg(sensor.params_sn);
                            if (sensor.params_sn == positionSensor || sensorIdentifier == positionSensor) {
                                // 使用传感器的实际参数
                                positionParams["model"] = sensor.params_model;
                                positionParams["sn"] = sensor.params_sn;
                                positionParams["k"] = sensor.params_k;
                                positionParams["b"] = sensor.params_b;
                                positionParams["precision"] = sensor.params_precision;
                                positionParams["polarity"] = sensor.params_polarity;
                                positionParams["meas_unit"] = sensor.meas_unit;
                                positionParams["meas_range_min"] = sensor.meas_range_min;
                                positionParams["meas_range_max"] = sensor.meas_range_max;
                                positionParams["output_signal_unit"] = sensor.output_signal_unit;
                                positionParams["output_signal_range_min"] = sensor.output_signal_range_min;
                                positionParams["output_signal_range_max"] = sensor.output_signal_range_max;
                                foundSensor = true;
                                break;
                            }
                        }
                        
                        if (foundSensor) {
                            break;
                        }
                    }
                }
                
                // 将参数对象添加到位置传感器对象中
                position["params"] = positionParams;
                
                // 将位置传感器添加到通道对象中
                channelObj["position"] = position;
                
                // 将通道对象添加到通道数组中
                channelsArray.append(channelObj);
            }
        }
    }

    return channelsArray;
}
