# 通道配置方案

## 配置文件信息

- **文件位置**: <mcfile name="channel_config.json" path="d:\Work\Project\004_SiteResConfig_ProjectAll\001_Code\001_SiteResConfig\channel_config.json"></mcfile>
- **创建时间**: 最近生成
- **文件用途**: 存储控制通道的完整配置信息，供主程序读取使用

## 配置结构概述

配置文件采用JSON格式，根节点为`channels`数组，包含2个通道配置(CH1和CH2)。每个通道配置包含以下主要部分：

1. **基本信息**: lc_id、station_id、id、name等
2. **极性参数**: servo_control_polarity、payload_sensor1_polarity等4个极性参数
3. **作动器模块**: servo_control，包含硬件参数和详细参数
4. **传感器模块**: payload_sensor1、payload_sensor2和position_sensor，每个模块包含基本信息和详细参数

## 通道配置详情

### CH1 (id: 1)
- **名称**: 加载电机
- **下位机/站点**: lc_id=1, station_id=1
- **端口配置**: port_id_ao=1, port_id_do=1
- **作动器型号**: MD500
- **量程范围**: ±100.0
- **极性参数**: 全部为1

### CH2 (id: 2)
- **名称**: 位移电机
- **下位机/站点**: lc_id=1, station_id=1
- **端口配置**: port_id_ao=2, port_id_do=2
- **作动器型号**: MD300
- **量程范围**: ±50.0
- **极性参数**: 全部为1

## 模块配置说明

### 作动器模块 (servo_control)
每个通道的作动器模块包含：
- 基本信息: name、type、zero_offset
- 硬件参数: lc_id、station_id、board_id_ao、port_id_ao等
- 详细参数: model、sn、k、b、precision、polarity等

### 传感器模块
每个传感器模块(payload_sensor1/2、position_sensor)包含：
- 基本信息: name、zero_offset、enable
- 详细参数: model、sn、k、b、precision、polarity、meas_unit、meas_range等

## 使用说明

1. 该配置文件可直接集成到`createControlChannelGroupsJson`方法中
2. 系统将根据`id`字段识别通道(1对应CH1，2对应CH2)
3. 所有参数均已设置明确值，无需额外配置
4. 如需修改，可直接编辑JSON文件中的相应字段

## 配置示例

以下是完整的JSON配置示例：

```json
{
    "channels": [
        {
            "lc_id": 1,
            "station_id": 1,
            "id": 1,
            "name": "加载电机",
            "control_mode": 4,
            "enable": false,
            "servo_control_polarity": 1,
            "payload_sensor1_polarity": 1,
            "payload_sensor2_polarity": 1,
            "position_sensor_polarity": 1,
            "servo_control": {
                "name": "控制量",
                "type": 1,
                "zero_offset": 0,
                "lc_id": 1,
                "station_id": 1,
                "board_id_ao": 1,
                "board_type_ao": 1,
                "port_id_ao": 1,
                "board_id_do": 1,
                "board_type_do": 1,
                "port_id_do": 1,
                "params": {
                    "model": "MD500",
                    "sn": "123",
                    "k": 1.0,
                    "b": 0.0,
                    "precision": 0.1,
                    "polarity": 1,
                    "meas_unit": 1,
                    "meas_range_min": -100.0,
                    "meas_range_max": 100.0,
                    "output_signal_unit": 1,
                    "output_signal_range_min": -100.0,
                    "output_signal_range_max": 100.0
                }
            },
            "payload_sensor1": {
                "name": "载荷传感器1",
                "zero_offset": 0.0,
                "enable": true,
                "params": {
                    "model": "AKD-8A",
                    "sn": "2223",
                    "k": 20.0,
                    "b": 0.0,
                    "precision": 0.1,
                    "polarity": -1,
                    "meas_unit": 1,
                    "meas_range_min": -100.0,
                    "meas_range_max": 100.0,
                    "output_signal_unit": 1,
                    "output_signal_range_min": -100.0,
                    "output_signal_range_max": 100.0
                }
            },
            "payload_sensor2": {
                "name": "载荷传感器2",
                "zero_offset": 0.0,
                "enable": true,
                "params": {
                    "model": "AKD-8A",
                    "sn": "2223",
                    "k": 20.0,
                    "b": 0.0,
                    "precision": 0.1,
                    "polarity": -1,
                    "meas_unit": 1,
                    "meas_range_min": -100.0,
                    "meas_range_max": 100.0,
                    "output_signal_unit": 1,
                    "output_signal_range_min": -100.0,
                    "output_signal_range_max": 100.0
                }
            },
            "position_sensor": {
                "name": "位置传感器",
                "zero_offset": 0.0,
                "enable": true,
                "params": {
                    "model": "AKD-8A",
                    "sn": "2223",
                    "k": 20.0,
                    "b": 0.0,
                    "precision": 0.1,
                    "polarity": -1,
                    "meas_unit": 1,
                    "meas_range_min": -100.0,
                    "meas_range_max": 100.0,
                    "output_signal_unit": 1,
                    "output_signal_range_min": -100.0,
                    "output_signal_range_max": 100.0
                }
            }
        },
        {
            "lc_id": 1,
            "station_id": 1,
            "id": 2,
            "name": "位移电机",
            "control_mode": 4,
            "enable": false,
            "servo_control_polarity": 1,
            "payload_sensor1_polarity": 1,
            "payload_sensor2_polarity": 1,
            "position_sensor_polarity": 1,
            "servo_control": {
                "name": "控制量",
                "type": 1,
                "zero_offset": 0,
                "lc_id": 1,
                "station_id": 1,
                "board_id_ao": 1,
                "board_type_ao": 1,
                "port_id_ao": 2,
                "board_id_do": 1,
                "board_type_do": 1,
                "port_id_do": 2,
                "params": {
                    "model": "MD300",
                    "sn": "456",
                    "k": 1.0,
                    "b": 0.0,
                    "precision": 0.1,
                    "polarity": 1,
                    "meas_unit": 1,
                    "meas_range_min": -50.0,
                    "meas_range_max": 50.0,
                    "output_signal_unit": 1,
                    "output_signal_range_min": -50.0,
                    "output_signal_range_max": 50.0
                }
            },
            "payload_sensor1": {
                "name": "载荷传感器1",
                "zero_offset": 0.0,
                "enable": true,
                "params": {
                    "model": "AKD-8A",
                    "sn": "2224",
                    "k": 10.0,
                    "b": 0.0,
                    "precision": 0.1,
                    "polarity": -1,
                    "meas_unit": 1,
                    "meas_range_min": -50.0,
                    "meas_range_max": 50.0,
                    "output_signal_unit": 1,
                    "output_signal_range_min": -50.0,
                    "output_signal_range_max": 50.0
                }
            },
            "payload_sensor2": {
                "name": "载荷传感器2",
                "zero_offset": 0.0,
                "enable": true,
                "params": {
                    "model": "AKD-8A",
                    "sn": "2224",
                    "k": 10.0,
                    "b": 0.0,
                    "precision": 0.1,
                    "polarity": -1,
                    "meas_unit": 1,
                    "meas_range_min": -50.0,
                    "meas_range_max": 50.0,
                    "output_signal_unit": 1,
                    "output_signal_range_min": -50.0,
                    "output_signal_range_max": 50.0
                }
            },
            "position_sensor": {
                "name": "位置传感器",
                "zero_offset": 0.0,
                "enable": true,
                "params": {
                    "model": "AKD-8A",
                    "sn": "2224",
                    "k": 10.0,
                    "b": 0.0,
                    "precision": 0.1,
                    "polarity": -1,
                    "meas_unit": 1,
                    "meas_range_min": -50.0,
                    "meas_range_max": 50.0,
                    "output_signal_unit": 1,
                    "output_signal_range_min": -50.0,
                    "output_signal_range_max": 50.0
                }
            }
        }
    ]
}
```