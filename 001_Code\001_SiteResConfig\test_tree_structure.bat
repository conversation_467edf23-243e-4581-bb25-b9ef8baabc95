@echo off
echo ========================================
echo  树形控件结构修改编译测试
echo ========================================

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o
if exist "ui_*.h" del ui_*.h

echo.
echo 生成UI头文件...
uic ui\MainWindow.ui -o ui_MainWindow.h
if errorlevel 1 (
    echo UI文件生成失败！
    pause
    exit /b 1
) else (
    echo ✅ UI头文件生成成功: ui_MainWindow.h
)

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo.
    echo 修改的内容：
    echo ✅ 硬件资源树：隐藏列头，只保留一列
    echo ✅ 试验配置树：隐藏列头，只保留一列
    echo ✅ 添加根节点"任务1"到两个树形控件
    echo ✅ 硬件资源树结构：任务1 → 作动器、传感器、硬件节点资源
    echo ✅ 试验配置树结构：任务1 → 加载通道、载荷谱
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！树形控件结构修改完成
    echo ========================================
    
    if exist "SiteResConfig.exe" (
        echo 可执行文件: SiteResConfig.exe
        echo.
        echo ✅ 树形控件修改完成！
        echo.
        echo 🌳 硬件资源树结构:
        echo 任务1
        echo ├─ 作动器
        echo ├─ 传感器
        echo └─ 硬件节点资源
        echo.
        echo 🌳 试验配置树结构:
        echo 任务1
        echo ├─ 加载通道
        echo └─ 载荷谱
        echo.
        echo 🎯 修改内容:
        echo - 隐藏了树形控件的列头
        echo - 只保留一列显示
        echo - 添加了"任务1"根节点
        echo - 重新组织了树形结构
        echo - 状态信息合并到节点名称中
        echo.
        echo 🎨 显示效果:
        echo - 硬件节点: "节点名称 [状态]"
        echo - 作动器: "作动器名称 [容量]"
        echo - 传感器: "传感器名称 [量程]"
        echo - 加载通道: "通道名称 [容量]"
        echo.
        echo 启动程序...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
        echo 启动程序...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
        echo 启动程序...
        start release\SiteResConfig.exe
    ) else (
        echo 警告: 找不到可执行文件！
    )
)

echo.
echo 📖 树形控件使用说明:
echo.
echo 🌳 硬件资源树:
echo - 根节点: 任务1
echo - 子节点: 作动器、传感器、硬件节点资源
echo - 显示格式: 名称 [状态/参数]
echo - 工具提示: 显示详细信息
echo.
echo 🌳 试验配置树:
echo - 根节点: 任务1
echo - 子节点: 加载通道、载荷谱
echo - 显示格式: 名称 [参数]
echo - 工具提示: 显示详细信息
echo.
echo 🎯 操作方式:
echo - 单击节点: 选择节点
echo - 双击节点: 展开/折叠
echo - 右键菜单: 上下文操作
echo - 工具提示: 鼠标悬停显示详情
echo.
pause
