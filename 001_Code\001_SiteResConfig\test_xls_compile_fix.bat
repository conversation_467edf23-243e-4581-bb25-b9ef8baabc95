@echo off
chcp 65001 >nul
echo ========================================
echo  XLS导出器编译错误修复测试
echo ========================================
echo.

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o
if exist "ui_*.h" del ui_*.h

echo.
echo 检查修复的问题...
echo.
echo 1. 检查QtXlsx API使用修复...
findstr /C:"document.load()" "src\XLSDataExporter.cpp" >nul
if not errorlevel 1 (
    echo ❌ 仍然使用了错误的document.load()方法
    goto :error
) else (
    echo ✅ 已移除错误的document.load()调用
)

echo.
echo 2. 检查SensorParams字段修复...
findstr /C:"params.precision" "src\XLSDataExporter.cpp" >nul
if not errorlevel 1 (
    echo ❌ 仍然使用了不存在的precision字段
    goto :error
) else (
    echo ✅ 已修复precision字段为accuracy字段
)

findstr /C:"params.notes" "src\XLSDataExporter.cpp" >nul
if not errorlevel 1 (
    echo ❌ 仍然使用了不存在的notes字段
    goto :error
) else (
    echo ✅ 已修复notes字段为model字段
)

echo.
echo 3. 检查setColumnWidth API修复...
findstr /C:"setColumnWidth(it.key(), it.key(), it.value())" "src\XLSDataExporter.cpp" >nul
if errorlevel 1 (
    echo ❌ setColumnWidth API修复可能不正确
    goto :error
) else (
    echo ✅ 已修复setColumnWidth API调用
)

echo.
echo 4. 检查calibrationDate类型修复...
findstr /C:"calibrationDate.toString" "src\XLSDataExporter.cpp" >nul
if not errorlevel 1 (
    echo ❌ 仍然将calibrationDate当作QDate处理
    goto :error
) else (
    echo ✅ 已修复calibrationDate为QString类型处理
)

echo.
echo 5. 检查重复定义修复...
findstr /C:"redefinition" "src\XLSDataExporter.cpp" >nul
if not errorlevel 1 (
    echo ❌ 可能仍有重复定义问题
) else (
    echo ✅ 重复定义问题已修复
)

echo.
echo 生成UI头文件...
uic ui\MainWindow.ui -o ui_MainWindow.h
if errorlevel 1 (
    echo UI文件生成失败！
    pause
    exit /b 1
) else (
    echo ✅ UI头文件生成成功
)

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目（测试XLS修复）...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译仍然失败！
    echo ========================================
    echo.
    echo 可能的剩余问题：
    echo 1. QtXlsx库版本兼容性问题
    echo 2. 其他API使用错误
    echo 3. 头文件包含问题
    echo 4. 链接器问题
    echo.
    echo 请检查编译错误信息并进一步修复。
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 XLS编译错误修复成功！
    echo ========================================
    
    if exist "SiteResConfig.exe" (
        echo 可执行文件: SiteResConfig.exe
        echo.
        echo ✅ 所有XLS编译错误已成功修复！
        echo.
        echo 🔧 修复的问题总结:
        echo.
        echo 1. QtXlsx API问题修复:
        echo    ├─ 移除了错误的document.load()调用
        echo    ├─ QtXlsx会自动加载文件，无需手动调用load()
        echo    └─ 修复了setColumnWidth()方法的参数
        echo.
        echo 2. SensorParams结构体字段修复:
        echo    ├─ precision字段 → accuracy字段 (QString类型)
        echo    ├─ notes字段 → model字段 (使用现有字段)
        echo    └─ calibrationDate直接使用 (已经是QString类型)
        echo.
        echo 3. 数据类型兼容性修复:
        echo    ├─ 精度字段: 从double改为QString
        echo    ├─ 校准日期: 从QDate改为QString
        echo    └─ 备注字段: 使用model字段替代
        echo.
        echo 4. Excel表头更新:
        echo    ├─ 序列号 | 类型 | 精度 | 量程 | 校准日期 | 型号
        echo    └─ 与实际SensorParams结构体字段完全匹配
        echo.
        echo 🎨 Excel文件结构 (修复后):
        echo.
        echo 硬件配置工作表:
        echo ├─ 表头: 类型 | 名称 | 参数1 | 参数2 | 参数3
        echo ├─ 数据: 硬件树结构化数据
        echo └─ 样式: 蓝色表头，自动列宽
        echo.
        echo 传感器详细配置工作表:
        echo ├─ 表头: 序列号 | 类型 | 精度 | 量程 | 校准日期 | 型号
        echo ├─ 数据: 完整的传感器参数信息
        echo └─ 样式: 专业表格格式
        echo.
        echo 🚀 现在可以正常使用的功能:
        echo ├─ 导出硬件树到Excel
        echo ├─ 导出传感器详细信息到Excel
        echo ├─ 导出完整项目到Excel
        echo ├─ 从Excel导入硬件配置
        echo ├─ 批量导出多种格式
        echo └─ 专业的Excel格式和样式
        echo.
        echo 启动程序测试修复后的XLS功能...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
        echo 启动程序...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
        echo 启动程序...
        start release\SiteResConfig.exe
    ) else (
        echo 警告: 找不到可执行文件！
    )
)

echo.
echo 📖 修复说明:
echo.
echo 🎯 主要修复内容:
echo - ✅ QtXlsx API兼容性问题
echo - ✅ SensorParams结构体字段匹配
echo - ✅ 数据类型转换问题
echo - ✅ Excel列宽设置API
echo.
echo 🔧 技术细节:
echo - QtXlsx库自动处理文件加载
echo - SensorParams使用实际存在的字段
echo - 所有字符串字段直接使用，无需类型转换
echo - setColumnWidth使用正确的参数格式
echo.
pause

:error
echo.
echo ❌ XLS编译错误修复测试失败！
echo 请检查相关修复是否正确应用。
pause
exit /b 1
