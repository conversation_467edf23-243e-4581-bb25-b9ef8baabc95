@echo off
echo ========================================
echo  CSV格式化空行测试
echo ========================================

echo 格式化需求：
echo 1. 同级节点间隔1行（如"作动器组"之间、"传感器组"之间）
echo 2. 不同大类间隔2行（如"作动器"、"传感器"、"硬件节点资源"之间）
echo 3. 子节点间隔1行（如"LD-B1"等子节点之间）
echo.

echo 技术实现：
echo ├─ 添加depth（深度）和siblingIndex（兄弟索引）参数
echo ├─ 根据节点层级和位置决定空行数量
echo ├─ 顶级大类（depth=1）之间添加2个空行
echo └─ 其他同级节点之间添加1个空行
echo.

echo 格式化逻辑：
echo 顶级大类识别：
echo   ├─ depth == 1（第一层子节点）
echo   ├─ 名称包含"作动器"、"传感器"、"硬件节点资源"
echo   └─ siblingIndex > 0（不是第一个节点）
echo.
echo 空行规则：
echo   ├─ 顶级大类之间：添加2个空行（\n\n）
echo   ├─ 同级节点之间：添加1个空行（\n）
echo   └─ 第一个节点：不添加空行
echo.

echo 预期CSV格式：
echo [硬件配置]
echo 类型,名称,参数1,参数2,参数3
echo 硬件节点,作动器,,,
echo 硬件节点,作动器组1,,,
echo.
echo 硬件节点,作动器组2,,,
echo.
echo.
echo 硬件节点,传感器,,,
echo 硬件节点,传感器组1,,,
echo.
echo 硬件节点,传感器组2,,,
echo.
echo.
echo 硬件节点,硬件节点资源,,,
echo 硬件节点,LD-B1,,,
echo.
echo 硬件节点,LD-B2,,,
echo.

echo 调试功能：
echo ✅ 显示保存的节点及其深度信息
echo ✅ 便于验证格式化逻辑是否正确
echo ✅ 帮助排查空行添加问题
echo.

echo 测试步骤：
echo 1. 重新编译项目
echo 2. 确保硬件树有完整的层次结构
echo 3. 保存工程配置为CSV文件
echo 4. 用文本编辑器打开CSV文件
echo 5. 检查空行格式是否符合要求
echo 6. 查看控制台调试输出
echo.

echo 预期结果：
echo - 同级节点之间有1个空行
echo - 大类节点之间有2个空行
echo - CSV文件结构清晰易读
echo - 控制台显示节点深度信息
echo.

pause
