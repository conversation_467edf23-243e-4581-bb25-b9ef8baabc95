# ActuatorDataManager 快速迁移指南

## 🎯 迁移目标

将MainWindow_Qt_Simple.cpp中的39处`actuatorDataManager_`调用快速迁移到`actuatorViewModel1_2_`。

## 🚀 快速迁移方案

### 方案1：使用IDE的查找替换功能

在IDE中打开`MainWindow_Qt_Simple.cpp`，使用查找替换功能：

#### 第一步：基础替换
```
查找：actuatorDataManager_->
替换为：actuatorViewModel1_2_->
```

#### 第二步：条件检查替换
```
查找：if (actuatorDataManager_)
替换为：if (actuatorViewModel1_2_)
```

#### 第三步：方法名称调整
```
查找：actuatorViewModel1_2_->addActuator(
替换为：actuatorViewModel1_2_->saveActuator(
```

### 方案2：手动逐一修改（推荐）

为了确保准确性，建议手动修改每一处。以下是具体的修改清单：

## 📋 详细修改清单

### 已完成的修改 ✅
1. 第1303行：统计信息 - `getAllActuatorGroups().size()`
2. 第1422行：验证数据 - `getAllActuatorGroups().size()`
3. 第1820行：获取组列表 - `getAllActuatorGroups()`
4. 第2651行：序列号唯一性检查 - `isSerialNumberUniqueInGroup()`
5. 第2670行：错误信息 - `getLastError()`
6. 第2677行：错误信息 - `getLastError()`
7. 第5178行：安全统计 - `getAllActuatorGroups().size()`

### 待修改的代码 🔄

#### 数据获取方法
```cpp
// 第2721行
QList<UI::ActuatorGroup> allGroups = actuatorDataManager_->getAllActuatorGroups();
// 修改为：
QList<UI::ActuatorGroup> allGroups = actuatorViewModel1_2_->getAllActuatorGroups();

// 第4198行
QList<UI::ActuatorGroup> actuatorGroups = actuatorDataManager_->getAllActuatorGroups();
// 修改为：
QList<UI::ActuatorGroup> actuatorGroups = actuatorViewModel1_2_->getAllActuatorGroups();

// 第4675行
auto actuatorGroups = actuatorDataManager_->getAllActuatorGroups();
// 修改为：
auto actuatorGroups = actuatorViewModel1_2_->getAllActuatorGroups();
```

#### 数据修改方法
```cpp
// 第2802行 - 注意方法名称变化
if (!actuatorDataManager_->addActuator(actuatorWithId)) {
// 修改为：
if (!actuatorViewModel1_2_->saveActuator(actuatorWithId)) {

// 第2810行
bool success = actuatorDataManager_->saveActuatorGroup(group);
// 修改为：
bool success = actuatorViewModel1_2_->saveActuatorGroup(group);

// 第5996行
if (!actuatorDataManager_->removeActuator(deviceName)) {
// 修改为：
if (!actuatorViewModel1_2_->removeActuator(deviceName)) {

// 第6087行
if (!actuatorDataManager_->updateActuator(deviceName, newParams)) {
// 修改为：
if (!actuatorViewModel1_2_->updateActuator(deviceName, newParams)) {
```

#### 数据验证方法
```cpp
// 第2761行
if (!actuatorDataManager_->validateActuatorInGroup(actuatorWithId, group)) {
// 修改为：
if (!actuatorViewModel1_2_->validateActuatorInGroup(actuatorWithId, group)) {

// 第2791行
if (!actuatorDataManager_->validateActuatorInGroup(actuatorWithId, group)) {
// 修改为：
if (!actuatorViewModel1_2_->validateActuatorInGroup(actuatorWithId, group)) {

// 第6078行
if (groupId > 0 && !actuatorDataManager_->isSerialNumberUniqueInGroup(newParams.serialNumber, groupId, currentParams.actuatorId)) {
// 修改为：
if (groupId > 0 && !actuatorViewModel1_2_->isSerialNumberUniqueInGroup(newParams.serialNumber, groupId, currentParams.actuatorId)) {
```

#### 数据查询方法
```cpp
// 第2701行
bool isNewActuator = !actuatorDataManager_->hasActuator(params.serialNumber);
// 修改为：
bool isNewActuator = !actuatorViewModel1_2_->hasActuator(params.serialNumber);

// 第2705行
actuatorWithId = actuatorDataManager_->getActuator(params.serialNumber);
// 修改为：
actuatorWithId = actuatorViewModel1_2_->getActuator(params.serialNumber);

// 第5881行
if (actuatorDataManager_->hasActuator(deviceName)) {
// 修改为：
if (actuatorViewModel1_2_->hasActuator(deviceName)) {

// 第5882行
UI::ActuatorParams actuator = actuatorDataManager_->getActuator(deviceName);
// 修改为：
UI::ActuatorParams actuator = actuatorViewModel1_2_->getActuator(deviceName);
```

#### 错误处理
```cpp
// 第2762行
AddLogEntry("ERROR", QString(u8"作动器在组内验证失败: %1").arg(actuatorDataManager_->getLastError()));
// 修改为：
AddLogEntry("ERROR", QString(u8"作动器在组内验证失败: %1").arg(actuatorViewModel1_2_->getLastError()));

// 第2792行
AddLogEntry("ERROR", QString(u8"作动器在新组内验证失败: %1").arg(actuatorDataManager_->getLastError()));
// 修改为：
AddLogEntry("ERROR", QString(u8"作动器在新组内验证失败: %1").arg(actuatorViewModel1_2_->getLastError()));

// 第2803行
AddLogEntry("ERROR", QString(u8"添加作动器到DataManager失败: %1").arg(actuatorDataManager_->getLastError()));
// 修改为：
AddLogEntry("ERROR", QString(u8"添加作动器到ViewModel失败: %1").arg(actuatorViewModel1_2_->getLastError()));
```

## 🔧 特殊注意事项

### 1. 方法名称变化
- `addActuator()` → `saveActuator()`
- 其他方法名称保持不变

### 2. 条件检查
```cpp
// 修改前
if (actuatorDataManager_) {
    // 操作
}

// 修改后
if (actuatorViewModel1_2_) {
    // 操作
}
```

### 3. 错误信息更新
将错误信息中的"DataManager"更新为"ViewModel"以保持一致性。

## ✅ 验证步骤

完成修改后，请进行以下验证：

1. **编译检查**
   ```bash
   qmake SiteResConfig_Simple.pro
   make
   ```

2. **功能测试**
   - 创建作动器
   - 编辑作动器
   - 删除作动器
   - 作动器组管理
   - 数据保存和加载

3. **错误处理测试**
   - 验证错误信息正确显示
   - 验证异常情况处理

## 📊 进度跟踪

- [x] 已完成：7/39 (18%)
- [ ] 待完成：32/39 (82%)

### 优先级排序
1. **高优先级**：数据修改方法（影响数据完整性）
2. **中优先级**：数据查询方法（影响功能正常性）
3. **低优先级**：统计和调试方法（影响用户体验）

## 🎯 预期结果

完成迁移后：
- MainWindow完全通过ActuatorViewModel1_2访问作动器数据
- 不再直接依赖ActuatorDataManager
- 获得ViewModel的所有增强功能（缓存、扩展字段等）
- 为MVVM架构奠定基础

## 💡 建议

1. **分批进行**：建议分3-4批完成，每批完成后进行编译测试
2. **备份代码**：修改前备份当前版本
3. **逐步验证**：每修改几个方法就进行一次编译检查
4. **功能测试**：完成后进行完整的功能测试

这个迁移是实现完整MVVM架构的关键步骤，完成后将大大提升代码的可维护性和扩展性。
