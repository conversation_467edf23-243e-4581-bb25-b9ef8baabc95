/**
 * @file SensorDialog_1_2.cpp
 * @brief 传感器创建对话框类实现1_2版本
 * @details 使用Qt Designer设计的传感器参数输入对话框实现，基于MVVM架构
 * <AUTHOR> Assistant
 * @date 2025-08-23
 * @version 1.2.0
 */

#include "SensorDialog_1_2.h"
#include <QJsonObject>
#include <QJsonDocument>
#include <QFile>
#include <QTextStream>
#include "ui_SensorDialog_1_2.h"
#include <QtWidgets/QMessageBox>
#include <QtWidgets/QCalendarWidget>
#include <QtCore/QDateTime>
// 🆕 新增：Excel支持
#include "QtXlsxWriter-master/src/xlsx/xlsxdocument.h"
#include "QtXlsxWriter-master/src/xlsx/xlsxworksheet.h"
#include <QVariant>

namespace UI {

SensorDialog_1_2::SensorDialog_1_2(const QString& groupName, const QString& autoNumber, QWidget* parent)
    : QDialog(parent)
    , ui(new Ui::SensorDialog_1_2)
    , groupName_(groupName)
    , autoNumber_(autoNumber)
    , sensorId_(0)
    , editMode_(false)
    , isValid_(false) {
    
    ui->setupUi(this);
    initializeUI();
    connectSignals();
    setDefaultValues();
}

SensorDialog_1_2::~SensorDialog_1_2() {
    delete ui;
}

void SensorDialog_1_2::initializeUI() {
    // 设置信息标签显示组名和自动编号
    QString displayInfo = QString("%1\\%2").arg(groupName_).arg(autoNumber_);
    ui->infoLabel->setText(displayInfo);

    // 🆕 新增：设置新需求字段的默认值
    // 基础配置 (2字段)
    ui->zeroOffsetSpinBox->setValue(0.0);
    ui->zeroOffsetSpinBox->setRange(-999999.0, 999999.0);
    ui->zeroOffsetSpinBox->setDecimals(6);
    
    ui->enableCheckBox->setChecked(true);
    
    // params参数组 (12字段)
    ui->paramsModelEdit->setText("AKD-8A");
    // 🔧 修复：使用传入的自动生成序列号，格式：传感器_000001
    ui->paramsSnEdit->setText(autoNumber_);
    
    ui->paramsKSpinBox->setValue(20.0);
    ui->paramsKSpinBox->setRange(-999999.0, 999999.0);
    ui->paramsKSpinBox->setDecimals(6);
    
    ui->paramsBSpinBox->setValue(0.0);
    ui->paramsBSpinBox->setRange(-999999.0, 999999.0);
    ui->paramsBSpinBox->setDecimals(6);
    
    ui->paramsPrecisionSpinBox->setValue(0.1);
    ui->paramsPrecisionSpinBox->setRange(0.000001, 999999.0);
    ui->paramsPrecisionSpinBox->setDecimals(6);
    
    // 初始化极性下拉框
    ui->paramsPolarityCombo->addItem("正极性 (+)", 1);
    ui->paramsPolarityCombo->addItem("负极性 (-)", -1);
    ui->paramsPolarityCombo->addItem("双极性 (±)", 9);
    ui->paramsPolarityCombo->addItem("无极性", 0);
    ui->paramsPolarityCombo->setCurrentIndex(1); // 默认为正极性
    
    // 测量范围参数
    ui->paramsMeasUnitSpinBox->setValue(1);
    ui->paramsMeasUnitSpinBox->setRange(0, 999);
    
    ui->paramsMeasRangeMinSpinBox->setValue(-100.0);
    ui->paramsMeasRangeMinSpinBox->setRange(-999999.0, 999999.0);
    ui->paramsMeasRangeMinSpinBox->setDecimals(3);
    
    ui->paramsMeasRangeMaxSpinBox->setValue(100.0);
    ui->paramsMeasRangeMaxSpinBox->setRange(-999999.0, 999999.0);
    ui->paramsMeasRangeMaxSpinBox->setDecimals(3);
    
    // 输出信号范围参数
    ui->paramsOutputSignalUnitSpinBox->setValue(1);
    ui->paramsOutputSignalUnitSpinBox->setRange(0, 999);
    
    ui->paramsOutputSignalRangeMinSpinBox->setValue(-100.0);
    ui->paramsOutputSignalRangeMinSpinBox->setRange(-999999.0, 999999.0);
    ui->paramsOutputSignalRangeMinSpinBox->setDecimals(3);
    
    ui->paramsOutputSignalRangeMaxSpinBox->setValue(100.0);
    ui->paramsOutputSignalRangeMaxSpinBox->setRange(-999999.0, 999999.0);
    ui->paramsOutputSignalRangeMaxSpinBox->setDecimals(3);
    
    // 🆕 设置窗口布局和大小
    setWindowTitle(tr("传感器参数配置 - 新需求版本"));
    //resize(480, 600); // 适应新的简洁界面
}

void SensorDialog_1_2::connectSignals() {
    // 🆕 新增：连接新控件的信号
    // 基础配置参数变化信号
    connect(ui->zeroOffsetSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &SensorDialog_1_2::updateUIState);
    connect(ui->enableCheckBox, &QCheckBox::toggled,
            this, &SensorDialog_1_2::updateUIState);
            
    // 传感器参数变化信号
    connect(ui->paramsModelEdit, &QLineEdit::textChanged,
            this, &SensorDialog_1_2::updateUIState);
    connect(ui->paramsSnEdit, &QLineEdit::textChanged,
            this, &SensorDialog_1_2::updateUIState);
    connect(ui->paramsKSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &SensorDialog_1_2::updateUIState);
    connect(ui->paramsBSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &SensorDialog_1_2::updateUIState);
    connect(ui->paramsPrecisionSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &SensorDialog_1_2::updateUIState);
    connect(ui->paramsPolarityCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &SensorDialog_1_2::updateUIState);
            
    // 测量范围参数变化信号
    connect(ui->paramsMeasUnitSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &SensorDialog_1_2::updateUIState);
    connect(ui->paramsMeasRangeMinSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &SensorDialog_1_2::updateUIState);
    connect(ui->paramsMeasRangeMaxSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &SensorDialog_1_2::updateUIState);
            
    // 输出信号范围参数变化信号
    connect(ui->paramsOutputSignalUnitSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &SensorDialog_1_2::updateUIState);
    connect(ui->paramsOutputSignalRangeMinSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &SensorDialog_1_2::updateUIState);
    connect(ui->paramsOutputSignalRangeMaxSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &SensorDialog_1_2::updateUIState);

    // 重新连接确定按钮，添加验证
    disconnect(ui->okButton, &QPushButton::clicked, this, &QDialog::accept);
    connect(ui->okButton, &QPushButton::clicked, this, &SensorDialog_1_2::onAcceptClicked);

    // 连接取消按钮
    connect(ui->cancelButton, &QPushButton::clicked, this, &SensorDialog_1_2::onRejectClicked);
    
    // 📝 注意：旧的复杂控件信号连接已被删除
}

void SensorDialog_1_2::setDefaultValues() {
    // 🆕 设置新需求字段的默认值
    // 基础配置参数
    ui->zeroOffsetSpinBox->setValue(0.0);
    ui->enableCheckBox->setChecked(true);
    
    // params参数组
    ui->paramsModelEdit->setText("AKD-8A");
    // 🔧 修复：使用自动生成的序列号，确保格式为传感器_000001
    ui->paramsSnEdit->setText(autoNumber_);
    ui->paramsKSpinBox->setValue(20.0);
    ui->paramsBSpinBox->setValue(0.0);
    ui->paramsPrecisionSpinBox->setValue(0.1);
    ui->paramsPolarityCombo->setCurrentIndex(1); // 默认为Positive
    
    // 测量范围参数组
    ui->paramsMeasUnitSpinBox->setValue(1);
    ui->paramsMeasRangeMinSpinBox->setValue(-100.0);
    ui->paramsMeasRangeMaxSpinBox->setValue(100.0);
    
    // 输出信号范围参数组
    ui->paramsOutputSignalUnitSpinBox->setValue(1);
    ui->paramsOutputSignalRangeMinSpinBox->setValue(-100.0);
    ui->paramsOutputSignalRangeMaxSpinBox->setValue(100.0);
    
    // 更新UI状态
    updateUIState();
}

void SensorDialog_1_2::updateUIState() {
    // 验证当前输入并更新状态
    bool valid = validateInput();
    isValid_ = valid;
    
    // 发射验证状态改变信号
    emit validationStatusChanged(valid);
    
    // 更新确定按钮状态
    ui->okButton->setEnabled(valid);
}



void SensorDialog_1_2::onAcceptClicked() {
    if (validateInput()) {
        // 发射参数改变信号
        emit sensorParamsChanged(getSensorParams());
        accept();
    } else {
        QMessageBox::warning(this, tr("输入验证"), tr("请检查输入的参数是否正确。"));
    }
}

SensorParams_1_2 SensorDialog_1_2::getSensorParams() const {
    SensorParams_1_2 params;

    // ================= 🆕 新需求核心字段 (14字段) =================
    
    // 1. 零点偏移
    params.zero_offset = ui->zeroOffsetSpinBox->value();
    
    // 2. 使能状态
    params.enable = ui->enableCheckBox->isChecked();
    
    // 3-14. params嵌套对象的12个字段
    params.params_model = ui->paramsModelEdit->text().trimmed();
    if (params.params_model.isEmpty()) {
        params.params_model = "AKD-8A"; // 默认值
    }
    
    params.params_sn = ui->paramsSnEdit->text().trimmed();
    if (params.params_sn.isEmpty()) {
        params.params_sn = "2223"; // 默认值
    }
    
    params.params_k = ui->paramsKSpinBox->value();
    params.params_b = ui->paramsBSpinBox->value();
    params.params_precision = ui->paramsPrecisionSpinBox->value();
    params.params_polarity = ui->paramsPolarityCombo->currentData().toInt();
    
    params.meas_unit = ui->paramsMeasUnitSpinBox->value();
    params.meas_range_min = ui->paramsMeasRangeMinSpinBox->value();
    params.meas_range_max = ui->paramsMeasRangeMaxSpinBox->value();
    
    params.output_signal_unit = ui->paramsOutputSignalUnitSpinBox->value();
    params.output_signal_range_min = ui->paramsOutputSignalRangeMinSpinBox->value();
    params.output_signal_range_max = ui->paramsOutputSignalRangeMaxSpinBox->value();

    // ================= 🔄 兼容性字段计算 =================
    
    // 基本标识信息（最小化保留）
    params.sensorId = sensorId_;
    params.sensorType = "NewRequirement_1_2"; // 标识新需求版本

    return params;
}

void SensorDialog_1_2::setSensorParams(const SensorParams_1_2& params) {
    // 设置传感器ID
    sensorId_ = params.sensorId;

    // 🆕 新增：设置核心控制参数
    ui->zeroOffsetSpinBox->setValue(params.zero_offset);
    ui->enableCheckBox->setChecked(params.enable);
    
    // 🆕 新增：设置物理参数 (params)
    ui->paramsModelEdit->setText(params.params_model);
    ui->paramsSnEdit->setText(params.params_sn);
    ui->paramsKSpinBox->setValue(params.params_k);
    ui->paramsBSpinBox->setValue(params.params_b);
    ui->paramsPrecisionSpinBox->setValue(params.params_precision);
    // 设置极性下拉框选中项
    for (int i = 0; i < ui->paramsPolarityCombo->count(); ++i) {
        if (ui->paramsPolarityCombo->itemData(i).toInt() == params.params_polarity) {
            ui->paramsPolarityCombo->setCurrentIndex(i);
            break;
        }
    }
    
    // 🆕 新增：设置测量范围参数
    ui->paramsMeasUnitSpinBox->setValue(params.meas_unit);
    ui->paramsMeasRangeMinSpinBox->setValue(params.meas_range_min);
    ui->paramsMeasRangeMaxSpinBox->setValue(params.meas_range_max);
    
    // 🆕 新增：设置输出信号范围参数
    ui->paramsOutputSignalUnitSpinBox->setValue(params.output_signal_unit);
    ui->paramsOutputSignalRangeMinSpinBox->setValue(params.output_signal_range_min);
    ui->paramsOutputSignalRangeMaxSpinBox->setValue(params.output_signal_range_max);

    // 📝 注意：旧的复杂UI控件已被删除，不再设置
    
    // 更新UI状态
    updateUIState();
}

bool SensorDialog_1_2::validateInput() {
    // 基本验证：参数模型和序列号不能为空
    if (ui->paramsModelEdit->text().trimmed().isEmpty()) {
        return false;
    }
    
    if (ui->paramsSnEdit->text().trimmed().isEmpty()) {
        return false;
    }
    
    // 精度必须大于0
    if (ui->paramsPrecisionSpinBox->value() <= 0.0) {
        return false;
    }
    
    // 测量范围验证：最小值必须小于最大值
    if (ui->paramsMeasRangeMinSpinBox->value() >= ui->paramsMeasRangeMaxSpinBox->value()) {
        return false;
    }
    
    // 输出信号范围验证：最小值必须小于最大值
    if (ui->paramsOutputSignalRangeMinSpinBox->value() >= ui->paramsOutputSignalRangeMaxSpinBox->value()) {
        return false;
    }
    
    return true;
}

bool SensorDialog_1_2::validateField(const QString& fieldName, const QVariant& value) {
    // 🆕 新需求：核心字段级验证
    if (fieldName == "params_model") {
        return !value.toString().trimmed().isEmpty();
    } else if (fieldName == "params_sn") {
        return !value.toString().trimmed().isEmpty();
    } else if (fieldName == "params_precision") {
        return value.toDouble() > 0.0;
    } else if (fieldName == "sensorType") {
        return !value.toString().trimmed().isEmpty();
    }
    
    return true;
}

void SensorDialog_1_2::setSensorType(const QString& sensorType) {
    // 🆕 新需求：设置型号到新的控件
    ui->paramsModelEdit->setText(sensorType);
}

void SensorDialog_1_2::setSensorId(int sensorId) {
    sensorId_ = sensorId;
}

int SensorDialog_1_2::getSensorId() const {
    return sensorId_;
}

void SensorDialog_1_2::resetForm() {
    // 🆕 新需求：重置14字段控件到默认值
    
    // 基础配置组 (2字段)
    ui->zeroOffsetSpinBox->setValue(0.0);
    ui->enableCheckBox->setChecked(true);
    
    // 传感器参数配置组 (12字段)
    ui->paramsModelEdit->setText("AKD-8A");
    // 🔧 修复：重置时也使用自动生成的序列号，保持格式一致性
    ui->paramsSnEdit->setText(autoNumber_);
    ui->paramsKSpinBox->setValue(20.0);
    ui->paramsBSpinBox->setValue(0.0);
    ui->paramsPrecisionSpinBox->setValue(0.1);
    // 设置极性为Negative (-1)，需要找到对应的ComboBox索引
    for (int i = 0; i < ui->paramsPolarityCombo->count(); ++i) {
        if (ui->paramsPolarityCombo->itemData(i).toInt() == -1) {
            ui->paramsPolarityCombo->setCurrentIndex(i);
            break;
        }
    }
    ui->paramsMeasUnitSpinBox->setValue(1);
    ui->paramsMeasRangeMinSpinBox->setValue(-100.0);
    ui->paramsMeasRangeMaxSpinBox->setValue(100.0);
    ui->paramsOutputSignalUnitSpinBox->setValue(1);
    ui->paramsOutputSignalRangeMinSpinBox->setValue(-100.0);
    ui->paramsOutputSignalRangeMaxSpinBox->setValue(100.0);
}

void SensorDialog_1_2::setEditMode(bool enabled) {
    editMode_ = enabled;
    
    // 🆕 新需求：在编辑模式下，某些字段可能需要禁用
    if (editMode_) {
        // 编辑模式：序列号通常不能修改
        ui->paramsSnEdit->setReadOnly(true);
    } else {
        // 创建模式：所有字段都可以编辑
        ui->paramsSnEdit->setReadOnly(false);
    }
}

bool SensorDialog_1_2::exportToCSV(const QString& filePath) const {
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        return false;
    }

    QTextStream out(&file);
    out.setCodec("UTF-8");

    SensorParams_1_2 params = getSensorParams();
    
    // ================= 🆕 写入CSV文件头部信息 =================
    
    out << "# 传感器参数配置文件 - 14字段新需求版本" << "\n";
    out << "# 导出时间: " << QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss") << "\n";
    out << "# 格式版本: 1.2" << "\n";
    out << "# 字段数量: 14" << "\n";
    out << "#" << "\n";
    
    // ================= 🆕 写入CSV列头 (14字段) =================
    out << "Field,Value,Type,Description" << "\n";
    
    // ================= 🆕 写入核心数据 (14字段) =================
    
    // 基础配置 (2字段)
    out << "zero_offset," << params.zero_offset << ",double,零点偏移" << "\n";
    out << "enable," << (params.enable ? "true" : "false") << ",bool,使能状态" << "\n";
    
    // params嵌套对象 (12字段)
    out << "params.model,\"" << params.params_model << "\",string,型号" << "\n";
    out << "params.sn,\"" << params.params_sn << "\",string,序列号" << "\n";
    out << "params.k," << params.params_k << ",double,系数K" << "\n";
    out << "params.b," << params.params_b << ",double,系数B" << "\n";
    out << "params.precision," << params.params_precision << ",double,精度" << "\n";
    out << "params.polarity," << params.params_polarity << ",int,极性" << "\n";
    out << "params.meas_unit," << params.meas_unit << ",int,测量单位" << "\n";
    out << "params.meas_range_min," << params.meas_range_min << ",double,测量范围最小值" << "\n";
    out << "params.meas_range_max," << params.meas_range_max << ",double,测量范围最大值" << "\n";
    out << "params.output_signal_unit," << params.output_signal_unit << ",int,输出信号单位" << "\n";
    out << "params.output_signal_range_min," << params.output_signal_range_min << ",double,输出信号范围最小值" << "\n";
    out << "params.output_signal_range_max," << params.output_signal_range_max << ",double,输出信号范围最大值" << "\n";
    
    // ================= 🆕 写入总结信息 =================
    
    out << "\n";
    out << "# 总结信息" << "\n";
    out << "# 字段数量: 14" << "\n";
    out << "# 基础配置: 2个字段" << "\n";
    out << "# 传感器参数: 12个字段" << "\n";
    out << "# 版本: 新需求1_2" << "\n";
    
    file.close();
    return true;
}

// 🚫 已注释：独立JSON导出功能已废弃
//bool SensorDialog_1_2::exportToJSON(const QString& filePath) const {
//    QFile file(filePath);
//    if (!file.open(QIODevice::WriteOnly)) {
//        return false;
//    }
//
//    SensorParams_1_2 params = getSensorParams();
//    QJsonObject jsonObj;
//    
//    // ================= 🆕 新需求的精简JSON结构 (14字段) =================
//    
//    // 核心控制参数（顶级字段）
//    jsonObj["zero_offset"] = params.zero_offset;
//    jsonObj["enable"] = params.enable;
//    
//    // 嵌套的params对象，包含物理和范围参数 (12字段)
//    QJsonObject paramsObj;
//    
//    // 基本物理参数
//    paramsObj["model"] = params.params_model;
//    paramsObj["sn"] = params.params_sn;
//    paramsObj["k"] = params.params_k;
//    paramsObj["b"] = params.params_b;
//    paramsObj["precision"] = params.params_precision;
//    paramsObj["polarity"] = params.params_polarity;
//    
//    // 测量范围参数
//    paramsObj["meas_unit"] = params.meas_unit;
//    paramsObj["meas_range_min"] = params.meas_range_min;
//    paramsObj["meas_range_max"] = params.meas_range_max;
//    
//    // 输出信号范围参数
//    paramsObj["output_signal_unit"] = params.output_signal_unit;
//    paramsObj["output_signal_range_min"] = params.output_signal_range_min;
//    paramsObj["output_signal_range_max"] = params.output_signal_range_max;
//    
//    // 将params对象添加到主 JSON 对象
//    jsonObj["params"] = paramsObj;
//    
//    // ================= 🆕 添加元数据信息（可选） =================
//    
//    QJsonObject metaObj;
//    metaObj["export_time"] = QDateTime::currentDateTime().toString(Qt::ISODate);
//    metaObj["version"] = "1.2";
//    metaObj["format"] = "sensor_params_14_fields";
//    metaObj["total_fields"] = 14;
//    metaObj["description"] = "传感器参数配置 - 14字段新需求版本";
//    
//    // 将元数据添加到 JSON（作为附加信息）
//    jsonObj["_metadata"] = metaObj;
//    
//    // ================= 🆕 写入文件 =================
//    
//    // 写入文件
//    QJsonDocument doc(jsonObj);
//    QByteArray jsonData = doc.toJson(QJsonDocument::Indented); // 使用缩进格式便于阅读
//    
//    qint64 bytesWritten = file.write(jsonData);
//    file.close();
//    
//    // 验证写入是否成功
//    return bytesWritten > 0 && bytesWritten == jsonData.size();
//}

// ================= 🆕 新增：缺失的槽函数实现 =================

void SensorDialog_1_2::onSensorChanged() {
    // 🆕 新需求：传感器选择改变处理（当前版本暂无需特殊处理）
    updateUIState();
}

void SensorDialog_1_2::onSensorTypeChanged() {
    // 🆕 新需求：传感器类型改变处理
    QString currentType = ui->paramsModelEdit->text();
    emit sensorTypeChanged(currentType);
    updateUIState();
}

void SensorDialog_1_2::onUnitTypeChanged() {
    // 🆕 新需求：单位类型改变处理（当前版本暂无需特殊处理）
    updateUIState();
}

void SensorDialog_1_2::onCalibrationDateButtonClicked() {
    // 🆕 新需求：校准日期按钮点击处理（当前版本暂无需特殊处理）
    // 可在此处添加日期选择器等功能
}

void SensorDialog_1_2::onLoginButtonClicked() {
    // 登录按钮点击处理
    QMessageBox::information(this, tr("登录"), tr("登录功能待实现"));
}

void SensorDialog_1_2::onRejectClicked() {
    // 🆕 新需求：取消按钮点击处理
    reject();
}

void SensorDialog_1_2::onResetButtonClicked() {
    // 🆕 新需求：重置按钮点击处理
    resetForm();
    QMessageBox::information(this, tr("重置完成"), tr("所有字段已重置为默认值。"));
}

void SensorDialog_1_2::onPreviewButtonClicked() {
    // 🆕 新需求：预览按钮点击处理
    SensorParams_1_2 params = getSensorParams();
    
    QString preview = tr("传感器参数预览:\n\n")
                     + tr("基础配置:\n")
                     + tr("  零点偏移: %1\n").arg(params.zero_offset)
                     + tr("  使能状态: %1\n\n").arg(params.enable ? "启用" : "禁用")
                     + tr("传感器参数:\n")
                     + tr("  型号: %1\n").arg(params.params_model)
                     + tr("  序列号: %1\n").arg(params.params_sn)
                     + tr("  系数K: %1\n").arg(params.params_k)
                     + tr("  系数B: %1\n").arg(params.params_b)
                     + tr("  精度: %1\n").arg(params.params_precision)
                     + tr("  极性: %1\n\n").arg(params.params_polarity)
                     + tr("测量范围:\n")
                     + tr("  单位类型: %1\n").arg(params.meas_unit)
                     + tr("  最小值: %1\n").arg(params.meas_range_min)
                     + tr("  最大值: %1\n\n").arg(params.meas_range_max)
                     + tr("输出信号范围:\n")
                     + tr("  单位类型: %1\n").arg(params.output_signal_unit)
                     + tr("  最小值: %1\n").arg(params.output_signal_range_min)
                     + tr("  最大值: %1").arg(params.output_signal_range_max);
    
    QMessageBox::information(this, tr("传感器预览"), preview);
}

// ================= 🆕 新增：Excel导入导出功能 =================

bool SensorDialog_1_2::exportToExcel(const QString& filePath) const {
    using namespace QXlsx;
    
    // 创建新的Excel文档
    Document xlsx;
    
    SensorParams_1_2 params = getSensorParams();
    
    // ================= 🆕 设置工作表标题和时间戳 =================
    
    // 添加标题行
    xlsx.write("A1", QString("传感器参数配置表 - %1").arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss")));
    
    // 设置标题样式
    Format titleFormat;
    titleFormat.setFontBold(true);
    titleFormat.setFontSize(14);
    titleFormat.setFontColor(QColor(Qt::darkBlue));
    titleFormat.setHorizontalAlignment(Format::AlignHCenter);
    xlsx.write("A1", QString("传感器参数配置表 - %1").arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss")), titleFormat);
    xlsx.mergeCells("A1:D1"); // 合并标题单元格
    
    // 空行
    int headerRow = 3;
    
    // ================= 🆕 设置列头 (14字段) =================
    
    // 列头设置
    xlsx.write(headerRow, 1, "字段名称");
    xlsx.write(headerRow, 2, "值");
    xlsx.write(headerRow, 3, "类型");
    xlsx.write(headerRow, 4, "描述");
    
    // 设置列头样式
    Format headerFormat;
    headerFormat.setFontBold(true);
    headerFormat.setFontColor(QColor(Qt::white));
    headerFormat.setPatternBackgroundColor(QColor(79, 129, 189));
    headerFormat.setHorizontalAlignment(Format::AlignHCenter);
    headerFormat.setBorderStyle(Format::BorderThin);
    
    xlsx.write(headerRow, 1, "字段名称", headerFormat);
    xlsx.write(headerRow, 2, "值", headerFormat);
    xlsx.write(headerRow, 3, "类型", headerFormat);
    xlsx.write(headerRow, 4, "描述", headerFormat);
    
    // ================= 🆕 写入数据 (14字段) =================
    
    int row = headerRow + 1; // 从列头下一行开始写入数据
    
    // 数据行样式
    Format dataFormat;
    dataFormat.setBorderStyle(Format::BorderThin);
    dataFormat.setPatternBackgroundColor(QColor(245, 245, 245)); // 浅灰色背景
    
    Format alternateFormat;
    alternateFormat.setBorderStyle(Format::BorderThin);
    alternateFormat.setPatternBackgroundColor(QColor(Qt::white)); // 白色背景
    
    // 基础配置分组标题
    Format groupFormat;
    groupFormat.setFontBold(true);
    groupFormat.setPatternBackgroundColor(QColor(200, 230, 201));
    groupFormat.setBorderStyle(Format::BorderThin);
    
    xlsx.write(row, 1, "═══ 基础配置 (2字段) ═══", groupFormat);
    xlsx.mergeCells(QString("A%1:D%1").arg(row));
    row++;
    
    // 基础配置 (2字段)
    Format currentFormat = (row % 2 == 0) ? dataFormat : alternateFormat;
    xlsx.write(row, 1, "zero_offset", currentFormat);
    xlsx.write(row, 2, params.zero_offset, currentFormat);
    xlsx.write(row, 3, "double", currentFormat);
    xlsx.write(row, 4, "零点偏移", currentFormat);
    row++;
    
    currentFormat = (row % 2 == 0) ? dataFormat : alternateFormat;
    xlsx.write(row, 1, "enable", currentFormat);
    xlsx.write(row, 2, params.enable ? "true" : "false", currentFormat);
    xlsx.write(row, 3, "bool", currentFormat);
    xlsx.write(row, 4, "使能状态", currentFormat);
    row++;
    
    // params参数分组标题
    xlsx.write(row, 1, "═══ 传感器参数 (12字段) ═══", groupFormat);
    xlsx.mergeCells(QString("A%1:D%1").arg(row));
    row++;
    
    // params嵌套对象 (12字段)
    currentFormat = (row % 2 == 0) ? dataFormat : alternateFormat;
    xlsx.write(row, 1, "params.model", currentFormat);
    xlsx.write(row, 2, params.params_model, currentFormat);
    xlsx.write(row, 3, "string", currentFormat);
    xlsx.write(row, 4, "型号", currentFormat);
    row++;
    
    currentFormat = (row % 2 == 0) ? dataFormat : alternateFormat;
    xlsx.write(row, 1, "params.sn", currentFormat);
    xlsx.write(row, 2, params.params_sn, currentFormat);
    xlsx.write(row, 3, "string", currentFormat);
    xlsx.write(row, 4, "序列号", currentFormat);
    row++;
    
    currentFormat = (row % 2 == 0) ? dataFormat : alternateFormat;
    xlsx.write(row, 1, "params.k", currentFormat);
    xlsx.write(row, 2, params.params_k, currentFormat);
    xlsx.write(row, 3, "double", currentFormat);
    xlsx.write(row, 4, "系数K (线性系数)", currentFormat);
    row++;
    
    currentFormat = (row % 2 == 0) ? dataFormat : alternateFormat;
    xlsx.write(row, 1, "params.b", currentFormat);
    xlsx.write(row, 2, params.params_b, currentFormat);
    xlsx.write(row, 3, "double", currentFormat);
    xlsx.write(row, 4, "系数B (线性偏移)", currentFormat);
    row++;
    
    currentFormat = (row % 2 == 0) ? dataFormat : alternateFormat;
    xlsx.write(row, 1, "params.precision", currentFormat);
    xlsx.write(row, 2, params.params_precision, currentFormat);
    xlsx.write(row, 3, "double", currentFormat);
    xlsx.write(row, 4, "精度 (必须>0)", currentFormat);
    row++;
    
    currentFormat = (row % 2 == 0) ? dataFormat : alternateFormat;
    xlsx.write(row, 1, "params.polarity", currentFormat);
    xlsx.write(row, 2, params.params_polarity, currentFormat);
    xlsx.write(row, 3, "int", currentFormat);
    xlsx.write(row, 4, "极性 (-1或1)", currentFormat);
    row++;
    
    currentFormat = (row % 2 == 0) ? dataFormat : alternateFormat;
    xlsx.write(row, 1, "params.meas_unit", currentFormat);
    xlsx.write(row, 2, params.meas_unit, currentFormat);
    xlsx.write(row, 3, "int", currentFormat);
    xlsx.write(row, 4, "测量单位类型", currentFormat);
    row++;
    
    currentFormat = (row % 2 == 0) ? dataFormat : alternateFormat;
    xlsx.write(row, 1, "params.meas_range_min", currentFormat);
    xlsx.write(row, 2, params.meas_range_min, currentFormat);
    xlsx.write(row, 3, "double", currentFormat);
    xlsx.write(row, 4, "测量范围最小值", currentFormat);
    row++;
    
    currentFormat = (row % 2 == 0) ? dataFormat : alternateFormat;
    xlsx.write(row, 1, "params.meas_range_max", currentFormat);
    xlsx.write(row, 2, params.meas_range_max, currentFormat);
    xlsx.write(row, 3, "double", currentFormat);
    xlsx.write(row, 4, "测量范围最大值", currentFormat);
    row++;
    
    currentFormat = (row % 2 == 0) ? dataFormat : alternateFormat;
    xlsx.write(row, 1, "params.output_signal_unit", currentFormat);
    xlsx.write(row, 2, params.output_signal_unit, currentFormat);
    xlsx.write(row, 3, "int", currentFormat);
    xlsx.write(row, 4, "输出信号单位类型", currentFormat);
    row++;
    
    currentFormat = (row % 2 == 0) ? dataFormat : alternateFormat;
    xlsx.write(row, 1, "params.output_signal_range_min", currentFormat);
    xlsx.write(row, 2, params.output_signal_range_min, currentFormat);
    xlsx.write(row, 3, "double", currentFormat);
    xlsx.write(row, 4, "输出信号范围最小值", currentFormat);
    row++;
    
    currentFormat = (row % 2 == 0) ? dataFormat : alternateFormat;
    xlsx.write(row, 1, "params.output_signal_range_max", currentFormat);
    xlsx.write(row, 2, params.output_signal_range_max, currentFormat);
    xlsx.write(row, 3, "double", currentFormat);
    xlsx.write(row, 4, "输出信号范围最大值", currentFormat);
    row++;
    
    // ================= 🆕 添加总结信息 =================
    
    row++; // 空行
    Format summaryFormat;
    summaryFormat.setFontBold(true);
    summaryFormat.setFontColor(QColor(Qt::darkGreen));
    summaryFormat.setPatternBackgroundColor(QColor(230, 238, 156));
    summaryFormat.setBorderStyle(Format::BorderThin);
    
    xlsx.write(row, 1, "导出信息", summaryFormat);
    xlsx.write(row, 2, QString("共导出14个字段"), summaryFormat);
    xlsx.write(row, 3, "版本", summaryFormat);
    xlsx.write(row, 4, "新需求1_2", summaryFormat);
    
    // ================= 🆕 设置列宽和样式 =================
    
    // 设置列宽
    xlsx.setColumnWidth(1, 1, 28); // 字段名称列
    xlsx.setColumnWidth(2, 2, 25); // 值列
    xlsx.setColumnWidth(3, 3, 12); // 类型列
    xlsx.setColumnWidth(4, 4, 30); // 描述列
    
    // 设置行高
    xlsx.setRowHeight(1, 25); // 标题行
    xlsx.setRowHeight(headerRow, 20); // 列头行
    
    // 保存文件
    bool saveResult = xlsx.saveAs(filePath);
    
    if (!saveResult) {
        // 如果保存失败，可能是文件被占用或权限问题
        return false;
    }
    
    return true;
}

bool SensorDialog_1_2::importFromExcel(const QString& filePath) {
    using namespace QXlsx;
    
    // 打开Excel文件
    Document xlsx(filePath);
    // QtXlsx会自动加载文件，不需要调用load()方法
    
    // 检查文件是否有效
    if (xlsx.sheetNames().isEmpty()) {
        QMessageBox::warning(const_cast<SensorDialog_1_2*>(this), 
                           tr("错误"), 
                           tr("无法打开Excel文件: %1\n\n请确保：\n1. 文件未被其他程序占用\n2. 文件格式正确(.xlsx)\n3. 文件没有损坏").arg(filePath));
        return false;
    }
    
    // ================= 🆕 从 Excel 读取数据 =================
    
    SensorParams_1_2 params;
    bool hasValidData = false;
    QStringList importedFields;
    QStringList errorMessages;
    
    // 预设默认值
    params.zero_offset = 0.0;
    params.enable = true;
    params.params_model = "AKD-8A";
    params.params_sn = "2223";
    params.params_k = 20.0;
    params.params_b = 0.0;
    params.params_precision = 0.1;
    params.params_polarity = -1;
    params.meas_unit = 1;
    params.meas_range_min = -100.0;
    params.meas_range_max = 100.0;
    params.output_signal_unit = 1;
    params.output_signal_range_min = -100.0;
    params.output_signal_range_max = 100.0;
    
    // 从Excel读取数据（假设数据从第2行开始）
    for (int row = 2; row <= 50; ++row) { // 最多读取50行
        QVariant fieldNameVar = xlsx.read(row, 1);
        QVariant valueVar = xlsx.read(row, 2);
        
        if (fieldNameVar.isNull() || valueVar.isNull()) {
            continue; // 跳过空行
        }
        
        QString fieldName = fieldNameVar.toString().trimmed();
        if (fieldName.isEmpty()) {
            continue;
        }
        
        hasValidData = true;
        
        // ================= 🆕 按字段名称赋值（加强验证） =================
        
        try {
            if (fieldName == "zero_offset") {
                params.zero_offset = valueVar.toDouble();
                importedFields << "零点偏移";
            } else if (fieldName == "enable") {
                QString enableStr = valueVar.toString().toLower();
                params.enable = (enableStr == "true" || enableStr == "1" || enableStr == "yes" || enableStr == "是");
                importedFields << "使能状态";
            } else if (fieldName == "params.model") {
                params.params_model = valueVar.toString().trimmed();
                if (params.params_model.isEmpty()) {
                    errorMessages << QString("第%1行：型号不能为空").arg(row);
                } else {
                    importedFields << "型号";
                }
            } else if (fieldName == "params.sn") {
                params.params_sn = valueVar.toString().trimmed();
                if (params.params_sn.isEmpty()) {
                    errorMessages << QString("第%1行：序列号不能为空").arg(row);
                } else {
                    importedFields << "序列号";
                }
            } else if (fieldName == "params.k") {
                params.params_k = valueVar.toDouble();
                importedFields << "系数K";
            } else if (fieldName == "params.b") {
                params.params_b = valueVar.toDouble();
                importedFields << "系数B";
            } else if (fieldName == "params.precision") {
                params.params_precision = valueVar.toDouble();
                if (params.params_precision <= 0.0) {
                    errorMessages << QString("第%1行：精度必须大于0").arg(row);
                } else {
                    importedFields << "精度";
                }
            } else if (fieldName == "params.polarity") {
                params.params_polarity = valueVar.toInt();
                if (params.params_polarity != -1 && params.params_polarity != 1) {
                    errorMessages << QString("第%1行：极性必须为-1或1").arg(row);
                } else {
                    importedFields << "极性";
                }
            } else if (fieldName == "params.meas_unit") {
                params.meas_unit = valueVar.toInt();
                importedFields << "测量单位";
            } else if (fieldName == "params.meas_range_min") {
                params.meas_range_min = valueVar.toDouble();
                importedFields << "测量范围最小值";
            } else if (fieldName == "params.meas_range_max") {
                params.meas_range_max = valueVar.toDouble();
                importedFields << "测量范围最大值";
            } else if (fieldName == "params.output_signal_unit") {
                params.output_signal_unit = valueVar.toInt();
                importedFields << "输出信号单位";
            } else if (fieldName == "params.output_signal_range_min") {
                params.output_signal_range_min = valueVar.toDouble();
                importedFields << "输出信号范围最小值";
            } else if (fieldName == "params.output_signal_range_max") {
                params.output_signal_range_max = valueVar.toDouble();
                importedFields << "输出信号范围最大值";
            }
        } catch (...) {
            errorMessages << QString("第%1行：数据转换失败 (%2)").arg(row).arg(fieldName);
        }
    }
    
    if (!hasValidData) {
        QMessageBox::warning(const_cast<SensorDialog_1_2*>(this), 
                           tr("错误"), 
                           tr("Excel文件中没有找到有效的传感器数据。\n\n请确保：\n1. 第一列为字段名称\n2. 第二列为对应值\n3. 数据从第2行开始"));
        return false;
    }
    
    // ================= 🆕 范围数据验证 =================
    
    if (params.meas_range_min >= params.meas_range_max) {
        errorMessages << "测量范围最小值必须小于最大值";
    }
    
    if (params.output_signal_range_min >= params.output_signal_range_max) {
        errorMessages << "输出信号范围最小值必须小于最大值";
    }
    
    // ================= 🆕 显示导入结果和错误信息 =================
    
    if (!errorMessages.isEmpty()) {
        QString errorText = "导入过程中发现以下问题：\n\n" + errorMessages.join("\n");
        int result = QMessageBox::question(const_cast<SensorDialog_1_2*>(this), 
                                         tr("数据验证警告"), 
                                         errorText + "\n\n是否仍要继续导入?",
                                         QMessageBox::Yes | QMessageBox::No,
                                         QMessageBox::No);
        if (result == QMessageBox::No) {
            return false;
        }
    }
    
    // ================= 🆕 应用数据到UI =================
    
    setSensorParams(params);
    
    // 显示导入成功信息
    QString successMsg = QString("从 Excel 文件中成功导入传感器数据！\n\n导入的字段：\n%1")
                        .arg(importedFields.join("\n"));
    
    if (!errorMessages.isEmpty()) {
        successMsg += QString("\n\n⚠️ 警告：存在 %1 个数据问题，请检查导入结果。").arg(errorMessages.size());
    }
    
    QMessageBox::information(const_cast<SensorDialog_1_2*>(this), 
                           tr("导入成功"), 
                           successMsg);
    
    return true;
}

} // namespace UI
