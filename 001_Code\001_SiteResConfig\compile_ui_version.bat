@echo off
echo ========================================
echo  SiteResConfig UI文件版本编译测试
echo ========================================

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

g++ --version
if errorlevel 1 (
    echo 错误: MinGW编译器未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o
if exist "ui_*.h" del ui_*.h

echo.
echo 生成UI头文件...
uic ui\MainWindow.ui -o ui_MainWindow.h
if errorlevel 1 (
    echo UI文件生成失败！
    pause
    exit /b 1
)

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo 请检查上面的错误信息。
    echo.
    echo 可能的问题：
    echo 1. UI文件中的控件名称与代码不匹配
    echo 2. 缺少必要的头文件包含
    echo 3. 信号槽连接错误
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  编译成功！UI文件版本已就绪
    echo ========================================
    
    if exist "SiteResConfig.exe" (
        echo 可执行文件: SiteResConfig.exe
        echo.
        echo 🎯 UI文件版本特色:
        echo ✅ 完全基于.ui文件的界面设计
        echo ✅ 标准的Qt开发模式 (.h + .cpp + .ui)
        echo ✅ 可视化界面设计和编辑
        echo ✅ 样式和布局统一管理
        echo.
        echo 启动程序...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
        echo 启动程序...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
        echo 启动程序...
        start release\SiteResConfig.exe
    ) else (
        echo 警告: 找不到可执行文件！
    )
)

echo.
echo 📖 UI文件开发说明:
echo.
echo 🎨 界面设计:
echo - 主界面: ui\MainWindow.ui
echo - 使用Qt Designer可视化编辑
echo - 自动生成ui_MainWindow.h头文件
echo.
echo 🔧 代码结构:
echo - 头文件: include\MainWindow_Qt_Simple.h
echo - 源文件: src\MainWindow_Qt_Simple.cpp  
echo - UI文件: ui\MainWindow.ui
echo.
echo 🎯 开发流程:
echo 1. 使用Qt Designer编辑.ui文件
echo 2. 在.h文件中声明Ui::MainWindow *ui成员
echo 3. 在.cpp文件中包含ui_MainWindow.h
echo 4. 构造函数中调用ui->setupUi(this)
echo.
pause
