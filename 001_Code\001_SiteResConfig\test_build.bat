@echo off
echo ========================================
echo  SiteResConfig 编译测试
echo ========================================

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo 清理之前的构建文件...
if exist "Makefile" del Makefile
if exist "Makefile.Debug" del Makefile.Debug
if exist "Makefile.Release" del Makefile.Release

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo 错误: qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目...
mingw32-make clean
mingw32-make
if errorlevel 1 (
    echo 编译失败！请检查错误信息。
    pause
    exit /b 1
) else (
    echo 编译成功！
)

pause
