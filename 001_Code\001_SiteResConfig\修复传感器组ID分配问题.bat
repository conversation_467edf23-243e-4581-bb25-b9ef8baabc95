@echo off
chcp 65001 >nul
echo.
echo ========================================
echo ✅ 修复传感器组ID分配问题
echo ========================================
echo.

echo 🔍 问题根源分析:
echo 从调试输出发现了问题的真正原因：
echo.
echo 错误的数据结构:
echo   组ID: 1, 传感器索引: 0, 组名称: 载荷_传感器组  ← 传感器1
echo   组ID: 2, 传感器索引: 0, 组名称: 载荷_传感器组  ← 传感器2
echo   组ID: 3, 传感器索引: 0, 组名称: 载荷_传感器组  ← 传感器3
echo.
echo 问题分析:
echo 1. 每个传感器都被分配了不同的组ID（1、2、3）
echo 2. 每个传感器的索引都是0，所以都显示组名称
echo 3. 本应该是1个组包含3个传感器，变成了3个组各包含1个传感器
echo.

echo 🔧 修复内容:
echo.
echo 修复 generateSensorGroupIdFromName() 方法:
echo.
echo 修复前:
echo   - 每次调用都生成新的组ID
echo   - 相同组名称被分配不同ID
echo   - 导致每个传感器成为独立的组
echo.
echo 修复后:
echo   - 首先检查是否已存在相同名称的组
echo   - 如果存在，返回现有组的ID
echo   - 如果不存在，才生成新的组ID
echo.
echo 修复逻辑:
echo   1. 获取所有现有传感器组
echo   2. 遍历查找是否有相同组名称
echo   3. 如果找到，返回现有组ID
echo   4. 如果没找到，生成新的唯一组ID
echo.

echo 📊 预期修复效果:
echo.
echo 修复后的数据结构应该是:
echo   组ID: 1, 传感器索引: 0, 组名称: 载荷_传感器组  ← 第1个传感器
echo   组ID: 1, 传感器索引: 1, 组名称: 载荷_传感器组  ← 第2个传感器
echo   组ID: 1, 传感器索引: 2, 组名称: 载荷_传感器组  ← 第3个传感器
echo.
echo Excel显示效果:
echo   组序号 ^| 传感器组名称    ^| 传感器序列号      ^| ...
echo   ------|---------------|-----------------|----
echo   1     ^| 载荷_传感器组   ^| 传感器_000001    ^| ...  ← 显示组名称
echo   1     ^|               ^| 传感器_000002    ^| ...  ← 空白
echo   1     ^|               ^| 传感器_000003    ^| ...  ← 空白
echo.

echo 🚀 测试步骤:
echo.
echo 步骤1: 重新编译应用程序
echo - 确保修复的generateSensorGroupIdFromName方法生效
echo.
echo 步骤2: 清理现有数据（重要！）
echo - 删除或重命名现有的传感器组
echo - 或者重新启动应用程序
echo - 确保从干净状态开始测试
echo.
echo 步骤3: 重新创建传感器组和传感器
echo - 创建一个传感器组（如"载荷_传感器组"）
echo - 在该组中添加多个传感器
echo - 观察日志输出
echo.
echo 步骤4: 检查日志输出
echo 应该看到类似的日志:
echo   "为传感器组生成新ID: 载荷_传感器组 → 1234"  ← 第1个传感器
echo   "找到现有传感器组: 载荷_传感器组 → ID=1234"  ← 第2个传感器
echo   "找到现有传感器组: 载荷_传感器组 → ID=1234"  ← 第3个传感器
echo.
echo 步骤5: 导出并验证
echo - 导出传感器详细配置
echo - 检查调试输出和Excel文件
echo.

echo ✅ 预期的正确调试输出:
echo.
echo 创建传感器时的日志:
echo   "为传感器组生成新ID: 载荷_传感器组 → 1234"
echo   "传感器组保存成功: 载荷_传感器组 (组ID: 1234, 传感器数量: 1)"
echo   
echo   "找到现有传感器组: 载荷_传感器组 → ID=1234"
echo   "传感器组保存成功: 载荷_传感器组 (组ID: 1234, 传感器数量: 2)"
echo   
echo   "找到现有传感器组: 载荷_传感器组 → ID=1234"
echo   "传感器组保存成功: 载荷_传感器组 (组ID: 1234, 传感器数量: 3)"
echo.
echo 导出时的调试输出:
echo   "=== 传感器组名称显示逻辑 ==="
echo   "组ID: 1234, 传感器索引: 0, 组名称: 载荷_传感器组"
echo   "传感器序列号: 传感器_000001, 显示组名称: 是"
echo   "写入组名称到行4列2: '载荷_传感器组'"
echo   
echo   "=== 传感器组名称显示逻辑 ==="
echo   "组ID: 1234, 传感器索引: 1, 组名称: 载荷_传感器组"
echo   "传感器序列号: 传感器_000002, 显示组名称: 否"
echo   "写入空值到行5列2"
echo   
echo   "=== 传感器组名称显示逻辑 ==="
echo   "组ID: 1234, 传感器索引: 2, 组名称: 载荷_传感器组"
echo   "传感器序列号: 传感器_000003, 显示组名称: 否"
echo   "写入空值到行6列2"
echo.

echo 🎯 关键验证点:
echo.
echo 1. 组ID一致性:
echo    - 同一组的所有传感器应该有相同的组ID
echo    - 不同组应该有不同的组ID
echo.
echo 2. 传感器索引正确:
echo    - 第1个传感器: 索引=0, 显示组名称=是
echo    - 第2个传感器: 索引=1, 显示组名称=否
echo    - 第3个传感器: 索引=2, 显示组名称=否
echo.
echo 3. Excel显示正确:
echo    - 组名称只在每组第一行显示
echo    - 同组其他行的组名称列为空白
echo.

echo ⚠️ 注意事项:
echo.
echo 1. 数据清理:
echo    - 修复前创建的数据可能仍有问题
echo    - 建议重新创建传感器组和传感器
echo.
echo 2. 测试顺序:
echo    - 先测试单个组多个传感器
echo    - 再测试多个组的情况
echo.
echo 3. 日志监控:
echo    - 重点关注组ID分配的日志
echo    - 确认相同组名称复用相同ID
echo.

echo 🎉 修复完成！
echo 现在传感器组ID分配应该正确，组名称显示功能也应该正常工作了。
echo.

pause
