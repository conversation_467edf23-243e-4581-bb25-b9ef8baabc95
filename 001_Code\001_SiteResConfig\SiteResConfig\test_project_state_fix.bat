@echo off
chcp 65001 >nul
echo ========================================
echo 工程状态修复测试程序编译和运行
echo ========================================
echo.

echo 正在检查Qt环境...
where qmake >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未找到qmake，请确保Qt环境已正确配置
    echo 请检查PATH环境变量是否包含Qt的bin目录
    pause
    exit /b 1
)

echo ✅ Qt环境检查通过
echo.

echo 正在清理旧的编译文件...
if exist Makefile del /q Makefile
if exist Makefile.Debug del /q Makefile.Debug
if exist Makefile.Release del /q Makefile.Release
if exist object_script.* del /q object_script.*
if exist .qmake.stash del /q .qmake.stash
if exist debug\ del /q /s debug\
if exist release\ del /q /s release\
echo ✅ 清理完成
echo.

echo 正在生成Makefile...
qmake test_project_state_fix.pro
if %errorlevel% neq 0 (
    echo ❌ 错误：qmake失败
    pause
    exit /b 1
)
echo ✅ Makefile生成成功
echo.

echo 正在编译测试程序...
mingw32-make
if %errorlevel% neq 0 (
    echo ❌ 错误：编译失败
    echo.
    echo 可能的解决方案：
    echo 1. 检查是否安装了MinGW编译器
    echo 2. 检查Qt版本是否与编译器兼容
    echo 3. 检查项目文件配置是否正确
    pause
    exit /b 1
)
echo ✅ 编译成功
echo.

echo 正在运行测试程序...
if exist debug\test_project_state_fix.exe (
    echo 🚀 启动测试程序...
    start debug\test_project_state_fix.exe
    echo.
    echo 📋 测试说明：
    echo 1. 程序启动后，观察状态显示
    echo 2. 点击"新建工程"按钮，测试新建工程流程
    echo 3. 点击"打开工程"按钮，测试打开工程流程
    echo 4. 点击"测试详细信息"按钮，验证详细信息功能
    echo 5. 点击树形控件中的节点，测试详细信息显示
    echo 6. 点击"清空"按钮，测试工程关闭流程
    echo.
    echo 🎯 测试重点：
    echo - 新建工程和打开工程后，详细信息功能是否一致
    echo - 工程状态验证是否正常工作
    echo - 数据管理器同步验证是否有效
    echo.
    echo ✅ 测试程序已启动，请按照上述步骤进行测试
) else (
    echo ❌ 错误：可执行文件未找到
    echo 预期路径：debug\test_project_state_fix.exe
    pause
    exit /b 1
)

echo.
echo ========================================
echo 测试完成
echo ========================================
pause 