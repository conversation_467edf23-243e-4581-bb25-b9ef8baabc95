#include <QApplication>
#include <QMainWindow>
#include <QVBoxLayout>
#include <QPushButton>
#include <QDebug>
#include <QMessageBox>

// 包含必要的头文件
#include "src/BasicInfoWidget.h"
#include "src/DetailInfoPanel.h"

class TestWindow : public QMainWindow
{
    Q_OBJECT

public:
    TestWindow(QWidget *parent = nullptr) : QMainWindow(parent)
    {
        setWindowTitle("载荷1传感器选择列修复测试");
        setGeometry(100, 100, 1200, 800);

        // 创建中央部件
        QWidget *centralWidget = new QWidget(this);
        setCentralWidget(centralWidget);

        // 创建布局
        QVBoxLayout *layout = new QVBoxLayout(centralWidget);

        // 创建详细信息面板
        m_detailPanel = new DetailInfoPanel(this);
        layout->addWidget(m_detailPanel);

        // 创建测试按钮
        QPushButton *testBtn = new QPushButton("测试载荷1传感器选择列修复", this);
        layout->addWidget(testBtn);

        // 连接信号
        connect(testBtn, &QPushButton::clicked, this, &TestWindow::testLoad1SensorFix);
    }

private slots:
    void testLoad1SensorFix()
    {
        qDebug() << "\n🧪 测试载荷1传感器选择列修复";
        
        // 创建测试用的控制通道根节点信息
        NodeInfo rootInfo;
        rootInfo.nodeName = "控制通道";
        rootInfo.nodeType = "控制通道组";
        rootInfo.status = NodeStatus::Online;
        
        // 添加子通道信息
        SubNodeInfo channel1;
        channel1.name = "CH1";
        channel1.type = "控制通道";
        channel1.deviceName = "LD-B1";
        channel1.deviceId = "CH1";
        channel1.isConnected = true;
        
        // 🎯 关键测试：设置载荷1传感器选择属性
        channel1.setProperty("载荷1传感器选择", "载荷传感器_001");
        channel1.setProperty("载荷2传感器选择", "载荷传感器_002");
        channel1.setProperty("位置传感器选择", "位置传感器_001");
        channel1.setProperty("控制作动器选择", "伺服作动器_001");
        channel1.setProperty("下位机ID", "1");
        channel1.setProperty("站点ID", "1");
        channel1.setProperty("使能状态", "✅");
        channel1.setProperty("控制作动器极性", "正向");
        channel1.setProperty("载荷1传感器极性", "正向");
        channel1.setProperty("载荷2传感器极性", "正向");
        channel1.setProperty("位置传感器极性", "正向");
        rootInfo.addSubNode(channel1);
        
        SubNodeInfo channel2;
        channel2.name = "CH2";
        channel2.type = "控制通道";
        channel2.deviceName = "LD-B2";
        channel2.deviceId = "CH2";
        channel2.isConnected = true;
        
        // 🎯 关键测试：设置载荷1传感器选择属性（空值测试）
        channel2.setProperty("载荷1传感器选择", "");  // 空值测试
        channel2.setProperty("载荷2传感器选择", "载荷传感器_004");
        channel2.setProperty("位置传感器选择", "位置传感器_002");
        channel2.setProperty("控制作动器选择", "伺服作动器_002");
        channel2.setProperty("下位机ID", "2");
        channel2.setProperty("站点ID", "2");
        channel2.setProperty("使能状态", "✅");
        channel2.setProperty("控制作动器极性", "正向");
        channel2.setProperty("载荷1传感器极性", "正向");
        channel2.setProperty("载荷2传感器极性", "正向");
        channel2.setProperty("位置传感器极性", "正向");
        rootInfo.addSubNode(channel2);
        
        // 设置到详细信息面板
        m_detailPanel->setNodeInfo(rootInfo);
        
        qDebug() << "✅ 测试数据已设置，包含" << rootInfo.subNodes.size() << "个子通道";
        qDebug() << "  - CH1 载荷1传感器选择:" << channel1.getProperty("载荷1传感器选择").toString();
        qDebug() << "  - CH2 载荷1传感器选择:" << channel2.getProperty("载荷1传感器选择").toString();
        
        QMessageBox::information(this, "测试完成", 
            QString("载荷1传感器选择列修复测试完成！\n\n"
                   "CH1: %1\n"
                   "CH2: %2\n\n"
                   "请检查详细信息面板中的载荷1传感器选择列是否正确显示数据。")
            .arg(channel1.getProperty("载荷1传感器选择").toString())
            .arg(channel2.getProperty("载荷1传感器选择").toString().isEmpty() ? "空值" : channel2.getProperty("载荷1传感器选择").toString()));
    }

private:
    DetailInfoPanel *m_detailPanel;
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    TestWindow window;
    window.show();
    
    return app.exec();
}

#include "test_load1_sensor_fix.moc" 