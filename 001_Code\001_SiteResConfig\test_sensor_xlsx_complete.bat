@echo off
echo ========================================
echo  测试传感器详细配置完整XLSX导出
echo ========================================

REM 设置Qt环境变量
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目（测试传感器详细配置完整XLSX导出）...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo 请检查上面的错误信息。
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！传感器详细配置完整XLSX导出已实现
    echo ========================================
    
    echo.
    echo ✅ 实现的功能:
    echo - 传感器详细配置33列完整导出
    echo - 参考作动器17列格式实现
    echo - 包含所有SensorParams字段
    echo - 支持单独导出和完整项目导出
    echo.
    echo 🔧 实现详情:
    echo 1. 修改XLSDataExporter::exportSensorDetails()
    echo 2. 更新传感器详细信息表头（33列）
    echo 3. 重写addSensorDetailToExcel()方法
    echo 4. 更新完整项目导出中的传感器部分
    echo 5. 自动调整列宽支持33列
    echo.
    echo 📊 传感器详细配置33列格式:
    echo 基本信息（11列）:
    echo - 传感器ID, 传感器名称, 序列号, 传感器类型, EDS标识
    echo - 尺寸, 型号, 量程, 精度, 单位, 灵敏度
    echo.
    echo 校准和范围信息（11列）:
    echo - 校准启用, 校准日期, 校准执行人, 单位类型, 单位值
    echo - 输入范围, 满量程最大值, 满量程最大值单位
    echo - 满量程最小值, 满量程最小值单位
    echo.
    echo 信号调理参数（11列）:
    echo - 极性, 前置放大增益, 后置放大增益, 总增益, Delta K增益
    echo - 比例因子, 启用激励, 激励电压, 激励平衡, 激励频率
    echo - 相位, 编码器分辨率
    echo.
    echo 🎯 与作动器对比:
    echo - 作动器: 17列完整格式 ✅
    echo - 传感器: 33列完整格式 ✅（新增）
    echo - 统一的导出风格和格式
    echo - 相同的样式和布局
    echo.
    
    if exist "SiteResConfig.exe" (
        echo 启动程序验证功能...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 启动程序验证功能...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 启动程序验证功能...
        start release\SiteResConfig.exe
    )
)

echo.
echo 📖 详细测试指南:
echo.
echo 🎮 传感器详细配置导出测试:
echo 1. 启动软件后，新建一个项目
echo 2. 创建传感器组，添加传感器
echo 3. 配置传感器的详细参数（所有字段）
echo 4. 选择菜单: 数据导出 → 导出传感器详细信息到Excel
echo 5. 保存Excel文件
echo 6. 打开Excel文件验证33列完整格式
echo.
echo 🎮 完整项目导出测试:
echo 1. 创建包含传感器和作动器的完整项目
echo 2. 选择菜单: 数据导出 → 导出完整项目到Excel
echo 3. 验证Excel文件包含多个工作表
echo 4. 检查传感器详细信息工作表是否为33列格式
echo 5. 检查作动器详细信息工作表是否为17列格式
echo.
echo 🎮 数据完整性验证:
echo 1. 验证所有传感器字段都正确导出
echo 2. 验证布尔值字段显示为"是/否"
echo 3. 验证数值字段格式正确
echo 4. 验证字符串字段内容完整
echo 5. 验证表头与数据列对应正确
echo.
echo ✅ 预期结果:
echo - 传感器详细配置导出为33列完整格式
echo - 所有SensorParams字段都包含在导出中
echo - Excel格式专业，样式统一
echo - 与作动器导出风格一致
echo - 支持单独导出和完整项目导出
echo.
echo 🚨 如果测试失败:
echo - 检查编译错误信息
echo - 验证SensorParams字段访问正确
echo - 确认Excel文件格式正确
echo - 检查列数和表头对应关系
echo.
pause
