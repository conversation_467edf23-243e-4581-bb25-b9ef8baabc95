# 📊 作动器工作表功能使用指南

## 🎯 功能概述

现在XLSX导出功能支持为"作动器"创建独立的工作表，使用我们设计好的17列作动器存储格式，支持作动器组层级结构管理。

## 📋 核心功能

### 1. 独立作动器工作表
- **工作表名称**: "作动器"
- **存储格式**: 17列完整布局
- **数据结构**: 支持作动器组层级结构
- **样式设计**: 专业的表头和分组样式

### 2. 三种使用方式

#### 方式一：单独创建作动器工作表
```cpp
// 在现有Excel文档中创建作动器工作表
QXlsx::Document document;
QList<UI::ActuatorGroup> actuatorGroups = getActuatorGroups();
XLSDataExporter exporter;
bool success = exporter.createActuatorWorksheet(&document, actuatorGroups);
```

#### 方式二：向现有Excel文件添加作动器工作表
```cpp
// 向现有Excel文件添加作动器工作表
QString filePath = "existing_project.xlsx";
QList<UI::ActuatorGroup> actuatorGroups = getActuatorGroups();
XLSDataExporter exporter;
bool success = exporter.addActuatorWorksheetToExcel(filePath, actuatorGroups);
```

#### 方式三：导出完整项目（硬件树+作动器）
```cpp
// 导出完整项目，包含硬件树和作动器工作表
QTreeWidget* treeWidget = getHardwareTree();
QList<UI::ActuatorGroup> actuatorGroups = getActuatorGroups();
QString filePath = "complete_project.xlsx";
XLSDataExporter exporter;
bool success = exporter.exportCompleteProjectWithActuators(treeWidget, actuatorGroups, filePath);
```

## 📊 作动器工作表格式

### 表头信息区域 (1-4行)
```
A1: 作动器配置数据表
A2: 导出时间: 2025-08-14 15:30:00
A3: 说明: 包含作动器组及其作动器的完整配置信息
A4: [空行]
```

### 数据表头 (5行) - 17列完整布局
```
A5: 组序号          | B5: 作动器组名称    | C5: 作动器序号      | D5: 作动器序列号
E5: 作动器类型      | F5: Unit类型        | G5: Unit名称        | H5: 行程(m)
I5: 位移(m)         | J5: 拉伸面积(m²)    | K5: 压缩面积(m²)    | L5: 极性
M5: Deliver(V)      | N5: 频率(Hz)        | O5: 输出倍数        | P5: 平衡(V)
Q5: 备注
```

### 数据行示例 (6行开始)
```
1 | 液压作动器组 | 1 | ACT_HYD_001 | 单出杆 | m | 米 | 0.30 | 0.30 | 0.60 | 0.50 | Positive | 0.100 | 100.00 | 1.000 | 0.000 | 主液压缸
1 |             | 2 | ACT_HYD_002 | 单出杆 | m | 米 | 0.25 | 0.25 | 0.45 | 0.40 | Positive | 0.080 | 120.00 | 1.000 | 0.000 | 副液压缸
2 | 电动作动器组 | 1 | ACT_ELE_001 | 双出杆 | mm| 毫米| 0.20 | 0.20 | 0.30 | 0.30 | Positive | 0.050 | 1000.00| 1.000 | 0.000 | 精密电动缸
```

## 🎨 样式设计特点

### 表头样式
- **背景色**: 深蓝色 (#4472C4)
- **字体色**: 白色
- **字体**: 微软雅黑, 11pt, 粗体
- **对齐**: 居中对齐
- **边框**: 全边框, 白色, 1pt

### 分组行样式
- **背景色**: 浅蓝色 (#E7F3FF)
- **字体**: 微软雅黑, 10pt, 粗体
- **边框**: 全边框, 蓝色, 1pt

### 数据行样式
- **奇数组背景**: 浅灰色 (#F8F9FA)
- **偶数组背景**: 白色
- **字体**: 微软雅黑, 10pt, 常规
- **边框**: 全边框, 灰色, 0.5pt

### 列宽设置
```
A列(组序号): 8        | B列(作动器组名称): 20  | C列(作动器序号): 10    | D列(作动器序列号): 15
E列(作动器类型): 12   | F列(Unit类型): 10      | G列(Unit名称): 10      | H列(行程): 10
I列(位移): 10         | J列(拉伸面积): 12      | K列(压缩面积): 12      | L列(极性): 10
M列(Deliver): 12      | N列(频率): 10          | O列(输出倍数): 12      | P列(平衡): 10
Q列(备注): 25
```

## 🔧 API接口详解

### 核心方法

#### createActuatorWorksheet
```cpp
/**
 * @brief 在现有Excel文档中创建作动器工作表
 * @param document Excel文档指针
 * @param actuatorGroups 作动器组列表
 * @return 创建是否成功
 */
bool createActuatorWorksheet(QXlsx::Document* document, const QList<UI::ActuatorGroup>& actuatorGroups);
```

#### addActuatorWorksheetToExcel
```cpp
/**
 * @brief 向现有Excel文档添加作动器工作表
 * @param filePath Excel文件路径
 * @param actuatorGroups 作动器组列表
 * @return 添加是否成功
 */
bool addActuatorWorksheetToExcel(const QString& filePath, const QList<UI::ActuatorGroup>& actuatorGroups);
```

#### exportCompleteProjectWithActuators
```cpp
/**
 * @brief 导出完整项目（硬件树 + 作动器工作表）
 * @param treeWidget 硬件树控件
 * @param actuatorGroups 作动器组列表
 * @param filePath Excel文件路径
 * @return 导出是否成功
 */
bool exportCompleteProjectWithActuators(QTreeWidget* treeWidget, const QList<UI::ActuatorGroup>& actuatorGroups, const QString& filePath);
```

### 辅助方法

#### writeActuatorWorksheetHeader
```cpp
/**
 * @brief 写入作动器工作表表头信息
 * @param worksheet 工作表指针
 */
void writeActuatorWorksheetHeader(QXlsx::Worksheet* worksheet);
```

#### applyActuatorWorksheetStyles
```cpp
/**
 * @brief 应用作动器工作表样式
 * @param worksheet 工作表指针
 * @param lastRow 最后一行行号
 */
void applyActuatorWorksheetStyles(QXlsx::Worksheet* worksheet, int lastRow);
```

## 📁 文件结构示例

### 完整项目Excel文件结构
```
SiteResConfig_Complete_20250814_153000.xlsx
├── 硬件配置 (原有硬件树数据)
├── 作动器 (新增作动器工作表)
│   ├── 表头信息区域 (1-4行)
│   ├── 数据表头 (5行)
│   └── 作动器数据 (6行开始)
└── 传感器详细配置 (如果有传感器数据)
```

## 🎯 使用场景

### 场景一：项目导出
当需要导出完整的项目配置时，使用 `exportCompleteProjectWithActuators` 方法，一次性生成包含硬件树和作动器的完整Excel文件。

### 场景二：增量更新
当已有项目Excel文件，需要添加或更新作动器信息时，使用 `addActuatorWorksheetToExcel` 方法，向现有文件添加作动器工作表。

### 场景三：专门导出
当只需要导出作动器配置时，可以先创建空的Excel文档，然后使用 `createActuatorWorksheet` 方法创建专门的作动器工作表。

## 🔄 数据流程

### 1. 数据准备
```cpp
// 准备作动器组数据
QList<UI::ActuatorGroup> actuatorGroups;
UI::ActuatorGroup group1;
group1.groupId = 1;
group1.groupName = u8"液压作动器组";
group1.groupType = u8"液压系统";
group1.createTime = QDateTime::currentDateTime().toString("yyyy-MM-dd");
// ... 添加作动器到group1.actuators
actuatorGroups.append(group1);
```

### 2. 导出执行
```cpp
// 执行导出
XLSDataExporter exporter;
QString filePath = "project_with_actuators.xlsx";
bool success = exporter.addActuatorWorksheetToExcel(filePath, actuatorGroups);

if (success) {
    qDebug() << u8"作动器工作表创建成功";
} else {
    qDebug() << u8"创建失败:" << exporter.getLastError();
}
```

### 3. 结果验证
- 检查Excel文件中是否存在"作动器"工作表
- 验证17列数据格式是否正确
- 确认分组显示和样式是否符合设计

## 🎉 功能优势

### 1. 专业性
- 独立的作动器工作表
- 17列完整的数据格式
- 专业的表头和样式设计

### 2. 灵活性
- 三种不同的使用方式
- 支持现有文件的增量更新
- 与硬件树导出完美集成

### 3. 完整性
- 支持作动器组层级结构
- Unit字段双列存储
- 完整的数据验证机制

### 4. 易用性
- 简洁的API接口
- 详细的错误信息
- 完整的使用文档

现在您可以在XLSX导出时为作动器创建专门的工作表，使用我们设计好的17列存储格式，完美支持作动器组的层级结构管理！
