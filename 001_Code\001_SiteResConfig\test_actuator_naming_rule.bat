@echo off
echo ========================================
echo  作动器组命名规则测试 - "容量_作动器"
echo ========================================

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o
if exist "ui_*.h" del ui_*.h

echo.
echo 生成UI头文件...
uic ui\MainWindow.ui -o ui_MainWindow.h
if errorlevel 1 (
    echo UI文件生成失败！
    pause
    exit /b 1
) else (
    echo ✅ UI头文件生成成功: ui_MainWindow.h
)

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo.
    echo 修改内容：
    echo ✅ 作动器组命名规则更新
    echo ✅ 菜单显示名称更新
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！作动器组命名规则已更新
    echo ========================================
    
    if exist "SiteResConfig.exe" (
        echo 可执行文件: SiteResConfig.exe
        echo.
        echo ✅ 作动器组命名规则已实现！
        echo.
        echo 📝 新的命名规则:
        echo 格式: "容量_作动器"
        echo.
        echo 🎯 快速创建选项:
        echo ├─ 50kN_作动器
        echo ├─ 100kN_作动器
        echo ├─ 200kN_作动器
        echo └─ 500kN_作动器
        echo.
        echo 🌳 创建后的树形结构示例:
        echo 任务1
        echo ├─ 作动器
        echo │  ├─ 50kN_作动器 (类型: "作动器组", 无图标)
        echo │  │  ├─ 作动器1 [50kN] (类型: "作动器设备")
        echo │  │  └─ 作动器2 [50kN] (类型: "作动器设备")
        echo │  ├─ 100kN_作动器 (类型: "作动器组", 无图标)
        echo │  │  ├─ 作动器1 [100kN] (类型: "作动器设备")
        echo │  │  └─ 作动器2 [100kN] (类型: "作动器设备")
        echo │  └─ 自定义作动器组 (类型: "作动器组", 无图标)
        echo ├─ 传感器
        echo └─ 硬件节点资源
        echo.
        echo 🎨 命名规则优势:
        echo - 简洁明了: 直接显示容量和类型
        echo - 易于识别: 下划线分隔，结构清晰
        echo - 统一格式: 所有量程组使用相同格式
        echo - 便于排序: 按容量大小自然排序
        echo.
        echo 📋 测试要点:
        echo 1. 快速创建"50kN_作动器"组
        echo 2. 快速创建"100kN_作动器"组
        echo 3. 快速创建"200kN_作动器"组
        echo 4. 快速创建"500kN_作动器"组
        echo 5. 验证组名格式正确
        echo 6. 验证组内设备自动创建
        echo 7. 验证日志记录信息
        echo.
        echo 🔧 技术实现:
        echo - CreateActuatorGroupByCapacity方法
        echo - 格式: QString("%1_作动器").arg(capacity)
        echo - 菜单显示: tr("50kN_作动器")
        echo - 类型标识: "作动器组"
        echo.
        echo 💡 使用场景:
        echo - 50kN_作动器: 小型精密试验
        echo - 100kN_作动器: 中型常规试验
        echo - 200kN_作动器: 大型结构试验
        echo - 500kN_作动器: 重载极限试验
        echo.
        echo 启动程序测试作动器组命名规则...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
        echo 启动程序...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
        echo 启动程序...
        start release\SiteResConfig.exe
    ) else (
        echo 警告: 找不到可执行文件！
    )
)

echo.
echo 📖 作动器组命名规则测试指南:
echo.
echo 🎯 快速创建测试:
echo 1. 右键点击"作动器"节点
echo 2. 选择"快速创建作动器组"
echo 3. 验证菜单选项显示为:
echo    - "50kN_作动器"
echo    - "100kN_作动器"
echo    - "200kN_作动器"
echo    - "500kN_作动器"
echo 4. 点击任一选项，如"100kN_作动器"
echo 5. 验证创建的组名为"100kN_作动器"
echo 6. 验证组内自动创建两个示例作动器
echo.
echo 🎯 自定义创建测试:
echo 1. 右键点击"作动器"节点
echo 2. 选择"新建" → "作动器组"
echo 3. 输入自定义名称，如"液压_作动器"
echo 4. 验证组创建成功
echo 5. 验证组类型为"作动器组"
echo.
echo 🔍 验证要点:
echo - 快速创建的组名格式: "容量_作动器"
echo - 菜单显示名称与组名一致
echo - 组内设备自动创建且类型正确
echo - 日志记录包含正确的组名
echo - 树形结构层次清晰
echo.
echo 📊 命名规则对比:
echo 旧格式: "50kN作动器组", "100kN作动器组"
echo 新格式: "50kN_作动器", "100kN_作动器"
echo 优势: 更简洁、更统一、更易识别
echo.
pause
