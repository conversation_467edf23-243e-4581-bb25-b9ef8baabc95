# 🔧 CSV层级格式修改完成

## 📋 **修改要求**

将试验配置的CSV输出格式修改为层级结构：

**修改前**：
```
试验节点,控制通道,,,
试验节点,CH1,,,
试验节点,载荷1,传感器_000001,,
试验节点,载荷2,传感器_000002,,
试验节点,位置,传感器_000003,,
试验节点,控制,作动器_000001,,
```

**修改后**：
```
试验节点,控制通道,,,
,CH1,,,
,,载荷1,传感器_000001,
,,载荷2,传感器_000002,
,,位置,传感器_000003,
,,控制,作动器_000001,
```

## 🔧 **修改实现**

### **修改了SaveTreeToCSV方法中的输出格式**

在第2020-2036行实现了层级格式：

```cpp
// 对试验配置的子节点使用特殊格式
if (prefix == QStringLiteral("试验") && itemType == QStringLiteral("试验节点")) {
    if (parsedName == QStringLiteral("CH1") || parsedName == QStringLiteral("CH2")) {
        // CH1、CH2节点：第一列为空
        out << "," << FormatCSVField(parsedName) << "," << FormatCSVField(param1) << "," << FormatCSVField(param2) << "," << FormatCSVField(param3) << "\n";
    } else if (parsedName == QStringLiteral("载荷1") || parsedName == QStringLiteral("载荷2") ||
               parsedName == QStringLiteral("位置") || parsedName == QStringLiteral("控制")) {
        // 载荷1、载荷2、位置、控制节点：前两列为空
        out << ",," << FormatCSVField(parsedName) << "," << FormatCSVField(param1) << "," << FormatCSVField(param3) << "\n";
    } else {
        // 其他试验节点：正常显示类型
        out << FormatCSVField(itemType) << "," << FormatCSVField(parsedName) << "," << FormatCSVField(param1) << "," << FormatCSVField(param2) << "," << FormatCSVField(param3) << "\n";
    }
} else {
    // 非试验节点：正常显示类型
    out << FormatCSVField(itemType) << "," << FormatCSVField(parsedName) << "," << FormatCSVField(param1) << "," << FormatCSVField(param2) << "," << FormatCSVField(param3) << "\n";
}
```

## 🎯 **层级结构说明**

### **三级层级结构**

1. **0级（根级）**：控制通道
   - 格式：`试验节点,控制通道,,,`
   - 显示完整的类型和名称

2. **1级（一级缩进）**：CH1、CH2
   - 格式：`,CH1,,,`
   - 第一列为空，形成缩进效果

3. **2级（二级缩进）**：载荷1、载荷2、位置、控制
   - 格式：`,,载荷1,传感器_000001,`
   - 前两列为空，形成更深的缩进效果

### **输出格式详解**

| 节点类型 | 第1列 | 第2列 | 第3列 | 第4列 | 第5列 |
|---------|-------|-------|-------|-------|-------|
| **控制通道** | 试验节点 | 控制通道 | 空 | 空 | 空 |
| **CH1/CH2** | 空 | CH1/CH2 | 空 | 空 | 空 |
| **载荷1** | 空 | 空 | 载荷1 | 传感器_000001 | 空 |
| **载荷2** | 空 | 空 | 载荷2 | 传感器_000002 | 空 |
| **位置** | 空 | 空 | 位置 | 传感器_000003 | 空 |
| **控制** | 空 | 空 | 控制 | 作动器_000001 | 空 |

## 📊 **完整的CSV输出示例**

```
类型,名称,参数1,参数2,参数3
试验节点,实验,,,
试验节点,指令,,,
试验节点,DI,,,
试验节点,DO,,,
试验节点,控制通道,,,
,CH1,,,
,,载荷1,传感器_000001,
,,载荷2,传感器_000002,
,,位置,传感器_000003,
,,控制,作动器_000001,
,CH2,,,
,,载荷1,传感器_000001,
,,载荷2,传感器_000002,
,,位置,传感器_000003,
,,控制,作动器_000002,
```

## 🎯 **修改特点**

### **精确控制**
- ✅ **CH1/CH2**：第一列为空（一级缩进）
- ✅ **载荷/位置/控制**：前两列为空（二级缩进）
- ✅ **其他试验节点**：保持原格式
- ✅ **硬件配置节点**：完全不受影响

### **层级清晰**
- ✅ 通过空列形成视觉缩进
- ✅ 体现了控制通道 → CH1/CH2 → 载荷/位置/控制的层次关系
- ✅ 便于在Excel等工具中查看层级结构

### **数据完整**
- ✅ 关联信息正确显示在对应列
- ✅ 载荷1、载荷2、位置、控制的关联传感器/作动器信息完整保留
- ✅ 所有节点信息都正确导出

## 🚀 **测试方法**

### **1. 重新编译项目**
```bash
cd build-SiteResConfig_Simple-Desktop_Qt_5_14_2_MinGW_32_bit-Debug
make clean
make
```

### **2. 启动应用程序**
```bash
cd debug
./SiteResConfig.exe
```

### **3. 导出CSV并验证**
1. 在应用程序中点击"导出为CSV格式"
2. 用Excel或文本编辑器打开生成的CSV文件
3. 验证试验配置部分的层级格式

### **4. 预期结果**
- 控制通道：正常显示"试验节点"
- CH1、CH2：第一列为空，形成一级缩进
- 载荷1、载荷2、位置、控制：前两列为空，形成二级缩进
- 关联信息正确显示在第4列

## ✅ **修改完成状态**

**CSV层级格式修改已完全实现！**

现在：
- ✅ 形成了清晰的三级层级结构
- ✅ CH1、CH2节点使用一级缩进（第一列为空）
- ✅ 载荷1、载荷2、位置、控制节点使用二级缩进（前两列为空）
- ✅ 关联信息正确显示
- ✅ 其他节点格式保持不变

您现在可以测试CSV导出功能，应该能看到清晰的层级结构格式。
