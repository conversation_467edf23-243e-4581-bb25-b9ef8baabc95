/****************************************************************************
** Meta object code from reading C++ file 'MainWindow_Qt_Simple.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../001_SiteResConfig_OldStruct/SiteResConfig/include/MainWindow_Qt_Simple.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'MainWindow_Qt_Simple.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_CMyMainWindow_t {
    QByteArrayData data[60];
    char stringdata0[1272];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_CMyMainWindow_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_CMyMainWindow_t qt_meta_stringdata_CMyMainWindow = {
    {
QT_MOC_LITERAL(0, 0, 13), // "CMyMainWindow"
QT_MOC_LITERAL(1, 14, 12), // "OnNewProject"
QT_MOC_LITERAL(2, 27, 0), // ""
QT_MOC_LITERAL(3, 28, 13), // "OnOpenProject"
QT_MOC_LITERAL(4, 42, 13), // "OnSaveProject"
QT_MOC_LITERAL(5, 56, 15), // "OnSaveAsProject"
QT_MOC_LITERAL(6, 72, 6), // "OnExit"
QT_MOC_LITERAL(7, 79, 20), // "OnConfigureNodeLD_B1"
QT_MOC_LITERAL(8, 100, 20), // "OnConfigureNodeLD_B2"
QT_MOC_LITERAL(9, 121, 10), // "OnClearLog"
QT_MOC_LITERAL(10, 132, 9), // "OnSaveLog"
QT_MOC_LITERAL(11, 142, 7), // "OnAbout"
QT_MOC_LITERAL(12, 150, 15), // "OnRestoreColors"
QT_MOC_LITERAL(13, 166, 14), // "OnUpdateStatus"
QT_MOC_LITERAL(14, 181, 26), // "OnShowDetailInfoDlgChanged"
QT_MOC_LITERAL(15, 208, 7), // "checked"
QT_MOC_LITERAL(16, 216, 15), // "OnProjectOpened"
QT_MOC_LITERAL(17, 232, 11), // "projectPath"
QT_MOC_LITERAL(18, 244, 11), // "projectName"
QT_MOC_LITERAL(19, 256, 15), // "OnProjectClosed"
QT_MOC_LITERAL(20, 272, 14), // "OnProjectSaved"
QT_MOC_LITERAL(21, 287, 22), // "initializeJSONExporter"
QT_MOC_LITERAL(22, 310, 31), // "saveSensorDetailedParamsInGroup"
QT_MOC_LITERAL(23, 342, 7), // "groupId"
QT_MOC_LITERAL(24, 350, 20), // "UI::SensorParams_1_2"
QT_MOC_LITERAL(25, 371, 6), // "params"
QT_MOC_LITERAL(26, 378, 39), // "saveOrUpdateSensorDetailedPar..."
QT_MOC_LITERAL(27, 418, 30), // "getSensorDetailedParamsInGroup"
QT_MOC_LITERAL(28, 449, 12), // "serialNumber"
QT_MOC_LITERAL(29, 462, 5), // "bool&"
QT_MOC_LITERAL(30, 468, 9), // "isHasData"
QT_MOC_LITERAL(31, 478, 33), // "updateSensorDetailedParamsInG..."
QT_MOC_LITERAL(32, 512, 33), // "removeSensorDetailedParamsInG..."
QT_MOC_LITERAL(33, 546, 32), // "getAllSensorSerialNumbersInGroup"
QT_MOC_LITERAL(34, 579, 33), // "getAllSensorDetailedParamsInG..."
QT_MOC_LITERAL(35, 613, 27), // "QList<UI::SensorParams_1_2>"
QT_MOC_LITERAL(36, 641, 18), // "validateSensorData"
QT_MOC_LITERAL(37, 660, 32), // "validateSensorStorageConsistency"
QT_MOC_LITERAL(38, 693, 23), // "getSensorTypeStatistics"
QT_MOC_LITERAL(39, 717, 17), // "QMap<QString,int>"
QT_MOC_LITERAL(40, 735, 28), // "getAllActuatorGroups_MainDlg"
QT_MOC_LITERAL(41, 764, 28), // "QList<UI::ActuatorGroup_1_2>"
QT_MOC_LITERAL(42, 793, 18), // "getAllSensorGroups"
QT_MOC_LITERAL(43, 812, 26), // "QList<UI::SensorGroup_1_2>"
QT_MOC_LITERAL(44, 839, 31), // "buildControlChannelGroupsFromUI"
QT_MOC_LITERAL(45, 871, 30), // "QList<UI::ControlChannelGroup>"
QT_MOC_LITERAL(46, 902, 33), // "collectControlChannelGroupsFr..."
QT_MOC_LITERAL(47, 936, 30), // "buildHardwareNodeConfigsFromUI"
QT_MOC_LITERAL(48, 967, 27), // "QList<UI::NodeConfigParams>"
QT_MOC_LITERAL(49, 995, 30), // "onActuatorGroupCreatedBusiness"
QT_MOC_LITERAL(50, 1026, 9), // "groupName"
QT_MOC_LITERAL(51, 1036, 31), // "onActuatorDeviceCreatedBusiness"
QT_MOC_LITERAL(52, 1068, 30), // "onActuatorDeviceEditedBusiness"
QT_MOC_LITERAL(53, 1099, 31), // "onActuatorDeviceDeletedBusiness"
QT_MOC_LITERAL(54, 1131, 25), // "onActuatorValidationError"
QT_MOC_LITERAL(55, 1157, 5), // "error"
QT_MOC_LITERAL(56, 1163, 40), // "onControlChannelTreeItemSelec..."
QT_MOC_LITERAL(57, 1204, 28), // "updateChannelSettingsDisplay"
QT_MOC_LITERAL(58, 1233, 9), // "channelId"
QT_MOC_LITERAL(59, 1243, 28) // "onEditChannelConfigTriggered"

    },
    "CMyMainWindow\0OnNewProject\0\0OnOpenProject\0"
    "OnSaveProject\0OnSaveAsProject\0OnExit\0"
    "OnConfigureNodeLD_B1\0OnConfigureNodeLD_B2\0"
    "OnClearLog\0OnSaveLog\0OnAbout\0"
    "OnRestoreColors\0OnUpdateStatus\0"
    "OnShowDetailInfoDlgChanged\0checked\0"
    "OnProjectOpened\0projectPath\0projectName\0"
    "OnProjectClosed\0OnProjectSaved\0"
    "initializeJSONExporter\0"
    "saveSensorDetailedParamsInGroup\0groupId\0"
    "UI::SensorParams_1_2\0params\0"
    "saveOrUpdateSensorDetailedParamsInGroup\0"
    "getSensorDetailedParamsInGroup\0"
    "serialNumber\0bool&\0isHasData\0"
    "updateSensorDetailedParamsInGroup\0"
    "removeSensorDetailedParamsInGroup\0"
    "getAllSensorSerialNumbersInGroup\0"
    "getAllSensorDetailedParamsInGroup\0"
    "QList<UI::SensorParams_1_2>\0"
    "validateSensorData\0validateSensorStorageConsistency\0"
    "getSensorTypeStatistics\0QMap<QString,int>\0"
    "getAllActuatorGroups_MainDlg\0"
    "QList<UI::ActuatorGroup_1_2>\0"
    "getAllSensorGroups\0QList<UI::SensorGroup_1_2>\0"
    "buildControlChannelGroupsFromUI\0"
    "QList<UI::ControlChannelGroup>\0"
    "collectControlChannelGroupsFromUI\0"
    "buildHardwareNodeConfigsFromUI\0"
    "QList<UI::NodeConfigParams>\0"
    "onActuatorGroupCreatedBusiness\0groupName\0"
    "onActuatorDeviceCreatedBusiness\0"
    "onActuatorDeviceEditedBusiness\0"
    "onActuatorDeviceDeletedBusiness\0"
    "onActuatorValidationError\0error\0"
    "onControlChannelTreeItemSelectionChanged\0"
    "updateChannelSettingsDisplay\0channelId\0"
    "onEditChannelConfigTriggered"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_CMyMainWindow[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      40,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    0,  214,    2, 0x0a /* Public */,
       3,    0,  215,    2, 0x0a /* Public */,
       4,    0,  216,    2, 0x0a /* Public */,
       5,    0,  217,    2, 0x0a /* Public */,
       6,    0,  218,    2, 0x0a /* Public */,
       7,    0,  219,    2, 0x0a /* Public */,
       8,    0,  220,    2, 0x0a /* Public */,
       9,    0,  221,    2, 0x0a /* Public */,
      10,    0,  222,    2, 0x0a /* Public */,
      11,    0,  223,    2, 0x0a /* Public */,
      12,    0,  224,    2, 0x0a /* Public */,
      13,    0,  225,    2, 0x0a /* Public */,
      14,    1,  226,    2, 0x0a /* Public */,
      16,    2,  229,    2, 0x0a /* Public */,
      19,    0,  234,    2, 0x0a /* Public */,
      20,    1,  235,    2, 0x0a /* Public */,
      21,    0,  238,    2, 0x0a /* Public */,
      22,    2,  239,    2, 0x0a /* Public */,
      26,    2,  244,    2, 0x0a /* Public */,
      27,    3,  249,    2, 0x0a /* Public */,
      31,    3,  256,    2, 0x0a /* Public */,
      32,    2,  263,    2, 0x0a /* Public */,
      33,    1,  268,    2, 0x0a /* Public */,
      34,    1,  271,    2, 0x0a /* Public */,
      36,    0,  274,    2, 0x0a /* Public */,
      37,    0,  275,    2, 0x0a /* Public */,
      38,    0,  276,    2, 0x0a /* Public */,
      40,    0,  277,    2, 0x0a /* Public */,
      42,    0,  278,    2, 0x0a /* Public */,
      44,    0,  279,    2, 0x0a /* Public */,
      46,    0,  280,    2, 0x0a /* Public */,
      47,    0,  281,    2, 0x0a /* Public */,
      49,    2,  282,    2, 0x0a /* Public */,
      51,    2,  287,    2, 0x0a /* Public */,
      52,    2,  292,    2, 0x0a /* Public */,
      53,    1,  297,    2, 0x0a /* Public */,
      54,    1,  300,    2, 0x0a /* Public */,
      56,    0,  303,    2, 0x0a /* Public */,
      57,    2,  304,    2, 0x0a /* Public */,
      59,    0,  309,    2, 0x0a /* Public */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Bool,   15,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,   17,   18,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,   17,
    QMetaType::Void,
    QMetaType::Bool, QMetaType::Int, 0x80000000 | 24,   23,   25,
    QMetaType::Bool, QMetaType::Int, 0x80000000 | 24,   23,   25,
    0x80000000 | 24, QMetaType::Int, QMetaType::QString, 0x80000000 | 29,   23,   28,   30,
    QMetaType::Bool, QMetaType::Int, QMetaType::QString, 0x80000000 | 24,   23,   28,   25,
    QMetaType::Bool, QMetaType::Int, QMetaType::QString,   23,   28,
    QMetaType::QStringList, QMetaType::Int,   23,
    0x80000000 | 35, QMetaType::Int,   23,
    QMetaType::Bool,
    QMetaType::Bool,
    0x80000000 | 39,
    0x80000000 | 41,
    0x80000000 | 43,
    0x80000000 | 45,
    0x80000000 | 45,
    0x80000000 | 48,
    QMetaType::Void, QMetaType::QString, QMetaType::Int,   50,   23,
    QMetaType::Void, QMetaType::QString, QMetaType::Int,   28,   23,
    QMetaType::Void, QMetaType::QString, QMetaType::Int,   28,   23,
    QMetaType::Void, QMetaType::QString,   28,
    QMetaType::Void, QMetaType::QString,   55,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString, QMetaType::Int,   58,   23,
    QMetaType::Void,

       0        // eod
};

void CMyMainWindow::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<CMyMainWindow *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->OnNewProject(); break;
        case 1: _t->OnOpenProject(); break;
        case 2: _t->OnSaveProject(); break;
        case 3: _t->OnSaveAsProject(); break;
        case 4: _t->OnExit(); break;
        case 5: _t->OnConfigureNodeLD_B1(); break;
        case 6: _t->OnConfigureNodeLD_B2(); break;
        case 7: _t->OnClearLog(); break;
        case 8: _t->OnSaveLog(); break;
        case 9: _t->OnAbout(); break;
        case 10: _t->OnRestoreColors(); break;
        case 11: _t->OnUpdateStatus(); break;
        case 12: _t->OnShowDetailInfoDlgChanged((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 13: _t->OnProjectOpened((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 14: _t->OnProjectClosed(); break;
        case 15: _t->OnProjectSaved((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 16: _t->initializeJSONExporter(); break;
        case 17: { bool _r = _t->saveSensorDetailedParamsInGroup((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< const UI::SensorParams_1_2(*)>(_a[2])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 18: { bool _r = _t->saveOrUpdateSensorDetailedParamsInGroup((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< const UI::SensorParams_1_2(*)>(_a[2])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 19: { UI::SensorParams_1_2 _r = _t->getSensorDetailedParamsInGroup((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2])),(*reinterpret_cast< bool(*)>(_a[3])));
            if (_a[0]) *reinterpret_cast< UI::SensorParams_1_2*>(_a[0]) = std::move(_r); }  break;
        case 20: { bool _r = _t->updateSensorDetailedParamsInGroup((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2])),(*reinterpret_cast< const UI::SensorParams_1_2(*)>(_a[3])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 21: { bool _r = _t->removeSensorDetailedParamsInGroup((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 22: { QStringList _r = _t->getAllSensorSerialNumbersInGroup((*reinterpret_cast< int(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< QStringList*>(_a[0]) = std::move(_r); }  break;
        case 23: { QList<UI::SensorParams_1_2> _r = _t->getAllSensorDetailedParamsInGroup((*reinterpret_cast< int(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< QList<UI::SensorParams_1_2>*>(_a[0]) = std::move(_r); }  break;
        case 24: { bool _r = _t->validateSensorData();
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 25: { bool _r = _t->validateSensorStorageConsistency();
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 26: { QMap<QString,int> _r = _t->getSensorTypeStatistics();
            if (_a[0]) *reinterpret_cast< QMap<QString,int>*>(_a[0]) = std::move(_r); }  break;
        case 27: { QList<UI::ActuatorGroup_1_2> _r = _t->getAllActuatorGroups_MainDlg();
            if (_a[0]) *reinterpret_cast< QList<UI::ActuatorGroup_1_2>*>(_a[0]) = std::move(_r); }  break;
        case 28: { QList<UI::SensorGroup_1_2> _r = _t->getAllSensorGroups();
            if (_a[0]) *reinterpret_cast< QList<UI::SensorGroup_1_2>*>(_a[0]) = std::move(_r); }  break;
        case 29: { QList<UI::ControlChannelGroup> _r = _t->buildControlChannelGroupsFromUI();
            if (_a[0]) *reinterpret_cast< QList<UI::ControlChannelGroup>*>(_a[0]) = std::move(_r); }  break;
        case 30: { QList<UI::ControlChannelGroup> _r = _t->collectControlChannelGroupsFromUI();
            if (_a[0]) *reinterpret_cast< QList<UI::ControlChannelGroup>*>(_a[0]) = std::move(_r); }  break;
        case 31: { QList<UI::NodeConfigParams> _r = _t->buildHardwareNodeConfigsFromUI();
            if (_a[0]) *reinterpret_cast< QList<UI::NodeConfigParams>*>(_a[0]) = std::move(_r); }  break;
        case 32: _t->onActuatorGroupCreatedBusiness((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 33: _t->onActuatorDeviceCreatedBusiness((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 34: _t->onActuatorDeviceEditedBusiness((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 35: _t->onActuatorDeviceDeletedBusiness((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 36: _t->onActuatorValidationError((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 37: _t->onControlChannelTreeItemSelectionChanged(); break;
        case 38: _t->updateChannelSettingsDisplay((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 39: _t->onEditChannelConfigTriggered(); break;
        default: ;
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject CMyMainWindow::staticMetaObject = { {
    QMetaObject::SuperData::link<QMainWindow::staticMetaObject>(),
    qt_meta_stringdata_CMyMainWindow.data,
    qt_meta_data_CMyMainWindow,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *CMyMainWindow::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *CMyMainWindow::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CMyMainWindow.stringdata0))
        return static_cast<void*>(this);
    return QMainWindow::qt_metacast(_clname);
}

int CMyMainWindow::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QMainWindow::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 40)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 40;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 40)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 40;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
