/********************************************************************************
** Form generated from reading UI file 'HardwareConfigDialog.ui'
**
** Created by: Qt User Interface Compiler version 5.14.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_HARDWARECONFIGDIALOG_H
#define UI_HARDWARECONFIGDIALOG_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QDialog>
#include <QtWidgets/QDoubleSpinBox>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QVBoxLayout>

QT_BEGIN_NAMESPACE

class Ui_HardwareConfigDialog
{
public:
    QVBoxLayout *verticalLayout;
    QGroupBox *nodeGroupBox;
    QGridLayout *nodeGridLayout;
    QLabel *nodeCountLabel;
    QSpinBox *nodeCountSpinBox;
    QLabel *channelCountLabel;
    QSpinBox *channelCountSpinBox;
    QGroupBox *actuatorGroupBox;
    QGridLayout *actuatorGridLayout;
    QLabel *actuatorCountLabel;
    QSpinBox *actuatorCountSpinBox;
    QLabel *maxForceLabel;
    QDoubleSpinBox *maxForceSpinBox;
    QGroupBox *sensorGroupBox;
    QGridLayout *sensorGridLayout;
    QLabel *sensorCountLabel;
    QSpinBox *sensorCountSpinBox;
    QSpacerItem *verticalSpacer;
    QHBoxLayout *buttonLayout;
    QSpacerItem *horizontalSpacer;
    QPushButton *okButton;
    QPushButton *cancelButton;

    void setupUi(QDialog *HardwareConfigDialog)
    {
        if (HardwareConfigDialog->objectName().isEmpty())
            HardwareConfigDialog->setObjectName(QString::fromUtf8("HardwareConfigDialog"));
        HardwareConfigDialog->resize(500, 400);
        HardwareConfigDialog->setModal(true);
        verticalLayout = new QVBoxLayout(HardwareConfigDialog);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        nodeGroupBox = new QGroupBox(HardwareConfigDialog);
        nodeGroupBox->setObjectName(QString::fromUtf8("nodeGroupBox"));
        nodeGridLayout = new QGridLayout(nodeGroupBox);
        nodeGridLayout->setObjectName(QString::fromUtf8("nodeGridLayout"));
        nodeCountLabel = new QLabel(nodeGroupBox);
        nodeCountLabel->setObjectName(QString::fromUtf8("nodeCountLabel"));

        nodeGridLayout->addWidget(nodeCountLabel, 0, 0, 1, 1);

        nodeCountSpinBox = new QSpinBox(nodeGroupBox);
        nodeCountSpinBox->setObjectName(QString::fromUtf8("nodeCountSpinBox"));
        nodeCountSpinBox->setMinimum(1);
        nodeCountSpinBox->setMaximum(10);
        nodeCountSpinBox->setValue(2);

        nodeGridLayout->addWidget(nodeCountSpinBox, 0, 1, 1, 1);

        channelCountLabel = new QLabel(nodeGroupBox);
        channelCountLabel->setObjectName(QString::fromUtf8("channelCountLabel"));

        nodeGridLayout->addWidget(channelCountLabel, 1, 0, 1, 1);

        channelCountSpinBox = new QSpinBox(nodeGroupBox);
        channelCountSpinBox->setObjectName(QString::fromUtf8("channelCountSpinBox"));
        channelCountSpinBox->setMinimum(1);
        channelCountSpinBox->setMaximum(16);
        channelCountSpinBox->setValue(4);

        nodeGridLayout->addWidget(channelCountSpinBox, 1, 1, 1, 1);


        verticalLayout->addWidget(nodeGroupBox);

        actuatorGroupBox = new QGroupBox(HardwareConfigDialog);
        actuatorGroupBox->setObjectName(QString::fromUtf8("actuatorGroupBox"));
        actuatorGridLayout = new QGridLayout(actuatorGroupBox);
        actuatorGridLayout->setObjectName(QString::fromUtf8("actuatorGridLayout"));
        actuatorCountLabel = new QLabel(actuatorGroupBox);
        actuatorCountLabel->setObjectName(QString::fromUtf8("actuatorCountLabel"));

        actuatorGridLayout->addWidget(actuatorCountLabel, 0, 0, 1, 1);

        actuatorCountSpinBox = new QSpinBox(actuatorGroupBox);
        actuatorCountSpinBox->setObjectName(QString::fromUtf8("actuatorCountSpinBox"));
        actuatorCountSpinBox->setMinimum(1);
        actuatorCountSpinBox->setMaximum(20);
        actuatorCountSpinBox->setValue(4);

        actuatorGridLayout->addWidget(actuatorCountSpinBox, 0, 1, 1, 1);

        maxForceLabel = new QLabel(actuatorGroupBox);
        maxForceLabel->setObjectName(QString::fromUtf8("maxForceLabel"));

        actuatorGridLayout->addWidget(maxForceLabel, 1, 0, 1, 1);

        maxForceSpinBox = new QDoubleSpinBox(actuatorGroupBox);
        maxForceSpinBox->setObjectName(QString::fromUtf8("maxForceSpinBox"));
        maxForceSpinBox->setDecimals(1);
        maxForceSpinBox->setMinimum(1.000000000000000);
        maxForceSpinBox->setMaximum(1000.000000000000000);
        maxForceSpinBox->setValue(100.000000000000000);

        actuatorGridLayout->addWidget(maxForceSpinBox, 1, 1, 1, 1);


        verticalLayout->addWidget(actuatorGroupBox);

        sensorGroupBox = new QGroupBox(HardwareConfigDialog);
        sensorGroupBox->setObjectName(QString::fromUtf8("sensorGroupBox"));
        sensorGridLayout = new QGridLayout(sensorGroupBox);
        sensorGridLayout->setObjectName(QString::fromUtf8("sensorGridLayout"));
        sensorCountLabel = new QLabel(sensorGroupBox);
        sensorCountLabel->setObjectName(QString::fromUtf8("sensorCountLabel"));

        sensorGridLayout->addWidget(sensorCountLabel, 0, 0, 1, 1);

        sensorCountSpinBox = new QSpinBox(sensorGroupBox);
        sensorCountSpinBox->setObjectName(QString::fromUtf8("sensorCountSpinBox"));
        sensorCountSpinBox->setMinimum(1);
        sensorCountSpinBox->setMaximum(50);
        sensorCountSpinBox->setValue(8);

        sensorGridLayout->addWidget(sensorCountSpinBox, 0, 1, 1, 1);


        verticalLayout->addWidget(sensorGroupBox);

        verticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout->addItem(verticalSpacer);

        buttonLayout = new QHBoxLayout();
        buttonLayout->setObjectName(QString::fromUtf8("buttonLayout"));
        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        buttonLayout->addItem(horizontalSpacer);

        okButton = new QPushButton(HardwareConfigDialog);
        okButton->setObjectName(QString::fromUtf8("okButton"));

        buttonLayout->addWidget(okButton);

        cancelButton = new QPushButton(HardwareConfigDialog);
        cancelButton->setObjectName(QString::fromUtf8("cancelButton"));

        buttonLayout->addWidget(cancelButton);


        verticalLayout->addLayout(buttonLayout);


        retranslateUi(HardwareConfigDialog);
        QObject::connect(cancelButton, SIGNAL(clicked()), HardwareConfigDialog, SLOT(reject()));

        okButton->setDefault(true);


        QMetaObject::connectSlotsByName(HardwareConfigDialog);
    } // setupUi

    void retranslateUi(QDialog *HardwareConfigDialog)
    {
        HardwareConfigDialog->setWindowTitle(QCoreApplication::translate("HardwareConfigDialog", "\346\211\213\345\212\250\351\205\215\347\275\256\347\241\254\344\273\266", nullptr));
        nodeGroupBox->setTitle(QCoreApplication::translate("HardwareConfigDialog", "\347\241\254\344\273\266\350\212\202\347\202\271\351\205\215\347\275\256", nullptr));
        nodeCountLabel->setText(QCoreApplication::translate("HardwareConfigDialog", "\350\212\202\347\202\271\346\225\260\351\207\217:", nullptr));
        channelCountLabel->setText(QCoreApplication::translate("HardwareConfigDialog", "\346\257\217\350\212\202\347\202\271\351\200\232\351\201\223\346\225\260:", nullptr));
        actuatorGroupBox->setTitle(QCoreApplication::translate("HardwareConfigDialog", "\344\275\234\345\212\250\345\231\250\351\205\215\347\275\256", nullptr));
        actuatorCountLabel->setText(QCoreApplication::translate("HardwareConfigDialog", "\344\275\234\345\212\250\345\231\250\346\225\260\351\207\217:", nullptr));
        maxForceLabel->setText(QCoreApplication::translate("HardwareConfigDialog", "\346\234\200\345\244\247\345\212\233\345\200\274 (kN):", nullptr));
        sensorGroupBox->setTitle(QCoreApplication::translate("HardwareConfigDialog", "\344\274\240\346\204\237\345\231\250\351\205\215\347\275\256", nullptr));
        sensorCountLabel->setText(QCoreApplication::translate("HardwareConfigDialog", "\344\274\240\346\204\237\345\231\250\346\225\260\351\207\217:", nullptr));
        okButton->setText(QCoreApplication::translate("HardwareConfigDialog", "\347\241\256\345\256\232", nullptr));
        cancelButton->setText(QCoreApplication::translate("HardwareConfigDialog", "\345\217\226\346\266\210", nullptr));
    } // retranslateUi

};

namespace Ui {
    class HardwareConfigDialog: public Ui_HardwareConfigDialog {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_HARDWARECONFIGDIALOG_H
