#include "DetailInfoPanel.h"
#include "DataModels_Fixed.h"  // 🆕 新增：用于UI::ControlChannelParams
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGroupBox>
#include <QLabel>
#include <QTableWidget>
#include <QTreeWidget>
#include <QTreeWidgetItem>
#include <QPushButton>
#include <QHeaderView>
#include <QTimer>
#include <QApplication>
#include <QFont>
#include <QStyle>
#include <QMessageBox>
#include <QResizeEvent>
#include <QDebug> // 🆕 新增：用于调试输出
// qWarning is defined in QDebug header

// 常量定义
const QStringList BASIC_INFO_HEADERS = {
    "通道名称", "硬件关联选择", "载荷1传感器选择", "载荷2传感器选择",
    "位置传感器选择", "控制作动器选择", "下位机ID", "站点ID", 
    "使能状态", "控制作动器极性", "载荷1传感器极性", "载荷2传感器极性", "位置传感器极性"
};

const QStringList SUB_NODE_ROW_HEADERS = {
    "子节点类型", "关联设备", "关联状态", "设备编号", "配置详情"
};

DetailInfoPanel::DetailInfoPanel(QWidget *parent)
    : QWidget(parent)
    , m_basicInfoWidget(nullptr)
{
    initUI();
    setupConnections();
    applyStyles();
}

DetailInfoPanel::~DetailInfoPanel()
{
}

void DetailInfoPanel::setNodeInfo(const NodeInfo& nodeInfo)
{
    qDebug() << "🔍 [DetailInfoPanel] 开始设置节点信息";
    
    try {
        // 🆕 新增：验证NodeInfo对象的有效性
        if (nodeInfo.nodeName.isEmpty() && nodeInfo.nodeType.isEmpty()) {
            qDebug() << "ℹ️ [DetailInfoPanel] 节点信息为空，跳过设置";
            return;
        }
        
        qDebug() << "   📋 节点名称:" << nodeInfo.nodeName;
        qDebug() << "   🏷️  节点类型:" << nodeInfo.nodeType;
        qDebug() << "   📊 节点状态:" << (nodeInfo.status == NodeStatus::Online ? "在线" : 
                                          nodeInfo.status == NodeStatus::Offline ? "离线" : 
                                          nodeInfo.status == NodeStatus::Error ? "错误" : "未知");
        qDebug() << "   🔗 子节点数量:" << nodeInfo.subNodes.size();
        
        // 打印子节点详细信息
        try {
            for (int i = 0; i < nodeInfo.subNodes.size(); ++i) {
                const SubNodeInfo& subNode = nodeInfo.subNodes[i];
                qDebug() << "   📍 子节点" << (i+1) << ":" << subNode.name << "(" << subNode.type << ")";
            }
        } catch (const std::exception& e) {
            qWarning() << "❌ [DetailInfoPanel] 打印子节点信息时发生异常:" << e.what();
        } catch (...) {
            qWarning() << "❌ [DetailInfoPanel] 打印子节点信息时发生未知异常";
        }
        
        // 🆕 新增：安全的成员变量赋值
        try {
            m_currentNodeInfo = nodeInfo;
            qDebug() << "✅ [DetailInfoPanel] 节点信息已保存到成员变量";
        } catch (const std::exception& e) {
            qWarning() << "❌ [DetailInfoPanel] 保存节点信息到成员变量时发生异常:" << e.what();
            return;
        } catch (...) {
            qWarning() << "❌ [DetailInfoPanel] 保存节点信息到成员变量时发生未知异常";
            return;
        }
        
        // 🆕 修改：使用BasicInfoWidget更新基本信息
        if (m_basicInfoWidget) {
            qDebug() << "✅ [DetailInfoPanel] 找到BasicInfoWidget，调用setNodeInfo";
            try {
                m_basicInfoWidget->setNodeInfo(nodeInfo);
                qDebug() << "✅ [DetailInfoPanel] BasicInfoWidget->setNodeInfo调用完成";
            } catch (const std::exception& e) {
                qWarning() << "❌ [DetailInfoPanel] BasicInfoWidget->setNodeInfo调用异常:" << e.what();
            } catch (...) {
                qWarning() << "❌ [DetailInfoPanel] BasicInfoWidget->setNodeInfo调用未知异常";
            }
        } else {
            qWarning() << "❌ [DetailInfoPanel] 错误：BasicInfoWidget为空！";
        }
        
        qDebug() << "🔄 [DetailInfoPanel] 准备调用updateAllTables";
        try {
            updateAllTables(nodeInfo);
            qDebug() << "✅ [DetailInfoPanel] updateAllTables调用完成";
        } catch (const std::exception& e) {
            qWarning() << "❌ [DetailInfoPanel] updateAllTables调用异常:" << e.what();
        } catch (...) {
            qWarning() << "❌ [DetailInfoPanel] updateAllTables调用未知异常";
        }
        
        qDebug() << "🎉 [DetailInfoPanel] 节点信息设置完成";
        
    } catch (const std::exception& e) {
        qWarning() << "❌ [DetailInfoPanel] 设置节点信息时发生异常:" << e.what();
    } catch (...) {
        qWarning() << "❌ [DetailInfoPanel] 设置节点信息时发生未知异常";
    }
}

void DetailInfoPanel::clearInfo()
{
    qDebug() << "🧹 [DetailInfoPanel] 开始清空节点信息";
    
    m_currentNodeInfo = NodeInfo();
    
    // 🆕 修改：使用BasicInfoWidget清空基本信息
    if (m_basicInfoWidget) {
        qDebug() << "✅ [DetailInfoPanel] 找到BasicInfoWidget，调用clearInfo";
        m_basicInfoWidget->clearInfo();
        qDebug() << "✅ [DetailInfoPanel] BasicInfoWidget->clearInfo调用完成";
    } else {
        qDebug() << "❌ [DetailInfoPanel] 错误：BasicInfoWidget为空！";
    }
    
    // 清空其他表格（如果有的话）
    // 这里可以添加其他表格的清空逻辑
    
    qDebug() << "🎉 [DetailInfoPanel] 节点信息清空完成";
}

void DetailInfoPanel::updateStatus(NodeStatus status)
{
    qDebug() << "🔄 [DetailInfoPanel] 开始更新节点状态";
    qDebug() << "   📊 当前状态:" << (m_currentNodeInfo.status == NodeStatus::Online ? "在线" : 
                                      m_currentNodeInfo.status == NodeStatus::Offline ? "离线" : 
                                      m_currentNodeInfo.status == NodeStatus::Error ? "错误" : "未知");
    qDebug() << "   📊 新状态:" << (status == NodeStatus::Online ? "在线" : 
                                    status == NodeStatus::Offline ? "离线" : 
                                    status == NodeStatus::Error ? "错误" : "未知");
    
    if (m_currentNodeInfo.status == status) {
        qDebug() << "ℹ️  [DetailInfoPanel] 状态未变化，无需更新";
        return;
    }
    
    m_currentNodeInfo.status = status;
    qDebug() << "✅ [DetailInfoPanel] 节点状态已更新";
    
    // 🆕 修改：直接更新BasicInfoWidget的节点信息
    if (m_basicInfoWidget) {
        qDebug() << "🔄 [DetailInfoPanel] 调用BasicInfoWidget->setNodeInfo更新状态";
        m_basicInfoWidget->setNodeInfo(m_currentNodeInfo);
        qDebug() << "✅ [DetailInfoPanel] BasicInfoWidget状态更新完成";
    } else {
        qDebug() << "❌ [DetailInfoPanel] 错误：BasicInfoWidget为空！";
    }
    
    qDebug() << "🎉 [DetailInfoPanel] 节点状态更新完成";
}

void DetailInfoPanel::initUI()
{
    qDebug() << "🔧 [DetailInfoPanel] 开始初始化UI界面";
    
    // 主布局
    QVBoxLayout* mainLayout = new QVBoxLayout(this);
    mainLayout->setSpacing(15);
    mainLayout->setContentsMargins(20, 20, 20, 20);
    
    // 🆕 修改：创建BasicInfoWidget
    m_basicInfoWidget = new BasicInfoWidget(this);
    mainLayout->addWidget(m_basicInfoWidget);
    
    // 🆕 新增：创建其他界面组件
    createOtherSections(mainLayout);
    
    // 设置最小尺寸
    setMinimumSize(600, 400);
}

void DetailInfoPanel::setupConnections()
{
    qDebug() << "🔗 [DetailInfoPanel] 开始设置信号连接";
    
    // 当前没有需要连接的信号
    // 所有按钮和交互功能已被移除
    
    qDebug() << "✅ [DetailInfoPanel] 信号连接设置完成";
}

void DetailInfoPanel::applyStyles()
{
    qDebug() << "🎨 [DetailInfoPanel] 开始应用样式表";
    
    // 设置整体样式
    setStyleSheet(R"(
        QWidget {
            background-color: #ffffff;
            color: #2c3e50;
            font-family: "Microsoft YaHei UI", "Segoe UI", sans-serif;
        }
        
        QGroupBox {
            font-weight: bold;
            font-size: 11pt;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            margin-top: 10px;
            padding-top: 10px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 8px 0 8px;
            background-color: #34495e;
            color: #ffffff;
            border-radius: 4px;
        }
        
        QTableWidget {
            background-color: #ffffff;
            alternate-background-color: #f8f9fa;
            border: 1px solid #e1e8ed;
            border-radius: 4px;
            gridline-color: #e1e8ed;
            selection-background-color: #3498db;
            selection-color: #ffffff;
        }
        
        QTableWidget::item {
            padding: 8px;
            border: none;
        }
        
        QTableWidget::item:selected {
            background-color: #3498db;
            color: #ffffff;
        }
        
        QHeaderView::section {
            background-color: #34495e;
            color: #ffffff;
            padding: 8px;
            border: none;
            font-weight: bold;
            font-size: 9pt;
        }
        
        QHeaderView::section:horizontal {
            border-right: 1px solid #2c3e50;
        }
        
        QHeaderView::section:vertical {
            border-bottom: 1px solid #2c3e50;
        }
        
        QPushButton {
            background-color: #3498db;
            color: #ffffff;
            border: none;
            border-radius: 6px;
            padding: 10px 20px;
            font-size: 9pt;
            font-weight: bold;
            min-width: 100px;
        }
        
        QPushButton:hover {
            background-color: #2980b9;
        }
        
        QPushButton:pressed {
            background-color: #21618c;
        }
        
        QPushButton:disabled {
            background-color: #bdc3c7;
            color: #7f8c8d;
        }
    )");
    
    qDebug() << "✅ [DetailInfoPanel] 样式表应用完成";
}



// 🆕 新增：创建其他界面组件的方法
void DetailInfoPanel::createOtherSections(QVBoxLayout* mainLayout)
{
    // 这里可以添加其他界面组件，比如子节点信息、配置详情等
    // 目前暂时为空，可以根据需要扩展
    
//    // 示例：可以添加一个占位符标签
//    QLabel* placeholderLabel = new QLabel("其他配置区域（待扩展）", this);
//    placeholderLabel->setStyleSheet("color: #7f8c8d; font-style: italic; padding: 20px; text-align: center;");
//    placeholderLabel->setAlignment(Qt::AlignCenter);
//    mainLayout->addWidget(placeholderLabel);
}





QTableWidget* DetailInfoPanel::createBasicInfoTable()
{
    // 🆕 修改：基本信息表格已由BasicInfoWidget处理，此方法可以移除或保留为空
    // 如果需要创建基本信息表格，可以通过 m_basicInfoWidget->getBasicInfoTable() 获取
    return nullptr;
}





void DetailInfoPanel::updateAllTables(const NodeInfo& nodeInfo)
{
    qDebug() << "🔄 [DetailInfoPanel] 开始更新所有表格";
    
    try {
        // 🆕 新增：验证NodeInfo对象的有效性
        if (nodeInfo.nodeName.isEmpty() && nodeInfo.nodeType.isEmpty()) {
            qDebug() << "ℹ️ [DetailInfoPanel] 节点信息为空，跳过表格更新";
            return;
        }
        
        qDebug() << "   📋 节点名称:" << nodeInfo.nodeName;
        qDebug() << "   🏷️  节点类型:" << nodeInfo.nodeType;
        qDebug() << "   🔗 子节点数量:" << nodeInfo.subNodes.size();
        
        // 🆕 新增：安全的子节点数量检查
        try {
            int subNodeCount = nodeInfo.subNodes.size();
            qDebug() << "   🔍 子节点数量验证完成:" << subNodeCount;
        } catch (const std::exception& e) {
            qWarning() << "❌ [DetailInfoPanel] 获取子节点数量时发生异常:" << e.what();
        } catch (...) {
            qWarning() << "❌ [DetailInfoPanel] 获取子节点数量时发生未知异常";
        }
        
        // 🆕 修改：基本信息已由BasicInfoWidget处理，这里只需要处理其他表格
        // 目前没有其他表格需要更新
        
        qDebug() << "✅ [DetailInfoPanel] 所有表格更新完成";
        
    } catch (const std::exception& e) {
        qWarning() << "❌ [DetailInfoPanel] 更新所有表格时发生异常:" << e.what();
    } catch (...) {
        qWarning() << "❌ [DetailInfoPanel] 更新所有表格时发生未知异常";
    }
}

// 🆕 修改：标签控件更新已由BasicInfoWidget处理，此方法可以移除或保留为空
void DetailInfoPanel::updateLabels(const NodeInfo& nodeInfo)
{
    // 标签控件更新已由BasicInfoWidget处理
    // 这里可以添加其他需要更新的控件
}

void DetailInfoPanel::updateBasicInfoTable(const NodeInfo& nodeInfo)
{
    // 🆕 修改：基本信息表格已由BasicInfoWidget处理，此方法可以移除或保留为空
    // 如果需要访问基本信息表格，可以通过 m_basicInfoWidget->getBasicInfoTable() 获取
}



void DetailInfoPanel::updateUIStatus()
{
    // 根据当前状态更新UI
}

void DetailInfoPanel::updateStatusIndicator(NodeStatus status)
{
    // 🆕 修改：状态指示器已由BasicInfoWidget处理，此方法可以移除或保留为空
    // 如果需要更新状态指示器，可以通过 m_basicInfoWidget->updateStatus(status) 调用
}

QString DetailInfoPanel::getStatusText(NodeStatus status)
{
    switch (status) {
        case NodeStatus::Online: return "在线";
        case NodeStatus::Offline: return "离线";
        case NodeStatus::Warning: return "警告";
        case NodeStatus::Error: return "错误";
        case NodeStatus::Maintenance: return "维护";
        default: return "未知";
    }
}

QString DetailInfoPanel::getStatusStateText(NodeStatus status)
{
    switch (status) {
        case NodeStatus::Online: return "运行中";
        case NodeStatus::Offline: return "已停止";
        case NodeStatus::Warning: return "需注意";
        case NodeStatus::Error: return "异常";
        case NodeStatus::Maintenance: return "维护中";
        default: return "未知状态";
    }
}

QString DetailInfoPanel::getPolarityText(int polarity)
{
    switch (polarity) {
        case 1: return "正极性 (+)";
        case -1: return "负极性 (-)";
        case 9: return "双极性 (±)";
        case 0: return "无极性";
        default: return QString("未知极性 (%1)").arg(polarity);
    }
}

void DetailInfoPanel::resizeEvent(QResizeEvent* event)
{
    QWidget::resizeEvent(event);
    // 🆕 修改：基本信息表格列宽调整已由BasicInfoWidget处理
    // 这里可以添加其他需要响应窗口大小变化的逻辑
}

void DetailInfoPanel::adjustTableColumns()
{
    // 🆕 修改：基本信息表格列宽调整已由BasicInfoWidget处理，此方法可以移除或保留为空
    // 如果需要调整表格列宽，可以通过 m_basicInfoWidget->getBasicInfoTable() 获取表格后调整
}

// 🆕 新增：创建控制通道节点信息的静态方法
NodeInfo DetailInfoPanel::createControlChannelNodeInfo(const QString& channelName, 
                                                      const QString& channelId,
                                                      const UI::ControlChannelParams& channelParams)
{
    qDebug() << "🔧 [DetailInfoPanel] 开始创建控制通道节点信息";
    qDebug() << "   📋 通道名称:" << channelName;
    qDebug() << "   🆔 通道ID:" << channelId;
    qDebug() << "   🔧 硬件关联:" << QString::fromStdString(channelParams.hardwareAssociation);
    
    NodeInfo nodeInfo;
    
    // 设置基本信息
    nodeInfo.nodeName = channelName;
    nodeInfo.nodeType = "控制通道";
    nodeInfo.status = NodeStatus::Online; // 默认在线状态
    nodeInfo.nodeId = channelId;
    nodeInfo.createTime = QDateTime::currentDateTime().addDays(-30); // 模拟创建时间
    nodeInfo.updateTime = QDateTime::currentDateTime();
    
    qDebug() << "✅ [DetailInfoPanel] 基本信息设置完成";
    
    // 设置基本信息属性
    nodeInfo.setBasicProperty("通道名称", channelName);
    nodeInfo.setBasicProperty("硬件关联选择", QString::fromStdString(channelParams.hardwareAssociation));
    nodeInfo.setBasicProperty("载荷1传感器选择", QString::fromStdString(channelParams.load1Sensor));
    nodeInfo.setBasicProperty("载荷2传感器选择", QString::fromStdString(channelParams.load2Sensor));
    nodeInfo.setBasicProperty("位置传感器选择", QString::fromStdString(channelParams.positionSensor));
    nodeInfo.setBasicProperty("控制作动器选择", QString::fromStdString(channelParams.controlActuator));
    nodeInfo.setBasicProperty("下位机ID", QString::number(channelParams.lc_id));
    nodeInfo.setBasicProperty("站点ID", QString::number(channelParams.station_id));
    nodeInfo.setBasicProperty("使能状态", channelParams.enable);
    
    // 设置各个极性字段
    nodeInfo.setBasicProperty("控制作动器极性", channelParams.servo_control_polarity);
    nodeInfo.setBasicProperty("载荷1传感器极性", channelParams.payload_sensor1_polarity);
    nodeInfo.setBasicProperty("载荷2传感器极性", channelParams.payload_sensor2_polarity);
    nodeInfo.setBasicProperty("位置传感器极性", channelParams.position_sensor_polarity);
    
    // 添加子节点信息
    qDebug() << "🔄 [DetailInfoPanel] 开始创建子节点信息...";
    
    // 子节点1: 载荷1传感器
    if (!channelParams.load1Sensor.empty()) {
        qDebug() << "   📍 创建载荷1传感器子节点";
        SubNodeInfo load1Node;
        load1Node.name = "载荷1传感器";
        load1Node.type = "载荷传感器";
        load1Node.deviceName = QString::fromStdString(channelParams.load1Sensor);
        load1Node.deviceId = QString("LS%1_1").arg(channelParams.lc_id);
        load1Node.isConnected = true;
        load1Node.setProperty("量程", "0-1000N");
        load1Node.setProperty("精度", "±0.1%");
        load1Node.setProperty("极性", channelParams.payload_sensor1_polarity == 1 ? "正极性" : 
                             channelParams.payload_sensor1_polarity == -1 ? "负极性" : "未知");
        load1Node.setProperty("测量单位", "N");
        load1Node.setProperty("关联状态", "已关联");
        nodeInfo.addSubNode(load1Node);
        qDebug() << "      ✅ 载荷1传感器子节点创建完成:" << load1Node.deviceName;
    } else {
        qDebug() << "      ⚠️  载荷1传感器参数为空，跳过创建";
    }
    
    // 子节点2: 载荷2传感器
    if (!channelParams.load2Sensor.empty()) {
        qDebug() << "   📍 创建载荷2传感器子节点";
        SubNodeInfo load2Node;
        load2Node.name = "载荷2传感器";
        load2Node.type = "载荷传感器";
        load2Node.deviceName = QString::fromStdString(channelParams.load2Sensor);
        load2Node.deviceId = QString("LS%1_2").arg(channelParams.lc_id);
        load2Node.isConnected = true;
        load2Node.setProperty("量程", "0-1000N");
        load2Node.setProperty("精度", "±0.1%");
        load2Node.setProperty("极性", channelParams.payload_sensor2_polarity == 1 ? "正极性" : 
                             channelParams.payload_sensor2_polarity == -1 ? "负极性" : "未知");
        load2Node.setProperty("测量单位", "N");
        load2Node.setProperty("关联状态", "已关联");
        nodeInfo.addSubNode(load2Node);
        qDebug() << "      ✅ 载荷2传感器子节点创建完成:" << load2Node.deviceName;
    } else {
        qDebug() << "      ⚠️  载荷2传感器参数为空，跳过创建";
    }
    
    // 子节点3: 位置传感器
    if (!channelParams.positionSensor.empty()) {
        qDebug() << "   📍 创建位置传感器子节点";
        SubNodeInfo posNode;
        posNode.name = "位置传感器";
        posNode.type = "位置传感器";
        posNode.deviceName = QString::fromStdString(channelParams.positionSensor);
        posNode.deviceId = QString("PS%1_%2").arg(channelParams.lc_id).arg(channelParams.station_id);
        posNode.isConnected = true;
        posNode.setProperty("量程", "±100mm");
        posNode.setProperty("精度", "±0.01mm");
        posNode.setProperty("极性", channelParams.position_sensor_polarity == 1 ? "正极性" : 
                             channelParams.position_sensor_polarity == -1 ? "负极性" : "未知");
        posNode.setProperty("测量单位", "mm");
        posNode.setProperty("关联状态", "已关联");
        nodeInfo.addSubNode(posNode);
        qDebug() << "      ✅ 位置传感器子节点创建完成:" << posNode.deviceName;
    } else {
        qDebug() << "      ⚠️  位置传感器参数为空，跳过创建";
    }
    
    // 子节点4: 控制作动器
    if (!channelParams.controlActuator.empty()) {
        qDebug() << "   📍 创建控制作动器子节点";
        SubNodeInfo actuatorNode;
        actuatorNode.name = "控制作动器";
        actuatorNode.type = "伺服作动器";
        actuatorNode.deviceName = QString::fromStdString(channelParams.controlActuator);
        actuatorNode.deviceId = QString("SA%1_%2").arg(channelParams.lc_id).arg(channelParams.station_id);
        actuatorNode.isConnected = true;
        actuatorNode.setProperty("控制模式", channelParams.control_mode == 4 ? "力控制" : 
                                channelParams.control_mode == 5 ? "位移控制" : "未知");
        actuatorNode.setProperty("极性", channelParams.servo_control_polarity == 1 ? "正极性" : 
                                channelParams.servo_control_polarity == -1 ? "负极性" : 
                                channelParams.servo_control_polarity == 9 ? "双极性" : "未知");
        actuatorNode.setProperty("最大行程", "±100mm");
        actuatorNode.setProperty("最大力", "1000N");
        actuatorNode.setProperty("关联状态", "已关联");
        nodeInfo.addSubNode(actuatorNode);
        qDebug() << "      ✅ 控制作动器子节点创建完成:" << actuatorNode.deviceName;
    } else {
        qDebug() << "      ⚠️  控制作动器参数为空，跳过创建";
    }
    
    // 子节点5: 硬件关联节点
    if (!channelParams.hardwareAssociation.empty()) {
        qDebug() << "   📍 创建硬件关联节点子节点";
        SubNodeInfo hwNode;
        hwNode.name = "硬件关联节点";
        hwNode.type = "硬件节点";
        hwNode.deviceName = QString::fromStdString(channelParams.hardwareAssociation);
        hwNode.deviceId = QString("HW%1_%2").arg(channelParams.lc_id).arg(channelParams.station_id);
        hwNode.isConnected = true;
        hwNode.setProperty("节点类型", "加载节点");
        hwNode.setProperty("通道数量", "2");
        hwNode.setProperty("功能说明", "提供载荷加载和位移控制功能");
        hwNode.setProperty("关联状态", "已关联");
        nodeInfo.addSubNode(hwNode);
        qDebug() << "      ✅ 硬件关联节点子节点创建完成:" << hwNode.deviceName;
    } else {
        qDebug() << "      ⚠️  硬件关联节点参数为空，跳过创建";
    }
    
    qDebug() << "🎉 [DetailInfoPanel] 控制通道节点信息创建完成";
    qDebug() << "   📊 总子节点数量:" << nodeInfo.subNodes.size();
    
    return nodeInfo;
}

// 🆕 新增：创建测试用的控制通道节点信息
NodeInfo DetailInfoPanel::createTestControlChannelNodeInfo()
{
    // 创建测试用的控制通道参数
    UI::ControlChannelParams testParams;
    testParams.channelId = "CH1";
    testParams.channelName = "CH1";
    testParams.hardwareAssociation = "LD-B1 - CH1";
    testParams.load1Sensor = "LS-001";
    testParams.load2Sensor = "LS-002";
    testParams.positionSensor = "PS-001";
    testParams.controlActuator = "SA-001";
    testParams.notes = "测试通道1";
    testParams.lc_id = 1;
    testParams.station_id = 1;
    testParams.enable = true;
    testParams.control_mode = 4;
    testParams.servo_control_polarity = 1;
    testParams.payload_sensor1_polarity = 1;
    testParams.payload_sensor2_polarity = -1;
    testParams.position_sensor_polarity = 1;
    
    // 使用现有的创建方法
    return createControlChannelNodeInfo("测试通道1", "CH1", testParams);
}

void DetailInfoPanel::setTestData()
{
    // 设置测试用的控制通道节点信息
    NodeInfo testNodeInfo = createTestControlChannelNodeInfo();
    setNodeInfo(testNodeInfo);
}

// 🆕 新增：设置控制通道详细信息
void DetailInfoPanel::setControlChannelInfo(const QString& channelName, const QList<SubNodeInfo>& subNodes)
{
    // 清空当前信息
    clearInfo();
    
    // 🆕 修改：标签控件已由BasicInfoWidget处理
    // 这里可以添加其他需要设置的逻辑
    
    // 设置状态为在线（假设控制通道默认在线）
    updateStatus(NodeStatus::Online);
    
    // 🆕 修改：基本信息表格已由BasicInfoWidget处理
    // 这里可以添加其他需要设置的逻辑
    
    qDebug() << "DetailInfoPanel: 已设置控制通道信息:" << channelName << "子节点数量:" << subNodes.size();
}

// 🆕 新增：设置表格选中行
void DetailInfoPanel::setSelectedRow(int row)
{
    if (!m_basicInfoWidget) {
        qDebug() << "⚠️ DetailInfoPanel::setSelectedRow: BasicInfoWidget未初始化";
        return;
    }
    
    qDebug() << "🎯 DetailInfoPanel::setSelectedRow: 设置选中行:" << row;
    
    // 通知BasicInfoWidget设置选中行
    m_basicInfoWidget->setSelectedRow(row);
}

// 🚫 已移除：HTML格式详细信息功能

// 🆕 新增：更新汇总信息方法


// 🆕 新增：设置控制通道根节点信息
void DetailInfoPanel::setControlChannelRootInfo(const QString& rootName, 
                                               const QList<QTreeWidgetItem*>& childChannels)
{
    // 清空当前信息
    clearInfo();
    
    // 🆕 修改：标签控件已由BasicInfoWidget处理
    // 这里可以添加其他需要设置的逻辑
    
    // 设置状态为在线
    updateStatus(NodeStatus::Online);
    
    qDebug() << "DetailInfoPanel: 已设置控制通道根节点信息:" << rootName << "子通道数量:" << childChannels.size();
}

// 🆕 新增：添加控制通道行
void DetailInfoPanel::addControlChannelRow(int row, const SubNodeInfo& channel)
{
    if (!m_basicInfoWidget) return;
    
    // 使用BasicInfoWidget的addControlChannelRow方法
    m_basicInfoWidget->addControlChannelRow(row, channel);
}

// 🆕 新增：添加控制子节点行
void DetailInfoPanel::addControlSubNodeRow(int row, const QString& channelName, const SubNodeInfo& subNode)
{
    if (!m_basicInfoWidget) return;
    
    // 使用BasicInfoWidget的addControlSubNodeRow方法
    m_basicInfoWidget->addControlSubNodeRow(row, channelName, subNode);
}









// ==================== 静态辅助方法实现 ====================












