<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>SensorDialog_1_2</class>
 <widget class="QDialog" name="SensorDialog_1_2">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>522</width>
    <height>568</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>传感器配置 1_2 - 新需求</string>
  </property>
  <property name="modal">
   <bool>true</bool>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QLabel" name="infoLabel">
     <property name="font">
      <font>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="text">
      <string>传感器组\传感器_000001</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="basicGroupBox">
     <property name="title">
      <string>基础配置 (2字段)</string>
     </property>
     <layout class="QFormLayout" name="basicFormLayout">
      <item row="0" column="0">
       <widget class="QLabel" name="zeroOffsetLabel">
        <property name="text">
         <string>零点偏移:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QDoubleSpinBox" name="zeroOffsetSpinBox">
        <property name="decimals">
         <number>6</number>
        </property>
        <property name="minimum">
         <double>-999999.000000000000000</double>
        </property>
        <property name="maximum">
         <double>999999.000000000000000</double>
        </property>
        <property name="value">
         <double>0.000000000000000</double>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="enableLabel">
        <property name="text">
         <string>使能状态:</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QCheckBox" name="enableCheckBox">
        <property name="text">
         <string>启用传感器</string>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="paramsGroupBox">
     <property name="title">
      <string>传感器参数配置 (12字段)</string>
     </property>
     <layout class="QFormLayout" name="paramsFormLayout">
      <item row="0" column="0">
       <widget class="QLabel" name="paramsModelLabel">
        <property name="text">
         <string>型号:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QLineEdit" name="paramsModelEdit">
        <property name="placeholderText">
         <string>请输入传感器型号 (如: AKD-8A)</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="paramsSnLabel">
        <property name="text">
         <string>序列号:</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QLineEdit" name="paramsSnEdit">
        <property name="placeholderText">
         <string>请输入序列号 (如: 2223)</string>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="paramsKLabel">
        <property name="text">
         <string>系数K:</string>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="QDoubleSpinBox" name="paramsKSpinBox">
        <property name="decimals">
         <number>6</number>
        </property>
        <property name="minimum">
         <double>-999999.000000000000000</double>
        </property>
        <property name="maximum">
         <double>999999.000000000000000</double>
        </property>
        <property name="value">
         <double>20.000000000000000</double>
        </property>
       </widget>
      </item>
      <item row="3" column="0">
       <widget class="QLabel" name="paramsBLabel">
        <property name="text">
         <string>系数B:</string>
        </property>
       </widget>
      </item>
      <item row="3" column="1">
       <widget class="QDoubleSpinBox" name="paramsBSpinBox">
        <property name="decimals">
         <number>6</number>
        </property>
        <property name="minimum">
         <double>-999999.000000000000000</double>
        </property>
        <property name="maximum">
         <double>999999.000000000000000</double>
        </property>
        <property name="value">
         <double>0.000000000000000</double>
        </property>
       </widget>
      </item>
      <item row="4" column="0">
       <widget class="QLabel" name="paramsPrecisionLabel">
        <property name="text">
         <string>精度:</string>
        </property>
       </widget>
      </item>
      <item row="4" column="1">
       <widget class="QDoubleSpinBox" name="paramsPrecisionSpinBox">
        <property name="decimals">
         <number>6</number>
        </property>
        <property name="minimum">
         <double>0.000001000000000</double>
        </property>
        <property name="maximum">
         <double>999999.000000000000000</double>
        </property>
        <property name="value">
         <double>0.100000000000000</double>
        </property>
       </widget>
      </item>
      <item row="5" column="0">
       <widget class="QLabel" name="paramsPolarityLabel">
        <property name="text">
         <string>极性:</string>
        </property>
       </widget>
      </item>
      <item row="5" column="1">
       <widget class="QComboBox" name="paramsPolarityCombo">
       </widget>
      </item>
      <item row="6" column="0">
       <widget class="QLabel" name="paramsMeasUnitLabel">
        <property name="text">
         <string>测量单位:</string>
        </property>
       </widget>
      </item>
      <item row="6" column="1">
       <widget class="QSpinBox" name="paramsMeasUnitSpinBox">
        <property name="minimum">
         <number>0</number>
        </property>
        <property name="maximum">
         <number>999</number>
        </property>
        <property name="value">
         <number>1</number>
        </property>
       </widget>
      </item>
      <item row="7" column="0">
       <widget class="QLabel" name="paramsMeasRangeMinLabel">
        <property name="text">
         <string>测量范围最小值:</string>
        </property>
       </widget>
      </item>
      <item row="7" column="1">
       <widget class="QDoubleSpinBox" name="paramsMeasRangeMinSpinBox">
        <property name="decimals">
         <number>3</number>
        </property>
        <property name="minimum">
         <double>-999999.000000000000000</double>
        </property>
        <property name="maximum">
         <double>999999.000000000000000</double>
        </property>
        <property name="value">
         <double>-100.000000000000000</double>
        </property>
       </widget>
      </item>
      <item row="8" column="0">
       <widget class="QLabel" name="paramsMeasRangeMaxLabel">
        <property name="text">
         <string>测量范围最大值:</string>
        </property>
       </widget>
      </item>
      <item row="8" column="1">
       <widget class="QDoubleSpinBox" name="paramsMeasRangeMaxSpinBox">
        <property name="decimals">
         <number>3</number>
        </property>
        <property name="minimum">
         <double>-999999.000000000000000</double>
        </property>
        <property name="maximum">
         <double>999999.000000000000000</double>
        </property>
        <property name="value">
         <double>100.000000000000000</double>
        </property>
       </widget>
      </item>
      <item row="9" column="0">
       <widget class="QLabel" name="paramsOutputSignalUnitLabel">
        <property name="text">
         <string>输出信号单位:</string>
        </property>
       </widget>
      </item>
      <item row="9" column="1">
       <widget class="QSpinBox" name="paramsOutputSignalUnitSpinBox">
        <property name="minimum">
         <number>0</number>
        </property>
        <property name="maximum">
         <number>999</number>
        </property>
        <property name="value">
         <number>1</number>
        </property>
       </widget>
      </item>
      <item row="10" column="0">
       <widget class="QLabel" name="paramsOutputSignalRangeMinLabel">
        <property name="text">
         <string>输出信号范围最小值:</string>
        </property>
       </widget>
      </item>
      <item row="10" column="1">
       <widget class="QDoubleSpinBox" name="paramsOutputSignalRangeMinSpinBox">
        <property name="decimals">
         <number>3</number>
        </property>
        <property name="minimum">
         <double>-999999.000000000000000</double>
        </property>
        <property name="maximum">
         <double>999999.000000000000000</double>
        </property>
        <property name="value">
         <double>-100.000000000000000</double>
        </property>
       </widget>
      </item>
      <item row="11" column="0">
       <widget class="QLabel" name="paramsOutputSignalRangeMaxLabel">
        <property name="text">
         <string>输出信号范围最大值:</string>
        </property>
       </widget>
      </item>
      <item row="11" column="1">
       <widget class="QDoubleSpinBox" name="paramsOutputSignalRangeMaxSpinBox">
        <property name="decimals">
         <number>3</number>
        </property>
        <property name="minimum">
         <double>-999999.000000000000000</double>
        </property>
        <property name="maximum">
         <double>999999.000000000000000</double>
        </property>
        <property name="value">
         <double>100.000000000000000</double>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="buttonLayout">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="okButton">
       <property name="text">
        <string>确定</string>
       </property>
       <property name="default">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="cancelButton">
       <property name="text">
        <string>取消</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>okButton</sender>
   <signal>clicked()</signal>
   <receiver>SensorDialog_1_2</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>278</x>
     <y>382</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>cancelButton</sender>
   <signal>clicked()</signal>
   <receiver>SensorDialog_1_2</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>369</x>
     <y>382</y>
    </hint>
    <hint type="destinationlabel">
     <x>286</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
