@echo off
chcp 65001 > nul
echo ========================================
echo 控制通道界面显示修复测试
echo ========================================
echo.

echo 🔧 正在编译修复后的代码...
cd SiteResConfig
qmake SiteResConfig_Simple.pro
mingw32-make clean
mingw32-make

if %ERRORLEVEL% NEQ 0 (
    echo ❌ 编译失败！
    pause
    exit /b 1
)

echo ✅ 编译成功！
echo.

echo 🚀 启动应用程序进行测试...
echo.
echo 📋 测试步骤：
echo.
echo 🎯 **修改内容总结**：
echo - 修改RefreshTestConfigTreeFromDataManagers方法
echo - 改为动态创建控制通道节点，而不是固定创建CH1和CH2
echo - 从数据管理器获取实际的控制通道数据并显示
echo - 修复Excel导入时的列位置错误（8列vs9列格式兼容）
echo.
echo 🧪 **测试用例1：Excel导入控制通道显示**
echo 1. 导入包含控制通道数据的Excel文件
echo 2. 检查"实验配置"树中的"控制通道"节点
echo 3. 验证是否显示了Excel中定义的实际通道名称（而不是固定的CH1、CH2）
echo 4. 验证每个通道的关联信息是否正确显示
echo.
echo 🧪 **测试用例2：控制通道关联信息验证**
echo 1. 展开控制通道节点
echo 2. 检查每个通道下的子节点：载荷1、载荷2、位置、控制
echo 3. 验证关联信息是否正确显示在第二列
echo 4. 验证工具提示是否正确显示关联详情
echo.
echo 🧪 **测试用例3：新建工程默认行为**
echo 1. 创建新工程（不导入Excel）
echo 2. 验证是否仍然显示默认的CH1和CH2结构
echo 3. 确保新建工程的行为没有被破坏
echo.
echo 🔍 **预期修复效果**：
echo ✅ Excel导入后控制通道能够正确显示
echo ✅ 显示Excel中定义的实际通道名称
echo ✅ 正确显示每个通道的关联信息
echo ✅ 载荷1、载荷2、位置、控制子节点正确显示关联设备
echo ✅ 新建工程仍然显示默认的CH1、CH2结构
echo.
echo 🔧 **修复的核心问题**：
echo - 原来固定创建CH1和CH2，无法显示Excel导入的实际通道
echo - 现在动态从数据管理器获取通道数据并创建对应的界面节点
echo - 修复了Excel导入时的列位置错误（导出8列，导入期望9列）
echo - 保持了向后兼容性，新建工程仍显示默认结构
echo.
echo 📝 **修复的文件**：
echo - MainWindow_Qt_Simple.cpp - RefreshTestConfigTreeFromDataManagers方法
echo - XLSDataExporter.cpp - importControlChannelDetails方法（列位置修复）
echo.
echo 🔍 **Excel格式兼容性**：
echo - 自动检测8列简化格式（完整项目导出）
echo - 自动检测9列详细格式（单独控制通道导出）
echo - 根据列数自动选择正确的读取方式
echo.

start "" "debug\SiteResConfig.exe"

echo 📊 应用程序已启动，请按照上述测试用例进行验证
echo.
echo ⚠️ **测试重点**：
echo 1. 导入Excel文件后，检查控制通道是否显示
echo 2. 验证通道名称是否与Excel中的一致
echo 3. 验证关联信息是否正确显示
echo.
echo 如果问题仍然存在，请查看控制台输出的调试信息
pause
