# 📊 作动器XLSX存储格式设计方案

## 🎯 设计概述

基于新的ActuatorParams结构体和双面板界面设计，为作动器数据在XLSX文件中的存储制定详细的格式规范。

## 📋 工作表结构设计

### 主工作表：作动器配置表 (Actuator Configuration)

#### 表头信息区域 (A1:H5)
```
A1: 作动器配置数据表
B1: [空]
C1: 导出时间: 2025-08-14 15:30:00
D1: [空]
E1: 版本: v1.0
F1: [空]
G1: 项目: SiteResConfig
H1: [空]

A2: [空行]

A3: 说明: 本表包含完整的作动器参数配置信息
B3: [空]
C3: 包括基本信息、截面数据、伺服控制器参数
D3: [空]
E3: 物理参数由截面数据自动计算
F3: [空]
G3: 支持多种作动器类型
H3: [空]

A4: [空行]
A5: [空行]
```

#### 数据表头 (A6:R6)
```
A6: 序号          | B6: 作动器分类    | C6: 序列号        | D6: 类型
E6: 单位长度      | F6: 行程(m)       | G6: 位移(m)       | H6: 拉伸面积(m²)
I6: 压缩面积(m²)  | J6: 极性          | K6: Deliver(V)    | L6: 频率(Hz)
M6: 输出倍数      | N6: 平衡(V)       | O6: 缸径(m)       | P6: 杆径(m)
Q6: 创建时间      | R6: 备注
```

#### 数据行格式 (A7开始)
```
A7: 1             | B7: 液压作动器    | C7: ACT_001       | D7: 单出杆
E7: m             | F7: 0.30          | G7: 0.30          | H7: 0.60
I7: 0.50          | J7: Positive      | K7: 0.100         | L7: 100.00
M7: 1.000         | N7: 0.000         | O7: 0.874         | P7: 0.357
Q7: 2025-08-14    | R7: 液压系统主作动器

A8: 2             | B8: 电动作动器    | C8: ACT_002       | D8: 双出杆
E8: m             | F8: 0.20          | G8: 0.20          | H8: 0.30
I8: 0.30          | J8: Positive      | K8: 0.050         | L8: 1000.00
M8: 1.000         | N8: 0.000         | O8: 0.618         | P8: 0.000
Q8: 2025-08-14    | R8: 精密定位作动器
```

## 📊 详细字段说明

### 基本信息字段
| 列 | 字段名 | 数据类型 | 说明 | 示例值 |
|---|--------|----------|------|--------|
| A | 序号 | Integer | 自动递增序号 | 1, 2, 3... |
| B | 作动器分类 | String | 液压/电动/气动/伺服作动器 | "液压作动器" |
| C | 序列号 | String | 作动器唯一标识 | "ACT_001" |
| D | 类型 | String | 单出杆/双出杆等 | "单出杆" |
| Q | 创建时间 | Date | 创建日期 | "2025-08-14" |
| R | 备注 | String | 附加说明信息 | "液压系统主作动器" |

### 截面数据字段 (Actuator基本参数)
| 列 | 字段名 | 数据类型 | 单位 | 说明 | 示例值 |
|---|--------|----------|------|------|--------|
| E | 单位长度 | String | - | 测量单位 | "m" |
| F | 行程 | Double | m | 作动器行程长度 | 0.30 |
| G | 位移 | Double | m | 位移量 | 0.30 |
| H | 拉伸面积 | Double | m² | 拉伸有效面积 | 0.60 |
| I | 压缩面积 | Double | m² | 压缩有效面积 | 0.50 |

### 伺服控制器参数字段 (Value)
| 列 | 字段名 | 数据类型 | 单位 | 说明 | 示例值 |
|---|--------|----------|------|------|--------|
| J | 极性 | String | - | Positive/Negative | "Positive" |
| K | Deliver | Double | V | 输出电压 | 0.100 |
| L | 频率 | Double | Hz | 控制频率 | 100.00 |
| M | 输出倍数 | Double | - | 输出倍数 | 1.000 |
| N | 平衡 | Double | V | 平衡电压 | 0.000 |

### 计算参数字段
| 列 | 字段名 | 数据类型 | 单位 | 说明 | 计算公式 |
|---|--------|----------|------|------|----------|
| O | 缸径 | Double | m | 从拉伸面积计算 | D = √(4A/π) |
| P | 杆径 | Double | m | 从面积差计算 | d = √(4(A₁-A₂)/π) |

## 🎨 格式样式设计

### 表头样式
- **背景色**: #4472C4 (深蓝色)
- **字体色**: 白色
- **字体**: 微软雅黑, 11pt, 粗体
- **对齐**: 居中对齐
- **边框**: 全边框, 白色, 1pt

### 数据行样式
- **奇数行背景**: #F2F2F2 (浅灰色)
- **偶数行背景**: 白色
- **字体**: 微软雅黑, 10pt, 常规
- **对齐**: 数值右对齐, 文本左对齐
- **边框**: 全边框, 灰色, 0.5pt

### 列宽设置
```
A列(序号): 8
B列(作动器分类): 15
C列(序列号): 12
D列(类型): 10
E列(单位长度): 10
F列(行程): 10
G列(位移): 10
H列(拉伸面积): 12
I列(压缩面积): 12
J列(极性): 10
K列(Deliver): 12
L列(频率): 10
M列(输出倍数): 12
N列(平衡): 10
O列(缸径): 10
P列(杆径): 10
Q列(创建时间): 12
R列(备注): 20
```

## 📋 辅助工作表设计

### 工作表2：作动器类型说明 (Actuator Types)

#### 表头 (A1:D1)
```
A1: 作动器分类 | B1: 特点描述 | C1: 适用场景 | D1: 默认参数
```

#### 数据内容
```
A2: 液压作动器 | B2: 输出力大，功率密度高，响应速度快 | C2: 重载应用，大功率输出 | D2: 行程0.3m，频率100Hz
A3: 电动作动器 | B3: 精度高，控制简单，维护成本低 | C3: 精密定位，自动化设备 | D3: 行程0.2m，频率1000Hz
A4: 气动作动器 | B4: 结构简单，成本低，动作迅速 | C4: 快速动作，安全要求高 | D4: 行程0.25m，频率50Hz
A5: 伺服作动器 | B5: 高精度位置控制，闭环反馈 | C5: 精密控制，高动态响应 | D5: 行程0.15m，频率528Hz
```

### 工作表3：计算公式说明 (Calculation Formulas)

#### 公式说明表
```
A1: 计算项目 | B1: 公式 | C1: 说明 | D1: 示例

A2: 缸径计算 | B2: D = √(4A/π) | C2: A为拉伸面积(m²) | D2: A=0.6m² → D=0.874m
A3: 杆径计算 | B3: d = √(4(A₁-A₂)/π) | C3: A₁拉伸面积，A₂压缩面积 | D3: A₁=0.6，A₂=0.5 → d=0.357m
A4: 面积关系 | B4: A₂ = A₁ - A_rod | C4: 压缩面积=拉伸面积-杆截面积 | D4: 用于验证数据合理性
```

## 🔄 数据验证规则

### 必填字段验证
- 序列号: 不能为空，必须唯一
- 作动器分类: 必须为预定义类型之一
- 行程: 必须大于0
- 拉伸面积: 必须大于0
- 压缩面积: 必须大于0且小于拉伸面积

### 数值范围验证
```
行程: 0.01 - 10.00 m
位移: 0.01 - 10.00 m
拉伸面积: 0.01 - 100.00 m²
压缩面积: 0.01 - 100.00 m²
Deliver: -10.000 - 10.000 V
频率: 1.00 - 10000.00 Hz
输出倍数: 0.001 - 1000.000
平衡: -10.000 - 10.000 V
```

### 逻辑关系验证
- 压缩面积 < 拉伸面积
- 杆径 < 缸径
- 位移 ≤ 行程

## 📤 导出示例

### 完整的XLSX文件结构
```
SiteResConfig_Actuators_20250814_153000.xlsx
├── 作动器配置表 (主数据)
├── 作动器类型说明 (参考信息)
└── 计算公式说明 (技术文档)
```

### 示例数据行
```
1 | 液压作动器 | ACT_HYD_001 | 单出杆 | m | 0.30 | 0.30 | 0.60 | 0.50 | Positive | 0.100 | 100.00 | 1.000 | 0.000 | 0.874 | 0.357 | 2025-08-14 | 主液压缸
2 | 电动作动器 | ACT_ELE_001 | 双出杆 | m | 0.20 | 0.20 | 0.30 | 0.30 | Positive | 0.050 | 1000.00 | 1.000 | 0.000 | 0.618 | 0.000 | 2025-08-14 | 精密定位器
3 | 气动作动器 | ACT_PNE_001 | 单出杆 | m | 0.25 | 0.25 | 0.40 | 0.35 | Positive | 0.200 | 50.00 | 1.000 | 0.000 | 0.714 | 0.252 | 2025-08-14 | 快速夹具
4 | 伺服作动器 | ACT_SRV_001 | 单出杆 | m | 0.15 | 0.15 | 0.20 | 0.18 | Positive | 0.000 | 528.00 | 1.000 | 0.000 | 0.505 | 0.159 | 2025-08-14 | 伺服控制器
```

## 🎯 设计优势

### 数据完整性
- ✅ 包含所有ActuatorParams字段
- ✅ 支持自动计算的物理参数
- ✅ 完整的元数据信息

### 可读性
- ✅ 清晰的表头和分组
- ✅ 专业的格式样式
- ✅ 详细的说明文档

### 可扩展性
- ✅ 支持新增作动器类型
- ✅ 支持新增参数字段
- ✅ 支持多工作表结构

### 兼容性
- ✅ 标准XLSX格式
- ✅ Excel/WPS完全兼容
- ✅ 支持数据验证和公式

这个存储格式设计完全基于新的ActuatorParams结构体，支持双面板界面的所有参数，并提供了专业的XLSX文件格式规范。
