/****************************************************************************
** Meta object code from reading C++ file 'CtrlChanDataManager.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../001_SiteResConfig_OldStruct/SiteResConfig/include/CtrlChanDataManager.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'CtrlChanDataManager.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_CtrlChanDataManager_t {
    QByteArrayData data[10];
    char stringdata0[184];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_CtrlChanDataManager_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_CtrlChanDataManager_t qt_meta_stringdata_CtrlChanDataManager = {
    {
QT_MOC_LITERAL(0, 0, 19), // "CtrlChanDataManager"
QT_MOC_LITERAL(1, 20, 26), // "controlChannelGroupCreated"
QT_MOC_LITERAL(2, 47, 0), // ""
QT_MOC_LITERAL(3, 48, 7), // "groupId"
QT_MOC_LITERAL(4, 56, 26), // "controlChannelGroupUpdated"
QT_MOC_LITERAL(5, 83, 26), // "controlChannelGroupDeleted"
QT_MOC_LITERAL(6, 110, 19), // "controlChannelAdded"
QT_MOC_LITERAL(7, 130, 9), // "channelId"
QT_MOC_LITERAL(8, 140, 21), // "controlChannelUpdated"
QT_MOC_LITERAL(9, 162, 21) // "controlChannelRemoved"

    },
    "CtrlChanDataManager\0controlChannelGroupCreated\0"
    "\0groupId\0controlChannelGroupUpdated\0"
    "controlChannelGroupDeleted\0"
    "controlChannelAdded\0channelId\0"
    "controlChannelUpdated\0controlChannelRemoved"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_CtrlChanDataManager[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       6,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       6,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   44,    2, 0x06 /* Public */,
       4,    1,   47,    2, 0x06 /* Public */,
       5,    1,   50,    2, 0x06 /* Public */,
       6,    2,   53,    2, 0x06 /* Public */,
       8,    2,   58,    2, 0x06 /* Public */,
       9,    2,   63,    2, 0x06 /* Public */,

 // signals: parameters
    QMetaType::Void, QMetaType::Int,    3,
    QMetaType::Void, QMetaType::Int,    3,
    QMetaType::Void, QMetaType::Int,    3,
    QMetaType::Void, QMetaType::Int, QMetaType::QString,    3,    7,
    QMetaType::Void, QMetaType::Int, QMetaType::QString,    3,    7,
    QMetaType::Void, QMetaType::Int, QMetaType::QString,    3,    7,

       0        // eod
};

void CtrlChanDataManager::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<CtrlChanDataManager *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->controlChannelGroupCreated((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 1: _t->controlChannelGroupUpdated((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 2: _t->controlChannelGroupDeleted((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 3: _t->controlChannelAdded((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 4: _t->controlChannelUpdated((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 5: _t->controlChannelRemoved((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (CtrlChanDataManager::*)(int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CtrlChanDataManager::controlChannelGroupCreated)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (CtrlChanDataManager::*)(int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CtrlChanDataManager::controlChannelGroupUpdated)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (CtrlChanDataManager::*)(int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CtrlChanDataManager::controlChannelGroupDeleted)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (CtrlChanDataManager::*)(int , const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CtrlChanDataManager::controlChannelAdded)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (CtrlChanDataManager::*)(int , const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CtrlChanDataManager::controlChannelUpdated)) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (CtrlChanDataManager::*)(int , const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&CtrlChanDataManager::controlChannelRemoved)) {
                *result = 5;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject CtrlChanDataManager::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_CtrlChanDataManager.data,
    qt_meta_data_CtrlChanDataManager,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *CtrlChanDataManager::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *CtrlChanDataManager::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CtrlChanDataManager.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int CtrlChanDataManager::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 6)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 6;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 6)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 6;
    }
    return _id;
}

// SIGNAL 0
void CtrlChanDataManager::controlChannelGroupCreated(int _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void CtrlChanDataManager::controlChannelGroupUpdated(int _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void CtrlChanDataManager::controlChannelGroupDeleted(int _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void CtrlChanDataManager::controlChannelAdded(int _t1, const QString & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void CtrlChanDataManager::controlChannelUpdated(int _t1, const QString & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}

// SIGNAL 5
void CtrlChanDataManager::controlChannelRemoved(int _t1, const QString & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 5, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
