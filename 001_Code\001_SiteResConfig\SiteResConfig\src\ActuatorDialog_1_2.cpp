/**
 * @file ActuatorDialog_1_2.cpp
 * @brief 作动器创建对话框类实现
 * @details 使用Qt Designer设计的作动器参数输入对话框实现
 * <AUTHOR> Assistant
 * @date 2025-08-06
 * @version 1.0.0
 */

#include "ActuatorDialog_1_2.h"
#include "ui_ActuatorDialog_1_2.h"
#include "ActuatorDataManager_1_2.h"  // 🆕 新增：数据管理器头文件
#include <QtWidgets/QMessageBox>
#include <QtCore/QRegularExpression>  // 🆕 新增：正则表达式库
#include <cmath>  // 🆕 新增：数学函数库，包含sqrt函数
#include <QDebug>

namespace UI {

ActuatorDialog_1_2::ActuatorDialog_1_2(const QString& groupName, const QString& autoNumber, QWidget* parent)
    : QDialog(parent)
    , ui(new Ui::ActuatorDialog_1_2)
    , groupName_(groupName)
    , autoNumber_(autoNumber)
    , editMode_(false)              // 🆕 初始化编辑模式为否
    , originalSerialNumber_("")     // 🆕 初始化原始序列号为空
    , currentGroupId_(-1)           // 🆕 初始化当前组ID为-1
    , dataManager_(nullptr) {       // 🆕 初始化数据管理器指针为空
    
    ui->setupUi(this);
    initializeUI();
    connectSignals();
}

ActuatorDialog_1_2::~ActuatorDialog_1_2() {
    delete ui;
}

void ActuatorDialog_1_2::initializeUI() {
    // 🔧 修复：使用新的UI控件
    // 设置标题显示组名和自动编号
    QString displayInfo = QString("%1\\%2").arg(groupName_).arg(autoNumber_);
    ui->titleLabel->setText(displayInfo);

    // 设置序列号默认值
    ui->serialEdit->setText(autoNumber_);

    // 设置控制量名称默认值
    ui->nameEdit->setText(QString("控制量_%1").arg(autoNumber_));

    // 🆕 新增：设置型号默认值
    ui->modelEdit->setText(QString("型号_%1").arg(autoNumber_));

    // 🆕 新增：设置占位符文本提示用户正确格式
    ui->nameEdit->setPlaceholderText(u8"例如: 控制量_作动器_000001");
    ui->serialEdit->setPlaceholderText(u8"例如: 作动器_000001");
    ui->modelEdit->setPlaceholderText(u8"例如: 液压作动器_50kN");

    // 设置窗口大小（适应新的标签页界面）
    resize(900, 650);
}

void ActuatorDialog_1_2::connectSignals() {
    // 🔧 修复：使用新的UI控件
    // 重新连接确定按钮，添加验证
    disconnect(ui->okButton, &QPushButton::clicked, this, &QDialog::accept);
    connect(ui->okButton, &QPushButton::clicked, this, &ActuatorDialog_1_2::onAcceptClicked);

    // 🚫 注释掉不存在的控件连接
    // connect(ui->autoBalanceButton, &QPushButton::clicked, this, &ActuatorDialog_1_2::onAutoBalanceClicked);
    // connect(ui->previewButton, &QPushButton::clicked, this, &ActuatorDialog_1_2::onPreviewClicked);
    // connect(ui->helpButton, &QPushButton::clicked, this, &ActuatorDialog_1_2::onHelpClicked);
    // connect(ui->infoButton, &QPushButton::clicked, this, &ActuatorDialog_1_2::onInfoClicked);

    // 连接作动器类型选择变化
    connect(ui->typeCombo, QOverload<const QString&>::of(&QComboBox::currentTextChanged),
            this, &ActuatorDialog_1_2::onTypeChanged);
}

void ActuatorDialog_1_2::onAutoBalanceClicked() {
    // 🚫 注释掉：自动平衡功能已移除（因为移除了兼容性字段）
    // ui->balanceSpinBox->setValue(0.000);
}

void ActuatorDialog_1_2::onTypeChanged(const QString& type) {
    // 🆕 新增：作动器类型变化处理
    // 可以根据类型调整其他参数的默认值
    Q_UNUSED(type);
}

void ActuatorDialog_1_2::onAcceptClicked() {
    // 🆕 新增：确定按钮点击处理（带验证）
    
    // 先进行输入验证
    if (!validateInput()) {
        return; // 验证失败，返回
    }

    // 验证通过后，显示数据预览让用户确认
    QString controlName = ui->nameEdit->text().trimmed();
    QString serialNumber = ui->serialEdit->text().trimmed();
    QString model = ui->modelEdit->text().trimmed();
    
    QString previewText = QString(
        u8"请确认作动器参数：\n\n"
        u8"📝 基本信息：\n"
        u8"  控制量名称: %1\n"
        u8"  序列号: %2\n"
        u8"  型号: %3\n"
        u8"  作动器类型: %4\n\n"
        u8"💡 提示：界面将显示序列号 '%2'\n\n"
        u8"确认无误后点击'是'保存，点击'否'返回修改"
    ).arg(controlName).arg(serialNumber).arg(model).arg(ui->typeCombo->currentText());

    int result = QMessageBox::question(this, u8"确认作动器参数", previewText,
                                     QMessageBox::Yes | QMessageBox::No, QMessageBox::Yes);
    if (result != QMessageBox::Yes) {
        return; // 用户选择返回修改
    }

    // 验证通过，接受对话框
    accept();
}

//void ActuatorDialog_1_2::onPreviewClicked() {
//    // 预览配置功能
//    ActuatorParams_1_2 params = getActuatorParams();

//    QString previewText = QString(
//        u8"作动器配置预览:\n\n"
//        u8"📝 基本信息:\n"
//        u8"  序列号: %1\n"
//        u8"  类型: %2\n"
//        u8"  分类: %3\n\n"
//        u8"🔧 Actuator参数:\n"
//        u8"  单位长度: %4\n"
//        u8"  行程: %5 m\n"
//        u8"  位移: %6 m\n"
//        u8"  拉伸面积: %7 m²\n"
//        u8"  压缩面积: %8 m²\n\n"
//        u8"⚡ 伺服控制器参数:\n"
//        u8"  极性: %9\n"
//        u8"  Deliver: %10 V\n"
//        u8"  频率: %11 Hz\n"
//        u8"  输出倍数: %12\n"
//        u8"  平衡: %13 V\n\n"
//        u8"📊 计算参数:\n"
//        u8"  缸径: %14 m\n"
//        u8"  杆径: %15 m"
//    ).arg(params.serialNumber)
//     .arg(params.type)
//     .arg(ui->categoryCombo->currentText())
//     .arg(params.unitLength)
//     .arg(params.stroke, 0, 'f', 2)
//     .arg(params.displacement, 0, 'f', 2)
//     .arg(params.tensionArea, 0, 'f', 2)
//     .arg(params.compressionArea, 0, 'f', 2)
//     .arg(params.polarity)
//     .arg(params.dither, 0, 'f', 3)
//     .arg(params.frequency, 0, 'f', 2)
//     .arg(params.outputMultiplier, 0, 'f', 3)
//     .arg(params.balance, 0, 'f', 3)
//     .arg(params.cylinderDiameter, 0, 'f', 3)
//     .arg(params.rodDiameter, 0, 'f', 3);

//    QMessageBox::information(this, u8"配置预览", previewText);
//}

//void ActuatorDialog_1_2::onHelpClicked() {
//    QString helpText = QString(
//        u8"新建作动器帮助说明\n\n"
//        u8"🎯 界面说明:\n"
//        u8"• 左侧面板: Actuator基本参数设置\n"
//        u8"• 右侧面板: 伺服控制器参数设置\n\n"
//        u8"📊 参数说明:\n"
//        u8"• Serial Number: 作动器唯一标识\n"
//        u8"• Unit Length: 测量单位选择\n"
//        u8"• Stroke: 作动器行程长度\n"
//        u8"• Displacement: 位移量\n"
//        u8"• Tension Area: 拉伸有效面积\n"
//        u8"• Compression Area: 压缩有效面积\n"
//        u8"• Polarity: 伺服控制器极性\n"
//        u8"• Deliver: 输出电压\n"
//        u8"• Frequency: 控制频率\n"
//        u8"• Output Multiplier: 输出倍数\n"
//        u8"• Balance: 平衡电压\n\n"
//        u8"🔧 操作提示:\n"
//        u8"• 使用Auto Balance自动设置平衡值\n"
//        u8"• 缸径和杆径会根据面积自动计算\n"
//        u8"• 点击预览查看完整配置信息"
//    );

//    QMessageBox::information(this, u8"帮助", helpText);
//}

//void ActuatorDialog_1_2::onInfoClicked() {
//    QString category = ui->categoryCombo->currentText();
//    QString infoText;

//    if (category == u8"液压作动器") {
//        infoText = u8"液压作动器特点:\n• 输出力大，功率密度高\n• 响应速度快\n• 适用于重载应用\n• 需要液压系统支持";
//    } else if (category == u8"电动作动器") {
//        infoText = u8"电动作动器特点:\n• 精度高，控制简单\n• 维护成本低\n• 环保无污染\n• 适用于精密定位";
//    } else if (category == u8"气动作动器") {
//        infoText = u8"气动作动器特点:\n• 结构简单，成本低\n• 动作迅速\n• 安全可靠\n• 适用于快速动作";
//    } else if (category == u8"伺服作动器") {
//        infoText = u8"伺服作动器特点:\n• 高精度位置控制\n• 闭环反馈系统\n• 动态响应好\n• 适用于精密控制";
//    }

//    QMessageBox::information(this, u8"作动器类型说明", infoText);
//}

//void ActuatorDialog_1_2::onCategoryChanged(const QString& category) {
//    // 根据选择的作动器类型自动调整默认参数
//    if (category == u8"液压作动器") {
//        ui->strokeSpinBox->setValue(0.30);
//        ui->displacementSpinBox->setValue(0.30);
//        ui->tensionAreaSpinBox->setValue(0.60);
//        ui->compressionAreaSpinBox->setValue(0.50);
//        ui->frequencySpinBox->setValue(100.00);
//        ui->ditherSpinBox->setValue(0.100);
//    } else if (category == u8"电动作动器") {
//        ui->strokeSpinBox->setValue(0.20);
//        ui->displacementSpinBox->setValue(0.20);
//        ui->tensionAreaSpinBox->setValue(0.30);
//        ui->compressionAreaSpinBox->setValue(0.30);
//        ui->frequencySpinBox->setValue(1000.00);
//        ui->ditherSpinBox->setValue(0.050);
//    } else if (category == u8"气动作动器") {
//        ui->strokeSpinBox->setValue(0.25);
//        ui->displacementSpinBox->setValue(0.25);
//        ui->tensionAreaSpinBox->setValue(0.40);
//        ui->compressionAreaSpinBox->setValue(0.35);
//        ui->frequencySpinBox->setValue(50.00);
//        ui->ditherSpinBox->setValue(0.200);
//    } else if (category == u8"伺服作动器") {
//        ui->strokeSpinBox->setValue(0.15);
//        ui->displacementSpinBox->setValue(0.15);
//        ui->tensionAreaSpinBox->setValue(0.20);
//        ui->compressionAreaSpinBox->setValue(0.18);
//        ui->frequencySpinBox->setValue(528.00);
//        ui->ditherSpinBox->setValue(0.000);
//    }
//}

// 🚫 删除重复的方法定义（已在前面定义过）

ActuatorParams_1_2 ActuatorDialog_1_2::getActuatorParams() const {
    ActuatorParams_1_2 params;

    // 🆕 从新的UI控件获取数据

    // 基本信息标签页
    params.name = ui->nameEdit->text().trimmed();
    params.params.sn = ui->serialEdit->text().trimmed();
    params.params.model = ui->modelEdit->text().trimmed();

    // 🔍 调试信息：输出对话框获取的参数
    qDebug() << QString(u8"🔍 对话框获取参数 - 控制量名称: '%1', 序列号: '%2', 型号: '%3'")
                .arg(params.name).arg(params.params.sn).arg(params.params.model);
    params.zero_offset = ui->zeroOffsetSpinBox->value();

    // 作动器类型
    QString typeText = ui->typeCombo->currentText();
    if (typeText == "双出杆") {
        params.type = UI::ActuatorType_1_2::DoubleRod;
    } else {
        params.type = UI::ActuatorType_1_2::SingleRod;
    }

    // 硬件配置标签页
    params.lc_id = ui->lcIdSpinBox->value();
    params.station_id = ui->stationIdSpinBox->value();
    params.board_id_ao = ui->boardIdAoSpinBox->value();
    params.board_type_ao = ui->boardTypeAoSpinBox->value();
    params.port_id_ao = ui->portIdAoSpinBox->value();
    params.board_id_do = ui->boardIdDoSpinBox->value();
    params.board_type_do = ui->boardTypeDoSpinBox->value();
    params.port_id_do = ui->portIdDoSpinBox->value();

    // 详细参数标签页
    params.params.k = ui->kSpinBox->value();
    params.params.b = ui->bSpinBox->value();
    params.params.precision = ui->precisionSpinBox->value();

    // 极性转换
    QString polarityText = ui->polarityCombo->currentText();
    if (polarityText.contains("负极性") || polarityText.contains("-")) {
        params.params.polarity = UI::Polarity_1_2::Negative;
    } else if (polarityText.contains("双极性") || polarityText.contains("±")) {
        params.params.polarity = UI::Polarity_1_2::Both;
    } else if (polarityText.contains("无极性")) {
        params.params.polarity = UI::Polarity_1_2::Unknown;
    } else {
        params.params.polarity = UI::Polarity_1_2::Positive;
    }

    // 测量单位转换
    QString unitValue = ui->measUnitCombo->currentText();
    if (unitValue == "m") {
        params.params.meas_unit = UI::MeasurementUnit_1_2::Meter;
    } else if (unitValue == "cm") {
        params.params.meas_unit = UI::MeasurementUnit_1_2::Centimeter;
    } else if (unitValue == "inch") {
        params.params.meas_unit = UI::MeasurementUnit_1_2::Inch;
    } else {
        params.params.meas_unit = UI::MeasurementUnit_1_2::Millimeter;
    }

    // 测量范围
    params.params.meas_range_min = ui->measRangeMinSpinBox->value();
    params.params.meas_range_max = ui->measRangeMaxSpinBox->value();

    // 输出信号配置标签页
    params.params.output_signal_unit = ui->outputSignalUnitSpinBox->value();
    params.params.output_signal_range_min = ui->outputRangeMinSpinBox->value();
    params.params.output_signal_range_max = ui->outputRangeMaxSpinBox->value();

    return params;
}

void ActuatorDialog_1_2::setActuatorParams(const ActuatorParams_1_2& params) {
    // 🆕 设置新的UI控件数据

    // 基本信息标签页
    ui->nameEdit->setText(params.name);
    ui->serialEdit->setText(params.params.sn);
    ui->modelEdit->setText(params.params.model);
    ui->zeroOffsetSpinBox->setValue(params.zero_offset);

    // 设置作动器类型
    QString typeText = ActuatorDataManager_1_2::actuatorTypeToString(params.type);
    ui->typeCombo->setCurrentText(typeText);

    // 硬件配置标签页
    ui->lcIdSpinBox->setValue(params.lc_id);
    ui->stationIdSpinBox->setValue(params.station_id);
    ui->boardIdAoSpinBox->setValue(params.board_id_ao);
    ui->boardTypeAoSpinBox->setValue(params.board_type_ao);
    ui->portIdAoSpinBox->setValue(params.port_id_ao);
    ui->boardIdDoSpinBox->setValue(params.board_id_do);
    ui->boardTypeDoSpinBox->setValue(params.board_type_do);
    ui->portIdDoSpinBox->setValue(params.port_id_do);

    // 详细参数标签页
    ui->kSpinBox->setValue(params.params.k);
    ui->bSpinBox->setValue(params.params.b);
    ui->precisionSpinBox->setValue(params.params.precision);

    // 设置极性 - 使用数据值匹配而不是文本匹配
    int polarityValue = static_cast<int>(params.params.polarity);
    for (int i = 0; i < ui->polarityCombo->count(); ++i) {
        if (ui->polarityCombo->itemData(i).toInt() == polarityValue) {
            ui->polarityCombo->setCurrentIndex(i);
            break;
        }
    }

    // 设置测量单位
    QString unitValue = ActuatorDataManager_1_2::measurementUnitToString(params.params.meas_unit);
    if (unitValue == u8"米") unitValue = "m";
    else if (unitValue == u8"厘米") unitValue = "cm";
    else if (unitValue == u8"英寸") unitValue = "inch";
    else unitValue = "mm";
    ui->measUnitCombo->setCurrentText(unitValue);

    // 设置测量范围
    ui->measRangeMinSpinBox->setValue(params.params.meas_range_min);
    ui->measRangeMaxSpinBox->setValue(params.params.meas_range_max);

    // 输出信号配置标签页
    ui->outputSignalUnitSpinBox->setValue(params.params.output_signal_unit);
    ui->outputRangeMinSpinBox->setValue(params.params.output_signal_range_min);
    ui->outputRangeMaxSpinBox->setValue(params.params.output_signal_range_max);
}

// 🆕 新增：编辑模式相关方法实现
void ActuatorDialog_1_2::setEditMode(bool enabled, const QString& originalSerialNumber) {
    editMode_ = enabled;
    originalSerialNumber_ = originalSerialNumber;
    
    if (editMode_) {
        setWindowTitle(u8"编辑作动器参数");
        // 🔒 编辑模式下，序列号不允许修改
        ui->serialEdit->setEnabled(false);
        ui->serialEdit->setStyleSheet("QLineEdit { background-color: #f0f0f0; color: #666; }");
    } else {
        setWindowTitle(u8"新建作动器");
        ui->serialEdit->setEnabled(true);
        ui->serialEdit->setStyleSheet("");
    }
}

bool ActuatorDialog_1_2::isEditMode() const {
    return editMode_;
}

void ActuatorDialog_1_2::setCurrentGroupId(int groupId) {
    currentGroupId_ = groupId;
}

int ActuatorDialog_1_2::getCurrentGroupId() const {
    return currentGroupId_;
}

void ActuatorDialog_1_2::setDataManager(ActuatorDataManager_1_2* manager) {
    dataManager_ = manager;
}

void ActuatorDialog_1_2::applyParameterTemplate(const QString& templateName) {
    if (templateName == u8"液压作动器") {
        ui->typeCombo->setCurrentText(u8"单出杆");
        ui->kSpinBox->setValue(2.0);
        ui->bSpinBox->setValue(0.3);
        ui->precisionSpinBox->setValue(0.1);
        ui->polarityCombo->setCurrentText(u8"正极性 (+)");
        ui->measUnitCombo->setCurrentText("mm");
        ui->measRangeMinSpinBox->setValue(0.0);
        ui->measRangeMaxSpinBox->setValue(100.0);
    }
    else if (templateName == u8"电动作动器") {
        ui->typeCombo->setCurrentText(u8"单出杆");
        ui->kSpinBox->setValue(1.5);
        ui->bSpinBox->setValue(0.2);
        ui->precisionSpinBox->setValue(0.05);
        ui->polarityCombo->setCurrentText(u8"正极性 (+)");
        ui->measUnitCombo->setCurrentText("mm");
        ui->measRangeMinSpinBox->setValue(0.0);
        ui->measRangeMaxSpinBox->setValue(50.0);
    }
    else if (templateName == u8"气动作动器") {
        ui->typeCombo->setCurrentText(u8"双出杆");
        ui->kSpinBox->setValue(1.0);
        ui->bSpinBox->setValue(0.15);
        ui->precisionSpinBox->setValue(0.2);
        ui->polarityCombo->setCurrentText(u8"正极性 (+)");
        ui->measUnitCombo->setCurrentText("mm");
        ui->measRangeMinSpinBox->setValue(0.0);
        ui->measRangeMaxSpinBox->setValue(75.0);
    }
    else if (templateName == u8"伺服作动器") {
        ui->typeCombo->setCurrentText(u8"单出杆");
        ui->kSpinBox->setValue(3.0);
        ui->bSpinBox->setValue(0.1);
        ui->precisionSpinBox->setValue(0.01);
        ui->polarityCombo->setCurrentText(u8"正极性 (+)");
        ui->measUnitCombo->setCurrentText("mm");
        ui->measRangeMinSpinBox->setValue(0.0);
        ui->measRangeMaxSpinBox->setValue(25.0);
    }
}

QString ActuatorDialog_1_2::exportParameterTemplate() const {
    QStringList params;
    params << QString("type=%1").arg(ui->typeCombo->currentText());
    params << QString("k=%1").arg(ui->kSpinBox->value());
    params << QString("b=%1").arg(ui->bSpinBox->value());
    params << QString("precision=%1").arg(ui->precisionSpinBox->value());
    params << QString("polarity=%1").arg(ui->polarityCombo->currentText());
    params << QString("unit=%1").arg(ui->measUnitCombo->currentText());
    params << QString("range=%1-%2").arg(ui->measRangeMinSpinBox->value()).arg(ui->measRangeMaxSpinBox->value());
    
    return params.join(";");
}

void ActuatorDialog_1_2::resetForm() {
    // 重置为默认值
    ui->nameEdit->setText(QString("控制量_%1").arg(autoNumber_));
    ui->serialEdit->setText(autoNumber_);
    ui->modelEdit->setText(QString("型号_%1").arg(autoNumber_));
    
    // 重置参数标签页
    ui->typeCombo->setCurrentIndex(0);
    ui->kSpinBox->setValue(1.0);
    ui->bSpinBox->setValue(0.1);
    ui->precisionSpinBox->setValue(0.1);
    
    // 重置配置标签页
    ui->polarityCombo->setCurrentIndex(0);
    ui->measUnitCombo->setCurrentText("mm");
    ui->measRangeMinSpinBox->setValue(0.0);
    ui->measRangeMaxSpinBox->setValue(100.0);
    
    // 重置输出信号标签页
    ui->outputSignalUnitSpinBox->setValue(1.0);
    ui->outputRangeMinSpinBox->setValue(0.0);
    ui->outputRangeMaxSpinBox->setValue(10.0);
}

bool ActuatorDialog_1_2::validateInput() {
    // 验证必填字段
    QString controlName = ui->nameEdit->text().trimmed();
    QString serialNumber = ui->serialEdit->text().trimmed();
    QString model = ui->modelEdit->text().trimmed();

    if (controlName.isEmpty()) {
        QMessageBox::warning(this, u8"验证错误", u8"请输入控制量名称");
        ui->nameEdit->setFocus();
        return false;
    }

    if (serialNumber.isEmpty()) {
        QMessageBox::warning(this, u8"验证错误", u8"请输入序列号");
        ui->serialEdit->setFocus();
        return false;
    }

    if (model.isEmpty()) {
        QMessageBox::warning(this, u8"验证错误", u8"请输入型号信息");
        ui->modelEdit->setFocus();
        return false;
    }

    // 序列号唯一性验证（仅当有数据管理器时）
    if (dataManager_ && currentGroupId_ >= 0) {
        // 在编辑模式下，如果序列号与原始序列号相同，则跳过唯一性检查
        if (editMode_ && serialNumber == originalSerialNumber_) {
            // 序列号没有变化，不需要检查唯一性
        } else {
            // 检查序列号是否已存在（不唯一则表示已存在）
            if (!dataManager_->isSerialNumberUniqueInGroup(serialNumber, currentGroupId_)) {
                QMessageBox::warning(this, u8"验证错误", 
                    QString(u8"序列号 '%1' 已存在于当前组中，请选择其他序列号").arg(serialNumber));
                ui->serialEdit->setFocus();
                return false;
            }
        }
    }

    // 验证测量范围
    if (ui->measRangeMinSpinBox->value() >= ui->measRangeMaxSpinBox->value()) {
        QMessageBox::warning(this, u8"验证错误", u8"测量下限必须小于测量上限");
        ui->measRangeMinSpinBox->setFocus();
        return false;
    }

    // 验证输出信号范围
    if (ui->outputRangeMinSpinBox->value() >= ui->outputRangeMaxSpinBox->value()) {
        QMessageBox::warning(this, u8"验证错误", u8"输出信号下限必须小于输出信号上限");
        ui->outputRangeMinSpinBox->setFocus();
        return false;
    }

    return true;
}

} // namespace UI
