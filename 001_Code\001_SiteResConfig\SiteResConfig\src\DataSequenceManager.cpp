#include "DataSequenceManager.h"
#include <QTime>
#include <algorithm>

DataSequenceManager::DataSequenceManager(QObject *parent)
    : QObject(parent)
{
    // 初始化支持的数据类型
    QStringList dataTypes = {u8"作动器", u8"传感器", u8"硬件节点", u8"控制通道"};
    for (const QString& dataType : dataTypes) {
        nextSequentialIds_[dataType] = 1;
        sequenceMappings_[dataType] = QMap<int, int>();
        registeredSerialNumbers_[dataType] = QSet<QString>();
        statistics_[dataType] = QMap<QString, int>();
    }
}

// ============================================================================
// 序号映射管理
// ============================================================================

void DataSequenceManager::resetSequenceMapping(const QString& dataType) {
    nextSequentialIds_[dataType] = 1;
    sequenceMappings_[dataType].clear();
    registeredSerialNumbers_[dataType].clear();
    statistics_[dataType].clear();
    
    qDebug() << QString(u8"🔄 重置序号映射器：%1").arg(dataType);
}

int DataSequenceManager::getSequentialId(const QString& dataType, int excelSequence, const QString& groupName) {
    if (!sequenceMappings_.contains(dataType)) {
        qWarning() << QString(u8"不支持的数据类型：%1").arg(dataType);
        return excelSequence; // 返回原始序号
    }
    
    auto& mapping = sequenceMappings_[dataType];
    
    if (mapping.contains(excelSequence)) {
        // 已存在映射，返回已分配的序号
        int actualId = mapping[excelSequence];
        qDebug() << QString(u8"🔍 使用已有映射：%1 Excel序号 %2 -> 实际序号 %3")
                    .arg(dataType).arg(excelSequence).arg(actualId);
        return actualId;
    } else {
        // 创建新映射
        int actualId = nextSequentialIds_[dataType]++;
        mapping[excelSequence] = actualId;
        
        updateStatistics(dataType, u8"映射数量");
        
        qDebug() << QString(u8"🆕 创建新映射：%1 Excel序号 %2 -> 实际序号 %3 (组名称: %4)")
                    .arg(dataType).arg(excelSequence).arg(actualId).arg(groupName);
        
        emit sequenceMapped(dataType, excelSequence, actualId, groupName);
        return actualId;
    }
}

QMap<int, int> DataSequenceManager::getSequenceMapping(const QString& dataType) const {
    return sequenceMappings_.value(dataType, QMap<int, int>());
}

// ============================================================================
// 数据验证
// ============================================================================

QPair<bool, QStringList> DataSequenceManager::validateSequenceContinuity(const QString& dataType, const QList<int>& sequenceList) {
    QStringList report;
    bool isValid = true;
    
    if (sequenceList.isEmpty()) {
        report << QString(u8"⚠️  %1：序号列表为空").arg(dataType);
        return qMakePair(true, report); // 空列表认为是有效的
    }
    
    QList<int> sortedList = sequenceList;
    std::sort(sortedList.begin(), sortedList.end());
    
    // 检查是否从1开始
    if (sortedList.first() != 1) {
        report << QString(u8"❌ %1：序号不是从1开始，实际起始序号：%2").arg(dataType).arg(sortedList.first());
        isValid = false;
    }
    
    // 检查连续性
    for (int i = 0; i < sortedList.size(); ++i) {
        int expected = i + 1;
        int actual = sortedList[i];
        if (actual != expected) {
            report << QString(u8"❌ %1：序号不连续，期望 %2，实际 %3").arg(dataType).arg(expected).arg(actual);
            isValid = false;
        }
    }
    
    if (isValid) {
        report << QString(u8"✅ %1：序号连续性检查通过 (1-%2)").arg(dataType).arg(sortedList.size());
    }
    
    updateStatistics(dataType, u8"连续性检查", isValid ? 1 : 0);
    return qMakePair(isValid, report);
}

QPair<bool, QList<int>> DataSequenceManager::validateSequenceUniqueness(const QString& dataType, const QList<int>& sequenceList) {
    QSet<int> seen;
    QList<int> duplicates;
    
    for (int sequence : sequenceList) {
        if (seen.contains(sequence)) {
            if (!duplicates.contains(sequence)) {
                duplicates.append(sequence);
            }
        } else {
            seen.insert(sequence);
        }
    }
    
    bool isUnique = duplicates.isEmpty();
    updateStatistics(dataType, u8"唯一性检查", isUnique ? 1 : 0);
    
    if (!isUnique) {
        // 将整数列表转换为字符串列表
        QStringList duplicateStrings;
        for (int duplicate : duplicates) {
            duplicateStrings << QString::number(duplicate);
        }
        qWarning() << QString(u8"❌ %1：发现重复序号：%2").arg(dataType).arg(duplicateStrings.join(", "));
    } else {
        qDebug() << QString(u8"✅ %1：序号唯一性检查通过").arg(dataType);
    }
    
    return qMakePair(isUnique, duplicates);
}

QStringList DataSequenceManager::generateValidationReport(const QString& dataType, int totalGroups, int totalItems) {
    QStringList report;
    report << QString(u8"=== %1 数据序号验证报告 ===").arg(dataType);
    report << QString(u8"📊 统计信息：%1个组，%2个项目").arg(totalGroups).arg(totalItems);
    
    // 序号映射信息
    auto mapping = getSequenceMapping(dataType);
    if (!mapping.isEmpty()) {
        report << QString(u8"🔢 序号映射：%1个映射关系").arg(mapping.size());
        
        QList<int> excelIds = mapping.keys();
        QList<int> actualIds = mapping.values();
        std::sort(excelIds.begin(), excelIds.end());
        std::sort(actualIds.begin(), actualIds.end());
        
        report << QString(u8"📈 Excel序号范围：%1-%2").arg(excelIds.first()).arg(excelIds.last());
        report << QString(u8"📈 实际序号范围：%1-%2").arg(actualIds.first()).arg(actualIds.last());
    }
    
    // 序列号统计
    auto serialNumbers = registeredSerialNumbers_.value(dataType, QSet<QString>());
    report << QString(u8"🏷️  序列号：%1个已注册").arg(serialNumbers.size());
    
    // 验证结果
    auto continuityResult = validateSequenceContinuity(dataType, mapping.values());
    report << continuityResult.second;
    
    auto uniquenessResult = validateSequenceUniqueness(dataType, mapping.values());
    if (!uniquenessResult.first) {
        // 将整数列表转换为字符串列表
        QStringList duplicateStrings;
        for (int duplicate : uniquenessResult.second) {
            duplicateStrings << QString::number(duplicate);
        }
        report << QString(u8"❌ 发现重复序号：%1").arg(duplicateStrings.join(", "));
    }
    
    report << QString(u8"=== 验证完成 ===");
    
    emit validationCompleted(dataType, continuityResult.first && uniquenessResult.first, report);
    return report;
}

// ============================================================================
// 序列号管理
// ============================================================================

//bool DataSequenceManager::isSerialNumberUnique(const QString& dataType, const QString& serialNumber) {
//    auto& serialNumbers = registeredSerialNumbers_[dataType];
//    return !serialNumbers.contains(serialNumber);
//}

void DataSequenceManager::registerSerialNumber(const QString& dataType, const QString& serialNumber) {
    registeredSerialNumbers_[dataType].insert(serialNumber);
    updateStatistics(dataType, u8"序列号数量");
}

QString DataSequenceManager::generateUniqueSerialNumber(const QString& dataType, const QString& originalSerialNumber) {
    // 🔄 修改：DataSequenceManager不再处理序列号重复，改为组内唯一性检查
    // 序列号重复处理应该在Excel导入层面进行，使用组内唯一性检查

    // 直接注册序列号，不进行全局唯一性检查
    registerSerialNumber(dataType, originalSerialNumber);

    qDebug() << QString(u8"📝 注册序列号：%1 - %2").arg(dataType).arg(originalSerialNumber);

    return originalSerialNumber;
}

// ============================================================================
// 统计和报告
// ============================================================================

QString DataSequenceManager::getStatistics(const QString& dataType) const {
    auto stats = statistics_.value(dataType, QMap<QString, int>());
    QStringList statsList;
    
    for (auto it = stats.begin(); it != stats.end(); ++it) {
        statsList << QString("%1: %2").arg(it.key()).arg(it.value());
    }
    
    return QString(u8"%1 统计：%2").arg(dataType).arg(statsList.join(", "));
}

void DataSequenceManager::clearAll() {
    sequenceMappings_.clear();
    nextSequentialIds_.clear();
    registeredSerialNumbers_.clear();
    statistics_.clear();
    
    qDebug() << u8"🧹 清空所有数据序号管理器数据";
}

// ============================================================================
// 私有方法
// ============================================================================

void DataSequenceManager::updateStatistics(const QString& dataType, const QString& key, int increment) {
    statistics_[dataType][key] += increment;
}
