# 📊 CSV管理器使用指南

## 🎯 **概述**

CSVManager是一个功能完整的CSV文件操作类，提供了读写、验证、格式处理等全面的CSV操作功能。

## 🏗️ **类结构**

### **核心类**
- `CSVManager` - 主要的CSV操作管理器
- `CSVConfig` - CSV配置结构体
- `CSVUtils` - 静态工具类
- `CSVError` - 错误码枚举

### **文件结构**
```
include/CSVManager.h    # 头文件定义
src/CSVManager.cpp      # 实现文件
src/CSVManagerExample.cpp # 使用示例
```

## 🚀 **快速开始**

### **1. 基础使用**

```cpp
#include "CSVManager.h"

// 创建CSV管理器
CSVManager csvManager;

// 设置配置
csvManager.setSeparator(',');
csvManager.setEncoding("UTF-8");
csvManager.setHasHeader(true);

// 创建数据
QVector<QStringList> data;
data.append(QStringList() << "序号" << "时间" << "数值");
data.append(QStringList() << "1" << "0.0" << "100.5");
data.append(QStringList() << "2" << "0.1" << "200.3");

// 设置数据并保存
csvManager.setData(data);
csvManager.saveToFile("output.csv");

// 从文件加载
CSVManager loader;
if (loader.loadFromFile("input.csv")) {
    auto loadedData = loader.getData();
    qDebug() << "行数:" << loader.getRowCount();
    qDebug() << "列数:" << loader.getColumnCount();
}
```

### **2. 配置管理**

```cpp
// 使用配置结构体
CSVConfig config;
config.separator = ';';           // 分隔符
config.quoteChar = '"';          // 引号字符
config.encoding = "UTF-8";       // 编码格式
config.hasHeader = true;         // 包含表头
config.autoDetect = true;        // 自动检测格式
config.maxRows = 10000;          // 最大行数限制
config.trimWhitespace = true;    // 去除空白字符

csvManager.setConfig(config);
```

## 📋 **主要功能**

### **1. 文件操作**

#### **加载CSV文件**
```cpp
CSVManager manager;
if (manager.loadFromFile("data.csv")) {
    qDebug() << "加载成功";
} else {
    qDebug() << "加载失败:" << manager.getErrorString();
}
```

#### **保存CSV文件**
```cpp
if (manager.saveToFile("output.csv")) {
    qDebug() << "保存成功";
} else {
    qDebug() << "保存失败:" << manager.getErrorString();
}
```

#### **导出为其他格式**
```cpp
manager.exportToFormat("data.json", "json");
manager.exportToFormat("data.xml", "xml");
manager.exportToFormat("data.tsv", "tsv");
```

### **2. 数据操作**

#### **行操作**
```cpp
// 获取行数据
QStringList row = manager.getRow(0);

// 设置行数据
manager.setRow(0, QStringList() << "新" << "数据");

// 添加行
manager.addRow(QStringList() << "新行" << "数据");

// 删除行
manager.removeRow(2);
```

#### **列操作**
```cpp
// 按索引获取列
QStringList column = manager.getColumn(1);

// 按名称获取列（需要表头）
QStringList nameColumn = manager.getColumn("姓名");

// 获取表头
QStringList headers = manager.getHeaders();

// 设置表头
manager.setHeaders(QStringList() << "ID" << "名称" << "数值");
```

### **3. 高级功能**

#### **数据过滤**
```cpp
// 过滤分数大于80的记录
manager.filterData([](const QStringList& row, int index) {
    if (index == 0) return true; // 保留表头
    if (row.size() >= 3) {
        bool ok;
        double score = row[2].toDouble(&ok);
        return ok && score > 80.0;
    }
    return false;
});
```

#### **数据排序**
```cpp
// 按第2列降序排列
manager.sortData(2, false);
```

#### **数据合并**
```cpp
CSVManager other;
other.loadFromFile("other.csv");

// 追加模式合并
manager.mergeData(other, true);

// 列合并模式
manager.mergeData(other, false);
```

#### **批量处理**
```cpp
QStringList inputFiles = {"file1.csv", "file2.csv", "file3.csv"};

int processed = CSVManager::batchProcess(
    inputFiles,
    "output/",
    [](CSVManager& manager, const QString& outputFile) {
        // 处理逻辑
        auto data = manager.getData();
        // ... 修改数据 ...
        manager.setData(data);
        return manager.saveToFile(outputFile);
    }
);
```

### **4. 进度监控**

```cpp
manager.setProgressCallback([](int current, int total, const QString& message) {
    int percentage = (current * 100) / total;
    qDebug() << QString("进度: %1% - %2").arg(percentage).arg(message);
    return true; // 返回false可取消操作
});
```

### **5. 格式检测**

```cpp
// 自动检测编码
QString encoding = CSVManager::detectEncoding("file.csv");

// 自动检测分隔符
QChar separator = CSVManager::detectSeparator("file.csv");

// 检查是否为CSV文件
bool isCSV = CSVUtils::isCSVFile("file.csv");
```

## 🔧 **配置选项**

### **编码支持**
- UTF-8 (默认)
- GBK
- GB2312
- UTF-16LE
- UTF-16BE
- ISO-8859-1

### **分隔符支持**
- `,` 逗号 (默认)
- `;` 分号
- `\t` 制表符
- `|` 竖线
- ` ` 空格

### **特殊字符处理**
- 自动转义包含分隔符的字段
- 支持字段内换行符
- 处理引号转义

## ⚠️ **错误处理**

### **错误码类型**
```cpp
enum class CSVError {
    NoError = 0,           // 无错误
    FileNotFound,          // 文件未找到
    FileOpenFailed,        // 文件打开失败
    FileWriteFailed,       // 文件写入失败
    InvalidFormat,         // 格式无效
    EncodingError,         // 编码错误
    DataValidationFailed,  // 数据验证失败
    MemoryError,          // 内存错误
    UserCancelled         // 用户取消
};
```

### **错误处理示例**
```cpp
if (!manager.loadFromFile("file.csv")) {
    CSVError error = manager.getLastError();
    QString errorMsg = manager.getErrorString();
    QString errorDesc = CSVUtils::errorToString(error);
    
    qDebug() << "错误码:" << static_cast<int>(error);
    qDebug() << "错误信息:" << errorMsg;
    qDebug() << "错误描述:" << errorDesc;
}
```

## 📊 **统计信息**

```cpp
auto stats = manager.getStatistics();
qDebug() << "行数:" << stats["rowCount"];
qDebug() << "列数:" << stats["columnCount"];
qDebug() << "总单元格数:" << stats["totalCells"];
qDebug() << "空单元格数:" << stats["emptyCells"];
qDebug() << "空值百分比:" << stats["emptyPercentage"];
```

## 🎯 **最佳实践**

### **1. 性能优化**
- 对大文件使用进度回调
- 设置合理的最大行数限制
- 使用批量处理处理多个文件

### **2. 内存管理**
- 及时调用clear()释放内存
- 避免同时加载过多大文件
- 使用智能指针管理CSVManager对象

### **3. 错误处理**
- 始终检查操作返回值
- 使用getLastError()获取详细错误信息
- 为用户提供友好的错误提示

### **4. 编码处理**
- 优先使用UTF-8编码
- 启用自动检测功能
- 处理中文时注意编码设置

## 🔗 **集成到现有项目**

### **1. 添加到项目文件**
在`SiteResConfig_Simple.pro`中添加：
```pro
HEADERS += include/CSVManager.h
SOURCES += src/CSVManager.cpp
```

### **2. 在代码中使用**
```cpp
#include "CSVManager.h"

// 在需要CSV操作的地方使用
CSVManager csvManager;
// ... 使用CSV功能
```

### **3. 替换现有CSV代码**
将现有的手动CSV处理代码替换为CSVManager调用，提高代码质量和可维护性。

## 📝 **示例文件**

完整的使用示例请参考：
- `src/CSVManagerExample.cpp` - 详细的功能演示
- `test_csv_manager.bat` - 编译测试脚本

运行示例：
```bash
# 编译并测试
test_csv_manager.bat
```
