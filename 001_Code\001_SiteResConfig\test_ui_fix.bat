@echo off
echo ========================================
echo  测试UI控件编译错误修复
echo ========================================

REM 设置Qt环境变量
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo 编译失败！请检查错误信息。
    pause
    exit /b 1
) else (
    echo.
    echo 编译成功！UI控件错误已修复
    echo.
    echo 修复的错误:
    echo - setSensorDataManager 参数类型错误
    echo - setActuatorDataManager 参数类型错误  
    echo - systemOverviewLabel 控件不存在
    echo - refreshHardwareButton 控件不存在
    echo - dataTableWidget 控件不存在
    echo.
    echo 项目状态管理功能已集成
    
    if exist "SiteResConfig.exe" (
        echo 启动程序...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 启动程序...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 启动程序...
        start release\SiteResConfig.exe
    )
)

pause
