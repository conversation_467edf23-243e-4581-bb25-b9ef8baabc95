/**
 * @file test_datamanager_integration.cpp
 * @brief 测试DataManager集成到DataModels的功能
 * @details 验证传感器和作动器接口是否正确使用DataManager
 * <AUTHOR> Assistant
 * @date 2025-08-14
 * @version 1.0.0
 */

#include <iostream>
#include <cassert>
#include "DataModels_Fixed.h"
#include "SensorDataManager.h"
#include "ActuatorDataManager.h"

using namespace DataModels;

/**
 * @brief 测试传感器DataManager集成
 */
void testSensorDataManagerIntegration() {
    std::cout << "=== 测试传感器DataManager集成 ===" << std::endl;
    
    // 创建测试项目
    TestProject project;
    
    // 创建DataManager实例
    SensorDataManager sensorManager(&project);
    project.setSensorDataManager(&sensorManager);
    
    // 创建测试传感器参数
    UI::SensorParams sensorParams;
    sensorParams.serialNumber = "SENSOR001";
    sensorParams.sensorName = "测试力传感器";
    sensorParams.sensorType = "Force";
    sensorParams.fullScale = 100000.0;
    sensorParams.unit = "N";
    
    // 测试添加传感器
    bool addResult = project.addSensorDetailedParams("SENSOR001", sensorParams);
    assert(addResult && "添加传感器应该成功");
    std::cout << "✓ 添加传感器成功" << std::endl;
    
    // 测试检查传感器存在
    bool hasResult = project.hasSensorDetailedParams("SENSOR001");
    assert(hasResult && "传感器应该存在");
    std::cout << "✓ 传感器存在检查成功" << std::endl;
    
    // 测试获取传感器
    UI::SensorParams retrievedParams = project.getSensorDetailedParams("SENSOR001");
    assert(retrievedParams.serialNumber == "SENSOR001" && "获取的传感器序列号应该匹配");
    std::cout << "✓ 获取传感器成功" << std::endl;
    
    // 测试更新传感器
    sensorParams.fullScale = 200000.0;
    bool updateResult = project.updateSensorDetailedParams("SENSOR001", sensorParams);
    assert(updateResult && "更新传感器应该成功");
    std::cout << "✓ 更新传感器成功" << std::endl;
    
    // 测试获取传感器数量
    int sensorCount = project.getSensorCount();
    assert(sensorCount == 1 && "传感器数量应该为1");
    std::cout << "✓ 传感器数量检查成功: " << sensorCount << std::endl;
    
    // 测试获取所有传感器序列号
    std::vector<std::string> serialNumbers = project.getAllSensorSerialNumbers();
    assert(serialNumbers.size() == 1 && "应该有1个传感器序列号");
    assert(serialNumbers[0] == "SENSOR001" && "序列号应该匹配");
    std::cout << "✓ 获取所有传感器序列号成功" << std::endl;
    
    // 测试删除传感器
    bool removeResult = project.removeSensorDetailedParams("SENSOR001");
    assert(removeResult && "删除传感器应该成功");
    std::cout << "✓ 删除传感器成功" << std::endl;
    
    // 验证传感器已删除
    bool hasAfterRemove = project.hasSensorDetailedParams("SENSOR001");
    assert(!hasAfterRemove && "传感器应该已被删除");
    std::cout << "✓ 传感器删除验证成功" << std::endl;
    
    std::cout << "=== 传感器DataManager集成测试完成 ===" << std::endl << std::endl;
}

/**
 * @brief 测试作动器DataManager集成
 */
void testActuatorDataManagerIntegration() {
    std::cout << "=== 测试作动器DataManager集成 ===" << std::endl;
    
    // 创建测试项目
    TestProject project;
    
    // 创建DataManager实例
    ActuatorDataManager actuatorManager(&project);
    project.setActuatorDataManager(&actuatorManager);
    
    // 创建测试作动器参数
    UI::ActuatorParams actuatorParams;
    actuatorParams.serialNumber = "ACT001";
    actuatorParams.actuatorName = "测试液压缸";
    actuatorParams.actuatorType = "Hydraulic";
    actuatorParams.maxForce = 100000.0;
    actuatorParams.stroke = 200.0;
    
    // 测试添加作动器
    bool addResult = project.addActuatorDetailedParams("ACT001", actuatorParams);
    assert(addResult && "添加作动器应该成功");
    std::cout << "✓ 添加作动器成功" << std::endl;
    
    // 测试检查作动器存在
    bool hasResult = project.hasActuatorDetailedParams("ACT001");
    assert(hasResult && "作动器应该存在");
    std::cout << "✓ 作动器存在检查成功" << std::endl;
    
    // 测试获取作动器
    UI::ActuatorParams retrievedParams = project.getActuatorDetailedParams("ACT001");
    assert(retrievedParams.serialNumber == "ACT001" && "获取的作动器序列号应该匹配");
    std::cout << "✓ 获取作动器成功" << std::endl;
    
    // 测试更新作动器
    actuatorParams.maxForce = 200000.0;
    bool updateResult = project.updateActuatorDetailedParams("ACT001", actuatorParams);
    assert(updateResult && "更新作动器应该成功");
    std::cout << "✓ 更新作动器成功" << std::endl;
    
    // 测试获取作动器数量
    int actuatorCount = project.getActuatorCount();
    assert(actuatorCount == 1 && "作动器数量应该为1");
    std::cout << "✓ 作动器数量检查成功: " << actuatorCount << std::endl;
    
    // 测试获取所有作动器序列号
    std::vector<std::string> serialNumbers = project.getAllActuatorSerialNumbers();
    assert(serialNumbers.size() == 1 && "应该有1个作动器序列号");
    assert(serialNumbers[0] == "ACT001" && "序列号应该匹配");
    std::cout << "✓ 获取所有作动器序列号成功" << std::endl;
    
    // 测试删除作动器
    bool removeResult = project.removeActuatorDetailedParams("ACT001");
    assert(removeResult && "删除作动器应该成功");
    std::cout << "✓ 删除作动器成功" << std::endl;
    
    // 验证作动器已删除
    bool hasAfterRemove = project.hasActuatorDetailedParams("ACT001");
    assert(!hasAfterRemove && "作动器应该已被删除");
    std::cout << "✓ 作动器删除验证成功" << std::endl;
    
    std::cout << "=== 作动器DataManager集成测试完成 ===" << std::endl << std::endl;
}

/**
 * @brief 测试作动器组DataManager集成
 */
void testActuatorGroupDataManagerIntegration() {
    std::cout << "=== 测试作动器组DataManager集成 ===" << std::endl;
    
    // 创建测试项目
    TestProject project;
    
    // 创建DataManager实例
    ActuatorDataManager actuatorManager(&project);
    project.setActuatorDataManager(&actuatorManager);
    
    // 创建测试作动器组
    UI::ActuatorGroup group;
    group.groupId = 1;
    group.groupName = "测试组1";
    group.description = "测试作动器组";
    
    // 测试添加作动器组
    bool addResult = project.addActuatorGroup(1, group);
    assert(addResult && "添加作动器组应该成功");
    std::cout << "✓ 添加作动器组成功" << std::endl;
    
    // 测试检查作动器组存在
    bool hasResult = project.hasActuatorGroup(1);
    assert(hasResult && "作动器组应该存在");
    std::cout << "✓ 作动器组存在检查成功" << std::endl;
    
    // 测试获取作动器组
    UI::ActuatorGroup retrievedGroup = project.getActuatorGroup(1);
    assert(retrievedGroup.groupId == 1 && "获取的作动器组ID应该匹配");
    std::cout << "✓ 获取作动器组成功" << std::endl;
    
    // 测试更新作动器组
    group.description = "更新后的描述";
    bool updateResult = project.updateActuatorGroup(1, group);
    assert(updateResult && "更新作动器组应该成功");
    std::cout << "✓ 更新作动器组成功" << std::endl;
    
    // 测试获取作动器组数量
    int groupCount = project.getActuatorGroupCount();
    assert(groupCount == 1 && "作动器组数量应该为1");
    std::cout << "✓ 作动器组数量检查成功: " << groupCount << std::endl;
    
    // 测试删除作动器组
    bool removeResult = project.removeActuatorGroup(1);
    assert(removeResult && "删除作动器组应该成功");
    std::cout << "✓ 删除作动器组成功" << std::endl;
    
    // 验证作动器组已删除
    bool hasAfterRemove = project.hasActuatorGroup(1);
    assert(!hasAfterRemove && "作动器组应该已被删除");
    std::cout << "✓ 作动器组删除验证成功" << std::endl;
    
    std::cout << "=== 作动器组DataManager集成测试完成 ===" << std::endl << std::endl;
}

/**
 * @brief 主测试函数
 */
int main() {
    std::cout << "开始DataManager集成测试..." << std::endl << std::endl;
    
    try {
        testSensorDataManagerIntegration();
        testActuatorDataManagerIntegration();
        testActuatorGroupDataManagerIntegration();
        
        std::cout << "🎉 所有测试通过！DataManager集成成功！" << std::endl;
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "❌ 测试失败: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "❌ 测试失败: 未知错误" << std::endl;
        return 1;
    }
}
