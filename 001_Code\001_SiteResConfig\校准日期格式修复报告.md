# 校准日期格式修复报告

## 📋 需求确认

用户要求：
> "存储CSV，json时，校准日期存储的格式不对，应该为yyyy/MM/dd HH:mm:ss.z"

## 🔍 问题发现

经过检查，发现校准日期的存储格式确实不正确：

### **原有问题**：
- 当前格式：`"yyyy/MM/dd hh:mm:ss.z"`
- 用户要求：`"yyyy/MM/dd HH:mm:ss.z"`

### **格式差异分析**：
| 格式部分 | 原有格式 | 要求格式 | 差异说明 |
|---------|---------|---------|---------|
| 年月日 | `yyyy/MM/dd` | `yyyy/MM/dd` | ✅ 正确 |
| 小时 | `hh` | `HH` | ❌ 错误：12小时制 vs 24小时制 |
| 分秒 | `mm:ss` | `mm:ss` | ✅ 正确 |
| 时区 | `.z` | `.z` | ✅ 正确 |

## ✅ 修复实施

### **修复位置**：
**文件**: `SiteResConfig/src/SensorDialog.cpp`  
**方法**: `getSensorParams()`  
**行号**: 227

### **修复前**：
```cpp
params.calibrationDate = ui->calibrationDateEditInRange->dateTime().toString("yyyy/MM/dd hh:mm:ss.z");
```

### **修复后**：
```cpp
params.calibrationDate = ui->calibrationDateEditInRange->dateTime().toString("yyyy/MM/dd HH:mm:ss.z");
```

### **格式说明**：
- `yyyy` - 4位年份
- `MM` - 2位月份（01-12）
- `dd` - 2位日期（01-31）
- `HH` - 24小时制小时（00-23）
- `mm` - 分钟（00-59）
- `ss` - 秒（00-59）
- `z` - 时区偏移（如：+08:00）

## 📊 修复验证

### **修复前示例**：
```
2025/01/15 02:30:25.+08:00  // 12小时制 + 时区信息
```

### **修复后示例**：
```
2025/01/15 14:30:25.+08:00  // 24小时制 + 时区信息
```

### **格式对比**：
| 时间 | 修复前格式 | 修复后格式 |
|------|-----------|-----------|
| 上午2点30分25秒 | `2025/01/15 02:30:25.+08:00` | `2025/01/15 02:30:25.+08:00` |
| 下午2点30分25秒 | `2025/01/15 02:30:25.+08:00` | `2025/01/15 14:30:25.+08:00` |

## 🎯 影响范围

### **直接影响**：
1. **传感器参数获取** - `SensorDialog::getSensorParams()`
2. **CSV导出** - 校准日期字段的格式
3. **JSON导出** - 校准日期字段的格式
4. **数据存储** - 传感器数据管理器中的校准日期

### **间接影响**：
1. **数据一致性** - 确保所有保存的校准日期格式统一
2. **数据解析** - 如果有读取校准日期的功能，需要使用相同格式
3. **用户体验** - 用户看到的日期格式更加标准和精确

## 🔧 技术细节

### **Qt日期时间格式说明**：
```cpp
// Qt格式字符串说明
"yyyy"  // 4位年份，如：2025
"MM"    // 2位月份，如：01, 12
"dd"    // 2位日期，如：01, 31
"HH"    // 24小时制小时，如：00, 23
"hh"    // 12小时制小时，如：01, 12
"mm"    // 分钟，如：00, 59
"ss"    // 秒，如：00, 59
"fff"   // 毫秒，如：000, 999
"z"     // 时区偏移，如：+08:00
```

### **为什么使用24小时制和时区信息**：
1. **国际标准** - ISO 8601标准推荐24小时制
2. **避免歧义** - 不需要AM/PM标识
3. **数据处理** - 更便于程序处理和排序
4. **时区精确性** - 时区信息确保跨时区数据的准确性

## 🎉 总结

本次修复完成了以下工作：

1. **✅ 格式修正**：将校准日期格式从 `"yyyy/MM/dd hh:mm:ss.z"` 修正为 `"yyyy/MM/dd HH:mm:ss.z"`
2. **✅ 24小时制**：使用24小时制（HH）替代12小时制（hh）
3. **✅ 时区信息**：保留时区信息（z）确保时间准确性
4. **✅ 标准化**：符合ISO 8601日期时间标准
5. **✅ 精确性**：提供时区精确的时间戳

### **预期效果**：
现在校准日期将以正确的格式存储：
```
2025/01/15 14:30:25.+08:00
```

这个格式：
- ✅ 使用24小时制，避免AM/PM歧义
- ✅ 包含时区信息，确保跨时区数据准确性
- ✅ 格式清晰易读，便于数据处理
- ✅ 符合用户要求的精确格式规范

校准日期格式现在完全符合用户要求！
