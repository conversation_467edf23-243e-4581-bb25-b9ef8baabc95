#include "InfoPanelManager.h"
#include "BasicInfoWidget.h"
#include "DetailInfoPanel.h"
#include <QDebug>
#include <QTreeWidgetItem>

InfoPanelManager::InfoPanelManager(QObject* parent)
    : QObject(parent)
    , basicInfoWidget_(nullptr)
    , detailInfoPanel_(nullptr)
    , currentNodeStatus_(NodeStatus::Unknown)
{
    qDebug() << "InfoPanelManager: 初始化信息面板管理器 (基于现有信息面板组件)";
}

InfoPanelManager::~InfoPanelManager()
{
    disconnectInfoPanelSignals();
    qDebug() << "InfoPanelManager: 信息面板管理器已销毁";
}

void InfoPanelManager::setBasicInfoWidget(BasicInfoWidget* widget)
{
    if (basicInfoWidget_) {
        // 断开旧的信号连接
        disconnect(basicInfoWidget_, nullptr, this, nullptr);
    }

    basicInfoWidget_ = widget;
    
    if (basicInfoWidget_) {
        connectInfoPanelSignals();
        qDebug() << "InfoPanelManager: 已设置基本信息面板";
    }
}

void InfoPanelManager::setDetailInfoPanel(DetailInfoPanel* panel)
{
    if (detailInfoPanel_) {
        // 断开旧的信号连接
        disconnect(detailInfoPanel_, nullptr, this, nullptr);
    }

    detailInfoPanel_ = panel;
    
    if (detailInfoPanel_) {
        connectInfoPanelSignals();
        qDebug() << "InfoPanelManager: 已设置详细信息面板";
    }
}

void InfoPanelManager::updateBasicInfo(const NodeInfo& nodeInfo)
{
    if (!basicInfoWidget_) {
        qWarning() << "InfoPanelManager: 基本信息面板未设置，无法更新信息";
        return;
    }

    try {
        // 使用现有BasicInfoWidget的方法
        basicInfoWidget_->setNodeInfo(nodeInfo);
        
        // 缓存当前信息
        currentNodeInfo_ = nodeInfo;
        
        emit infoUpdated("BasicInfo");
        qDebug() << "InfoPanelManager: 基本信息已更新";
    }
    catch (const std::exception& e) {
        qCritical() << "InfoPanelManager: 更新基本信息时发生异常:" << e.what();
    }
}

void InfoPanelManager::updateDetailInfo(const NodeInfo& nodeInfo)
{
    if (!detailInfoPanel_) {
        qWarning() << "InfoPanelManager: 详细信息面板未设置，无法更新信息";
        return;
    }

    try {
        // 使用现有DetailInfoPanel的方法
        detailInfoPanel_->setNodeInfo(nodeInfo);
        
        // 缓存当前信息
        currentNodeInfo_ = nodeInfo;
        
        emit infoUpdated("DetailInfo");
        qDebug() << "InfoPanelManager: 详细信息已更新";
    }
    catch (const std::exception& e) {
        qCritical() << "InfoPanelManager: 更新详细信息时发生异常:" << e.what();
    }
}

void InfoPanelManager::clearAllInfo()
{
    try {
        if (basicInfoWidget_) {
            basicInfoWidget_->clearInfo();
            qDebug() << "InfoPanelManager: 基本信息面板已清空";
        }

        if (detailInfoPanel_) {
            detailInfoPanel_->clearInfo();
            qDebug() << "InfoPanelManager: 详细信息面板已清空";
        }

        // 清空缓存
        currentNodeInfo_ = NodeInfo();
        currentNodeStatus_ = NodeStatus::Unknown;

        emit infoCleared();
        qDebug() << "InfoPanelManager: 所有信息面板已清空";
    }
    catch (const std::exception& e) {
        qCritical() << "InfoPanelManager: 清空信息时发生异常:" << e.what();
    }
}

void InfoPanelManager::updateNodeStatus(NodeStatus status)
{
    try {
        // 创建一个包含状态信息的NodeInfo对象
        NodeInfo statusNodeInfo;
        statusNodeInfo.status = status;
        
        if (basicInfoWidget_) {
            // 如果已有节点信息，保留现有信息并更新状态
            if (!currentNodeInfo_.nodeName.isEmpty() || !currentNodeInfo_.nodeType.isEmpty()) {
                NodeInfo updatedInfo = currentNodeInfo_;
                updatedInfo.status = status;
                basicInfoWidget_->setNodeInfo(updatedInfo);
            } else {
                // 只设置状态信息
                basicInfoWidget_->setNodeInfo(statusNodeInfo);
            }
        }

        if (detailInfoPanel_) {
            // 如果已有节点信息，保留现有信息并更新状态
            if (!currentNodeInfo_.nodeName.isEmpty() || !currentNodeInfo_.nodeType.isEmpty()) {
                NodeInfo updatedInfo = currentNodeInfo_;
                updatedInfo.status = status;
                detailInfoPanel_->setNodeInfo(updatedInfo);
            } else {
                // 只设置状态信息
                detailInfoPanel_->setNodeInfo(statusNodeInfo);
            }
        }

        // 缓存当前状态
        currentNodeStatus_ = status;

        emit nodeStatusChanged(status);
        qDebug() << "InfoPanelManager: 节点状态已更新:" << static_cast<int>(status);
    }
    catch (const std::exception& e) {
        qCritical() << "InfoPanelManager: 更新节点状态时发生异常:" << e.what();
    }
}

void InfoPanelManager::setControlChannelInfo(const QString& channelName, const QList<SubNodeInfo>& subNodes)
{
    if (!detailInfoPanel_) {
        qWarning() << "InfoPanelManager: 详细信息面板未设置，无法设置控制通道信息";
        return;
    }

    try {
        // 使用现有DetailInfoPanel的方法设置控制通道信息
        detailInfoPanel_->setControlChannelInfo(channelName, subNodes);
        
        emit infoUpdated("ControlChannelInfo");
        qDebug() << "InfoPanelManager: 控制通道信息已设置:" << channelName;
    }
    catch (const std::exception& e) {
        qCritical() << "InfoPanelManager: 设置控制通道信息时发生异常:" << e.what();
    }
}

void InfoPanelManager::setControlChannelRootInfo(const QString& rootName, const QList<QTreeWidgetItem*>& childChannels)
{
    if (!detailInfoPanel_) {
        qWarning() << "InfoPanelManager: 详细信息面板未设置，无法设置控制通道根信息";
        return;
    }

    try {
        // 使用现有DetailInfoPanel的方法设置控制通道根信息
        detailInfoPanel_->setControlChannelRootInfo(rootName, childChannels);
        
        emit infoUpdated("ControlChannelRootInfo");
        qDebug() << "InfoPanelManager: 控制通道根信息已设置:" << rootName;
    }
    catch (const std::exception& e) {
        qCritical() << "InfoPanelManager: 设置控制通道根信息时发生异常:" << e.what();
    }
}

bool InfoPanelManager::hasBasicInfoWidget() const
{
    return basicInfoWidget_ != nullptr;
}

bool InfoPanelManager::hasDetailInfoPanel() const
{
    return detailInfoPanel_ != nullptr;
}

NodeInfo InfoPanelManager::getCurrentNodeInfo() const
{
    return currentNodeInfo_;
}

void InfoPanelManager::initializeInfoPanels()
{
    // 初始化信息面板的默认状态
    if (basicInfoWidget_) {
        // basicInfoWidget_->initialize();
    }

    if (detailInfoPanel_) {
        // detailInfoPanel_->initialize();
    }
}

void InfoPanelManager::connectInfoPanelSignals()
{
    // 连接BasicInfoWidget的信号 (如果有的话)
    if (basicInfoWidget_) {
        // connect(basicInfoWidget_, &BasicInfoWidget::infoChanged,
        //         this, [this]() { emit infoUpdated("BasicInfo"); });
    }

    // 连接DetailInfoPanel的信号 (如果有的话)
    if (detailInfoPanel_) {
        // connect(detailInfoPanel_, &DetailInfoPanel::infoChanged,
        //         this, [this]() { emit infoUpdated("DetailInfo"); });
    }
}

void InfoPanelManager::disconnectInfoPanelSignals()
{
    if (basicInfoWidget_) {
        disconnect(basicInfoWidget_, nullptr, this, nullptr);
    }
    if (detailInfoPanel_) {
        disconnect(detailInfoPanel_, nullptr, this, nullptr);
    }
} 