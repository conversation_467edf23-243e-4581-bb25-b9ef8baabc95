/**
 * @file TreeInteractionHandler.cpp
 * @brief 树形控件交互处理类实现
 * @details 实现树形控件节点的多级详细信息显示功能
 * <AUTHOR> Assistant
 * @date 2025-01-23
 * @version 1.0.0
 */

#include "TreeInteractionHandler.h"
#include "MainWindow_Qt_Simple.h"
#include <QtWidgets/QApplication>
#include <QtWidgets/QDialog>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtCore/QDebug>
#include <QJsonDocument>
#include <QJsonObject>
#include <QDateTime>

TreeInteractionHandler::TreeInteractionHandler(QTreeWidget* treeWidget, DetailInfoPanel* detailPanel, CMyMainWindow* mainWindow, QObject* parent)
    : QObject(parent)
    , m_treeWidget(treeWidget)
    , m_detailPanel(detailPanel)
    , m_mainWindow(mainWindow)
    , m_tooltipTimer(new QTimer(this))
    , m_currentItem(nullptr)
    , m_persistentTooltip(nullptr)
    , m_tooltipLabel(nullptr)
    , m_currentHoverItem(nullptr)
{
    // 🆕 修改：试验资源提示信息显示时间与硬件资源保持一致
    // 设置工具提示计时器（保留原有功能作为备用，但不自动启动）
    m_tooltipTimer->setSingleShot(true);
    m_tooltipTimer->setInterval(5000); // 5秒后隐藏工具提示（参考硬件资源设置）
    connect(m_tooltipTimer, SIGNAL(timeout()), this, SLOT(hideTooltipDelayed()));
    
    // 创建持久提示窗口（与硬件资源使用相同的持久提示机制）
    createPersistentTooltip();
    
    // 初始化交互
    initializeInteraction();
}

TreeInteractionHandler::~TreeInteractionHandler() {
    // 清理持久提示窗口资源
    if (m_persistentTooltip) {
        m_persistentTooltip->deleteLater();
        m_persistentTooltip = nullptr;
    }
}

void TreeInteractionHandler::initializeInteraction() {
    // 安装事件过滤器
    m_treeWidget->installEventFilter(this);
    
    // 连接信号和槽
    connect(m_treeWidget, SIGNAL(itemEntered(QTreeWidgetItem*,int)), this, SLOT(onItemEntered(QTreeWidgetItem*,int)));
    connect(m_treeWidget, SIGNAL(itemClicked(QTreeWidgetItem*,int)), this, SLOT(onItemClicked(QTreeWidgetItem*,int)));
    connect(m_treeWidget, SIGNAL(customContextMenuRequested(QPoint)), this, SLOT(onContextMenuRequested(QPoint)));
    connect(m_treeWidget, SIGNAL(itemDoubleClicked(QTreeWidgetItem*,int)), this, SLOT(onItemDoubleClicked(QTreeWidgetItem*,int)));
    
    // 设置上下文菜单策略
    m_treeWidget->setContextMenuPolicy(Qt::CustomContextMenu);
    
    qDebug() << "TreeInteractionHandler: Interaction initialized successfully";
}

void TreeInteractionHandler::setDetailPanel(DetailInfoPanel* detailPanel) {
    m_detailPanel = detailPanel;
}

void TreeInteractionHandler::updateNodeDetailInfo(QTreeWidgetItem* item) {
    if (!item || !m_detailPanel) return;
    
    // 使用新的详细信息面板显示信息
    NodeInfo nodeInfo;
    nodeInfo.nodeName = item->text(0);
    nodeInfo.nodeType = getNodeType(item);
    nodeInfo.status = NodeStatus::Online; // 使用枚举值
    
    // 根据节点类型设置基本信息属性
    if (nodeInfo.nodeType == "控制通道组") {
        nodeInfo.setBasicProperty("描述", "控制通道组节点，包含多个控制通道");
        nodeInfo.setBasicProperty("父节点", item->parent() ? item->parent()->text(0) : "无");
    } else if (nodeInfo.nodeType == "控制通道") {
        nodeInfo.setBasicProperty("描述", "控制通道节点，包含载荷、位置和控制传感器");
        nodeInfo.setBasicProperty("父节点", item->parent() ? item->parent()->text(0) : "无");
    } else if (nodeInfo.nodeType == "载荷传感器") {
        nodeInfo.setBasicProperty("描述", "载荷传感器节点，用于测量力或载荷");
        nodeInfo.setBasicProperty("父节点", item->parent() ? item->parent()->text(0) : "无");
    } else if (nodeInfo.nodeType == "位置传感器") {
        nodeInfo.setBasicProperty("描述", "位置传感器节点，用于测量位移或位置");
        nodeInfo.setBasicProperty("父节点", item->parent() ? item->parent()->text(0) : "无");
    } else if (nodeInfo.nodeType == "控制作动器") {
        nodeInfo.setBasicProperty("描述", "控制作动器节点，用于执行控制动作");
        nodeInfo.setBasicProperty("父节点", item->parent() ? item->parent()->text(0) : "无");
    } else {
        nodeInfo.setBasicProperty("描述", "未知节点类型");
        nodeInfo.setBasicProperty("父节点", item->parent() ? item->parent()->text(0) : "无");
    }
    
    // 更新详细信息面板
    m_detailPanel->setNodeInfo(nodeInfo);
}

void TreeInteractionHandler::onItemEntered(QTreeWidgetItem* item, int column) {
    Q_UNUSED(column)
    
    if (!item) {
        hidePersistentTooltip();
        return;
    }
    
    // 记录当前悬停的节点
    m_currentHoverItem = item;
    
    // 🆕 修改：试验资源提示信息显示时间与硬件资源保持一致
    // Layer 1: 基础信息工具提示 - 使用持久提示窗口（与硬件资源相同）
    QString tooltipInfo = generateLayer1Info(item);
    
    // 获取当前鼠标位置
    QPoint globalPos = QCursor::pos();
    QPoint localPos = m_treeWidget->mapFromGlobal(globalPos);
    
    // 显示持久提示信息（与硬件资源使用相同的持久提示机制）
    showPersistentTooltip(tooltipInfo, globalPos);
    
    // 🆕 新增：记录提示信息显示，便于调试
    qDebug() << "TreeInteractionHandler: Showing persistent Layer 1 info for" << item->text(0);
    qDebug() << "TreeInteractionHandler: Using persistent tooltip (same as hardware resources)";
}

void TreeInteractionHandler::onItemClicked(QTreeWidgetItem* item, int column) {
    Q_UNUSED(column)
    
    if (!item) {
        qDebug() << "❌ TreeInteractionHandler::onItemClicked: item is null!";
        return;
    }
    
    m_currentItem = item;
    
    // 🆕 新增：检查是否为控制通道根节点
    QString nodeType = getNodeType(item);
    QString nodeName = getNodeName(item);
    
    qDebug() << "=== TreeInteractionHandler::onItemClicked: 节点点击事件 ===";
    qDebug() << "TreeInteractionHandler::onItemClicked: 节点名称:" << nodeName;
    qDebug() << "TreeInteractionHandler::onItemClicked: 节点类型:" << nodeType;
    qDebug() << "TreeInteractionHandler::onItemClicked: 父节点:" << (item->parent() ? item->parent()->text(0) : "无");
    qDebug() << "TreeInteractionHandler::onItemClicked: 子节点数量:" << item->childCount();
    
    // 🆕 新增：打印当前节点的详细信息
    qDebug() << "TreeInteractionHandler::onItemClicked: 当前节点详细信息:";
    qDebug() << "  - 文本(列0):" << item->text(0);
    qDebug() << "  - 文本(列1):" << item->text(1);
    qDebug() << "  - 文本(列2):" << item->text(2);
    qDebug() << "  - 文本(列3):" << item->text(3);
    qDebug() << "  - 文本(列4):" << item->text(4);
    qDebug() << "  - 文本(列5):" << item->text(5);
    
    // 🆕 新增：打印子节点信息
    if (item->childCount() > 0) {
        qDebug() << "TreeInteractionHandler::onItemClicked: 子节点信息:";
        for (int i = 0; i < item->childCount(); ++i) {
            QTreeWidgetItem* child = item->child(i);
            qDebug() << "  - 子节点[" << i << "]:" << child->text(0);
            qDebug() << "    * 类型:" << getNodeType(child);
            qDebug() << "    * 列1:" << child->text(1);
            qDebug() << "    * 列2:" << child->text(2);
            qDebug() << "    * 列3:" << child->text(3);
            qDebug() << "    * 列4:" << child->text(4);
            qDebug() << "    * 列5:" << child->text(5);
            qDebug() << "    * 子节点数量:" << child->childCount();
            
            // 打印孙节点信息
            if (child->childCount() > 0) {
                qDebug() << "    * 孙节点信息:";
                for (int j = 0; j < child->childCount(); ++j) {
                    QTreeWidgetItem* grandChild = child->child(j);
                    qDebug() << "      - 孙节点[" << j << "]:" << grandChild->text(0);
                    qDebug() << "        > 类型:" << getNodeType(grandChild);
                    qDebug() << "        > 列1:" << grandChild->text(1);
                }
            }
        }
    }
    
    if (nodeName == "控制通道" && item->parent() && item->parent()->text(0) == "实验") {
        qDebug() << "🎯 TreeInteractionHandler::onItemClicked: 检测到控制通道根节点！";
        
        // 控制通道根节点：获取所有子通道并调用BasicInfoWidget
        QList<QTreeWidgetItem*> childChannels;
        for (int i = 0; i < item->childCount(); ++i) {
            QTreeWidgetItem* child = item->child(i);
            // 🆕 修复：子通道识别逻辑，支持多种命名方式
            QString childName = child->text(0);
            QString childType = getNodeType(child);
            
            // 检查是否为控制通道子节点：
            // 1. 名称以"CH"开头（如CH1、CH2）
            // 2. 或者节点类型为"控制通道"
            // 3. 或者有4个子节点（载荷1、载荷2、位置、控制）
            bool isControlChannel = childName.startsWith("CH") || 
                                  childType == "控制通道" || 
                                  child->childCount() == 4;
            
            if (isControlChannel) {
                childChannels.append(child);
                qDebug() << "TreeInteractionHandler::onItemClicked: 找到子通道:" << childName << "，类型:" << childType << "，子节点数:" << child->childCount();
                
                // 打印子通道的子节点信息
                qDebug() << "TreeInteractionHandler::onItemClicked: 子通道" << childName << "有" << child->childCount() << "个子节点";
                for (int j = 0; j < child->childCount(); ++j) {
                    QTreeWidgetItem* subChild = child->child(j);
                    qDebug() << "TreeInteractionHandler::onItemClicked:   - 子节点" << j << ":" << subChild->text(0);
                    qDebug() << "TreeInteractionHandler::onItemClicked:     * 类型:" << getNodeType(subChild);
                    qDebug() << "TreeInteractionHandler::onItemClicked:     * 列1:" << subChild->text(1);
                }
            } else {
                qDebug() << "TreeInteractionHandler::onItemClicked: 跳过非控制通道子节点:" << childName << "，类型:" << childType << "，子节点数:" << child->childCount();
            }
        }
        
        qDebug() << "TreeInteractionHandler::onItemClicked: 子通道总数:" << childChannels.size();
        
        // 调用主窗口的详细信息显示方法
        if (m_mainWindow) {
            qDebug() << "TreeInteractionHandler::onItemClicked: 调用主窗口显示控制通道组信息";
            // 对于控制通道根节点，显示控制通道组汇总信息
            m_mainWindow->ShowControlChannelGroupInfo();
        } else {
            qDebug() << "❌ TreeInteractionHandler::onItemClicked: 主窗口指针为空！";
        }
        
    } else if ((nodeName.startsWith("CH") || (item->parent() && item->parent()->text(0) == "控制通道" && item->childCount() == 4)) && 
               item->parent() && item->parent()->text(0) == "控制通道") {
        qDebug() << "🎛️ TreeInteractionHandler::onItemClicked: 检测到控制通道子节点:" << nodeName;
        
        // 控制通道子节点（CH1、CH2、111、2222等）：显示通道详细信息
        qDebug() << "TreeInteractionHandler::onItemClicked: 通道详细信息:";
        qDebug() << "  - 通道名称:" << nodeName;
        qDebug() << "  - 硬件关联:" << item->text(1);
        qDebug() << "  - 下位机ID:" << item->text(2);
        qDebug() << "  - 站点ID:" << item->text(3);
        qDebug() << "  - 使能状态:" << item->text(4);
        qDebug() << "  - 控制作动器极性:" << item->text(5);
        qDebug() << "  - 子节点数量:" << item->childCount();
        
        // 🆕 新增：打印子节点信息
        if (item->childCount() > 0) {
            qDebug() << "TreeInteractionHandler::onItemClicked: 子节点信息:";
            for (int i = 0; i < item->childCount(); ++i) {
                QTreeWidgetItem* subChild = item->child(i);
                qDebug() << "  - 子节点[" << i << "]:" << subChild->text(0);
                qDebug() << "    * 类型:" << getNodeType(subChild);
                qDebug() << "    * 列1:" << subChild->text(1);
            }
        }
        
        // 调用主窗口的详细信息显示方法
        if (m_mainWindow) {
            qDebug() << "TreeInteractionHandler::onItemClicked: 调用主窗口显示控制通道信息";
            // 对于控制通道子节点，传递通道名称
            m_mainWindow->ShowControlChannelDetailInfo(nodeName);
        } else {
            qDebug() << "❌ TreeInteractionHandler::onItemClicked: 主窗口指针为空！";
        }
        
    } else {
        qDebug() << "📋 TreeInteractionHandler::onItemClicked: 其他类型节点，使用默认处理";
        
        // 🆕 修复：检查是否为控制通道的子节点（如载荷1、载荷2、位置、控制等）
        if (item->parent() && (item->parent()->text(0).startsWith("CH") || 
                               (item->parent()->parent() && item->parent()->parent()->text(0) == "控制通道" && item->parent()->childCount() == 4))) {
            QString parentChannelName = item->parent()->text(0);
            qDebug() << "🎯 TreeInteractionHandler::onItemClicked: 检测到控制通道子节点:" << nodeName << "，父通道:" << parentChannelName;
            
            // 对于控制通道的子节点，显示父通道的详细信息
            if (m_mainWindow) {
                qDebug() << "TreeInteractionHandler::onItemClicked: 调用主窗口显示父通道信息";
                m_mainWindow->ShowControlChannelDetailInfo(parentChannelName);
            } else {
                qDebug() << "❌ TreeInteractionHandler::onItemClicked: 主窗口指针为空！";
            }
        } else {
            // 其他节点：使用默认的详细信息显示
            if (m_mainWindow) {
                qDebug() << "TreeInteractionHandler::onItemClicked: 调用主窗口显示默认节点信息";
                // 🆕 修复：不再调用showNormal()，避免窗口大小变化
                // 而是调用详细信息显示方法
                if (nodeName.contains("传感器") || nodeName.contains("作动器")) {
                    // 对于传感器和作动器节点，显示基本信息
                    m_mainWindow->ShowControlChannelDetailInfo(nodeName);
                } else {
                    // 其他节点，暂时不显示详细信息，避免窗口状态变化
                    qDebug() << "TreeInteractionHandler::onItemClicked: 跳过未知节点类型，避免窗口状态变化";
                }
            } else {
                qDebug() << "❌ TreeInteractionHandler::onItemClicked: 主窗口指针为空！";
            }
        }
    }
    
    qDebug() << "=== TreeInteractionHandler::onItemClicked: 节点点击事件处理完成 ===";
}

void TreeInteractionHandler::onContextMenuRequested(const QPoint& pos) {
    QTreeWidgetItem* item = m_treeWidget->itemAt(pos);
    if (!item) return;
    
    showContextMenu(item, pos);
    
    qDebug() << "TreeInteractionHandler: Context menu requested for" << item->text(0);
}

void TreeInteractionHandler::onItemDoubleClicked(QTreeWidgetItem* item, int column) {
    if (!item || !m_mainWindow) return;
    
    // 保持原有的双击功能不变
    // 具体实现保留之前已有的双击处理逻辑
    
    // 调用主窗口的原有双击处理方法
    m_mainWindow->OnTestConfigTreeItemDoubleClicked(item, column);
    
    qDebug() << "TreeInteractionHandler: Double-click preserved for" << item->text(0);
}

void TreeInteractionHandler::hideTooltipDelayed() {
    QToolTip::hideText();
}

QString TreeInteractionHandler::generateLayer1Info(QTreeWidgetItem* item) {
    if (!item) return QString();
    
    QString nodeType = getNodeType(item);
    QString nodeName = getNodeName(item);
    QString associationInfo = getAssociationInfo(item);
    
    // 根据节点类型选择不同的图标
    QString icon = "📋";
    if (nodeType.contains("控制通道")) {
        icon = "🎛️";
    } else if (nodeType.contains("传感器")) {
        icon = "📊";
    } else if (nodeType.contains("作动器")) {
        icon = "⚙️";
    } else if (nodeType.contains("硬件")) {
        icon = "🖥️";
    }
    
    QString tooltip = QString("<b>%1 %2</b><br/>").arg(icon, nodeName);
    tooltip += QString("<span style='color: #7f8c8d;'>类型:</span> <span style='color: #2980b9;'>%1</span><br/>").arg(nodeType);
    
    // 关联信息
    if (!associationInfo.isEmpty()) {
        tooltip += QString("<span style='color: #7f8c8d;'>关联:</span> <span style='color: #27ae60;'>%1</span><br/>").arg(associationInfo);
    } else {
        tooltip += QString("<span style='color: #7f8c8d;'>关联:</span> <span style='color: #e74c3c;'>⚠️ 未配置关联</span><br/>");
    }
    
    // 技术参数信息
    if (item->columnCount() > 2) {
        QString lcId = item->text(2);
        QString stationId = item->text(3);
        QString enabled = item->text(4);
        QString polarity = item->text(5);
        
        if (!lcId.isEmpty()) {
            tooltip += QString("<span style='color: #7f8c8d;'>下位机ID:</span> <span style='color: #8e44ad;'>%1</span><br/>").arg(lcId);
        }
        if (!stationId.isEmpty()) {
            tooltip += QString("<span style='color: #7f8c8d;'>站点ID:</span> <span style='color: #8e44ad;'>%1</span><br/>").arg(stationId);
        }
        if (!enabled.isEmpty()) {
            QString statusColor = enabled.contains("启用") || enabled == "1" ? "#27ae60" : "#e74c3c";
            QString statusIcon = enabled.contains("启用") || enabled == "1" ? "✅" : "❌";
            tooltip += QString("<span style='color: #7f8c8d;'>状态:</span> <span style='color: %2;'>%3 %1</span><br/>").arg(enabled, statusColor, statusIcon);
        }
        if (!polarity.isEmpty()) {
            tooltip += QString("<span style='color: #7f8c8d;'>极性:</span> <span style='color: #34495e;'>%1</span><br/>").arg(polarity);
        }
    }
    
    // 子节点信息
    if (item->childCount() > 0) {
        tooltip += QString("<hr style='border: none; border-top: 1px solid #bdc3c7; margin: 5px 0;'/>");
        tooltip += QString("<span style='color: #7f8c8d;'>子节点:</span> <span style='color: #3498db;'>📁 %1 个</span><br/>").arg(item->childCount());
        
        // 显示前3个子节点名称
        QStringList childNames;
        for (int i = 0; i < qMin(3, item->childCount()); ++i) {
            childNames << item->child(i)->text(0);
        }
        if (item->childCount() > 3) {
            childNames << "...";
        }
        tooltip += QString("<span style='color: #95a5a6; font-size: 11px;'>包含: %1</span>").arg(childNames.join(", "));
    }
    
    return tooltip;
}

QString TreeInteractionHandler::generateLayer2Info(QTreeWidgetItem* item) {
    if (!item) return QString();
    
    // 🚫 已移除：HTML框架依赖，直接使用传统方法生成详细信息
    QString nodeType = getNodeType(item);
    
    if (nodeType == "试验节点" || nodeType == "控制通道") {
        return generateControlChannelInfo(item);
    } else if (nodeType.contains("传感器") || nodeType.contains("载荷") || nodeType.contains("位置")) {
        return generateSensorNodeInfo(item);
    } else if (nodeType.contains("作动器") || nodeType.contains("控制")) {
        return generateActuatorNodeInfo(item);
    } else if (nodeType.contains("硬件节点")) {
        return generateHardwareNodeInfo(item);
    }
    
    // 默认的详细信息生成，包含所有可能的信息
    QString nodeName = getNodeName(item);
    QString associationInfo = getAssociationInfo(item);
    
    QString content = QString(
        "<!DOCTYPE html>"
        "<html>"
        "<head>"
        "<meta charset='UTF-8'>"
        "<style>"
        "body { font-family: 'Microsoft YaHei UI', sans-serif; margin: 20px; background-color: #f8f9fa; }"
        "h3 { color: #2c3e50; margin-bottom: 15px; text-align: center; border-bottom: 2px solid #3498db; padding-bottom: 10px; }"
        "table { width: 100%; border-collapse: collapse; margin: 10px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }"
        "th { background-color: #3498db; color: white; padding: 12px 8px; text-align: left; font-weight: bold; border: 1px solid #2980b9; }"
        "td { padding: 10px 8px; border: 1px solid #ddd; background-color: white; }"
        "tr:nth-child(even) td { background-color: #f9f9f9; }"
        ".section-header { background-color: #ecf0f1; font-weight: bold; text-align: center; color: #2c3e50; }"
        ".status-ok { color: #27ae60; font-weight: bold; }"
        ".status-error { color: #e74c3c; font-weight: bold; }"
        ".status-warning { color: #f39c12; font-weight: bold; }"
        ".path-info { background-color: #e8f4fd; padding: 4px 8px; border-radius: 4px; font-family: monospace; }"
        ".config-table { margin: 15px 0; }"
        ".config-table th { background-color: #e67e22; border-color: #d35400; }"
        ".extended-table { margin: 15px 0; }"
        ".extended-table th { background-color: #9b59b6; border-color: #8e44ad; }"
        "</style>"
        "</head>"
        "<body>"
        "<h3>📋 %1</h3>"
        "<table>"
    ).arg(nodeName);
    
    // 基本信息部分
    content += QString(
        "<tr class='section-header'><td colspan='2'>📊 基本信息</td></tr>"
        "<tr><td style='font-weight: bold; width: 30%;'>节点名称:</td>"
        "<td>%1</td></tr>"
        "<tr><td style='font-weight: bold;'>节点类型:</td>"
        "<td>%2</td></tr>"
    ).arg(nodeName).arg(nodeType);
    
    // 节点路径信息
    QStringList pathList;
    QTreeWidgetItem* currentItem = item;
    while (currentItem) {
        pathList.prepend(currentItem->text(0));
        currentItem = currentItem->parent();
    }
    content += QString(
        "<tr><td style='font-weight: bold;'>节点路径:</td>"
        "<td><span class='path-info'>%1</span></td></tr>"
    ).arg(pathList.join(" → "));
    
    // 层级信息
    int depth = pathList.size() - 1;
    content += QString(
        "<tr><td style='font-weight: bold;'>层级深度:</td>"
        "<td>第 %1 层</td></tr>"
    ).arg(depth);
    
    // 关联信息
    if (!associationInfo.isEmpty()) {
        content += QString(
            "<tr><td style='font-weight: bold;'>关联信息:</td>"
            "<td class='status-ok'>✅ %1</td></tr>"
        ).arg(associationInfo);
    } else {
        content += QString(
            "<tr><td style='font-weight: bold;'>关联信息:</td>"
            "<td class='status-warning'>⚠️ 未配置关联</td></tr>"
        );
    }
    
    // 分组横向表格布局
    if (item->columnCount() > 2) {
        QStringList columnHeaders = {"节点名称", "关联信息", "下位机ID", "站点ID", "启用状态", "极性设置", "扩展1", "扩展2", "扩展3", "扩展4"};
        
        // 收集有效的列数据
        QList<QPair<QString, QString>> basicConfig; // 基础配置组
        QList<QPair<QString, QString>> extendedParams; // 扩展参数组
        
        for (int col = 2; col < item->columnCount(); ++col) {
            QString colText = item->text(col);
            if (!colText.isEmpty()) {
                QString colHeader = col < columnHeaders.size() ? columnHeaders[col] : QString("列%1").arg(col);
                
                // 状态列特殊处理
                if (col == 4) {
                    QString statusIcon = colText.contains("启用") || colText == "1" ? "✅" : "❌";
                    colText = QString("%1 %2").arg(statusIcon).arg(colText);
                }
                
                // 分组：基础配置(下位机ID, 站点ID, 启用状态, 极性设置)和扩展参数
                if (col >= 2 && col <= 5) {
                    basicConfig.append(qMakePair(colHeader, colText));
                } else {
                    extendedParams.append(qMakePair(colHeader, colText));
                }
            }
        }
        
        // 生成基础配置横向表格
        if (!basicConfig.isEmpty()) {
            content += QString(
                "<tr class='section-header'><td colspan='2'>🔧 基础配置</td></tr>"
                "<tr><td colspan='2' style='padding: 0;'>"
                "<table class='config-table'>"
                "<thead><tr>"
            );
            
            // 基础配置表头
            for (const auto& config : basicConfig) {
                content += QString("<th>%1</th>").arg(config.first);
            }
            content += "</tr></thead><tbody><tr>";
            
            // 基础配置数据行
            for (const auto& config : basicConfig) {
                QString cellStyle = "text-align: center;";
                if (config.first == "启用状态") {
                    cellStyle += " background-color: #e8f5e8; font-weight: bold;";
                }
                content += QString("<td style='%1'>%2</td>").arg(cellStyle).arg(config.second);
            }
            content += "</tr></tbody></table></td></tr>";
        }
        
        // 生成扩展参数横向表格
        if (!extendedParams.isEmpty()) {
            content += QString(
                "<tr class='section-header'><td colspan='2'>⚙️ 扩展参数</td></tr>"
                "<tr><td colspan='2' style='padding: 0;'>"
                "<table class='extended-table'>"
                "<thead><tr>"
            );
            
            // 扩展参数表头
            for (const auto& param : extendedParams) {
                content += QString("<th>%1</th>").arg(param.first);
            }
            content += "</tr></thead><tbody><tr>";
            
            // 扩展参数数据行
            for (const auto& param : extendedParams) {
                content += QString("<td style='text-align: center;'>%1</td>").arg(param.second);
            }
            content += "</tr></tbody></table></td></tr>";
        }
    }
    
    // 统计信息
    content += QString(
        "<tr class='section-header'><td colspan='2'>📈 统计信息</td></tr>"
        "<tr><td style='font-weight: bold;'>直接子节点:</td>"
        "<td>%1 个</td></tr>"
    ).arg(item->childCount());
    
    // 计算总子节点数量（递归）
    int totalChildren = 0;
    std::function<void(QTreeWidgetItem*)> countChildren = [&](QTreeWidgetItem* currentItem) {
        for (int i = 0; i < currentItem->childCount(); ++i) {
            totalChildren++;
            countChildren(currentItem->child(i));
        }
    };
    countChildren(item);
    
    content += QString(
        "<tr><td style='font-weight: bold;'>总子节点数:</td>"
        "<td>%1 个（包含所有层级）</td></tr>"
    ).arg(totalChildren);
    
    // 兄弟节点信息
    if (item->parent()) {
        content += QString(
            "<tr><td style='font-weight: bold;'>兄弟节点数:</td>"
            "<td>%1 个</td></tr>"
        ).arg(item->parent()->childCount() - 1);
    }
    
    // 如果有子节点，显示完整的子节点详细信息
    if (item->childCount() > 0) {
        content += generateChildNodesInfo(item, "所有子节点详细信息");
    }
    
    content += "</table>";
    
    // 添加操作提示
    content += QString(
        "<div style='margin-top: 20px; padding: 15px; background-color: #e8f4fd; border-radius: 8px; border-left: 4px solid #3498db;'>"
        "<h4 style='margin-top: 0; color: #2980b9;'>💡 操作提示</h4>"
        "<ul style='margin: 5px 0; padding-left: 20px;'>"
        "<li>鼠标悬停节点可查看基础信息</li>"
        "<li>右键点击节点可打开编辑菜单</li>"
        "<li>双击节点可进行快速操作</li>"
        "</ul>"
        "</div>"
    );
    
    content += "</body></html>";
    
    return content;
}

QString TreeInteractionHandler::generateControlChannelInfo(QTreeWidgetItem* item) {
    if (!item) return QString();
    
    QString nodeName = getNodeName(item);
    QString associationInfo = getAssociationInfo(item);
    
    QString content = QString(
        "<!DOCTYPE html>"
        "<html>"
        "<head>"
        "<meta charset='UTF-8'>"
        "<style>"
        "body { font-family: 'Microsoft YaHei UI', sans-serif; margin: 20px; background-color: #f8f9fa; }"
        "h3 { color: #2980b9; margin-bottom: 15px; text-align: center; border-bottom: 2px solid #3498db; padding-bottom: 10px; }"
        "table { width: 100%; border-collapse: collapse; margin: 10px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }"
        "th { background-color: #3498db; color: white; padding: 12px 8px; text-align: left; font-weight: bold; border: 1px solid #2980b9; }"
        "td { padding: 10px 8px; border: 1px solid #ddd; background-color: white; }"
        "tr:nth-child(even) td { background-color: #f9f9f9; }"
        ".section-header { background-color: #ecf0f1; font-weight: bold; text-align: center; color: #2c3e50; }"
        ".status-ok { color: #27ae60; font-weight: bold; }"
        ".status-error { color: #e74c3c; font-weight: bold; }"
        ".status-warning { color: #f39c12; font-weight: bold; }"
        ".status-info { color: #3498db; font-weight: bold; }"
        ".status-purple { color: #9b59b6; font-weight: bold; }"
        ".path-info { background-color: #e8f4fd; padding: 4px 8px; border-radius: 4px; font-family: monospace; }"
        ".compact-table { margin: 15px 0; }"
        ".compact-table th { background-color: #e67e22; border-color: #d35400; }"
        ".status-table { margin: 15px 0; }"
        ".status-table th { background-color: #9b59b6; border-color: #8e44ad; }"
        "</style>"
        "</head>"
        "<body>"
        "<h3>🎛️ 控制通道 - %1</h3>"
        "<table>"
        "<tr class='section-header'><td colspan='2'>📊 基本信息</td></tr>"
        "<tr><td style='font-weight: bold; width: 30%;'>通道名称</td><td>%1</td></tr>"
        "<tr><td style='font-weight: bold;'>通道类型</td><td>试验控制通道</td></tr>"
    ).arg(nodeName).arg(nodeName);
    
    // 节点路径
    QStringList pathList;
    QTreeWidgetItem* currentItem = item;
    while (currentItem) {
        pathList.prepend(currentItem->text(0));
        currentItem = currentItem->parent();
    }
    content += QString(
        "<tr><td style='font-weight: bold;'>通道路径</td><td><span class='path-info'>%1</span></td></tr>"
    ).arg(pathList.join(" → "));
    
    // 关联信息
    if (!associationInfo.isEmpty()) {
        content += QString(
            "<tr><td style='font-weight: bold;'>硬件关联</td><td><span class='status-ok'>✅ %1</span></td></tr>"
        ).arg(associationInfo);
    } else {
        content += QString(
            "<tr><td style='font-weight: bold;'>硬件关联</td><td><span class='status-error'>⚠️ 未配置硬件关联</span></td></tr>"
        );
    }
    
    content += "</table>";
    
    // 技术参数表格
    if (item->columnCount() > 2) {
        content += QString(
            "<table class='compact-table'>"
            "<tr class='section-header'><td colspan='2'>🔧 技术参数</td></tr>"
            "<tr><th>配置项</th><th>参数值</th></tr>"
        );
        
        QString lcId = item->text(2);
        QString stationId = item->text(3);
        QString enabled = item->text(4);
        QString polarity = item->text(5);
        
        if (!lcId.isEmpty()) {
            content += QString(
                "<tr><td>下位机ID</td><td><span class='status-purple'>%1</span></td></tr>"
            ).arg(lcId);
        }
        
        if (!stationId.isEmpty()) {
            content += QString(
                "<tr><td>站点ID</td><td><span class='status-purple'>%1</span></td></tr>"
            ).arg(stationId);
        }
        
        if (!enabled.isEmpty()) {
            QString statusClass = enabled.contains("启用") || enabled == "1" ? "status-ok" : "status-error";
            QString statusIcon = enabled.contains("启用") || enabled == "1" ? "✅" : "❌";
            QString statusText = enabled.contains("启用") || enabled == "1" ? "通道已启用" : "通道已禁用";
            content += QString(
                "<tr><td>启用状态</td><td><span class='%3'>%4 %1 (%2)</span></td></tr>"
            ).arg(enabled).arg(statusText).arg(statusClass).arg(statusIcon);
        }
        
        if (!polarity.isEmpty()) {
            content += QString(
                "<tr><td>极性设置</td><td><span class='status-info'>%1</span></td></tr>"
            ).arg(polarity);
        }
        
        // 显示其他配置列
        QStringList additionalHeaders = {"最大量程", "精度", "校准系数", "偏移量", "滤波参数"};
        for (int col = 6; col < item->columnCount() && col < 11; ++col) {
            QString colText = item->text(col);
            if (!colText.isEmpty()) {
                QString header = col-6 < additionalHeaders.size() ? additionalHeaders[col-6] : QString("参数%1").arg(col-5);
                content += QString(
                    "<tr><td>%1</td><td><span class='status-purple'>%2</span></td></tr>"
                ).arg(header).arg(colText);
            }
        }
        
        content += "</table>";
    }
    
    // 通道状态评估表格
    content += QString(
        "<table class='status-table'>"
        "<tr class='section-header'><td colspan='2'>📈 状态评估</td></tr>"
        "<tr><th>状态项</th><th>评估结果</th></tr>"
    );
    
    QString channelStatus = "正常运行";
    QString statusClass = "status-ok";
    QString statusIcon = "✅";
    
    if (associationInfo.isEmpty()) {
        channelStatus = "未关联硬件";
        statusClass = "status-error";
        statusIcon = "⚠️";
    } else {
        QString enabled = item->text(4);
        if (!enabled.isEmpty() && !enabled.contains("启用") && enabled != "1") {
            channelStatus = "通道已禁用";
            statusClass = "status-warning";
            statusIcon = "⚠️";
        }
    }
    
    content += QString(
        "<tr><td>通道状态</td><td><span class='%2'>%3 %1</span></td></tr>"
        "<tr><td>关联状态</td><td><span class='%5'>%6 %4</span></td></tr>"
    ).arg(channelStatus).arg(statusClass).arg(statusIcon)
     .arg(associationInfo.isEmpty() ? "未关联" : "已关联")
     .arg(associationInfo.isEmpty() ? "status-error" : "status-ok")
     .arg(associationInfo.isEmpty() ? "❌" : "✅");
    
    content += "</table>";
    
    // 添加操作提示
    content += QString(
        "<div style='margin-top: 20px; padding: 15px; background-color: #e8f4fd; border-radius: 8px; border-left: 4px solid #3498db;'>"
        "<h4 style='margin-top: 0; color: #2980b9;'>💡 控制通道操作</h4>"
        "<ul style='margin: 5px 0; padding-left: 20px;'>"
        "<li>右键点击可编辑通道配置</li>"
        "<li>双击可快速启用/禁用通道</li>"
        "<li>拖拽可调整通道顺序</li>"
        "</ul>"
        "</div>"
    );
    
    content += "</body></html>";
    
    return content;
}

QString TreeInteractionHandler::generateSensorNodeInfo(QTreeWidgetItem* item) {
    if (!item) return QString();
    
    QString nodeName = getNodeName(item);
    QString associationInfo = getAssociationInfo(item);
    
    QString content = QString(
        "<!DOCTYPE html>"
        "<html>"
        "<head>"
        "<meta charset='UTF-8'>"
        "<style>"
        "body { font-family: 'Microsoft YaHei UI', sans-serif; margin: 20px; background-color: #f8f9fa; }"
        "h3 { color: #e67e22; margin-bottom: 15px; text-align: center; border-bottom: 2px solid #e67e22; padding-bottom: 10px; }"
        "table { width: 100%; border-collapse: collapse; margin: 10px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }"
        "th { background-color: #e67e22; color: white; padding: 12px 8px; text-align: left; font-weight: bold; border: 1px solid #d35400; }"
        "td { padding: 10px 8px; border: 1px solid #ddd; background-color: white; }"
        "tr:nth-child(even) td { background-color: #f9f9f9; }"
        ".section-header { background-color: #ecf0f1; font-weight: bold; text-align: center; color: #2c3e50; }"
        ".status-ok { color: #27ae60; font-weight: bold; }"
        ".status-error { color: #e74c3c; font-weight: bold; }"
        ".status-warning { color: #f39c12; font-weight: bold; }"
        ".status-info { color: #3498db; font-weight: bold; }"
        ".status-purple { color: #9b59b6; font-weight: bold; }"
        ".path-info { background-color: #e8f4fd; padding: 4px 8px; border-radius: 4px; font-family: monospace; }"
        ".compact-table { margin: 15px 0; }"
        ".compact-table th { background-color: #e67e22; border-color: #d35400; }"
        ".spec-table { margin: 15px 0; }"
        ".spec-table th { background-color: #9b59b6; border-color: #8e44ad; }"
        ".status-table { margin: 15px 0; }"
        ".status-table th { background-color: #27ae60; border-color: #229954; }"
        "</style>"
        "</head>"
        "<body>"
        "<h3>📊 传感器节点 - %1</h3>"
        "<table>"
        "<tr class='section-header'><td colspan='2'>📋 基本信息</td></tr>"
        "<tr><td style='font-weight: bold; width: 30%;'>传感器名称</td><td>%1</td></tr>"
    ).arg(nodeName).arg(nodeName);
    
    // 节点路径
    QStringList pathList;
    QTreeWidgetItem* currentItem = item;
    while (currentItem) {
        pathList.prepend(currentItem->text(0));
        currentItem = currentItem->parent();
    }
    content += QString(
        "<tr><td style='font-weight: bold;'>节点路径</td><td><span class='path-info'>%1</span></td></tr>"
    ).arg(pathList.join(" → "));
    
    // 设备关联
    if (!associationInfo.isEmpty()) {
        content += QString(
            "<tr><td style='font-weight: bold;'>设备关联</td><td><span class='status-ok'>✅ %1</span></td></tr>"
        ).arg(associationInfo);
    } else {
        content += QString(
            "<tr><td style='font-weight: bold;'>设备关联</td><td><span class='status-error'>⚠️ 未配置设备关联</span></td></tr>"
        );
    }
    
    content += "</table>";
    
    // 传感器技术规格表格
    content += QString(
        "<table class='spec-table'>"
        "<tr class='section-header'><td colspan='2'>🔧 技术规格</td></tr>"
        "<tr><th>技术规格</th><th>规格参数</th></tr>"
    );
    
    // 获取传感器类型
    QString sensorType = "通用传感器";
    if (nodeName.contains("载荷")) {
        sensorType = "载荷传感器";
    } else if (nodeName.contains("位置")) {
        sensorType = "位置传感器";
    } else if (nodeName.contains("温度")) {
        sensorType = "温度传感器";
    } else if (nodeName.contains("压力")) {
        sensorType = "压力传感器";
    }
    
    content += QString(
        "<tr><td>传感器类型</td><td><span class='status-info'>%1</span></td></tr>"
        "<tr><td>测量范围</td><td><span class='status-purple'>0-100%</span></td></tr>"
        "<tr><td>精度等级</td><td><span class='status-purple'>±0.1%</span></td></tr>"
        "<tr><td>响应时间</td><td><span class='status-purple'>&lt;10ms</span></td></tr>"
        "<tr><td>工作温度</td><td><span class='status-purple'>-20°C ~ +60°C</span></td></tr>"
    ).arg(sensorType);
    
    content += "</table>";
    
    // 配置参数表格
    if (item->columnCount() > 2) {
        content += QString(
            "<table class='compact-table'>"
            "<tr class='section-header'><td colspan='2'>⚙️ 配置参数</td></tr>"
            "<tr><th>配置项</th><th>参数值</th></tr>"
        );
        
        QStringList configHeaders = {"下位机ID", "站点ID", "启用状态", "极性设置", "校准系数", "偏移量", "滤波参数"};
        
        for (int col = 2; col < item->columnCount() && col < 9; ++col) {
            QString colText = item->text(col);
            if (!colText.isEmpty()) {
                QString header = col-2 < configHeaders.size() ? configHeaders[col-2] : QString("参数%1").arg(col-1);
                
                // 状态列特殊处理
                if (col == 4) {
                    QString statusIcon = colText.contains("启用") || colText == "1" ? "✅" : "❌";
                    QString statusClass = colText.contains("启用") || colText == "1" ? "status-ok" : "status-error";
                    colText = QString("<span class='%1'>%2 %3</span>").arg(statusClass).arg(statusIcon).arg(colText);
                }
                
                content += QString(
                    "<tr><td>%1</td><td>%2</td></tr>"
                ).arg(header).arg(colText);
            }
        }
        
        content += "</table>";
    }
    
    // 传感器状态评估表格
    content += QString(
        "<table class='status-table'>"
        "<tr class='section-header'><td colspan='2'>📈 状态评估</td></tr>"
        "<tr><th>状态项</th><th>评估结果</th></tr>"
    );
    
    QString sensorStatus = "正常运行";
    QString statusClass = "status-ok";
    QString statusIcon = "✅";
    
    if (associationInfo.isEmpty()) {
        sensorStatus = "未关联设备";
        statusClass = "status-error";
        statusIcon = "⚠️";
    }
    
    content += QString(
        "<tr><td>传感器状态</td><td><span class='%2'>%3 %1</span></td></tr>"
        "<tr><td>关联状态</td><td><span class='%5'>%6 %4</span></td></tr>"
        "<tr><td>数据质量</td><td><span class='status-ok'>✅ 数据正常</span></td></tr>"
        "<tr><td>通讯状态</td><td><span class='status-ok'>✅ 通讯正常</span></td></tr>"
    ).arg(sensorStatus).arg(statusClass).arg(statusIcon)
     .arg(associationInfo.isEmpty() ? "未关联" : "已关联")
     .arg(associationInfo.isEmpty() ? "status-error" : "status-ok")
     .arg(associationInfo.isEmpty() ? "❌" : "✅");
    
    content += "</table>";
    
    // 添加操作提示
    content += QString(
        "<div style='margin-top: 20px; padding: 15px; background-color: #fef9e7; border-radius: 8px; border-left: 4px solid #e67e22;'>"
        "<h4 style='margin-top: 0; color: #d35400;'>💡 传感器操作</h4>"
        "<ul style='margin: 5px 0; padding-left: 20px;'>"
        "<li>右键点击可编辑传感器配置</li>"
        "<li>双击可查看实时数据</li>"
        "<li>拖拽可调整传感器顺序</li>"
        "</ul>"
        "</div>"
    );
    
    content += "</body></html>";
    
    return content;
}

QString TreeInteractionHandler::generateActuatorNodeInfo(QTreeWidgetItem* item) {
    if (!item) return QString();
    
    QString nodeName = getNodeName(item);
    QString associationInfo = getAssociationInfo(item);
    
    QString content = QString(
        "<!DOCTYPE html>"
        "<html>"
        "<head>"
        "<meta charset='UTF-8'>"
        "<style>"
        "body { font-family: 'Microsoft YaHei UI', sans-serif; margin: 20px; background-color: #f8f9fa; }"
        "h3 { color: #8e44ad; margin-bottom: 15px; text-align: center; border-bottom: 2px solid #8e44ad; padding-bottom: 10px; }"
        "table { width: 100%; border-collapse: collapse; margin: 10px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }"
        "th { background-color: #8e44ad; color: white; padding: 12px 8px; text-align: left; font-weight: bold; border: 1px solid #7d3c98; }"
        "td { padding: 10px 8px; border: 1px solid #ddd; background-color: white; }"
        "tr:nth-child(even) td { background-color: #f9f9f9; }"
        ".section-header { background-color: #ecf0f1; font-weight: bold; text-align: center; color: #2c3e50; }"
        ".status-ok { color: #27ae60; font-weight: bold; }"
        ".status-error { color: #e74c3c; font-weight: bold; }"
        ".status-warning { color: #f39c12; font-weight: bold; }"
        ".status-info { color: #3498db; font-weight: bold; }"
        ".status-purple { color: #9b59b6; font-weight: bold; }"
        ".path-info { background-color: #e8f4fd; padding: 4px 8px; border-radius: 4px; font-family: monospace; }"
        ".compact-table { margin: 15px 0; }"
        ".compact-table th { background-color: #8e44ad; border-color: #7d3c98; }"
        ".spec-table { margin: 15px 0; }"
        ".spec-table th { background-color: #e67e22; border-color: #d35400; }"
        ".status-table { margin: 15px 0; }"
        ".status-table th { background-color: #27ae60; border-color: #229954; }"
        "</style>"
        "</head>"
        "<body>"
        "<h3>⚙️ 作动器节点 - %1</h3>"
        "<table>"
        "<tr class='section-header'><td colspan='2'>📋 基本信息</td></tr>"
        "<tr><td style='font-weight: bold; width: 30%;'>作动器名称</td><td>%1</td></tr>"
    ).arg(nodeName).arg(nodeName);
    
    // 节点路径
    QStringList pathList;
    QTreeWidgetItem* currentItem = item;
    while (currentItem) {
        pathList.prepend(currentItem->text(0));
        currentItem = currentItem->parent();
    }
    content += QString(
        "<tr><td style='font-weight: bold;'>节点路径</td><td><span class='path-info'>%1</span></td></tr>"
    ).arg(pathList.join(" → "));
    
    // 设备关联
    if (!associationInfo.isEmpty()) {
        content += QString(
            "<tr><td style='font-weight: bold;'>设备关联</td><td><span class='status-ok'>✅ %1</span></td></tr>"
        ).arg(associationInfo);
    } else {
        content += QString(
            "<tr><td style='font-weight: bold;'>设备关联</td><td><span class='status-error'>⚠️ 未配置设备关联</span></td></tr>"
        );
    }
    
    content += "</table>";
    
    // 作动器技术规格表格
    content += QString(
        "<table class='spec-table'>"
        "<tr class='section-header'><td colspan='2'>🔧 技术规格</td></tr>"
        "<tr><th>技术规格</th><th>规格参数</th></tr>"
    );
    
    if (nodeName.contains("控制")) {
        content += QString(
            "<tr><td>作动器类型</td><td><span class='status-info'>伺服控制作动器</span></td></tr>"
            "<tr><td>控制模式</td><td><span class='status-purple'>位置控制/力值控制/速度控制</span></td></tr>"
            "<tr><td>驱动方式</td><td><span class='status-purple'>电液伺服/电动伺服</span></td></tr>"
            "<tr><td>响应特性</td><td><span class='status-purple'>高精度、快速响应</span></td></tr>"
            "<tr><td>应用场景</td><td><span class='status-purple'>疲劳试验、静态加载、动态控制</span></td></tr>"
        );
    } else if (nodeName.contains("液压")) {
        content += QString(
            "<tr><td>作动器类型</td><td><span class='status-info'>液压作动器</span></td></tr>"
            "<tr><td>工作原理</td><td><span class='status-purple'>液压油驱动活塞运动</span></td></tr>"
            "<tr><td>输出特性</td><td><span class='status-purple'>大力输出、稳定性好</span></td></tr>"
        );
    } else {
        content += QString(
            "<tr><td>作动器类型</td><td><span class='status-info'>通用作动器</span></td></tr>"
            "<tr><td>功能描述</td><td><span class='status-purple'>执行控制指令、产生运动输出</span></td></tr>"
        );
    }
    
    content += "</table>";
    
    // 配置参数表格
    if (item->columnCount() > 2) {
        content += QString(
            "<table class='compact-table'>"
            "<tr class='section-header'><td colspan='2'>⚙️ 配置参数</td></tr>"
            "<tr><th>配置项</th><th>参数值</th></tr>"
        );
        
        QStringList configHeaders = {"下位机ID", "站点ID", "启用状态", "极性设置", "最大力值", "最大位移", "控制增益", "安全限位"};
        
        for (int col = 2; col < item->columnCount() && col < 10; ++col) {
            QString colText = item->text(col);
            if (!colText.isEmpty()) {
                QString header = col-2 < configHeaders.size() ? configHeaders[col-2] : QString("参数%1").arg(col-1);
                
                // 状态列特殊处理
                if (col == 4) {
                    QString statusIcon = colText.contains("启用") || colText == "1" ? "✅" : "❌";
                    QString statusClass = colText.contains("启用") || colText == "1" ? "status-ok" : "status-error";
                    colText = QString("<span class='%1'>%2 %3</span>").arg(statusClass).arg(statusIcon).arg(colText);
                }
                
                content += QString(
                    "<tr><td>%1</td><td>%2</td></tr>"
                ).arg(header).arg(colText);
            }
        }
        
        content += "</table>";
    }
    
    // 作动器状态评估表格
    content += QString(
        "<table class='status-table'>"
        "<tr class='section-header'><td colspan='2'>📈 状态评估</td></tr>"
        "<tr><th>状态项</th><th>评估结果</th></tr>"
    );
    
    QString actuatorStatus = "正常运行";
    QString statusClass = "status-ok";
    QString statusIcon = "✅";
    
    if (associationInfo.isEmpty()) {
        actuatorStatus = "未关联设备";
        statusClass = "status-error";
        statusIcon = "⚠️";
    }
    
    content += QString(
        "<tr><td>作动器状态</td><td><span class='%2'>%3 %1</span></td></tr>"
        "<tr><td>关联状态</td><td><span class='%5'>%6 %4</span></td></tr>"
        "<tr><td>控制状态</td><td><span class='status-ok'>✅ 控制正常</span></td></tr>"
        "<tr><td>安全状态</td><td><span class='status-ok'>✅ 安全限位正常</span></td></tr>"
    ).arg(actuatorStatus).arg(statusClass).arg(statusIcon)
     .arg(associationInfo.isEmpty() ? "未关联" : "已关联")
     .arg(associationInfo.isEmpty() ? "status-error" : "status-ok")
     .arg(associationInfo.isEmpty() ? "❌" : "✅");
    
    content += "</table>";
    
    // 添加操作提示
    content += QString(
        "<div style='margin-top: 20px; padding: 15px; background-color: #f4e6f7; border-radius: 8px; border-left: 4px solid #8e44ad;'>"
        "<h4 style='margin-top: 0; color: #7d3c98;'>💡 作动器操作</h4>"
        "<ul style='margin: 5px 0; padding-left: 20px;'>"
        "<li>右键点击可编辑作动器配置</li>"
        "<li>双击可进行手动控制</li>"
        "<li>拖拽可调整作动器顺序</li>"
        "</ul>"
        "</div>"
    );
    
    content += "</body></html>";
    
    return content;
}

QString TreeInteractionHandler::generateHardwareNodeInfo(QTreeWidgetItem* item) {
    if (!item) return QString();
    
    QString nodeName = getNodeName(item);
    
    QString content = QString(
        "<!DOCTYPE html>"
        "<html>"
        "<head>"
        "<meta charset='UTF-8'>"
        "<style>"
        "body { font-family: 'Microsoft YaHei UI', sans-serif; margin: 20px; background-color: #f8f9fa; }"
        "h3 { color: #34495e; margin-bottom: 15px; text-align: center; border-bottom: 2px solid #34495e; padding-bottom: 10px; }"
        "table { width: 100%; border-collapse: collapse; margin: 10px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }"
        "th { background-color: #34495e; color: white; padding: 12px 8px; text-align: left; font-weight: bold; border: 1px solid #2c3e50; }"
        "td { padding: 10px 8px; border: 1px solid #ddd; background-color: white; }"
        "tr:nth-child(even) td { background-color: #f9f9f9; }"
        ".section-header { background-color: #ecf0f1; font-weight: bold; text-align: center; color: #2c3e50; }"
        ".status-ok { color: #27ae60; font-weight: bold; }"
        ".status-error { color: #e74c3c; font-weight: bold; }"
        ".status-warning { color: #f39c12; font-weight: bold; }"
        ".status-info { color: #3498db; font-weight: bold; }"
        ".status-purple { color: #9b59b6; font-weight: bold; }"
        ".path-info { background-color: #e8f4fd; padding: 4px 8px; border-radius: 4px; font-family: monospace; }"
        ".compact-table { margin: 15px 0; }"
        ".compact-table th { background-color: #34495e; border-color: #2c3e50; }"
        ".spec-table { margin: 15px 0; }"
        ".spec-table th { background-color: #e67e22; border-color: #d35400; }"
        ".status-table { margin: 15px 0; }"
        ".status-table th { background-color: #27ae60; border-color: #229954; }"
        "</style>"
        "</head>"
        "<body>"
        "<h3>🖥️ 硬件节点 - %1</h3>"
        "<table>"
        "<tr class='section-header'><td colspan='2'>📋 基本信息</td></tr>"
        "<tr><td style='font-weight: bold; width: 30%;'>节点名称</td><td>%1</td></tr>"
    ).arg(nodeName).arg(nodeName);
    
    // 节点路径
    QStringList pathList;
    QTreeWidgetItem* currentItem = item;
    while (currentItem) {
        pathList.prepend(currentItem->text(0));
        currentItem = currentItem->parent();
    }
    content += QString(
        "<tr><td style='font-weight: bold;'>节点路径</td><td><span class='path-info'>%1</span></td></tr>"
    ).arg(pathList.join(" → "));
    
    // 设备类型和功能
    content += QString(
        "<tr><td style='font-weight: bold;'>设备类型</td><td><span class='status-info'>硬件控制节点</span></td></tr>"
        "<tr><td style='font-weight: bold;'>主要功能</td><td><span class='status-purple'>数据采集与控制</span></td></tr>"
    );
    
    // 通道数量信息
    if (item->childCount() > 0) {
        content += QString(
            "<tr><td style='font-weight: bold;'>通道数量</td><td><span class='status-ok'>%1 个</span></td></tr>"
        ).arg(item->childCount());
    } else {
        content += QString(
            "<tr><td style='font-weight: bold;'>通道数量</td><td><span class='status-warning'>0 个</span></td></tr>"
        );
    }
    
    content += "</table>";
    
    // 硬件规格表格
    content += QString(
        "<table class='spec-table'>"
        "<tr class='section-header'><td colspan='2'>🔧 硬件规格</td></tr>"
        "<tr><th>规格项</th><th>规格参数</th></tr>"
        "<tr><td>处理器</td><td><span class='status-purple'>ARM Cortex-A系列</span></td></tr>"
        "<tr><td>内存容量</td><td><span class='status-purple'>512MB DDR3</span></td></tr>"
        "<tr><td>存储容量</td><td><span class='status-purple'>8GB eMMC</span></td></tr>"
        "<tr><td>通讯接口</td><td><span class='status-purple'>以太网、USB、RS485</span></td></tr>"
        "<tr><td>工作温度</td><td><span class='status-purple'>-20°C ~ +70°C</span></tr>"
        "<tr><td>电源要求</td><td><span class='status-purple'>24V DC ±10%</span></td></tr>"
        "</table>"
    );
    
    // 状态评估表格
    content += QString(
        "<table class='status-table'>"
        "<tr class='section-header'><td colspan='2'>📈 状态评估</td></tr>"
        "<tr><th>状态项</th><th>评估结果</th></tr>"
        "<tr><td>硬件状态</td><td><span class='status-ok'>✅ 运行正常</span></td></tr>"
        "<tr><td>通讯状态</td><td><span class='status-ok'>✅ 通讯正常</span></td></tr>"
        "<tr><td>数据采集</td><td><span class='status-ok'>✅ 采集正常</span></td></tr>"
        "<tr><td>控制输出</td><td><span class='status-ok'>✅ 输出正常</span></td></tr>"
        "</table>"
    );
    
    // 如果有子节点，显示通道详细信息
    if (item->childCount() > 0) {
        content += QString(
            "<table class='compact-table'>"
            "<tr class='section-header'><td colspan='2'>🔌 硬件通道</td></tr>"
            "<tr><th>通道名称</th><th>通道类型</th></tr>"
        );
        
        for (int i = 0; i < item->childCount(); ++i) {
            QTreeWidgetItem* child = item->child(i);
            if (child) {
                QString channelName = child->text(0);
                QString channelType = "通用通道";
                
                if (channelName.contains("AI") || channelName.contains("模拟输入")) {
                    channelType = "模拟输入通道";
                } else if (channelName.contains("AO") || channelName.contains("模拟输出")) {
                    channelType = "模拟输出通道";
                } else if (channelName.contains("DI") || channelName.contains("数字输入")) {
                    channelType = "数字输入通道";
                } else if (channelName.contains("DO") || channelName.contains("数字输出")) {
                    channelType = "数字输出通道";
                }
                
                content += QString(
                    "<tr><td>%1</td><td><span class='status-info'>%2</span></td></tr>"
                ).arg(channelName).arg(channelType);
            }
        }
        
        content += "</table>";
        
        // 添加子节点详细信息
        content += generateChildNodesInfo(item, "硬件通道详细信息");
    }
    
    // 添加操作提示
    content += QString(
        "<div style='margin-top: 20px; padding: 15px; background-color: #ecf0f1; border-radius: 8px; border-left: 4px solid #34495e;'>"
        "<h4 style='margin-top: 0; color: #2c3e50;'>💡 硬件节点操作</h4>"
        "<ul style='margin: 5px 0; padding-left: 20px;'>"
        "<li>右键点击可查看详细配置</li>"
        "<li>双击可编辑节点参数</li>"
        "<li>拖拽可调整节点顺序</li>"
        "</ul>"
        "</div>"
    );
    
    content += "</body></html>";
    
    return content;
}

QString TreeInteractionHandler::getNodeType(QTreeWidgetItem* item) {
    if (!item) return QString();
    
    QVariant userData = item->data(0, Qt::UserRole);
    if (userData.isValid()) {
        return userData.toString();
    }
    
    return "未知类型";
}

QString TreeInteractionHandler::getNodeName(QTreeWidgetItem* item) {
    if (!item) return QString();
    
    return item->text(0);
}

QString TreeInteractionHandler::getAssociationInfo(QTreeWidgetItem* item) {
    if (!item || item->columnCount() < 2) return QString();
    
    return item->text(1);
}

void TreeInteractionHandler::showContextMenu(QTreeWidgetItem* item, const QPoint& pos) {
    if (!item) return;
    
    // Layer 3: 专门对话框
    QMenu contextMenu(m_treeWidget);
    
    // 获取节点类型
    QString nodeType = getNodeType(item);
    
    // 不再显示"查看详细信息"菜单
    
    // 添加节点特定的操作选项
    if (nodeType == "试验节点") {
        // 编辑通道配置菜单
        QAction* editChannelAction = contextMenu.addAction("✏️ 编辑通道配置");
        connect(editChannelAction, SIGNAL(triggered()), this, SLOT(editChannelConfig()));
        
        contextMenu.addSeparator();
        
        // 删除关联信息菜单
        QAction* clearAssociationAction = contextMenu.addAction("🗑️ 删除关联信息");
        connect(clearAssociationAction, SIGNAL(triggered()), this, SLOT(clearAssociation()));
        
        // 删除所有关联信息菜单
        QAction* clearAllAssociationAction = contextMenu.addAction("🗑️ 删除所有关联信息");
        connect(clearAllAssociationAction, SIGNAL(triggered()), this, SLOT(clearAllAssociation()));
        
        contextMenu.addSeparator();
        
        // 删除通道菜单
        QAction* deleteChannelAction = contextMenu.addAction("❌ 删除通道");
        connect(deleteChannelAction, SIGNAL(triggered()), this, SLOT(deleteChannel()));
    } else if (nodeType == "传感器节点" || nodeType == "作动器节点") {
        // 为传感器和作动器节点添加删除关联信息菜单
        contextMenu.addSeparator();
        
        QAction* clearAssociationAction = contextMenu.addAction("🗑️ 删除关联信息");
        connect(clearAssociationAction, SIGNAL(triggered()), this, SLOT(clearAssociation()));
    } else if (isControlChannelSubNode(item)) {
        // 🆕 智能菜单合并：控制通道子节点显示完整功能菜单
        generateFullControlChannelMenu(item, contextMenu);
    } else if (nodeType == "控制通道组") {
        // 为控制通道组节点添加管理菜单
        contextMenu.addSeparator();
        
        QAction* createChannelAction = contextMenu.addAction("➕ 新建控制通道");
        connect(createChannelAction, SIGNAL(triggered()), this, SLOT(createChannel()));
    }
    
    // 在鼠标位置显示菜单
    contextMenu.exec(m_treeWidget->mapToGlobal(pos));
}

// 🆕 智能节点检测：判断是否为控制通道的子节点
bool TreeInteractionHandler::isControlChannelSubNode(QTreeWidgetItem* item) {
    if (!item) return false;
    
    // 检查父节点是否为控制通道节点
    QTreeWidgetItem* parent = item->parent();
    if (!parent) return false;
    
    QString parentType = getNodeType(parent);
    QString itemType = getNodeType(item);
    
    // 父节点是"试验节点"（控制通道），子节点是传感器/作动器类型
    return (parentType == "试验节点" && 
            (itemType == "载荷传感器" || itemType == "位置传感器" || itemType == "控制作动器"));
}

// 🆕 智能菜单生成：为控制通道子节点生成完整功能菜单
void TreeInteractionHandler::generateFullControlChannelMenu(QTreeWidgetItem* item, QMenu& contextMenu) {
    // 不再显示"查看详细信息"菜单
    
    // 2. 编辑通道配置（从父节点继承）
    QAction* editChannelAction = contextMenu.addAction("✏️ 编辑通道配置");
    connect(editChannelAction, SIGNAL(triggered()), this, SLOT(editChannelConfig()));
    
    contextMenu.addSeparator();
    
    // 3. 删除关联信息
    QAction* clearAssociationAction = contextMenu.addAction("🗑️ 删除关联信息");
    connect(clearAssociationAction, SIGNAL(triggered()), this, SLOT(clearAssociation()));
    
    // 4. 删除所有关联信息
    QAction* clearAllAssociationAction = contextMenu.addAction("🗑️ 删除所有关联信息");
    connect(clearAllAssociationAction, SIGNAL(triggered()), this, SLOT(clearAllAssociation()));
    
    contextMenu.addSeparator();
    
    // 5. 删除通道
    QAction* deleteChannelAction = contextMenu.addAction("❌ 删除通道");
    connect(deleteChannelAction, SIGNAL(triggered()), this, SLOT(deleteChannel()));
}

// 已删除：不再需要统一详细信息对话框功能

// 已删除：不再需要统一通道信息生成功能

QString TreeInteractionHandler::generateChildNodesInfo(QTreeWidgetItem* item, const QString& childNodeTitle) {
    if (!item || item->childCount() == 0) {
        return QString();
    }
    
    QString childInfo = QString(
        "<tr><td colspan='2' style='padding: 10px 5px; border-bottom: 1px solid #ecf0f1;'>"
        "<div style='background-color: #f8f9fa; padding: 15px; border-radius: 8px; border: 1px solid #dee2e6;'>"
        "<h4 style='color: #34495e; margin: 0 0 15px 0; font-size: 16px; text-align: center; background-color: #e9ecef; padding: 8px; border-radius: 5px;'>🔗 %1</h4>"
    ).arg(childNodeTitle);
    
    for (int i = 0; i < item->childCount(); ++i) {
        QTreeWidgetItem* child = item->child(i);
        QString childName = child->text(0);
        QString childAssociation = child->text(1);
        QString nodeType = getNodeType(child);
        
        // 根据子节点类型设置不同的边框颜色和图标
        QString borderColor = "#3498db"; // 默认蓝色
        QString nodeIcon = "📋";
        
        if (nodeType.contains("传感器") || nodeType.contains("载荷") || nodeType.contains("位置")) {
            borderColor = "#e67e22"; // 橙色用于传感器
            nodeIcon = "📊";
        } else if (nodeType.contains("作动器") || nodeType.contains("控制")) {
            borderColor = "#8e44ad"; // 紫色用于作动器
            nodeIcon = "⚙️";
        } else if (nodeType.contains("硬件")) {
            borderColor = "#34495e"; // 深灰色用于硬件
            nodeIcon = "🖥️";
        } else if (nodeType.contains("控制通道")) {
            borderColor = "#2980b9"; // 蓝色用于控制通道
            nodeIcon = "🎛️";
        }
        
        childInfo += QString(
            "<div style='margin-bottom: 12px; padding: 12px; background-color: white; border-left: 4px solid %1; border-radius: 6px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);'>"
            "<div style='font-weight: bold; color: #2c3e50; margin-bottom: 8px; font-size: 14px;'>%2 %3. %4</div>"
            "<div style='margin-left: 15px;'>"
        ).arg(borderColor).arg(nodeIcon).arg(i + 1).arg(childName);
        
        // 节点类型信息
        childInfo += QString(
            "<div style='margin-bottom: 5px;'><span style='color: #7f8c8d; font-weight: bold;'>类型:</span> <span style='color: %1; font-weight: bold;'>%2</span></div>"
        ).arg(borderColor).arg(nodeType);
        
        // 关联信息
        if (!childAssociation.isEmpty()) {
            childInfo += QString(
                "<div style='margin-bottom: 5px;'><span style='color: #7f8c8d; font-weight: bold;'>关联:</span> <span style='color: #27ae60;'>✅ %1</span></div>"
            ).arg(childAssociation);
        } else {
            childInfo += QString(
                "<div style='margin-bottom: 5px;'><span style='color: #7f8c8d; font-weight: bold;'>关联:</span> <span style='color: #e74c3c;'>⚠️ 未配置关联</span></div>"
            );
        }
        
        // 显示子节点的所有列信息
        if (child->columnCount() > 2) {
            QStringList detailParams;
            QStringList columnHeaders = {"节点名称", "关联信息", "下位机ID", "站点ID", "启用状态", "极性设置", "扩展1", "扩展2", "扩展3", "扩展4"};
            
            for (int col = 2; col < child->columnCount(); ++col) {
                QString colText = child->text(col);
                if (!colText.isEmpty()) {
                    QString colName = col < columnHeaders.size() ? columnHeaders[col] : QString("列%1").arg(col);
                    
                    // 状态列特殊处理
                    if (col == 4) {
                        QString statusColor = colText.contains("启用") || colText == "1" ? "#27ae60" : "#e74c3c";
                        QString statusIcon = colText.contains("启用") || colText == "1" ? "✅" : "❌";
                        detailParams << QString("<span style='color: %3;'>%1: %4 %2</span>").arg(colName).arg(colText).arg(statusColor).arg(statusIcon);
                    } else {
                        detailParams << QString("%1: <span style='color: #8e44ad;'>%2</span>").arg(colName).arg(colText);
                    }
                }
            }
            
            if (!detailParams.isEmpty()) {
                childInfo += QString(
                    "<div style='margin-bottom: 5px;'><span style='color: #7f8c8d; font-weight: bold;'>参数:</span></div>"
                    "<div style='margin-left: 10px; font-size: 12px; color: #5d6d7e;'>%1</div>"
                ).arg(detailParams.join(" | "));
            }
        }
        
        // 递归显示子节点的子节点信息
        if (child->childCount() > 0) {
            childInfo += QString(
                "<div style='margin-top: 8px; padding: 8px; background-color: #f1f2f6; border-radius: 4px; border: 1px dashed #bdc3c7;'>"
                "<div style='color: #3498db; font-weight: bold; margin-bottom: 5px;'>📁 子项详情 (%1 个)</div>"
            ).arg(child->childCount());
            
            // 列出前5个子节点的详细信息
            for (int j = 0; j < qMin(5, child->childCount()); ++j) {
                QTreeWidgetItem* grandChild = child->child(j);
                QString grandChildName = grandChild->text(0);
                QString grandChildType = getNodeType(grandChild);
                QString grandChildAssoc = grandChild->text(1);
                
                childInfo += QString(
                    "<div style='margin-bottom: 3px; padding-left: 10px; font-size: 11px;'>"
                    "<span style='color: #2c3e50; font-weight: bold;'>%1.</span> "
                    "<span style='color: #34495e;'>%2</span> "
                    "<span style='color: #95a5a6;'>(%3)</span>"
                ).arg(j + 1).arg(grandChildName).arg(grandChildType);
                
                if (!grandChildAssoc.isEmpty()) {
                    childInfo += QString(" <span style='color: #27ae60; font-size: 10px;'>→ %1</span>").arg(grandChildAssoc);
                }
                
                childInfo += "</div>";
            }
            
            if (child->childCount() > 5) {
                childInfo += QString(
                    "<div style='color: #95a5a6; font-size: 10px; text-align: center; margin-top: 5px;'>... 还有 %1 个子项</div>"
                ).arg(child->childCount() - 5);
            }
            
            childInfo += "</div>";
        }
        
        // 子节点路径信息
        QStringList childPath;
        QTreeWidgetItem* pathItem = child;
        while (pathItem) {
            childPath.prepend(pathItem->text(0));
            pathItem = pathItem->parent();
        }
        childInfo += QString(
            "<div style='margin-top: 5px; font-size: 10px; color: #95a5a6;'>路径: %1</div>"
        ).arg(childPath.join(" → "));
        
        childInfo += "</div></div>";
    }
    
    // 添加子节点统计摘要
    childInfo += QString(
        "<div style='margin-top: 15px; padding: 10px; background-color: #e8f4f8; border-radius: 5px; border: 1px solid #bee5eb;'>"
        "<h5 style='color: #0c5460; margin: 0 0 8px 0; text-align: center;'>📊 子节点统计摘要</h5>"
    );
    
    // 按类型统计
    QMap<QString, int> typeCount;
    QMap<QString, int> statusCount;
    int configuredCount = 0;
    int totalGrandChildren = 0;
    
    for (int i = 0; i < item->childCount(); ++i) {
        QTreeWidgetItem* child = item->child(i);
        QString nodeType = getNodeType(child);
        typeCount[nodeType]++;
        
        if (!child->text(1).isEmpty()) {
            configuredCount++;
        }
        
        // 统计启用状态
        QString enabled = child->text(4);
        if (!enabled.isEmpty()) {
            if (enabled.contains("启用") || enabled == "1") {
                statusCount["启用"]++;
            } else {
                statusCount["禁用"]++;
            }
        }
        
        totalGrandChildren += child->childCount();
    }
    
    // 显示类型分布
    if (!typeCount.isEmpty()) {
        QStringList typeStats;
        for (auto it = typeCount.begin(); it != typeCount.end(); ++it) {
            typeStats << QString("%1: %2个").arg(it.key()).arg(it.value());
        }
        childInfo += QString(
            "<div style='margin-bottom: 5px;'><span style='color: #0c5460; font-weight: bold;'>类型分布:</span> %1</div>"
        ).arg(typeStats.join(" | "));
    }
    
    // 显示配置状态
    childInfo += QString(
        "<div style='margin-bottom: 5px;'><span style='color: #0c5460; font-weight: bold;'>配置状态:</span> "
        "<span style='color: #27ae60;'>已配置: %1</span> | "
        "<span style='color: #e74c3c;'>未配置: %2</span></div>"
    ).arg(configuredCount).arg(item->childCount() - configuredCount);
    
    // 显示启用状态统计
    if (!statusCount.isEmpty()) {
        QStringList statusStats;
        for (auto it = statusCount.begin(); it != statusCount.end(); ++it) {
            QString color = it.key() == "启用" ? "#27ae60" : "#e74c3c";
            statusStats << QString("<span style='color: %2;'>%1: %3个</span>").arg(it.key()).arg(color).arg(it.value());
        }
        childInfo += QString(
            "<div style='margin-bottom: 5px;'><span style='color: #0c5460; font-weight: bold;'>状态分布:</span> %1</div>"
        ).arg(statusStats.join(" | "));
    }
    
    // 显示子项总数
    if (totalGrandChildren > 0) {
        childInfo += QString(
            "<div><span style='color: #0c5460; font-weight: bold;'>包含子项:</span> 总计 %1 个子级节点</div>"
        ).arg(totalGrandChildren);
    }
    
    childInfo += "</div></div></td></tr>";
    
    return childInfo;
}

QString TreeInteractionHandler::formatHtmlInfo(const QString& title, const QString& content) {
    return QString(
        "<!DOCTYPE html>"
        "<html>"
        "<head>"
        "<meta charset='UTF-8'>"
        "<style>"
        "body { "
            "font-family: 'Microsoft YaHei UI', 'Segoe UI', sans-serif; "
            "margin: 10px; "
            "background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); "
            "line-height: 1.4; "
            "font-size: 13px; "
        "}"
        "h2 { "
            "color: #2c3e50; "
            "text-align: center; "
            "margin: 0 0 15px 0; "
            "padding: 10px; "
            "background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); "
            "color: white; "
            "border-radius: 8px; "
            "box-shadow: 0 3px 10px rgba(0,0,0,0.2); "
            "font-weight: 600; "
            "font-size: 16px; "
        "}"
        "h3 { "
            "color: #34495e; "
            "margin: 15px 0 8px 0; "
            "padding: 6px 0; "
            "border-bottom: 2px solid #3498db; "
            "font-weight: 500; "
            "font-size: 14px; "
        "}"
        "table { "
            "width: 100%; "
            "border-collapse: collapse; "
            "background-color: white; "
            "border-radius: 6px; "
            "overflow: hidden; "
            "box-shadow: 0 4px 12px rgba(0,0,0,0.1); "
            "margin-bottom: 12px; "
            "font-size: 12px; "
        "}"
        "thead { "
            "background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); "
            "color: white; "
        "}"
        "thead th { "
            "padding: 8px 10px; "
            "text-align: left; "
            "font-weight: 600; "
            "font-size: 12px; "
            "border: none; "
        "}"
        "tbody tr { "
            "transition: background-color 0.2s ease; "
        "}"
        "tbody tr:nth-child(even) { "
            "background-color: #f8f9fa; "
        "}"
        "tbody tr:hover { "
            "background-color: #e3f2fd; "
        "}"
        "td, th { "
            "padding: 6px 10px; "
            "border-bottom: 1px solid #e0e0e0; "
            "vertical-align: top; "
        "}"
        "td:first-child { "
            "font-weight: 600; "
            "color: #34495e; "
            "background-color: rgba(52, 152, 219, 0.08); "
            "width: 30%; "
            "border-right: 2px solid #3498db; "
        "}"
        "td:last-child { "
            "color: #2c3e50; "
        "}"
        ".section-header { "
            "background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important; "
            "color: white !important; "
            "text-align: center !important; "
            "font-weight: 600 !important; "
            "font-size: 12px !important; "
            "padding: 8px !important; "
            "border: none !important; "
        "}"
        ".status-ok { color: #27ae60; font-weight: bold; }"
        ".status-warning { color: #f39c12; font-weight: bold; }"
        ".status-error { color: #e74c3c; font-weight: bold; }"
        ".status-info { color: #3498db; font-weight: bold; }"
        ".status-purple { color: #8e44ad; font-weight: bold; }"
        ".child-info { "
            "background-color: #f8f9fa; "
            "border-radius: 6px; "
            "padding: 10px; "
            "margin: 8px 0; "
            "border-left: 3px solid #3498db; "
        "}"
        ".child-item { "
            "background-color: white; "
            "border-radius: 4px; "
            "padding: 6px; "
            "margin: 5px 0; "
            "border-left: 2px solid #2ecc71; "
            "box-shadow: 0 1px 3px rgba(0,0,0,0.1); "
        "}"
        ".stats-summary { "
            "background-color: #f8f9fa; "
            "border-radius: 6px; "
            "padding: 10px; "
            "margin: 10px 0; "
            "border: 1px solid #d1ecf1; "
        "}"
        ".path-info { "
            "font-family: 'Consolas', 'Courier New', monospace; "
            "background-color: #f8f9fa; "
            "padding: 3px 6px; "
            "border-radius: 3px; "
            "border: 1px solid #dee2e6; "
            "font-size: 11px; "
        "}"
        ".compact-table { "
            "font-size: 11px; "
        "}"
        ".compact-table td, .compact-table th { "
            "padding: 4px 8px; "
        "}"
        ".nodes-container { "
            "display: flex !important; "
            "flex-wrap: wrap !important; "
            "gap: 8px !important; "
            "justify-content: flex-start !important; "
        "}"
        ".node-card { "
            "flex: 0 0 calc(50% - 4px) !important; "
            "min-width: 180px !important; "
            "max-width: 280px !important; "
            "box-sizing: border-box !important; "
        "}"
        "</style>"
        "</head>"
        "<body>"
        "<h2>%1</h2>"
        "%2"
        "</body>"
        "</html>"
    ).arg(title).arg(content);
}

// ==================== 持久提示窗口相关实现 ====================

void TreeInteractionHandler::createPersistentTooltip() {
    // 🆕 修改：试验资源提示信息样式与硬件资源保持一致
    // 创建提示窗口框架（与硬件资源使用相同的样式）
    m_persistentTooltip = new QFrame(nullptr, Qt::ToolTip | Qt::FramelessWindowHint);
    m_persistentTooltip->setWindowFlags(Qt::ToolTip | Qt::FramelessWindowHint | Qt::WindowStaysOnTopHint);
    m_persistentTooltip->setAttribute(Qt::WA_DeleteOnClose, false);
    m_persistentTooltip->setFixedWidth(350); // 固定宽度，避免过窄（与硬件资源相同）
    
    // 🆕 修改：设置与硬件资源一致的窗口样式
    m_persistentTooltip->setStyleSheet(
        "QFrame {"
        "    background-color: #2c3e50;"  // 深蓝色背景（与硬件资源相同）
        "    border: 2px solid #34495e;"  // 深色边框（与硬件资源相同）
        "    border-radius: 8px;"         // 圆角边框（与硬件资源相同）
        "    padding: 10px;"              // 内边距（与硬件资源相同）
        "}"
    );
    
    // 🆕 修改：创建与硬件资源一致的文本标签
    m_tooltipLabel = new QLabel(m_persistentTooltip);
    m_tooltipLabel->setWordWrap(true);
    m_tooltipLabel->setAlignment(Qt::AlignTop | Qt::AlignLeft);
    m_tooltipLabel->setTextFormat(Qt::RichText);
    m_tooltipLabel->setStyleSheet(
        "QLabel {"
        "    color: #ecf0f1;"                    // 浅色文字（与硬件资源相同）
        "    font-family: 'Microsoft YaHei', Arial, sans-serif;"  // 中文字体（与硬件资源相同）
        "    font-size: 12px;"                   // 字体大小（与硬件资源相同）
        "    line-height: 1.4;"                  // 行高（与硬件资源相同）
        "    background-color: transparent;"     // 透明背景（与硬件资源相同）
        "    border: none;"                      // 无边框（与硬件资源相同）
        "    padding: 5px;"                      // 内边距（与硬件资源相同）
        "}"
    );
    
    // 🆕 修改：设置与硬件资源一致的布局
    QVBoxLayout* layout = new QVBoxLayout(m_persistentTooltip);
    layout->setContentsMargins(5, 5, 5, 5);  // 边距（与硬件资源相同）
    layout->addWidget(m_tooltipLabel);
    
    // 初始状态隐藏
    m_persistentTooltip->hide();
    
    qDebug() << "TreeInteractionHandler: Created persistent tooltip with hardware resource style";
}

void TreeInteractionHandler::showPersistentTooltip(const QString& text, const QPoint& position) {
    if (!m_persistentTooltip || !m_tooltipLabel) {
        return;
    }
    
    // 🆕 修改：试验资源提示信息显示位置与硬件资源保持一致
    // 更新提示文本（支持HTML格式，与硬件资源相同）
    m_tooltipLabel->setText(text);
    m_tooltipLabel->setTextFormat(Qt::RichText);
    
    // 调整窗口大小以适应内容（与硬件资源相同）
    m_persistentTooltip->adjustSize();
    
    // 🆕 修改：计算与硬件资源一致的显示位置
    // 计算显示位置，避免超出屏幕边界（与硬件资源相同）
    QPoint tooltipPos = position + QPoint(15, 10); // 鼠标右下方偏移（与硬件资源相同）
    
    QScreen* screen = QGuiApplication::screenAt(position);
    if (screen) {
        QRect screenGeometry = screen->availableGeometry();
        QSize tooltipSize = m_persistentTooltip->size();
        
        // 🆕 修改：边界检测逻辑与硬件资源保持一致
        // 防止右边界溢出（与硬件资源相同）
        if (tooltipPos.x() + tooltipSize.width() > screenGeometry.right()) {
            tooltipPos.setX(position.x() - tooltipSize.width() - 15);
        }
        
        // 防止下边界溢出（与硬件资源相同）
        if (tooltipPos.y() + tooltipSize.height() > screenGeometry.bottom()) {
            tooltipPos.setY(position.y() - tooltipSize.height() - 10);
        }
        
        // 防止左边界溢出（与硬件资源相同）
        if (tooltipPos.x() < screenGeometry.left()) {
            tooltipPos.setX(screenGeometry.left() + 5);
        }
        
        // 防止上边界溢出（与硬件资源相同）
        if (tooltipPos.y() < screenGeometry.top()) {
            tooltipPos.setY(screenGeometry.top() + 5);
        }
    }
    
    // 🆕 修改：移动并显示提示窗口（与硬件资源相同）
    // 移动并显示提示窗口
    m_persistentTooltip->move(tooltipPos);
    m_persistentTooltip->show();
    m_persistentTooltip->raise();
    
    qDebug() << "TreeInteractionHandler: Showing persistent tooltip at position:" << tooltipPos;
    qDebug() << "TreeInteractionHandler: Tooltip content length:" << text.length();
}

void TreeInteractionHandler::hidePersistentTooltip() {
    if (m_persistentTooltip) {
        m_persistentTooltip->hide();
    }
    m_currentHoverItem = nullptr;
}

void TreeInteractionHandler::updatePersistentTooltip(const QString& text) {
    if (m_persistentTooltip && m_persistentTooltip->isVisible() && m_tooltipLabel) {
        m_tooltipLabel->setText(text);
        m_tooltipLabel->setTextFormat(Qt::RichText);
        m_persistentTooltip->adjustSize();
    }
}

bool TreeInteractionHandler::eventFilter(QObject* obj, QEvent* event) {
    if (obj == m_treeWidget) {
        if (event->type() == QEvent::Leave) {
            // 🆕 修改：试验资源提示信息隐藏机制与硬件资源保持一致
            // 鼠标离开树形控件时隐藏提示（与硬件资源相同）
            hidePersistentTooltip();
            qDebug() << "TreeInteractionHandler: Mouse left tree widget, hiding persistent tooltip";
        } else if (event->type() == QEvent::MouseMove) {
            // 🆕 修改：试验资源提示信息更新机制与硬件资源保持一致
            // 鼠标移动时更新提示位置（与硬件资源相同）
            QMouseEvent* mouseEvent = static_cast<QMouseEvent*>(event);
            if (m_currentHoverItem && m_persistentTooltip && m_persistentTooltip->isVisible()) {
                QPoint globalPos = m_treeWidget->mapToGlobal(mouseEvent->pos());
                
                // 检查是否仍在当前节点上
                QTreeWidgetItem* itemAtPos = m_treeWidget->itemAt(mouseEvent->pos());
                if (itemAtPos != m_currentHoverItem) {
                    // 如果鼠标移到了其他节点，更新提示内容（与硬件资源相同）
                    if (itemAtPos) {
                        m_currentHoverItem = itemAtPos;
                        QString tooltipInfo = generateLayer1Info(itemAtPos);
                        updatePersistentTooltip(tooltipInfo);
                        qDebug() << "TreeInteractionHandler: Updated tooltip for new item:" << itemAtPos->text(0);
                    } else {
                        hidePersistentTooltip();
                        qDebug() << "TreeInteractionHandler: No item at mouse position, hiding tooltip";
                    }
                }
            }
        }
    }
    
    return QObject::eventFilter(obj, event);
}

void TreeInteractionHandler::onMouseLeave() {
    hidePersistentTooltip();
}

QString TreeInteractionHandler::generateChildNodesTableInfo(QTreeWidgetItem* item, const QString& childNodeTitle) {
    if (!item) return QString();
    
    QString childInfo = QString(
        "<div class='child-info'>"
        "<h4 style='color: #2c3e50; margin: 0 0 15px 0; text-align: center;'>🔗 %1</h4>"
    ).arg(childNodeTitle);
    
    if (item->childCount() == 0) {
        childInfo += "<p style='text-align: center; color: #7f8c8d; font-style: italic;'>暂无子节点</p>";
        childInfo += "</div>";
        return childInfo;
    }
    
    // 统计变量
    QMap<QString, int> typeCount;
    int configuredCount = 0;
    QMap<QString, int> statusCount;
    int totalGrandChildren = 0;
    
    // 先显示统计摘要
    for (int i = 0; i < item->childCount(); ++i) {
        QTreeWidgetItem* child = item->child(i);
        QString childType = getNodeType(child);
        typeCount[childType]++;
        
        if (!child->text(1).isEmpty()) {
            configuredCount++;
        }
        
        QString enabledText = child->text(4);
        if (!enabledText.isEmpty()) {
            if (enabledText.contains("启用") || enabledText == "1") {
                statusCount["启用"]++;
            } else {
                statusCount["禁用"]++;
            }
        }
        
        totalGrandChildren += child->childCount();
    }
    
    // 统计摘要表格（放在前面）- 横向布局优化
    childInfo += QString(
        "<table class='compact-table' style='margin-bottom: 15px; max-width: 100%; table-layout: fixed;'>"
        "<thead>"
        "<tr><th style='width: 25%;'>统计项</th><th style='width: 75%;'>数据</th></tr>"
        "</thead>"
        "<tbody>"
    );
    
    // 总数统计
    childInfo += QString(
        "<tr><td><strong>节点总数</strong></td><td><span class='status-info'>%1 个子节点</span></td></tr>"
    ).arg(item->childCount());
    
    // 类型分布
    if (!typeCount.isEmpty()) {
        QStringList typeStats;
        for (auto it = typeCount.begin(); it != typeCount.end(); ++it) {
            typeStats << QString("<span class='status-purple'>%1: %2个</span>").arg(it.key()).arg(it.value());
        }
        childInfo += QString(
            "<tr><td>类型分布</td><td>%1</td></tr>"
        ).arg(typeStats.join(" | "));
    }
    
    // 配置状态
    childInfo += QString(
        "<tr><td>配置状态</td><td>"
        "<span class='status-ok'>✅ 已配置: %1</span> | "
        "<span class='status-error'>⚠️ 未配置: %2</span>"
        "</td></tr>"
    ).arg(configuredCount).arg(item->childCount() - configuredCount);
    
    // 启用状态统计
    if (!statusCount.isEmpty()) {
        QStringList statusStats;
        for (auto it = statusCount.begin(); it != statusCount.end(); ++it) {
            QString cssClass = it.key() == "启用" ? "status-ok" : "status-error";
            QString icon = it.key() == "启用" ? "✅" : "❌";
            statusStats << QString("<span class='%2'>%4 %1: %3个</span>").arg(it.key()).arg(cssClass).arg(it.value()).arg(icon);
        }
        childInfo += QString(
            "<tr><td>状态分布</td><td>%1</td></tr>"
        ).arg(statusStats.join(" | "));
    }
    
    // 子项总数
    if (totalGrandChildren > 0) {
        childInfo += QString(
            "<tr><td>包含子项</td><td><span class='status-info'>总计 %1 个子级节点</span></td></tr>"
        ).arg(totalGrandChildren);
    }
    
    childInfo += "</tbody></table>";
    
    // 节点数量分页控制
    int maxNodesPerPage = 8; // 每页最多显示8个节点
    int totalPages = (item->childCount() + maxNodesPerPage - 1) / maxNodesPerPage;
    
    if (item->childCount() > maxNodesPerPage) {
        childInfo += QString(
            "<div class='pagination-info' style='text-align: center; margin: 10px 0; color: #7f8c8d;'>"
            "📄 节点数量较多，分页显示（共 %1 页，每页 %2 个节点）"
            "</div>"
        ).arg(totalPages).arg(maxNodesPerPage);
    }
    
    // 卡片式节点布局 - 强制横向排列
    childInfo += "<div class='nodes-container' style='display: flex; flex-wrap: wrap; gap: 8px; margin: 15px 0; justify-content: flex-start;'>";
    
    for (int page = 0; page < totalPages; ++page) {
        int startIdx = page * maxNodesPerPage;
        int endIdx = qMin(startIdx + maxNodesPerPage, item->childCount());
        
        if (totalPages > 1) {
            QString pageClass = page == 0 ? "page-visible" : "page-hidden";
            childInfo += QString(
                "</div><div class='page-section %1' data-page='%2' style='width: 100%; margin: 10px 0;'>"
                "<h5 style='color: #34495e; margin: 10px 0 5px 0; text-align: center; background: #ecf0f1; padding: 5px; border-radius: 3px;'>"
                "📋 第 %3 页 (节点 %4-%5)"
                "</h5>"
                "</div>"
                "<div class='nodes-container' style='display: flex; flex-wrap: wrap; gap: 8px; margin: 15px 0; justify-content: flex-start;'>"
            ).arg(pageClass).arg(page + 1).arg(page + 1).arg(startIdx + 1).arg(endIdx);
        }
        
        for (int i = startIdx; i < endIdx; ++i) {
            QTreeWidgetItem* child = item->child(i);
            QString childName = child->text(0);
            QString childType = getNodeType(child);
            QString childAssoc = getAssociationInfo(child);
            
            // 关联状态
            QString assocIcon = childAssoc.isEmpty() ? "⚠️" : "✅";
            QString assocClass = childAssoc.isEmpty() ? "status-error" : "status-ok";
            QString assocText = childAssoc.isEmpty() ? "未配置" : childAssoc;
            
            // 启用状态
            QString enabledText = child->text(4);
            QString statusIcon = "❓";
            QString statusClass = "status-info";
            QString statusText = "未设置";
            
            if (!enabledText.isEmpty()) {
                if (enabledText.contains("启用") || enabledText == "1") {
                    statusIcon = "✅";
                    statusClass = "status-ok";
                    statusText = "已启用";
                } else {
                    statusIcon = "❌";
                    statusClass = "status-error";
                    statusText = "已禁用";
                }
            }
            
            // 子项数量
            int grandChildCount = child->childCount();
            QString childCountText = grandChildCount > 0 ? QString("%1个子项").arg(grandChildCount) : "无子项";
            QString childCountClass = grandChildCount > 0 ? "status-info" : "status-muted";
            
            // 节点卡片 - 横向布局优化
            QString pageClass = (totalPages > 1 && page > 0) ? "page-hidden" : "page-visible";
            childInfo += QString(
                "<div class='node-card %8' data-page='%9' style='"
                "border: 1px solid #ddd; border-radius: 6px; padding: 8px; "
                "background: linear-gradient(145deg, #ffffff, #f8f9fa); "
                "box-shadow: 0 2px 4px rgba(0,0,0,0.1); "
                "transition: all 0.2s ease; "
                "position: relative; overflow: hidden; "
                "flex: 0 0 calc(50% - 4px); min-width: 200px; max-width: 300px;"
                "'>"
                
                // 节点序号标签
                "<div style='position: absolute; top: 5px; right: 5px; "
                "background: #3498db; color: white; border-radius: 12px; "
                "width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; "
                "font-size: 10px; font-weight: bold;'>%1</div>"
                
                // 节点名称
                "<div style='font-weight: 600; font-size: 13px; color: #2c3e50; margin-bottom: 8px; padding-right: 30px;'>"
                "📍 %2"
                "</div>"
                
                // 节点信息表格
                "<table style='font-size: 10px; width: 100%; border-collapse: collapse;'>"
                "<tr><td style='width: 25%; color: #7f8c8d; padding: 2px 0;'>类型</td><td style='padding: 2px 0;'><span class='status-purple'>%3</span></td></tr>"
                "<tr><td style='color: #7f8c8d; padding: 2px 0;'>关联</td><td style='padding: 2px 0;'><span class='%4'>%7 %5</span></td></tr>"
                "<tr><td style='color: #7f8c8d; padding: 2px 0;'>状态</td><td style='padding: 2px 0;'><span class='%6'>%7 %5</span></td></tr>"
                "<tr><td style='color: #7f8c8d; padding: 2px 0;'>子项</td><td style='padding: 2px 0;'><span class='%8'>%9</span></td></tr>"
            ).arg(i + 1).arg(childName).arg(childType).arg(assocClass).arg(assocText).arg(statusClass).arg(statusText).arg(pageClass).arg(page + 1)
             .arg(assocIcon).arg(statusIcon).arg(childCountClass).arg(childCountText);
            
            // 技术参数（如果有）
            QStringList techParams;
            QStringList paramNames = {"下位机ID", "站点ID", "", "极性", "量程", "精度", "参数7", "参数8"};
            
            for (int col = 2; col < child->columnCount() && col < 10; ++col) {
                QString colText = child->text(col);
                if (!colText.isEmpty() && col != 4) { // 跳过状态列
                    QString paramName = col-2 < paramNames.size() ? paramNames[col-2] : QString("参数%1").arg(col-1);
                    if (!paramName.isEmpty()) {
                        techParams << QString("%1:%2").arg(paramName).arg(colText);
                    }
                }
            }
            
            if (!techParams.isEmpty()) {
                childInfo += QString(
                    "<tr><td colspan='2' style='padding: 4px 0; border-top: 1px solid #eee; color: #6c757d; font-style: italic;'>"
                    "🔧 %1"
                    "</td></tr>"
                ).arg(techParams.join(" | "));
            }
            
            childInfo += "</table></div>";
        }
    }
    
    childInfo += "</div>"; // 结束 nodes-container
    
    // 分页控制按钮（如果有多页）
    if (totalPages > 1) {
        childInfo += QString(
            "<div class='pagination-controls' style='text-align: center; margin: 15px 0;'>"
            "<button onclick='showPage(1)' style='margin: 2px; padding: 5px 10px; border: 1px solid #3498db; background: #3498db; color: white; border-radius: 3px; cursor: pointer;'>第1页</button>"
        );
        
        for (int p = 2; p <= totalPages; ++p) {
            childInfo += QString(
                "<button onclick='showPage(%1)' style='margin: 2px; padding: 5px 10px; border: 1px solid #bdc3c7; background: #ecf0f1; color: #2c3e50; border-radius: 3px; cursor: pointer;'>第%1页</button>"
            ).arg(p);
        }
        
        childInfo += QString(
            "</div>"
            "<script>"
            "function showPage(pageNum) {"
            "  var pages = document.querySelectorAll('.page-section, .node-card');"
            "  var buttons = document.querySelectorAll('.pagination-controls button');"
            "  pages.forEach(function(page) {"
            "    if (page.getAttribute('data-page') == pageNum) {"
            "      page.style.display = 'block';"
            "      page.classList.remove('page-hidden');"
            "      page.classList.add('page-visible');"
            "    } else {"
            "      page.style.display = 'none';"
            "      page.classList.remove('page-visible');"
            "      page.classList.add('page-hidden');"
            "    }"
            "  });"
            "  buttons.forEach(function(btn, idx) {"
            "    if (idx + 1 == pageNum) {"
            "      btn.style.background = '#3498db';"
            "      btn.style.color = 'white';"
            "      btn.style.borderColor = '#3498db';"
            "    } else {"
            "      btn.style.background = '#ecf0f1';"
            "      btn.style.color = '#2c3e50';"
            "      btn.style.borderColor = '#bdc3c7';"
            "    }"
            "  });"
            "}"
            "</script>"
        );
    }
    
    childInfo += "</div>"; // 结束 child-info
    
    return childInfo;
}

QString TreeInteractionHandler::generateErrorHtml(const QString& nodeName, const QString& nodeType, const QString& errorMessage) {
    return QString(
        "<!DOCTYPE html>"
        "<html>"
        "<head>"
        "<meta charset='UTF-8'>"
        "<style>"
        "body { font-family: 'Microsoft YaHei UI', sans-serif; margin: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }"
        ".container { max-width: 800px; margin: 0 auto; background: white; border-radius: 15px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); overflow: hidden; }"
        ".header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }"
        ".header h1 { font-size: 2.5em; margin-bottom: 10px; font-weight: 300; }"
        ".content { padding: 30px; }"
        ".error-section { background: #fff5f5; border: 1px solid #fed7d7; border-radius: 8px; padding: 20px; margin: 20px 0; }"
        ".error-title { color: #e53e3e; font-size: 1.5em; margin-bottom: 15px; }"
        ".error-message { color: #c53030; margin-bottom: 15px; }"
        ".info-table { width: 100%; border-collapse: collapse; margin: 20px 0; }"
        ".info-table th, .info-table td { padding: 12px; border: 1px solid #e2e8f0; text-align: left; }"
        ".info-table th { background-color: #4a5568; color: white; }"
        ".info-table tr:nth-child(even) { background-color: #f7fafc; }"
        ".retry-button { background: #3182ce; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; font-size: 1em; margin-top: 20px; }"
        ".retry-button:hover { background: #2c5aa0; }"
        "</style>"
        "</head>"
        "<body>"
        "<div class='container'>"
        "<div class='header'>"
        "<h1>⚠️ 模板加载错误</h1>"
        "<div class='subtitle'>%1</div>"
        "</div>"
        "<div class='content'>"
        "<div class='error-section'>"
        "<div class='error-title'>❌ 错误信息</div>"
        "<div class='error-message'>%2</div>"
        "</div>"
        "<table class='info-table'>"
        "<tr><th>节点名称</th><td>%1</td></tr>"
        "<tr><th>节点类型</th><td>%3</td></tr>"
        "<tr><th>错误时间</th><td>%4</td></tr>"
        "</table>"
        "<div style='text-align: center;'>"
        "<button class='retry-button' onclick='location.reload()'>🔄 重新加载</button>"
        "</div>"
        "</div>"
        "</div>"
        "<script>"
        "console.log('错误页面已加载，节点: %1');"
        "setTimeout(function() { location.reload(); }, 5000);"
        "</script>"
        "</body>"
        "</html>"
    ).arg(nodeName).arg(errorMessage).arg(nodeType).arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"));
}

// 🆕 新增：右键菜单槽函数实现
void TreeInteractionHandler::editChannelConfig() {
    QAction* action = qobject_cast<QAction*>(sender());
    if (!action) return;
    
    // 获取当前选中的节点
    QTreeWidgetItem* currentItem = m_treeWidget->currentItem();
    if (!currentItem || !m_mainWindow) return;
    
    QString itemText = currentItem->text(0);
    if (itemText == "CH1" || itemText == "CH2") {
        m_mainWindow->OnTestConfigTreeItemDoubleClicked(currentItem, 0);
        qDebug() << "TreeInteractionHandler: Editing channel config for" << itemText;
    }
}

void TreeInteractionHandler::clearAssociation() {
    QAction* action = qobject_cast<QAction*>(sender());
    if (!action) return;
    
    // 获取当前选中的节点
    QTreeWidgetItem* currentItem = m_treeWidget->currentItem();
    if (!currentItem || !m_mainWindow) return;
    
    m_mainWindow->OnClearSingleAssociation(currentItem);
    qDebug() << "TreeInteractionHandler: Clearing association for" << currentItem->text(0);
}

void TreeInteractionHandler::clearAllAssociation() {
    QAction* action = qobject_cast<QAction*>(sender());
    if (!action) return;
    
    // 获取当前选中的节点
    QTreeWidgetItem* currentItem = m_treeWidget->currentItem();
    if (!currentItem || !m_mainWindow) return;
    
    m_mainWindow->OnClearAllAssociation(currentItem);
    qDebug() << "TreeInteractionHandler: Clearing all associations for" << currentItem->text(0);
}

void TreeInteractionHandler::deleteChannel() {
    QAction* action = qobject_cast<QAction*>(sender());
    if (!action) return;
    
    // 获取当前选中的节点
    QTreeWidgetItem* currentItem = m_treeWidget->currentItem();
    if (!currentItem || !m_mainWindow) return;
    
    m_mainWindow->OnDeleteControlChannel(currentItem);
    qDebug() << "TreeInteractionHandler: Deleting channel" << currentItem->text(0);
}

void TreeInteractionHandler::createChannel() {
    QAction* action = qobject_cast<QAction*>(sender());
    if (!action) return;
    
    // 获取当前选中的节点
    QTreeWidgetItem* currentItem = m_treeWidget->currentItem();
    if (!currentItem || !m_mainWindow) return;
    
    m_mainWindow->OnCreateControlChannel(currentItem);
    qDebug() << "TreeInteractionHandler: Creating new control channel under" << currentItem->text(0);
}
