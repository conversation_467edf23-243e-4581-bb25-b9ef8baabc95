@echo off
chcp 65001 >nul
echo ========================================
echo  JSON导出数据同步问题修复验证
echo ========================================

echo 检查数据同步修复状态...

REM 检查硬件节点数据同步
echo.
echo 1. 检查硬件节点数据同步修复...
findstr /C:"currentProject_->hardwareNodes.push_back(node)" "SiteResConfig\src\MainWindow_Qt_Simple.cpp" >nul
if %errorlevel%==0 (
    echo ✅ 硬件节点数据同步已修复
) else (
    echo ❌ 硬件节点数据同步未修复
)

findstr /C:"CreateHardwareNodeInTree" "SiteResConfig\src\MainWindow_Qt_Simple.cpp" >nul
if %errorlevel%==0 (
    echo ✅ 硬件节点创建方法存在
) else (
    echo ❌ 硬件节点创建方法不存在
)

REM 检查作动器数据同步
echo.
echo 2. 检查作动器数据同步修复...
findstr /C:"currentProject_->actuators.push_back(actuator)" "SiteResConfig\src\MainWindow_Qt_Simple.cpp" >nul
if %errorlevel%==0 (
    echo ✅ 作动器数据同步已修复
) else (
    echo ❌ 作动器数据同步未修复
)

findstr /C:"CreateActuatorDeviceWithExtendedParams" "SiteResConfig\src\MainWindow_Qt_Simple.cpp" >nul
if %errorlevel%==0 (
    echo ✅ 作动器创建方法存在
) else (
    echo ❌ 作动器创建方法不存在
)

REM 检查传感器数据同步
echo.
echo 3. 检查传感器数据同步修复...
findstr /C:"currentProject_->sensors.push_back(sensor)" "SiteResConfig\src\MainWindow_Qt_Simple.cpp" >nul
if %errorlevel%==0 (
    echo ✅ 传感器数据同步已修复
) else (
    echo ❌ 传感器数据同步未修复
)

findstr /C:"CreateSensorDevice" "SiteResConfig\src\MainWindow_Qt_Simple.cpp" >nul
if %errorlevel%==0 (
    echo ✅ 传感器创建方法存在
) else (
    echo ❌ 传感器创建方法不存在
)

REM 检查文件名编码修复
echo.
echo 4. 检查文件名编码修复...
findstr /C:"toLocal8Bit().constData()" "SiteResConfig\src\MainWindow_Qt_Simple.cpp" >nul
if %errorlevel%==0 (
    echo ✅ MainWindow文件名编码已修复
) else (
    echo ❌ MainWindow文件名编码未修复
)

findstr /C:"QFile file(qFilePath)" "SiteResConfig\src\DataModels_Simple.cpp" >nul
if %errorlevel%==0 (
    echo ✅ DataModels文件操作已修复
) else (
    echo ❌ DataModels文件操作未修复
)

REM 检查枚举类型转换
echo.
echo 5. 检查枚举类型转换...
findstr /C:"ActuatorType::Hydraulic" "SiteResConfig\src\MainWindow_Qt_Simple.cpp" >nul
if %errorlevel%==0 (
    echo ✅ 作动器枚举转换已实现
) else (
    echo ❌ 作动器枚举转换未实现
)

findstr /C:"SensorType::Force" "SiteResConfig\src\MainWindow_Qt_Simple.cpp" >nul
if %errorlevel%==0 (
    echo ✅ 传感器枚举转换已实现
) else (
    echo ❌ 传感器枚举转换未实现
)

echo.
echo ========================================
echo  修复内容总结
echo ========================================
echo.
echo 🔧 硬件节点数据同步修复:
echo ✅ CreateHardwareNodeInTree方法中添加数据同步
echo ✅ 自动分配节点ID和默认参数
echo ✅ 将UI创建的节点同步到currentProject_
echo.
echo 🔧 作动器数据同步修复:
echo ✅ CreateActuatorDeviceWithExtendedParams方法中添加数据同步
echo ✅ 根据类型名称自动设置枚举值
echo ✅ 根据缸径计算最大力
echo ✅ 自动分配控制通道
echo.
echo 🔧 传感器数据同步修复:
echo ✅ CreateSensorDevice方法中添加数据同步
echo ✅ 根据类型名称自动设置枚举值和单位
echo ✅ 解析量程数值
echo ✅ 自动分配数据采集通道
echo.
echo 🔧 文件名编码修复:
echo ✅ MainWindow使用toLocal8Bit()处理中文文件名
echo ✅ DataModels使用QFile替代std::ofstream
echo ✅ 支持中文路径和文件名
echo.

REM 检查可执行文件
echo 6. 检查可执行文件状态...
if exist "build-SiteResConfig_Simple-Desktop_Qt_5_14_2_MinGW_32_bit-Debug\debug\SiteResConfig.exe" (
    echo ✅ 可执行文件存在
    echo    建议重新编译以应用数据同步修复
) else (
    echo ❌ 可执行文件不存在，需要编译项目
)

echo.
echo ========================================
echo  测试步骤建议
echo ========================================
echo 1. 重新编译项目以应用所有修复
echo 2. 启动应用程序创建新项目
echo 3. 通过右键菜单创建硬件节点
echo 4. 通过右键菜单创建作动器组和作动器
echo 5. 通过右键菜单创建传感器组和传感器
echo 6. 导出JSON文件验证内容
echo 7. 检查JSON文件名是否正确显示中文
echo 8. 验证JSON内容包含所有创建的设备
echo.

echo ========================================
echo  预期的JSON格式
echo ========================================
echo 修复后的JSON应该包含:
echo {
echo   "projectName": "项目名称",
echo   "hardwareNodes": [
echo     {
echo       "nodeId": 0,
echo       "nodeName": "LD-B1",
echo       "nodeType": "ServoController",
echo       "ipAddress": "*************",
echo       "port": 8080,
echo       "channelCount": 2,
echo       "maxSampleRate": 10000.0,
echo       "firmwareVersion": "v2.1.0"
echo     }
echo   ],
echo   "actuators": [
echo     {
echo       "actuatorId": "作动器_000001",
echo       "actuatorName": "作动器_000001_液压",
echo       "actuatorType": "Hydraulic",
echo       "maxForce": 计算值,
echo       "stroke": 行程值,
echo       "maxVelocity": 500.0,
echo       "boundNodeId": 0,
echo       "boundControlChannel": 0
echo     }
echo   ],
echo   "sensors": [
echo     {
echo       "sensorId": "传感器_000001",
echo       "sensorName": "传感器_000001_Load Cell",
echo       "sensorType": "Force",
echo       "fullScale": 量程值,
echo       "unit": "N",
echo       "boundNodeId": 1,
echo       "boundChannel": 0
echo     }
echo   ]
echo }
echo.

REM 询问是否启动应用程序测试
set /p choice="是否启动应用程序进行数据同步测试? (y/n): "
if /i "%choice%"=="y" (
    echo.
    echo 启动应用程序...
    cd /d "%~dp0build-SiteResConfig_Simple-Desktop_Qt_5_14_2_MinGW_32_bit-Debug\debug"
    if exist "SiteResConfig.exe" (
        start SiteResConfig.exe
        echo 应用程序已启动！
        echo.
        echo ========================================
        echo  数据同步测试重点
        echo ========================================
        echo 1. 创建硬件节点 (右键"硬件节点资源" -> 新建 -> 硬件节点)
        echo 2. 创建作动器组 (右键"作动器资源" -> 新建 -> 作动器组)
        echo 3. 创建作动器 (右键作动器组 -> 新建 -> 作动器)
        echo 4. 创建传感器组 (右键"传感器资源" -> 新建 -> 传感器组)
        echo 5. 创建传感器 (右键传感器组 -> 新建 -> 传感器)
        echo 6. 导出JSON文件 (文件 -> 导出 -> JSON格式)
        echo 7. 检查JSON文件内容是否包含所有设备
        echo 8. 验证中文文件名是否正确显示
        echo.
        echo 预期结果:
        echo - JSON文件名正确显示中文
        echo - hardwareNodes数组包含创建的节点
        echo - actuators数组包含创建的作动器
        echo - sensors数组包含创建的传感器
        echo - 所有枚举值为可读字符串
        echo - 数据类型和数值正确
    ) else (
        echo 错误: 找不到可执行文件，请先重新编译项目
    )
)

echo.
echo JSON导出数据同步问题修复完成！
pause
