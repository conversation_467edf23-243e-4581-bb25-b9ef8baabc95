# SiteResConfig 构建指南

## 🚀 快速开始

### 环境要求
- Qt 5.14.2 或更高版本
- MinGW 7.3.0 或 MSVC 2019
- Windows 7/10/11

### 编译步骤

#### 方法1: 使用批处理脚本（推荐）
```batch
# 运行UI文件版本编译脚本
ultimate_cleanup_compile.bat
```

#### 方法2: 手动编译
```batch
# 设置Qt环境
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

# 进入项目目录
cd SiteResConfig

# 生成UI头文件
uic ui\MainWindow.ui -o ui_MainWindow.h

# 生成Makefile
qmake SiteResConfig_Simple.pro -spec win32-g++

# 编译
mingw32-make clean
mingw32-make -j4
```

## 📁 项目结构

```
SiteResConfig/
├── include/                    # 头文件
├── src/                       # 源文件
├── ui/                        # UI文件
├── sample_configs/            # 示例配置
└── SiteResConfig_Simple.pro   # 项目文件
```

## 🎯 核心功能

- 配置文件管理 (JSON/XML/CSV)
- 数据制作 (6种数据类型)
- 手动控制 (位置/力值/速度)
- 数据导出 (CSV格式)
- 硬件资源管理
- 试验配置管理
- 系统日志管理

## 🔧 开发说明

项目采用标准Qt开发模式：
- **.h** - 头文件声明
- **.cpp** - 源文件实现
- **.ui** - 界面定义文件

使用Qt Designer可视化编辑界面，代码与界面完全分离。
