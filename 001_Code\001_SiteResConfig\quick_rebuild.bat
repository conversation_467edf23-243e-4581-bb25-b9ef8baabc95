@echo off
echo Quick Rebuild for TreeLineStyle Fix
echo ===================================

REM Try to find Qt installation
set QTDIR=
if exist "C:\Qt\5.14.2\mingw73_32\bin\qmake.exe" (
    set QTDIR=C:\Qt\5.14.2\mingw73_32
    set PATH=%QTDIR%\bin;%PATH%
    echo Found Qt at %QTDIR%
) else if exist "D:\Qt\5.14.2\mingw73_32\bin\qmake.exe" (
    set QTDIR=D:\Qt\5.14.2\mingw73_32
    set PATH=%QTDIR%\bin;%PATH%
    echo Found Qt at %QTDIR%
) else (
    echo Qt not found, trying system PATH...
)

REM Go to build directory
cd build-SiteResConfig_Simple-Desktop_Qt_5_14_2_MinGW_32_bit-Debug

REM Try to rebuild
echo Attempting to rebuild...
if exist "Makefile" (
    mingw32-make clean
    mingw32-make
    if %ERRORLEVEL% EQU 0 (
        echo Build successful!
        echo Starting application...
        cd debug
        start SiteResConfig.exe
    ) else (
        echo Build failed!
    )
) else (
    echo Makefile not found, trying qmake...
    qmake ..\SiteResConfig\SiteResConfig_Simple.pro
    if %ERRORLEVEL% EQU 0 (
        mingw32-make
        if %ERRORLEVEL% EQU 0 (
            echo Build successful!
            echo Starting application...
            cd debug
            start SiteResConfig.exe
        )
    )
)

pause
