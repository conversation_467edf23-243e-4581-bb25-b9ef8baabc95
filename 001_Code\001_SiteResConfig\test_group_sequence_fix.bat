@echo off
echo ========================================
echo  测试组序号从1开始递增修复
echo ========================================

REM 设置Qt环境变量
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目（测试组序号从1开始递增修复）...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo 请检查上面的错误信息。
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！组序号从1开始递增修复完成
    echo ========================================
    
    echo.
    echo ✅ 修复的问题:
    echo - 传感器组序号从1开始递增（不再使用哈希值）
    echo - 作动器组序号从1开始递增（不再使用i+1）
    echo - 确保组序号连续，无跳跃
    echo - 传感器详细配置去掉"传感器名称"列
    echo.
    echo 🔧 修复详情:
    echo 1. 传感器组序号生成：使用递增变量groupSequence
    echo 2. 作动器组序号生成：使用递增变量groupSequence
    echo 3. 组序号递增：只有包含设备的组才递增序号
    echo 4. 列数调整：传感器详细配置为34列
    echo.
    echo 📊 传感器详细配置34列格式:
    echo 组信息（2列）:
    echo - 组序号（从1开始递增）, 传感器组名称
    echo.
    echo 传感器信息（32列）:
    echo - 传感器序号, 传感器序列号, 传感器类型, EDS标识, 尺寸
    echo - 型号, 量程, 精度, 单位, 灵敏度, 校准启用, 校准日期
    echo - 校准执行人, 单位类型, 单位值, 输入范围, 满量程最大值
    echo - 满量程最大值单位, 满量程最小值, 满量程最小值单位
    echo - 极性, 前置放大增益, 后置放大增益, 总增益, Delta K增益
    echo - 比例因子, 启用激励, 激励电压, 激励平衡, 激励频率
    echo - 相位, 编码器分辨率
    echo.
    echo 📊 作动器详细配置17列格式:
    echo 组信息（2列）:
    echo - 组序号（从1开始递增）, 作动器组名称
    echo.
    echo 作动器信息（15列）:
    echo - 作动器序号, 作动器序列号, 作动器类型, Unit类型, Unit值
    echo - 行程(m), 位移(m), 拉伸面积(m²), 压缩面积(m²), 极性
    echo - Deliver(V), 频率(Hz), 输出倍数, 平衡(V), 备注
    echo.
    echo 🎯 组序号规则:
    echo - 起始值: 从1开始
    echo - 递增规则: 只有包含设备的组才分配序号
    echo - 连续性: 序号连续，无跳跃
    echo - 一致性: 传感器组和作动器组使用相同规则
    echo.
    
    if exist "SiteResConfig.exe" (
        echo 启动程序验证功能...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 启动程序验证功能...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 启动程序验证功能...
        start release\SiteResConfig.exe
    )
)

echo.
echo 📖 详细测试指南:
echo.
echo 🎮 传感器组序号测试:
echo 1. 启动软件后，新建一个项目
echo 2. 创建多个传感器组（如载荷、位置、压力）
echo 3. 在每个组中添加传感器
echo 4. 导出传感器详细信息到Excel
echo 5. 验证组序号从1开始：1, 2, 3, 4...
echo 6. 验证没有"传感器名称"列
echo.
echo 🎮 作动器组序号测试:
echo 1. 创建多个作动器组
echo 2. 在每个组中添加作动器
echo 3. 导出作动器详细信息到Excel
echo 4. 验证组序号从1开始：1, 2, 3, 4...
echo 5. 验证组序号连续，无跳跃
echo.
echo 🎮 组序号连续性测试:
echo 1. 创建5个传感器组
echo 2. 只在第1、3、5个组中添加传感器
echo 3. 导出Excel文件
echo 4. 验证组序号为：1, 2, 3（连续，无跳跃）
echo 5. 验证空组不分配序号
echo.
echo 🎮 Excel格式验证:
echo 1. 检查传感器详细配置为34列
echo 2. 验证第1列为"组序号"，第2列为"传感器组名称"
echo 3. 验证第3列为"传感器序号"，第4列为"传感器序列号"
echo 4. 确认没有"传感器名称"列
echo 5. 验证组序号数值正确（1, 2, 3...）
echo.
echo ✅ 预期结果:
echo - 传感器组序号从1开始递增
echo - 作动器组序号从1开始递增
echo - 组序号连续，无跳跃
echo - 传感器详细配置为34列格式
echo - 没有"传感器名称"列
echo - Excel格式专业，数据正确
echo.
echo 🚨 如果测试失败:
echo - 检查组序号是否从1开始
echo - 验证组序号是否连续
echo - 确认列数是否正确
echo - 检查是否还有"传感器名称"列
echo.
pause
