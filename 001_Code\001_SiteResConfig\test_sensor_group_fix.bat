@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🔧 传感器组序号和排序问题修复测试
echo ========================================
echo.

echo 🎯 问题描述:
echo 根据用户反馈，XLSX导出时传感器详细配置存在以下问题：
echo 1. 组序号为4的数据和第1行、第2行数据应该是一组
echo 2. 组序号为2、3、5的数据应该是一组  
echo 3. 数据的顺序不对
echo.

echo 🔍 问题分析:
echo 问题根源在于数据来源的不一致：
echo - SensorDataManager::getAllSensorGroups() 重新分配组序号
echo - 但重新分配的逻辑与实际数据结构不匹配
echo.

echo 🔧 修复方案:
echo.
echo 1. ✅ 修复 SensorDataManager::getAllSensorGroups()
echo    - 不再重新分配组序号
echo    - 保持原有组ID，只进行排序
echo.
echo 2. ✅ 修复 XLSDataExporter::exportCompleteProject()  
echo    - 使用连续的显示序号 (groupIndex + 1)
echo    - 确保组序号从1开始连续递增
echo.
echo 3. ✅ 添加详细调试信息
echo    - 输出传感器组的详细信息
echo    - 便于排查数据问题
echo.

echo 📊 修复详情:
echo.
echo 修复前的问题代码:
echo ```cpp
echo // SensorDataManager::getAllSensorGroups() 中的问题代码
echo for (int i = 0; i ^< originalGroups.size(); ++i) {
echo     UI::SensorGroup group = originalGroups[i];
echo     group.groupId = i + 1; // ❌ 重新分配组序号，破坏了原有结构
echo     groups.append(group);
echo }
echo ```
echo.
echo 修复后的代码:
echo ```cpp
echo // 🔄 修复：直接从内存存储获取所有组，保持原有组ID不变
echo for (auto it = groupStorage_.begin(); it != groupStorage_.end(); ++it) {
echo     groups.append(it.value());
echo }
echo // 🔄 修复：只按组ID排序，不重新分配序号
echo std::sort(groups.begin(), groups.end(), ...);
echo ```
echo.

echo 🎯 预期效果:
echo 1. 传感器组序号将按照数据管理器中的实际组ID显示
echo 2. 组内传感器按照正确的顺序排列
echo 3. 组名称只在每组第一行显示
echo 4. 数据结构与实际的传感器组织结构保持一致
echo.

echo 🧪 测试建议:
echo 1. 重新编译项目
echo 2. 使用"保存工程"功能导出XLSX文件
echo 3. 检查传感器详细配置工作表中的组序号和数据排序
echo 4. 查看调试输出中的传感器组信息
echo.

echo ✅ 修复完成！
echo 请重新测试XLSX导出功能，检查传感器组序号和数据排序是否正确。
echo.
pause
