/**
 * @file test_channel_config_from_memory.cpp
 * @brief 测试CH1、CH2数据来源自内存数据的功能
 */

#include <QCoreApplication>
#include <QDebug>
#include <QFile>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include "CtrlChanDataManager.h"

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "=== 测试CH1、CH2数据来源自内存数据 ===";
    
    // 创建控制通道数据管理器
    CtrlChanDataManager ctrlChanManager;
    
    // 创建测试控制通道组
    UI::ControlChannelGroup testGroup;
    testGroup.groupId = 1;
    testGroup.groupName = "测试控制通道组";
    testGroup.groupType = "控制通道";
    testGroup.createTime = "2025-08-25 10:00:00";
    testGroup.groupNotes = "测试用控制通道组";
    
    // 添加CH1通道
    UI::ControlChannelParams ch1;
    ch1.channelId = "CH1";
    ch1.channelName = "CH1";
    ch1.hardwareAssociation = "LD-B1 - CH1";
    ch1.load1Sensor = "载荷传感器1";
    ch1.load2Sensor = "载荷传感器2";
    ch1.positionSensor = "位置传感器";
    ch1.controlActuator = "加载电机";
    ch1.notes = "CH1测试通道";
    testGroup.channels.push_back(ch1);
    
    // 添加CH2通道
    UI::ControlChannelParams ch2;
    ch2.channelId = "CH2";
    ch2.channelName = "CH2";
    ch2.hardwareAssociation = "LD-B2 - CH2";
    ch2.load1Sensor = "载荷传感器1";
    ch2.load2Sensor = "载荷传感器2";
    ch2.positionSensor = "位置传感器";
    ch2.controlActuator = "位移电机";
    ch2.notes = "CH2测试通道";
    testGroup.channels.push_back(ch2);
    
    // 创建控制通道组
    if (ctrlChanManager.createControlChannelGroup(testGroup)) {
        qDebug() << "✅ 控制通道组创建成功";
    } else {
        qDebug() << "❌ 控制通道组创建失败";
        return -1;
    }
    
    // 验证控制通道组数据
    auto groups = ctrlChanManager.getAllControlChannelGroups();
    qDebug() << QString("📊 获取到 %1 个控制通道组").arg(groups.size());
    
    if (!groups.isEmpty()) {
        const auto& group = groups.first();
        qDebug() << QString("📋 组名: %1, 通道数: %2")
                    .arg(QString::fromStdString(group.groupName))
                    .arg(group.channels.size());
        
        for (const auto& channel : group.channels) {
            qDebug() << QString("   通道ID: %1, 通道名: %2, 硬件关联: %3")
                        .arg(QString::fromStdString(channel.channelId))
                        .arg(QString::fromStdString(channel.channelName))
                        .arg(QString::fromStdString(channel.hardwareAssociation));
        }
    }
    
    qDebug() << "✅ 测试完成，CH1、CH2数据已正确存储在内存数据管理器中";
    
    return 0;
}