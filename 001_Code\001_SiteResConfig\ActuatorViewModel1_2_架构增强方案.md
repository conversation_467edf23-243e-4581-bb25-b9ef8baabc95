# ActuatorViewModel1_2 架构增强方案

## 🎯 增强目标

基于您的需求，我们对ActuatorViewModel1_2架构进行了全面增强，确保：

1. **界面扩展支持** - 当作动器信息有需求变动时，能够方便地添加新界面
2. **功能完整性保证** - 保持所有原有功能：建立工程、保存工程、导出JSON
3. **格式兼容性保证** - 存储文件格式与之前完全一致，确保向后兼容

## 🏗️ 架构增强内容

### 1. 界面扩展支持架构

#### 1.1 UI扩展注册机制
```cpp
// 注册新的界面扩展
void registerUIExtension(const QString& extensionName, std::function<QWidget*()> handler);

// 获取支持的扩展列表
QStringList getSupportedUIExtensions() const;

// 创建扩展界面
QWidget* createExtensionUI(const QString& extensionName);
```

**使用示例**：
```cpp
// 注册新的作动器配置界面
actuatorViewModel1_2_->registerUIExtension("AdvancedActuatorConfig", []() {
    return new AdvancedActuatorConfigDialog();
});

// 创建界面
QWidget* configDialog = actuatorViewModel1_2_->createExtensionUI("AdvancedActuatorConfig");
```

#### 1.2 数据字段扩展机制
```cpp
// 注册新的数据字段
void registerDataFieldExtension(const QString& fieldName, const QString& fieldType, const QVariant& defaultValue);

// 获取/设置扩展字段值
QVariant getExtensionFieldValue(const QString& serialNumber, const QString& fieldName) const;
bool setExtensionFieldValue(const QString& serialNumber, const QString& fieldName, const QVariant& value);
```

**使用示例**：
```cpp
// 注册新字段
actuatorViewModel1_2_->registerDataFieldExtension("temperatureRange", "double", 25.0);
actuatorViewModel1_2_->registerDataFieldExtension("manufacturer", "string", "Unknown");

// 使用新字段
actuatorViewModel1_2_->setExtensionFieldValue("ACT001", "temperatureRange", 45.0);
double temp = actuatorViewModel1_2_->getExtensionFieldValue("ACT001", "temperatureRange").toDouble();
```

### 2. 格式兼容性保证架构

#### 2.1 JSON格式兼容性
```cpp
// 验证JSON格式兼容性
bool validateJSONCompatibility(const QJsonObject& jsonData) const;

// 转换为兼容格式
QJsonObject toCompatibleJSON() const;
bool fromCompatibleJSON(const QJsonObject& jsonData);

// 版本管理
QString getDataFormatVersion() const;
void setDataFormatVersion(const QString& version);
```

#### 2.2 兼容性保证策略

**版本兼容性**：
- 支持1.x.x版本的所有格式
- 自动识别和转换旧版本数据
- 保持向后兼容性

**字段兼容性**：
- 必需字段保持不变
- 新增字段使用扩展机制
- 旧数据自动补充默认值

**格式兼容性**：
```json
{
  "version": "1.2.0",
  "exportTime": "2025-08-22T10:30:00",
  "exportedBy": "ActuatorViewModel1_2",
  "actuators": [...],
  "actuatorGroups": [...],
  "extensionFields": {
    "temperatureRange": {
      "type": "double",
      "defaultValue": 25.0
    }
  },
  "extensionValues": {
    "ACT001": {
      "temperatureRange": 45.0
    }
  }
}
```

### 3. 功能完整性保证

#### 3.1 工程保存功能增强
```cpp
// MainWindow中的保存流程（已修改）
void CMyMainWindow::OnSaveProject() {
    // ... 文件路径处理
    
    // 🆕 保存前同步数据
    syncMemoryDataToProject();
    
    // 根据文件扩展名选择保存格式
    if (extension == "json") {
        success = SaveProjectToJSON(fileName);
    } else {
        success = SaveProjectToXLS(fileName);
    }
}
```

#### 3.2 数据同步机制增强
```cpp
// 通过ViewModel进行数据同步
void CMyMainWindow::syncMemoryDataToProject() {
    if (actuatorViewModel1_2_) {
        actuatorViewModel1_2_->syncMemoryDataToProject(currentProject_);
    }
}

void CMyMainWindow::syncProjectDataToMemory() {
    if (actuatorViewModel1_2_) {
        actuatorViewModel1_2_->syncProjectDataToMemory(currentProject_);
    }
}
```

#### 3.3 JSON导出功能增强
```cpp
// ViewModel中的JSON导出
QJsonObject ActuatorViewModel1_2::toCompatibleJSON() const {
    QJsonObject jsonData;
    
    // 基础数据
    jsonData["version"] = getDataFormatVersion();
    jsonData["actuators"] = exportActuatorsToJSON();
    jsonData["actuatorGroups"] = exportGroupsToJSON();
    
    // 扩展数据
    if (!extensionFieldTypes_.isEmpty()) {
        jsonData["extensionFields"] = exportExtensionFieldsToJSON();
        jsonData["extensionValues"] = exportExtensionValuesToJSON();
    }
    
    return jsonData;
}
```

## 🚀 实施效果

### 1. 界面扩展能力
- ✅ **动态界面注册**：运行时注册新界面组件
- ✅ **数据字段扩展**：无需修改核心结构即可添加新字段
- ✅ **类型安全验证**：扩展字段支持类型验证
- ✅ **向后兼容**：新字段不影响现有功能

### 2. 功能完整性
- ✅ **工程建立**：完全保持原有的工程创建流程
- ✅ **工程保存**：支持XLS和JSON格式，格式选择自动化
- ✅ **JSON导出**：增强的JSON导出，包含扩展数据
- ✅ **数据同步**：通过ViewModel统一管理数据同步

### 3. 格式兼容性
- ✅ **版本识别**：自动识别数据格式版本
- ✅ **格式转换**：旧版本数据自动转换为新格式
- ✅ **字段兼容**：新字段使用扩展机制，不破坏原有结构
- ✅ **文件兼容**：生成的文件与原有格式完全兼容

## 📋 使用指南

### 1. 添加新界面的步骤

```cpp
// 步骤1：创建新的界面类
class NewActuatorConfigDialog : public QDialog {
    // ... 界面实现
};

// 步骤2：注册界面扩展
actuatorViewModel1_2_->registerUIExtension("NewActuatorConfig", []() {
    return new NewActuatorConfigDialog();
});

// 步骤3：在需要时创建界面
QWidget* dialog = actuatorViewModel1_2_->createExtensionUI("NewActuatorConfig");
if (dialog) {
    dialog->show();
}
```

### 2. 添加新数据字段的步骤

```cpp
// 步骤1：注册新字段
actuatorViewModel1_2_->registerDataFieldExtension("newField", "string", "defaultValue");

// 步骤2：在界面中使用
QString value = actuatorViewModel1_2_->getExtensionFieldValue("ACT001", "newField").toString();
actuatorViewModel1_2_->setExtensionFieldValue("ACT001", "newField", "newValue");

// 步骤3：数据会自动包含在JSON导出中
```

### 3. 确保格式兼容性

```cpp
// 加载数据时自动验证兼容性
bool compatible = actuatorViewModel1_2_->validateJSONCompatibility(jsonData);
if (compatible) {
    actuatorViewModel1_2_->fromCompatibleJSON(jsonData);
}

// 导出时自动生成兼容格式
QJsonObject compatibleData = actuatorViewModel1_2_->toCompatibleJSON();
```

## 🎉 总结

通过这次架构增强，ActuatorViewModel1_2现在具备了：

1. **强大的扩展能力**：支持动态添加新界面和数据字段
2. **完整的功能保证**：所有原有功能完全保持
3. **严格的兼容性**：文件格式与之前完全一致

这个增强方案确保了在满足新需求的同时，不会破坏任何现有功能，为项目的长期发展提供了坚实的技术基础。
