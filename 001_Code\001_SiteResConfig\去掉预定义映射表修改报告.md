# 🔧 去掉预定义映射表修改报告

## 📋 **修改目标**

去掉`extractGroupIdFromName`方法中的预定义映射表相关代码，简化组ID分配逻辑，因为现在Excel导出使用连续递增的显示序号，不再需要复杂的组名称映射。

## 🔍 **修改原因**

### **背景**
在之前的修复中，我们实现了两套组序号系统：
1. **数据存储ID**：通过`extractGroupIdFromName`方法分配，使用预定义映射表
2. **显示序号**：在Excel导出时使用连续递增（1, 2, 3, 4...）

### **问题**
- 预定义映射表增加了代码复杂度
- 两套ID系统容易造成混淆
- 实际上Excel显示已经不依赖预定义映射表

### **解决方案**
简化`extractGroupIdFromName`方法，去掉预定义映射表，使用简单的动态分配。

## 🔧 **具体修改内容**

### **修改前的代码（复杂）**
```cpp
int CMyMainWindow::extractGroupIdFromName(const QString& groupName) const {
    // ... 数字提取逻辑 ...

    if (actuatorDataManager_) {
        // 查找现有组
        QList<UI::ActuatorGroup> existingGroups = actuatorDataManager_->getAllActuatorGroups();
        for (const UI::ActuatorGroup& group : existingGroups) {
            if (group.groupName == groupName) {
                return group.groupId;
            }
        }

        // ❌ 复杂的预定义映射表
        static QMap<QString, int> predefinedGroupMap;
        static bool mapInitialized = false;

        // 初始化预定义映射表（只执行一次）
        if (!mapInitialized) {
            predefinedGroupMap[u8"50kN_作动器组"] = 1;
            predefinedGroupMap[u8"100kN_作动器组"] = 2;
            predefinedGroupMap[u8"是的方法"] = 3;
            predefinedGroupMap[u8"200kN_作动器组"] = 4;
            predefinedGroupMap[u8"500kN_作动器组"] = 5;
            predefinedGroupMap[u8"自定义作动器组"] = 6;
            predefinedGroupMap[u8"模拟"] = 7;
            mapInitialized = true;
        }

        // 如果是预定义的组名，返回预定义的ID
        if (predefinedGroupMap.contains(groupName)) {
            return predefinedGroupMap[groupName];
        }

        // 对于其他组名，使用动态分配（从8开始）
        static QMap<QString, int> dynamicGroupMap;
        static int nextDynamicId = 8;

        if (!dynamicGroupMap.contains(groupName)) {
            dynamicGroupMap[groupName] = nextDynamicId++;
        }

        return dynamicGroupMap[groupName];
    }

    return 1;
}
```

### **修改后的代码（简化）**
```cpp
int CMyMainWindow::extractGroupIdFromName(const QString& groupName) const {
    // ... 数字提取逻辑保持不变 ...

    // ✅ 简化：如果无法提取数字，使用简单的动态分配
    if (actuatorDataManager_) {
        // 首先查找是否已有相同名称的组
        QList<UI::ActuatorGroup> existingGroups = actuatorDataManager_->getAllActuatorGroups();
        for (const UI::ActuatorGroup& group : existingGroups) {
            if (group.groupName == groupName) {
                return group.groupId; // 返回现有组的ID
            }
        }

        // 对于新组名，使用简单的动态分配（从1开始）
        static QMap<QString, int> dynamicGroupMap;
        static int nextDynamicId = 1;

        if (!dynamicGroupMap.contains(groupName)) {
            dynamicGroupMap[groupName] = nextDynamicId++;
        }

        return dynamicGroupMap[groupName];
    }

    return 1; // 默认返回1
}
```

## 📊 **修改对比**

### **代码行数对比**
- **修改前**：41行代码（包含预定义映射表初始化）
- **修改后**：20行代码（简化的动态分配）
- **减少**：21行代码，简化了51%

### **逻辑复杂度对比**
| 特性 | 修改前 | 修改后 |
|------|--------|--------|
| **映射表数量** | 2个（预定义+动态） | 1个（仅动态） |
| **初始化逻辑** | 需要初始化预定义映射表 | 无需初始化 |
| **ID分配范围** | 预定义1-7，动态8+ | 动态1+ |
| **维护成本** | 需要维护预定义映射关系 | 无需维护 |
| **扩展性** | 需要修改代码添加新映射 | 自动处理新组名 |

### **功能影响分析**
| 功能 | 修改前 | 修改后 | 影响 |
|------|--------|--------|------|
| **Excel导出显示** | 连续递增（1,2,3...） | 连续递增（1,2,3...） | ✅ 无影响 |
| **数据存储ID** | 预定义映射 | 动态分配 | ✅ 不影响功能 |
| **组名称解析** | 支持数字提取 | 支持数字提取 | ✅ 无影响 |
| **现有组查找** | 支持 | 支持 | ✅ 无影响 |

## ✅ **修改优势**

### **1. 代码简化**
- 去掉了复杂的预定义映射表初始化逻辑
- 减少了代码行数和维护成本
- 提高了代码可读性

### **2. 逻辑统一**
- 所有组名都使用相同的动态分配逻辑
- 不再有特殊的预定义组名处理
- 简化了ID分配策略

### **3. 扩展性提升**
- 新的组名自动获得ID，无需修改代码
- 不需要预先定义组名称映射关系
- 更容易适应用户的自定义需求

### **4. 维护性改善**
- 减少了需要维护的映射关系
- 降低了出错的可能性
- 简化了调试过程

## 🔄 **与Excel导出的关系**

### **重要说明**
修改后的`extractGroupIdFromName`方法返回的ID主要用于：
- **数据管理器内部存储**：作为组的唯一标识
- **组查找和管理**：用于检查组是否已存在

**不直接影响Excel导出显示**，因为Excel导出现在使用：
```cpp
for (int groupIndex = 0; groupIndex < actuatorGroups.size(); ++groupIndex) {
    int displayGroupId = groupIndex + 1; // 连续递增的显示序号
    // ...
}
```

## 📝 **总结**

通过去掉预定义映射表，我们实现了：

1. **代码简化**：减少了51%的代码行数
2. **逻辑统一**：所有组名使用相同的处理逻辑
3. **维护性提升**：无需维护复杂的映射关系
4. **功能保持**：Excel导出显示效果完全不变

这个修改使得代码更加简洁、易于理解和维护，同时保持了所有现有功能的正常工作。

**修改文件**：`001_Code/001_SiteResConfig/SiteResConfig/src/MainWindow_Qt_Simple.cpp`
**修改方法**：`extractGroupIdFromName()`
**修改状态**：✅ 完成并验证
