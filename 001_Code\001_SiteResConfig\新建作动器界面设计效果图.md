# 📋 新建作动器界面设计效果图

## 🎯 整体布局设计

```
┌─────────────────────────────────────────────────────────────────────────────┐
│  📝 新建作动器 - 配置参数                                            [❌]  │
├─────────────────────────────────────────────────────────────────────────────┤
│  🗂️ 作动器类型: [液压作动器 ▼]                              [ℹ️ 提示]    │
│     ├─ 液压作动器                                                          │
│     ├─ 电动作动器                                                          │
│     ├─ 气动作动器                                                          │
│     └─ 伺服作动器                                                          │
├─────────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────┐  ┌─────────────────────────────────┐   │
│  │  🔧 Actuator                   │  │  ⚡ 伺服控制器设置 Value        │   │
│  │                                │  │                                │   │
│  │  📝 Serial Number:             │  │  🔄 Polarity: [Positive ▼]    │   │
│  │  [_____________________]       │  │                                │   │
│  │                                │  │  📤 Deliver:   [0.000] V [+][-]│   │
│  │  📏 Unit Length: [____] [m ▼]  │  │                                │   │
│  │                                │  │  📊 Frequency: [528.00] Hz     │   │
│  │  ↔️ Stroke:      [0.30] m      │  │                        [+][-]  │   │
│  │                        [+][-]  │  │                                │   │
│  │                                │  │  📈 Output Multiplier: [1.000] │   │
│  │  📐 Displacement: [0.30] m     │  │                        [+][-]  │   │
│  │                        [+][-]  │  │                                │   │
│  │                                │  │  ⚖️ Balance:   [0.000] V       │   │
│  │  🔺 Tension Area: [0.60] m²    │  │                        [+][-]  │   │
│  │                        [+][-]  │  │                                │   │
│  │                                │  │  [🔄 Auto Balance]             │   │
│  │  🔻 Compression Area: [0.60] m²│  │                                │   │
│  │                        [+][-]  │  │                                │   │
│  └─────────────────────────────────┘  └─────────────────────────────────┘   │
├─────────────────────────────────────────────────────────────────────────────┤
│  [💾 保存并添加] [❌ 取消] [👁️ 预览配置] [❓ 帮助]                        │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 📊 详细控件规格

### 🎯 对话框主体
- **尺寸**: 800x600 像素
- **标题**: "新建作动器 - 配置参数"
- **模态**: 是
- **可调整大小**: 否

### 🗂️ 顶部分类区域 (800x80)
```
作动器类型: [液压作动器 ▼]                    [ℹ️]
```
- **组合框选项**:
  - 液压作动器 (默认选中)
  - 电动作动器
  - 气动作动器
  - 伺服作动器
- **提示按钮**: 显示各类型作动器的特点说明

### 🔧 左侧面板 - Actuator 基本参数 (380x380)

#### 控件列表:
1. **Serial Number** (序列号)
   - 类型: QLineEdit
   - 占位符: "请输入作动器序列号"
   - 验证: 非空，唯一性检查

2. **Unit Length** (单位长度)
   - 类型: QDoubleSpinBox + QComboBox
   - 单位选项: m, mm, cm, inch
   - 默认: m

3. **Stroke** (行程)
   - 类型: QDoubleSpinBox + 增减按钮
   - 默认值: 0.30 m
   - 范围: 0.01 - 10.00 m
   - 精度: 0.01

4. **Displacement** (位移)
   - 类型: QDoubleSpinBox + 增减按钮
   - 默认值: 0.30 m
   - 范围: 0.01 - 10.00 m
   - 精度: 0.01

5. **Tension Area** (拉伸面积)
   - 类型: QDoubleSpinBox + 增减按钮
   - 默认值: 0.60 m²
   - 范围: 0.01 - 100.00 m²
   - 精度: 0.01

6. **Compression Area** (压缩面积)
   - 类型: QDoubleSpinBox + 增减按钮
   - 默认值: 0.60 m²
   - 范围: 0.01 - 100.00 m²
   - 精度: 0.01

### ⚡ 右侧面板 - 伺服控制器参数 (380x380)

#### 控件列表:
1. **Polarity** (极性)
   - 类型: QComboBox
   - 选项: Positive (默认), Negative
   - 工具提示: "设置伺服控制器的极性"

2. **Deliver** (输出)
   - 类型: QDoubleSpinBox + 增减按钮
   - 默认值: 0.000 V
   - 范围: -10.000 - 10.000 V
   - 精度: 0.001

3. **Frequency** (频率)
   - 类型: QDoubleSpinBox + 增减按钮
   - 默认值: 528.00 Hz
   - 范围: 1.00 - 10000.00 Hz
   - 精度: 0.01

4. **Output Multiplier** (输出倍数)
   - 类型: QDoubleSpinBox + 增减按钮
   - 默认值: 1.000
   - 范围: 0.001 - 1000.000
   - 精度: 0.001

5. **Balance** (平衡)
   - 类型: QDoubleSpinBox + 增减按钮
   - 默认值: 0.000 V
   - 范围: -10.000 - 10.000 V
   - 精度: 0.001

6. **Auto Balance** (自动平衡)
   - 类型: QPushButton
   - 功能: 自动计算并设置平衡值
   - 图标: 🔄

### 💾 底部操作区域 (800x70)

#### 按钮布局:
```
[💾 保存并添加]  [❌ 取消]  [👁️ 预览配置]  [❓ 帮助]
```

1. **保存并添加** - 主要操作按钮
   - 验证所有输入
   - 保存作动器配置
   - 添加到硬件树

2. **取消** - 取消操作
   - 关闭对话框
   - 不保存任何更改

3. **预览配置** - 预览功能
   - 显示配置摘要
   - 验证参数合理性

4. **帮助** - 帮助信息
   - 显示使用说明
   - 参数配置指导

## 🎨 界面样式设计

### 颜色方案:
- **主背景**: #f8f9fa (浅灰)
- **标题栏**: #007bff (蓝色)
- **左侧面板**: #fff8e1 (浅橙)
- **右侧面板**: #fce4ec (浅粉)
- **按钮区域**: #e8f5e8 (浅绿)

### 字体设置:
- **标题**: 14pt, 粗体
- **标签**: 10pt, 常规
- **输入框**: 10pt, 常规
- **按钮**: 10pt, 粗体

### 间距设置:
- **面板间距**: 20px
- **控件间距**: 15px
- **边距**: 10px

## 🔄 交互逻辑

### 分类选择联动:
- 选择不同作动器类型时，自动调整默认参数
- 显示对应类型的参数说明和建议值

### 参数验证:
- 实时验证输入值的合理性
- 显示参数范围提示
- 高亮显示无效输入

### 智能计算:
- 根据基本参数自动计算相关值
- 提供参数建议和优化提示

## 📱 响应式设计

### 最小尺寸: 600x450
### 最大尺寸: 1200x800
### 自适应布局: 支持不同分辨率显示

这个设计将您提供的两个子界面完美整合到一个统一的"新建作动器"对话框中，提供直观、专业的用户体验。
