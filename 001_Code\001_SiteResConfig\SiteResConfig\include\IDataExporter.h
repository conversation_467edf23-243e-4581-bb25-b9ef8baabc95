#ifndef IDATAEXPORTER_H
#define IDATAEXPORTER_H

#include <QString>
#include <QTreeWidget>
#include <QJsonArray>
#include "SensorDialog_1_2.h"

// 前向声明作动器相关结构体
namespace UI {
    struct ActuatorGroup_1_2;
}

/**
 * @brief 数据导出器接口
 * @details 定义数据导出的通用接口，支持多种导出格式
 */
class IDataExporter {
public:
    virtual ~IDataExporter() = default;

    /**
     * @brief 导出完整项目数据（硬件树 + 传感器详细信息）
     * @param treeWidget 硬件树控件
     * @param filePath 导出文件路径
     * @return 导出是否成功
     */
    virtual bool exportCompleteProject(const QString& filePath) = 0;
    
    /**
     * @brief 获取支持的文件扩展名
     * @return 文件扩展名（如 "csv", "json"）
     */
    virtual QString getSupportedExtension() const = 0;
    
    /**
     * @brief 获取格式描述
     * @return 格式描述字符串
     */
    virtual QString getFormatDescription() const = 0;
    
    /**
     * @brief 获取最后的错误信息
     * @return 错误信息字符串
     */
    virtual QString getLastError() const = 0;

    /**
     * @brief 从文件导入数据到硬件树（可选接口）
     * @param filePath 导入文件路径
     * @param treeWidget 目标硬件树控件
     * @return 导入是否成功
     */
    virtual bool importToHardwareTree(const QString& filePath, QTreeWidget* treeWidget) {
        Q_UNUSED(filePath);
        Q_UNUSED(treeWidget);
        return false;
    }
};

#endif // IDATAEXPORTER_H
