# ActuatorDataManager 到 ActuatorViewModel1_2 迁移计划

## 🎯 迁移目标

将MainWindow_Qt_Simple.cpp中所有直接使用`actuatorDataManager_`的代码修改为使用`actuatorViewModel1_2_`，实现完全的解耦合。

## 📊 需要修改的代码统计

根据搜索结果，发现了**39处**需要修改的代码，主要分布在以下功能模块：

### 1. 数据获取方法（15处）
- `actuatorDataManager_->getAllActuatorGroups()` → `actuatorViewModel1_2_->getAllActuatorGroups()`
- `actuatorDataManager_->getAllActuators()` → `actuatorViewModel1_2_->getAllActuators()`
- `actuatorDataManager_->getActuator()` → `actuatorViewModel1_2_->getActuator()`
- `actuatorDataManager_->hasActuator()` → `actuatorViewModel1_2_->hasActuator()`

### 2. 数据修改方法（8处）
- `actuatorDataManager_->addActuator()` → `actuatorViewModel1_2_->saveActuator()`
- `actuatorDataManager_->updateActuator()` → `actuatorViewModel1_2_->updateActuator()`
- `actuatorDataManager_->removeActuator()` → `actuatorViewModel1_2_->removeActuator()`
- `actuatorDataManager_->saveActuatorGroup()` → `actuatorViewModel1_2_->saveActuatorGroup()`

### 3. 数据验证方法（6处）
- `actuatorDataManager_->validateActuatorInGroup()` → `actuatorViewModel1_2_->validateActuatorInGroup()`
- `actuatorDataManager_->isSerialNumberUniqueInGroup()` → `actuatorViewModel1_2_->isSerialNumberUniqueInGroup()`

### 4. 错误处理方法（10处）
- `actuatorDataManager_->getLastError()` → `actuatorViewModel1_2_->getLastError()`

## 🔧 标准迁移模式

### 模式1：基础方法调用
```cpp
// 修改前
if (actuatorDataManager_) {
    result = actuatorDataManager_->someMethod(params);
}

// 修改后
if (actuatorViewModel1_2_) {
    result = actuatorViewModel1_2_->someMethod(params);
}
```

### 模式2：错误处理
```cpp
// 修改前
if (!actuatorDataManager_->someMethod(params)) {
    QString error = actuatorDataManager_->getLastError();
    // 处理错误
}

// 修改后
if (!actuatorViewModel1_2_->someMethod(params)) {
    QString error = actuatorViewModel1_2_->getLastError();
    // 处理错误
}
```

### 模式3：数据获取和遍历
```cpp
// 修改前
QList<UI::ActuatorGroup> groups = actuatorDataManager_->getAllActuatorGroups();
for (const auto& group : groups) {
    // 处理逻辑
}

// 修改后
QList<UI::ActuatorGroup> groups = actuatorViewModel1_2_->getAllActuatorGroups();
for (const auto& group : groups) {
    // 处理逻辑保持不变
}
```

## 📋 具体修改清单

### 第一批：数据获取方法（已部分完成）
- [x] 第1303行：`getAllActuatorGroups().size()` - 统计信息
- [x] 第1422行：`getAllActuatorGroups().size()` - 验证数据
- [x] 第1820行：`getAllActuatorGroups()` - 获取组列表
- [x] 第5178行：`getAllActuatorGroups().size()` - 安全统计
- [ ] 第2721行：`getAllActuatorGroups()` - 创建或更新组
- [ ] 第4198行：`getAllActuatorGroups()` - 统一数据获取
- [ ] 第4675行：`getAllActuatorGroups()` - 填充作动器数据
- [ ] 第5564行：`getAllActuatorGroups()` - 获取组详细信息
- [ ] 第5691行：`getAllActuatorGroups()` - 获取作动器统计
- [ ] 第5908行：`getAllActuatorGroups()` - 查找所属组
- [ ] 第6259行：`getAllActuatorGroups()` - 通过组名查找
- [ ] 第6299行：`getAllActuatorGroups()` - 调试信息
- [ ] 第6328行：`getAllActuatorGroups()` - 查找所属组ID
- [ ] 第6360行：`getAllActuators()` - 显示所有作动器
- [ ] 第6573行：`getAllActuatorGroups()` - 统计信息

### 第二批：数据修改方法
- [ ] 第2802行：`addActuator()` - 添加新作动器
- [ ] 第2810行：`saveActuatorGroup()` - 保存组
- [ ] 第5996行：`removeActuator()` - 删除作动器设备
- [ ] 第6087行：`updateActuator()` - 更新作动器设备

### 第三批：数据验证方法
- [ ] 第2651行：`isSerialNumberUniqueInGroup()` - 检查序列号唯一性
- [ ] 第2761行：`validateActuatorInGroup()` - 验证作动器在组内
- [ ] 第2791行：`validateActuatorInGroup()` - 验证新组
- [ ] 第6078行：`isSerialNumberUniqueInGroup()` - 编辑时验证

### 第四批：数据查询方法
- [ ] 第2701行：`hasActuator()` - 检查作动器存在
- [ ] 第2705行：`getActuator()` - 获取作动器参数
- [ ] 第5881行：`hasActuator()` - 检查设备存在
- [ ] 第5882行：`getActuator()` - 获取设备信息
- [ ] 第6059行：`hasActuator()` - 编辑前检查
- [ ] 第6065行：`getActuator()` - 获取当前参数
- [ ] 第6324行：`hasActuator()` - 调试信息检查
- [ ] 第6325行：`getActuator()` - 调试信息获取

### 第五批：错误处理
- [ ] 第2670行：`getLastError()` - 保存失败错误
- [ ] 第2677行：`getLastError()` - 组保存失败
- [ ] 第2762行：`getLastError()` - 验证失败错误
- [ ] 第2792行：`getLastError()` - 新组验证失败
- [ ] 第2803行：`getLastError()` - 添加失败错误
- [ ] 第2815行：`getLastError()` - 组保存失败
- [ ] 第5998行：`getLastError()` - 删除失败错误
- [ ] 第6089行：`getLastError()` - 更新失败错误

## 🚀 实施策略

### 阶段1：批量替换基础调用
使用查找替换功能进行批量修改：
- `actuatorDataManager_->` → `actuatorViewModel1_2_->`
- `actuatorDataManager_` → `actuatorViewModel1_2_`

### 阶段2：方法名称调整
某些方法名称需要调整：
- `addActuator()` → `saveActuator()`
- 其他方法名称保持一致

### 阶段3：验证和测试
- 编译测试确保无语法错误
- 功能测试确保行为一致
- 性能测试确保无性能下降

## ⚠️ 注意事项

### 1. 方法名称差异
- `ActuatorDataManager::addActuator()` → `ActuatorViewModel1_2::saveActuator()`
- 其他方法名称基本一致

### 2. 错误处理
- 错误处理机制保持一致
- `getLastError()`方法在两个类中都存在

### 3. 数据格式
- 数据结构完全一致
- 返回值类型完全一致
- 参数类型完全一致

### 4. 性能考虑
- ViewModel可能有缓存机制
- 某些操作可能更高效
- 需要验证性能影响

## 📝 验证清单

完成迁移后需要验证：
- [ ] 编译无错误
- [ ] 作动器创建功能正常
- [ ] 作动器编辑功能正常
- [ ] 作动器删除功能正常
- [ ] 作动器组管理功能正常
- [ ] 数据保存和加载功能正常
- [ ] 错误处理机制正常
- [ ] 统计信息显示正常
- [ ] 调试信息功能正常

## 🎯 预期收益

完成迁移后将获得：
1. **完全解耦** - MainWindow不再直接依赖ActuatorDataManager
2. **统一接口** - 所有作动器操作通过ViewModel进行
3. **更好的缓存** - ViewModel提供智能缓存机制
4. **增强功能** - 支持扩展字段和界面扩展
5. **更好的测试** - ViewModel可以独立测试

这个迁移是实现MVVM架构的关键步骤，将为项目的长期维护和扩展奠定坚实基础。
