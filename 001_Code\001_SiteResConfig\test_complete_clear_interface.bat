@echo off
echo ========================================
echo  彻底清空界面数据功能测试
echo ========================================

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o
if exist "ui_*.h" del ui_*.h

echo.
echo 生成UI头文件...
uic ui\MainWindow.ui -o ui_MainWindow.h
if errorlevel 1 (
    echo UI文件生成失败！
    pause
    exit /b 1
) else (
    echo ✅ UI头文件生成成功: ui_MainWindow.h
)

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo.
    echo 请检查以下可能的问题：
    echo 1. ClearInterfaceData函数修改
    echo 2. 界面清空逻辑
    echo 3. 工程对象管理
    echo 4. 窗口标题重置
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！彻底清空界面功能已实现
    echo ========================================
    
    if exist "SiteResConfig.exe" (
        echo 可执行文件: SiteResConfig.exe
        echo.
        echo ✅ 彻底清空界面数据功能已实现！
        echo.
        echo 🧹 界面清空增强功能:
        echo ├─ 完全清空: 使用clear()方法完全清空树形控件
        echo ├─ 立即执行: 用户确认新建工程后立即清空
        echo ├─ 状态重置: 清空工程对象和窗口标题
        echo ├─ 日志保留: 保留操作日志便于调试
        echo └─ 重新初始化: 清空后重新创建基本结构
        echo.
        echo 🔧 修复的清空问题:
        echo ├─ 问题分析: 之前的清空不够彻底
        echo │  ├─ 只删除子节点，保留了根节点结构
        echo │  ├─ 关联信息可能没有完全清除
        echo │  └─ 工程对象和标题没有及时重置
        echo ├─ 修复方案: 使用完全清空方法
        echo │  ├─ hardwareTreeWidget->clear(): 完全清空硬件树
        echo │  ├─ testConfigTreeWidget->clear(): 完全清空配置树
        echo │  ├─ 删除currentProject_对象: 清空工程信息
        echo │  └─ 重置窗口标题: 恢复默认标题
        echo └─ 修复结果: ✅ 界面完全清空，无残留信息
        echo.
        echo 📝 新的清空流程:
        echo ├─ 第一步: 检查并提示保存当前数据
        echo ├─ 第二步: 立即清空所有界面数据
        echo │  ├─ 完全清空硬件树: ui->hardwareTreeWidget->clear()
        echo │  ├─ 完全清空配置树: ui->testConfigTreeWidget->clear()
        echo │  ├─ 删除工程对象: delete currentProject_
        echo │  └─ 重置窗口标题: setWindowTitle("SiteResConfig")
        echo ├─ 第三步: 用户选择保存路径
        echo ├─ 第四步: 创建新的工程对象
        echo └─ 第五步: 重新初始化界面基本结构
        echo.
        echo 🔍 清空验证要点:
        echo ├─ 硬件树状态: 应该完全空白，无任何节点
        echo ├─ 配置树状态: 应该完全空白，无任何节点
        echo ├─ 窗口标题: 应该显示"SiteResConfig"
        echo ├─ 工程信息: currentProject_应该为nullptr
        echo └─ 关联信息: 所有拖拽关联应该清空
        echo.
        echo 📋 测试步骤:
        echo.
        echo 🎯 准备测试数据:
        echo 1. 启动程序
        echo 2. 创建一些硬件设备:
        echo    - 右键"作动器" → 新建 → 作动器组 → 选择"50kN_作动器组"
        echo    - 右键"传感器" → 新建 → 传感器组 → 选择"载荷"
        echo    - 右键"硬件节点资源" → 新建硬件节点
        echo 3. 进行一些拖拽关联:
        echo    - 拖拽作动器到"控制"节点
        echo    - 拖拽传感器到"载荷1"节点
        echo 4. 验证界面有数据内容
        echo.
        echo 🎯 测试清空功能:
        echo 1. 点击"文件" → "新建工程"
        echo 2. 观察界面变化:
        echo    - 硬件树应该立即变为完全空白
        echo    - 配置树应该立即变为完全空白
        echo    - 窗口标题应该变为"SiteResConfig"
        echo 3. 检查清空彻底性:
        echo    - 确认没有任何节点残留
        echo    - 确认没有任何关联信息残留
        echo    - 确认工程信息已清空
        echo 4. 完成新建工程流程:
        echo    - 选择保存路径
        echo    - 验证界面重新初始化为基本结构
        echo.
        echo 🎯 测试取消操作:
        echo 1. 创建测试数据后点击"新建工程"
        echo 2. 观察界面立即清空
        echo 3. 在文件保存对话框中点击"取消"
        echo 4. 验证界面保持空白状态（不恢复旧数据）
        echo.
        echo 🔍 关键验证点:
        echo ├─ ✅ 界面立即完全清空（无节点残留）
        echo ├─ ✅ 窗口标题正确重置
        echo ├─ ✅ 工程对象正确清空
        echo ├─ ✅ 关联信息完全清除
        echo ├─ ✅ 取消操作时界面保持空白
        echo └─ ✅ 重新初始化后结构正确
        echo.
        echo 启动程序测试彻底清空界面功能...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
        echo 启动程序...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
        echo 启动程序...
        start release\SiteResConfig.exe
    ) else (
        echo 警告: 找不到可执行文件！
    )
)

echo.
echo 📖 彻底清空界面功能详细测试指南:
echo.
echo 🎯 清空彻底性验证:
echo 1. 数据准备阶段:
echo    - 创建多个作动器组和传感器组
echo    - 创建多个硬件节点
echo    - 进行多个拖拽关联操作
echo    - 确保界面有丰富的数据内容
echo.
echo 2. 清空操作验证:
echo    - 点击"文件" → "新建工程"
echo    - 立即观察界面变化
echo    - 硬件树应该瞬间变为完全空白
echo    - 配置树应该瞬间变为完全空白
echo    - 窗口标题应该变为"SiteResConfig"
echo.
echo 3. 清空完整性检查:
echo    - 确认硬件树没有任何节点（包括根节点）
echo    - 确认配置树没有任何节点（包括根节点）
echo    - 确认所有关联信息都已清除
echo    - 确认窗口标题不显示工程名称
echo.
echo 🎯 重新初始化验证:
echo 1. 完成新建工程流程:
echo    - 选择保存路径并完成创建
echo    - 观察界面重新初始化
echo.
echo 2. 基本结构检查:
echo    - 硬件树应该有基本的根节点结构
echo    - 配置树应该有基本的根节点结构
echo    - 窗口标题应该显示新工程名称
echo.
echo 🎯 边界情况测试:
echo 1. 空界面新建工程:
echo    - 在没有数据的情况下新建工程
echo    - 验证不会出现错误
echo.
echo 2. 取消操作测试:
echo    - 清空后取消文件选择
echo    - 验证界面保持空白状态
echo    - 验证不会恢复之前的数据
echo.
echo 🔍 技术验证点:
echo ✓ clear()方法正确清空所有节点
echo ✓ 工程对象正确删除和重置
echo ✓ 窗口标题正确管理
echo ✓ 内存管理正确（无泄漏）
echo ✓ 界面状态一致性
echo ✓ 操作日志正确记录
echo.
pause
