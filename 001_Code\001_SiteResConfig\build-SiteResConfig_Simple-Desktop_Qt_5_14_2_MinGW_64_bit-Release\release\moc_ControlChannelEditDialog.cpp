/****************************************************************************
** Meta object code from reading C++ file 'ControlChannelEditDialog.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../001_SiteResConfig_OldStruct/SiteResConfig/include/ControlChannelEditDialog.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ControlChannelEditDialog.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_UI__ControlChannelEditDialog_t {
    QByteArrayData data[45];
    char stringdata0[843];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_UI__ControlChannelEditDialog_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_UI__ControlChannelEditDialog_t qt_meta_stringdata_UI__ControlChannelEditDialog = {
    {
QT_MOC_LITERAL(0, 0, 28), // "UI::ControlChannelEditDialog"
QT_MOC_LITERAL(1, 29, 12), // "editFinished"
QT_MOC_LITERAL(2, 42, 0), // ""
QT_MOC_LITERAL(3, 43, 20), // "ControlChannelParams"
QT_MOC_LITERAL(4, 64, 6), // "params"
QT_MOC_LITERAL(5, 71, 8), // "accepted"
QT_MOC_LITERAL(6, 80, 11), // "closeEditor"
QT_MOC_LITERAL(7, 92, 15), // "onAcceptClicked"
QT_MOC_LITERAL(8, 108, 15), // "onCancelClicked"
QT_MOC_LITERAL(9, 124, 6), // "reject"
QT_MOC_LITERAL(10, 131, 17), // "onPolarityChanged"
QT_MOC_LITERAL(11, 149, 20), // "onAssociationChanged"
QT_MOC_LITERAL(12, 170, 22), // "onHardwareGroupChanged"
QT_MOC_LITERAL(13, 193, 25), // "onLoad1SensorGroupChanged"
QT_MOC_LITERAL(14, 219, 25), // "onLoad2SensorGroupChanged"
QT_MOC_LITERAL(15, 245, 28), // "onPositionSensorGroupChanged"
QT_MOC_LITERAL(16, 274, 29), // "onControlActuatorGroupChanged"
QT_MOC_LITERAL(17, 304, 13), // "validateInput"
QT_MOC_LITERAL(18, 318, 17), // "validateAllFields"
QT_MOC_LITERAL(19, 336, 26), // "validateAllFieldsWithFocus"
QT_MOC_LITERAL(20, 363, 9), // "QWidget*&"
QT_MOC_LITERAL(21, 373, 16), // "firstErrorWidget"
QT_MOC_LITERAL(22, 390, 13), // "loadGroupData"
QT_MOC_LITERAL(23, 404, 32), // "loadDeviceDataForCurrentChannels"
QT_MOC_LITERAL(24, 437, 19), // "loadDevicesForGroup"
QT_MOC_LITERAL(25, 457, 9), // "groupName"
QT_MOC_LITERAL(26, 467, 10), // "QComboBox*"
QT_MOC_LITERAL(27, 478, 11), // "deviceCombo"
QT_MOC_LITERAL(28, 490, 18), // "getAvailableGroups"
QT_MOC_LITERAL(29, 509, 18), // "getDevicesForGroup"
QT_MOC_LITERAL(30, 528, 27), // "extractGroupFromAssociation"
QT_MOC_LITERAL(31, 556, 11), // "association"
QT_MOC_LITERAL(32, 568, 28), // "extractDeviceFromAssociation"
QT_MOC_LITERAL(33, 597, 20), // "getTabIndexForWidget"
QT_MOC_LITERAL(34, 618, 8), // "QWidget*"
QT_MOC_LITERAL(35, 627, 6), // "widget"
QT_MOC_LITERAL(36, 634, 17), // "switchToWidgetTab"
QT_MOC_LITERAL(37, 652, 25), // "loadRealHardwareGroupData"
QT_MOC_LITERAL(38, 678, 23), // "loadRealSensorGroupData"
QT_MOC_LITERAL(39, 702, 25), // "loadRealActuatorGroupData"
QT_MOC_LITERAL(40, 728, 22), // "getRealDevicesForGroup"
QT_MOC_LITERAL(41, 751, 21), // "onPayloadLimitChanged"
QT_MOC_LITERAL(42, 773, 22), // "onPositionLimitChanged"
QT_MOC_LITERAL(43, 796, 19), // "onSpeedLimitChanged"
QT_MOC_LITERAL(44, 816, 26) // "onLimitsValidationRequired"

    },
    "UI::ControlChannelEditDialog\0editFinished\0"
    "\0ControlChannelParams\0params\0accepted\0"
    "closeEditor\0onAcceptClicked\0onCancelClicked\0"
    "reject\0onPolarityChanged\0onAssociationChanged\0"
    "onHardwareGroupChanged\0onLoad1SensorGroupChanged\0"
    "onLoad2SensorGroupChanged\0"
    "onPositionSensorGroupChanged\0"
    "onControlActuatorGroupChanged\0"
    "validateInput\0validateAllFields\0"
    "validateAllFieldsWithFocus\0QWidget*&\0"
    "firstErrorWidget\0loadGroupData\0"
    "loadDeviceDataForCurrentChannels\0"
    "loadDevicesForGroup\0groupName\0QComboBox*\0"
    "deviceCombo\0getAvailableGroups\0"
    "getDevicesForGroup\0extractGroupFromAssociation\0"
    "association\0extractDeviceFromAssociation\0"
    "getTabIndexForWidget\0QWidget*\0widget\0"
    "switchToWidgetTab\0loadRealHardwareGroupData\0"
    "loadRealSensorGroupData\0"
    "loadRealActuatorGroupData\0"
    "getRealDevicesForGroup\0onPayloadLimitChanged\0"
    "onPositionLimitChanged\0onSpeedLimitChanged\0"
    "onLimitsValidationRequired"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_UI__ControlChannelEditDialog[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      32,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       2,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    2,  174,    2, 0x06 /* Public */,
       6,    0,  179,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       7,    0,  180,    2, 0x08 /* Private */,
       8,    0,  181,    2, 0x08 /* Private */,
       9,    0,  182,    2, 0x08 /* Private */,
      10,    0,  183,    2, 0x08 /* Private */,
      11,    0,  184,    2, 0x08 /* Private */,
      12,    0,  185,    2, 0x08 /* Private */,
      13,    0,  186,    2, 0x08 /* Private */,
      14,    0,  187,    2, 0x08 /* Private */,
      15,    0,  188,    2, 0x08 /* Private */,
      16,    0,  189,    2, 0x08 /* Private */,
      17,    0,  190,    2, 0x08 /* Private */,
      18,    0,  191,    2, 0x08 /* Private */,
      19,    1,  192,    2, 0x08 /* Private */,
      22,    0,  195,    2, 0x08 /* Private */,
      23,    0,  196,    2, 0x08 /* Private */,
      24,    2,  197,    2, 0x08 /* Private */,
      28,    0,  202,    2, 0x08 /* Private */,
      29,    1,  203,    2, 0x08 /* Private */,
      30,    1,  206,    2, 0x08 /* Private */,
      32,    1,  209,    2, 0x08 /* Private */,
      33,    1,  212,    2, 0x08 /* Private */,
      36,    1,  215,    2, 0x08 /* Private */,
      37,    0,  218,    2, 0x08 /* Private */,
      38,    0,  219,    2, 0x08 /* Private */,
      39,    0,  220,    2, 0x08 /* Private */,
      40,    1,  221,    2, 0x08 /* Private */,
      41,    0,  224,    2, 0x08 /* Private */,
      42,    0,  225,    2, 0x08 /* Private */,
      43,    0,  226,    2, 0x08 /* Private */,
      44,    0,  227,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3, QMetaType::Bool,    4,    5,
    QMetaType::Void,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::QStringList,
    QMetaType::QStringList, 0x80000000 | 20,   21,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString, 0x80000000 | 26,   25,   27,
    QMetaType::QStringList,
    QMetaType::QStringList, QMetaType::QString,   25,
    QMetaType::QString, QMetaType::QString,   31,
    QMetaType::QString, QMetaType::QString,   31,
    QMetaType::Int, 0x80000000 | 34,   35,
    QMetaType::Void, 0x80000000 | 34,   35,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::QStringList, QMetaType::QString,   25,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void UI::ControlChannelEditDialog::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ControlChannelEditDialog *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->editFinished((*reinterpret_cast< const ControlChannelParams(*)>(_a[1])),(*reinterpret_cast< bool(*)>(_a[2]))); break;
        case 1: _t->closeEditor(); break;
        case 2: _t->onAcceptClicked(); break;
        case 3: _t->onCancelClicked(); break;
        case 4: _t->reject(); break;
        case 5: _t->onPolarityChanged(); break;
        case 6: _t->onAssociationChanged(); break;
        case 7: _t->onHardwareGroupChanged(); break;
        case 8: _t->onLoad1SensorGroupChanged(); break;
        case 9: _t->onLoad2SensorGroupChanged(); break;
        case 10: _t->onPositionSensorGroupChanged(); break;
        case 11: _t->onControlActuatorGroupChanged(); break;
        case 12: _t->validateInput(); break;
        case 13: { QStringList _r = _t->validateAllFields();
            if (_a[0]) *reinterpret_cast< QStringList*>(_a[0]) = std::move(_r); }  break;
        case 14: { QStringList _r = _t->validateAllFieldsWithFocus((*reinterpret_cast< QWidget*(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< QStringList*>(_a[0]) = std::move(_r); }  break;
        case 15: _t->loadGroupData(); break;
        case 16: _t->loadDeviceDataForCurrentChannels(); break;
        case 17: _t->loadDevicesForGroup((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< QComboBox*(*)>(_a[2]))); break;
        case 18: { QStringList _r = _t->getAvailableGroups();
            if (_a[0]) *reinterpret_cast< QStringList*>(_a[0]) = std::move(_r); }  break;
        case 19: { QStringList _r = _t->getDevicesForGroup((*reinterpret_cast< const QString(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< QStringList*>(_a[0]) = std::move(_r); }  break;
        case 20: { QString _r = _t->extractGroupFromAssociation((*reinterpret_cast< const QString(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        case 21: { QString _r = _t->extractDeviceFromAssociation((*reinterpret_cast< const QString(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< QString*>(_a[0]) = std::move(_r); }  break;
        case 22: { int _r = _t->getTabIndexForWidget((*reinterpret_cast< QWidget*(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< int*>(_a[0]) = std::move(_r); }  break;
        case 23: _t->switchToWidgetTab((*reinterpret_cast< QWidget*(*)>(_a[1]))); break;
        case 24: _t->loadRealHardwareGroupData(); break;
        case 25: _t->loadRealSensorGroupData(); break;
        case 26: _t->loadRealActuatorGroupData(); break;
        case 27: { QStringList _r = _t->getRealDevicesForGroup((*reinterpret_cast< const QString(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< QStringList*>(_a[0]) = std::move(_r); }  break;
        case 28: _t->onPayloadLimitChanged(); break;
        case 29: _t->onPositionLimitChanged(); break;
        case 30: _t->onSpeedLimitChanged(); break;
        case 31: _t->onLimitsValidationRequired(); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 22:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QWidget* >(); break;
            }
            break;
        case 23:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QWidget* >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (ControlChannelEditDialog::*)(const ControlChannelParams & , bool );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ControlChannelEditDialog::editFinished)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (ControlChannelEditDialog::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ControlChannelEditDialog::closeEditor)) {
                *result = 1;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject UI::ControlChannelEditDialog::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_UI__ControlChannelEditDialog.data,
    qt_meta_data_UI__ControlChannelEditDialog,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *UI::ControlChannelEditDialog::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *UI::ControlChannelEditDialog::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_UI__ControlChannelEditDialog.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int UI::ControlChannelEditDialog::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 32)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 32;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 32)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 32;
    }
    return _id;
}

// SIGNAL 0
void UI::ControlChannelEditDialog::editFinished(const ControlChannelParams & _t1, bool _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void UI::ControlChannelEditDialog::closeEditor()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
