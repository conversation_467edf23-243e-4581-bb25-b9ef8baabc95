@echo off
echo ========================================
echo  测试去掉作动器序号列
echo ========================================

REM 设置Qt环境变量
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目（测试去掉作动器序号列）...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo 请检查上面的错误信息。
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！作动器序号列已去掉
    echo ========================================
    
    echo.
    echo ✅ 修改完成的内容:
    echo.
    echo 📊 作动器详细配置修改:
    echo - 修改前: 17列（组序号, 作动器组名称, 作动器序号, 作动器序列号, ...）
    echo - 修改后: 16列（组序号, 作动器组名称, 作动器序列号, ...）
    echo - 变化: ❌ 去掉了"作动器序号"列
    echo.
    echo 🎯 作动器详细配置（16列）:
    echo 第1列: 组序号
    echo 第2列: 作动器组名称
    echo 第3列: 作动器序列号（原第4列前移）
    echo 第4列: 作动器类型
    echo 第5列: Unit类型
    echo 第6列: Unit值
    echo 第7列: 行程(m)
    echo 第8列: 位移(m)
    echo 第9列: 拉伸面积(m²)
    echo 第10列: 压缩面积(m²)
    echo 第11列: 极性
    echo 第12列: Deliver(V)
    echo 第13列: 频率(Hz)
    echo 第14列: 输出倍数
    echo 第15列: 平衡(V)
    echo 第16列: 备注
    echo.
    echo 📝 Excel导出格式示例:
    echo.
    echo 组序号 ^| 作动器组名称 ^| 作动器序列号 ^| 作动器类型 ^| Unit类型 ^| ...
    echo ------|------------|------------|----------|---------|----
    echo 1     ^| 主作动器组   ^| ACT001     ^| 液压作动器 ^| Force   ^| ...
    echo 1     ^|            ^| ACT002     ^| 液压作动器 ^| Force   ^| ...
    echo 2     ^| 辅助作动器组 ^| ACT003     ^| 液压作动器 ^| Force   ^| ...
    echo.
    echo 🔧 技术实现:
    echo 1. 表头修改: 去掉"作动器序号"列
    echo 2. 数据写入: 删除actuator.actuatorId的写入
    echo 3. 列号调整: 所有后续列号减1
    echo 4. 列宽调整: 从17列改为16列
    echo.
    echo 💡 优势:
    echo - 去掉冗余的序号列
    echo - 数据更加紧凑
    echo - 作动器序列号已足够标识
    echo - Excel文件更简洁
    echo.
    
    if exist "SiteResConfig.exe" (
        echo 启动程序验证修改...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 启动程序验证修改...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 启动程序验证修改...
        start release\SiteResConfig.exe
    )
)

echo.
echo 📖 验证步骤:
echo.
echo 🎮 作动器详细配置验证:
echo 1. 启动软件，新建项目
echo 2. 创建多个作动器组
echo 3. 在每个组中添加作动器
echo 4. 导出作动器详细信息到Excel
echo 5. 验证Excel文件：
echo    - 总共16列（不是17列）
echo    - 第1列：组序号
echo    - 第2列：作动器组名称
echo    - 第3列：作动器序列号（没有作动器序号列）
echo    - 第4列：作动器类型
echo    - 第16列：备注
echo.
echo 🎮 列结构验证:
echo 1. 打开导出的Excel文件
echo 2. 检查表头是否为16列
echo 3. 确认没有"作动器序号"列
echo 4. 验证"作动器序列号"是第3列
echo 5. 确认所有数据列对应正确
echo.
echo 🎮 数据完整性验证:
echo 1. 验证所有作动器数据都正确导出
echo 2. 确认组序号从1开始递增
echo 3. 验证组名称只在每组第一行显示
echo 4. 检查作动器序列号、类型等信息完整
echo.
echo ✅ 预期结果:
echo - 作动器详细配置为16列格式
echo - 没有"作动器序号"列
echo - "作动器序列号"是第3列
echo - 所有作动器数据完整导出
echo - Excel格式专业，数据准确
echo.
echo 🚨 如果测试失败:
echo - 检查Excel文件是否为16列
echo - 验证是否还有"作动器序号"列
echo - 确认"作动器序列号"列位置
echo - 检查数据完整性
echo.
pause
