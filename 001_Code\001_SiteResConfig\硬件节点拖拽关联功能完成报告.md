# 硬件节点拖拽关联功能完成报告

## 📋 功能需求

实现拖拽功能：将硬件资源树中的硬件节点通道（如LD-B1的CH1、LD-B2的CH2）拖拽到试验配置树的控制通道（CH1/CH2）上，并根据实际数据自动添加关联信息。

## 🎯 拖拽路径

### 拖拽源（硬件资源树）：
```
硬件资源
└── 硬件节点资源
    ├── LD-B1
    │   ├── CH1  ← 可拖拽
    │   └── CH2  ← 可拖拽
    └── LD-B2
        ├── CH1  ← 可拖拽
        └── CH2  ← 可拖拽
```

### 拖拽目标（试验配置树）：
```
实验资源
└── 控制通道
    ├── CH1  ← 可接收拖拽
    └── CH2  ← 可接收拖拽
```

## ✅ 实现的功能

### 1. 自定义树控件类

**CustomHardwareTreeWidget（拖拽发送端）**：
```cpp
class CustomHardwareTreeWidget : public QTreeWidget {
    Q_OBJECT

public:
    explicit CustomHardwareTreeWidget(QWidget* parent = nullptr);
    void setMainWindow(CMyMainWindow* mainWindow);

protected:
    void startDrag(Qt::DropActions supportedActions) override;
    
private:
    CMyMainWindow* mainWindow_;
};
```

**CustomTestConfigTreeWidget（拖拽接收端）**：
```cpp
class CustomTestConfigTreeWidget : public QTreeWidget {
    Q_OBJECT

public:
    explicit CustomTestConfigTreeWidget(QWidget* parent = nullptr);
    void setMainWindow(CMyMainWindow* mainWindow);

protected:
    void dragEnterEvent(QDragEnterEvent* event) override;
    void dragMoveEvent(QDragMoveEvent* event) override;
    void dropEvent(QDropEvent* event) override;
    
private:
    CMyMainWindow* mainWindow_;
};
```

### 2. 拖拽数据格式

**MIME数据格式**：
```
"HARDWARE_CHANNEL|硬件节点名|通道名"
```

**示例**：
- `"HARDWARE_CHANNEL|LD-B1|CH1"` - LD-B1的CH1通道
- `"HARDWARE_CHANNEL|LD-B2|CH2"` - LD-B2的CH2通道

### 3. 拖拽发送逻辑

**startDrag()方法**：
```cpp
void CustomHardwareTreeWidget::startDrag(Qt::DropActions supportedActions) {
    QTreeWidgetItem* item = currentItem();
    QString itemText = item->text(0);
    QTreeWidgetItem* parent = item->parent();
    
    // 检查是否是硬件节点的通道（CH1或CH2）
    if (parent && (itemText == "CH1" || itemText == "CH2")) {
        QString hardwareNodeName = parent->text(0);
        
        // 检查父节点是否是硬件节点（如LD-B1, LD-B2等）
        if (hardwareNodeName.startsWith("LD-B")) {
            // 创建拖拽数据
            QString dragData = QString("HARDWARE_CHANNEL|%1|%2")
                              .arg(hardwareNodeName).arg(itemText);
            
            QMimeData* mimeData = new QMimeData();
            mimeData->setText(dragData);
            
            QDrag* drag = new QDrag(this);
            drag->setMimeData(mimeData);
            
            // 设置拖拽图标
            QPixmap pixmap(100, 30);
            pixmap.fill(Qt::transparent);
            QPainter painter(&pixmap);
            painter.setPen(Qt::black);
            painter.drawText(pixmap.rect(), Qt::AlignCenter, 
                           QString("%1-%2").arg(hardwareNodeName).arg(itemText));
            drag->setPixmap(pixmap);
            
            drag->exec(Qt::CopyAction);
        }
    }
}
```

### 4. 拖拽接收逻辑

**dropEvent()方法**：
```cpp
void CustomTestConfigTreeWidget::dropEvent(QDropEvent* event) {
    QTreeWidgetItem* targetItem = itemAt(event->pos());
    QString targetText = targetItem->text(0);
    
    // 检查是否拖拽到控制通道的CH1或CH2上
    if (targetText == "CH1" || targetText == "CH2") {
        // 解析拖拽数据
        QString dragData = event->mimeData()->text();
        QStringList parts = dragData.split("|");
        
        if (parts.size() >= 3 && parts[0] == "HARDWARE_CHANNEL") {
            QString hardwareNode = parts[1];  // 如：LD-B1
            QString hardwareChannel = parts[2];  // 如：CH1
            
            // 设置关联信息
            QString association = QString("%1 - %2").arg(hardwareNode).arg(hardwareChannel);
            targetItem->setText(1, association);
            
            // 记录日志
            mainWindow_->AddLogEntry("INFO", 
                QString("拖拽关联: %1 -> %2").arg(targetText).arg(association));
            
            event->acceptProposedAction();
        }
    }
}
```

### 5. 拖拽功能启用

**EnableTestConfigTreeDragDrop()方法**：
```cpp
void CMyMainWindow::EnableTestConfigTreeDragDrop() {
    if (!ui->testConfigTreeWidget) return;

    // 启用拖拽接收
    ui->testConfigTreeWidget->setAcceptDrops(true);
    ui->testConfigTreeWidget->setDropIndicatorShown(true);

    // 启用硬件树的拖拽发送
    if (ui->hardwareTreeWidget) {
        ui->hardwareTreeWidget->setDragEnabled(true);
        ui->hardwareTreeWidget->setDragDropMode(QAbstractItemView::DragOnly);
    }

    AddLogEntry("INFO", "拖拽功能已启用：硬件节点通道可拖拽到试验配置通道");
}
```

## 🚀 使用场景

### 场景1：拖拽LD-B1的CH1到试验配置的CH1
1. **操作**：用户从硬件树中拖拽"LD-B1 → CH1"到试验配置树的"CH1"
2. **结果**：试验配置的CH1关联信息显示为"LD-B1 - CH1"
3. **日志**：`"拖拽关联: CH1 -> LD-B1 - CH1"`

### 场景2：拖拽LD-B2的CH2到试验配置的CH2
1. **操作**：用户从硬件树中拖拽"LD-B2 → CH2"到试验配置树的"CH2"
2. **结果**：试验配置的CH2关联信息显示为"LD-B2 - CH2"
3. **日志**：`"拖拽关联: CH2 -> LD-B2 - CH2"`

### 场景3：交叉拖拽（LD-B1的CH2到试验配置的CH1）
1. **操作**：用户从硬件树中拖拽"LD-B1 → CH2"到试验配置树的"CH1"
2. **结果**：试验配置的CH1关联信息显示为"LD-B1 - CH2"
3. **日志**：`"拖拽关联: CH1 -> LD-B1 - CH2"`

## 🎯 功能特性

### 1. 智能识别
- ✅ **源识别**：只有硬件节点下的CH1/CH2通道可以被拖拽
- ✅ **目标识别**：只有试验配置的控制通道CH1/CH2可以接收拖拽
- ✅ **数据验证**：验证拖拽数据格式的正确性

### 2. 视觉反馈
- ✅ **拖拽图标**：显示"硬件节点-通道"格式的拖拽图标
- ✅ **拖拽指示器**：显示拖拽位置指示器
- ✅ **即时更新**：拖拽完成后立即更新关联信息

### 3. 日志记录
- ✅ **启用日志**：记录拖拽功能启用状态
- ✅ **拖拽日志**：记录每次拖拽操作的详细信息
- ✅ **关联日志**：记录关联信息的变化

### 4. 错误处理
- ✅ **无效拖拽**：忽略无效的拖拽操作
- ✅ **格式验证**：验证MIME数据格式
- ✅ **目标验证**：只接受有效的拖拽目标

## 📊 拖拽关联矩阵

| 拖拽源 | 拖拽目标 | 关联结果 | 状态 |
|--------|---------|---------|------|
| **LD-B1 → CH1** | **试验CH1** | **LD-B1 - CH1** | ✅ 支持 |
| **LD-B1 → CH2** | **试验CH1** | **LD-B1 - CH2** | ✅ 支持 |
| **LD-B1 → CH1** | **试验CH2** | **LD-B1 - CH1** | ✅ 支持 |
| **LD-B2 → CH1** | **试验CH1** | **LD-B2 - CH1** | ✅ 支持 |
| **LD-B2 → CH2** | **试验CH2** | **LD-B2 - CH2** | ✅ 支持 |
| **其他节点** | **试验通道** | **无效** | ❌ 忽略 |

## 🔧 技术实现

### 1. 继承机制
- 继承QTreeWidget并重写拖拽相关方法
- 使用Q_OBJECT宏支持Qt的信号槽机制
- 通过setMainWindow()建立与主窗口的连接

### 2. 数据传输
- 使用QMimeData传输拖拽数据
- 自定义数据格式确保数据完整性
- 字符串分割解析拖拽信息

### 3. 事件处理
- dragEnterEvent()：处理拖拽进入事件
- dragMoveEvent()：处理拖拽移动事件
- dropEvent()：处理拖拽放下事件
- startDrag()：处理拖拽开始事件

### 4. 界面集成
- 在构造函数中替换原有的QTreeWidget
- 启用拖拽功能并设置相关属性
- 连接主窗口引用以便调用日志功能

## 📝 日志示例

**功能启用时**：
```
[INFO] 拖拽功能已启用：硬件节点通道可拖拽到试验配置通道
```

**拖拽开始时**：
```
[INFO] 开始拖拽: LD-B1-CH1
```

**拖拽完成时**：
```
[INFO] 拖拽关联: CH1 -> LD-B1 - CH1
```

## 📖 总结

成功实现了硬件节点拖拽关联功能：

1. **完整的拖拽支持**：从硬件树到试验配置树的完整拖拽流程
2. **智能数据处理**：自动解析和验证拖拽数据
3. **灵活的关联方式**：支持任意硬件通道到任意试验通道的关联
4. **完善的用户体验**：视觉反馈、日志记录、错误处理
5. **技术实现优雅**：使用Qt标准的拖拽机制，代码结构清晰

现在用户可以通过简单的拖拽操作，将硬件资源树中的硬件节点通道关联到试验配置树的控制通道上，大大提升了操作的便捷性和直观性！
