#pragma once

#include <QObject>
#include <QString>
#include <QStringList>
#include <QTreeWidgetItem>
#include <QJsonObject>

class CMyMainWindow;
class LogManager;
class DeviceManager;

/**
 * @brief MainWindow辅助类 - 分离复杂的业务逻辑
 * @details 将MainWindow中的复杂方法提取到独立的辅助类中，提高代码的可维护性
 */
class MainWindowHelper : public QObject {
    Q_OBJECT

public:
    explicit MainWindowHelper(CMyMainWindow* mainWindow, QObject* parent = nullptr);
    ~MainWindowHelper();

    // UI更新辅助方法
    void updateProjectTitle(const QString& projectName);
    void updateTreeWidgetTooltips();
    void updateStatusDisplay(const QString& message, int timeout = 2000);
    
    // 数据验证辅助方法
    bool validateProjectData();
    bool validateTreeData();
    bool validateManagerStates();
    QStringList getValidationErrors() const;
    
    // 文件操作辅助方法
    QString selectProjectFile(bool forSave = false);
    QString selectExportFile(const QString& defaultName, const QString& filter);
    bool confirmDataLoss();
    
    // 树形控件辅助方法
    void expandAllTreeNodes();
    void collapseAllTreeNodes();
    void refreshTreeFromManagers();
    QTreeWidgetItem* findTreeItem(const QString& itemName, const QString& treeType);
    
    // 日志和错误处理辅助方法
    void logOperation(const QString& operation, bool success, const QString& details = "");
    void showErrorDialog(const QString& title, const QString& message);
    void showSuccessMessage(const QString& message);
    
    // 项目状态辅助方法
    void updateOperationState(bool hasProject);
    void saveCurrentState();
    void restoreState();
    
signals:
    void operationCompleted(const QString& operation, bool success);
    void validationCompleted(bool valid, const QStringList& errors);
    void statusChanged(const QString& status);

private slots:
    void onManagerError(const QString& error);
    void onValidationRequested();

private:
    CMyMainWindow* mainWindow_;
    LogManager* logManager_;
    DeviceManager* deviceManager_;
    QStringList validationErrors_;
    
    // 内部辅助方法
    void connectManagerSignals();
    void setupValidationRules();
    QString formatErrorMessage(const QString& error);
    void updateUIState();
}; 