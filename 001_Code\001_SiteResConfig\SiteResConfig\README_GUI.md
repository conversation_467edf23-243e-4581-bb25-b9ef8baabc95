# SiteResConfig GUI 使用指南

## 🎨 界面概览

SiteResConfig 采用现代化的Qt界面设计，提供直观易用的用户体验。

### 主界面布局

```
┌─────────────────────────────────────────────────────────┐
│ 菜单栏: 文件 | 配置 | 数据 | 控制 | 帮助                    │
├─────────────────────────────────────────────────────────┤
│ 工具栏: [新建] [打开] [保存] [连接] [断开] [急停]          │
├─────────────┬───────────────────────────────────────────┤
│ 左侧面板     │ 右侧工作区域                               │
│             │                                           │
│ • 硬件资源   │ 标签页:                                   │
│ • 试验配置   │ • 系统概览                                │
│             │ • 数据制作                                │
│             │ • 手动控制                                │
│             │ • 系统日志                                │
├─────────────┴───────────────────────────────────────────┤
│ 状态栏: 系统状态 | 连接状态 | 时间                        │
└─────────────────────────────────────────────────────────┘
```

## 🔧 功能模块

### 1. 配置管理
- **新建工程**: 创建新的试验配置
- **打开工程**: 加载已有配置文件
- **保存工程**: 保存当前配置
- **格式支持**: JSON, XML, CSV

### 2. 硬件管理
- **硬件资源树**: 显示所有硬件节点
- **连接状态**: 实时显示设备连接状态
- **参数配置**: PID参数、安全限制设置

### 3. 数据制作
- **数据类型**: 
  - 线性递增/递减
  - 正弦波/余弦波
  - 三角波/锯齿波
- **参数设置**: 数据点数、时间间隔、幅值范围
- **实时预览**: 数据表格显示
- **导出功能**: CSV格式导出

### 4. 手动控制
- **位置控制**: 精确位置定位
- **力值控制**: 力值闭环控制
- **速度控制**: 运动速度设置
- **安全保护**: 急停、限位保护

### 5. 系统监控
- **实时日志**: 彩色分级日志显示
- **状态监控**: 系统运行状态实时更新
- **数据统计**: 试验数据统计信息

## 🎯 操作流程

### 基本操作流程
1. **启动软件** → 加载主界面
2. **加载配置** → 选择配置文件或手动配置
3. **连接硬件** → 建立硬件通信
4. **制作数据** → 生成试验数据
5. **执行控制** → 手动控制或自动执行
6. **监控状态** → 实时监控系统状态
7. **导出数据** → 保存试验结果

### 快捷键
- **Ctrl+N**: 新建工程
- **Ctrl+O**: 打开工程
- **Ctrl+S**: 保存工程
- **F5**: 连接硬件
- **F6**: 断开硬件
- **F12**: 紧急停止

## 🎨 界面特色

### 现代化设计
- **扁平化风格**: 简洁现代的界面设计
- **响应式布局**: 自适应窗口大小调整
- **主题支持**: 支持明暗主题切换

### 用户体验
- **直观操作**: 拖拽式操作，所见即所得
- **实时反馈**: 操作结果实时显示
- **状态提示**: 详细的状态信息和提示

### 可视化元素
- **图标按钮**: 直观的图标设计
- **颜色编码**: 不同状态使用不同颜色
- **进度指示**: 操作进度实时显示

## 📖 使用技巧

### 高效操作
1. **使用快捷键**: 提高操作效率
2. **批量操作**: 支持多选和批量处理
3. **模板功能**: 保存常用配置为模板
4. **历史记录**: 自动保存操作历史

### 故障排除
1. **日志查看**: 通过系统日志定位问题
2. **状态检查**: 检查硬件连接状态
3. **参数验证**: 确认配置参数正确性
4. **重启恢复**: 必要时重启软件恢复

## 🔍 界面元素说明

### 状态指示
- **绿色**: 正常运行状态
- **红色**: 错误或断开状态
- **黄色**: 警告或等待状态
- **蓝色**: 信息提示状态

### 按钮状态
- **启用**: 可以执行操作
- **禁用**: 当前不可操作（灰色显示）
- **高亮**: 当前激活状态

### 数据显示
- **表格视图**: 结构化数据显示
- **树形视图**: 层次化数据显示
- **实时更新**: 数据自动刷新显示
