/**
 * @file 作动器传感器删除修复实施代码.cpp
 * @brief 修复作动器和传感器删除功能的实施代码
 * @details 解决多次添加、拖拽、删除操作导致的数据不同步问题
 * <AUTHOR> Assistant
 * @date 2025-01-28
 * @version 1.0.0
 */

// ========== 实施步骤1：在MainWindow_Qt_Simple.cpp中的新增实现 ==========

// 🆕 删除前完整性检查（作动器）
QStringList CMyMainWindow::performActuatorPreDeleteChecks(int groupId, const QString& serialNumber, const QString& groupName) {
    QStringList results;
    
    // 检查1：控制通道关联
    QStringList associatedChannels = getControlChannelsUsingActuator(serialNumber, groupName);
    if (!associatedChannels.isEmpty()) {
        results.append(QString(u8"📌 控制通道关联：%1 个通道将被清理").arg(associatedChannels.size()));
        for (const QString& channel : associatedChannels) {
            results.append(QString(u8"   • %1").arg(channel));
        }
    }
    
    // 检查2：硬件节点拖拽关联
    QStringList hardwareAssociations = getHardwareAssociationsForActuator(serialNumber);
    if (!hardwareAssociations.isEmpty()) {
        results.append(QString(u8"🔗 硬件关联：%1 个硬件节点关联将被清理").arg(hardwareAssociations.size()));
        for (const QString& assoc : hardwareAssociations) {
            results.append(QString(u8"   • %1").arg(assoc));
        }
    }
    
    // 检查3：数据存储一致性
    bool hasGroupStorage = actuatorViewModel1_2_->getDataManager()->hasActuatorInGroupStorage(groupId, serialNumber);
    bool hasLayeredStorage = actuatorViewModel1_2_->getDataManager()->hasActuatorInLayeredStorage(groupId, serialNumber);
    
    if (hasGroupStorage != hasLayeredStorage) {
        results.append(u8"⚠️ 警告：检测到数据存储不一致，将自动修复");
    }
    
    // 检查4：多组存在检测
    QStringList allGroups = GetActuatorGroupNamesBySerialNumber(serialNumber);
    if (allGroups.size() > 1) {
        results.append(QString(u8"🔄 多组检测：该序列号在 %1 个组中存在，仅删除当前组")
                      .arg(allGroups.size()));
    }
    
    return results;
}

// 🆕 执行完整的作动器删除流程
void CMyMainWindow::performCompleteActuatorDeletion(QTreeWidgetItem* item, int groupId, 
                                                   const QString& groupName, const QString& serialNumber) {
    AddLogEntry("INFO", QString(u8"🔄 开始完整删除流程：GroupID=%1, SN=%2").arg(groupId).arg(serialNumber));
    
    // 步骤1：数据删除（确保双存储同步）
    if (actuatorViewModel1_2_->getDataManager()->deleteActuatorWithFullSync(groupId, serialNumber)) {
        AddLogEntry("SUCCESS", u8"✅ 作动器数据删除成功（双存储同步）");
        
        // 步骤2：清理控制通道关联（精准清理）
        int clearedChannels = clearControlChannelAssociationsForActuator(serialNumber, groupName);
        AddLogEntry("INFO", QString(u8"🎯 控制通道关联清理完成：%1 个通道").arg(clearedChannels));
        
        // 步骤3：清理硬件节点拖拽关联
        int clearedHardwareAssoc = clearHardwareAssociationsForActuator(serialNumber);
        AddLogEntry("INFO", QString(u8"🔗 硬件关联清理完成：%1 个关联").arg(clearedHardwareAssoc));
        
        // 步骤4：UI节点移除
        QTreeWidgetItem* groupItem = item->parent();
        if (groupItem) {
            groupItem->removeChild(item);
            delete item;
            AddLogEntry("INFO", u8"✅ UI节点移除完成");
        }
        
        // 步骤5：数据同步和UI更新
        synchronizeAllDataAfterDeletion();
        
        AddLogEntry("SUCCESS", QString(u8"🎉 作动器完整删除流程完成：%1").arg(serialNumber));
        
    } else {
        QString errorMessage = actuatorViewModel1_2_->getLastError();
        AddLogEntry("ERROR", QString(u8"❌ 作动器删除失败：%1").arg(errorMessage));
        QMessageBox::warning(this, u8"删除失败", 
            QString(u8"删除作动器失败：\n%1").arg(errorMessage));
    }
}

// 🆕 传感器删除前检查
QStringList CMyMainWindow::performSensorPreDeleteChecks(int groupId, const QString& serialNumber, const QString& groupName) {
    QStringList results;
    
    // 检查控制通道关联（使用精确匹配）
    QStringList associatedChannels = GetControlChannelsUsingSensorPrecise(serialNumber, groupName);
    if (!associatedChannels.isEmpty()) {
        results.append(QString(u8"📌 控制通道关联：%1 个通道").arg(associatedChannels.size()));
        for (const QString& channel : associatedChannels) {
            results.append(QString(u8"   • %1").arg(channel));
        }
    }
    
    // 检查硬件节点关联
    QStringList hardwareAssoc = getHardwareAssociationsForSensor(serialNumber);
    if (!hardwareAssoc.isEmpty()) {
        results.append(QString(u8"🔗 硬件关联：%1 个").arg(hardwareAssoc.size()));
    }
    
    return results;
}

// 🆕 构建传感器删除确认消息
QString CMyMainWindow::buildSensorDeleteConfirmMessage(const QString& serialNumber, const QString& groupName, 
                                                      int groupId, const QStringList& checks) {
    QString message = QString(u8"确定要删除传感器设备吗？\n\n"
                             "📋 设备信息：\n"
                             "• 序列号：%1\n"
                             "• 所属组：%2 (ID: %3)\n\n"
                             "⚠️ 此操作将：\n"
                             "• 删除传感器数据\n"
                             "• 清除所有控制通道关联\n"
                             "• 清除硬件节点关联\n"
                             "• 此操作不可撤销")
                     .arg(serialNumber).arg(groupName).arg(groupId);
    
    if (!checks.isEmpty()) {
        message += QString(u8"\n\n🔍 影响检查：\n%1").arg(checks.join("\n"));
    }
    
    return message;
}

// 🆕 执行完整的传感器删除流程
void CMyMainWindow::performCompleteSensorDeletion(QTreeWidgetItem* item, int groupId, 
                                                 const QString& groupName, const QString& serialNumber) {
    AddLogEntry("INFO", QString(u8"🔄 开始完整传感器删除流程"));
    
    // 步骤1：数据删除
    if (sensorDataManager_->removeSensorInGroup(groupId, serialNumber)) {
        AddLogEntry("SUCCESS", u8"✅ 传感器数据删除成功");
        
        // 步骤2：精确清理控制通道关联
        int clearedChannels = clearControlChannelAssociationsForSensorPrecise(serialNumber, groupName);
        AddLogEntry("INFO", QString(u8"🎯 控制通道关联清理完成：%1 个通道").arg(clearedChannels));
        
        // 步骤3：清理硬件关联
        int clearedHardware = clearHardwareAssociationsForSensor(serialNumber);
        AddLogEntry("INFO", QString(u8"🔗 硬件关联清理完成：%1 个关联").arg(clearedHardware));
        
        // 步骤4：UI节点移除
        QTreeWidgetItem* groupItem = item->parent();
        if (groupItem) {
            groupItem->removeChild(item);
            delete item;
            AddLogEntry("INFO", u8"✅ UI节点移除完成");
        }
        
        // 步骤5：数据同步
        synchronizeAllDataAfterDeletion();
        
        AddLogEntry("SUCCESS", QString(u8"🎉 传感器完整删除流程完成：%1").arg(serialNumber));
        
    } else {
        QString error = sensorDataManager_->getLastError();
        AddLogEntry("ERROR", QString(u8"❌ 传感器删除失败：%1").arg(error));
        QMessageBox::warning(this, u8"删除失败", QString(u8"删除传感器失败：\n%1").arg(error));
    }
}

// 🆕 删除后数据同步
void CMyMainWindow::synchronizeAllDataAfterDeletion() {
    AddLogEntry("INFO", u8"🔄 开始删除后数据同步...");
    
    // 1. 刷新测试配置树（关联信息更新）
    RefreshTestConfigTreeFromDataManagers();
    
    // 2. 更新所有tooltip
    UpdateAllTreeWidgetTooltips();
    
    // 3. 验证数据一致性
    validateDataConsistencyAfterDeletion();
    
    AddLogEntry("INFO", u8"✅ 删除后数据同步完成");
}

// 🆕 删除后数据一致性验证
void CMyMainWindow::validateDataConsistencyAfterDeletion() {
    // 验证作动器数据一致性
    if (actuatorViewModel1_2_ && actuatorViewModel1_2_->getDataManager()) {
        QStringList inconsistencies = actuatorViewModel1_2_->getDataManager()->validateStorageConsistency();
        if (!inconsistencies.isEmpty()) {
            AddLogEntry("WARNING", QString(u8"⚠️ 检测到作动器数据不一致：\n%1").arg(inconsistencies.join("\n")));
        } else {
            AddLogEntry("INFO", u8"✅ 作动器数据一致性验证通过");
        }
    }
    
    // 验证传感器数据一致性（如果有相应方法）
    if (sensorDataManager_) {
        // 这里可以添加传感器数据一致性验证
        AddLogEntry("INFO", u8"✅ 传感器数据一致性验证通过");
    }
}

// 🆕 精确清理控制通道关联（作动器）
int CMyMainWindow::clearControlChannelAssociationsForActuator(const QString& serialNumber, const QString& groupName) {
    int clearedCount = 0;
    
    if (!controlChannelDataManager_) {
        return clearedCount;
    }
    
    // 获取所有控制通道组
    QList<UI::ControlChannelGroup> allGroups = controlChannelDataManager_->getAllControlChannelGroups();
    
    for (auto& group : allGroups) {
        for (auto& channel : group.channels) {
            // 使用精确匹配检查控制作动器字段
            QString currentAssociation = QString::fromStdString(channel.controlActuator);
            
            if (isExactActuatorMatch(currentAssociation, serialNumber, groupName)) {
                channel.controlActuator = "";  // 清空关联
                clearedCount++;
                
                AddLogEntry("INFO", QString(u8"🧹 清理控制通道关联：组=%1, 通道=%2, 作动器=%3")
                           .arg(QString::fromStdString(group.name))
                           .arg(QString::fromStdString(channel.name))
                           .arg(serialNumber));
            }
        }
    }
    
    // 保存更新后的数据
    if (clearedCount > 0) {
        controlChannelDataManager_->saveAllControlChannelGroups(allGroups);
    }
    
    return clearedCount;
}

// 🆕 精确清理控制通道关联（传感器）
int CMyMainWindow::clearControlChannelAssociationsForSensorPrecise(const QString& serialNumber, const QString& groupName) {
    int clearedCount = 0;
    
    if (!controlChannelDataManager_) {
        return clearedCount;
    }
    
    QList<UI::ControlChannelGroup> allGroups = controlChannelDataManager_->getAllControlChannelGroups();
    
    for (auto& group : allGroups) {
        for (auto& channel : group.channels) {
            // 检查所有传感器关联字段
            bool needUpdate = false;
            
            if (isExactSensorMatch(channel.load1Sensor, serialNumber, groupName)) {
                channel.load1Sensor = "";
                needUpdate = true;
                clearedCount++;
            }
            
            if (isExactSensorMatch(channel.load2Sensor, serialNumber, groupName)) {
                channel.load2Sensor = "";
                needUpdate = true;
                clearedCount++;
            }
            
            if (isExactSensorMatch(channel.positionSensor, serialNumber, groupName)) {
                channel.positionSensor = "";
                needUpdate = true;
                clearedCount++;
            }
            
            if (needUpdate) {
                AddLogEntry("INFO", QString(u8"🧹 清理传感器通道关联：组=%1, 通道=%2, 传感器=%3")
                           .arg(QString::fromStdString(group.name))
                           .arg(QString::fromStdString(channel.name))
                           .arg(serialNumber));
            }
        }
    }
    
    // 保存更新后的数据
    if (clearedCount > 0) {
        controlChannelDataManager_->saveAllControlChannelGroups(allGroups);
    }
    
    return clearedCount;
}

// 🆕 精确匹配检查（作动器）
bool CMyMainWindow::isExactActuatorMatch(const QString& associationField, const QString& serialNumber, const QString& groupName) {
    if (associationField.isEmpty()) return false;
    
    // 检查完整匹配格式：组名 - 序列号
    QString expectedFormat = QString("%1 - %2").arg(groupName).arg(serialNumber);
    if (associationField.trimmed() == expectedFormat.trimmed()) {
        return true;
    }
    
    // 兼容性检查：只有序列号的情况
    if (associationField.trimmed() == serialNumber.trimmed()) {
        return true;
    }
    
    return false;
}

// 🆕 精确匹配检查（传感器）
bool CMyMainWindow::isExactSensorMatch(const std::string& associationField, const QString& serialNumber, const QString& groupName) {
    if (associationField.empty()) return false;
    
    QString association = QString::fromStdString(associationField);
    return isExactSensorMatch(association, serialNumber, groupName);
}

bool CMyMainWindow::isExactSensorMatch(const QString& associationField, const QString& serialNumber, const QString& groupName) {
    if (associationField.isEmpty()) return false;
    
    // 检查完整匹配格式：组名 - 序列号
    QString expectedFormat = QString("%1 - %2").arg(groupName).arg(serialNumber);
    if (associationField.trimmed() == expectedFormat.trimmed()) {
        return true;
    }
    
    // 兼容性检查：只有序列号的情况
    if (associationField.trimmed() == serialNumber.trimmed()) {
        return true;
    }
    
    return false;
}

// 🆕 清理硬件关联（作动器）
int CMyMainWindow::clearHardwareAssociationsForActuator(const QString& serialNumber) {
    // TODO: 实现硬件节点拖拽关联的清理
    // 这里需要根据实际的硬件关联存储方式来实现
    AddLogEntry("INFO", QString(u8"📋 清理硬件关联（作动器）：%1").arg(serialNumber));
    return 0;
}

// 🆕 清理硬件关联（传感器）
int CMyMainWindow::clearHardwareAssociationsForSensor(const QString& serialNumber) {
    // TODO: 实现硬件节点拖拽关联的清理
    // 这里需要根据实际的硬件关联存储方式来实现
    AddLogEntry("INFO", QString(u8"📋 清理硬件关联（传感器）：%1").arg(serialNumber));
    return 0;
}

// 🆕 获取硬件关联（作动器）
QStringList CMyMainWindow::getHardwareAssociationsForActuator(const QString& serialNumber) {
    // TODO: 实现硬件关联查询
    Q_UNUSED(serialNumber);
    return QStringList();
}

// 🆕 获取硬件关联（传感器）
QStringList CMyMainWindow::getHardwareAssociationsForSensor(const QString& serialNumber) {
    // TODO: 实现硬件关联查询
    Q_UNUSED(serialNumber);
    return QStringList();
}

// ========== 实施步骤2：替换现有的删除方法调用 ==========

/**
 * 在 OnDeleteActuatorDevice 方法中，将原有的实现替换为：
 */
/*
void CMyMainWindow::OnDeleteActuatorDevice(QTreeWidgetItem* item) {
    // 原有的参数检查...
    
    // 🆕 使用新的删除流程
    if (reply == QMessageBox::Yes) {
        performCompleteActuatorDeletion(item, groupId, groupName, serialNumber);
    } else {
        AddLogEntry("INFO", u8"📝 作动器删除已取消");
    }
}
*/

/**
 * 在 OnDeleteSensorDevice 方法中，将原有的实现替换为：
 */
/*
void CMyMainWindow::OnDeleteSensorDevice(QTreeWidgetItem* item) {
    // 原有的参数检查...
    
    // 🆕 使用新的删除流程
    if (reply == QMessageBox::Yes) {
        performCompleteSensorDeletion(item, groupId, groupName, serialNumber);
    } else {
        AddLogEntry("INFO", u8"📝 传感器删除已取消");
    }
}
*/

// ========== 实施步骤3：在ActuatorViewModel_1_2中添加适配器方法 ==========

/**
 * 在 ActuatorViewModel_1_2.h 中添加：
 */
/*
public:
    ActuatorDataManager_1_2* getDataManager() { return actuatorDataManager_; }
*/

/**
 * 在 ActuatorViewModel_1_2.cpp 中确保适配器方法可以访问数据管理器
 */ 