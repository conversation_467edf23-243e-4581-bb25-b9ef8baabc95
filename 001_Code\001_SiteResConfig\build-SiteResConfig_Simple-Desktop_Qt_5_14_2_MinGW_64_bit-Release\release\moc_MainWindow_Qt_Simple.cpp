/****************************************************************************
** Meta object code from reading C++ file 'MainWindow_Qt_Simple.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../001_SiteResConfig_OldStruct/SiteResConfig/include/MainWindow_Qt_Simple.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'MainWindow_Qt_Simple.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_CMyMainWindow_t {
    QByteArrayData data[67];
    char stringdata0[1453];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_CMyMainWindow_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_CMyMainWindow_t qt_meta_stringdata_CMyMainWindow = {
    {
QT_MOC_LITERAL(0, 0, 13), // "CMyMainWindow"
QT_MOC_LITERAL(1, 14, 12), // "OnNewProject"
QT_MOC_LITERAL(2, 27, 0), // ""
QT_MOC_LITERAL(3, 28, 13), // "OnOpenProject"
QT_MOC_LITERAL(4, 42, 13), // "OnSaveProject"
QT_MOC_LITERAL(5, 56, 15), // "OnSaveAsProject"
QT_MOC_LITERAL(6, 72, 6), // "OnExit"
QT_MOC_LITERAL(7, 79, 20), // "OnConfigureNodeLD_B1"
QT_MOC_LITERAL(8, 100, 20), // "OnConfigureNodeLD_B2"
QT_MOC_LITERAL(9, 121, 10), // "OnClearLog"
QT_MOC_LITERAL(10, 132, 9), // "OnSaveLog"
QT_MOC_LITERAL(11, 142, 7), // "OnAbout"
QT_MOC_LITERAL(12, 150, 15), // "OnRestoreColors"
QT_MOC_LITERAL(13, 166, 14), // "OnUpdateStatus"
QT_MOC_LITERAL(14, 181, 15), // "OnProjectOpened"
QT_MOC_LITERAL(15, 197, 11), // "projectPath"
QT_MOC_LITERAL(16, 209, 11), // "projectName"
QT_MOC_LITERAL(17, 221, 15), // "OnProjectClosed"
QT_MOC_LITERAL(18, 237, 14), // "OnProjectSaved"
QT_MOC_LITERAL(19, 252, 12), // "OnSensorCali"
QT_MOC_LITERAL(20, 265, 22), // "initializeJSONExporter"
QT_MOC_LITERAL(21, 288, 31), // "saveSensorDetailedParamsInGroup"
QT_MOC_LITERAL(22, 320, 7), // "groupId"
QT_MOC_LITERAL(23, 328, 20), // "UI::SensorParams_1_2"
QT_MOC_LITERAL(24, 349, 6), // "params"
QT_MOC_LITERAL(25, 356, 39), // "saveOrUpdateSensorDetailedPar..."
QT_MOC_LITERAL(26, 396, 30), // "getSensorDetailedParamsInGroup"
QT_MOC_LITERAL(27, 427, 12), // "serialNumber"
QT_MOC_LITERAL(28, 440, 5), // "bool&"
QT_MOC_LITERAL(29, 446, 9), // "isHasData"
QT_MOC_LITERAL(30, 456, 33), // "updateSensorDetailedParamsInG..."
QT_MOC_LITERAL(31, 490, 33), // "removeSensorDetailedParamsInG..."
QT_MOC_LITERAL(32, 524, 32), // "getAllSensorSerialNumbersInGroup"
QT_MOC_LITERAL(33, 557, 33), // "getAllSensorDetailedParamsInG..."
QT_MOC_LITERAL(34, 591, 27), // "QList<UI::SensorParams_1_2>"
QT_MOC_LITERAL(35, 619, 18), // "validateSensorData"
QT_MOC_LITERAL(36, 638, 32), // "validateSensorStorageConsistency"
QT_MOC_LITERAL(37, 671, 23), // "getSensorTypeStatistics"
QT_MOC_LITERAL(38, 695, 17), // "QMap<QString,int>"
QT_MOC_LITERAL(39, 713, 28), // "getAllActuatorGroups_MainDlg"
QT_MOC_LITERAL(40, 742, 28), // "QList<UI::ActuatorGroup_1_2>"
QT_MOC_LITERAL(41, 771, 18), // "getAllSensorGroups"
QT_MOC_LITERAL(42, 790, 26), // "QList<UI::SensorGroup_1_2>"
QT_MOC_LITERAL(43, 817, 31), // "buildControlChannelGroupsFromUI"
QT_MOC_LITERAL(44, 849, 30), // "QList<UI::ControlChannelGroup>"
QT_MOC_LITERAL(45, 880, 33), // "collectControlChannelGroupsFr..."
QT_MOC_LITERAL(46, 914, 22), // "getCtrlChanDataManager"
QT_MOC_LITERAL(47, 937, 20), // "CtrlChanDataManager*"
QT_MOC_LITERAL(48, 958, 30), // "buildHardwareNodeConfigsFromUI"
QT_MOC_LITERAL(49, 989, 27), // "QList<UI::NodeConfigParams>"
QT_MOC_LITERAL(50, 1017, 30), // "onActuatorGroupCreatedBusiness"
QT_MOC_LITERAL(51, 1048, 9), // "groupName"
QT_MOC_LITERAL(52, 1058, 31), // "onActuatorDeviceCreatedBusiness"
QT_MOC_LITERAL(53, 1090, 30), // "onActuatorDeviceEditedBusiness"
QT_MOC_LITERAL(54, 1121, 31), // "onActuatorDeviceDeletedBusiness"
QT_MOC_LITERAL(55, 1153, 25), // "onActuatorValidationError"
QT_MOC_LITERAL(56, 1179, 5), // "error"
QT_MOC_LITERAL(57, 1185, 40), // "onControlChannelTreeItemSelec..."
QT_MOC_LITERAL(58, 1226, 28), // "updateChannelSettingsDisplay"
QT_MOC_LITERAL(59, 1255, 9), // "channelId"
QT_MOC_LITERAL(60, 1265, 28), // "onEditChannelConfigTriggered"
QT_MOC_LITERAL(61, 1294, 28), // "extractControlChannelGroupId"
QT_MOC_LITERAL(62, 1323, 16), // "QTreeWidgetItem*"
QT_MOC_LITERAL(63, 1340, 11), // "channelItem"
QT_MOC_LITERAL(64, 1352, 32), // "onChannelDataUpdatedFromDragDrop"
QT_MOC_LITERAL(65, 1385, 33), // "onChannelGroupUpdatedFromDrag..."
QT_MOC_LITERAL(66, 1419, 33) // "refreshChannelSettingsDisplay..."

    },
    "CMyMainWindow\0OnNewProject\0\0OnOpenProject\0"
    "OnSaveProject\0OnSaveAsProject\0OnExit\0"
    "OnConfigureNodeLD_B1\0OnConfigureNodeLD_B2\0"
    "OnClearLog\0OnSaveLog\0OnAbout\0"
    "OnRestoreColors\0OnUpdateStatus\0"
    "OnProjectOpened\0projectPath\0projectName\0"
    "OnProjectClosed\0OnProjectSaved\0"
    "OnSensorCali\0initializeJSONExporter\0"
    "saveSensorDetailedParamsInGroup\0groupId\0"
    "UI::SensorParams_1_2\0params\0"
    "saveOrUpdateSensorDetailedParamsInGroup\0"
    "getSensorDetailedParamsInGroup\0"
    "serialNumber\0bool&\0isHasData\0"
    "updateSensorDetailedParamsInGroup\0"
    "removeSensorDetailedParamsInGroup\0"
    "getAllSensorSerialNumbersInGroup\0"
    "getAllSensorDetailedParamsInGroup\0"
    "QList<UI::SensorParams_1_2>\0"
    "validateSensorData\0validateSensorStorageConsistency\0"
    "getSensorTypeStatistics\0QMap<QString,int>\0"
    "getAllActuatorGroups_MainDlg\0"
    "QList<UI::ActuatorGroup_1_2>\0"
    "getAllSensorGroups\0QList<UI::SensorGroup_1_2>\0"
    "buildControlChannelGroupsFromUI\0"
    "QList<UI::ControlChannelGroup>\0"
    "collectControlChannelGroupsFromUI\0"
    "getCtrlChanDataManager\0CtrlChanDataManager*\0"
    "buildHardwareNodeConfigsFromUI\0"
    "QList<UI::NodeConfigParams>\0"
    "onActuatorGroupCreatedBusiness\0groupName\0"
    "onActuatorDeviceCreatedBusiness\0"
    "onActuatorDeviceEditedBusiness\0"
    "onActuatorDeviceDeletedBusiness\0"
    "onActuatorValidationError\0error\0"
    "onControlChannelTreeItemSelectionChanged\0"
    "updateChannelSettingsDisplay\0channelId\0"
    "onEditChannelConfigTriggered\0"
    "extractControlChannelGroupId\0"
    "QTreeWidgetItem*\0channelItem\0"
    "onChannelDataUpdatedFromDragDrop\0"
    "onChannelGroupUpdatedFromDragDrop\0"
    "refreshChannelSettingsDisplayOnly"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_CMyMainWindow[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      45,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    0,  239,    2, 0x0a /* Public */,
       3,    0,  240,    2, 0x0a /* Public */,
       4,    0,  241,    2, 0x0a /* Public */,
       5,    0,  242,    2, 0x0a /* Public */,
       6,    0,  243,    2, 0x0a /* Public */,
       7,    0,  244,    2, 0x0a /* Public */,
       8,    0,  245,    2, 0x0a /* Public */,
       9,    0,  246,    2, 0x0a /* Public */,
      10,    0,  247,    2, 0x0a /* Public */,
      11,    0,  248,    2, 0x0a /* Public */,
      12,    0,  249,    2, 0x0a /* Public */,
      13,    0,  250,    2, 0x0a /* Public */,
      14,    2,  251,    2, 0x0a /* Public */,
      17,    0,  256,    2, 0x0a /* Public */,
      18,    1,  257,    2, 0x0a /* Public */,
      19,    0,  260,    2, 0x0a /* Public */,
      20,    0,  261,    2, 0x0a /* Public */,
      21,    2,  262,    2, 0x0a /* Public */,
      25,    2,  267,    2, 0x0a /* Public */,
      26,    3,  272,    2, 0x0a /* Public */,
      30,    3,  279,    2, 0x0a /* Public */,
      31,    2,  286,    2, 0x0a /* Public */,
      32,    1,  291,    2, 0x0a /* Public */,
      33,    1,  294,    2, 0x0a /* Public */,
      35,    0,  297,    2, 0x0a /* Public */,
      36,    0,  298,    2, 0x0a /* Public */,
      37,    0,  299,    2, 0x0a /* Public */,
      39,    0,  300,    2, 0x0a /* Public */,
      41,    0,  301,    2, 0x0a /* Public */,
      43,    0,  302,    2, 0x0a /* Public */,
      45,    0,  303,    2, 0x0a /* Public */,
      46,    0,  304,    2, 0x0a /* Public */,
      48,    0,  305,    2, 0x0a /* Public */,
      50,    2,  306,    2, 0x0a /* Public */,
      52,    2,  311,    2, 0x0a /* Public */,
      53,    2,  316,    2, 0x0a /* Public */,
      54,    1,  321,    2, 0x0a /* Public */,
      55,    1,  324,    2, 0x0a /* Public */,
      57,    0,  327,    2, 0x0a /* Public */,
      58,    2,  328,    2, 0x0a /* Public */,
      60,    0,  333,    2, 0x0a /* Public */,
      61,    1,  334,    2, 0x0a /* Public */,
      64,    2,  337,    2, 0x08 /* Private */,
      65,    1,  342,    2, 0x08 /* Private */,
      66,    2,  345,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,   15,   16,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,   15,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Bool, QMetaType::Int, 0x80000000 | 23,   22,   24,
    QMetaType::Bool, QMetaType::Int, 0x80000000 | 23,   22,   24,
    0x80000000 | 23, QMetaType::Int, QMetaType::QString, 0x80000000 | 28,   22,   27,   29,
    QMetaType::Bool, QMetaType::Int, QMetaType::QString, 0x80000000 | 23,   22,   27,   24,
    QMetaType::Bool, QMetaType::Int, QMetaType::QString,   22,   27,
    QMetaType::QStringList, QMetaType::Int,   22,
    0x80000000 | 34, QMetaType::Int,   22,
    QMetaType::Bool,
    QMetaType::Bool,
    0x80000000 | 38,
    0x80000000 | 40,
    0x80000000 | 42,
    0x80000000 | 44,
    0x80000000 | 44,
    0x80000000 | 47,
    0x80000000 | 49,
    QMetaType::Void, QMetaType::QString, QMetaType::Int,   51,   22,
    QMetaType::Void, QMetaType::QString, QMetaType::Int,   27,   22,
    QMetaType::Void, QMetaType::QString, QMetaType::Int,   27,   22,
    QMetaType::Void, QMetaType::QString,   27,
    QMetaType::Void, QMetaType::QString,   56,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString, QMetaType::Int,   59,   22,
    QMetaType::Void,
    QMetaType::Int, 0x80000000 | 62,   63,
    QMetaType::Void, QMetaType::Int, QMetaType::QString,   22,   59,
    QMetaType::Void, QMetaType::Int,   22,
    QMetaType::Void, QMetaType::QString, QMetaType::Int,   59,   22,

       0        // eod
};

void CMyMainWindow::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<CMyMainWindow *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->OnNewProject(); break;
        case 1: _t->OnOpenProject(); break;
        case 2: _t->OnSaveProject(); break;
        case 3: _t->OnSaveAsProject(); break;
        case 4: _t->OnExit(); break;
        case 5: _t->OnConfigureNodeLD_B1(); break;
        case 6: _t->OnConfigureNodeLD_B2(); break;
        case 7: _t->OnClearLog(); break;
        case 8: _t->OnSaveLog(); break;
        case 9: _t->OnAbout(); break;
        case 10: _t->OnRestoreColors(); break;
        case 11: _t->OnUpdateStatus(); break;
        case 12: _t->OnProjectOpened((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 13: _t->OnProjectClosed(); break;
        case 14: _t->OnProjectSaved((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 15: _t->OnSensorCali(); break;
        case 16: _t->initializeJSONExporter(); break;
        case 17: { bool _r = _t->saveSensorDetailedParamsInGroup((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< const UI::SensorParams_1_2(*)>(_a[2])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 18: { bool _r = _t->saveOrUpdateSensorDetailedParamsInGroup((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< const UI::SensorParams_1_2(*)>(_a[2])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 19: { UI::SensorParams_1_2 _r = _t->getSensorDetailedParamsInGroup((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2])),(*reinterpret_cast< bool(*)>(_a[3])));
            if (_a[0]) *reinterpret_cast< UI::SensorParams_1_2*>(_a[0]) = std::move(_r); }  break;
        case 20: { bool _r = _t->updateSensorDetailedParamsInGroup((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2])),(*reinterpret_cast< const UI::SensorParams_1_2(*)>(_a[3])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 21: { bool _r = _t->removeSensorDetailedParamsInGroup((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 22: { QStringList _r = _t->getAllSensorSerialNumbersInGroup((*reinterpret_cast< int(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< QStringList*>(_a[0]) = std::move(_r); }  break;
        case 23: { QList<UI::SensorParams_1_2> _r = _t->getAllSensorDetailedParamsInGroup((*reinterpret_cast< int(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< QList<UI::SensorParams_1_2>*>(_a[0]) = std::move(_r); }  break;
        case 24: { bool _r = _t->validateSensorData();
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 25: { bool _r = _t->validateSensorStorageConsistency();
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 26: { QMap<QString,int> _r = _t->getSensorTypeStatistics();
            if (_a[0]) *reinterpret_cast< QMap<QString,int>*>(_a[0]) = std::move(_r); }  break;
        case 27: { QList<UI::ActuatorGroup_1_2> _r = _t->getAllActuatorGroups_MainDlg();
            if (_a[0]) *reinterpret_cast< QList<UI::ActuatorGroup_1_2>*>(_a[0]) = std::move(_r); }  break;
        case 28: { QList<UI::SensorGroup_1_2> _r = _t->getAllSensorGroups();
            if (_a[0]) *reinterpret_cast< QList<UI::SensorGroup_1_2>*>(_a[0]) = std::move(_r); }  break;
        case 29: { QList<UI::ControlChannelGroup> _r = _t->buildControlChannelGroupsFromUI();
            if (_a[0]) *reinterpret_cast< QList<UI::ControlChannelGroup>*>(_a[0]) = std::move(_r); }  break;
        case 30: { QList<UI::ControlChannelGroup> _r = _t->collectControlChannelGroupsFromUI();
            if (_a[0]) *reinterpret_cast< QList<UI::ControlChannelGroup>*>(_a[0]) = std::move(_r); }  break;
        case 31: { CtrlChanDataManager* _r = _t->getCtrlChanDataManager();
            if (_a[0]) *reinterpret_cast< CtrlChanDataManager**>(_a[0]) = std::move(_r); }  break;
        case 32: { QList<UI::NodeConfigParams> _r = _t->buildHardwareNodeConfigsFromUI();
            if (_a[0]) *reinterpret_cast< QList<UI::NodeConfigParams>*>(_a[0]) = std::move(_r); }  break;
        case 33: _t->onActuatorGroupCreatedBusiness((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 34: _t->onActuatorDeviceCreatedBusiness((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 35: _t->onActuatorDeviceEditedBusiness((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 36: _t->onActuatorDeviceDeletedBusiness((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 37: _t->onActuatorValidationError((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 38: _t->onControlChannelTreeItemSelectionChanged(); break;
        case 39: _t->updateChannelSettingsDisplay((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 40: _t->onEditChannelConfigTriggered(); break;
        case 41: { int _r = _t->extractControlChannelGroupId((*reinterpret_cast< QTreeWidgetItem*(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< int*>(_a[0]) = std::move(_r); }  break;
        case 42: _t->onChannelDataUpdatedFromDragDrop((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 43: _t->onChannelGroupUpdatedFromDragDrop((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 44: _t->refreshChannelSettingsDisplayOnly((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        default: ;
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject CMyMainWindow::staticMetaObject = { {
    QMetaObject::SuperData::link<QMainWindow::staticMetaObject>(),
    qt_meta_stringdata_CMyMainWindow.data,
    qt_meta_data_CMyMainWindow,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *CMyMainWindow::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *CMyMainWindow::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CMyMainWindow.stringdata0))
        return static_cast<void*>(this);
    return QMainWindow::qt_metacast(_clname);
}

int CMyMainWindow::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QMainWindow::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 45)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 45;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 45)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 45;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
