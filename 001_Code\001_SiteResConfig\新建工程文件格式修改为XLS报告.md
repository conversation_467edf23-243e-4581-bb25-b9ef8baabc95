# 新建工程文件格式修改为XLS报告

## 📋 修改概述

根据您的要求，我已经成功将"新建工程"的默认文件格式从xlsx修改为xls，并同时优化了相关的文件对话框设置。

## ✅ 已完成的修改

### 1. 新建工程默认格式修改

**修改前**：
```cpp
// 默认文件名使用xlsx格式
QString defaultFileName = projectName + ".xlsx";

// 文件对话框只显示xlsx格式
tr("Excel文件 (*.xlsx);;所有文件 (*.*)")
```

**修改后**：
```cpp
// 默认文件名使用xls格式
QString defaultFileName = projectName + ".xls";

// 文件对话框优先显示xls格式，同时支持xlsx
tr("Excel文件 (*.xls);;Excel 2007+ (*.xlsx);;所有文件 (*.*)")
```

### 2. 打开工程文件过滤器优化

**修改前**：
```cpp
tr("Excel工程文件 (*.xlsx *.xls);;所有文件 (*.*)")
```

**修改后**：
```cpp
tr("Excel工程文件 (*.xls *.xlsx);;所有文件 (*.*)")
```

### 3. 保存工程默认格式修改

**修改前**：
```cpp
// 默认文件名使用xlsx格式
QString defaultFileName = QString("%1.xlsx").arg(QString::fromStdString(currentProject_->projectName));

// 文件对话框优先显示xlsx格式
tr("Excel文件 (*.xlsx);;JSON文件 (*.json);;所有文件 (*.*)")
```

**修改后**：
```cpp
// 默认文件名使用xls格式
QString defaultFileName = QString("%1.xls").arg(QString::fromStdString(currentProject_->projectName));

// 文件对话框优先显示xls格式
tr("Excel文件 (*.xls);;Excel 2007+ (*.xlsx);;JSON文件 (*.json);;所有文件 (*.*)")
```

## 🔧 具体修改内容

### 修改文件
`MainWindow_Qt_Simple.cpp`

### 修改位置

1. **新建工程** (第589-596行)：
   - 默认文件扩展名：`.xlsx` → `.xls`
   - 文件过滤器：优先显示xls格式

2. **打开工程** (第661-664行)：
   - 文件过滤器：将xls格式放在前面

3. **保存工程** (第696-700行)：
   - 默认文件扩展名：`.xlsx` → `.xls`
   - 文件过滤器：优先显示xls格式

## 🎯 修改效果

### 新建工程
- ✅ 默认文件名：`20250819140500_实验工程.xls`
- ✅ 文件对话框默认选择：Excel文件 (*.xls)
- ✅ 用户仍可选择xlsx格式（在下拉列表中）

### 打开工程
- ✅ 文件过滤器优先显示：`*.xls *.xlsx`
- ✅ 兼容两种格式的文件打开

### 保存工程
- ✅ 默认保存格式：`.xls`
- ✅ 文件对话框优先显示：Excel文件 (*.xls)
- ✅ 用户仍可选择xlsx或JSON格式

## 💡 技术考虑

### 1. 向后兼容性
- ✅ 保持对xlsx格式的完全支持
- ✅ 现有的xlsx工程文件仍可正常打开
- ✅ 用户可以在需要时选择xlsx格式

### 2. 用户体验优化
- ✅ 默认格式统一为xls，减少用户选择困扰
- ✅ 文件对话框清晰标识不同Excel格式
- ✅ 保持所有功能的完整性

### 3. 文件兼容性
- ✅ xls格式兼容性更好，支持更多Excel版本
- ✅ 文件大小通常更小
- ✅ 在某些环境下打开速度更快

## 📊 文件格式对比

| 特性 | XLS格式 | XLSX格式 |
|------|---------|----------|
| **兼容性** | Excel 97-2003及以上 | Excel 2007及以上 |
| **文件大小** | 通常较小 | 通常较大 |
| **打开速度** | 较快 | 较慢 |
| **功能支持** | 基础功能完整 | 高级功能更多 |
| **默认选择** | ✅ 现在是默认 | 可选 |

## 🧪 验证方法

### 测试步骤
1. **新建工程测试**：
   - 执行"新建工程"菜单
   - 验证默认文件名是否为`.xls`格式
   - 验证文件对话框是否优先显示xls格式

2. **保存工程测试**：
   - 创建新工程后执行"保存工程"
   - 验证默认保存格式是否为`.xls`
   - 验证文件对话框格式选项

3. **打开工程测试**：
   - 执行"打开工程"菜单
   - 验证文件过滤器是否优先显示xls格式
   - 测试打开xls和xlsx格式文件

4. **兼容性测试**：
   - 创建xls格式工程文件
   - 创建xlsx格式工程文件
   - 验证两种格式都能正常打开和保存

### 验证要点
- ✅ 默认文件扩展名为`.xls`
- ✅ 文件对话框优先显示xls格式
- ✅ 仍支持xlsx格式选择
- ✅ 两种格式文件都能正常处理
- ✅ 所有功能保持完整

## 📝 使用说明

### 新建工程
1. 执行"新建工程"菜单
2. 系统自动生成`.xls`格式的默认文件名
3. 在文件对话框中，默认选择"Excel文件 (*.xls)"
4. 如需xlsx格式，可在下拉列表中选择"Excel 2007+ (*.xlsx)"

### 保存工程
1. 执行"保存工程"菜单
2. 系统默认使用`.xls`格式
3. 可根据需要选择其他格式（xlsx、JSON）

### 打开工程
1. 执行"打开工程"菜单
2. 文件过滤器显示"Excel工程文件 (*.xls *.xlsx)"
3. 两种格式的文件都能正常打开

## 🎉 总结

修改完成！现在软件的默认工程文件格式已改为XLS，同时保持了对XLSX格式的完全兼容性。

**主要改进**：
- ✅ 统一默认格式为xls
- ✅ 优化文件对话框显示顺序
- ✅ 保持向后兼容性
- ✅ 提升用户体验

用户现在可以享受xls格式的优势（更好的兼容性、更小的文件大小），同时在需要时仍可选择xlsx格式。
