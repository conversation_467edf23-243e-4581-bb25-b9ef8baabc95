@echo off
echo ========================================
echo  main_qt.cpp 编译错误修复测试
echo ========================================

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o
if exist "ui_*.h" del ui_*.h

echo.
echo 生成UI头文件...
uic ui\MainWindow.ui -o ui_MainWindow.h
if errorlevel 1 (
    echo UI文件生成失败！
    pause
    exit /b 1
) else (
    echo ✅ UI头文件生成成功: ui_MainWindow.h
)

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo.
    echo 修复的问题：
    echo ✅ 添加了QPainter头文件包含
    echo ✅ 添加了QFont, QPalette等头文件
    echo ✅ 修复了MainWindow类型声明
    echo ✅ 修复了UI命名空间引用
    echo ✅ 修复了lambda表达式变量捕获
    echo.
    echo 如果仍有编译错误，请检查：
    echo 1. MainWindow_Qt_Simple.h 是否正确包含
    echo 2. UI命名空间是否正确定义
    echo 3. 所有Qt头文件是否正确包含
    echo 4. 项目文件是否包含main_qt.cpp
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！main_qt.cpp 错误已修复
    echo ========================================
    
    if exist "SiteResConfig.exe" (
        echo 可执行文件: SiteResConfig.exe
        echo.
        echo ✅ main_qt.cpp 编译错误已修复！
        echo ✅ 完整的Qt版本主函数可用
        echo ✅ 现代化启动体验
        echo ✅ 深色主题界面
        echo ✅ 启动画面支持
        echo ✅ 异常处理机制
        echo.
        echo 🎨 main_qt.cpp 特色功能:
        echo - 现代化启动画面
        echo - 深色主题界面风格
        echo - 国际化语言支持
        echo - 完善的异常处理
        echo - 系统环境检查
        echo - 专业级用户体验
        echo.
        echo 🔧 修复的编译错误:
        echo - QPainter 类型不完整 → 添加 #include <QPainter>
        echo - MainWindow 未声明 → 修复UI命名空间引用
        echo - lambda变量捕获 → 使用 [=] 捕获所有变量
        echo - 缺少Qt头文件 → 添加完整的头文件包含
        echo.
        echo 启动程序...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
        echo 启动程序...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
        echo 启动程序...
        start release\SiteResConfig.exe
    ) else (
        echo 警告: 找不到可执行文件！
    )
)

echo.
echo 📖 main_qt.cpp 使用说明:
echo.
echo 🎯 两个主函数版本:
echo - main_simple.cpp: 简化版本，快速启动
echo - main_qt.cpp: 完整版本，专业体验
echo.
echo 🔧 选择使用方式:
echo 1. 修改项目文件中的SOURCES，选择对应的main函数
echo 2. 或者重命名文件来切换版本
echo.
echo 🎨 main_qt.cpp 优势:
echo - 启动画面提供视觉反馈
echo - 深色主题现代化界面
echo - 完善的错误处理和提示
echo - 系统环境自动检查
echo - 国际化语言支持
echo.
pause
