@echo off
chcp 65001 >nul
echo ========================================
echo 🧪 载荷1传感器选择列修复测试
echo ========================================
echo.

echo 📁 当前目录: %CD%
echo.

REM 检查Qt环境
echo 🔍 检查Qt环境...
where qmake >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: Qt环境未找到！
    echo 请确保Qt已安装并添加到PATH环境变量中
    echo.
    pause
    exit /b 1
)

echo ✅ Qt环境检查通过
echo.

REM 清理之前的构建
echo 🧹 清理之前的构建...
if exist debug rmdir /s /q debug
if exist release rmdir /s /q release
if exist *.o del *.o
if exist *.exe del *.exe
echo ✅ 清理完成
echo.

REM 生成Makefile
echo 🔧 生成Makefile...
qmake test_load1_sensor_fix.pro
if %errorlevel% neq 0 (
    echo ❌ 错误: qmake失败！
    echo.
    pause
    exit /b 1
)
echo ✅ Makefile生成成功
echo.

REM 编译项目
echo 🚀 开始编译...
mingw32-make
if %errorlevel% neq 0 (
    echo ❌ 编译失败！
    echo.
    echo 📋 编译错误信息：
    echo.
    mingw32-make 2>&1
    echo.
    echo 🔍 请检查以下可能的问题：
    echo 1. 源代码语法错误
    echo 2. 头文件路径问题
    echo 3. 依赖库缺失
    echo.
    pause
    exit /b 1
)

echo ✅ 编译成功！
echo.

REM 检查生成的可执行文件
echo 🔍 检查生成的文件...
if exist debug\test_load1_sensor_fix.exe (
    echo ✅ 可执行文件生成成功: debug\test_load1_sensor_fix.exe
    echo.
    echo 🎯 载荷1传感器选择列修复测试程序已准备就绪！
    echo.
    echo 📋 修复内容总结：
    echo ✅ 修复了载荷1传感器选择列属性名不一致问题
    echo ✅ 统一使用"载荷1传感器选择"作为属性键名
    echo ✅ 空值数据现在正确显示为空而不是"已配置"
    echo ✅ 有数据的行正确显示数据内容
    echo.
    echo 🚀 运行测试程序...
    echo.
    start debug\test_load1_sensor_fix.exe
) else (
    echo ❌ 错误: 可执行文件未生成！
    echo.
    pause
    exit /b 1
)

echo.
echo 🎉 载荷1传感器选择列修复测试完成！
echo.
echo 📋 测试说明：
echo 1. 程序启动后会显示详细信息面板
echo 2. 点击"测试载荷1传感器选择列修复"按钮
echo 3. 检查载荷1传感器选择列是否正确显示数据
echo 4. CH1应该显示"载荷传感器_001"
echo 5. CH2应该显示空值（因为设置为空字符串）
echo.
pause 