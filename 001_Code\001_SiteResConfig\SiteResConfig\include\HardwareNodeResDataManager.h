#ifndef HARDWARE_NODE_RES_DATA_MANAGER_H
#define HARDWARE_NODE_RES_DATA_MANAGER_H

/**
 * @file HardwareNodeResDataManager.h
 * @brief 硬件节点资源数据管理器
 * @details 管理硬件节点组和硬件节点参数的存储、检索和操作
 * <AUTHOR> Agent
 * @date 2024-01-15
 * @version 1.0.0
 */

#include <QObject>
#include <QMap>
#include <QList>
#include <QString>
#include <QDateTime>
#include <QDebug>
#include <QMutex>
#include <QMutexLocker>
#include "DataModels_Fixed.h"
#include "HardwareNodeStructs.h"  // 🔄 修正：使用独立的硬件节点结构体定义
#include "CreateHardwareNodeDialog.h"  // 🔄 修正：使用原有的CreateHardwareNodeParams结构体

// ============================================================================
// 硬件节点相关数据结构 - 使用原有的正确实现
// ============================================================================
// 注意：使用原有的NodeConfigParams和CreateHardwareNodeParams结构体
// 不重新定义，避免破坏已有的正确实现

/**
 * @brief 硬件节点资源数据管理器类
 * @details 提供硬件节点组和硬件节点参数的完整管理功能
 */
class HardwareNodeResDataManager : public QObject {
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父对象指针
     */
    explicit HardwareNodeResDataManager(QObject* parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~HardwareNodeResDataManager();

    // ============================================================================
    // 硬件节点组管理方法
    // ============================================================================

    /**
     * @brief 创建硬件节点组
     * @param groupName 组名称
     * @return 创建是否成功
     */
    bool createHardwareNodeGroup(const QString& groupName);

    /**
     * @brief 更新硬件节点组
     * @param groupId 组ID
     * @param groupName 新的组名称
     * @return 更新是否成功
     */
    bool updateHardwareNodeGroup(int groupId, const QString& groupName);

    /**
     * @brief 删除硬件节点组
     * @param groupId 组ID
     * @return 删除是否成功
     */
    bool deleteHardwareNodeGroup(int groupId);

    /**
     * @brief 获取所有硬件节点配置
     * @return 硬件节点配置列表
     */
    QList<UI::NodeConfigParams> getAllHardwareNodeConfigs() const;

    /**
     * @brief 获取指定硬件节点配置
     * @param nodeId 节点ID
     * @return 硬件节点配置对象
     */
    UI::NodeConfigParams getHardwareNodeConfig(const QString& nodeId) const;

    /**
     * @brief 检查硬件节点组是否存在
     * @param groupId 组ID
     * @return 是否存在
     */
    bool hasHardwareNodeGroup(int groupId) const;

    // ============================================================================
    // 硬件节点管理方法
    // ============================================================================

    /**
     * @brief 添加硬件节点配置
     * @param nodeConfig 硬件节点配置参数
     * @return 添加是否成功
     */
    bool addHardwareNodeConfig(const UI::NodeConfigParams& nodeConfig);

    /**
     * @brief 更新硬件节点配置
     * @param nodeConfig 硬件节点配置参数
     * @return 更新是否成功
     */
    bool updateHardwareNodeConfig(const UI::NodeConfigParams& nodeConfig);

    /**
     * @brief 添加或更新硬件节点配置
     * @param nodeConfig 硬件节点配置参数
     * @param oldNodeName 旧节点名称（用于名称更改时保持顺序）
     * @return 操作是否成功
     */
    bool addOrUpdateHardwareNodeConfig(const UI::NodeConfigParams& nodeConfig, const QString& oldNodeName = QString());

    /**
     * @brief 删除硬件节点配置
     * @param nodeName 节点名称
     * @return 删除是否成功
     */
    bool removeHardwareNodeConfig(const QString& nodeName);

    /**
     * @brief 获取所有硬件节点配置
     * @return 硬件节点配置列表
     */
    QList<UI::NodeConfigParams> getAllNodeConfigs() const;

    // ============================================================================
    // 数据统计和查询方法
    // ============================================================================

    /**
     * @brief 获取硬件节点配置总数
     * @return 节点总数
     */
    int getTotalNodeCount() const;

    /**
     * @brief 按节点名称查找配置
     * @param nodeName 节点名称
     * @return 是否存在
     */
    bool hasNodeConfig(const QString& nodeName) const;

    /**
     * @brief 清空所有数据
     */
    void clearAllData();

    /**
     * @brief 清空所有硬件节点配置（与clearAllData相同）
     */
    void clearAllHardwareNodeConfigs();

    // ============================================================================
    // 数据导入导出方法
    // ============================================================================

    /**
     * @brief 导出到CSV文件
     * @param filePath 文件路径
     * @return 导出是否成功
     */
    bool exportToCSV(const QString& filePath) const;

    /**
     * @brief 导出到JSON文件
     * @param filePath 文件路径
     * @return 导出是否成功
     */
    // 🚫 已注释：独立JSON导出功能已废弃
    // bool exportToJSON(const QString& filePath) const;

    /**
     * @brief 从JSON文件导入
     * @param filePath 文件路径
     * @return 导入是否成功
     */
    bool importFromJSON(const QString& filePath);

signals:
    /**
     * @brief 硬件节点配置添加信号
     * @param nodeName 节点名称
     */
    void hardwareNodeConfigAdded(const QString& nodeName);

    /**
     * @brief 硬件节点配置更新信号
     * @param nodeName 节点名称
     */
    void hardwareNodeConfigUpdated(const QString& nodeName);

    /**
     * @brief 硬件节点配置删除信号
     * @param nodeName 节点名称
     */
    void hardwareNodeConfigRemoved(const QString& nodeName);

private:
    // ============================================================================
    // 私有成员变量
    // ============================================================================

    QMap<QString, UI::NodeConfigParams> nodeConfigStorage_;  // 硬件节点配置存储 (key: nodeName)
    QStringList nodeAdditionOrder_;                      // 🆕 新增：维护节点添加顺序的列表
    mutable QMutex dataMutex_;                           // 数据访问互斥锁

    // ============================================================================
    // 私有辅助方法
    // ============================================================================

    /**
     * @brief 验证硬件节点配置数据
     * @param nodeConfig 硬件节点配置
     * @return 验证是否通过
     */
    bool validateNodeConfig(const UI::NodeConfigParams& nodeConfig) const;
};

#endif // HARDWARE_NODE_RES_DATA_MANAGER_H
