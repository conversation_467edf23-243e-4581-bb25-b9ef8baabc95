@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🔍 调试传感器组名称显示问题
echo ========================================
echo.

echo 📋 问题分析:
echo 1. ❌ 传感器详细配置中有多余测试数据
echo 2. ❌ 组名称只在第一行显示功能在真实数据中没有实现
echo.

echo 🔧 已完成的修复:
echo.
echo 1. ✅ 删除测试数据自动创建:
echo    - 移除了 sensorDataManager_-^>createTestSensorGroups()
echo    - 确保启动时只清理数据，不创建测试数据
echo.
echo 2. ✅ 修复组名称显示逻辑:
echo    - 使用明确的 if-else 语句
echo    - 使用空字符串 "" 而不是 QString()
echo    - 添加详细的调试信息
echo.
echo 3. ✅ 添加调试信息:
echo    - 主窗口: 输出SensorDataManager中的传感器组详情
echo    - XLSDataExporter: 输出每个传感器的组名称显示状态
echo.

echo 🚀 调试步骤:
echo.
echo 步骤1: 重新编译应用程序
echo - 确保所有修复代码生效
echo.
echo 步骤2: 启动应用程序
echo - 检查日志: "已清理所有传感器数据，传感器数据管理器已初始化"
echo - 确认没有创建测试数据
echo.
echo 步骤3: 手动创建传感器组和传感器
echo - 右键"传感器"节点 -^> 新建传感器组
echo - 创建几个不同类型的传感器组
echo - 在每个组中添加多个传感器
echo.
echo 步骤4: 导出传感器详细配置
echo - 点击: 数据导出 -^> 导出传感器详细信息到Excel
echo - 观察控制台调试输出
echo.
echo 步骤5: 检查调试输出
echo 主窗口调试信息:
echo   "SensorDataManager中的传感器组数量: X"
echo   "组1: ID=1, 名称=载荷_传感器组, 传感器数量=3"
echo   "  传感器1: 序列号=SEN001, 类型=载荷传感器"
echo   "  传感器2: 序列号=SEN002, 类型=载荷传感器"
echo.
echo XLSDataExporter调试信息:
echo   "组ID: 1, 传感器索引: 0, 组名称: 载荷_传感器组, 显示组名称: 是, 传感器序列号: SEN001"
echo   "组ID: 1, 传感器索引: 1, 组名称: 载荷_传感器组, 显示组名称: 否, 传感器序列号: SEN002"
echo   "组ID: 1, 传感器索引: 2, 组名称: 载荷_传感器组, 显示组名称: 否, 传感器序列号: SEN003"
echo.
echo 步骤6: 验证Excel文件
echo - 打开生成的Excel文件
echo - 检查"传感器组名称"列（第2列）
echo - 确认组名称只在每组第一行显示
echo.

echo ✅ 预期结果:
echo.
echo 1. 无测试数据:
echo    - Excel中不应包含SEN001-SEN006等测试传感器
echo    - 只包含手动创建的真实传感器数据
echo.
echo 2. 调试输出正确:
echo    - 主窗口正确显示传感器组数量和详情
echo    - XLSDataExporter正确显示每个传感器的组名称显示状态
echo.
echo 3. Excel格式正确:
echo    - 组名称只在每组第一行显示
echo    - 同组其他行的组名称列为空白
echo    - 组序号在同组所有行都显示相同数字
echo.

echo 🔍 问题排查:
echo.
echo 如果仍有问题，检查以下方面:
echo.
echo 1. 数据源问题:
echo    - SensorDataManager中是否有正确的传感器组数据
echo    - 传感器是否正确添加到传感器组中
echo    - 组ID是否正确分配和管理
echo.
echo 2. 导出路径问题:
echo    - 是否使用了正确的导出方法
echo    - 是否调用了 addSensorGroupDetailToExcel 方法
echo    - 是否有其他导出路径绕过了组名称显示逻辑
echo.
echo 3. 格式问题:
echo    - Excel单元格是否真正为空
echo    - 格式是否正确应用
echo    - 边框和背景色是否正确
echo.

echo 💡 调试技巧:
echo.
echo 1. 查看控制台输出:
echo    - 启动时的初始化信息
echo    - 传感器创建时的日志
echo    - 导出时的调试信息
echo.
echo 2. 检查数据完整性:
echo    - 传感器组数量是否正确
echo    - 每个组中的传感器数量是否正确
echo    - 传感器参数是否完整
echo.
echo 3. 验证Excel内容:
echo    - 使用Excel打开文件
echo    - 检查单元格内容（不只是显示）
echo    - 确认格式和边框
echo.

echo 🎯 成功标准:
echo.
echo 1. ✅ 无多余测试数据
echo 2. ✅ 组名称只在第一行显示
echo 3. ✅ 调试信息输出正确
echo 4. ✅ Excel格式符合要求
echo 5. ✅ 数据完整准确
echo.

echo 📝 后续操作:
echo 如果调试成功，可以：
echo 1. 移除调试信息（可选）
echo 2. 在实际项目中使用
echo 3. 验证其他导出功能
echo.

pause
