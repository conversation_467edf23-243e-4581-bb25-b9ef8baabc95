/**
 * @file compile_test.cpp
 * @brief 编译测试文件，验证修复是否成功
 * <AUTHOR> Agent
 * @date 2024-01-15
 */

#include "HardwareNodeResDataManager.h"
#include "XLSDataExporter.h"
#include "HardwareNodeStructs.h"  // 🔄 修正：包含独立的硬件节点结构体定义
#include <QCoreApplication>

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    // 测试 HardwareNodeResDataManager 信号
    HardwareNodeResDataManager manager;
    
    // 测试信号连接（验证信号名称正确）
    QObject::connect(&manager, &HardwareNodeResDataManager::hardwareNodeConfigAdded,
                    [](const QString& nodeName) {
                        qDebug() << "节点配置已添加:" << nodeName;
                    });
    
    QObject::connect(&manager, &HardwareNodeResDataManager::hardwareNodeConfigUpdated,
                    [](const QString& nodeName) {
                        qDebug() << "节点配置已更新:" << nodeName;
                    });
    
    QObject::connect(&manager, &HardwareNodeResDataManager::hardwareNodeConfigRemoved,
                    [](const QString& nodeName) {
                        qDebug() << "节点配置已删除:" << nodeName;
                    });
    
    // 测试 XLSDataExporter 方法（验证没有重复声明和不完整类型错误）
    XLSDataExporter exporter;
    QList<UI::NodeConfigParams> nodeConfigs;

    // 创建一个测试节点配置
    UI::NodeConfigParams testConfig;
    testConfig.nodeName = "测试节点";
    testConfig.channelCount = 2;
    nodeConfigs.append(testConfig);

    // 这应该能正常编译，没有重复声明错误和不完整类型错误
    bool result = exporter.exportHardwareNodeDetails(nodeConfigs, "test.xlsx");
    
    qDebug() << "编译测试成功！";
    qDebug() << "HardwareNodeResDataManager 信号连接正常";
    qDebug() << "XLSDataExporter 方法声明正常";
    qDebug() << "UI::NodeConfigParams 类型识别正常";
    qDebug() << "方法名冲突已解决";
    
    return 0;
}
