/********************************************************************************
** Form generated from reading UI file 'CreateHardwareNodeDialog.ui'
**
** Created by: Qt User Interface Compiler version 5.14.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_CREATEHARDWARENODEDIALOG_H
#define UI_CREATEHARDWARENODEDIALOG_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QDialog>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QScrollArea>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_CreateHardwareNodeDialog
{
public:
    QVBoxLayout *verticalLayout;
    QHBoxLayout *nodeNameLayout;
    QLabel *nodeNameLabel;
    QLineEdit *nodeNameEdit;
    QHBoxLayout *channelCountLayout;
    QLabel *channelCountLabel;
    QSpinBox *channelCountSpinBox;
    QSpacerItem *horizontalSpacer;
    QGroupBox *channel1GroupBox;
    QGridLayout *ch1GridLayout;
    QLabel *ch1IpLabel;
    QLineEdit *ch1IpEdit;
    QLabel *ch1PortLabel;
    QSpinBox *ch1PortSpinBox;
    QGroupBox *channel2GroupBox;
    QGridLayout *ch2GridLayout;
    QLabel *ch2IpLabel;
    QLineEdit *ch2IpEdit;
    QLabel *ch2PortLabel;
    QSpinBox *ch2PortSpinBox;
    QScrollArea *scrollArea;
    QWidget *scrollAreaWidgetContents;
    QHBoxLayout *buttonLayout;
    QSpacerItem *horizontalSpacer_2;
    QPushButton *okButton;
    QPushButton *cancelButton;

    void setupUi(QDialog *CreateHardwareNodeDialog)
    {
        if (CreateHardwareNodeDialog->objectName().isEmpty())
            CreateHardwareNodeDialog->setObjectName(QString::fromUtf8("CreateHardwareNodeDialog"));
        CreateHardwareNodeDialog->resize(450, 400);
        CreateHardwareNodeDialog->setModal(true);
        verticalLayout = new QVBoxLayout(CreateHardwareNodeDialog);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        nodeNameLayout = new QHBoxLayout();
        nodeNameLayout->setObjectName(QString::fromUtf8("nodeNameLayout"));
        nodeNameLabel = new QLabel(CreateHardwareNodeDialog);
        nodeNameLabel->setObjectName(QString::fromUtf8("nodeNameLabel"));

        nodeNameLayout->addWidget(nodeNameLabel);

        nodeNameEdit = new QLineEdit(CreateHardwareNodeDialog);
        nodeNameEdit->setObjectName(QString::fromUtf8("nodeNameEdit"));

        nodeNameLayout->addWidget(nodeNameEdit);


        verticalLayout->addLayout(nodeNameLayout);

        channelCountLayout = new QHBoxLayout();
        channelCountLayout->setObjectName(QString::fromUtf8("channelCountLayout"));
        channelCountLabel = new QLabel(CreateHardwareNodeDialog);
        channelCountLabel->setObjectName(QString::fromUtf8("channelCountLabel"));

        channelCountLayout->addWidget(channelCountLabel);

        channelCountSpinBox = new QSpinBox(CreateHardwareNodeDialog);
        channelCountSpinBox->setObjectName(QString::fromUtf8("channelCountSpinBox"));
        channelCountSpinBox->setMinimumSize(QSize(80, 0));
        channelCountSpinBox->setMinimum(1);
        channelCountSpinBox->setMaximum(2);
        channelCountSpinBox->setValue(2);

        channelCountLayout->addWidget(channelCountSpinBox);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        channelCountLayout->addItem(horizontalSpacer);


        verticalLayout->addLayout(channelCountLayout);

        channel1GroupBox = new QGroupBox(CreateHardwareNodeDialog);
        channel1GroupBox->setObjectName(QString::fromUtf8("channel1GroupBox"));
        ch1GridLayout = new QGridLayout(channel1GroupBox);
        ch1GridLayout->setObjectName(QString::fromUtf8("ch1GridLayout"));
        ch1IpLabel = new QLabel(channel1GroupBox);
        ch1IpLabel->setObjectName(QString::fromUtf8("ch1IpLabel"));

        ch1GridLayout->addWidget(ch1IpLabel, 0, 0, 1, 1);

        ch1IpEdit = new QLineEdit(channel1GroupBox);
        ch1IpEdit->setObjectName(QString::fromUtf8("ch1IpEdit"));

        ch1GridLayout->addWidget(ch1IpEdit, 0, 1, 1, 1);

        ch1PortLabel = new QLabel(channel1GroupBox);
        ch1PortLabel->setObjectName(QString::fromUtf8("ch1PortLabel"));

        ch1GridLayout->addWidget(ch1PortLabel, 1, 0, 1, 1);

        ch1PortSpinBox = new QSpinBox(channel1GroupBox);
        ch1PortSpinBox->setObjectName(QString::fromUtf8("ch1PortSpinBox"));
        ch1PortSpinBox->setMinimum(1);
        ch1PortSpinBox->setMaximum(65535);
        ch1PortSpinBox->setValue(8080);

        ch1GridLayout->addWidget(ch1PortSpinBox, 1, 1, 1, 1);


        verticalLayout->addWidget(channel1GroupBox);

        channel2GroupBox = new QGroupBox(CreateHardwareNodeDialog);
        channel2GroupBox->setObjectName(QString::fromUtf8("channel2GroupBox"));
        ch2GridLayout = new QGridLayout(channel2GroupBox);
        ch2GridLayout->setObjectName(QString::fromUtf8("ch2GridLayout"));
        ch2IpLabel = new QLabel(channel2GroupBox);
        ch2IpLabel->setObjectName(QString::fromUtf8("ch2IpLabel"));

        ch2GridLayout->addWidget(ch2IpLabel, 0, 0, 1, 1);

        ch2IpEdit = new QLineEdit(channel2GroupBox);
        ch2IpEdit->setObjectName(QString::fromUtf8("ch2IpEdit"));

        ch2GridLayout->addWidget(ch2IpEdit, 0, 1, 1, 1);

        ch2PortLabel = new QLabel(channel2GroupBox);
        ch2PortLabel->setObjectName(QString::fromUtf8("ch2PortLabel"));

        ch2GridLayout->addWidget(ch2PortLabel, 1, 0, 1, 1);

        ch2PortSpinBox = new QSpinBox(channel2GroupBox);
        ch2PortSpinBox->setObjectName(QString::fromUtf8("ch2PortSpinBox"));
        ch2PortSpinBox->setMinimum(1);
        ch2PortSpinBox->setMaximum(65535);
        ch2PortSpinBox->setValue(8081);

        ch2GridLayout->addWidget(ch2PortSpinBox, 1, 1, 1, 1);


        verticalLayout->addWidget(channel2GroupBox);

        scrollArea = new QScrollArea(CreateHardwareNodeDialog);
        scrollArea->setObjectName(QString::fromUtf8("scrollArea"));
        QSizePolicy sizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(scrollArea->sizePolicy().hasHeightForWidth());
        scrollArea->setSizePolicy(sizePolicy);
        scrollArea->setSizeAdjustPolicy(QAbstractScrollArea::AdjustToContents);
        scrollArea->setWidgetResizable(true);
        scrollAreaWidgetContents = new QWidget();
        scrollAreaWidgetContents->setObjectName(QString::fromUtf8("scrollAreaWidgetContents"));
        scrollAreaWidgetContents->setGeometry(QRect(0, 0, 430, 125));
        scrollArea->setWidget(scrollAreaWidgetContents);

        verticalLayout->addWidget(scrollArea);

        buttonLayout = new QHBoxLayout();
        buttonLayout->setObjectName(QString::fromUtf8("buttonLayout"));
        horizontalSpacer_2 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        buttonLayout->addItem(horizontalSpacer_2);

        okButton = new QPushButton(CreateHardwareNodeDialog);
        okButton->setObjectName(QString::fromUtf8("okButton"));

        buttonLayout->addWidget(okButton);

        cancelButton = new QPushButton(CreateHardwareNodeDialog);
        cancelButton->setObjectName(QString::fromUtf8("cancelButton"));

        buttonLayout->addWidget(cancelButton);


        verticalLayout->addLayout(buttonLayout);


        retranslateUi(CreateHardwareNodeDialog);
        QObject::connect(cancelButton, SIGNAL(clicked()), CreateHardwareNodeDialog, SLOT(reject()));

        okButton->setDefault(true);


        QMetaObject::connectSlotsByName(CreateHardwareNodeDialog);
    } // setupUi

    void retranslateUi(QDialog *CreateHardwareNodeDialog)
    {
        CreateHardwareNodeDialog->setWindowTitle(QCoreApplication::translate("CreateHardwareNodeDialog", "\345\210\233\345\273\272\347\241\254\344\273\266\350\212\202\347\202\271", nullptr));
        nodeNameLabel->setText(QCoreApplication::translate("CreateHardwareNodeDialog", "\350\212\202\347\202\271\345\220\215\347\247\260:", nullptr));
        nodeNameEdit->setText(QCoreApplication::translate("CreateHardwareNodeDialog", "LD-B1", nullptr));
        nodeNameEdit->setPlaceholderText(QCoreApplication::translate("CreateHardwareNodeDialog", "\350\257\267\350\276\223\345\205\245\350\212\202\347\202\271\345\220\215\347\247\260", nullptr));
        channelCountLabel->setText(QCoreApplication::translate("CreateHardwareNodeDialog", "\351\200\232\351\201\223\346\225\260\351\207\217:", nullptr));
        channel1GroupBox->setTitle(QCoreApplication::translate("CreateHardwareNodeDialog", "CH1 \351\200\232\351\201\223\351\205\215\347\275\256", nullptr));
        ch1IpLabel->setText(QCoreApplication::translate("CreateHardwareNodeDialog", "IP\345\234\260\345\235\200:", nullptr));
        ch1IpEdit->setText(QCoreApplication::translate("CreateHardwareNodeDialog", "*************", nullptr));
        ch1IpEdit->setPlaceholderText(QCoreApplication::translate("CreateHardwareNodeDialog", "\350\257\267\350\276\223\345\205\245IP\345\234\260\345\235\200", nullptr));
        ch1PortLabel->setText(QCoreApplication::translate("CreateHardwareNodeDialog", "\347\253\257\345\217\243:", nullptr));
        channel2GroupBox->setTitle(QCoreApplication::translate("CreateHardwareNodeDialog", "CH2 \351\200\232\351\201\223\351\205\215\347\275\256", nullptr));
        ch2IpLabel->setText(QCoreApplication::translate("CreateHardwareNodeDialog", "IP\345\234\260\345\235\200:", nullptr));
        ch2IpEdit->setText(QCoreApplication::translate("CreateHardwareNodeDialog", "*************", nullptr));
        ch2IpEdit->setPlaceholderText(QCoreApplication::translate("CreateHardwareNodeDialog", "\350\257\267\350\276\223\345\205\245IP\345\234\260\345\235\200", nullptr));
        ch2PortLabel->setText(QCoreApplication::translate("CreateHardwareNodeDialog", "\347\253\257\345\217\243:", nullptr));
        okButton->setText(QCoreApplication::translate("CreateHardwareNodeDialog", "\345\210\233\345\273\272", nullptr));
        cancelButton->setText(QCoreApplication::translate("CreateHardwareNodeDialog", "\345\217\226\346\266\210", nullptr));
    } // retranslateUi

};

namespace Ui {
    class CreateHardwareNodeDialog: public Ui_CreateHardwareNodeDialog {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_CREATEHARDWARENODEDIALOG_H
