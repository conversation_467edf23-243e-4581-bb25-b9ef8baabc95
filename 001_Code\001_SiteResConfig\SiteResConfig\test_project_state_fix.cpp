#include <QApplication>
#include <QMainWindow>
#include <QTreeWidget>
#include <QVBoxLayout>
#include <QWidget>
#include <QPushButton>
#include <QMessageBox>
#include <QDebug>
#include <QTimer>
#include <QProgressBar>
#include <QLabel>

// 模拟主窗口类
class MockMainWindow : public QMainWindow {
    Q_OBJECT
    
public:
    MockMainWindow(QWidget *parent = nullptr) : QMainWindow(parent) {
        setWindowTitle("工程状态修复测试");
        resize(1000, 700);
        
        // 创建中心部件
        QWidget *centralWidget = new QWidget(this);
        setCentralWidget(centralWidget);
        
        QVBoxLayout *layout = new QVBoxLayout(centralWidget);
        
        // 创建状态显示标签
        m_statusLabel = new QLabel("状态: 未初始化", this);
        m_statusLabel->setStyleSheet("QLabel { font-size: 14pt; font-weight: bold; padding: 10px; }");
        layout->addWidget(m_statusLabel);
        
        // 创建进度条
        m_progressBar = new QProgressBar(this);
        m_progressBar->setVisible(false);
        layout->addWidget(m_progressBar);
        
        // 创建树形控件
        m_treeWidget = new QTreeWidget(this);
        m_treeWidget->setHeaderLabels({"名称", "类型", "状态", "详细信息"});
        layout->addWidget(m_treeWidget);
        
        // 创建详细信息面板
        m_detailPanel = new QWidget(this);
        m_detailPanel->setMinimumHeight(200);
        m_detailPanel->setStyleSheet("QWidget { background-color: #f0f0f0; border: 1px solid #ccc; }");
        layout->addWidget(m_detailPanel);
        
        // 创建测试按钮区域
        QHBoxLayout *buttonLayout = new QHBoxLayout();
        
        QPushButton *newProjectButton = new QPushButton("新建工程", this);
        QPushButton *openProjectButton = new QPushButton("打开工程", this);
        QPushButton *testDetailButton = new QPushButton("测试详细信息", this);
        QPushButton *clearButton = new QPushButton("清空", this);
        
        buttonLayout->addWidget(newProjectButton);
        buttonLayout->addWidget(openProjectButton);
        buttonLayout->addWidget(testDetailButton);
        buttonLayout->addWidget(clearButton);
        buttonLayout->addStretch();
        
        layout->addLayout(buttonLayout);
        
        // 连接信号
        connect(newProjectButton, &QPushButton::clicked, this, &MockMainWindow::onNewProject);
        connect(openProjectButton, &QPushButton::clicked, this, &MockMainWindow::onOpenProject);
        connect(testDetailButton, &QPushButton::clicked, this, &MockMainWindow::onTestDetail);
        connect(clearButton, &QPushButton::clicked, this, &MockMainWindow::onClear);
        connect(m_treeWidget, &QTreeWidget::itemClicked, this, &MockMainWindow::onItemClicked);
        
        // 初始化状态
        m_hasProject = false;
        updateStatus();
        
        qDebug() << "MockMainWindow: 初始化完成";
    }
    
private slots:
    void onNewProject() {
        qDebug() << "🎯 开始新建工程流程...";
        
        // 模拟新建工程
        m_hasProject = true;
        m_projectName = "测试工程_" + QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss");
        
        // 初始化树形控件数据
        setupTreeData();
        
        // 模拟工程打开完成
        onProjectOpened("新建工程", m_projectName);
        
        qDebug() << "✅ 新建工程完成:" << m_projectName;
    }
    
    void onOpenProject() {
        qDebug() << "🎯 开始打开工程流程...";
        
        // 模拟打开工程
        m_hasProject = true;
        m_projectName = "已保存工程_" + QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss");
        
        // 显示进度条
        m_progressBar->setVisible(true);
        m_progressBar->setRange(0, 100);
        m_progressBar->setValue(0);
        
        // 模拟加载过程
        QTimer::singleShot(100, this, [this]() {
            m_progressBar->setValue(50);
            qDebug() << "🔄 工程数据加载中...";
        });
        
        QTimer::singleShot(200, this, [this]() {
            m_progressBar->setValue(100);
            qDebug() << "🔄 工程数据加载完成";
            
            // 初始化树形控件数据
            setupTreeData();
            
            // 模拟工程打开完成（使用延迟确保数据加载完成）
            QTimer::singleShot(100, this, [this]() {
                onProjectOpened("打开工程", m_projectName);
            });
            
            // 隐藏进度条
            QTimer::singleShot(500, m_progressBar, &QProgressBar::setVisible);
        });
        
        qDebug() << "✅ 打开工程完成:" << m_projectName;
    }
    
    void onTestDetail() {
        if (!m_hasProject) {
            QMessageBox::warning(this, "警告", "请先新建或打开工程！");
            return;
        }
        
        qDebug() << "🧪 测试详细信息功能...";
        
        // 模拟点击树形控件节点
        if (m_treeWidget->topLevelItemCount() > 0) {
            QTreeWidgetItem *item = m_treeWidget->topLevelItem(0);
            if (item->childCount() > 0) {
                QTreeWidgetItem *child = item->child(0);
                qDebug() << "点击节点:" << child->text(0);
                onItemClicked(child, 0);
            }
        }
    }
    
    void onClear() {
        qDebug() << "🗑️ 清空工程数据...";
        
        m_hasProject = false;
        m_projectName.clear();
        m_treeWidget->clear();
        m_detailPanel->setStyleSheet("QWidget { background-color: #f0f0f0; border: 1px solid #ccc; }");
        
        updateStatus();
        
        qDebug() << "✅ 工程数据已清空";
    }
    
    void onItemClicked(QTreeWidgetItem *item, int column) {
        Q_UNUSED(column)
        
        if (!item) return;
        
        QString itemText = item->text(0);
        QString itemType = item->text(1);
        
        qDebug() << "点击节点:" << itemText << "类型:" << itemType;
        
        // 模拟详细信息显示
        if (m_hasProject) {
            QString detailInfo = QString("节点: %1\n类型: %2\n状态: 正常\n详细信息功能: 正常")
                               .arg(itemText).arg(itemType);
            
            // 更新详细信息面板
            QLabel *detailLabel = m_detailPanel->findChild<QLabel>();
            if (!detailLabel) {
                detailLabel = new QLabel(m_detailPanel);
                QVBoxLayout *detailLayout = new QVBoxLayout(m_detailPanel);
                detailLayout->addWidget(detailLabel);
                detailLayout->setContentsMargins(10, 10, 10, 10);
            }
            
            detailLabel->setText(detailInfo);
            detailLabel->setStyleSheet("QLabel { font-size: 12pt; color: #2c3e50; }");
            
            qDebug() << "✅ 详细信息显示成功";
        } else {
            qDebug() << "❌ 工程未加载，详细信息功能异常";
        }
    }
    
private:
    void onProjectOpened(const QString &source, const QString &projectName) {
        qDebug() << QString("📂 项目打开完成处理：%1 - %2").arg(projectName).arg(source);
        
        // 模拟数据管理器同步验证
        if (!ensureDataManagerSync()) {
            qDebug() << "❌ 数据管理器同步失败，详细信息功能可能异常";
            QMessageBox::warning(this, "警告", 
                "工程数据加载完成，但数据同步存在问题。\n详细信息功能可能无法正常工作。");
        }
        
        // 模拟重新初始化详细信息面板
        reinitializeDetailInfoPanel();
        
        // 更新状态
        updateStatus();
        
        // 模拟验证工程状态完整性
        validateProjectState();
        
        qDebug() << QString("✅ 项目打开处理完成");
    }
    
    bool ensureDataManagerSync() {
        qDebug() << "🔄 开始验证数据管理器同步状态...";
        
        bool syncSuccess = true;
        
        // 模拟验证各种数据管理器
        qDebug() << "✅ 传感器数据管理器状态正常";
        qDebug() << "✅ 作动器数据管理器状态正常";
        qDebug() << "✅ 控制通道数据管理器状态正常";
        qDebug() << "✅ 硬件节点数据管理器状态正常";
        
        qDebug() << QString("🔄 数据管理器同步验证完成，结果：%1").arg(syncSuccess ? "成功" : "失败");
        return syncSuccess;
    }
    
    void reinitializeDetailInfoPanel() {
        qDebug() << "🔄 重新初始化详细信息面板...";
        
        // 模拟重新初始化
        m_detailPanel->setStyleSheet("QWidget { background-color: #e8f5e8; border: 2px solid #27ae60; }");
        
        qDebug() << "✅ 详细信息面板重新初始化成功";
    }
    
    void validateProjectState() {
        qDebug() << "🔍 开始验证工程状态完整性...";
        
        bool isValid = true;
        QStringList issues;
        
        // 验证数据管理器状态
        if (!m_hasProject) {
            isValid = false;
            issues << "工程未加载";
        }
        
        // 验证树形控件数据
        if (m_treeWidget->topLevelItemCount() == 0) {
            isValid = false;
            issues << "树形控件数据为空";
        }
        
        // 验证详细信息面板
        if (!m_detailPanel) {
            isValid = false;
            issues << "详细信息面板未初始化";
        }
        
        // 记录验证结果
        if (isValid) {
            qDebug() << "✅ 工程状态验证通过，详细信息功能就绪";
        } else {
            qDebug() << QString("⚠️ 工程状态验证发现问题：%1").arg(issues.join("; "));
            
            // 显示警告信息
            QMessageBox::warning(this, "工程状态警告", 
                QString("工程加载完成，但发现以下问题：\n%1\n\n详细信息功能可能无法正常工作。")
                .arg(issues.join("\n")));
        }
    }
    
    void setupTreeData() {
        m_treeWidget->clear();
        
        // 创建实验节点
        QTreeWidgetItem *experimentItem = new QTreeWidgetItem(m_treeWidget);
        experimentItem->setText(0, "实验");
        experimentItem->setText(1, "试验节点");
        experimentItem->setText(2, "正常");
        
        // 创建控制通道节点
        QTreeWidgetItem *controlChannelItem = new QTreeWidgetItem(experimentItem);
        controlChannelItem->setText(0, "控制通道");
        controlChannelItem->setText(1, "控制通道");
        controlChannelItem->setText(2, "正常");
        
        // 创建CH1节点
        QTreeWidgetItem *ch1Item = new QTreeWidgetItem(controlChannelItem);
        ch1Item->setText(0, "CH1");
        ch1Item->setText(1, "控制通道");
        ch1Item->setText(2, "正常");
        
        // 创建CH1的子节点
        QTreeWidgetItem *load1Item = new QTreeWidgetItem(ch1Item);
        load1Item->setText(0, "载荷1");
        load1Item->setText(1, "载荷传感器");
        load1Item->setText(2, "正常");
        
        QTreeWidgetItem *load2Item = new QTreeWidgetItem(ch1Item);
        load2Item->setText(0, "载荷2");
        load2Item->setText(1, "载荷传感器");
        load2Item->setText(2, "正常");
        
        // 展开所有节点
        m_treeWidget->expandAll();
        
        qDebug() << "✅ 树形控件数据初始化完成";
    }
    
    void updateStatus() {
        if (m_hasProject) {
            m_statusLabel->setText(QString("状态: ✅ 项目已就绪 - %1").arg(m_projectName));
            m_statusLabel->setStyleSheet("QLabel { font-size: 14pt; font-weight: bold; padding: 10px; color: #27ae60; }");
        } else {
            m_statusLabel->setText("状态: ⚠️ 没有项目，请新建项目或打开项目");
            m_statusLabel->setStyleSheet("QLabel { font-size: 14pt; font-weight: bold; padding: 10px; color: #e74c3c; }");
        }
    }
    
    QLabel *m_statusLabel;
    QProgressBar *m_progressBar;
    QTreeWidget *m_treeWidget;
    QWidget *m_detailPanel;
    bool m_hasProject;
    QString m_projectName;
};

int main(int argc, char *argv[]) {
    QApplication app(argc, argv);
    
    MockMainWindow window;
    window.show();
    
    return app.exec();
}

#include "test_project_state_fix.moc" 