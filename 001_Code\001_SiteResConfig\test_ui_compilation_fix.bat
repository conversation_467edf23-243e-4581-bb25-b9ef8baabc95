@echo off
echo ========================================
echo  测试UI控件编译错误修复
echo ========================================

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    echo 请确保Qt 5.14.2 MinGW版本已正确安装
    pause
    exit /b 1
)

g++ --version
if errorlevel 1 (
    echo 错误: MinGW编译器未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目（测试UI控件错误修复）...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo 请检查上面的错误信息。
    echo.
    echo 已修复的错误:
    echo ✅ setSensorDataManager 参数类型错误
    echo ✅ setActuatorDataManager 参数类型错误
    echo ✅ systemOverviewLabel 控件不存在错误
    echo ✅ refreshHardwareButton 控件不存在错误
    echo ✅ dataTableWidget 控件不存在错误
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  编译成功！UI控件错误已修复
    echo ========================================
    
    if exist "SiteResConfig.exe" (
        echo 可执行文件: SiteResConfig.exe
        echo.
        echo ✅ 修复的编译错误:
        echo - MainWindow_Qt_Simple.cpp:735 setSensorDataManager 参数类型错误
        echo - MainWindow_Qt_Simple.cpp:736 setActuatorDataManager 参数类型错误
        echo - MainWindow_Qt_Simple.cpp:1811 setSensorDataManager 参数类型错误
        echo - MainWindow_Qt_Simple.cpp:1812 setActuatorDataManager 参数类型错误
        echo - MainWindow_Qt_Simple.cpp:7822 systemOverviewLabel 控件不存在
        echo - MainWindow_Qt_Simple.cpp:7872 refreshHardwareButton 控件不存在
        echo - MainWindow_Qt_Simple.cpp:7881 dataTableWidget 控件不存在
        echo.
        echo 🔧 修复方案:
        echo - 使用 .get() 方法获取 unique_ptr 的原始指针
        echo - 使用实际存在的 statusLabel 替代 systemOverviewLabel
        echo - 注释掉不存在的UI控件，等UI完善后启用
        echo.
        echo 🎯 项目状态管理功能:
        echo - 软件启动时操作区禁用（硬件树、试验配置树）
        echo - 状态标签显示项目状态提示
        echo - 菜单项根据项目状态启用/禁用
        echo - 新建/打开项目后操作区启用
        echo.
        echo 启动程序验证功能...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
        echo 启动程序验证功能...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
        echo 启动程序验证功能...
        start release\SiteResConfig.exe
    ) else (
        echo 警告: 找不到可执行文件！
    )
)

echo.
echo 📖 修复详情:
echo.
echo 🔧 DataManager指针类型修复:
echo - unique_ptr 需要使用 .get() 方法获取原始指针
echo - setSensorDataManager(sensorDataManager_.get())
echo - setActuatorDataManager(actuatorDataManager_.get())
echo.
echo 🎨 UI控件适配:
echo - 使用实际存在的 statusLabel 显示项目状态
echo - 注释掉不存在的按钮和表格控件
echo - 保留菜单项的启用/禁用功能
echo.
echo 🚀 项目状态管理核心功能:
echo - 硬件树和试验配置树的启用/禁用
echo - 状态标签显示项目状态信息
echo - 菜单项根据项目状态动态调整
echo.
echo 💡 后续UI完善建议:
echo - 添加工具栏按钮（添加硬件、刷新等）
echo - 添加数据表格控件
echo - 添加试验控制按钮
echo - 完善系统概览区域
echo.
pause
