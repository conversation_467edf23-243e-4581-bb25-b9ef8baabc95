/**
 * @file ConfigManager.cpp
 * @brief 配置管理模块实现 - 基于现有Config::ConfigManager
 * @details 封装现有的Config::ConfigManager，提供统一的配置管理接口
 * <AUTHOR> Assistant
 * @date 2025-01-27
 * @version 3.4.0
 */

#include "ConfigManager.h"
#include <QSettings>
#include <QDebug>

ConfigManager::ConfigManager(QObject* parent)
    : QObject(parent), existingConfigManager_(nullptr) {
    // 从QSettings加载最近项目
    QSettings settings;
    recentProjects_ = settings.value("recentProjects").toStringList();
}

ConfigManager::~ConfigManager() {
    // 保存最近项目到QSettings
    QSettings settings;
    settings.setValue("recentProjects", recentProjects_);
}

void ConfigManager::setExistingConfigManager(Config::ConfigManager* configManager) {
    existingConfigManager_ = configManager;
}

QVariant ConfigManager::getValue(const QString& key, const QVariant& defaultValue) {
    if (config_.contains(key)) {
        return config_[key];
    }
    
    // 如果本地没有，尝试从QSettings读取
    QSettings settings;
    return settings.value(key, defaultValue);
}

void ConfigManager::setValue(const QString& key, const QVariant& value) {
    config_[key] = value;
    
    // 同时保存到QSettings
    QSettings settings;
    settings.setValue(key, value);
    
    emit configChanged(key, value);
}

void ConfigManager::addRecentProject(const QString& projectName, const QString& projectPath) {
    QString recentItem = QString("%1|%2").arg(projectName).arg(projectPath);
    
    // 移除已存在的相同项目
    recentProjects_.removeAll(recentItem);
    
    // 添加到列表开头
    recentProjects_.prepend(recentItem);
    
    // 限制最大数量为10个
    while (recentProjects_.size() > 10) {
        recentProjects_.removeLast();
    }
    
    // 保存到QSettings
    QSettings settings;
    settings.setValue("recentProjects", recentProjects_);
}

QStringList ConfigManager::getRecentProjects() const {
    return recentProjects_;
}

bool ConfigManager::loadConfig(const QString& configPath) {
    Q_UNUSED(configPath)
    
    // 从QSettings加载配置
    QSettings settings;
    QStringList keys = settings.allKeys();
    
    for (const QString& key : keys) {
        config_[key] = settings.value(key);
    }
    
    // 加载最近项目
    recentProjects_ = settings.value("recentProjects").toStringList();
    
    return true;
}

bool ConfigManager::saveConfig(const QString& configPath) {
    Q_UNUSED(configPath)
    
    // 保存到QSettings
    QSettings settings;
    
    for (auto it = config_.begin(); it != config_.end(); ++it) {
        settings.setValue(it.key(), it.value());
    }
    
    // 保存最近项目
    settings.setValue("recentProjects", recentProjects_);
    
    return true;
} 