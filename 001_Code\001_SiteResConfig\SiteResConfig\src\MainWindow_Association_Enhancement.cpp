/**
 * @file MainWindow_Association_Enhancement.cpp
 * @brief 控制通道关联信息增强功能实现
 * @details 包含阶段1基础优化和阶段2功能增强的所有实现
 * <AUTHOR> Team
 * @date 2024
 */

#include "MainWindow_Qt_Simple.h"
#include "ui_MainWindow.h"
#include <QMessageBox>
#include <QStringList>

// ===============================
// 🚀 阶段1：基础优化功能实现
// ===============================

/**
 * @brief 获取使用指定传感器的控制通道列表（删除前检查，增强版支持组名验证）
 * @param serialNumber 传感器序列号
 * @param groupName 传感器所属组名（用于精确匹配，防止误判）
 * @return 受影响的控制通道列表
 */
QStringList CMyMainWindow::GetControlChannelsUsingSensor(const QString& serialNumber, const QString& groupName) {
    QStringList affectedChannels;
    
    if (!ctrlChanDataManager_) {
        return affectedChannels;
    }
    
    auto groups = ctrlChanDataManager_->getAllControlChannelGroups();
    
    for (const auto& group : groups) {
        for (const auto& channel : group.channels) {
            QString channelName = QString::fromStdString(channel.channelName);
            QStringList associations;
            
            // 🆕 增强：使用精确匹配检查载荷1传感器关联
            if (IsExactSensorMatch(channel.load1Sensor, serialNumber, groupName)) {
                associations.append(u8"载荷1");
            }
            
            // 🆕 增强：使用精确匹配检查载荷2传感器关联
            if (IsExactSensorMatch(channel.load2Sensor, serialNumber, groupName)) {
                associations.append(u8"载荷2");
            }
            
            // 🆕 增强：使用精确匹配检查位置传感器关联
            if (IsExactSensorMatch(channel.positionSensor, serialNumber, groupName)) {
                associations.append(u8"位置");
            }
            
            // 如果有关联，添加到受影响的通道列表
            if (!associations.isEmpty()) {
                affectedChannels.append(QString(u8"  • %1 (%2)").arg(channelName).arg(associations.join(u8", ")));
            }
        }
    }
    
    return affectedChannels;
}

/**
 * @brief 获取使用指定传感器的控制通道列表（删除前检查，兼容版仅检查序列号）
 */
QStringList CMyMainWindow::GetControlChannelsUsingSensor(const QString& serialNumber) {
    QStringList affectedChannels;
    
    if (!ctrlChanDataManager_) {
        return affectedChannels;
    }
    
    auto groups = ctrlChanDataManager_->getAllControlChannelGroups();
    
    for (const auto& group : groups) {
        for (const auto& channel : group.channels) {
            QString channelName = QString::fromStdString(channel.channelName);
            QStringList associations;
            
            // 检查载荷1传感器关联
            if (IsExactSensorMatch(channel.load1Sensor, serialNumber)) {
                associations.append(u8"载荷1");
            }
            
            // 检查载荷2传感器关联
            if (IsExactSensorMatch(channel.load2Sensor, serialNumber)) {
                associations.append(u8"载荷2");
            }
            
            // 检查位置传感器关联
            if (IsExactSensorMatch(channel.positionSensor, serialNumber)) {
                associations.append(u8"位置");
            }
            
            // 如果有关联，添加到受影响的通道列表
            if (!associations.isEmpty()) {
                affectedChannels.append(QString(u8"  • %1 (%2)").arg(channelName).arg(associations.join(u8", ")));
            }
        }
    }
    
    return affectedChannels;
}

/**
 * @brief 获取使用指定作动器的控制通道列表（删除前检查）
 */
QStringList CMyMainWindow::GetControlChannelsUsingActuator(const QString& serialNumber) {
    QStringList affectedChannels;
    
    if (!ctrlChanDataManager_) {
        return affectedChannels;
    }
    
    auto groups = ctrlChanDataManager_->getAllControlChannelGroups();
    
    for (const auto& group : groups) {
        for (const auto& channel : group.channels) {
            QString channelName = QString::fromStdString(channel.channelName);
            
            // 检查控制作动器关联
            if (IsExactActuatorMatch(channel.controlActuator, serialNumber)) {
                affectedChannels.append(QString(u8"  • %1 (控制)").arg(channelName));
            }
        }
    }
    
    return affectedChannels;
}

/**
 * @brief 精确匹配并清除传感器关联信息
 * @param associationField 关联字段内容（格式："组名 - 序列号"）
 * @param serialNumber 要删除的传感器序列号
 * @param groupName 要删除的传感器所属组名（用于精确匹配，防止误删）
 * @return 是否匹配成功
 */
bool CMyMainWindow::IsExactSensorMatch(const std::string& associationField, const QString& serialNumber, const QString& groupName) {
    if (associationField.empty()) {
        return false;
    }
    
    QString associationInfo = QString::fromStdString(associationField);
    
    // 🆕 增强：检查完整匹配，同时验证组名和序列号
    QStringList parts = associationInfo.split(" - ");
    if (parts.size() == 2) {
        QString storedGroupName = parts[0].trimmed();
        QString storedSerialNumber = parts[1].trimmed();
        
        // 🔑 关键：同时匹配组名和序列号，防止误删其他组的同序列号设备
        if (storedGroupName == groupName.trimmed() && storedSerialNumber == serialNumber.trimmed()) {
            return true;
        }
    }
    
    // 兼容性检查：如果关联信息就是序列号本身（旧格式）
    if (associationInfo.trimmed() == serialNumber.trimmed()) {
        // ⚠️ 旧格式没有组名信息，只能通过序列号匹配（存在误删风险）
        return true;
    }
    
    return false;
}

/**
 * @brief 精确匹配并清除传感器关联信息（重载版本，兼容旧调用）
 * @param associationField 关联字段内容
 * @param serialNumber 要删除的传感器序列号
 * @return 是否匹配成功
 * @note 此版本只检查序列号，建议使用带组名参数的版本
 */
bool CMyMainWindow::IsExactSensorMatch(const std::string& associationField, const QString& serialNumber) {
    if (associationField.empty()) {
        return false;
    }
    
    QString associationInfo = QString::fromStdString(associationField);
    
    // 检查完整匹配：格式为 "组名 - 序列号"
    QStringList parts = associationInfo.split(" - ");
    if (parts.size() == 2 && parts[1].trimmed() == serialNumber.trimmed()) {
        return true;
    }
    
    // 兼容性检查：如果关联信息就是序列号本身
    if (associationInfo.trimmed() == serialNumber.trimmed()) {
        return true;
    }
    
    return false;
}

/**
 * @brief 精确匹配并清除作动器关联信息
 */
bool CMyMainWindow::IsExactActuatorMatch(const std::string& associationField, const QString& serialNumber) {
    if (associationField.empty()) {
        return false;
    }
    
    QString associationInfo = QString::fromStdString(associationField);
    
    // 检查完整匹配：格式为 "组名 - 序列号"
    QStringList parts = associationInfo.split(" - ");
    if (parts.size() == 2 && parts[1].trimmed() == serialNumber.trimmed()) {
        return true;
    }
    
    // 兼容性检查：如果关联信息就是序列号本身
    if (associationInfo.trimmed() == serialNumber.trimmed()) {
        return true;
    }
    
    return false;
}

/**
 * @brief 精确匹配并清除作动器关联信息（增强版，支持组名验证）
 */
bool CMyMainWindow::IsExactActuatorMatch(const std::string& associationField, const QString& serialNumber, const QString& groupName) {
    if (associationField.empty()) {
        return false;
    }
    
    QString associationInfo = QString::fromStdString(associationField);
    
    // 精确匹配：格式为 "组名组 - 序列号" 或 "组名 - 序列号"
    QString expectedAssociation1 = QString(u8"%1组 - %2").arg(groupName).arg(serialNumber);
    QString expectedAssociation2 = QString(u8"%1 - %2").arg(groupName).arg(serialNumber);
    
    if (associationInfo == expectedAssociation1 || associationInfo == expectedAssociation2) {
        return true;
    }
    
    // 检查完整匹配：格式为 "组名 - 序列号"
    QStringList parts = associationInfo.split(" - ");
    if (parts.size() == 2) {
        QString assocGroupName = parts[0].trimmed().replace(u8"组", ""); // 移除"组"字符进行比较
        QString assocSerialNumber = parts[1].trimmed();
        
        if (assocGroupName == groupName && assocSerialNumber == serialNumber) {
            return true;
        }
    }
    
    return false;
}

// ===============================
// 🚀 阶段2：功能增强实现
// ===============================

/**
 * @brief 批量删除传感器后更新控制通道关联信息
 */
void CMyMainWindow::UpdateControlChannelAssociationsAfterSensorsBatchDelete(const QStringList& serialNumbers) {
    if (!ctrlChanDataManager_ || !ui->testConfigTreeWidget || serialNumbers.isEmpty()) {
        return;
    }
    
    LogMessage("INFO", QString(u8"🔄 批量更新控制通道关联信息：传感器删除 - %1个").arg(serialNumbers.size()));
    
    // 获取所有控制通道组
    auto groups = ctrlChanDataManager_->getAllControlChannelGroups();
    bool hasUpdates = false;
    int clearedAssociations = 0;
    
    for (auto& group : groups) {
        for (auto& channel : group.channels) {
            bool channelUpdated = false;
            
            // 批量检查并清除载荷1传感器关联
            for (const QString& serialNumber : serialNumbers) {
                if (IsExactSensorMatch(channel.load1Sensor, serialNumber)) {
                    QString oldAssociation = QString::fromStdString(channel.load1Sensor);
                    channel.load1Sensor = "";
                    channelUpdated = true;
                    clearedAssociations++;
                    AddLogEntry("INFO", QString(u8"✅ 批量清除CH%1载荷1关联: %2").arg(QString::fromStdString(channel.channelName)).arg(oldAssociation));
                    break; // 每个字段只需清除一次
                }
            }
            
            // 批量检查并清除载荷2传感器关联
            for (const QString& serialNumber : serialNumbers) {
                if (IsExactSensorMatch(channel.load2Sensor, serialNumber)) {
                    QString oldAssociation = QString::fromStdString(channel.load2Sensor);
                    channel.load2Sensor = "";
                    channelUpdated = true;
                    clearedAssociations++;
                    AddLogEntry("INFO", QString(u8"✅ 批量清除CH%1载荷2关联: %2").arg(QString::fromStdString(channel.channelName)).arg(oldAssociation));
                    break; // 每个字段只需清除一次
                }
            }
            
            // 批量检查并清除位置传感器关联
            for (const QString& serialNumber : serialNumbers) {
                if (IsExactSensorMatch(channel.positionSensor, serialNumber)) {
                    QString oldAssociation = QString::fromStdString(channel.positionSensor);
                    channel.positionSensor = "";
                    channelUpdated = true;
                    clearedAssociations++;
                    AddLogEntry("INFO", QString(u8"✅ 批量清除CH%1位置关联: %2").arg(QString::fromStdString(channel.channelName)).arg(oldAssociation));
                    break; // 每个字段只需清除一次
                }
            }
            
            if (channelUpdated) {
                hasUpdates = true;
            }
        }
        
        // 如果有更新，保存到数据管理器
        if (hasUpdates) {
            ctrlChanDataManager_->updateControlChannelGroup(group);
        }
    }
    
    // 更新UI显示
    if (hasUpdates) {
        UpdateAllTreeWidgetTooltips();
        AddLogEntry("SUCCESS", QString(u8"✅ 批量控制通道关联信息清除完成：共清除 %1 个关联").arg(clearedAssociations));
    } else {
        AddLogEntry("INFO", QString(u8"ℹ️ 无需清除控制通道关联信息"));
    }
}

/**
 * @brief 批量删除作动器后更新控制通道关联信息
 */
void CMyMainWindow::UpdateControlChannelAssociationsAfterActuatorsBatchDelete(const QStringList& serialNumbers) {
    if (!ctrlChanDataManager_ || !ui->testConfigTreeWidget || serialNumbers.isEmpty()) {
        return;
    }
    
    AddLogEntry("INFO", QString(u8"🔄 批量更新控制通道关联信息：作动器删除 - %1个").arg(serialNumbers.size()));
    
    // 获取所有控制通道组
    auto groups = ctrlChanDataManager_->getAllControlChannelGroups();
    bool hasUpdates = false;
    int clearedAssociations = 0;
    
    for (auto& group : groups) {
        for (auto& channel : group.channels) {
            // 批量检查并清除控制作动器关联
            for (const QString& serialNumber : serialNumbers) {
                if (IsExactActuatorMatch(channel.controlActuator, serialNumber)) {
                    QString oldAssociation = QString::fromStdString(channel.controlActuator);
                    channel.controlActuator = "";
                    hasUpdates = true;
                    clearedAssociations++;
                    AddLogEntry("INFO", QString(u8"✅ 批量清除CH%1控制关联: %2").arg(QString::fromStdString(channel.channelName)).arg(oldAssociation));
                    break; // 每个字段只需清除一次
                }
            }
        }
        
        // 如果有更新，保存到数据管理器
        if (hasUpdates) {
            ctrlChanDataManager_->updateControlChannelGroup(group);
        }
    }
    
    // 更新UI显示
    if (hasUpdates) {
        UpdateAllTreeWidgetTooltips();
        AddLogEntry("SUCCESS", QString(u8"✅ 批量控制通道关联信息清除完成：共清除 %1 个关联").arg(clearedAssociations));
    } else {
        AddLogEntry("INFO", QString(u8"ℹ️ 无需清除控制通道关联信息"));
    }
}

/**
 * @brief 验证所有控制通道关联信息的有效性
 */
CMyMainWindow::AssociationValidationResult CMyMainWindow::ValidateAllControlChannelAssociations() {
    AssociationValidationResult result;
    
    if (!ctrlChanDataManager_ || !sensorDataManager_ || !actuatorViewModel1_2_) {
        return result;
    }
    
    AddLogEntry("INFO", QString(u8"🔍 开始验证控制通道关联信息的有效性"));
    
    // 获取所有有效的传感器和作动器序列号
    QStringList validSensorSerials;
    // 遍历所有传感器组来收集序列号
    auto sensorGroups = sensorDataManager_->getAllSensorGroups();
    for (const auto& group : sensorGroups) {
        QStringList groupSerials = sensorDataManager_->getAllSensorSerialNumbersInGroup(group.groupId);
        validSensorSerials.append(groupSerials);
    }
    QStringList validActuatorSerials;
    
    // 获取所有作动器序列号
    auto actuatorGroups = actuatorViewModel1_2_->getAllActuatorGroups();
    for (const auto& group : actuatorGroups) {
        for (const auto& actuator : group.actuators) {
            validActuatorSerials.append(actuator.params.sn);
        }
    }
    
    // 获取所有控制通道组
    auto channelGroups = ctrlChanDataManager_->getAllControlChannelGroups();
    
    for (const auto& group : channelGroups) {
        for (const auto& channel : group.channels) {
            result.totalChannels++;
            QString channelName = QString::fromStdString(channel.channelName);
            bool hasValidAssociation = false;
            
            // 验证载荷1传感器关联
            if (!channel.load1Sensor.empty()) {
                QString associationInfo = QString::fromStdString(channel.load1Sensor);
                QStringList parts = associationInfo.split(" - ");
                QString serialNumber = (parts.size() == 2) ? parts[1].trimmed() : associationInfo.trimmed();
                
                if (!validSensorSerials.contains(serialNumber)) {
                    result.invalidSensorAssociations.append(QString(u8"%1载荷1: %2").arg(channelName).arg(associationInfo));
                } else {
                    hasValidAssociation = true;
                }
            }
            
            // 验证载荷2传感器关联
            if (!channel.load2Sensor.empty()) {
                QString associationInfo = QString::fromStdString(channel.load2Sensor);
                QStringList parts = associationInfo.split(" - ");
                QString serialNumber = (parts.size() == 2) ? parts[1].trimmed() : associationInfo.trimmed();
                
                if (!validSensorSerials.contains(serialNumber)) {
                    result.invalidSensorAssociations.append(QString(u8"%1载荷2: %2").arg(channelName).arg(associationInfo));
                } else {
                    hasValidAssociation = true;
                }
            }
            
            // 验证位置传感器关联
            if (!channel.positionSensor.empty()) {
                QString associationInfo = QString::fromStdString(channel.positionSensor);
                QStringList parts = associationInfo.split(" - ");
                QString serialNumber = (parts.size() == 2) ? parts[1].trimmed() : associationInfo.trimmed();
                
                if (!validSensorSerials.contains(serialNumber)) {
                    result.invalidSensorAssociations.append(QString(u8"%1位置: %2").arg(channelName).arg(associationInfo));
                } else {
                    hasValidAssociation = true;
                }
            }
            
            // 验证控制作动器关联
            if (!channel.controlActuator.empty()) {
                QString associationInfo = QString::fromStdString(channel.controlActuator);
                QStringList parts = associationInfo.split(" - ");
                QString serialNumber = (parts.size() == 2) ? parts[1].trimmed() : associationInfo.trimmed();
                
                if (!validActuatorSerials.contains(serialNumber)) {
                    result.invalidActuatorAssociations.append(QString(u8"%1控制: %2").arg(channelName).arg(associationInfo));
                } else {
                    hasValidAssociation = true;
                }
            }
            
            // 检查是否为孤立通道（没有任何有效关联）
            if (!hasValidAssociation && (channel.load1Sensor.empty() && channel.load2Sensor.empty() && 
                                       channel.positionSensor.empty() && channel.controlActuator.empty())) {
                result.orphanedChannels.append(channelName);
            } else if (hasValidAssociation) {
                result.validAssociations++;
            }
        }
    }
    
    // 输出验证结果统计
    AddLogEntry("INFO", QString(u8"📊 验证结果统计："));
    AddLogEntry("INFO", QString(u8"  • 总通道数：%1").arg(result.totalChannels));
    AddLogEntry("INFO", QString(u8"  • 有效关联数：%1").arg(result.validAssociations));
    AddLogEntry("INFO", QString(u8"  • 无效传感器关联：%1").arg(result.invalidSensorAssociations.size()));
    AddLogEntry("INFO", QString(u8"  • 无效作动器关联：%1").arg(result.invalidActuatorAssociations.size()));
    AddLogEntry("INFO", QString(u8"  • 孤立通道数：%1").arg(result.orphanedChannels.size()));
    
    if (!result.invalidSensorAssociations.isEmpty()) {
        AddLogEntry("WARNING", QString(u8"⚠️ 发现无效传感器关联："));
        for (const QString& invalid : result.invalidSensorAssociations) {
            AddLogEntry("WARNING", QString(u8"    %1").arg(invalid));
        }
    }
    
    if (!result.invalidActuatorAssociations.isEmpty()) {
        AddLogEntry("WARNING", QString(u8"⚠️ 发现无效作动器关联："));
        for (const QString& invalid : result.invalidActuatorAssociations) {
            AddLogEntry("WARNING", QString(u8"    %1").arg(invalid));
        }
    }
    
    return result;
}

/**
 * @brief 修复无效的控制通道关联信息
 */
int CMyMainWindow::RepairInvalidControlChannelAssociations(const AssociationValidationResult& validationResult) {
    if (!ctrlChanDataManager_) {
        return 0;
    }
    
    int repairedCount = 0;
    
    if (validationResult.invalidSensorAssociations.isEmpty() && 
        validationResult.invalidActuatorAssociations.isEmpty()) {
        AddLogEntry("INFO", QString(u8"ℹ️ 无需修复关联信息，所有关联均有效"));
        return 0;
    }
    
    AddLogEntry("INFO", QString(u8"🔧 开始修复无效的控制通道关联信息"));
    
    // 获取所有控制通道组
    auto groups = ctrlChanDataManager_->getAllControlChannelGroups();
    bool hasUpdates = false;
    
    for (auto& group : groups) {
        for (auto& channel : group.channels) {
            QString channelName = QString::fromStdString(channel.channelName);
            
            // 修复无效的传感器关联
            for (const QString& invalidAssociation : validationResult.invalidSensorAssociations) {
                if (invalidAssociation.startsWith(channelName)) {
                    // 清除对应的无效关联
                    if (invalidAssociation.contains(u8"载荷1") && !channel.load1Sensor.empty()) {
                        channel.load1Sensor = "";
                        hasUpdates = true;
                        repairedCount++;
                        AddLogEntry("INFO", QString(u8"🔧 修复CH%1载荷1无效关联").arg(channelName));
                    } else if (invalidAssociation.contains(u8"载荷2") && !channel.load2Sensor.empty()) {
                        channel.load2Sensor = "";
                        hasUpdates = true;
                        repairedCount++;
                        AddLogEntry("INFO", QString(u8"🔧 修复CH%1载荷2无效关联").arg(channelName));
                    } else if (invalidAssociation.contains(u8"位置") && !channel.positionSensor.empty()) {
                        channel.positionSensor = "";
                        hasUpdates = true;
                        repairedCount++;
                        AddLogEntry("INFO", QString(u8"🔧 修复CH%1位置无效关联").arg(channelName));
                    }
                }
            }
            
            // 修复无效的作动器关联
            for (const QString& invalidAssociation : validationResult.invalidActuatorAssociations) {
                if (invalidAssociation.startsWith(channelName) && invalidAssociation.contains(u8"控制")) {
                    if (!channel.controlActuator.empty()) {
                        channel.controlActuator = "";
                        hasUpdates = true;
                        repairedCount++;
                        AddLogEntry("INFO", QString(u8"🔧 修复CH%1控制无效关联").arg(channelName));
                    }
                }
            }
        }
        
        // 如果有更新，保存到数据管理器
        if (hasUpdates) {
            ctrlChanDataManager_->updateControlChannelGroup(group);
        }
    }
    
    // 更新UI显示
    if (hasUpdates) {
        UpdateAllTreeWidgetTooltips();
        AddLogEntry("SUCCESS", QString(u8"✅ 控制通道关联信息修复完成：共修复 %1 个无效关联").arg(repairedCount));
    }
    
    return repairedCount;
} 




 