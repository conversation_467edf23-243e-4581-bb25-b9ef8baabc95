@echo off
echo ========================================
echo  组节点类型设置和无图标测试
echo ========================================

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o
if exist "ui_*.h" del ui_*.h

echo.
echo 生成UI头文件...
uic ui\MainWindow.ui -o ui_MainWindow.h
if errorlevel 1 (
    echo UI文件生成失败！
    pause
    exit /b 1
) else (
    echo ✅ UI头文件生成成功: ui_MainWindow.h
)

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo.
    echo 修改内容：
    echo ✅ 组节点类型设置
    echo ✅ 移除组节点图标
    echo ✅ 设备节点类型设置
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！组节点类型和无图标已实现
    echo ========================================
    
    if exist "SiteResConfig.exe" (
        echo 可执行文件: SiteResConfig.exe
        echo.
        echo ✅ 组节点类型设置和无图标功能已实现！
        echo.
        echo 🏷️ 节点类型体系:
        echo.
        echo 📁 根节点类型:
        echo ├─ 作动器 (类型: "作动器")
        echo ├─ 传感器 (类型: "传感器")
        echo └─ 硬件节点资源 (类型: "硬件节点资源")
        echo.
        echo 📂 组节点类型 (无图标):
        echo ├─ 作动器组 (类型: "作动器组")
        echo ├─ 传感器组 (类型: "传感器组")
        echo └─ 硬件节点组 (类型: "硬件节点组")
        echo.
        echo 🔧 设备节点类型:
        echo ├─ 作动器设备 (类型: "作动器设备")
        echo ├─ 传感器设备 (类型: "传感器设备")
        echo ├─ 硬件节点设备 (类型: "硬件节点设备")
        echo └─ 加载通道设备 (类型: "加载通道设备")
        echo.
        echo 🎨 视觉效果:
        echo - 组节点: 纯文本显示，无图标
        echo - 设备节点: 纯文本显示，无图标
        echo - 根节点: 纯文本显示，无图标
        echo - 统一简洁的视觉风格
        echo.
        echo 🔍 类型标识优势:
        echo - 精确的节点类型识别
        echo - 支持基于类型的功能扩展
        echo - 便于右键菜单逻辑判断
        echo - 支持拖拽操作类型验证
        echo.
        echo 📋 测试要点:
        echo 1. 创建作动器组，验证无图标显示
        echo 2. 创建传感器组，验证无图标显示
        echo 3. 创建硬件节点组，验证无图标显示
        echo 4. 验证快速创建的组也无图标
        echo 5. 验证组内设备节点类型正确
        echo.
        echo 🎯 类型验证方法:
        echo - 通过右键菜单验证类型识别
        echo - 通过日志查看节点创建信息
        echo - 通过工具提示查看设备信息
        echo - 通过树形结构验证层级关系
        echo.
        echo 启动程序测试组节点类型和无图标功能...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
        echo 启动程序...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
        echo 启动程序...
        start release\SiteResConfig.exe
    ) else (
        echo 警告: 找不到可执行文件！
    )
)

echo.
echo 📖 组节点类型和无图标测试指南:
echo.
echo 🎯 作动器组测试:
echo 1. 右键"作动器"节点 → "新建" → "作动器组"
echo 2. 输入组名，如"液压作动器组"
echo 3. 验证创建的组节点无图标显示
echo 4. 验证组节点类型为"作动器组"
echo.
echo 🎯 快速创建测试:
echo 1. 右键"作动器"节点 → "快速创建作动器组" → "100kN_作动器"
echo 2. 验证创建的组节点无图标显示
echo 3. 验证组内自动创建的设备节点类型为"作动器设备"
echo 4. 验证设备节点也无图标显示
echo.
echo 🎯 传感器组测试:
echo 1. 右键"传感器"节点 → "新建" → "传感器组"
echo 2. 验证创建的组节点无图标显示
echo 3. 验证组节点类型为"传感器组"
echo.
echo 🎯 硬件节点组测试:
echo 1. 右键"硬件节点资源"节点 → "新建" → "硬件节点组"
echo 2. 验证创建的组节点无图标显示
echo 3. 验证组节点类型为"硬件节点组"
echo.
echo 🔍 验证要点:
echo - 所有组节点都不显示图标
echo - 所有节点都有正确的类型标识
echo - 树形结构层次清晰
echo - 右键菜单基于类型正确显示
echo - 日志记录操作信息完整
echo.
echo 💡 设计理念:
echo - 简洁的视觉风格
echo - 统一的显示效果
echo - 清晰的类型体系
echo - 良好的用户体验
echo.
pause
