#pragma once

/**
 * @file HardwareConfigDialog.h
 * @brief 硬件配置对话框类定义
 * @details 使用Qt Designer设计的硬件配置参数输入对话框
 * <AUTHOR> Assistant
 * @date 2025-08-06
 * @version 1.0.0
 */

#include <QtWidgets/QDialog>
#include <QtCore/QString>

QT_BEGIN_NAMESPACE
QT_END_NAMESPACE

namespace Ui {
class HardwareConfigDialog;
}

namespace UI {

/**
 * @brief 硬件配置参数结构体
 * @details 存储硬件配置的所有参数
 */
struct HardwareConfigParams {
    int nodeCount;          // 节点数量
    int channelCount;       // 每节点通道数
    int actuatorCount;      // 作动器数量
    double maxForce;        // 最大力值 (kN)
    int sensorCount;        // 传感器数量
    
    HardwareConfigParams() 
        : nodeCount(2), channelCount(4), actuatorCount(4)
        , maxForce(100.0), sensorCount(8) {}
};

/**
 * @brief 硬件配置对话框类
 * @details 使用.ui文件设计的标准Qt对话框
 */
class HardwareConfigDialog : public QDialog {
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口
     */
    explicit HardwareConfigDialog(QWidget* parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    virtual ~HardwareConfigDialog();

    /**
     * @brief 获取硬件配置参数
     * @return 硬件配置参数结构体
     */
    HardwareConfigParams getHardwareConfigParams() const;

    /**
     * @brief 验证输入参数
     * @return 验证是否通过
     */
    bool validateInput();

private slots:
    /**
     * @brief 确定按钮点击前的验证
     */
    void onAcceptClicked();

private:
    Ui::HardwareConfigDialog* ui;

    /**
     * @brief 初始化界面
     */
    void initializeUI();

    /**
     * @brief 连接信号槽
     */
    void connectSignals();
};

} // namespace UI
