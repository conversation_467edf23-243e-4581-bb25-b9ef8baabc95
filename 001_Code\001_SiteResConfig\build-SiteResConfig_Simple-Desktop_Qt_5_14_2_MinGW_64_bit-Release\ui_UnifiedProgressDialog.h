/********************************************************************************
** Form generated from reading UI file 'UnifiedProgressDialog.ui'
**
** Created by: Qt User Interface Compiler version 5.14.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_UNIFIEDPROGRESSDIALOG_H
#define UI_UNIFIEDPROGRESSDIALOG_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QDialog>
#include <QtWidgets/QFrame>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QProgressBar>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QStackedWidget>
#include <QtWidgets/QTextEdit>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_UnifiedProgressDialog
{
public:
    QVBoxLayout *mainLayout;
    QLabel *titleLabel;
    QStackedWidget *contentStackedWidget;
    QWidget *progressPage;
    QVBoxLayout *progressLayout;
    QLabel *currentOperationLabel;
    QProgressBar *progressBar;
    QLabel *timeLabel;
    QLabel *warningHeaderLabel;
    QTextEdit *warningTextEdit;
    QWidget *completionPage;
    QVBoxLayout *completionLayout;
    QHBoxLayout *completionHeaderLayout;
    QLabel *completionIconLabel;
    QVBoxLayout *completionTitleLayout;
    QLabel *completionTitleLabel;
    QLabel *completionMessageLabel;
    QFrame *statsFrame;
    QVBoxLayout *statsLayout;
    QLabel *statsLabel;
    QLabel *finalTimeLabel;
    QSpacerItem *verticalSpacer;
    QPushButton *detailsButton;
    QTextEdit *detailsTextEdit;
    QWidget *errorPage;
    QVBoxLayout *errorLayout;
    QHBoxLayout *errorHeaderLayout;
    QLabel *errorIconLabel;
    QVBoxLayout *errorTitleLayout;
    QLabel *errorTitleLabel;
    QLabel *errorMessageLabel;
    QTextEdit *errorDetailsTextEdit;
    QHBoxLayout *buttonLayout;
    QSpacerItem *horizontalSpacer;
    QPushButton *continueButton;
    QPushButton *pauseButton;
    QPushButton *cancelButton;

    void setupUi(QDialog *UnifiedProgressDialog)
    {
        if (UnifiedProgressDialog->objectName().isEmpty())
            UnifiedProgressDialog->setObjectName(QString::fromUtf8("UnifiedProgressDialog"));
        UnifiedProgressDialog->resize(748, 657);
        UnifiedProgressDialog->setStyleSheet(QString::fromUtf8(""));
        UnifiedProgressDialog->setModal(true);
        mainLayout = new QVBoxLayout(UnifiedProgressDialog);
        mainLayout->setSpacing(15);
        mainLayout->setObjectName(QString::fromUtf8("mainLayout"));
        mainLayout->setContentsMargins(20, 20, 20, 20);
        titleLabel = new QLabel(UnifiedProgressDialog);
        titleLabel->setObjectName(QString::fromUtf8("titleLabel"));
        QSizePolicy sizePolicy(QSizePolicy::Preferred, QSizePolicy::Minimum);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(titleLabel->sizePolicy().hasHeightForWidth());
        titleLabel->setSizePolicy(sizePolicy);
        QFont font;
        font.setPointSize(16);
        font.setBold(true);
        font.setWeight(75);
        titleLabel->setFont(font);
        titleLabel->setStyleSheet(QString::fromUtf8("QLabel#titleLabel {\n"
"    color: #2c3e50;\n"
"    padding: 10px;\n"
"    background-color: #ecf0f1;\n"
"    border-radius: 5px;\n"
"}"));
        titleLabel->setAlignment(Qt::AlignCenter);

        mainLayout->addWidget(titleLabel);

        contentStackedWidget = new QStackedWidget(UnifiedProgressDialog);
        contentStackedWidget->setObjectName(QString::fromUtf8("contentStackedWidget"));
        sizePolicy.setHeightForWidth(contentStackedWidget->sizePolicy().hasHeightForWidth());
        contentStackedWidget->setSizePolicy(sizePolicy);
        contentStackedWidget->setMaximumSize(QSize(16777215, 9999999));
        contentStackedWidget->setStyleSheet(QString::fromUtf8("QStackedWidget {\n"
"    border: none;\n"
"    background: transparent;\n"
"}"));
        progressPage = new QWidget();
        progressPage->setObjectName(QString::fromUtf8("progressPage"));
        progressLayout = new QVBoxLayout(progressPage);
        progressLayout->setSpacing(12);
        progressLayout->setObjectName(QString::fromUtf8("progressLayout"));
        currentOperationLabel = new QLabel(progressPage);
        currentOperationLabel->setObjectName(QString::fromUtf8("currentOperationLabel"));
        sizePolicy.setHeightForWidth(currentOperationLabel->sizePolicy().hasHeightForWidth());
        currentOperationLabel->setSizePolicy(sizePolicy);
        QFont font1;
        font1.setPointSize(10);
        currentOperationLabel->setFont(font1);
        currentOperationLabel->setStyleSheet(QString::fromUtf8("QLabel#currentOperationLabel {\n"
"    color: #34495e;\n"
"    padding: 5px;\n"
"}"));

        progressLayout->addWidget(currentOperationLabel);

        progressBar = new QProgressBar(progressPage);
        progressBar->setObjectName(QString::fromUtf8("progressBar"));
        progressBar->setMinimumSize(QSize(0, 25));
        progressBar->setStyleSheet(QString::fromUtf8("QProgressBar#progressBar {\n"
"    border: 1px solid #bdc3c7;\n"
"    border-radius: 5px;\n"
"    text-align: center;\n"
"}\n"
"\n"
"QProgressBar#progressBar::chunk {\n"
"    background-color: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n"
"        stop:0 #3498db, stop:1 #2980b9);\n"
"    border-radius: 4px;\n"
"}"));
        progressBar->setValue(0);
        progressBar->setTextVisible(true);

        progressLayout->addWidget(progressBar);

        timeLabel = new QLabel(progressPage);
        timeLabel->setObjectName(QString::fromUtf8("timeLabel"));
        sizePolicy.setHeightForWidth(timeLabel->sizePolicy().hasHeightForWidth());
        timeLabel->setSizePolicy(sizePolicy);
        QFont font2;
        font2.setPointSize(9);
        timeLabel->setFont(font2);
        timeLabel->setStyleSheet(QString::fromUtf8("QLabel#timeLabel {\n"
"    color: #7f8c8d;\n"
"}"));
        timeLabel->setAlignment(Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter);

        progressLayout->addWidget(timeLabel);

        warningHeaderLabel = new QLabel(progressPage);
        warningHeaderLabel->setObjectName(QString::fromUtf8("warningHeaderLabel"));
        sizePolicy.setHeightForWidth(warningHeaderLabel->sizePolicy().hasHeightForWidth());
        warningHeaderLabel->setSizePolicy(sizePolicy);
        QFont font3;
        font3.setPointSize(10);
        font3.setBold(true);
        font3.setWeight(75);
        warningHeaderLabel->setFont(font3);

        progressLayout->addWidget(warningHeaderLabel);

        warningTextEdit = new QTextEdit(progressPage);
        warningTextEdit->setObjectName(QString::fromUtf8("warningTextEdit"));
        QSizePolicy sizePolicy1(QSizePolicy::Expanding, QSizePolicy::Expanding);
        sizePolicy1.setHorizontalStretch(0);
        sizePolicy1.setVerticalStretch(0);
        sizePolicy1.setHeightForWidth(warningTextEdit->sizePolicy().hasHeightForWidth());
        warningTextEdit->setSizePolicy(sizePolicy1);
        warningTextEdit->setMaximumSize(QSize(16777215, 9999999));
        warningTextEdit->setFont(font2);
        warningTextEdit->setStyleSheet(QString::fromUtf8("QTextEdit#warningTextEdit {\n"
"    border: 1px solid #bdc3c7;\n"
"    border-radius: 5px;\n"
"    background-color: white;\n"
"}"));
        warningTextEdit->setReadOnly(true);

        progressLayout->addWidget(warningTextEdit);

        contentStackedWidget->addWidget(progressPage);
        completionPage = new QWidget();
        completionPage->setObjectName(QString::fromUtf8("completionPage"));
        completionLayout = new QVBoxLayout(completionPage);
        completionLayout->setSpacing(16);
        completionLayout->setObjectName(QString::fromUtf8("completionLayout"));
        completionHeaderLayout = new QHBoxLayout();
        completionHeaderLayout->setSpacing(16);
        completionHeaderLayout->setObjectName(QString::fromUtf8("completionHeaderLayout"));
        completionIconLabel = new QLabel(completionPage);
        completionIconLabel->setObjectName(QString::fromUtf8("completionIconLabel"));
        completionIconLabel->setMinimumSize(QSize(48, 48));
        completionIconLabel->setMaximumSize(QSize(48, 48));
        QFont font4;
        font4.setPointSize(24);
        completionIconLabel->setFont(font4);
        completionIconLabel->setAlignment(Qt::AlignCenter);

        completionHeaderLayout->addWidget(completionIconLabel);

        completionTitleLayout = new QVBoxLayout();
        completionTitleLayout->setObjectName(QString::fromUtf8("completionTitleLayout"));
        completionTitleLabel = new QLabel(completionPage);
        completionTitleLabel->setObjectName(QString::fromUtf8("completionTitleLabel"));
        sizePolicy.setHeightForWidth(completionTitleLabel->sizePolicy().hasHeightForWidth());
        completionTitleLabel->setSizePolicy(sizePolicy);
        QFont font5;
        font5.setPointSize(14);
        font5.setBold(true);
        font5.setWeight(75);
        completionTitleLabel->setFont(font5);
        completionTitleLabel->setStyleSheet(QString::fromUtf8("color: #28a745;"));

        completionTitleLayout->addWidget(completionTitleLabel);

        completionMessageLabel = new QLabel(completionPage);
        completionMessageLabel->setObjectName(QString::fromUtf8("completionMessageLabel"));
        sizePolicy.setHeightForWidth(completionMessageLabel->sizePolicy().hasHeightForWidth());
        completionMessageLabel->setSizePolicy(sizePolicy);
        completionMessageLabel->setStyleSheet(QString::fromUtf8("color: #666666;"));
        completionMessageLabel->setWordWrap(true);

        completionTitleLayout->addWidget(completionMessageLabel);


        completionHeaderLayout->addLayout(completionTitleLayout);


        completionLayout->addLayout(completionHeaderLayout);

        statsFrame = new QFrame(completionPage);
        statsFrame->setObjectName(QString::fromUtf8("statsFrame"));
        QSizePolicy sizePolicy2(QSizePolicy::Preferred, QSizePolicy::Expanding);
        sizePolicy2.setHorizontalStretch(0);
        sizePolicy2.setVerticalStretch(0);
        sizePolicy2.setHeightForWidth(statsFrame->sizePolicy().hasHeightForWidth());
        statsFrame->setSizePolicy(sizePolicy2);
        statsFrame->setStyleSheet(QString::fromUtf8("QFrame#statsFrame {\n"
"    background-color: #ffffff;\n"
"    border: 1px solid #dee2e6;\n"
"    border-radius: 6px;\n"
"    padding: 12px;\n"
"}"));
        statsFrame->setFrameShape(QFrame::Box);
        statsLayout = new QVBoxLayout(statsFrame);
        statsLayout->setSpacing(8);
        statsLayout->setObjectName(QString::fromUtf8("statsLayout"));
        statsLabel = new QLabel(statsFrame);
        statsLabel->setObjectName(QString::fromUtf8("statsLabel"));
        statsLabel->setFont(font1);
        statsLabel->setWordWrap(true);

        statsLayout->addWidget(statsLabel);

        finalTimeLabel = new QLabel(statsFrame);
        finalTimeLabel->setObjectName(QString::fromUtf8("finalTimeLabel"));
        finalTimeLabel->setFont(font2);
        finalTimeLabel->setStyleSheet(QString::fromUtf8("color: #6c757d;"));

        statsLayout->addWidget(finalTimeLabel);

        verticalSpacer = new QSpacerItem(0, 0, QSizePolicy::Minimum, QSizePolicy::Expanding);

        statsLayout->addItem(verticalSpacer);


        completionLayout->addWidget(statsFrame);

        detailsButton = new QPushButton(completionPage);
        detailsButton->setObjectName(QString::fromUtf8("detailsButton"));
        detailsButton->setFont(font2);
        detailsButton->setStyleSheet(QString::fromUtf8("QPushButton#detailsButton {\n"
"    background-color: transparent;\n"
"    border: none;\n"
"    color: #007bff;\n"
"    text-align: left;\n"
"    padding: 6px;\n"
"}\n"
"\n"
"QPushButton#detailsButton:hover {\n"
"    color: #0056b3;\n"
"    text-decoration: underline;\n"
"}"));
        detailsButton->setCheckable(true);

        completionLayout->addWidget(detailsButton);

        detailsTextEdit = new QTextEdit(completionPage);
        detailsTextEdit->setObjectName(QString::fromUtf8("detailsTextEdit"));
        detailsTextEdit->setMaximumSize(QSize(16777215, 120));
        QFont font6;
        font6.setFamily(QString::fromUtf8("Consolas"));
        font6.setPointSize(9);
        detailsTextEdit->setFont(font6);
        detailsTextEdit->setVisible(false);
        detailsTextEdit->setStyleSheet(QString::fromUtf8("QTextEdit#detailsTextEdit {\n"
"    background-color: #f8f9fa;\n"
"    border: 1px solid #dee2e6;\n"
"    border-radius: 4px;\n"
"    padding: 8px;\n"
"}"));
        detailsTextEdit->setReadOnly(true);

        completionLayout->addWidget(detailsTextEdit);

        contentStackedWidget->addWidget(completionPage);
        errorPage = new QWidget();
        errorPage->setObjectName(QString::fromUtf8("errorPage"));
        errorLayout = new QVBoxLayout(errorPage);
        errorLayout->setSpacing(16);
        errorLayout->setObjectName(QString::fromUtf8("errorLayout"));
        errorHeaderLayout = new QHBoxLayout();
        errorHeaderLayout->setSpacing(16);
        errorHeaderLayout->setObjectName(QString::fromUtf8("errorHeaderLayout"));
        errorIconLabel = new QLabel(errorPage);
        errorIconLabel->setObjectName(QString::fromUtf8("errorIconLabel"));
        errorIconLabel->setMinimumSize(QSize(48, 48));
        errorIconLabel->setMaximumSize(QSize(48, 48));
        errorIconLabel->setFont(font4);
        errorIconLabel->setAlignment(Qt::AlignCenter);

        errorHeaderLayout->addWidget(errorIconLabel);

        errorTitleLayout = new QVBoxLayout();
        errorTitleLayout->setObjectName(QString::fromUtf8("errorTitleLayout"));
        errorTitleLabel = new QLabel(errorPage);
        errorTitleLabel->setObjectName(QString::fromUtf8("errorTitleLabel"));
        sizePolicy.setHeightForWidth(errorTitleLabel->sizePolicy().hasHeightForWidth());
        errorTitleLabel->setSizePolicy(sizePolicy);
        errorTitleLabel->setFont(font5);
        errorTitleLabel->setStyleSheet(QString::fromUtf8("color: #dc3545;"));

        errorTitleLayout->addWidget(errorTitleLabel);

        errorMessageLabel = new QLabel(errorPage);
        errorMessageLabel->setObjectName(QString::fromUtf8("errorMessageLabel"));
        sizePolicy.setHeightForWidth(errorMessageLabel->sizePolicy().hasHeightForWidth());
        errorMessageLabel->setSizePolicy(sizePolicy);
        errorMessageLabel->setStyleSheet(QString::fromUtf8("color: #666666;"));
        errorMessageLabel->setWordWrap(true);

        errorTitleLayout->addWidget(errorMessageLabel);


        errorHeaderLayout->addLayout(errorTitleLayout);


        errorLayout->addLayout(errorHeaderLayout);

        errorDetailsTextEdit = new QTextEdit(errorPage);
        errorDetailsTextEdit->setObjectName(QString::fromUtf8("errorDetailsTextEdit"));
        errorDetailsTextEdit->setMaximumSize(QSize(16777215, 16777215));
        errorDetailsTextEdit->setFont(font6);
        errorDetailsTextEdit->setStyleSheet(QString::fromUtf8("QTextEdit#errorDetailsTextEdit {\n"
"    background-color: #fff5f5;\n"
"    border: 1px solid #fed7d7;\n"
"    border-radius: 4px;\n"
"    padding: 8px;\n"
"    color: #c53030;\n"
"}"));
        errorDetailsTextEdit->setReadOnly(true);

        errorLayout->addWidget(errorDetailsTextEdit);

        contentStackedWidget->addWidget(errorPage);

        mainLayout->addWidget(contentStackedWidget);

        buttonLayout = new QHBoxLayout();
        buttonLayout->setObjectName(QString::fromUtf8("buttonLayout"));
        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        buttonLayout->addItem(horizontalSpacer);

        continueButton = new QPushButton(UnifiedProgressDialog);
        continueButton->setObjectName(QString::fromUtf8("continueButton"));
        continueButton->setEnabled(false);
        continueButton->setMinimumSize(QSize(70, 30));
        continueButton->setFont(font1);
        continueButton->setStyleSheet(QString::fromUtf8(""));

        buttonLayout->addWidget(continueButton);

        pauseButton = new QPushButton(UnifiedProgressDialog);
        pauseButton->setObjectName(QString::fromUtf8("pauseButton"));
        pauseButton->setMinimumSize(QSize(70, 30));
        pauseButton->setFont(font1);
        pauseButton->setStyleSheet(QString::fromUtf8(""));

        buttonLayout->addWidget(pauseButton);

        cancelButton = new QPushButton(UnifiedProgressDialog);
        cancelButton->setObjectName(QString::fromUtf8("cancelButton"));
        cancelButton->setMinimumSize(QSize(70, 30));
        cancelButton->setFont(font1);
        cancelButton->setStyleSheet(QString::fromUtf8(""));

        buttonLayout->addWidget(cancelButton);


        mainLayout->addLayout(buttonLayout);


        retranslateUi(UnifiedProgressDialog);

        contentStackedWidget->setCurrentIndex(1);


        QMetaObject::connectSlotsByName(UnifiedProgressDialog);
    } // setupUi

    void retranslateUi(QDialog *UnifiedProgressDialog)
    {
        UnifiedProgressDialog->setWindowTitle(QCoreApplication::translate("UnifiedProgressDialog", "\346\223\215\344\275\234\350\277\233\345\272\246", nullptr));
        titleLabel->setText(QCoreApplication::translate("UnifiedProgressDialog", "\360\237\223\212 \346\225\260\346\215\256\345\244\204\347\220\206", nullptr));
        currentOperationLabel->setText(QCoreApplication::translate("UnifiedProgressDialog", "\345\207\206\345\244\207\345\274\200\345\247\213\346\223\215\344\275\234...", nullptr));
        timeLabel->setText(QCoreApplication::translate("UnifiedProgressDialog", "\345\267\262\347\224\250\346\227\266\351\227\264: 00:00:00", nullptr));
        warningHeaderLabel->setText(QCoreApplication::translate("UnifiedProgressDialog", "\342\232\240\357\270\217 \350\255\246\345\221\212\344\277\241\346\201\257:", nullptr));
        warningTextEdit->setHtml(QCoreApplication::translate("UnifiedProgressDialog", "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n"
"<html><head><meta name=\"qrichtext\" content=\"1\" /><style type=\"text/css\">\n"
"p, li { white-space: pre-wrap; }\n"
"</style></head><body style=\" font-family:'SimSun'; font-size:9pt; font-weight:400; font-style:normal;\">\n"
"<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><span style=\" color:#666666;\">\346\232\202\346\227\240\350\255\246\345\221\212\344\277\241\346\201\257</span></p></body></html>", nullptr));
        completionIconLabel->setText(QCoreApplication::translate("UnifiedProgressDialog", "\342\234\205", nullptr));
        completionTitleLabel->setText(QCoreApplication::translate("UnifiedProgressDialog", "\345\257\274\345\205\245\346\210\220\345\212\237\357\274\201", nullptr));
        completionMessageLabel->setText(QCoreApplication::translate("UnifiedProgressDialog", "Excel\345\267\245\347\250\213\346\226\207\344\273\266\345\267\262\346\210\220\345\212\237\345\257\274\345\205\245\345\210\260\347\263\273\347\273\237\344\270\255\343\200\202", nullptr));
        statsLabel->setText(QCoreApplication::translate("UnifiedProgressDialog", "\345\257\274\345\205\245\347\273\237\350\256\241\344\277\241\346\201\257", nullptr));
        finalTimeLabel->setText(QCoreApplication::translate("UnifiedProgressDialog", "\347\224\250\346\227\266: 00:00:00", nullptr));
        detailsButton->setText(QCoreApplication::translate("UnifiedProgressDialog", "\350\257\246\347\273\206\344\277\241\346\201\257", nullptr));
        errorIconLabel->setText(QCoreApplication::translate("UnifiedProgressDialog", "\342\235\214", nullptr));
        errorTitleLabel->setText(QCoreApplication::translate("UnifiedProgressDialog", "\346\223\215\344\275\234\345\244\261\350\264\245", nullptr));
        errorMessageLabel->setText(QCoreApplication::translate("UnifiedProgressDialog", "\346\223\215\344\275\234\350\277\207\347\250\213\344\270\255\345\217\221\347\224\237\344\272\206\351\224\231\350\257\257\357\274\214\350\257\267\346\237\245\347\234\213\350\257\246\347\273\206\344\277\241\346\201\257\343\200\202", nullptr));
        errorDetailsTextEdit->setHtml(QCoreApplication::translate("UnifiedProgressDialog", "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n"
"<html><head><meta name=\"qrichtext\" content=\"1\" /><style type=\"text/css\">\n"
"p, li { white-space: pre-wrap; }\n"
"</style></head><body style=\" font-family:'Consolas'; font-size:9pt; font-weight:400; font-style:normal;\">\n"
"<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\">\351\224\231\350\257\257\350\257\246\347\273\206\344\277\241\346\201\257\345\260\206\345\234\250\346\255\244\346\230\276\347\244\272...</p></body></html>", nullptr));
        continueButton->setText(QCoreApplication::translate("UnifiedProgressDialog", "\347\273\247\347\273\255", nullptr));
        pauseButton->setText(QCoreApplication::translate("UnifiedProgressDialog", "\346\232\202\345\201\234", nullptr));
        cancelButton->setText(QCoreApplication::translate("UnifiedProgressDialog", "\345\217\226\346\266\210", nullptr));
    } // retranslateUi

};

namespace Ui {
    class UnifiedProgressDialog: public Ui_UnifiedProgressDialog {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_UNIFIEDPROGRESSDIALOG_H
