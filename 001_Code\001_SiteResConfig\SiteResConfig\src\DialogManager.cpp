#include "DialogManager.h"
#include "SensorDialog_1_2.h"
#include "ActuatorDialog_1_2.h"

#include <QDebug>

DialogManager::DialogManager(QObject* parent)
    : QObject(parent)
{
    qDebug() << "DialogManager: 初始化对话框管理器 (基于现有对话框组件)";
}

DialogManager::~DialogManager()
{
    closeAllDialogs();
    qDebug() << "DialogManager: 对话框管理器已销毁";
}

bool DialogManager::showSensorDialog(const QString& serialNumber)
{
    const QString dialogType = "SensorDialog";
    
    try {
        // 检查是否已有对话框打开
        if (isDialogOpen(dialogType)) {
            qWarning() << "DialogManager: 传感器对话框已经打开";
            return false;
        }

        // 创建现有SensorDialog实例
        auto dialog = new UI::SensorDialog_1_2(QString("DefaultGroup"), QString("1"), nullptr);
        
        // 如果提供了序列号，设置对话框数据
        if (!serialNumber.isEmpty()) {
            // 这里可以根据序列号加载传感器数据
            // dialog->loadSensorData(serialNumber);
        }

        // 连接信号槽
        connect(dialog, &QDialog::accepted, this, &DialogManager::onDialogAccepted);
        connect(dialog, &QDialog::rejected, this, &DialogManager::onDialogRejected);

        // 注册对话框
        registerDialog(dialogType, dialog);

        // 显示对话框
        int result = dialog->exec();
        
        // 保存结果数据
        if (result == QDialog::Accepted) {
            lastSensorParams_ = dialog->getSensorParams();
        }

        // 清理对话框
        unregisterDialog(dialogType);
        dialog->deleteLater();

        return result == QDialog::Accepted;
    }
    catch (const std::exception& e) {
        qCritical() << "DialogManager: 显示传感器对话框时发生异常:" << e.what();
        return false;
    }
}

bool DialogManager::showSensorDialog(const UI::SensorParams_1_2& params)
{
    const QString dialogType = "SensorDialog";
    
    try {
        // 检查是否已有对话框打开
        if (isDialogOpen(dialogType)) {
            qWarning() << "DialogManager: 传感器对话框已经打开";
            return false;
        }

        // 创建现有SensorDialog实例
        auto dialog = new UI::SensorDialog_1_2(QString("DefaultGroup"), QString("1"), nullptr);
        
        // 设置传感器参数
        dialog->setSensorParams(params);

        // 连接信号槽
        connect(dialog, &QDialog::accepted, this, &DialogManager::onDialogAccepted);
        connect(dialog, &QDialog::rejected, this, &DialogManager::onDialogRejected);

        // 注册对话框
        registerDialog(dialogType, dialog);

        // 显示对话框
        int result = dialog->exec();
        
        // 保存结果数据
        if (result == QDialog::Accepted) {
            lastSensorParams_ = dialog->getSensorParams();
        }

        // 清理对话框
        unregisterDialog(dialogType);
        dialog->deleteLater();

        return result == QDialog::Accepted;
    }
    catch (const std::exception& e) {
        qCritical() << "DialogManager: 显示传感器对话框时发生异常:" << e.what();
        return false;
    }
}

UI::SensorParams_1_2 DialogManager::getSensorParams() const
{
    return lastSensorParams_;
}

bool DialogManager::showActuatorDialog(const QString& serialNumber)
{
    const QString dialogType = "ActuatorDialog";
    
    try {
        // 检查是否已有对话框打开
        if (isDialogOpen(dialogType)) {
            qWarning() << "DialogManager: 作动器对话框已经打开";
            return false;
        }

        // 创建现有ActuatorDialog实例
        auto dialog = new UI::ActuatorDialog_1_2(QString("DefaultGroup"), QString("1"), nullptr);
        
        // 如果提供了序列号，设置对话框数据
        if (!serialNumber.isEmpty()) {
            // 这里可以根据序列号加载作动器数据
            // dialog->loadActuatorData(serialNumber);
        }

        // 连接信号槽
        connect(dialog, &QDialog::accepted, this, &DialogManager::onDialogAccepted);
        connect(dialog, &QDialog::rejected, this, &DialogManager::onDialogRejected);

        // 注册对话框
        registerDialog(dialogType, dialog);

        // 显示对话框
        int result = dialog->exec();
        
        // 保存结果数据
        if (result == QDialog::Accepted) {
            lastActuatorParams_ = dialog->getActuatorParams();
        }

        // 清理对话框
        unregisterDialog(dialogType);
        dialog->deleteLater();

        return result == QDialog::Accepted;
    }
    catch (const std::exception& e) {
        qCritical() << "DialogManager: 显示作动器对话框时发生异常:" << e.what();
        return false;
    }
}

bool DialogManager::showActuatorDialog(const UI::ActuatorParams_1_2& params)
{
    const QString dialogType = "ActuatorDialog";
    
    try {
        // 检查是否已有对话框打开
        if (isDialogOpen(dialogType)) {
            qWarning() << "DialogManager: 作动器对话框已经打开";
            return false;
        }

        // 创建现有ActuatorDialog实例
        auto dialog = new UI::ActuatorDialog_1_2(QString("DefaultGroup"), QString("1"), nullptr);
        
        // 设置作动器参数
        dialog->setActuatorParams(params);

        // 连接信号槽
        connect(dialog, &QDialog::accepted, this, &DialogManager::onDialogAccepted);
        connect(dialog, &QDialog::rejected, this, &DialogManager::onDialogRejected);

        // 注册对话框
        registerDialog(dialogType, dialog);

        // 显示对话框
        int result = dialog->exec();
        
        // 保存结果数据
        if (result == QDialog::Accepted) {
            lastActuatorParams_ = dialog->getActuatorParams();
        }

        // 清理对话框
        unregisterDialog(dialogType);
        dialog->deleteLater();

        return result == QDialog::Accepted;
    }
    catch (const std::exception& e) {
        qCritical() << "DialogManager: 显示作动器对话框时发生异常:" << e.what();
        return false;
    }
}

UI::ActuatorParams_1_2 DialogManager::getActuatorParams() const
{
    return lastActuatorParams_;
}

bool DialogManager::isDialogOpen(const QString& dialogType) const
{
    return openDialogs_.contains(dialogType);
}

void DialogManager::closeAllDialogs()
{
    for (auto dialog : openDialogs_.values()) {
        if (dialog) {
            dialog->close();
            dialog->deleteLater();
        }
    }
    openDialogs_.clear();
}

void DialogManager::onDialogAccepted()
{
    QDialog* dialog = qobject_cast<QDialog*>(QObject::sender());
    if (!dialog) return;

    QString dialogType;
    QString identifier;

    // 识别对话框类型
    if (dynamic_cast<UI::SensorDialog_1_2*>(dialog)) {
        dialogType = "SensorDialog";
        auto sensorDialog = qobject_cast<UI::SensorDialog_1_2*>(dialog);
        // identifier = sensorDialog->getSerialNumber(); // 如果有这个方法
    }
    else if (dynamic_cast<UI::ActuatorDialog_1_2*>(dialog)) {
        dialogType = "ActuatorDialog";
        auto actuatorDialog = qobject_cast<UI::ActuatorDialog_1_2*>(dialog);
        // identifier = actuatorDialog->getSerialNumber(); // 如果有这个方法
    }

    emit dialogAccepted(dialogType, identifier);
}

void DialogManager::onDialogRejected()
{
    QDialog* dialog = qobject_cast<QDialog*>(QObject::sender());
    if (!dialog) return;

    QString dialogType;
    QString identifier;

    // 识别对话框类型
    if (dynamic_cast<UI::SensorDialog_1_2*>(dialog)) {
        dialogType = "SensorDialog";
    }
    else if (dynamic_cast<UI::ActuatorDialog_1_2*>(dialog)) {
        dialogType = "ActuatorDialog";
    }

    emit dialogRejected(dialogType, identifier);
}

void DialogManager::registerDialog(const QString& type, QDialog* dialog)
{
    openDialogs_[type] = dialog;
    qDebug() << "DialogManager: 注册对话框类型:" << type;
}

void DialogManager::unregisterDialog(const QString& type)
{
    openDialogs_.remove(type);
    qDebug() << "DialogManager: 注销对话框类型:" << type;
} 
