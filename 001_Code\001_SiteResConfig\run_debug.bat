@echo off
echo Starting debug version...

echo Current directory: %CD%
echo.

if exist "build-SiteResConfig_Simple-Desktop_Qt_5_14_2_MinGW_32_bit-Debug\debug\SiteResConfig.exe" (
    echo Found executable!
    echo Starting application with debug output...
    echo.
    echo Watch for debug messages starting with "=== dragMoveEvent START ==="
    echo.
    
    start /wait "SiteResConfig Debug" "build-SiteResConfig_Simple-Desktop_Qt_5_14_2_MinGW_32_bit-Debug\debug\SiteResConfig.exe"
    
) else (
    echo File not found at expected location.
    echo.
    echo Listing current directory contents:
    dir /b
    echo.
    echo Looking for build directories:
    dir /b build-*
)

pause
