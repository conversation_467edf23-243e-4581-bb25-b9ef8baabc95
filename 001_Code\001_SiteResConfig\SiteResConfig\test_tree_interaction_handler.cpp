/**
 * @file test_tree_interaction_handler.cpp
 * @brief 测试TreeInteractionHandler是否正常工作
 * @details 验证当点击控制通道节点时，TreeInteractionHandler是否正确响应
 * <AUTHOR> Assistant
 * @date 2025-01-27
 * @version 1.0.0
 */

#include <QApplication>
#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QMessageBox>
#include <QDebug>
#include <QTreeWidget>
#include <QTreeWidgetItem>
#include <QDockWidget>
#include <QGroupBox>
#include <QLabel>

#include "TreeInteractionHandler.h"
#include "DetailInfoPanel.h"

// 测试用的主窗口类
class TestTreeInteractionHandlerWindow : public QMainWindow
{
    Q_OBJECT
    
public:
    TestTreeInteractionHandlerWindow(QWidget *parent = nullptr) : QMainWindow(parent)
    {
        setupUI();
        setupTestData();
        setupConnections();
    }
    
private slots:
    void onTestControlChannelClicked()
    {
        testControlChannelRoot();
    }
    
    void onTestCH1Clicked()
    {
        testControlChannel("CH1");
    }
    
    void onTestCH2Clicked()
    {
        testControlChannel("CH2");
    }
    
    void onClearClicked()
    {
        m_detailInfoPanel->clearInfo();
        QMessageBox::information(this, "测试", "已清空详细信息");
    }
    
    void onTreeItemClicked(QTreeWidgetItem* item, int column)
    {
        qDebug() << "主窗口接收到树形控件点击事件:" << item->text(0);
    }
    
private:
    void setupUI()
    {
        setWindowTitle("TreeInteractionHandler测试程序");
        setMinimumSize(1400, 900);
        resize(1600, 1000);
        
        // 创建中央窗口
        QWidget* centralWidget = new QWidget(this);
        setCentralWidget(centralWidget);
        
        QVBoxLayout* centralLayout = new QVBoxLayout(centralWidget);
        
        // 创建说明标签
        QLabel* infoLabel = new QLabel("🎯 测试说明：点击树形控件中的不同节点，查看TreeInteractionHandler的响应", this);
        infoLabel->setStyleSheet("QLabel { font-size: 12pt; color: #2c3e50; padding: 10px; background-color: #ecf0f1; border-radius: 5px; }");
        centralLayout->addWidget(infoLabel);
        
        // 创建测试按钮
        QHBoxLayout* buttonLayout = new QHBoxLayout();
        
        QPushButton* testControlChannelButton = new QPushButton("测试控制通道根节点", this);
        QPushButton* testCH1Button = new QPushButton("测试CH1", this);
        QPushButton* testCH2Button = new QPushButton("测试CH2", this);
        QPushButton* clearButton = new QPushButton("清空信息", this);
        
        buttonLayout->addWidget(testControlChannelButton);
        buttonLayout->addWidget(testCH1Button);
        buttonLayout->addWidget(testCH2Button);
        buttonLayout->addWidget(clearButton);
        buttonLayout->addStretch();
        
        centralLayout->addLayout(buttonLayout);
        
        // 连接按钮信号
        connect(testControlChannelButton, &QPushButton::clicked, this, &TestTreeInteractionHandlerWindow::onTestControlChannelClicked);
        connect(testCH1Button, &QPushButton::clicked, this, &TestTreeInteractionHandlerWindow::onTestCH1Clicked);
        connect(testCH2Button, &QPushButton::clicked, this, &TestTreeInteractionHandlerWindow::onTestCH2Clicked);
        connect(clearButton, &QPushButton::clicked, this, &TestTreeInteractionHandlerWindow::onClearClicked);
        
        // 创建详细信息面板
        m_detailInfoPanel = new DetailInfoPanel(this);
        
        // 创建停靠窗口
        QDockWidget* detailDock = new QDockWidget("详细信息面板", this);
        detailDock->setWidget(m_detailInfoPanel);
        detailDock->setAllowedAreas(Qt::RightDockWidgetArea | Qt::LeftDockWidgetArea);
        addDockWidget(Qt::RightDockWidgetArea, detailDock);
        
        // 创建模拟树形控件
        m_testTree = new QTreeWidget(this);
        m_testTree->setHeaderLabels(QStringList() << "节点名称" << "类型" << "状态");
        m_testTree->setAlternatingRowColors(true);
        m_testTree->setContextMenuPolicy(Qt::CustomContextMenu);
        
        // 连接树形控件的点击事件
        connect(m_testTree, &QTreeWidget::itemClicked, this, &TestTreeInteractionHandlerWindow::onTreeItemClicked);
        
        // 创建停靠窗口
        QDockWidget* treeDock = new QDockWidget("测试树形控件", this);
        treeDock->setWidget(m_testTree);
        treeDock->setAllowedAreas(Qt::LeftDockWidgetArea | Qt::RightDockWidgetArea);
        addDockWidget(Qt::LeftDockWidgetArea, treeDock);
        
        // 创建TreeInteractionHandler
        m_treeInteractionHandler = new TreeInteractionHandler(m_testTree, nullptr, this, this);
        
        if (m_treeInteractionHandler) {
            qDebug() << "TreeInteractionHandler创建成功";
        } else {
            qDebug() << "TreeInteractionHandler创建失败";
        }
    }
    
    void setupTestData()
    {
        // 创建控制通道根节点
        QTreeWidgetItem* controlChannelRoot = new QTreeWidgetItem(m_testTree);
        controlChannelRoot->setText(0, "控制通道");
        controlChannelRoot->setText(1, "控制通道组");
        controlChannelRoot->setText(2, "在线");
        
        // 创建CH1通道
        QTreeWidgetItem* ch1Item = new QTreeWidgetItem(controlChannelRoot);
        ch1Item->setText(0, "CH1");
        ch1Item->setText(1, "控制通道");
        ch1Item->setText(2, "在线");
        
        // 创建CH1的子节点
        QTreeWidgetItem* load1Item = new QTreeWidgetItem(ch1Item);
        load1Item->setText(0, "载荷1");
        load1Item->setText(1, "载荷传感器");
        load1Item->setText(2, "已配置");
        
        QTreeWidgetItem* load2Item = new QTreeWidgetItem(ch1Item);
        load2Item->setText(0, "载荷2");
        load2Item->setText(1, "载荷传感器");
        load2Item->setText(2, "已配置");
        
        QTreeWidgetItem* positionItem = new QTreeWidgetItem(ch1Item);
        positionItem->setText(0, "位置");
        positionItem->setText(1, "位置传感器");
        positionItem->setText(2, "已配置");
        
        QTreeWidgetItem* controlItem = new QTreeWidgetItem(ch1Item);
        controlItem->setText(0, "控制");
        controlItem->setText(1, "控制作动器");
        controlItem->setText(2, "已配置");
        
        // 创建CH2通道
        QTreeWidgetItem* ch2Item = new QTreeWidgetItem(controlChannelRoot);
        ch2Item->setText(0, "CH2");
        ch2Item->setText(1, "控制通道");
        ch2Item->setText(2, "在线");
        
        // 创建CH2的子节点
        QTreeWidgetItem* load1Item2 = new QTreeWidgetItem(ch2Item);
        load1Item2->setText(0, "载荷1");
        load1Item2->setText(1, "载荷传感器");
        load1Item2->setText(2, "已配置");
        
        QTreeWidgetItem* load2Item2 = new QTreeWidgetItem(ch2Item);
        load2Item2->setText(0, "载荷2");
        load2Item2->setText(1, "载荷传感器");
        load2Item2->setText(2, "已配置");
        
        QTreeWidgetItem* positionItem2 = new QTreeWidgetItem(ch2Item);
        positionItem2->setText(0, "位置");
        positionItem2->setText(1, "位置传感器");
        positionItem2->setText(2, "已配置");
        
        QTreeWidgetItem* controlItem2 = new QTreeWidgetItem(ch2Item);
        controlItem2->setText(0, "控制");
        controlItem2->setText(1, "控制作动器");
        controlItem2->setText(2, "已配置");
        
        // 展开所有节点
        m_testTree->expandAll();
        
        qDebug() << "测试数据设置完成";
    }
    
    void setupConnections()
    {
        // 连接详细信息面板的信号
        connect(m_detailInfoPanel, &DetailInfoPanel::editConfigRequested,
                this, &TestTreeInteractionHandlerWindow::onEditConfigRequested);
        connect(m_detailInfoPanel, &DetailInfoPanel::viewHistoryRequested,
                this, &TestTreeInteractionHandlerWindow::onViewHistoryRequested);
        connect(m_detailInfoPanel, &DetailInfoPanel::exportInfoRequested,
                this, &TestTreeInteractionHandlerWindow::onExportInfoRequested);
    }
    
    void testControlChannelRoot()
    {
        qDebug() << "测试控制通道根节点";
        // 模拟点击控制通道根节点
        QTreeWidgetItem* rootItem = m_testTree->topLevelItem(0);
        if (rootItem) {
            m_testTree->setCurrentItem(rootItem);
            m_testTree->itemClicked(rootItem, 0);
        }
    }
    
    void testControlChannel(const QString& channelName)
    {
        qDebug() << "测试控制通道:" << channelName;
        // 查找并点击指定的通道节点
        QTreeWidgetItem* rootItem = m_testTree->topLevelItem(0);
        if (rootItem) {
            for (int i = 0; i < rootItem->childCount(); ++i) {
                QTreeWidgetItem* child = rootItem->child(i);
                if (child->text(0) == channelName) {
                    m_testTree->setCurrentItem(child);
                    m_testTree->itemClicked(child, 0);
                    break;
                }
            }
        }
    }
    
    void onEditConfigRequested(const NodeInfo& nodeInfo)
    {
        QMessageBox::information(this, "编辑配置", 
            QString("请求编辑节点配置: %1").arg(nodeInfo.nodeName));
    }
    
    void onViewHistoryRequested(const NodeInfo& nodeInfo)
    {
        QMessageBox::information(this, "查看历史", 
            QString("请求查看节点历史: %1").arg(nodeInfo.nodeName));
    }
    
    void onExportInfoRequested(const NodeInfo& nodeInfo)
    {
        QMessageBox::information(this, "导出信息", 
            QString("请求导出节点信息: %1").arg(nodeInfo.nodeName));
    }
    
private:
    DetailInfoPanel* m_detailInfoPanel;
    QTreeWidget* m_testTree;
    TreeInteractionHandler* m_treeInteractionHandler;
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    TestTreeInteractionHandlerWindow window;
    window.show();
    
    qDebug() << "TreeInteractionHandler测试程序已启动";
    qDebug() << "功能说明:";
    qDebug() << "1. 点击树形控件中的'控制通道'节点，查看TreeInteractionHandler的响应";
    qDebug() << "2. 点击树形控件中的'CH1'或'CH2'节点，查看TreeInteractionHandler的响应";
    qDebug() << "3. 使用测试按钮快速测试不同场景";
    qDebug() << "4. 在详细信息面板中查看显示的信息";
    qDebug() << "5. 查看控制台输出，了解TreeInteractionHandler的工作状态";
    
    return app.exec();
}

#include "test_tree_interaction_handler.moc" 