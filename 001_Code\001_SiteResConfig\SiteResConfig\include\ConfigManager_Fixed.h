#pragma once

/**
 * @file ConfigManager_Fixed.h
 * @brief Configuration Manager (Fixed Encoding)
 * @details Responsible for system configuration and project file read/write management
 * <AUTHOR> Assistant
 * @date 2025-08-05
 * @version 1.0.0
 */

#include "DataModels_Fixed.h"

namespace Config {

/**
 * @brief System configuration structure
 * @details Store system-level configuration information
 */
struct SystemConfig : public DataModels::IDataModel {
    // Interface configuration
    StringType language;           // Interface language
    StringType theme;              // Interface theme
    bool autoSave;                 // Auto save
    int autoSaveInterval;          // Auto save interval (seconds)
    
    // Data configuration
    StringType dataDirectory;      // Data directory
    StringType logDirectory;       // Log directory
    StringType tempDirectory;      // Temporary directory
    int maxLogFiles;               // Maximum log files
    int maxLogFileSize;            // Maximum log file size (MB)
    
    // Hardware configuration
    double defaultSampleRate;      // Default sample rate (Hz)
    double defaultControlPeriod;   // Default control period (ms)
    int communicationTimeout;      // Communication timeout (ms)
    int maxRetryCount;             // Maximum retry count
    
    // Safety configuration
    bool enableSafetyMonitor;      // Enable safety monitoring
    double emergencyStopTimeout;   // Emergency stop timeout (ms)
    bool requireConfirmation;      // Require operation confirmation
    
    SystemConfig();
    
//    json ToJson() const override;
//    bool FromJson(const json& jsonData) override;
//    bool IsValid() const override;
};

/**
 * @brief Recent project information
 */
struct RecentProject {
    StringType projectName;        // Project name
    StringType projectPath;        // Project path
    StringType lastOpenTime;       // Last open time
    bool isValid;                  // File validity
    
    RecentProject() : isValid(false) {}
    RecentProject(const StringType& name, const StringType& path) 
        : projectName(name), projectPath(path), isValid(true) {
        lastOpenTime = Utils::FormatTimestamp(Utils::GetCurrentTimestamp());
    }
};

/**
 * @brief Configuration manager
 * @details Singleton pattern, responsible for all configuration read/write and management
 */
class ConfigManager {
private:
    static UniquePtr<ConfigManager> instance_;
    static std::mutex instanceMutex_;
    
    SystemConfig systemConfig_;
    std::vector<RecentProject> recentProjects_;
    StringType configFilePath_;
    StringType recentProjectsFilePath_;
    
    mutable std::mutex configMutex_;
    
    ConfigManager();
    
public:
    ~ConfigManager() = default;
    
    // Disable copy constructor and assignment
    ConfigManager(const ConfigManager&) = delete;
    ConfigManager& operator=(const ConfigManager&) = delete;
    
    /**
     * @brief Get singleton instance
     * @return Configuration manager instance
     */
    static ConfigManager& GetInstance();
    
    /**
     * @brief Initialize configuration manager
     * @param configDir Configuration directory path
     * @return true if successful
     */
    bool Initialize(const StringType& configDir = "config");
    
    /**
     * @brief Save all configurations
     * @return true if successful
     */
    bool SaveAllConfigs();
    
    /**
     * @brief Load all configurations
     * @return true if successful
     */
    bool LoadAllConfigs();
    
    // ========================================================================
    // System configuration management
    // ========================================================================
    
    /**
     * @brief Get system configuration
     * @return System configuration reference
     */
    const SystemConfig& GetSystemConfig() const;
    
    /**
     * @brief Update system configuration
     * @param config New system configuration
     * @return true if successful
     */
    bool UpdateSystemConfig(const SystemConfig& config);
    
    /**
     * @brief Reset system configuration to default values
     */
    void ResetSystemConfig();
    
    // ========================================================================
    // Recent project management
    // ========================================================================
    
    /**
     * @brief Add recent project
     * @param projectName Project name
     * @param projectPath Project path
     */
    void AddRecentProject(const StringType& projectName, const StringType& projectPath);
    
    /**
     * @brief Remove recent project
     * @param projectPath Project path
     */
    void RemoveRecentProject(const StringType& projectPath);
    
    /**
     * @brief Get recent project list
     * @return Recent project list
     */
    std::vector<RecentProject> GetRecentProjects() const;
    
    /**
     * @brief Clear recent project list
     */
    void ClearRecentProjects();
    
    // ========================================================================
    // Directory management
    // ========================================================================
    
    /**
     * @brief Ensure directory exists
     * @param dirPath Directory path
     * @return true if successful
     */
    bool EnsureDirectoryExists(const StringType& dirPath);
    
    /**
     * @brief Get configuration directory path
     * @return Configuration directory path
     */
    StringType GetConfigDirectory() const;
    
    /**
     * @brief Get data directory path
     * @return Data directory path
     */
    StringType GetDataDirectory() const;
    
    /**
     * @brief Get log directory path
     * @return Log directory path
     */
    StringType GetLogDirectory() const;
    
    /**
     * @brief Get temporary directory path
     * @return Temporary directory path
     */
    StringType GetTempDirectory() const;
    
private:
    /**
     * @brief Create default directory structure
     */
    void CreateDefaultDirectories();
};

} // namespace Config
