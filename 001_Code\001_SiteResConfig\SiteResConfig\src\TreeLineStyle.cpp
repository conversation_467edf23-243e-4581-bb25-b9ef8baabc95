#include "treelinestyle.h"
#include <QTreeView>
#include <QPainter>
#include <QStyleOptionViewItem>
#include <QAbstractItemModel>
#include <QDebug>

void TreeLineStyle::drawPrimitive(PrimitiveElement elem,
                                  const QStyleOption *opt,
                                  QPainter *p,
                                  const QWidget *w) const
{
    // 只处理树形分支指示器，其他元素使用默认绘制
    if (elem != PE_IndicatorBranch) {
        QProxyStyle::drawPrimitive(elem, opt, p, w);
        return;
    }

    const QStyleOptionViewItem *vopt =
        qstyleoption_cast<const QStyleOptionViewItem*>(opt);
    if (!vopt) {
        // 无效选项，不绘制任何内容
        return;
    }

    auto *tv = qobject_cast<const QTreeView*>(w);
    if (!tv) {
        // 不是树形控件，不绘制任何内容
        return;
    }

    QModelIndex index = vopt->index;
    QAbstractItemModel *model = tv->model();

    // 检查是否有子节点
    bool hasChildren = false;
    if (model && index.isValid()) {
        int childCount = model->rowCount(index);
        hasChildren = (childCount > 0);

        // 调试输出
        QString nodeText = model->data(index, Qt::DisplayRole).toString();
        qDebug() << "TreeLineStyle - Node:" << nodeText << "Children:" << childCount << "HasChildren:" << hasChildren;

        // 注释掉这个返回，让叶子节点也能绘制虚线
        // if (!hasChildren) {
        //     return;
        // }
    } else {
        // 如果无法获取模型信息，使用Qt的默认行为
        QProxyStyle::drawPrimitive(elem, opt, p, w);
        return;
    }

    // 所有节点都绘制虚线连接
    QRect rect = opt->rect;

    p->save();
    p->setRenderHint(QPainter::Antialiasing, false);

    // 设置虚线样式
    QPen dottedPen(QColor(0x80, 0x80, 0x80), 1, Qt::DotLine);
    p->setPen(dottedPen);

    // 绘制树形连接线
    drawTreeLines(p, rect, vopt, tv, hasChildren);

    p->restore();

    // 只有有子节点的节点才绘制展开/折叠指示器
    if (hasChildren) {
        bool expanded = tv->isExpanded(index);

        p->save();
        p->setRenderHint(QPainter::Antialiasing, false);

        // 绘制展开/折叠指示器
        drawExpandCollapseIndicator(p, rect, expanded);

        p->restore();
    }
}

// 绘制树形连接线 - Windows标准风格
void TreeLineStyle::drawTreeLines(QPainter *p, const QRect &rect,
                                 const QStyleOptionViewItem *vopt,
                                 const QTreeView *treeView, bool hasChildren) const
{
    QModelIndex index = vopt->index;
    QAbstractItemModel *model = treeView->model();

    // 计算节点深度
    int depth = 0;
    QModelIndex parent = index.parent();
    while (parent.isValid()) {
        depth++;
        parent = parent.parent();
    }

    // 计算连接线位置
    int indentWidth = 20; // 每级缩进宽度
    int centerY = rect.center().y();

    // 当前节点的垂直线X位置
    int currentLineX = rect.left() + depth * indentWidth + 10;

    // 1. 绘制水平连接线（从垂直线连接到内容区域）
    if (index.parent().isValid()) { // 只有非根节点才绘制水平线
        if (hasChildren) {
            // 有子节点：连接到展开/折叠图标
            p->drawLine(currentLineX, centerY, currentLineX + 10, centerY);
        } else {
            // 叶子节点：连接到文本区域
            p->drawLine(currentLineX, centerY, currentLineX + 15, centerY);
        }
    }

    // 2. 绘制当前节点的垂直连接线
    if (index.parent().isValid()) {
        // 从上方连接到中心点
        p->drawLine(currentLineX, rect.top(), currentLineX, centerY);

        // 如果不是最后一个兄弟节点，继续向下绘制
        QModelIndex parentIndex = index.parent();
        int rowCount = model->rowCount(parentIndex);
        if (index.row() < rowCount - 1) {
            p->drawLine(currentLineX, centerY, currentLineX, rect.bottom());
        }
    }

    // 3. 绘制祖先节点的垂直连接线
    QModelIndex ancestor = index.parent();
    int ancestorDepth = depth - 1;
    while (ancestor.isValid() && ancestorDepth >= 0) {
        int ancestorLineX = rect.left() + ancestorDepth * indentWidth + 10;

        // 检查祖先节点是否还有后续兄弟节点
        QModelIndex ancestorParent = ancestor.parent();
        int ancestorRowCount = model->rowCount(ancestorParent);
        if (ancestor.row() < ancestorRowCount - 1) {
            // 绘制祖先的垂直连接线
            p->drawLine(ancestorLineX, rect.top(), ancestorLineX, rect.bottom());
        }

        ancestor = ancestor.parent();
        ancestorDepth--;
    }
}

// 绘制展开/折叠指示器 - Windows标准风格
void TreeLineStyle::drawExpandCollapseIndicator(QPainter *p, const QRect &rect, bool expanded) const
{
    const int buttonSize = 9; // Windows标准按钮大小

    // 计算按钮位置（在分支区域的中心）
    int buttonX = rect.center().x() - buttonSize / 2; // 居中显示
    int buttonY = rect.center().y() - buttonSize / 2;
    QRect buttonRect(buttonX, buttonY, buttonSize, buttonSize);

    // 绘制白色背景
    p->fillRect(buttonRect, Qt::white);

    // 绘制黑色边框
    QPen borderPen(Qt::black, 1);
    p->setPen(borderPen);
    p->drawRect(buttonRect);

    // 绘制符号
    int symbolMargin = 2;
    int symbolLeft = buttonRect.left() + symbolMargin;
    int symbolRight = buttonRect.right() - symbolMargin;
    int symbolTop = buttonRect.top() + symbolMargin;
    int symbolBottom = buttonRect.bottom() - symbolMargin;
    int symbolCenterX = buttonRect.center().x();
    int symbolCenterY = buttonRect.center().y();

    // 绘制水平线（加号和减号都有）
    p->drawLine(symbolLeft, symbolCenterY, symbolRight, symbolCenterY);

    // 如果是折叠状态（未展开），绘制垂直线形成加号
    if (!expanded) {
        p->drawLine(symbolCenterX, symbolTop, symbolCenterX, symbolBottom);
    }
    // 如果是展开状态，只有水平线，形成减号
}
