/********************************************************************************
** Form generated from reading UI file 'NodeConfigDialog.ui'
**
** Created by: Qt User Interface Compiler version 5.14.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_NODECONFIGDIALOG_H
#define UI_NODECONFIGDIALOG_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QCheckBox>
#include <QtWidgets/QDialog>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QVBoxLayout>

QT_BEGIN_NAMESPACE

class Ui_NodeConfigDialog
{
public:
    QVBoxLayout *verticalLayout;
    QHBoxLayout *nodeInfoLayout;
    QLabel *nodeLabel;
    QLabel *nodeNameLabel;
    QSpacerItem *horizontalSpacer;
    QHBoxLayout *channelCountLayout;
    QLabel *channelCountLabel;
    QSpinBox *channelCountSpinBox;
    QSpacerItem *horizontalSpacer_2;
    QGroupBox *channel1GroupBox;
    QGridLayout *ch1GridLayout;
    QLabel *ch1IpLabel;
    QLineEdit *ch1IpEdit;
    QLabel *ch1PortLabel;
    QSpinBox *ch1PortSpinBox;
    QCheckBox *ch1EnabledCheckBox;
    QGroupBox *channel2GroupBox;
    QGridLayout *ch2GridLayout;
    QLabel *ch2IpLabel;
    QLineEdit *ch2IpEdit;
    QLabel *ch2PortLabel;
    QSpinBox *ch2PortSpinBox;
    QCheckBox *ch2EnabledCheckBox;
    QSpacerItem *verticalSpacer;
    QHBoxLayout *buttonLayout;
    QSpacerItem *horizontalSpacer_3;
    QPushButton *okButton;
    QPushButton *cancelButton;

    void setupUi(QDialog *NodeConfigDialog)
    {
        if (NodeConfigDialog->objectName().isEmpty())
            NodeConfigDialog->setObjectName(QString::fromUtf8("NodeConfigDialog"));
        NodeConfigDialog->resize(450, 423);
        NodeConfigDialog->setModal(true);
        verticalLayout = new QVBoxLayout(NodeConfigDialog);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        nodeInfoLayout = new QHBoxLayout();
        nodeInfoLayout->setObjectName(QString::fromUtf8("nodeInfoLayout"));
        nodeLabel = new QLabel(NodeConfigDialog);
        nodeLabel->setObjectName(QString::fromUtf8("nodeLabel"));

        nodeInfoLayout->addWidget(nodeLabel);

        nodeNameLabel = new QLabel(NodeConfigDialog);
        nodeNameLabel->setObjectName(QString::fromUtf8("nodeNameLabel"));

        nodeInfoLayout->addWidget(nodeNameLabel);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        nodeInfoLayout->addItem(horizontalSpacer);


        verticalLayout->addLayout(nodeInfoLayout);

        channelCountLayout = new QHBoxLayout();
        channelCountLayout->setObjectName(QString::fromUtf8("channelCountLayout"));
        channelCountLabel = new QLabel(NodeConfigDialog);
        channelCountLabel->setObjectName(QString::fromUtf8("channelCountLabel"));

        channelCountLayout->addWidget(channelCountLabel);

        channelCountSpinBox = new QSpinBox(NodeConfigDialog);
        channelCountSpinBox->setObjectName(QString::fromUtf8("channelCountSpinBox"));
        channelCountSpinBox->setMinimumSize(QSize(80, 0));
        channelCountSpinBox->setMinimum(1);
        channelCountSpinBox->setMaximum(200);
        channelCountSpinBox->setValue(2);

        channelCountLayout->addWidget(channelCountSpinBox);

        horizontalSpacer_2 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        channelCountLayout->addItem(horizontalSpacer_2);


        verticalLayout->addLayout(channelCountLayout);

        channel1GroupBox = new QGroupBox(NodeConfigDialog);
        channel1GroupBox->setObjectName(QString::fromUtf8("channel1GroupBox"));
        ch1GridLayout = new QGridLayout(channel1GroupBox);
        ch1GridLayout->setObjectName(QString::fromUtf8("ch1GridLayout"));
        ch1IpLabel = new QLabel(channel1GroupBox);
        ch1IpLabel->setObjectName(QString::fromUtf8("ch1IpLabel"));

        ch1GridLayout->addWidget(ch1IpLabel, 0, 0, 1, 1);

        ch1IpEdit = new QLineEdit(channel1GroupBox);
        ch1IpEdit->setObjectName(QString::fromUtf8("ch1IpEdit"));

        ch1GridLayout->addWidget(ch1IpEdit, 0, 1, 1, 1);

        ch1PortLabel = new QLabel(channel1GroupBox);
        ch1PortLabel->setObjectName(QString::fromUtf8("ch1PortLabel"));

        ch1GridLayout->addWidget(ch1PortLabel, 1, 0, 1, 1);

        ch1PortSpinBox = new QSpinBox(channel1GroupBox);
        ch1PortSpinBox->setObjectName(QString::fromUtf8("ch1PortSpinBox"));
        ch1PortSpinBox->setMinimum(1);
        ch1PortSpinBox->setMaximum(65535);
        ch1PortSpinBox->setValue(8080);

        ch1GridLayout->addWidget(ch1PortSpinBox, 1, 1, 1, 1);

        ch1EnabledCheckBox = new QCheckBox(channel1GroupBox);
        ch1EnabledCheckBox->setObjectName(QString::fromUtf8("ch1EnabledCheckBox"));
        ch1EnabledCheckBox->setChecked(true);

        ch1GridLayout->addWidget(ch1EnabledCheckBox, 2, 0, 1, 2);


        verticalLayout->addWidget(channel1GroupBox);

        channel2GroupBox = new QGroupBox(NodeConfigDialog);
        channel2GroupBox->setObjectName(QString::fromUtf8("channel2GroupBox"));
        ch2GridLayout = new QGridLayout(channel2GroupBox);
        ch2GridLayout->setObjectName(QString::fromUtf8("ch2GridLayout"));
        ch2IpLabel = new QLabel(channel2GroupBox);
        ch2IpLabel->setObjectName(QString::fromUtf8("ch2IpLabel"));

        ch2GridLayout->addWidget(ch2IpLabel, 0, 0, 1, 1);

        ch2IpEdit = new QLineEdit(channel2GroupBox);
        ch2IpEdit->setObjectName(QString::fromUtf8("ch2IpEdit"));

        ch2GridLayout->addWidget(ch2IpEdit, 0, 1, 1, 1);

        ch2PortLabel = new QLabel(channel2GroupBox);
        ch2PortLabel->setObjectName(QString::fromUtf8("ch2PortLabel"));

        ch2GridLayout->addWidget(ch2PortLabel, 1, 0, 1, 1);

        ch2PortSpinBox = new QSpinBox(channel2GroupBox);
        ch2PortSpinBox->setObjectName(QString::fromUtf8("ch2PortSpinBox"));
        ch2PortSpinBox->setMinimum(1);
        ch2PortSpinBox->setMaximum(65535);
        ch2PortSpinBox->setValue(8081);

        ch2GridLayout->addWidget(ch2PortSpinBox, 1, 1, 1, 1);

        ch2EnabledCheckBox = new QCheckBox(channel2GroupBox);
        ch2EnabledCheckBox->setObjectName(QString::fromUtf8("ch2EnabledCheckBox"));
        ch2EnabledCheckBox->setChecked(true);

        ch2GridLayout->addWidget(ch2EnabledCheckBox, 2, 0, 1, 2);


        verticalLayout->addWidget(channel2GroupBox);

        verticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout->addItem(verticalSpacer);

        buttonLayout = new QHBoxLayout();
        buttonLayout->setObjectName(QString::fromUtf8("buttonLayout"));
        horizontalSpacer_3 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        buttonLayout->addItem(horizontalSpacer_3);

        okButton = new QPushButton(NodeConfigDialog);
        okButton->setObjectName(QString::fromUtf8("okButton"));

        buttonLayout->addWidget(okButton);

        cancelButton = new QPushButton(NodeConfigDialog);
        cancelButton->setObjectName(QString::fromUtf8("cancelButton"));

        buttonLayout->addWidget(cancelButton);


        verticalLayout->addLayout(buttonLayout);


        retranslateUi(NodeConfigDialog);
        QObject::connect(cancelButton, SIGNAL(clicked()), NodeConfigDialog, SLOT(reject()));

        okButton->setDefault(true);


        QMetaObject::connectSlotsByName(NodeConfigDialog);
    } // setupUi

    void retranslateUi(QDialog *NodeConfigDialog)
    {
        NodeConfigDialog->setWindowTitle(QCoreApplication::translate("NodeConfigDialog", "\350\212\202\347\202\271\351\205\215\347\275\256", nullptr));
        nodeLabel->setText(QCoreApplication::translate("NodeConfigDialog", "\350\212\202\347\202\271\345\220\215\347\247\260:", nullptr));
        nodeNameLabel->setText(QCoreApplication::translate("NodeConfigDialog", "LD-B1", nullptr));
        channelCountLabel->setText(QCoreApplication::translate("NodeConfigDialog", "\351\200\232\351\201\223\346\225\260\351\207\217:", nullptr));
        channel1GroupBox->setTitle(QCoreApplication::translate("NodeConfigDialog", "CH1 \351\200\232\351\201\223\351\205\215\347\275\256", nullptr));
        ch1IpLabel->setText(QCoreApplication::translate("NodeConfigDialog", "IP\345\234\260\345\235\200:", nullptr));
        ch1IpEdit->setText(QCoreApplication::translate("NodeConfigDialog", "*************", nullptr));
        ch1IpEdit->setPlaceholderText(QCoreApplication::translate("NodeConfigDialog", "\350\257\267\350\276\223\345\205\245IP\345\234\260\345\235\200", nullptr));
        ch1PortLabel->setText(QCoreApplication::translate("NodeConfigDialog", "\347\253\257\345\217\243:", nullptr));
        ch1EnabledCheckBox->setText(QCoreApplication::translate("NodeConfigDialog", "\345\220\257\347\224\250\346\255\244\351\200\232\351\201\223", nullptr));
        channel2GroupBox->setTitle(QCoreApplication::translate("NodeConfigDialog", "CH2 \351\200\232\351\201\223\351\205\215\347\275\256", nullptr));
        ch2IpLabel->setText(QCoreApplication::translate("NodeConfigDialog", "IP\345\234\260\345\235\200:", nullptr));
        ch2IpEdit->setText(QCoreApplication::translate("NodeConfigDialog", "*************", nullptr));
        ch2IpEdit->setPlaceholderText(QCoreApplication::translate("NodeConfigDialog", "\350\257\267\350\276\223\345\205\245IP\345\234\260\345\235\200", nullptr));
        ch2PortLabel->setText(QCoreApplication::translate("NodeConfigDialog", "\347\253\257\345\217\243:", nullptr));
        ch2EnabledCheckBox->setText(QCoreApplication::translate("NodeConfigDialog", "\345\220\257\347\224\250\346\255\244\351\200\232\351\201\223", nullptr));
        okButton->setText(QCoreApplication::translate("NodeConfigDialog", "\347\241\256\345\256\232", nullptr));
        cancelButton->setText(QCoreApplication::translate("NodeConfigDialog", "\345\217\226\346\266\210", nullptr));
    } // retranslateUi

};

namespace Ui {
    class NodeConfigDialog: public Ui_NodeConfigDialog {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_NODECONFIGDIALOG_H
