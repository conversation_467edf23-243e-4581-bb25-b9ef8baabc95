@echo off
echo ========================================
echo  Const Modifier Compilation Test
echo ========================================

echo.
echo Fixed const modifier issues:
echo 1. ActuatorDataManager::clearError() const
echo 2. SensorDataManager::clearError() const  
echo 3. SensorDataManager::setError() const
echo 4. MainWindow::AddLogEntry() const
echo 5. Removed all const_cast usage
echo.

if not exist "Makefile" (
    echo Generating Makefile...
    qmake SiteResConfig_Simple.pro -spec win32-g++
    if errorlevel 1 (
        echo qmake failed!
        pause
        exit /b 1
    )
)

echo.
echo Cleaning previous build...
mingw32-make clean

echo.
echo Starting compilation...
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  Compilation FAILED!
    echo ========================================
    echo.
    echo Please check:
    echo 1. const method declarations consistency
    echo 2. mutable member variables
    echo 3. remaining const_cast usage
    echo 4. error handling method const declarations
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  Compilation SUCCESS!
    echo ========================================
    
    if exist "SiteResConfig.exe" (
        echo Executable: SiteResConfig.exe
        echo.
        echo All const modifier errors fixed!
        echo.
        echo Starting program...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo Executable: debug\SiteResConfig.exe
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo Executable: release\SiteResConfig.exe
        start release\SiteResConfig.exe
    ) else (
        echo Warning: Executable not found!
    )
)

echo.
pause
