#ifndef XLSDATAEXPORTER_H
#define XLSDATAEXPORTER_H

#include "IDataExporter.h"
#include "HardwareNodeStructs.h"  // 🔄 修正：包含独立的硬件节点结构体定义
#include <QString>
#include <QTreeWidget>
#include <QStringList>
#include <QRegExp>
#include <memory>

// 前向声明
class SensorDataManager_1_2;
class ActuatorDataManager_1_2;  // 🆕 新增：作动器数据管理器前向声明
class CtrlChanDataManager;  // 🆕 新增：控制通道数据管理器前向声明
class HardwareNodeResDataManager;  // 🆕 新增：硬件节点资源数据管理器前向声明
namespace QXlsx {
    class Document;
    class Worksheet;
    class Format;
}

// 前向声明作动器相关结构体
namespace UI {
    struct ActuatorParams_1_2;
    struct ActuatorGroup_1_2;
    struct ControlChannelParams;
    struct ControlChannelGroup;
    struct HardwareNodeParams;
    struct HardwareNodeGroup;
}

// 注意：UI::NodeConfigParams 和 UI::ChannelInfo 已通过 HardwareNodeStructs.h 包含完整定义

/**
 * @brief XLS数据导出器
 * @details 负责将硬件树和传感器数据导出为Excel格式，同时支持从Excel文件导入数据
 */
class XLSDataExporter_1_2 : public IDataExporter {
public:
    explicit XLSDataExporter_1_2(SensorDataManager_1_2* sensorManager = nullptr, ActuatorDataManager_1_2* actuatorManager = nullptr, CtrlChanDataManager* ctrlChanManager = nullptr);
    virtual ~XLSDataExporter_1_2() = default;
    
    // 实现接口方法
    bool exportControlChannelDetails(const QList<UI::ControlChannelGroup>& channelGroups, const QString& filePath);  // 🆕 新增：控制通道详细信息导出
    bool exportHardwareNodeDetails(const QList<UI::NodeConfigParams>& nodeConfigs, const QString& filePath);  // 🆕 新增：硬件节点详细信息导出（封装原有功能）
    bool exportCompleteProject(const QString& filePath) override;

    // 🆕 新增：设置硬件节点配置数据（用于完整项目导出）
    void setHardwareNodeConfigs(const QList<UI::NodeConfigParams>& nodeConfigs);

    // 🆕 新增：硬件节点详细配置导出方法
    bool importToHardwareTree(const QString& filePath, QTreeWidget* treeWidget) override;
    QString getSupportedExtension() const override;
    QString getFormatDescription() const override;
    QString getLastError() const override;

    // 🆕 新增：工程导入功能
    bool importProject(const QString& filePath);

    // 🆕 新增：分工作表导入方法
    bool importActuatorDetails(QXlsx::Document& doc);
    bool importSensorDetails(QXlsx::Document& doc);
    bool importControlChannelDetails(QXlsx::Document& doc);
    bool importHardwareNodeDetails(QXlsx::Document& doc);

    // 🆕 新增：数据验证方法
    bool validateWorksheetStructure(QXlsx::Document& doc);

    // 🆕 新增：通用的组名称映射构建和处理函数
    QMap<int, QString> buildGroupNameMapping(QXlsx::Document& doc, int startRow, int groupIdCol, int groupNameCol);
    QString getGroupNameFromMapping(const QMap<int, QString>& mapping, int groupId, const QString& currentGroupName);

    // 🆕 新增：导入错误处理
    QString getImportError() const;
    void clearImportError();

    // XLS特有方法
    /**
     * @brief 设置工作表名称
     * @param sheetName 工作表名称
     */
    void setWorksheetName(const QString& sheetName);
    
    /**
     * @brief 设置是否包含表头
     * @param includeHeader 是否包含表头
     */
    void setIncludeHeader(bool includeHeader);
    
    /**
     * @brief 设置是否自动调整列宽
     * @param autoFitColumns 是否自动调整列宽
     */
    void setAutoFitColumns(bool autoFitColumns);
    
    /**
     * @brief 设置表格样式
     * @param useTableStyle 是否使用表格样式
     */
    void setUseTableStyle(bool useTableStyle);

    /**
     * @brief 设置作动器数据管理器
     * @param actuatorManager 作动器数据管理器指针
     */
    void setActuatorDataManager(ActuatorDataManager_1_2* actuatorManager);

    /**
     * @brief 设置控制通道数据管理器
     * @param ctrlChanManager 控制通道数据管理器指针
     */
    void setCtrlChanDataManager(CtrlChanDataManager* ctrlChanManager);

    /**
     * @brief 设置硬件节点资源数据管理器
     * @param hardwareNodeManager 硬件节点资源数据管理器指针
     */
    void setHardwareNodeResDataManager(HardwareNodeResDataManager* hardwareNodeManager);

    /**
     * @brief 获取传感器数据管理器（供JSON导出器使用）
     * @return 传感器数据管理器指针
     */
    SensorDataManager_1_2* getSensorDataManager() const { return sensorDataManager_; }

    /**
     * @brief 获取作动器数据管理器（供JSON导出器使用）
     * @return 作动器数据管理器指针
     */
    ActuatorDataManager_1_2* getActuatorDataManager() const { return actuatorDataManager_; }

    /**
     * @brief 获取控制通道数据管理器（供JSON导出器使用）
     * @return 控制通道数据管理器指针
     */
    CtrlChanDataManager* getCtrlChanDataManager() const { return ctrlChanDataManager_; }

    /**
     * @brief 获取硬件节点配置数据（供JSON导出器使用）
     * @return 硬件节点配置列表
     */
    const QList<UI::NodeConfigParams>& getHardwareNodeConfigs() const { return hardwareNodeConfigs_; }
    
    /**
     * @brief 从Excel文件读取硬件配置数据
     * @param filePath Excel文件路径
     * @return 读取的数据结构
     */
    QList<QStringList> readHardwareDataFromExcel(const QString& filePath);
    
    /**
     * @brief 从Excel文件读取传感器配置数据
     * @param filePath Excel文件路径
     * @return 读取的传感器参数列表
     */
    QList<UI::SensorParams_1_2> readSensorDataFromExcel(const QString& filePath);

    // 🆕 新增：作动器组相关方法（XLS特有）

    /**
     * @brief 导出作动器组到Excel文件
     * @param actuatorGroups 作动器组列表
     * @param filePath Excel文件路径
     * @return 导出是否成功
     */
    bool exportActuatorGroups(const QList<UI::ActuatorGroup_1_2>& actuatorGroups, const QString& filePath);

    /**
     * @brief 从Excel文件读取作动器组数据
     * @param filePath Excel文件路径
     * @return 作动器组列表
     */
    QList<UI::ActuatorGroup_1_2> readActuatorGroupsFromExcel(const QString& filePath);

    /**
     * @brief 导出单个作动器组到Excel文件
     * @param actuatorGroup 作动器组
     * @param filePath Excel文件路径
     * @return 导出是否成功
     */
    bool exportSingleActuatorGroup(const UI::ActuatorGroup_1_2& actuatorGroup, const QString& filePath);

    /**
     * @brief 在现有Excel文档中创建作动器工作表
     * @param document Excel文档指针
     * @param actuatorGroups 作动器组列表
     * @return 创建是否成功
     */
    bool createActuatorWorksheet(QXlsx::Document* document, const QList<UI::ActuatorGroup_1_2>& actuatorGroups);

    /**
     * @brief 向现有Excel文档添加作动器工作表
     * @param filePath Excel文件路径
     * @param actuatorGroups 作动器组列表
     * @return 添加是否成功
     */
    bool addActuatorWorksheetToExcel(const QString& filePath, const QList<UI::ActuatorGroup_1_2>& actuatorGroups);

    /**
     * @brief 导出完整项目（硬件树 + 作动器工作表）
     * @param treeWidget 硬件树控件
     * @param actuatorGroups 作动器组列表
     * @param filePath Excel文件路径
     * @return 导出是否成功
     */
    bool exportCompleteProjectWithActuators(QTreeWidget* treeWidget, const QList<UI::ActuatorGroup_1_2>& actuatorGroups, const QString& filePath);

    // 🆕 新增：传感器组相关方法（参考作动器组实现）

    /**
     * @brief 导出传感器组详细信息到Excel文件
     * @param sensorGroups 传感器组列表
     * @param filePath Excel文件路径
     * @return 导出是否成功
     */
    bool exportSensorGroupDetails(const QList<UI::SensorGroup_1_2>& sensorGroups, const QString& filePath);

//    /**
//     * @brief 添加传感器组详细信息到Excel
//     * @param worksheet 工作表指针
//     * @param group 传感器组
//     * @param row 起始行号
//     * @param displayGroupId 显示的组序号
//     * @return 下一个可用行号
//     */
//    int addSensorGroupDetailToExcel(QXlsx::Worksheet* worksheet, const UI::SensorGroup_1_2& group, int row, int displayGroupId = -1);

private:
    SensorDataManager_1_2*              sensorDataManager_;
    ActuatorDataManager_1_2*            actuatorDataManager_;   // 🆕 新增：作动器数据管理器
    CtrlChanDataManager*            ctrlChanDataManager_;   // 🆕 新增：控制通道数据管理器
    HardwareNodeResDataManager*     hardwareNodeResDataManager_;  // 🆕 新增：硬件节点资源数据管理器
    QList<UI::NodeConfigParams>     hardwareNodeConfigs_;   // 🆕 新增：用于完整项目导出的硬件节点配置数据

    QString lastError_;
    QString importError_;           // 🆕 新增：导入错误信息
    QString worksheetName_;
    bool includeHeader_;
    bool autoFitColumns_;

    bool useTableStyle_;
    
    // 私有辅助方法
    /**
     * @brief 清除错误信息
     */
    void clearError();
    
    /**
     * @brief 设置错误信息
     * @param error 错误信息
     */
    void setError(const QString& error);
    
    /**
     * @brief 创建Excel文档
     * @return Excel文档智能指针
     */
    std::unique_ptr<QXlsx::Document> createDocument();
    
    /**
     * @brief 设置表头样式
     * @param worksheet 工作表指针
     * @param row 行号
     * @param startCol 起始列号
     * @param headers 表头列表
     */
    void setupHeaderStyle(QXlsx::Worksheet* worksheet, int row, int startCol, const QStringList& headers);
    
    /**
     * @brief 导出树节点到Excel
     * @param worksheet 工作表指针
     * @param item 树节点
     * @param row 当前行号
     * @param level 层级
     * @return 下一个可用行号
     */
    int exportTreeItemToExcel(QXlsx::Worksheet* worksheet, QTreeWidgetItem* item, int row, int level = 0);
    
    /**
     * @brief 添加传感器详细信息到Excel
     * @param worksheet 工作表指针
     * @param params 传感器参数
     * @param row 起始行号
     * @return 下一个可用行号
     */
    int addSensorDetailToExcel(QXlsx::Worksheet* worksheet, const UI::SensorParams_1_2& params, int row);

    /**
     * @brief 添加作动器组详细信息到Excel
     * @param worksheet 工作表指针
     * @param group 作动器组
     * @param row 起始行号
     * @param displayGroupId 显示用的组序号（-1表示使用原组ID）
     * @return 下一个可用行号
     */
    int addActuatorGroupDetailToExcel(QXlsx::Worksheet* worksheet, const UI::ActuatorGroup_1_2& group, int row, int displayGroupId = -1);
    
    /**
     * @brief 获取节点类型
     * @param item 树节点
     * @return 节点类型字符串
     */
    QString getItemType(QTreeWidgetItem* item) const;
    
    /**
     * @brief 解析Excel行数据为传感器参数
     * @param rowData 行数据
     * @return 传感器参数
     */
    UI::SensorParams_1_2 parseRowToSensorParams(const QStringList& rowData);
    
    /**
     * @brief 验证Excel文件格式
     * @param filePath 文件路径
     * @return 是否为有效的Excel文件
     */
    bool validateExcelFile(const QString& filePath);
    
    /**
     * @brief 自动调整列宽
     * @param worksheet 工作表指针
     * @param maxCol 最大列数
     */
    void autoFitColumnWidths(QXlsx::Worksheet* worksheet, int maxCol);

    /**
     * @brief 导出作动器组到指定工作表
     * @param worksheet 工作表指针
     * @param actuatorGroups 作动器组列表
     * @return 导出是否成功
     */
    bool exportActuatorGroupsToWorksheet(QXlsx::Worksheet* worksheet, const QList<UI::ActuatorGroup_1_2>& actuatorGroups);

    /**
     * @brief 解析tooltip为参数列表
     * @param tooltip tooltip字符串
     * @return 参数列表
     */
    QStringList parseTooltipToParams(const QString& tooltip);

    /**
     * @brief 从行中提取指定键的值
     * @param line 文本行
     * @param key 键名
     * @return 提取的值
     */
    QString extractValueFromLine(const QString& line, const QString& key);

    // 🆕 新增：作动器组相关私有方法
    /**
     * @brief 写入作动器组表头信息
     * @param worksheet 工作表指针
     */
    void writeActuatorGroupHeader(QXlsx::Worksheet* worksheet);

    /**
     * @brief 写入作动器组数据
     * @param worksheet 工作表指针
     * @param group 作动器组
     * @param startRow 起始行号
     * @return 下一行行号
     */
    int writeActuatorGroupData(QXlsx::Worksheet* worksheet, const UI::ActuatorGroup_1_2& group, int startRow);

    /**
     * @brief 应用作动器组样式
     * @param worksheet 工作表指针
     * @param lastRow 最后一行行号
     */
    void applyActuatorGroupStyles(QXlsx::Worksheet* worksheet, int lastRow);

    // 🆕 新增：控制通道相关私有方法
    /**
     * @brief 添加控制通道组详细信息到Excel
     * @param worksheet 工作表指针
     * @param group 控制通道组
     * @param row 起始行号
     * @param displayGroupId 显示用的组序号（-1表示使用原组ID）
     * @return 下一个可用行号
     */
    int addControlChannelGroupDetailToExcel(QXlsx::Worksheet* worksheet, const UI::ControlChannelGroup& group, int row, int displayGroupId = -1);

    /**
     * @brief 添加单个控制通道详细信息到Excel（简化格式）
     * @param worksheet 工作表指针
     * @param channel 控制通道参数
     * @param row 起始行号
     * @param channelIndex 通道序号
     * @return 下一个可用行号
     */
    int addControlChannelDetailToExcel(QXlsx::Worksheet* worksheet, const UI::ControlChannelParams& channel, int row, int channelIndex);

    /**
     * @brief 写入控制通道组表头信息
     * @param worksheet 工作表指针
     */
    void writeControlChannelHeader(QXlsx::Worksheet* worksheet);

    /**
     * @brief 控制模式转换为字符串
     * @param mode 控制模式
     * @return 控制模式字符串
     */
    QString controlModeToString(const std::string& mode) const;

    /**
     * @brief 布尔值转换为是/否
     * @param value 布尔值
     * @return 是/否字符串
     */
    QString boolToYesNo(bool value) const;
    
    /**
     * @brief 极性值转字符串
     * @param polarity 极性值 (1=Positive, -1=Negative, 9=Both, 0=Unknown)
     * @return 极性字符串
     */
    QString polarityToString(int polarity) const;
    
    /**
     * @brief 字符串转极性值
     * @param polarityStr 极性字符串
     * @return 极性值 (1=Positive, -1=Negative, 9=Both, 0=Unknown)
     */
    int parsePolarityFromString(const QString& polarityStr) const;

    // 注意：硬件节点相关的私有方法已经在原有实现中存在：
    // - createHardwareNodeWorksheet()
    // - getHardwareNodeConfigsFromTree()
    // - addNodeConfigDetailToExcel()
    // 不需要重复声明

    /**
     * @brief 创建作动器组汇总工作表
     * @param document Excel文档指针
     * @param actuatorGroups 作动器组列表
     */
    void createActuatorGroupSummarySheet(QXlsx::Document* document, const QList<UI::ActuatorGroup_1_2>& actuatorGroups);

    /**
     * @brief 写入作动器工作表表头信息
     * @param worksheet 工作表指针
     */
    void writeActuatorWorksheetHeader(QXlsx::Worksheet* worksheet);

    /**
     * @brief 应用作动器工作表样式
     * @param worksheet 工作表指针
     * @param lastRow 最后一行行号
     */
    void applyActuatorWorksheetStyles(QXlsx::Worksheet* worksheet, int lastRow);

    /**
     * @brief 将Excel行数据解析为作动器参数
     * @param rowData Excel行数据
     * @return 作动器参数
     */
    UI::ActuatorParams_1_2 parseRowToActuatorParams(const QStringList& rowData);

    // 🆕 新增：硬件节点相关私有方法
    /**
     * @brief 在现有Excel文档中创建硬件节点工作表
     * @param document Excel文档指针
     * @param nodeConfigs 节点配置参数列表
     * @return 创建是否成功
     */
    bool createHardwareNodeWorksheet(QXlsx::Document* document, const QList<UI::NodeConfigParams>& nodeConfigs);

//    /**
//     * @brief 从硬件树获取节点配置数据
//     * @param treeWidget 硬件树控件
//     * @return 节点配置参数列表
//     */
//    QList<UI::NodeConfigParams> getHardwareNodeConfigsFromTree(QTreeWidget* treeWidget) const;

    /**
     * @brief 向Excel添加节点配置详细信息
     * @param worksheet Excel工作表
     * @param nodeConfig 节点配置参数
     * @param row 起始行号
     * @param nodeId 节点ID
     * @return 下一行号
     */
    int addNodeConfigDetailToExcel(QXlsx::Worksheet* worksheet, const UI::NodeConfigParams& nodeConfig, int row, int nodeId);

    /**
     * @brief 向Excel添加单个硬件节点通道详细信息
     * @param worksheet Excel工作表
     * @param nodeConfig 节点配置参数
     * @param channel 通道信息
     * @param row 行号
     * @param nodeId 节点ID
     * @param isFirstChannel 是否为节点的第一个通道
     * @return 下一行号
     */
    int addHardwareChannelDetailToExcel(QXlsx::Worksheet* worksheet, const UI::NodeConfigParams& nodeConfig, const UI::ChannelInfo& channel, int row, int nodeId, bool isFirstChannel);
};

#endif // XLSDATAEXPORTER_H
