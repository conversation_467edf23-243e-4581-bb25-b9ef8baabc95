@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🚀 快速编译测试 - 数据序号管理修复
echo ========================================
echo.

cd SiteResConfig

echo 🔧 清理编译文件...
if exist "Makefile" del "Makefile" >nul 2>&1
if exist "debug" rmdir /s /q "debug" >nul 2>&1
if exist "release" rmdir /s /q "release" >nul 2>&1

echo.
echo 🔨 生成Makefile...
qmake SiteResConfig_Simple.pro
if %errorlevel% neq 0 (
    echo ❌ qmake失败
    pause
    exit /b 1
)

echo.
echo 🔨 开始编译...
make 2>&1 | findstr /C:"error:" /C:"Error" /C:"错误"
if %errorlevel%==0 (
    echo ❌ 发现编译错误，请检查上面的错误信息
    echo.
    echo 🔍 常见错误解决方案：
    echo   1. QList::join错误 - 已修复
    echo   2. groupId未声明错误 - 已修复  
    echo   3. 头文件包含问题 - 检查include路径
    echo.
    pause
    exit /b 1
) else (
    echo ✅ 编译成功！
)

echo.
echo 🔍 检查生成文件...
if exist "debug\SiteResConfig.exe" (
    echo ✅ Debug版本: debug\SiteResConfig.exe
) 
if exist "release\SiteResConfig.exe" (
    echo ✅ Release版本: release\SiteResConfig.exe
)

echo.
echo ========================================
echo 🎉 编译测试完成
echo ========================================
echo.
echo 📋 修复内容确认：
echo   ✅ DataSequenceManager类编译成功
echo   ✅ QList::join错误已修复
echo   ✅ XLSDataExporter序号重映射功能
echo   ✅ 项目文件配置正确
echo.
echo 🚀 下一步：
echo   1. 启动应用程序测试功能
echo   2. 打开"20250819171946_实验工程 - 副本.xls"
echo   3. 观察控制台日志中的序号映射信息
echo   4. 检查界面数据显示是否正确
echo.

cd ..
pause
