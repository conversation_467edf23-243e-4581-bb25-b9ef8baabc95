@echo off
chcp 65001
echo =============================================
echo 作动器编辑功能完善测试脚本
echo =============================================
echo.

set TEST_NAME=test_actuator_edit_enhancement
set QT_DIR=C:\Qt\5.14.2\mingw73_32
set MINGW_DIR=C:\Qt\Tools\mingw730_32

echo 🔧 设置Qt环境...
set PATH=%QT_DIR%\bin;%MINGW_DIR%\bin;%PATH%

echo 📂 当前目录: %CD%
echo.

echo 🏗️ 编译测试程序...
g++ -std=c++11 ^
    -I./include ^
    -I%QT_DIR%\include ^
    -I%QT_DIR%\include\QtCore ^
    -I%QT_DIR%\include\QtGui ^
    -I%QT_DIR%\include\QtWidgets ^
    -L%QT_DIR%\lib ^
    -lQt5Core -lQt5Gui -lQt5Widgets ^
    %TEST_NAME%.cpp ^
    src\ActuatorDialog_1_2.cpp ^
    src\ActuatorDataManager_1_2.cpp ^
    -o %TEST_NAME%.exe

if %ERRORLEVEL% NEQ 0 (
    echo ❌ 编译失败！
    echo.
    echo 💡 可能的解决方案：
    echo 1. 检查Qt路径是否正确
    echo 2. 确保源文件存在
    echo 3. 检查依赖关系
    echo 4. 查看编译错误信息
    pause
    exit /b 1
)

echo ✅ 编译成功！
echo.

echo 🚀 运行测试程序...
echo =============================================
%TEST_NAME%.exe

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ 测试执行失败，错误代码: %ERRORLEVEL%
) else (
    echo.
    echo ✅ 测试执行完成！
)

echo.
echo =============================================
echo 🔍 测试结果分析：
echo.
if exist "%TEST_NAME%.exe" (
    echo ✓ 可执行文件生成成功
) else (
    echo ✗ 可执行文件生成失败
)

echo.
echo 📝 测试报告：
echo --------------------------------
echo 测试时间: %date% %time%
echo 测试程序: %TEST_NAME%.exe
echo Qt版本: 5.14.2
echo 编译器: MinGW GCC
echo --------------------------------
echo.

echo 💡 下一步：
echo 1. 检查测试输出结果
echo 2. 验证编辑功能是否正常工作
echo 3. 在主程序中集成这些改进
echo 4. 进行用户界面测试
echo.

echo 🧹 清理临时文件...
if exist "%TEST_NAME%.exe" (
    echo 保留测试可执行文件: %TEST_NAME%.exe
)

echo.
echo ✨ 作动器编辑功能完善测试完成！
echo =============================================
pause 