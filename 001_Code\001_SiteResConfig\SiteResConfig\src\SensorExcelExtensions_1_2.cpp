#include "SensorExcelExtensions_1_2.h"
#include "QtXlsxWriter-master/src/xlsx/xlsxdocument.h"
#include "QtXlsxWriter-master/src/xlsx/xlsxcellrange.h"
#include "QtXlsxWriter-master/src/xlsx/xlsxformat.h"
#include <QDebug>
#include <QDateTime>
#include <QFileInfo>
#include <QDir>

// 静态成员初始化
QString SensorExcelExtensions_1_2::lastError_;

// 本地极性转换函数
namespace {
    QString polarityToString(int polarity) {
        // 极性存储必须使用数字格式，不再使用文本描述
        return QString::number(polarity);
    }
}

bool SensorExcelExtensions_1_2::exportEnhancedSensorDetails(const QList<UI::SensorGroup_1_2>& sensorGroups, const QString& filePath) {
    clearError();
    
    if (sensorGroups.isEmpty()) {
        setError(u8"传感器组列表为空，无法导出");
        return false;
    }
    
    try {
        // 创建Excel文档
        QXlsx::Document document;
        
        // 创建传感器详细配置工作表
        if (!createSensorDetailWorksheet(&document, sensorGroups)) {
            return false; // 错误信息已在子方法中设置
        }
        
        // 保存文件
        if (!document.saveAs(filePath)) {
            setError(QString(u8"保存Excel文件失败: %1").arg(filePath));
            return false;
        }
        
        qDebug() << "SensorExcelExtensions_1_2: 成功导出传感器详细配置到" << filePath;
        return true;
        
    } catch (const std::exception& e) {
        setError(QString(u8"导出过程中发生异常: %1").arg(e.what()));
        return false;
    } catch (...) {
        setError(u8"导出过程中发生未知异常");
        return false;
    }
}

QList<UI::SensorGroup_1_2> SensorExcelExtensions_1_2::importEnhancedSensorDetails(const QString& filePath) {
    clearError();
    QList<UI::SensorGroup_1_2> result;
    
    if (!QFile::exists(filePath)) {
        setError(QString(u8"文件不存在: %1").arg(filePath));
        return result;
    }
    
    try {
        // 打开Excel文档
        QXlsx::Document document(filePath);
        
        // 读取传感器详细配置工作表
        if (!readSensorDetailWorksheet(&document, result)) {
            return QList<UI::SensorGroup_1_2>(); // 错误信息已在子方法中设置
        }
        
        qDebug() << "SensorExcelExtensions_1_2: 成功导入传感器详细配置，共" << result.size() << "个组";
        return result;
        
    } catch (const std::exception& e) {
        setError(QString(u8"导入过程中发生异常: %1").arg(e.what()));
        return QList<UI::SensorGroup_1_2>();
    } catch (...) {
        setError(u8"导入过程中发生未知异常");
        return QList<UI::SensorGroup_1_2>();
    }
}

bool SensorExcelExtensions_1_2::createSensorDetailWorksheet(QXlsx::Document* document, const QList<UI::SensorGroup_1_2>& sensorGroups) {
    if (!document) {
        setError(u8"Excel文档指针为空");
        return false;
    }
    
    try {
        // 选择或创建工作表
        QString sheetName = u8"传感器详细配置";
        document->addSheet(sheetName);
        document->selectSheet(sheetName);
        
        // 写入标题行
        document->write(1, 1, u8"传感器详细配置");
        
        // 合并标题行单元格 (A1:Q1)
        document->mergeCells(QXlsx::CellRange(1, 1, 1, 17));
        
        // 设置标题样式
        QXlsx::Format titleFormat;
        titleFormat.setFontBold(true);
        titleFormat.setFontSize(14);
        titleFormat.setHorizontalAlignment(QXlsx::Format::AlignHCenter);
        titleFormat.setVerticalAlignment(QXlsx::Format::AlignVCenter);
        document->write(1, 1, u8"传感器详细配置", titleFormat);
        
        // 写入表头 (第2行)
        QStringList headers = {
            u8"组序号",              // A
            u8"传感器组名称",        // B
            u8"传感器ID",           // C
            u8"传感器序列号",        // D
            u8"传感器型号",         // E
            u8"零点偏移",           // F
            u8"启用状态",           // G
            u8"线性系数K",          // H
            u8"线性系数B",          // I
            u8"精度",              // J
            u8"极性",              // K
            u8"测量单位类型",       // L
            u8"测量范围最小值",     // M
            u8"测量范围最大值",     // N
            u8"输出信号单位类型",   // O
            u8"输出信号范围最小值", // P
            u8"输出信号范围最大值"  // Q
        };
        
        // 设置表头格式
        QXlsx::Format headerFormat;
        headerFormat.setFontBold(true);
        headerFormat.setFontSize(10);
        headerFormat.setHorizontalAlignment(QXlsx::Format::AlignHCenter);
        headerFormat.setVerticalAlignment(QXlsx::Format::AlignVCenter);
        headerFormat.setBorderStyle(QXlsx::Format::BorderThin);
        headerFormat.setPatternBackgroundColor(QColor(220, 220, 220)); // 浅灰色背景
        
        for (int col = 0; col < headers.size(); ++col) {
            document->write(2, col + 1, headers[col], headerFormat);
        }
        
        // 写入数据行
        int currentRow = 3;
        for (int groupIndex = 0; groupIndex < sensorGroups.size(); ++groupIndex) {
            const UI::SensorGroup_1_2& group = sensorGroups[groupIndex];
            int groupDisplayId = groupIndex + 1; // 组序号从1开始
            
            for (int sensorIndex = 0; sensorIndex < group.sensors.size(); ++sensorIndex) {
                const UI::SensorParams_1_2& sensor = group.sensors[sensorIndex];
                
                // 设置数据格式
                QXlsx::Format dataFormat;
                dataFormat.setBorderStyle(QXlsx::Format::BorderThin);
                dataFormat.setHorizontalAlignment(QXlsx::Format::AlignLeft);
                dataFormat.setVerticalAlignment(QXlsx::Format::AlignVCenter);
                
                // 🆕 创建组名称格式（浅蓝色背景，粗体）- 参考作动器实现
                QXlsx::Format groupFormat;
                groupFormat.setBorderStyle(QXlsx::Format::BorderThin);
                groupFormat.setHorizontalAlignment(QXlsx::Format::AlignLeft);
                groupFormat.setVerticalAlignment(QXlsx::Format::AlignVCenter);
                groupFormat.setPatternBackgroundColor(QColor(231, 243, 255)); // 浅蓝色背景
                groupFormat.setFontBold(true);
                
                // 选择格式（第一个传感器使用组格式）
                QXlsx::Format currentFormat = (sensorIndex == 0) ? groupFormat : dataFormat;
                
                // A: 组序号
                document->write(currentRow, 1, groupDisplayId, currentFormat);
                
                // B: 传感器组名称 - 🆕 参考作动器：只在每组第一行显示
                document->write(currentRow, 2, (sensorIndex == 0) ? group.groupName : QString(), currentFormat);
                
                // C: 传感器ID
                document->write(currentRow, 3, sensor.sensorId, currentFormat);
                
                // D: 传感器序列号
                document->write(currentRow, 4, sensor.params_sn, currentFormat);
                
                // E: 传感器型号
                document->write(currentRow, 5, sensor.params_model, currentFormat);
                
                // F: 零点偏移
                document->write(currentRow, 6, sensor.zero_offset, currentFormat);
                
                // G: 启用状态
                QString enableText = sensor.enable ? u8"是" : u8"否";
                document->write(currentRow, 7, enableText, currentFormat);
                
                // H: 线性系数K
                document->write(currentRow, 8, sensor.params_k, currentFormat);
                
                // I: 线性系数B
                document->write(currentRow, 9, sensor.params_b, currentFormat);
                
                // J: 精度
                document->write(currentRow, 10, sensor.params_precision, currentFormat);
                
                // K: 极性（存储数字值）
                document->write(currentRow, 11, sensor.params_polarity, currentFormat);
                
                // L: 测量单位类型
                document->write(currentRow, 12, sensor.meas_unit, currentFormat);
                
                // M: 测量范围最小值
                document->write(currentRow, 13, sensor.meas_range_min, currentFormat);
                
                // N: 测量范围最大值
                document->write(currentRow, 14, sensor.meas_range_max, currentFormat);
                
                // O: 输出信号单位类型
                document->write(currentRow, 15, sensor.output_signal_unit, currentFormat);
                
                // P: 输出信号范围最小值
                document->write(currentRow, 16, sensor.output_signal_range_min, currentFormat);
                
                // Q: 输出信号范围最大值
                document->write(currentRow, 17, sensor.output_signal_range_max, currentFormat);
                
                currentRow++;
            }
        }
        
        // 自动调整列宽
        for (int col = 1; col <= 17; ++col) {
            document->setColumnWidth(col, 15.0); // 设置列宽为15个字符
        }
        
        // 特殊列宽调整
        document->setColumnWidth(2, 20.0);  // 传感器组名称列稍宽
        document->setColumnWidth(4, 18.0);  // 传感器序列号列稍宽
        document->setColumnWidth(5, 16.0);  // 传感器型号列稍宽
        
        qDebug() << "SensorExcelExtensions_1_2: 成功创建传感器详细配置工作表，共" << (currentRow - 3) << "行数据";
        return true;
        
    } catch (const std::exception& e) {
        setError(QString(u8"创建工作表时发生异常: %1").arg(e.what()));
        return false;
    } catch (...) {
        setError(u8"创建工作表时发生未知异常");
        return false;
    }
}

bool SensorExcelExtensions_1_2::readSensorDetailWorksheet(QXlsx::Document* document, QList<UI::SensorGroup_1_2>& sensorGroups) {
    if (!document) {
        setError(u8"Excel文档指针为空");
        return false;
    }
    
    try {
        // 选择传感器详细配置工作表
        QString sheetName = u8"传感器详细配置";
        if (!document->selectSheet(sheetName)) {
            setError(QString(u8"找不到工作表: %1").arg(sheetName));
            return false;
        }
        
        sensorGroups.clear();
        QMap<int, UI::SensorGroup_1_2> groupMap; // 组序号 -> 传感器组
        
        // 从第3行开始读取数据 (第1行是标题，第2行是表头)
        int row = 3;
        while (true) {
            // 检查A列是否为空，如果为空则结束读取
            QVariant groupIdVariant = document->read(row, 1);
            if (!groupIdVariant.isValid() || groupIdVariant.toString().isEmpty()) {
                break;
            }
            
            try {
                // 读取组信息
                int groupId = groupIdVariant.toInt();
                QString groupName = document->read(row, 2).toString();
                
                // 读取传感器数据
                UI::SensorParams_1_2 sensor;
                
                // C: 传感器ID
                sensor.sensorId = document->read(row, 3).toInt();
                
                // D: 传感器序列号
                sensor.params_sn = document->read(row, 4).toString();
                
                // 注释掉序列号自动转换功能（按用户要求）
                /*
                // 🆕 验证传感器序列号格式
                if (!sensor.params_sn.isEmpty()) {
                    bool isNumericOnly = true;
                    for (const QChar& ch : sensor.params_sn) {
                        if (!ch.isDigit()) {
                            isNumericOnly = false;
                            break;
                        }
                    }
                    
                    if (isNumericOnly) {
                        qWarning() << QString(u8"⚠️ 警告：传感器序列号 '%1' 是纯数字格式，建议使用有意义的序列号格式，如'SEN%2'").arg(sensor.params_sn).arg(sensor.params_sn.rightJustified(3, '0'));
                        // 自动转换为建议格式
                        QString suggestedSerialNumber = QString("SEN%1").arg(sensor.params_sn.rightJustified(3, '0'));
                        qDebug() << QString(u8"🔄 自动转换：序列号 '%1' → '%2'").arg(sensor.params_sn).arg(suggestedSerialNumber);
                        sensor.params_sn = suggestedSerialNumber;
                    }
                }
                */
                
                // E: 传感器型号
                sensor.params_model = document->read(row, 5).toString();
                
                // F: 零点偏移
                sensor.zero_offset = document->read(row, 6).toDouble();
                
                // G: 启用状态
                QString enableText = document->read(row, 7).toString();
                sensor.enable = (enableText == u8"是" || enableText.toLower() == "true" || enableText == "1");
                
                // H: 线性系数K
                sensor.params_k = document->read(row, 8).toDouble();
                
                // I: 线性系数B
                sensor.params_b = document->read(row, 9).toDouble();
                
                // J: 精度
                sensor.params_precision = document->read(row, 10).toDouble();
                
                // K: 极性（支持新格式数字和旧格式字符串）
                QVariant polarityValue = document->read(row, 11);
                if (polarityValue.type() == QVariant::Int) {
                    sensor.params_polarity = polarityValue.toInt();
                } else {
                    // 处理旧格式字符串
                    QString polarityStr = polarityValue.toString().trimmed();
                    if (polarityStr == "1" || polarityStr.contains(u8"正极性")) {
                        sensor.params_polarity = 1;
                    } else if (polarityStr == "-1" || polarityStr.contains(u8"负极性")) {
                        sensor.params_polarity = -1;
                    } else if (polarityStr == "9" || polarityStr.contains(u8"双极性")) {
                        sensor.params_polarity = 9;
                    } else if (polarityStr == "0" || polarityStr.contains(u8"未知")) {
                        sensor.params_polarity = 0;
                    } else {
                        sensor.params_polarity = 1; // 默认正极性
                    }
                }
                
                // L: 测量单位类型
                sensor.meas_unit = document->read(row, 12).toInt();
                
                // M: 测量范围最小值
                sensor.meas_range_min = document->read(row, 13).toDouble();
                
                // N: 测量范围最大值
                sensor.meas_range_max = document->read(row, 14).toDouble();
                
                // O: 输出信号单位类型
                sensor.output_signal_unit = document->read(row, 15).toInt();
                
                // P: 输出信号范围最小值
                sensor.output_signal_range_min = document->read(row, 16).toDouble();
                
                // Q: 输出信号范围最大值
                sensor.output_signal_range_max = document->read(row, 17).toDouble();
                
                // 📝 添加调试输出：追踪传感器序列号读取
                qDebug() << QString("SensorExcelExtensions_1_2: 第%1行 - 传感器序列号='%2', 型号='%3'")
                           .arg(row)
                           .arg(sensor.params_sn)
                           .arg(sensor.params_model);
                
                // 查找或创建传感器组
                if (!groupMap.contains(groupId)) {
                    UI::SensorGroup_1_2 newGroup;
                    newGroup.groupId = groupId;
                    newGroup.groupName = groupName;
                    newGroup.groupType = u8"传感器组"; // 默认组类型
                    newGroup.createTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
                    groupMap[groupId] = newGroup;
                }
                
                // 将传感器添加到对应的组
                groupMap[groupId].sensors.append(sensor);
                
                row++;
                
            } catch (const std::exception& e) {
                qWarning() << "SensorExcelExtensions_1_2: 读取第" << row << "行数据时发生错误:" << e.what();
                row++; // 跳过错误行继续处理
                continue;
            }
        }
        
        // 将组数据转换为列表
        for (auto it = groupMap.begin(); it != groupMap.end(); ++it) {
            sensorGroups.append(it.value());
        }
        
        // 按组ID排序
        std::sort(sensorGroups.begin(), sensorGroups.end(), 
                  [](const UI::SensorGroup_1_2& a, const UI::SensorGroup_1_2& b) {
                      return a.groupId < b.groupId;
                  });
        
        qDebug() << "SensorExcelExtensions_1_2: 成功读取传感器详细配置，共" << sensorGroups.size() << "个组，" << (row - 3) << "个传感器";
        return true;
        
    } catch (const std::exception& e) {
        setError(QString(u8"读取工作表时发生异常: %1").arg(e.what()));
        return false;
    } catch (...) {
        setError(u8"读取工作表时发生未知异常");
        return false;
    }
}

bool SensorExcelExtensions_1_2::createEmptyTemplate(const QString& filePath) {
    clearError();
    
    try {
        // 创建Excel文档
        QXlsx::Document document;
        
        // 创建空的传感器详细配置工作表（只有表头）
        QList<UI::SensorGroup_1_2> emptyGroups; // 空的组列表
        if (!createSensorDetailWorksheet(&document, emptyGroups)) {
            return false;
        }
        
        // 保存文件
        if (!document.saveAs(filePath)) {
            setError(QString(u8"保存模板文件失败: %1").arg(filePath));
            return false;
        }
        
        qDebug() << "SensorExcelExtensions_1_2: 成功创建空模板文件" << filePath;
        return true;
        
    } catch (const std::exception& e) {
        setError(QString(u8"创建模板时发生异常: %1").arg(e.what()));
        return false;
    } catch (...) {
        setError(u8"创建模板时发生未知异常");
        return false;
    }
}

QString SensorExcelExtensions_1_2::getLastError() {
    return lastError_;
}

void SensorExcelExtensions_1_2::setError(const QString& error) {
    lastError_ = error;
    qDebug() << "SensorExcelExtensions_1_2 Error:" << error;
}

void SensorExcelExtensions_1_2::clearError() {
    lastError_.clear();
}