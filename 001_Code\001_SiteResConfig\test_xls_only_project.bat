@echo off
chcp 65001 >nul
echo ========================================
echo  XLS专用工程管理功能测试
echo ========================================
echo.

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile* >nul 2>&1
if exist "debug" rmdir /s /q debug >nul 2>&1
if exist "release" rmdir /s /q release >nul 2>&1
if exist "*.o" del *.o >nul 2>&1
if exist "ui_*.h" del ui_*.h >nul 2>&1

echo.
echo 检查XLS专用工程管理功能修改...
echo.
echo 1. 检查新建工程功能修改...
findstr /C:".xlsx" "src\MainWindow_Qt_Simple.cpp" | findstr /C:"defaultFileName" >nul
if errorlevel 1 (
    echo ❌ 新建工程未修改为XLS格式
    goto :error
) else (
    echo ✅ 新建工程已修改为XLS格式
)

echo.
echo 2. 检查打开工程功能修改...
findstr /C:"Excel工程文件" "src\MainWindow_Qt_Simple.cpp" >nul
if errorlevel 1 (
    echo ❌ 打开工程未修改为XLS格式
    goto :error
) else (
    echo ✅ 打开工程已修改为XLS格式
)

echo.
echo 3. 检查保存工程功能修改...
findstr /C:"SaveProjectToXLS" "src\MainWindow_Qt_Simple.cpp" >nul
if errorlevel 1 (
    echo ❌ 保存工程未添加XLS支持
    goto :error
) else (
    echo ✅ 保存工程已添加XLS支持
)

echo.
echo 4. 检查XLS方法声明...
findstr /C:"SaveProjectToXLS" "include\MainWindow_Qt_Simple.h" >nul
if errorlevel 1 (
    echo ❌ 头文件未声明XLS保存方法
    goto :error
) else (
    echo ✅ 头文件已声明XLS保存方法
)

findstr /C:"LoadProjectFromXLS" "include\MainWindow_Qt_Simple.h" >nul
if errorlevel 1 (
    echo ❌ 头文件未声明XLS加载方法
    goto :error
) else (
    echo ✅ 头文件已声明XLS加载方法
)

echo.
echo 5. 检查XLS方法实现...
findstr /C:"bool CMyMainWindow::SaveProjectToXLS" "src\MainWindow_Qt_Simple.cpp" >nul
if errorlevel 1 (
    echo ❌ 源文件未实现XLS保存方法
    goto :error
) else (
    echo ✅ 源文件已实现XLS保存方法
)

findstr /C:"bool CMyMainWindow::LoadProjectFromXLS" "src\MainWindow_Qt_Simple.cpp" >nul
if errorlevel 1 (
    echo ❌ 源文件未实现XLS加载方法
    goto :error
) else (
    echo ✅ 源文件已实现XLS加载方法
)

echo.
echo 生成UI头文件...
uic ui\MainWindow.ui -o ui_MainWindow.h >nul 2>&1
if errorlevel 1 (
    echo ❌ UI文件生成失败
    goto :error
) else (
    echo ✅ UI头文件生成成功
)

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++ >nul 2>&1
if errorlevel 1 (
    echo ❌ qmake失败
    goto :error
) else (
    echo ✅ Makefile生成成功
)

echo.
echo 开始编译测试...
echo 这可能需要几分钟时间，请耐心等待...
echo.

mingw32-make clean >nul 2>&1
mingw32-make -j4 2>compile_errors.txt
if errorlevel 1 (
    echo ❌ 编译失败！
    echo.
    echo 编译错误信息：
    type compile_errors.txt
    echo.
    echo 请检查上述错误信息并进行修复。
    pause
    exit /b 1
) else (
    echo ✅ 编译成功！
    
    if exist compile_errors.txt del compile_errors.txt >nul 2>&1
    
    echo.
    echo ========================================
    echo  🎉 XLS专用工程管理功能编译成功！
    echo ========================================
    
    if exist "SiteResConfig.exe" (
        echo.
        echo 📊 编译结果:
        echo ├─ 可执行文件: SiteResConfig.exe
        echo ├─ 文件大小: 
        for %%F in (SiteResConfig.exe) do echo │  └─ %%~zF 字节
        echo └─ 修改时间: 
        for %%F in (SiteResConfig.exe) do echo    └─ %%~tF
        echo.
        echo 🎯 XLS专用工程管理功能:
        echo.
        echo 📁 新建工程功能:
        echo ├─ ✅ 只支持Excel格式 (.xlsx)
        echo ├─ ✅ 默认文件扩展名为 .xlsx
        echo ├─ ✅ 文件对话框过滤器: "Excel文件 (*.xlsx)"
        echo └─ ✅ 自动保存为XLS格式
        echo.
        echo 📂 打开工程功能:
        echo ├─ ✅ 只支持Excel格式 (.xlsx, .xls)
        echo ├─ ✅ 文件对话框过滤器: "Excel工程文件 (*.xlsx *.xls)"
        echo ├─ ✅ 格式验证和错误提示
        echo └─ ✅ 调用LoadProjectFromXLS方法
        echo.
        echo 💾 保存工程功能:
        echo ├─ ✅ 只支持Excel格式保存
        echo ├─ ✅ 默认扩展名为 .xlsx
        echo ├─ ✅ 调用SaveProjectToXLS方法
        echo └─ ✅ 自动更新项目路径
        echo.
        echo 🔧 XLS方法实现:
        echo ├─ ✅ SaveProjectToXLS: 使用XLS导出器保存完整项目
        echo ├─ ✅ LoadProjectFromXLS: 从Excel文件导入项目数据
        echo ├─ ✅ 完整的错误处理和日志记录
        echo ├─ ✅ 自动创建项目对象和设置基本信息
        echo └─ ✅ 界面更新和窗口标题设置
        echo.
        echo 🎨 用户体验特性:
        echo ├─ ✅ 智能提示保存现有数据
        echo ├─ ✅ 自动清空界面数据
        echo ├─ ✅ 专业的Excel文件格式
        echo ├─ ✅ 详细的操作日志记录
        echo └─ ✅ 友好的错误提示信息
        echo.
        echo 🚀 使用方法:
        echo.
        echo 1. 新建工程:
        echo    ├─ 文件 → 新建工程
        echo    ├─ 选择保存位置
        echo    ├─ 输入文件名 (自动添加.xlsx扩展名)
        echo    └─ 程序自动创建并保存Excel格式工程文件
        echo.
        echo 2. 打开工程:
        echo    ├─ 文件 → 打开工程
        echo    ├─ 选择Excel工程文件 (.xlsx 或 .xls)
        echo    └─ 程序自动导入配置并更新界面
        echo.
        echo 3. 保存工程:
        echo    ├─ 文件 → 保存工程
        echo    ├─ 如果是新工程，选择保存位置
        echo    └─ 程序自动保存为Excel格式
        echo.
        
        set /p choice="是否启动程序测试XLS专用工程管理功能？(Y/N): "
        if /i "%choice%"=="Y" (
            echo 启动程序...
            start SiteResConfig.exe
        )
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
        set /p choice="是否启动程序测试？(Y/N): "
        if /i "%choice%"=="Y" start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
        set /p choice="是否启动程序测试？(Y/N): "
        if /i "%choice%"=="Y" start release\SiteResConfig.exe
    ) else (
        echo ⚠️ 警告: 找不到可执行文件
    )
)

echo.
echo 📖 XLS专用工程管理说明:
echo.
echo 🎯 核心特性:
echo - 完全基于Excel格式的工程文件管理
echo - 统一的文件格式，便于数据交换和备份
echo - 专业的Excel样式和格式
echo - 完整的项目数据保存和加载
echo.
echo 🔧 技术优势:
echo - 利用现有的XLS导出器基础设施
echo - 完整的错误处理和异常管理
echo - 自动的界面更新和状态管理
echo - 详细的操作日志和用户反馈
echo.
echo 测试完成！
pause
exit /b 0

:error
echo.
echo ❌ XLS专用工程管理功能测试失败！
echo.
echo 可能的问题：
echo 1. 代码修改不完整
echo 2. 方法声明或实现缺失
echo 3. 编译环境问题
echo.
echo 请检查错误信息并重新修复。
pause
exit /b 1
