/**
 * @file test_sensor_dialog.cpp
 * @brief 传感器对话框测试程序
 * @details 用于测试改进后的传感器对话框功能
 * <AUTHOR> Assistant
 * @date 2025-08-06
 * @version 1.0.0
 */

#include <QtWidgets/QApplication>
#include <QtWidgets/QMessageBox>
#include "include/SensorDialog_1_2.h"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // 创建传感器对话框
    UI::SensorDialog_1_2 dialog("传感器组", "传感器_000001");
    
    // 显示对话框
    if (dialog.exec() == QDialog::Accepted) {
        // 获取传感器参数
        UI::SensorParams_1_2 params = dialog.getSensorParams();
        
        // 显示结果
        QString result = QString(u8"传感器参数:\n"
                               u8"序列号: %1\n"
                               u8"类型: %2\n"
                               u8"型号: %3\n"
                               u8"量程: %4\n"
                               u8"单位: %5\n"
                               u8"灵敏度: %6\n"
                               u8"校准日期: %7\n"
                               u8"执行人: %8\n"
                                                             u8"极性: %9\n"
                              u8"正向反馈系数: %10\n"
                              u8"负向反馈系数: %11\n"
                               u8"激励电压: %12V\n"
                               u8"满量程最大值: %13\n"
                               u8"允许分别设置最小最大值: %14\n"
                               u8"满量程最小值: %15\n\n"
                               u8"注意: 所有字段都是可选的，可以为空。")
                        .arg(params.serialNumber)
                        .arg(params.sensorType)
                        .arg(params.model)
                        .arg(params.range)
                        .arg(params.unit)
                        .arg(params.sensitivity)
                        .arg(params.calibrationDate)
                        .arg(params.performedBy)
                        .arg(params.isPositive ? u8"正极性 (+)" : u8"负极性 (-)")
                        .arg(params.positiveFeedback)
                        .arg(params.negativeFeedback)
                        .arg(params.excitationVoltage)
                        .arg(params.fullScaleMax)
                        .arg(params.allowSeparateMinMax ? u8"是" : u8"否")
                        .arg(params.fullScaleMin);
        
        QMessageBox::information(nullptr, u8"传感器参数", result);
    }
    
    return 0;
}
