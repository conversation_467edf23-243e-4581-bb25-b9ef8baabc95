@echo off
echo ========================================
echo  Qt Creator 编译速度优化
echo ========================================

echo 1. 清理构建目录...
if exist "build-*" (
    echo 删除旧的构建目录...
    rmdir /s /q build-*
    echo 构建目录已清理
) else (
    echo 没有找到构建目录
)

echo.
echo 2. 清理项目临时文件...
cd SiteResConfig
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o
if exist "ui_*.h" del ui_*.h
if exist "moc_*.cpp" del moc_*.cpp
if exist "moc_*.h" del moc_*.h
echo 临时文件已清理

echo.
echo 3. 检查系统资源...
echo CPU核心数:
wmic cpu get NumberOfCores /value | find "NumberOfCores"
echo.
echo 可用内存:
wmic OS get TotalVisibleMemorySize,FreePhysicalMemory /value | find "="

echo.
echo ========================================
echo  优化建议
echo ========================================
echo.
echo 🚀 Qt Creator设置优化:
echo ├─ 右键项目 → 构建设置
echo ├─ Make参数中添加: -j4 (根据CPU核心数调整)
echo ├─ 工具 → 选项 → C++ → 减少实时代码分析
echo └─ 工具 → 选项 → 构建和运行 → 设置并行作业数
echo.
echo 💾 系统优化:
echo ├─ 将项目移到SSD硬盘 (如果使用机械硬盘)
echo ├─ 关闭杀毒软件对项目目录的实时扫描
echo ├─ 增加虚拟内存设置
echo └─ 关闭不必要的后台程序
echo.
echo 🔧 项目优化 (已应用):
echo ├─ 编译器优化: CONFIG += optimize_full
echo ├─ Debug模式: 减少调试信息级别
echo ├─ Release模式: 启用 -O2 优化
echo └─ 禁用调试输出: QT_NO_DEBUG_OUTPUT
echo.
echo 📊 性能监控:
echo ├─ 使用任务管理器监控CPU和内存使用
echo ├─ 编译时间应该 < 30秒 (小项目)
echo ├─ CPU使用率应该 > 80% (充分利用多核)
echo └─ 避免内存使用超过物理内存
echo.

cd ..
pause
