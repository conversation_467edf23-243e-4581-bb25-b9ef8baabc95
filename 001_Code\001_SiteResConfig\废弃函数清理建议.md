# 废弃函数清理建议

## 🎯 需要清理的函数

### 1. CollectCompleteTreeData()
### 2. ConvertTreeItemToJson()

## ❌ **废弃原因**

### 1. 违反架构原则
```cpp
// ❌ 错误：直接从界面控件获取数据
QJsonObject CMyMainWindow::CollectCompleteTreeData() {
    if (ui->hardwareTreeWidget) {  // 违反原则：使用界面控件
        for (int i = 0; i < ui->hardwareTreeWidget->topLevelItemCount(); ++i) {
            // 从界面控件收集数据
        }
    }
}
```

**违反的核心原则**：
> Excel导出，JSON导出，不使用界面、控件，使用内存数据，必须遵守这个原则

### 2. 未被使用
- ✅ **SaveProjectToJSON** - 使用JSONDataExporter，不调用这些函数
- ✅ **JSONDataExporter** - 完全基于DataManager，不使用这些函数
- ❌ **CollectCompleteTreeData** - 只在文档中被提及，实际未被调用

### 3. 架构已更新
```cpp
// ✅ 当前正确的JSON导出架构
bool CMyMainWindow::SaveProjectToJSON(const QString& filePath) {
    // 使用JSONDataExporter，基于DataManager
    bool directSuccess = jsonDataExporter_->exportCompleteProject(filePath);
}

// ✅ JSONDataExporter内部实现
JSONDataExporter::ProjectData JSONDataExporter::collectProjectData() {
    // 完全基于DataManager获取数据
    if (xlsExporter_->getSensorDataManager()) {
        data.sensorGroups = xlsExporter_->getSensorDataManager()->getAllSensorGroups();
    }
    if (xlsExporter_->getActuatorDataManager()) {
        data.actuatorGroups = xlsExporter_->getActuatorDataManager()->getAllActuatorGroups();
    }
}
```

## 🔧 **清理方案**

### 方案1：完全删除（推荐）
```cpp
// 从MainWindow_Qt_Simple.h中删除声明
// 从MainWindow_Qt_Simple.cpp中删除实现
```

**优点**：
- 彻底清理废弃代码
- 避免误用
- 减少代码维护负担

**风险**：
- 如果有隐藏的调用会导致编译错误

### 方案2：标记为废弃
```cpp
// 在头文件中标记
/**
 * @brief Collect complete tree data from UI
 * @deprecated 此方法已废弃，违反内存数据原则，请使用JSONDataExporter
 * @return JSON object containing all tree widget data
 */
[[deprecated("使用JSONDataExporter替代")]]
QJsonObject CollectCompleteTreeData();
```

**优点**：
- 保持向后兼容
- 编译器会发出警告

**缺点**：
- 仍然保留违反原则的代码

### 方案3：重构为内存数据版本
```cpp
// 重构为基于DataManager的版本
QJsonObject CollectCompleteDataFromManagers() {
    QJsonObject data;
    
    // 从DataManager获取数据，不使用界面控件
    if (actuatorViewModel1_2_) {
        data["actuators"] = actuatorViewModel1_2_->exportToJSONString();
    }
    if (sensorDataManager_) {
        // 添加传感器数据
    }
    
    return data;
}
```

## 📋 **推荐的清理步骤**

### 第1步：确认无调用
```bash
# 搜索所有可能的调用
grep -r "CollectCompleteTreeData" .
grep -r "ConvertTreeItemToJson" .
```

### 第2步：删除声明
从`MainWindow_Qt_Simple.h`中删除：
```cpp
QJsonObject CollectCompleteTreeData();
QJsonObject ConvertTreeItemToJson(QTreeWidgetItem* item);
```

### 第3步：删除实现
从`MainWindow_Qt_Simple.cpp`中删除对应的实现代码（第1503-1571行）

### 第4步：编译验证
```bash
qmake SiteResConfig_Simple.pro
make
```

### 第5步：功能测试
- 测试JSON导出功能
- 测试Excel导出功能
- 确保所有导出都基于内存数据

## ✅ **替代方案**

### 当前正确的数据获取方式

#### 1. 获取作动器数据
```cpp
// ✅ 正确：从DataManager获取
if (actuatorViewModel1_2_) {
    QList<UI::ActuatorGroup> groups = actuatorViewModel1_2_->getAllActuatorGroups();
    QList<UI::ActuatorParams> actuators = actuatorViewModel1_2_->getAllActuators();
}
```

#### 2. 获取传感器数据
```cpp
// ✅ 正确：从DataManager获取
if (sensorDataManager_) {
    QList<UI::SensorGroup> groups = sensorDataManager_->getAllSensorGroups();
    QList<UI::SensorParams> sensors = sensorDataManager_->getAllSensors();
}
```

#### 3. JSON导出
```cpp
// ✅ 正确：使用JSONDataExporter
bool success = jsonDataExporter_->exportCompleteProject(filePath);
```

#### 4. Excel导出
```cpp
// ✅ 正确：使用XLSDataExporter
bool success = xlsDataExporter_->exportCompleteProject(filePath);
```

## 🎯 **清理后的架构优势**

### 1. 架构一致性
- 所有导出功能都基于内存数据
- 不再有违反原则的代码

### 2. 代码简洁性
- 移除未使用的废弃代码
- 减少维护负担

### 3. 避免误用
- 防止开发者误用违反原则的方法
- 强制使用正确的数据获取方式

## 🚨 **注意事项**

### 1. 备份代码
在删除前备份当前版本，以防需要回滚

### 2. 全面测试
删除后进行完整的功能测试，确保没有破坏现有功能

### 3. 文档更新
更新相关文档，移除对这些函数的引用

## 🎉 **总结**

**CollectCompleteTreeData** 和 **ConvertTreeItemToJson** 这两个函数：

1. ❌ **已废弃** - 违反内存数据原则
2. ❌ **未被使用** - 当前代码中没有调用
3. ❌ **架构过时** - 被更好的DataManager架构替代

**建议：立即删除这两个函数**，以保持代码的整洁性和架构的一致性。
