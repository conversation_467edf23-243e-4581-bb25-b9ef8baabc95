/**
 * @file simple_actuator_test.cpp
 * @brief 简化的作动器Excel测试程序
 * @details 测试作动器数据的Excel导入导出功能
 * <AUTHOR> Assistant
 * @date 2025-08-14
 * @version 1.0.0
 */

#include <iostream>
#include <fstream>
#include <string>
#include <vector>

using namespace std;

/**
 * @brief 创建作动器示例Excel文件（CSV格式）
 * @param filename 文件名
 * @return 创建是否成功
 */
bool createSampleActuatorCSV(const string& filename) {
    ofstream file(filename.c_str());
    if (!file.is_open()) {
        cout << "❌ 无法创建文件: " << filename << endl;
        return false;
    }
    
    // 写入CSV表头
    file << "组序号,作动器组名称,作动器序号,作动器序列号,作动器类型,Unit类型,Unit名称,行程(m),位移(m),拉伸面积(m²),压缩面积(m²),极性,Deliver(V),频率(Hz),输出倍数,平衡(V),备注\n";
    
    // 写入示例数据
    file << "1,主作动器组,1,ACT001,单出杆,m,米,0.15,0.05,0.0314,0.0254,Positive,5.0,50.0,1.0,2.5,主控制作动器\n";
    file << "1,,2,ACT002,单出杆,m,米,0.15,0.05,0.0314,0.0254,Positive,5.0,50.0,1.0,2.5,备用作动器\n";
    file << "2,辅助作动器组,1,ACT003,双出杆,mm,毫米,100.0,30.0,0.0201,0.0154,Negative,3.5,25.0,0.8,1.8,辅助控制\n";
    file << "2,,2,ACT004,单出杆,cm,厘米,20.0,8.0,0.0283,0.0226,Positive,4.2,40.0,1.2,2.1,精密控制\n";
    
    file.close();
    cout << "✅ 创建作动器CSV文件成功: " << filename << endl;
    return true;
}

/**
 * @brief 读取并验证作动器CSV文件
 * @param filename 文件名
 * @return 验证是否成功
 */
bool validateActuatorCSV(const string& filename) {
    ifstream file(filename.c_str());
    if (!file.is_open()) {
        cout << "❌ 无法打开文件: " << filename << endl;
        return false;
    }
    
    string line;
    int lineCount = 0;
    int dataRows = 0;
    
    cout << "🔍 验证作动器CSV文件内容..." << endl;
    
    while (getline(file, line)) {
        lineCount++;
        
        if (lineCount == 1) {
            // 验证表头
            if (line.find("组序号") != string::npos && 
                line.find("作动器组名称") != string::npos &&
                line.find("作动器序列号") != string::npos) {
                cout << "✅ 表头格式正确" << endl;
            } else {
                cout << "❌ 表头格式错误" << endl;
                return false;
            }
        } else {
            // 验证数据行
            if (!line.empty()) {
                dataRows++;
                
                // 简单验证：检查是否包含必要字段
                if (line.find("ACT") != string::npos) {
                    cout << "✅ 数据行 " << dataRows << ": " << line.substr(0, 50) << "..." << endl;
                } else {
                    cout << "❌ 数据行 " << dataRows << " 格式错误" << endl;
                    return false;
                }
            }
        }
    }
    
    file.close();
    
    cout << "📊 文件验证结果:" << endl;
    cout << "  - 总行数: " << lineCount << endl;
    cout << "  - 数据行数: " << dataRows << endl;
    cout << "  - 预期数据行数: 4" << endl;
    
    if (dataRows == 4) {
        cout << "✅ 作动器CSV文件验证成功" << endl;
        return true;
    } else {
        cout << "❌ 数据行数不匹配" << endl;
        return false;
    }
}

/**
 * @brief 创建作动器Excel格式说明文件
 * @param filename 文件名
 * @return 创建是否成功
 */
bool createActuatorFormatGuide(const string& filename) {
    ofstream file(filename.c_str());
    if (!file.is_open()) {
        cout << "❌ 无法创建格式说明文件: " << filename << endl;
        return false;
    }
    
    file << "# 作动器Excel存储格式说明\n\n";
    file << "## 工作表名称\n";
    file << "- 中文名称: 作动器\n";
    file << "- 英文名称: Actuators\n\n";
    
    file << "## 表头信息区域 (第1-4行)\n";
    file << "A1: 作动器配置数据表\n";
    file << "A2: 导出时间: 2025-08-14 15:30:00\n";
    file << "A3: 说明: 包含作动器组及其作动器的完整配置信息\n";
    file << "A4: (空行)\n\n";
    
    file << "## 数据表头 (第5行) - 17列完整格式\n";
    file << "A5: 组序号 | B5: 作动器组名称 | C5: 作动器序号 | D5: 作动器序列号\n";
    file << "E5: 作动器类型 | F5: Unit类型 | G5: Unit名称 | H5: 行程(m)\n";
    file << "I5: 位移(m) | J5: 拉伸面积(m²) | K5: 压缩面积(m²) | L5: 极性\n";
    file << "M5: Deliver(V) | N5: 频率(Hz) | O5: 输出倍数 | P5: 平衡(V) | Q5: 备注\n\n";
    
    file << "## 示例数据 (第6-9行)\n";
    file << "A6: 1 | B6: 主作动器组 | C6: 1 | D6: ACT001 | E6: 单出杆\n";
    file << "F6: m | G6: 米 | H6: 0.15 | I6: 0.05 | J6: 0.0314\n";
    file << "K6: 0.0254 | L6: Positive | M6: 5.0 | N6: 50.0 | O6: 1.0\n";
    file << "P6: 2.5 | Q6: 主控制作动器\n\n";
    
    file << "## 数据验证规则\n";
    file << "1. 组序号: 必须为正整数，同组内相同\n";
    file << "2. 作动器组名称: 只在组内第一个作动器行显示\n";
    file << "3. 作动器序号: 组内唯一，从1开始递增\n";
    file << "4. 作动器序列号: 全局唯一标识符\n";
    file << "5. 作动器类型: 单出杆/双出杆\n";
    file << "6. Unit类型: m/mm/cm/inch\n";
    file << "7. 极性: Positive/Negative\n\n";
    
    file.close();
    cout << "✅ 创建格式说明文件成功: " << filename << endl;
    return true;
}

int main() {
    cout << "========================================" << endl;
    cout << "🚀 作动器Excel功能简化测试程序" << endl;
    cout << "========================================" << endl;
    cout << endl;
    
    // 1. 创建示例作动器CSV文件
    cout << "1. 创建示例作动器CSV文件..." << endl;
    if (!createSampleActuatorCSV("作动器示例数据.csv")) {
        cout << "❌ 创建CSV文件失败" << endl;
        return 1;
    }
    cout << endl;
    
    // 2. 验证CSV文件内容
    cout << "2. 验证CSV文件内容..." << endl;
    if (!validateActuatorCSV("作动器示例数据.csv")) {
        cout << "❌ CSV文件验证失败" << endl;
        return 1;
    }
    cout << endl;
    
    // 3. 创建格式说明文件
    cout << "3. 创建格式说明文件..." << endl;
    if (!createActuatorFormatGuide("作动器Excel格式说明.md")) {
        cout << "❌ 创建格式说明文件失败" << endl;
        return 1;
    }
    cout << endl;
    
    cout << "========================================" << endl;
    cout << "🎉 作动器Excel功能测试完成！" << endl;
    cout << "========================================" << endl;
    cout << endl;
    
    cout << "📁 生成的文件:" << endl;
    cout << "  - 作动器示例数据.csv (示例数据)" << endl;
    cout << "  - 作动器Excel格式说明.md (格式说明)" << endl;
    cout << endl;
    
    cout << "🎯 下一步操作建议:" << endl;
    cout << "  1. 将CSV文件导入Excel，另存为.xlsx格式" << endl;
    cout << "  2. 在Excel中创建'作动器'工作表" << endl;
    cout << "  3. 按照17列格式整理数据" << endl;
    cout << "  4. 测试系统的Excel导入功能" << endl;
    cout << endl;
    
    return 0;
}
