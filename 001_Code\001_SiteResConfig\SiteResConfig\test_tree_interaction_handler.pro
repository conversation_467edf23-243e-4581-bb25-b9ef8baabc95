QT += core widgets

TARGET = test_tree_interaction_handler
TEMPLATE = app

CONFIG += debug

# 包含路径
INCLUDEPATH += include

# 源文件
SOURCES += test_tree_interaction_handler.cpp \
           src/TreeInteractionHandler.cpp \
           src/DetailInfoPanel.cpp \
           src/BasicInfoWidget.cpp

# 头文件
HEADERS += include/TreeInteractionHandler.h \
           include/DetailInfoPanel.h \
           include/BasicInfoWidget.h \
           include/DataModels_Fixed.h

# 编译选项
DEFINES += QT_DEPRECATED_WARNINGS

# 调试信息
CONFIG += debug
CONFIG += console

# 输出目录
DESTDIR = bin
OBJECTS_DIR = debug
MOC_DIR = debug
RCC_DIR = debug
UI_DIR = debug 