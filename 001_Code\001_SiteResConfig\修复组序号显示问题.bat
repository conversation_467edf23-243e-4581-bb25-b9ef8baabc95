@echo off
chcp 65001 >nul
echo.
echo ========================================
echo ✅ 修复传感器组序号显示问题
echo ========================================
echo.

echo 🎯 问题分析:
echo 从最新截图可以看到：
echo ✅ 组名称显示已正确：
echo    - 载荷_传感器组：第1行显示，第2行空白
echo    - 位置_传感器组：第3行显示，第4、5、6行空白
echo.
echo ❌ 组序号显示不正确：
echo    - 当前显示：1, 2, 3, 4, 5, 6（每行递增）
echo    - 应该显示：1, 1, 2, 2, 2, 2（同组相同序号）
echo.

echo 🔍 问题根源:
echo 组序号使用了内部的groupId（哈希生成的大数字）
echo 而不是用户期望的显示序号（1, 2, 3...）
echo.

echo 🔧 修复内容:
echo.
echo 1. 修改导出循环逻辑:
echo    修复前:
echo      for (const UI::SensorGroup& group : sensorGroups) {
echo          addSensorGroupDetailToExcel(worksheet, group, row);
echo      }
echo.
echo    修复后:
echo      for (int groupIndex = 0; groupIndex ^< sensorGroups.size(); ++groupIndex) {
echo          const UI::SensorGroup& group = sensorGroups[groupIndex];
echo          int displayGroupId = groupIndex + 1;  // 显示序号从1开始
echo          addSensorGroupDetailToExcel(worksheet, group, row, displayGroupId);
echo      }
echo.
echo 2. 修改addSensorGroupDetailToExcel方法:
echo    - 添加displayGroupId参数
echo    - 使用displayGroupId而不是group.groupId作为组序号
echo    - 支持向后兼容（默认参数-1时使用原来的groupId）
echo.
echo 3. 更新方法签名:
echo    int addSensorGroupDetailToExcel(worksheet, group, row, displayGroupId = -1);
echo.

echo 📊 预期修复效果:
echo.
echo 修复后应该显示为:
echo 组序号 ^| 传感器组名称    ^| 传感器序列号      ^| 传感器类型 ^| ...
echo ------|---------------|-----------------|----------|----
echo 1     ^| 载荷_传感器组   ^| 传感器_000002    ^| Axial Gage ^| ...
echo 1     ^|               ^| 传感器_000003    ^| Axial Gage ^| ...  ← 同组序号
echo 2     ^| 位置_传感器组   ^| 传感器_000003    ^| Axial Gage ^| ...
echo 2     ^|               ^| 传感器_000002    ^| Axial Gage ^| ...  ← 同组序号
echo 2     ^|               ^| 传感器_000001    ^| Axial Gage ^| ...  ← 同组序号
echo 2     ^|               ^| 传感器_000001    ^| Axial Gage ^| ...  ← 同组序号
echo.

echo 🚀 测试步骤:
echo.
echo 步骤1: 重新编译应用程序
echo - 确保修复的组序号逻辑生效
echo.
echo 步骤2: 导出传感器详细配置
echo - 使用现有的传感器组数据
echo - 不需要重新创建数据
echo.
echo 步骤3: 验证Excel结果
echo - 检查组序号列（第1列）
echo - 确认同组传感器有相同的组序号
echo - 确认不同组有不同的组序号
echo.

echo ✅ 预期结果验证:
echo.
echo 1. 组序号正确性:
echo    - 载荷_传感器组的所有传感器：组序号都是1
echo    - 位置_传感器组的所有传感器：组序号都是2
echo    - 如果有第3个组：组序号都是3
echo.
echo 2. 组名称显示正确性:
echo    - 每组第一行：显示完整组名称
echo    - 同组其他行：组名称列为空白
echo.
echo 3. 数据完整性:
echo    - 传感器序列号、类型等信息正确
echo    - 表头33列完整
echo    - 格式和边框正确
echo.

echo 🔍 关键验证点:
echo.
echo 1. 组序号一致性:
echo    - 同一组内所有传感器的组序号必须相同
echo    - 组序号应该从1开始连续递增
echo.
echo 2. 组名称显示规则:
echo    - 每组第一行显示组名称
echo    - 同组其他行组名称列为空
echo.
echo 3. 视觉效果:
echo    - 组结构清晰可见
echo    - 每组传感器明确分组
echo    - 序号和名称配合良好
echo.

echo 💡 技术要点:
echo.
echo 1. 显示序号 vs 内部ID:
echo    - 显示序号：用户看到的1, 2, 3...
echo    - 内部ID：系统生成的哈希值（如1234, 5678...）
echo    - 导出时使用显示序号，内部逻辑使用内部ID
echo.
echo 2. 向后兼容:
echo    - 新增displayGroupId参数有默认值-1
echo    - 当为-1时使用原来的group.groupId
echo    - 确保不影响其他调用
echo.
echo 3. 统一修复:
echo    - exportSensorGroupDetails方法 ✅
echo    - exportCompleteProject方法 ✅
echo    - 所有导出路径都使用相同逻辑
echo.

echo 🎉 修复完成！
echo.
echo 现在传感器组的显示应该完全正确：
echo ✅ 组序号：同组相同，不同组递增
echo ✅ 组名称：只在每组第一行显示
echo ✅ 传感器信息：完整准确
echo ✅ 格式样式：美观一致
echo.

echo 📝 后续验证:
echo 1. 检查组序号是否正确（1, 1, 2, 2, 2, 2）
echo 2. 检查组名称显示是否正确
echo 3. 检查数据完整性和格式
echo 4. 测试多个不同组的情况
echo.

pause
