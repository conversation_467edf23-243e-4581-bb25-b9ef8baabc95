#pragma once

/**
 * @file MainWindow_Qt_Simple.h
 * @brief Simplified Main Window Class Definition (Qt Version)
 * @details Simplified main window interface for Qt application
 * <AUTHOR> Assistant
 * @date 2025-08-05
 * @version 1.0.0
 */

#include <QtWidgets/QMainWindow>
#include <QtCore/QDateTime>
#include <QtCore/QTextStream>
#include <QtCore/QJsonObject>
#include <QtCore/QJsonArray>
#include <QtCore/QJsonDocument>
#include <memory>
#include <atomic>
#include <mutex>
#include <string>

// 前向声明以减少编译依赖
namespace Config {
    class ConfigManager;
}

// 包含必要的类型定义
#include "Common_Fixed.h"
//#include "CSVManager.h"
#include "SensorDataManager_1_2.h"  // 🆕 新增：传感器数据管理器
#include "ActuatorDataManager_1_2.h" // 🆕 新增：作动器数据管理器
#include "ActuatorViewModel_1_2.h" // 🆕 新增：作动器视图模型1_2版本
#include "CtrlChanDataManager.h" // 🆕 新增：控制通道数据管理器
#include "HardwareNodeResDataManager.h" // 🆕 新增：硬件节点资源数据管理器
#include "XLSDataExporter_1_2.h"    // 🆕 新增：XLS数据导出器
#include "JSONDataExporter_1_2.h"   // 🆕 新增：JSON数据导出器
#include "ActuatorDialog_1_2.h"     // 🆕 新增：作动器对话框（包含ActuatorGroup完整定义）
// #include "TreeInteractionHandler.h" // 🚫 已移除：树形控件交互处理器
#include "DetailInfoPanel.h" // 🆕 新增：详细信息面板
#include "BasicInfoWidget.h" // 🆕 新增：基本信息控件
#include "DataChangeListener.h"

// v3.4 架构管理器
#include "LogManager.h"
#include "ConfigManager.h"
#include "EventManager.h"
#include "DeviceManager.h"
#include "ExportManager.h"
#include "ProjectManager.h"
#include "DialogManager.h"
#include "TreeManager.h"
#include "InfoPanelManager.h"

// 前向声明DataModels类型
namespace DataModels {
    class TestProject;
    //struct HardwareNode;
    // 🚫 注释掉未使用的结构体
    // struct ActuatorInfo;
    // struct SensorInfo;
    // struct LoadControlChannel;
    // struct LoadSpectrum;
}

// 前向声明
namespace UI {
    struct CreateHardwareNodeParams;
    struct ChannelInfo;
    // ActuatorParams_1_2 和 ActuatorGroup_1_2 已通过 ActuatorDialog_1_2.h 包含
}

QT_BEGIN_NAMESPACE
class QWidget;
class QAction;
class QTreeWidget;
class QSplitter;
class QTabWidget;
class QLabel;
class QTimer;
class QTableWidget;
class QTextEdit;
class QPushButton;
class QScrollBar;
class QTreeWidgetItem;
QT_END_NAMESPACE

namespace Ui {
class MainWindow;
}

//namespace MyDlg {

// 前向声明
class CustomHardwareTreeWidget;
class CustomTestConfigTreeWidget;
class QMimeData;
class LogManager;
class DeviceManager;
class UIManager;
class MainWindowHelper;

/**
 * @brief Simplified main window class
 * @details Modern GUI main window with complete graphical interface operations
 */
class CMyMainWindow : public QMainWindow {
    Q_OBJECT

    // Friend classes for v3.4 architecture
    friend class DeviceManager;

private:
    // UI object
    Ui::MainWindow *ui;

    // 🆕 新增：从Qt资源系统加载样式表
    void loadStyleSheetFromFile();

    // 🆕 新增：设置树形控件的加号/减号图标
    void setupTreeWidgetIcons();

    // 🆕 新增：备用方案 - 简单的加号/减号样式
    void setupSimpleTreeIcons();

    // 🆕 新增：设置自定义树形控件样式
    void setupCustomTreeStyle();

    // Core data
    Config::ConfigManager* configManager_;

    // Hardware management
    DataModels::TestProject* currentProject_;

    // 注释掉CSV管理器 - CSV操作已弃用
    // std::unique_ptr<CSVManager> csvManager_;

    // 🆕 新增：传感器数据管理器1_2版本
    std::unique_ptr<SensorDataManager_1_2> sensorDataManager_;

    // 🆕 新增：作动器数据管理器（通过ViewModel访问）
    // std::unique_ptr<ActuatorDataManager_1_2> actuatorDataManager_; // 🔧 已移除：直接访问改为通过ViewModel

    // 🆕 新增：作动器视图模型1_2版本（解耦合专用）
    std::unique_ptr<ActuatorViewModel1_2> actuatorViewModel1_2_;

    // 🔧 临时兼容性方法：提供ActuatorDataManager接口的兼容访问
    ActuatorViewModel1_2* getActuatorDataManager() const { return actuatorViewModel1_2_.get(); }

    // 🆕 新增：控制通道数据管理器
    std::unique_ptr<CtrlChanDataManager> ctrlChanDataManager_;

    // 🆕 新增：硬件节点资源数据管理器
    std::unique_ptr<HardwareNodeResDataManager> hardwareNodeResDataManager_;

//    // 🆕 新增：数据导出管理器
//    std::unique_ptr<DataExportManager> dataExportManager_;

    // 🆕 新增：XLS数据导出器
    std::unique_ptr<XLSDataExporter_1_2> xlsDataExporter_;

    // 🆕 新增：JSON数据导出器
    std::unique_ptr<JSONDataExporter_1_2> jsonDataExporter_;

    // 🚫 已移除：树形控件交互处理器
    // std::unique_ptr<TreeInteractionHandler> treeInteractionHandler_;
    
    // 🆕 新增：详细信息面板
    std::unique_ptr<DetailInfoPanel> detailInfoPanel_;

    // 🆕 新增：数据变化监听器
    DataChangeListener* dataChangeListener_;

    // ===== 新增：管理器类 (v3.4架构) =====
    std::unique_ptr<LogManager> logManager_;
    std::unique_ptr<ConfigManager> configManager_wrapper_;
    std::unique_ptr<EventManager> eventManager_;
    std::unique_ptr<DeviceManager> deviceManager_;
    std::unique_ptr<ExportManager> exportManager_;
    std::unique_ptr<ProjectManager> projectManager_;
    std::unique_ptr<DialogManager> dialogManager_;
    std::unique_ptr<TreeManager> treeManager_;
    std::unique_ptr<InfoPanelManager> infoPanelManager_;
    std::unique_ptr<UIManager> uiManager_;           // 🆕 新增：UI管理器
    std::unique_ptr<MainWindowHelper> helper_;

    // Timers
    QTimer* statusUpdateTimer_;
    
    // System state
    bool isConnected_;
    bool isTestRunning_;
    QDateTime startTime_;

    // 🆕 新增：项目状态管理
    bool hasActiveProject_;              // 是否有活动项目
    QString currentProjectPath_;         // 当前项目路径
    QString currentProjectName_;         // 当前项目名称

//    // 🆕 新增：通道配置数据
//    QJsonObject channelConfigData_;      // 从channel_config.json加载的通道配置数据


public:
    explicit CMyMainWindow(QWidget* parent = nullptr);
    virtual ~CMyMainWindow();
    
    /**
     * @brief Initialize main window
     * @return true if successful
     */
    bool Initialize();

    /**
     * @brief Force restore all tree widget colors
     */
    void ForceRestoreAllTreeColors();

    /**
     * @brief Get test config tree widget
     */
    CustomTestConfigTreeWidget* getTestConfigTreeWidget() const;
    
    /**
     * @brief Get hardware tree widget
     */
    QTreeWidget* getHardwareTreeWidget() const;
    
    // ===== 管理器访问接口 (v3.4架构) =====
    /**
     * @brief 获取日志管理器
     */
    LogManager* getLogManager() const;
    
    /**
     * @brief 获取配置管理器
     */
    ConfigManager* getConfigManager() const;
    
    /**
     * @brief 获取事件管理器
     */
    EventManager* getEventManager() const;
    
    /**
     * @brief 获取设备管理器
     */
    DeviceManager* getDeviceManager() const;
    
    /**
     * @brief 获取导入导出管理器
     */
    ExportManager* getExportManager() const;
    
    /**
     * @brief 获取项目管理器
     */
    ProjectManager* getProjectManager() const;
    
    /**
     * @brief 获取对话框管理器
     */
    DialogManager* getDialogManager() const;
    
    /**
     * @brief 获取树形控件管理器
     */
    TreeManager* getTreeManager() const;
    
    /**
     * @brief 获取信息面板管理器
     */
    InfoPanelManager* getInfoPanelManager() const;
    
    /**
     * @brief 获取传感器数据管理器
     */
    SensorDataManager_1_2* getSensorDataManager() const;
    
    /**
     * @brief 获取作动器视图模型
     */
    ActuatorViewModel1_2* getActuatorViewModel() const;

public:
    // ===== v3.4架构管理器初始化方法 =====
    
    /**
     * @brief 初始化管理器依赖注入 (v3.4架构)
     * @details 按照设计方案设置现有组件的引用，不创建新组件
     */
    void initializeManagerDependencies();
    
    /**
     * @brief 完成管理器依赖注入 (在UI创建后调用)
     * @details 设置需要UI组件的管理器依赖
     */
    void completeManagerDependencies();
    
    /**
     * @brief 连接管理器信号槽 (v3.4架构)
     * @details 建立管理器之间的信号槽连接
     */
    void connectManagerSignals();
    
    /**
     * @brief 完成新项目设置 (v3.4架构)
     * @details 在ProjectManager创建新项目后，完成必要的初始化
     */
    void completeNewProjectSetup();

public:
    // ===== ProjectManager 访问器方法 =====
    /**
     * @brief 获取当前项目对象
     * @return 当前项目指针，可能为nullptr
     */
    DataModels::TestProject* getCurrentProject() const { return currentProject_; }
    
    /**
     * @brief 设置当前项目对象
     * @param project 项目对象指针
     */
    void setCurrentProject(DataModels::TestProject* project) { currentProject_ = project; }
    
    /**
     * @brief Load project from XLS format
     * @param filePath File path
     * @return true if successful
     */
    bool LoadProjectFromXLS(const QString& filePath);
    
    /**
     * @brief Save project to XLS format
     * @param filePath File path
     * @return true if successful
     */
    bool SaveProjectToXLS(const QString& filePath);
    
    /**
     * @brief Save project to JSON format
     * @param filePath File path
     * @return true if successful
     */
    bool SaveProjectToJSON(const QString& filePath);
    
    /**
     * @brief Clear all interface data and reset to default state
     */
    void ClearInterfaceData();
    
    // ===== 业务逻辑接口 =====
    /**
     * @brief 从数据管理器刷新硬件树显示
     */
    void RefreshHardwareTreeFromDataManagers();
    
    /**
     * @brief 从数据管理器刷新测试配置树显示
     */
    void RefreshTestConfigTreeFromDataManagers();
    
    /**
     * @brief 更新操作区域状态
     */
    void updateOperationAreaState(bool hasProject);

    // 🆕 新增：两级下拉框数据获取函数（供外部对话框使用）
    QPair<QStringList, QMap<QString, QStringList>> getHardwareGroupsAndMembers();
    QPair<QStringList, QMap<QString, QStringList>> getSensorGroupsAndMembers();
    QPair<QStringList, QMap<QString, QStringList>> getActuatorGroupsAndMembers();

    void OnTestConfigTreeItemDoubleClicked(QTreeWidgetItem* item, int column);
    
    // 🆕 新增：试验配置树单击事件处理（替代TreeInteractionHandler的onItemClicked功能）
    void OnTestConfigTreeItemClicked(QTreeWidgetItem* item, int column);

    // 🆕 新增：控制通道编辑辅助方法
    UI::ControlChannelParams getCurrentChannelParams(const QString& channelId);
    
    // 🆕 新增：显示控制通道详细信息
    void ShowControlChannelDetailInfo(const QString& channelName);
    
    // 🆕 新增：显示控制通道组汇总信息
    void ShowControlChannelGroupInfo();
    
    // 🆕 新增：获取详细信息面板实例
    DetailInfoPanel* getDetailInfoPanel() const { return detailInfoPanel_.get(); }
    
    // 🆕 新增：控制通道管理公共接口
    void OnClearSingleAssociation(QTreeWidgetItem* item);     // 删除单个关联
    void OnClearAllAssociation(QTreeWidgetItem* item);        // 删除所有关联
    void OnEditControlChannelDetailed(QTreeWidgetItem* item); // 编辑控制通道详细信息
    void OnDeleteControlChannel(QTreeWidgetItem* item);       // 删除控制通道
    void OnCreateControlChannel(QTreeWidgetItem* item);       // 新建控制通道
    
    // 🆕 新增：生成节点详细信息（替代TreeInteractionHandler的generateLayer2Info功能）
    QString generateNodeDetailInfo(QTreeWidgetItem* item);

public:
    // 🆕 新增：工程状态修复相关方法
    bool ensureDataManagerSync();
    void reinitializeDetailInfoPanel();
    void validateProjectState();

public slots:
    // File operations
    void OnNewProject();
    void OnOpenProject();
    void OnSaveProject();
    void OnSaveAsProject();
    // void OnSaveAsProject_1_2();  // 🚫 已注释：冗余的完整项目导出功能
    void OnExit();

    // Hardware node configuration
    void OnConfigureNodeLD_B1();
    void OnConfigureNodeLD_B2();
    
    void OnClearLog();
    void OnSaveLog();
    
    // Help
    void OnAbout();
    void OnRestoreColors();  // 手动恢复颜色
    
    // Status update
    void OnUpdateStatus();

    // 🆕 新增：详细信息显示控制
    void OnShowDetailInfoDlgChanged(bool checked);

    // 🆕 新增：树控件项目编辑处理


    // 🆕 新增：项目状态管理槽函数
    void OnProjectOpened(const QString& projectPath, const QString& projectName);
    void OnProjectClosed();
    void OnProjectSaved(const QString& projectPath);

    // 注释：独立的详细信息导出功能已移除，使用现有完整项目导出流程，避免代码复杂化
    // void OnExportControlChannelDetailsToExcel();  // 独立控制通道导出 - 已移除
    // void OnExportHardwareNodeDetailsToExcel();    // 独立硬件节点导出 - 已移除

    // 注释：独立的JSON导出功能已移除，使用现有项目保存和导出流程，避免代码复杂化
    // 🚫 已注释：独立JSON导出功能已废弃
    // void exportToJSON(const QString& filePath);   // 独立 JSON导出 - 恢复
    void initializeJSONExporter();                // JSON导出初始化 - 恢复

    // 注释：独立的servo_control格式导出功能已移除，使用现有硬件树管理流程，避免代码复杂化
    // void OnExportActuatorAsServoControl();  // 独立作动器导出 - 已移除
    // void OnImportActuatorFromServoControl(); // 独立作动器导入 - 已移除

    // 注释：传感器工程管理功能已移除，使用现有传感器管理流程，避免代码复杂化
    // void OnNewSensorProject();        // 新建传感器工程 - 已移除
    // void OnOpenSensorProject();       // 打开传感器工程 - 已移除
    // void OnSaveSensorProject();       // 保存传感器工程 - 已移除

    // void refreshSensorDisplay();      // 刷新传感器显示 - 已移除

    // 🆕 新增：传感器数据管理接口
    // bool saveSensorDetailedParams(const UI::SensorParams_1_2& params);
    // bool saveOrUpdateSensorDetailedParams(const UI::SensorParams_1_2& params);  // 🆕 支持组内重复序列号
    // UI::SensorParams_1_2 getSensorDetailedParams(const QString& serialNumber) const;
    // bool updateSensorDetailedParams(const QString& serialNumber, const UI::SensorParams_1_2& params);
    // bool removeSensorDetailedParams(const QString& serialNumber);
    // QStringList getAllSensorSerialNumbers() const;
    // QList<UI::SensorParams_1_2> getAllSensorDetailedParams() const;

    // 🆕 新增：带组ID的传感器数据管理接口
    bool saveSensorDetailedParamsInGroup(int groupId, const UI::SensorParams_1_2& params);  // 🆕 新增：组内保存
    bool saveOrUpdateSensorDetailedParamsInGroup(int groupId, const UI::SensorParams_1_2& params);
    UI::SensorParams_1_2 getSensorDetailedParamsInGroup(int groupId, const QString& serialNumber, bool &isHasData) const;
    bool updateSensorDetailedParamsInGroup(int groupId, const QString& serialNumber, const UI::SensorParams_1_2& params);
    bool removeSensorDetailedParamsInGroup(int groupId, const QString& serialNumber);
    QStringList getAllSensorSerialNumbersInGroup(int groupId) const;  // 🆕 新增：组内获取所有序列号
    QList<UI::SensorParams_1_2> getAllSensorDetailedParamsInGroup(int groupId) const;  // 🆕 新增：组内获取所有详细参数

    // 🆕 新增：作动器数据管理接口
    //bool saveActuatorDetailedParams(const UI::ActuatorParams_1_2& params);
    //bool saveOrUpdateActuatorDetailedParams(const UI::ActuatorParams_1_2& params);  // 🆕 支持组内重复序列号
    //UI::ActuatorParams_1_2 getActuatorDetailedParams(const QString& serialNumber) const;
    //bool updateActuatorDetailedParams(const QString& serialNumber, const UI::ActuatorParams_1_2& params);
    //bool removeActuatorDetailedParams(const QString& serialNumber);
    //QStringList getAllActuatorSerialNumbers() const;
    //QList<UI::ActuatorParams_1_2> getAllActuatorDetailedParams() const;

    // 🆕 新增：传感器数据验证和统计
    bool validateSensorData() const;
    bool validateSensorStorageConsistency() const;  // 🆕 新增：传感器存储一致性验证
    QMap<QString, int> getSensorTypeStatistics() const;

    // 🆕 新增：作动器组数据管理
    QList<UI::ActuatorGroup_1_2> getAllActuatorGroups_MainDlg() const;

    // 🆕 新增：传感器组数据管理
    QList<UI::SensorGroup_1_2> getAllSensorGroups() const;

    // 🆕 新增：控制通道数据收集
    QList<UI::ControlChannelGroup> buildControlChannelGroupsFromUI() const;
    QList<UI::ControlChannelGroup> collectControlChannelGroupsFromUI() const;

    // 🆕 新增：硬件节点数据收集（封装原有功能）
    QList<UI::NodeConfigParams> buildHardwareNodeConfigsFromUI() const;

    // ==================== ActuatorViewModel1_2业务信号处理 ====================

    /**
     * @brief 作动器组创建完成信号处理
     * @param groupName 组名称
     * @param groupId 组ID
     */
    void onActuatorGroupCreatedBusiness(const QString& groupName, int groupId);

    /**
     * @brief 作动器设备创建完成信号处理
     * @param serialNumber 序列号
     * @param groupId 组ID
     */
    void onActuatorDeviceCreatedBusiness(const QString& serialNumber, int groupId);

    /**
     * @brief 作动器设备编辑完成信号处理
     * @param serialNumber 序列号
     */
    void onActuatorDeviceEditedBusiness(const QString& serialNumber, int groupId);

    /**
     * @brief 作动器设备删除完成信号处理
     * @param serialNumber 序列号
     */
    void onActuatorDeviceDeletedBusiness(const QString& serialNumber);

    /**
     * @brief 作动器业务验证错误信号处理
     * @param error 错误信息
     */
    void onActuatorValidationError(const QString& error);

public:
    /**
     * @brief Setup UI connections and initialize components
     */
    void SetupUI();

    /**
     * @brief Connect UI signals to slots
     */
    void ConnectUISignals();
    
    /**
     * @brief Initialize hardware tree
     */
    void InitializeHardwareTree();
    
    /**
     * @brief Initialize test config tree
     */
    void InitializeTestConfigTree();

    /**
     * @brief Initialize tree interaction handler
     */
    // void initializeTreeInteractionHandler(); // 🚫 已移除：树形控件交互处理器
    
    /**
     * @brief Load initial sample data
     */
    void LoadInitialData();
    
    /**
     * @brief Connect signals and slots
     */
    void ConnectSignals();
    
    /**
     * @brief Update interface status
     */
    void UpdateUI();
    

    
    /**
     * @brief 获取控制通道数据管理器
     */
    CtrlChanDataManager* getCtrlChanDataManager() const { return ctrlChanDataManager_.get(); }
    
    /**
     * @brief 获取硬件节点资源数据管理器
     */
    HardwareNodeResDataManager* getHardwareNodeResDataManager() const { return hardwareNodeResDataManager_.get(); }
    
    /**
     * @brief Add sample hardware node
     */
    void AddSampleHardwareNode(const QString& name, const QString& ip, const QString& status);
    
    /**
     * @brief Add sample actuator
     */
    void AddSampleActuator(const QString& name, const QString& capacity, const QString& type);
    
    /**
     * @brief Add sample sensor
     */
    void AddSampleSensor(const QString& name, const QString& range, const QString& type);

    /**
     * @brief Hardware tree context menu slots
     */
    void OnHardwareTreeContextMenu(const QPoint& pos);
    void OnTestConfigTreeContextMenu(const QPoint& pos);
    void OnCreateActuatorGroup();
    void OnCreateSensorGroup();
    void OnCreateHardwareNode();
    void OnEditHardwareNode(QTreeWidgetItem* item);
    void OnDeleteHardwareNode(QTreeWidgetItem* item);
    void OnDeleteActuatorDevice(QTreeWidgetItem* item);
    void OnDeleteSensorDevice(QTreeWidgetItem* item);
    void OnEditActuatorDevice(QTreeWidgetItem* item);
    void OnEditSensorDevice(QTreeWidgetItem* item);
    void OnEditActuatorGroup(QTreeWidgetItem* item);
    void OnEditSensorGroup(QTreeWidgetItem* item);
    void OnClearAssociation(QTreeWidgetItem* item);
    

    
//    /**
//     * @brief 从LoadControlChannel转换为UI::ControlChannelParams
//     * @param loadChannel LoadControlChannel对象
//     * @return 通道参数对象
//     */
//    UI::ControlChannelParams convertFromLoadControlChannel(const DataModels::LoadControlChannel& loadChannel);
    
//    /**
//     * @brief 将UI::ControlChannelParams参数更新到LoadControlChannel
//     * @param channel LoadControlChannel对象引用
//     * @param params UI通道参数
//     */
//    void updateLoadControlChannelFromParams(DataModels::LoadControlChannel& channel, const UI::ControlChannelParams& params);
    
//    /**
//     * @brief 从关联字符串中提取设备ID
//     * @param association 关联字符串（格式："组名 - 设备名"）
//     * @param deviceType 设备类型（"作动器"或"传感器"）
//     * @return 设备ID，无法解析时返回原始字符串
//     */
//    std::string extractDeviceIdFromAssociation(const std::string& association, const std::string& deviceType);
    
    /**
     * @brief 从UI树形结构提取通道参数
     * @param channelId 通道ID
     * @return 通道参数对象
     */
    UI::ControlChannelParams extractChannelParamsFromUI(const QString& channelId);
    
//    /**
//     * @brief 在试验通道树中查找指定的通道节点
//     * @param channelId 通道ID
//     * @return 通道节点指针，未找到返回nullptr
//     */
//    QTreeWidgetItem* findChannelItemInTree(const QString& channelId);
    
    /**
     * @brief 递归查找通道节点
     * @param parentItem 父节点
     * @param channelId 通道ID
     * @return 通道节点指针，未找到返回nullptr
     */
    QTreeWidgetItem* findChannelItemRecursive(QTreeWidgetItem* parentItem, const QString& channelId);
    void updateControlChannelParams(const QString& channelId, const UI::ControlChannelParams& updatedParams);
    void updateControlChannelDisplay(const QString& channelId, const UI::ControlChannelParams& params);
    QTreeWidgetItem* findChannelItem(const QString& channelId);
    void updateChannelChildNodes(QTreeWidgetItem* channelItem, const UI::ControlChannelParams& params);
    QStringList getAvailableHardwareNodes();
    QStringList getAvailableSensors();
    QStringList getAvailableActuators();

    /**
     * @brief Create hardware groups
     */
    void CreateActuatorGroup(const QString& groupName);
    void CreateActuatorGroupByCapacity(const QString& capacity);
    void CreateSensorGroup(const QString& groupName);

    /**
     * @brief Create actuator in group
     */
    void OnCreateActuator(QTreeWidgetItem* groupItem);
    void CreateActuatorDevice(QTreeWidgetItem* groupItem, const QString& serialNumber,
                            const QString& type, const QString& model, double cylinderDiameter,
                            double rodDiameter, double stroke);
    void CreateActuatorDeviceWithExtendedParams(QTreeWidgetItem* groupItem, const QString& serialNumber,
                                              const QString& type, const QString& polarity,
                                              double dither, double frequency, double outputMultiplier,
                                              double balance, double cylinderDiameter,
                                              double rodDiameter, double stroke);

    // 🆕 新增：作动器组管理方法
    bool createOrUpdateActuatorGroup(QTreeWidgetItem* groupItem, const UI::ActuatorParams_1_2& params);
    int extractActuatorGroupIdFromItem(QTreeWidgetItem* groupItem) const;  // 🆕 新增：从作动器组项目提取组ID
    // ❌ 已废弃：int extractGroupIdFromName(const QString& groupName) const;

    // 🆕 新增：传感器组管理方法
    bool createOrUpdateSensorGroup(QTreeWidgetItem* groupItem, const UI::SensorParams_1_2& params);
    int extractSensorGroupIdFromItem(QTreeWidgetItem* groupItem) const;
    int generateSensorGroupIdFromName(const QString& groupName) const;

    /**
     * @brief Create sensor in group
     */
    void OnCreateSensor(QTreeWidgetItem* groupItem);
    void CreateSensorDevice(QTreeWidgetItem* groupItem, const QString& serialNumber,
                          const QString& sensorType, const QString& model,
                          const QString& range, const QString& accuracy);

    /**
     * @brief Hardware node creation helpers
     */
    QString GenerateNextHardwareNodeName();
    void CreateHardwareNodeInTree(const UI::CreateHardwareNodeParams& params);
    void UpdateHardwareNodeChannelsInTree(QTreeWidgetItem* nodeItem, const UI::CreateHardwareNodeParams& params);

    /**
     * @brief Add sample load channel
     */
    void AddSampleLoadChannel(const QString& name, const QString& capacity, const QString& mode);
    
    /**
     * @brief Add log entry
     */
    void AddLogEntry(const QString& level, const QString& message) const;
    
//    /**
//     * @brief Create new project
//     */
//    void CreateNewProject();
    
//    /**
//     * @brief Load project
//     * @param filePath File path
//     * @return true if successful
//     */
//    bool LoadProject(const StringType& filePath);
    
//    /**
//     * @brief Save project
//     * @param filePath File path
//     * @return true if successful
//     */
//    bool SaveProject(const StringType& filePath);



    /**
     * @brief 带有通道配置支持的JSON导出函数
     * @param filePath 文件路径
     * @return 导出成功返回true
     */
    bool SaveProjectToJSON_1_2(const QString& filePath);



    /**
     * @brief 重新分配所有传感器组的ID，确保组内ID从1开始连续递增
     * @note 在保存工程前调用，确保Excel中的传感器ID是连续的
     */
    void reassignAllSensorGroupIds();

    // ==================== 数据同步方法 ====================

    /**
     * @brief 同步内存数据到项目（准备数据以供保存）
     * @note 确保数据在各自的DataManager中是最新的
     */
    void syncMemoryDataToProject();

    /**
     * @brief 同步项目数据到内存（从外部数据源加载）
     * @note 清空现有数据并准备加载新数据
     */
    void syncProjectDataToMemory();

    /**
     * @brief 清空内存数据
     * @note 清空所有DataManager中的数据
     */
    void clearMemoryData();

    /**
     * @brief Extract parameter from tooltip string
     * @param tooltip Tooltip string
     * @param parameterName Parameter name to extract
     * @return Parameter value
     */
    QString ExtractParameterFromTooltip(const QString& tooltip, const QString& parameterName);

    /**
     * @brief Get tree item level
     * @param item Tree widget item
     * @return Item level (0 for root items)
     */
    int GetItemLevel(QTreeWidgetItem* item);

//    /**
//     * @brief Load project from JSON format
//     * @param filePath File path
//     * @return true if successful
//     */
//    bool LoadProjectFromJSON(const QString& filePath);



    /**
     * @brief Hide unused menu items
     */
    void HideUnusedMenuItems();

    /**
     * @brief Check if name already exists in tree
     * @param parentItem Parent tree item to search in
     * @param name Name to check
     * @return true if name exists, false otherwise
     */
    bool IsNameExistsInTree(QTreeWidgetItem* parentItem, const QString& name);

    /**
     * @brief Check if actuator group name exists
     * @param groupName Group name to check
     * @return true if exists, false otherwise
     */
    bool IsActuatorGroupNameExists(const QString& groupName);

    /**
     * @brief Check if sensor group name exists
     * @param groupName Group name to check
     * @return true if exists, false otherwise
     */
    bool IsSensorGroupNameExists(const QString& groupName);

    /**
     * @brief Check if sensor serial number exists in specific group
     * @param groupItem Sensor group item to check
     * @param serialNumber Serial number to check
     * @return true if exists, false otherwise
     */
    bool isSensorSerialNumberExistsInGroup(QTreeWidgetItem* groupItem, const QString& serialNumber);

    /**
     * @brief Check if actuator serial number exists in specific group
     * @param groupItem Actuator group item to check
     * @param serialNumber Serial number to check
     * @return true if exists, false otherwise
     */
    bool isActuatorSerialNumberExistsInGroup(QTreeWidgetItem* groupItem, const QString& serialNumber);

    /**
     * @brief Check if hardware node name exists
     * @param nodeName Node name to check
     * @return true if exists, false otherwise
     */
    bool IsHardwareNodeNameExists(const QString& nodeName);

    /**
     * @brief Check if interface has data content
     * @return true if has data, false if empty
     */
    bool HasInterfaceData();



    /**
     * @brief Set interface to default empty state
     */
    void SetDefaultEmptyInterface();

    /**
     * @brief Prompt user to save project if has unsaved changes
     * @return true if user wants to continue, false if cancelled
     */
    bool PromptSaveIfNeeded();

    /**
     * @brief Add sample association info for new project
     */
    void AddSampleAssociationInfoForNewProject();

    /**
     * @brief Clear node associations recursively
     * @param node Tree widget item to clear
     */
    void ClearNodeAssociations(QTreeWidgetItem* node);

    /**
     * @brief 处理拖拽关联
     */
    void HandleDragDropAssociation(QTreeWidgetItem* targetItem, const QString& sourceText, const QString& sourceType);

    /**
     * @brief 处理拖拽关联（包含父节点信息）
     */
    void HandleDragDropAssociationWithParent(QTreeWidgetItem* targetItem, const QString& sourceText, const QString& sourceType, const QString& parentText);

    /**
     * @brief 生成详细的关联信息，包含父节点信息
     */
    QString GenerateDetailedAssociationInfo(const QString& sourceText, const QString& sourceType);

    /**
     * @brief 查找源节点
     */
    QTreeWidgetItem* FindSourceItem(const QString& sourceText, const QString& sourceType);

    /**
     * @brief 验证并更新智能通道关联
     * @details 检查硬件节点是否存在，更新试验配置中CH1/CH2的关联信息
     */
    void UpdateSmartChannelAssociations();

    /**
     * @brief 检查硬件节点是否存在
     * @param nodeName 硬件节点名称（如LD-B1）
     * @return 存在返回true，否则返回false
     */
    bool IsHardwareNodeExists(const QString& nodeName) const;

    /**
     * @brief 启用试验配置树的拖拽功能
     */
    void EnableTestConfigTreeDragDrop();

    /**
     * @brief 保存拖拽关联信息到CtrlChanDataManager
     * @param targetItem 目标节点
     * @param associationInfo 关联信息
     * @param sourceType 源类型
     */
    void SaveDragDropAssociationToDataManager(QTreeWidgetItem* targetItem, const QString& associationInfo, const QString& sourceType);

    /**
     * @brief 处理试验配置树的拖拽事件
     * @param event 拖拽事件
     */
    void HandleTestConfigTreeDragDrop(QDropEvent* event);

    /**
     * @brief 解析硬件节点拖拽数据
     * @param mimeData 拖拽的MIME数据
     * @param hardwareNode 输出：硬件节点名称
     * @param channel 输出：通道名称
     * @return 解析成功返回true
     */
    bool ParseHardwareDragData(const QMimeData* mimeData, QString& hardwareNode, QString& channel) const;

    /**
     * @brief 设置试验通道关联信息
     * @param testChannel 试验通道名称（CH1/CH2）
     * @param hardwareNode 硬件节点名称（LD-B1/LD-B2）
     * @param hardwareChannel 硬件通道名称（CH1/CH2）
     */
    void SetTestChannelAssociation(const QString& testChannel, const QString& hardwareNode, const QString& hardwareChannel);

    /**
     * @brief 递归查找匹配的节点
     */
    QTreeWidgetItem* FindItemRecursive(QTreeWidgetItem* parent, const QString& targetText, const QString& targetType);

    /**
     * @brief 检查项目是否可以拖拽
     */
    bool canDragItem(QTreeWidgetItem* item) const;

    /**
     * @brief 获取项目类型
     */
    QString getItemType(QTreeWidgetItem* item) const;

    /**
     * @brief 检查是否可以接受拖拽
     */
    bool canAcceptDrop(QTreeWidgetItem* targetItem, const QString& sourceType) const;

    /**
     * @brief 🔧 新增：检查目标节点是否是主通道节点
     * @param item 要检查的节点
     * @return 如果是主通道节点返回true，否则返回false
     * @details 用于判断硬件节点通道是否可以拖拽到该节点
     */
    bool isMainChannelNode(QTreeWidgetItem* item) const;

    // 🆕 新增：传感器数据管理辅助方法
    void initializeSensorDataManager();
    bool collectSensorDetailedDataForExport(QJsonArray& jsonArray);

    // 🆕 新增：控制通道数据管理辅助方法
    void initializeCtrlChanDataManager();

    // 🆕 新增：硬件节点资源数据管理辅助方法
    void initializeHardwareNodeResDataManager();
    
    // 🆕 新增：详细信息面板管理
    void initializeDetailInfoPanel();
    void connectDetailInfoPanelSignals();
    void debugDetailInfoPanelStatus();

    // 🆕 新增：数据导出管理辅助方法
//    void initializeDataExportManager();
//    void connectExportManagerSignals();

    // 🆕 新增：XLS导出功能私有方法
    void initializeXLSExporter();
    // QString showExportOptionsDialog();  // 注释掉导出选项对话框
    bool showImportConfirmationDialog(const QString& fileName);
    void configureXLSExporterOptions(XLSDataExporter_1_2* exporter);
    void handleExportResult(bool success, const QString& filePath,
                           const QString& operation, const QString& errorMessage = QString());

    /**
     * @brief 更新树控件显示
     */
    void UpdateTreeDisplay();





    /**
     * @brief 🆕 新增：从数据管理器刷新所有界面数据
     * @details 用于工程导入后刷新界面显示
     */
    void refreshAllDataFromManagers();

    // 🆕 新增：项目状态管理私有方法

    /**
     * @brief 显示项目状态提示信息
     * @param hasProject 是否有活动项目
     */
    void showProjectStatusMessage(bool hasProject);

    /**
     * @brief 设置操作区域控件的启用状态
     * @param enabled 是否启用
     */
    void setOperationControlsEnabled(bool enabled);

    /**
     * @brief 初始化项目状态
     */
    void initializeProjectState();

    /**
     * @brief 检查当前是否有活动项目
     * @return 有活动项目返回true
     */
    bool hasActiveProject() const;

    /**
     * @brief 拖拽完成后更新节点提示信息
     * @param targetItem 目标节点
     * @param associationInfo 关联信息
     * @param sourceType 源节点类型
     */
    void UpdateNodeTooltipAfterDragDrop(QTreeWidgetItem* targetItem, const QString& associationInfo, const QString& sourceType);

    /**
     * @brief 生成更新后的tooltip
     * @param nodeName 节点名称
     * @param originalTooltip 原始tooltip
     * @param associationInfo 关联信息
     * @param sourceType 源节点类型
     * @return 更新后的tooltip
     */
    QString GenerateUpdatedTooltip(const QString& nodeName, const QString& originalTooltip, const QString& associationInfo, const QString& sourceType);

    /**
     * @brief 生成节点详细信息tooltip
     * @param nodeName 节点名称
     * @param associationInfo 关联信息
     * @param sourceType 源节点类型
     * @return 详细信息tooltip
     */
    QString GenerateDetailedNodeTooltip(const QString& nodeName, const QString& associationInfo, const QString& sourceType);

    /**
     * @brief 生成控制通道详细信息
     * @param channelName 通道名称
     * @return 控制通道详细信息
     */
    QString GenerateControlChannelDetailedInfo(const QString& channelName);

    /**
     * @brief 极性值转换为显示文本
     * @param polarity 极性值 (1=正极性, -1=负极性, 9=双极性, 0=未知)
     * @return 极性显示文本
     */
    QString getPolarityDisplayText(int polarity) const;

    /**
     * @brief 控制模式转换为显示文本  
     * @param controlMode 控制模式值 (1=力控制, 2=位移控制, 3=速度控制, 4=混合控制)
     * @return 控制模式显示文本
     */
    QString getControlModeDisplayText(int controlMode) const;

    /**
     * @brief 极性值转换为简短显示文本（用于树形控件列显示）
     * @param polarity 极性值 (1=正极性, -1=负极性, 9=双极性, 0=未知)
     * @return 极性简短显示文本
     */
    QString getPolarityShortText(int polarity) const;

    /**
     * @brief 生成载荷传感器详细信息
     * @param sensorName 传感器名称
     * @param associationInfo 关联信息
     * @return 载荷传感器详细信息
     */
    QString GenerateLoadSensorDetailedInfo(const QString& sensorName, const QString& associationInfo);

    /**
     * @brief 生成位置传感器详细信息
     * @param associationInfo 关联信息
     * @return 位置传感器详细信息
     */
    QString GeneratePositionSensorDetailedInfo(const QString& associationInfo);

    /**
     * @brief 生成控制作动器详细信息
     * @param associationInfo 关联信息
     * @return 控制作动器详细信息
     */
    QString GenerateControlActuatorDetailedInfo(const QString& associationInfo);

    /**
     * @brief 生成硬件节点详细信息
     * @param nodeName 节点名称
     * @return 硬件节点详细信息
     */
    QString GenerateHardwareNodeDetailedInfo(const QString& nodeName);

    /**
     * @brief 生成组详细信息
     * @param groupName 组名称
     * @return 组详细信息
     */
    QString GenerateGroupDetailedInfo(const QString& groupName);

    /**
     * @brief 生成通用节点详细信息
     * @param nodeName 节点名称
     * @return 通用节点详细信息
     */
    QString GenerateGenericNodeDetailedInfo(const QString& nodeName);

    /**
     * @brief 根据关联信息获取设备详细信息
     * @param associationInfo 关联信息
     * @return 设备详细信息
     */
    QString GetDeviceDetailsByAssociation(const QString& associationInfo);

    /**
     * @brief 根据名称获取传感器详细信息
     * @param sensorName 传感器名称
     * @return 传感器详细信息
     */
    QString GetSensorDetailsByName(const QString& sensorName);

    /**
     * @brief 根据名称获取作动器详细信息
     * @param actuatorName 作动器名称
     * @return 作动器详细信息
     */
    QString GetActuatorDetailsByName(const QString& actuatorName);

    /**
     * @brief 查找树形控件中的节点
     * @param treeWidget 树形控件
     * @param itemText 节点文本
     * @return 找到的节点项，未找到返回nullptr
     */
    QTreeWidgetItem* FindTreeItem(QTreeWidget* treeWidget, const QString& itemText) const;

    /**
     * @brief 更新所有树形控件节点提示信息
     */
    void UpdateAllTreeWidgetTooltips();

    /**
     * @brief 更新单个作动器的tooltip信息
     * @param serialNumber 作动器序列号
     * @param groupId 组ID
     */
    void UpdateSingleActuatorTooltip(const QString& serialNumber, int groupId);

    /**
     * @brief 在指定树形控件中更新作动器节点的tooltip
     * @param treeWidget 树形控件指针
     * @param serialNumber 作动器序列号
     * @param groupName 组名
     */
    void UpdateActuatorNodeTooltipInTree(QTreeWidget* treeWidget, const QString& serialNumber, const QString& groupName);

    /*
     * @brief 根据序列号获取传感器所属的组名（兼容性函数，返回第一个匹配的组名）
     * @param serialNumber 传感器序列号
     * @return 组名，如果未找到返回空字符串
     * @deprecated 不严谨！已注释掉。建议使用精准操作函数 GetSensorByGroupIdAndSerialNumber 或 GetSensorByGroupNameAndSerialNumber
     * @warning 此函数可能返回错误的组信息，因为同一序列号可能属于多个组
     */
    // QString GetSensorGroupNameBySerialNumber(const QString& serialNumber) const;

    /**
     * @brief 根据序列号获取作动器所属的所有组名
     * @param serialNumber 作动器序列号
     * @return 包含该序列号的所有组名列表
     */
    QStringList GetActuatorGroupNamesBySerialNumber(const QString& serialNumber) const;

    /*
     * @brief 根据序列号获取作动器所属的组名（兼容性函数，返回第一个匹配的组名）
     * @param serialNumber 作动器序列号
     * @return 组名，如果未找到返回空字符串
     * @deprecated 不严谨！已注释掉。建议使用精准操作函数 GetActuatorByGroupIdAndSerialNumber 或 GetActuatorByGroupNameAndSerialNumber
     * @warning 此函数可能返回错误的组信息，因为同一序列号可能属于多个组
     */
    // QString GetActuatorGroupNameBySerialNumber(const QString& serialNumber) const;

    // ===============================
    // 🔍 精准操作函数（推荐使用）
    // ===============================
    
    /**
     * @brief 通过组ID和序列号精准获取传感器信息
     * @param groupId 传感器组ID
     * @param serialNumber 传感器序列号
     * @return 传感器参数，如果未找到返回空的参数结构
     */
    UI::SensorParams_1_2 GetSensorByGroupIdAndSerialNumber(int groupId, const QString& serialNumber) const;
    
    /**
     * @brief 通过组名和序列号精准获取传感器信息
     * @param groupName 传感器组名
     * @param serialNumber 传感器序列号
     * @return 传感器参数，如果未找到返回空的参数结构
     */
    UI::SensorParams_1_2 GetSensorByGroupNameAndSerialNumber(const QString& groupName, const QString& serialNumber) const;
    
    /**
     * @brief 通过组ID和序列号精准删除传感器
     * @param groupId 传感器组ID
     * @param serialNumber 传感器序列号
     * @return 删除是否成功
     */
    bool DeleteSensorByGroupIdAndSerialNumber(int groupId, const QString& serialNumber);
    
    /**
     * @brief 通过组名和序列号精准删除传感器
     * @param groupName 传感器组名
     * @param serialNumber 传感器序列号
     * @return 删除是否成功
     */
    bool DeleteSensorByGroupNameAndSerialNumber(const QString& groupName, const QString& serialNumber);
    
    /**
     * @brief 通过组ID和序列号精准获取作动器信息
     * @param groupId 作动器组ID
     * @param serialNumber 作动器序列号
     * @return 作动器参数，如果未找到返回空的参数结构
     */
    UI::ActuatorParams_1_2 GetActuatorByGroupIdAndSerialNumber(int groupId, const QString& serialNumber) const;
    
    /**
     * @brief 通过组名和序列号精准获取作动器信息
     * @param groupName 作动器组名
     * @param serialNumber 作动器序列号
     * @return 作动器参数，如果未找到返回空的参数结构
     */
    UI::ActuatorParams_1_2 GetActuatorByGroupNameAndSerialNumber(const QString& groupName, const QString& serialNumber) const;
    
    /**
     * @brief 通过组ID和序列号精准删除作动器
     * @param groupId 作动器组ID
     * @param serialNumber 作动器序列号
     * @return 删除是否成功
     */
    bool DeleteActuatorByGroupIdAndSerialNumber(int groupId, const QString& serialNumber);
    
    /**
     * @brief 通过组名和序列号精准删除作动器
     * @param groupName 作动器组名
     * @param serialNumber 作动器序列号
     * @return 删除是否成功
     */
    bool DeleteActuatorByGroupNameAndSerialNumber(const QString& groupName, const QString& serialNumber);
    
    /**
     * @brief 精准更新控制通道中的传感器关联信息
     * @param groupId 传感器组ID
     * @param groupName 传感器组名
     * @param serialNumber 传感器序列号
     * @param updatedParams 更新后的传感器参数
     */
    void UpdateControlChannelAssociationsAfterSensorEditPrecise(int groupId, const QString& groupName, const QString& serialNumber, const UI::SensorParams_1_2& updatedParams);
    
    /**
     * @brief 精准更新控制通道中的作动器关联信息
     * @param groupId 作动器组ID
     * @param groupName 作动器组名
     * @param serialNumber 作动器序列号
     * @param updatedParams 更新后的作动器参数
     */
    void UpdateControlChannelAssociationsAfterActuatorEditPrecise(int groupId, const QString& groupName, const QString& serialNumber, const UI::ActuatorParams_1_2& updatedParams);
    
    /**
     * @brief 精准清理控制通道中的传感器关联信息
     * @param groupId 传感器组ID
     * @param groupName 传感器组名
     * @param serialNumber 传感器序列号
     */
    void ClearControlChannelAssociationsAfterSensorDeletePrecise(int groupId, const QString& groupName, const QString& serialNumber);
    
    /**
     * @brief 精准清理控制通道中的作动器关联信息
     * @param groupId 作动器组ID
     * @param groupName 作动器组名
     * @param serialNumber 作动器序列号
     */
    void ClearControlChannelAssociationsAfterActuatorDeletePrecise(int groupId, const QString& groupName, const QString& serialNumber);

    // ===============================
    // 🚀 阶段1：基础优化功能
    // ===============================
    
    /**
     * @brief 获取使用指定传感器的控制通道列表（删除前检查，增强版支持组名验证）
     * @param serialNumber 传感器序列号
     * @param groupName 传感器所属组名（用于精确匹配，防止误判）
     * @return 使用该传感器的控制通道列表
     */
    QStringList GetControlChannelsUsingSensor(const QString& serialNumber, const QString& groupName);
    
    /**
     * @brief 获取使用指定传感器的控制通道列表（删除前检查，兼容版仅检查序列号）
     * @param serialNumber 传感器序列号
     * @return 使用该传感器的控制通道列表
     * @note 建议使用带组名参数的版本以避免误判
     */
    QStringList GetControlChannelsUsingSensor(const QString& serialNumber);
    
    /**
     * @brief 获取使用指定作动器的控制通道列表（删除前检查）
     * @param serialNumber 作动器序列号
     * @return 使用该作动器的控制通道列表
     */
    QStringList GetControlChannelsUsingActuator(const QString& serialNumber);
    
    /**
     * @brief 精确匹配并清除传感器关联信息（增强版，支持组名验证）
     * @param associationField 关联字段内容（格式："组名 - 序列号"）
     * @param serialNumber 要清除的传感器序列号
     * @param groupName 要清除的传感器所属组名（用于精确匹配，防止误删）
     * @return 是否需要清除（匹配成功）
     */
    bool IsExactSensorMatch(const std::string& associationField, const QString& serialNumber, const QString& groupName);
    
    /**
     * @brief 精确匹配并清除传感器关联信息（兼容版，仅检查序列号）
     * @param associationField 关联字段内容
     * @param serialNumber 要清除的传感器序列号
     * @return 是否需要清除（匹配成功）
     * @note 建议使用带组名参数的版本以避免误删
     */
    bool IsExactSensorMatch(const std::string& associationField, const QString& serialNumber);
    
    /**
     * @brief 精确匹配并清除作动器关联信息（增强版，支持组名验证）
     * @param associationField 关联字段内容
     * @param serialNumber 要清除的作动器序列号
     * @param groupName 作动器所属组名（用于精确匹配）
     * @return 是否需要清除（匹配成功）
     */
    bool IsExactActuatorMatch(const std::string& associationField, const QString& serialNumber, const QString& groupName);

    /**
     * @brief 精确匹配并清除作动器关联信息（兼容版，仅检查序列号）
     * @param associationField 关联字段内容
     * @param serialNumber 要清除的作动器序列号
     * @return 是否需要清除（匹配成功）
     */
    bool IsExactActuatorMatch(const std::string& associationField, const QString& serialNumber);

    // ===============================
    // 🚀 阶段2：功能增强
    // ===============================
    
    /**
     * @brief 批量删除传感器后更新控制通道关联信息
     * @param serialNumbers 传感器序列号列表
     */
    void UpdateControlChannelAssociationsAfterSensorsBatchDelete(const QStringList& serialNumbers);
    
    /**
     * @brief 批量删除作动器后更新控制通道关联信息
     * @param serialNumbers 作动器序列号列表
     */
    void UpdateControlChannelAssociationsAfterActuatorsBatchDelete(const QStringList& serialNumbers);
    
    /**
     * @brief 关联信息验证结果结构
     */
    struct AssociationValidationResult {
        QStringList invalidSensorAssociations;   // 无效的传感器关联
        QStringList invalidActuatorAssociations; // 无效的作动器关联
        QStringList orphanedChannels;            // 孤立的通道
        int totalChannels;                       // 总通道数
        int validAssociations;                   // 有效关联数
        
        AssociationValidationResult() : totalChannels(0), validAssociations(0) {}
    };
    
    /**
     * @brief 验证所有控制通道关联信息的有效性
     * @return 验证结果统计信息
     */
    AssociationValidationResult ValidateAllControlChannelAssociations();
    
    /**
     * @brief 修复无效的控制通道关联信息
     * @param validationResult 验证结果
     * @return 修复成功的关联数量
     */
    int RepairInvalidControlChannelAssociations(const AssociationValidationResult& validationResult);

    /**
     * @brief 更新硬件配置树的所有节点提示
     */
    void UpdateHardwareTreeTooltips();

    /**
     * @brief 更新实验配置树的所有节点提示
     */
    void UpdateExperimentTreeTooltips();

    /**
     * @brief 递归更新树节点的提示信息
     * @param item 树节点项
     */
    void UpdateTreeItemTooltipsRecursively(QTreeWidgetItem* item);

    /**
     * @brief 更新单个节点的提示信息
     * @param item 树节点项
     */
    void UpdateSingleNodeTooltip(QTreeWidgetItem* item);

    /**
     * @brief 检测是否为debug模式
     * @return true表示debug模式，false表示release模式
     */
    bool IsDebugMode() const;

    /**
     * @brief 获取节点在树中的深度
     * @param item 树节点项
     * @return 节点深度（根节点为0）
     */
    int GetItemDepth(QTreeWidgetItem* item) const;

    /**
     * @brief 为tooltip添加debug信息
     * @param originalTooltip 原始tooltip
     * @param nodeName 节点名称
     * @param item 树节点项
     * @return 添加debug信息后的tooltip
     */
    QString AddDebugInfoToTooltip(const QString& originalTooltip, const QString& nodeName, QTreeWidgetItem* item);

    /**
     * @brief 添加作动器组debug信息
     * @param debugInfo debug信息字符串（引用传递）
     * @param groupName 组名称
     */
    void AddActuatorGroupDebugInfo(QString& debugInfo, const QString& groupName) const;

    /**
     * @brief 添加作动器设备debug信息
     * @param debugInfo debug信息字符串（引用传递）
     * @param serialNumber 设备序列号
     */
    void AddActuatorDeviceDebugInfo(QString& debugInfo, const QString& serialNumber) const;

    /**
     * @brief 添加传感器组debug信息
     * @param debugInfo debug信息字符串（引用传递）
     * @param groupName 组名称
     */
    void AddSensorGroupDebugInfo(QString& debugInfo, const QString& groupName) const;

    /**
     * @brief 添加传感器设备debug信息
     * @param debugInfo debug信息字符串（引用传递）
     * @param serialNumber 设备序列号
     */
    void AddSensorDeviceDebugInfo(QString& debugInfo, const QString& serialNumber) const;

    /**
     * @brief 添加硬件节点debug信息
     * @param debugInfo debug信息字符串（引用传递）
     * @param nodeName 节点名称
     */
    void AddHardwareNodeDebugInfo(QString& debugInfo, const QString& nodeName) const;

    /**
     * @brief 添加硬件通道debug信息
     * @param debugInfo debug信息字符串（引用传递）
     * @param channelName 通道名称
     * @param parentNodeName 父节点名称
     */
    void AddHardwareChannelDebugInfo(QString& debugInfo, const QString& channelName, const QString& parentNodeName) const;

    /**
     * @brief 添加控制通道debug信息
     * @param debugInfo debug信息字符串（引用传递）
     * @param channelName 通道名称
     */
    void AddControlChannelDebugInfo(QString& debugInfo, const QString& channelName) const;

    /**
     * @brief 添加载荷传感器debug信息
     * @param debugInfo debug信息字符串（引用传递）
     * @param sensorName 传感器名称
     * @param associationInfo 关联信息
     */
    void AddLoadSensorDebugInfo(QString& debugInfo, const QString& sensorName, const QString& associationInfo) const;

    /**
     * @brief 添加位置传感器debug信息
     * @param debugInfo debug信息字符串（引用传递）
     * @param associationInfo 关联信息
     */
    void AddPositionSensorDebugInfo(QString& debugInfo, const QString& associationInfo) const;

    /**
     * @brief 添加控制作动器debug信息
     * @param debugInfo debug信息字符串（引用传递）
     * @param associationInfo 关联信息
     */
    void AddControlActuatorDebugInfo(QString& debugInfo, const QString& associationInfo) const;

    /**
     * @brief 生成硬件配置树节点的tooltip
     * @param nodeName 节点名称
     * @param item 树节点项
     * @return 硬件树节点tooltip
     */
    QString GenerateHardwareTreeNodeTooltip(const QString& nodeName, QTreeWidgetItem* item);

    /**
     * @brief 生成试验配置树节点的tooltip
     * @param nodeName 节点名称
     * @param associationInfo 关联信息
     * @param item 树节点项
     * @return 试验配置树节点tooltip
     */
    QString GenerateTestConfigTreeNodeTooltip(const QString& nodeName, const QString& associationInfo, QTreeWidgetItem* item);

    /**
     * @brief 生成硬件通道详细信息
     * @param parentName 父节点名称
     * @param channelName 通道名称
     * @return 硬件通道详细信息
     */
    QString GenerateHardwareChannelDetailedInfo(const QString& parentName, const QString& channelName);

    /**
     * @brief 生成作动器设备详细信息
     * @param deviceName 设备名称
     * @return 作动器设备详细信息
     */
    QString GenerateActuatorDeviceDetailedInfo(const QString& deviceName);

    /**
     * @brief 生成传感器设备详细信息
     * @param deviceName 设备名称
     * @return 传感器设备详细信息
     */
    QString GenerateSensorDeviceDetailedInfo(const QString& deviceName);

    /**
     * @brief 生成硬件配置根节点tooltip
     * @param nodeName 节点名称
     * @param item 树节点项
     * @return 硬件配置根节点tooltip
     */
    QString GenerateHardwareRootNodeTooltip(const QString& nodeName, QTreeWidgetItem* item);

    /**
     * @brief 生成作动器根节点tooltip
     * @param item 树节点项
     * @return 作动器根节点tooltip
     */
    QString GenerateActuatorRootNodeTooltip(QTreeWidgetItem* item);

    /**
     * @brief 生成传感器根节点tooltip
     * @param item 树节点项
     * @return 传感器根节点tooltip
     */
    QString GenerateSensorRootNodeTooltip(QTreeWidgetItem* item);

    /**
     * @brief 生成硬件节点资源根节点tooltip
     * @param item 树节点项
     * @return 硬件节点资源根节点tooltip
     */
    QString GenerateHardwareNodeResourceRootTooltip(QTreeWidgetItem* item);

    /**
     * @brief 生成硬件树通用节点tooltip（不显示关联信息）
     * @param nodeName 节点名称
     * @param item 树节点项
     * @return 硬件树通用节点tooltip
     */
    QString GenerateHardwareGenericNodeTooltip(const QString& nodeName, QTreeWidgetItem* item);

    /**
     * @brief 验证导入数据的完整性
     * @return 验证是否通过
     */
    bool ValidateImportedData();

public:
    // 公共方法，供自定义控件使用
    bool canDragItemPublic(QTreeWidgetItem* item) const { return canDragItem(item); }
    QString getItemTypePublic(QTreeWidgetItem* item) const { return getItemType(item); }
    bool canAcceptDropPublic(QTreeWidgetItem* targetItem, const QString& sourceType) const { return canAcceptDrop(targetItem, sourceType); }
    void handleDragDropAssociationPublic(QTreeWidgetItem* targetItem, const QString& sourceText, const QString& sourceType) { HandleDragDropAssociation(targetItem, sourceText, sourceType); }
    void handleDragDropAssociationWithParentPublic(QTreeWidgetItem* targetItem, const QString& sourceText, const QString& sourceType, const QString& parentText) { HandleDragDropAssociationWithParent(targetItem, sourceText, sourceType, parentText); }

    /**
     * @brief 公共日志记录方法（供自定义控件调用）
     * @param level 日志级别
     * @param message 日志消息
     */
    void LogMessage(const QString& level, const QString& message);

    // ==================== ActuatorViewModel1_2业务逻辑集成方法 ====================

    /**
     * @brief 安全获取作动器视图模型，如果为空则自动重新创建
     * @return 作动器视图模型指针，如果创建失败返回nullptr
     */
    ActuatorViewModel1_2* getSafeActuatorViewModel();

    /**
     * @brief 连接ActuatorViewModel1_2的业务信号
     */
    void connectActuatorViewModelSignals();

    /**
     * @brief 创建作动器组UI节点
     * @param groupName 组名称
     * @param groupId 组ID
     */
    void CreateActuatorGroupUI(const QString& groupName, int groupId);

    /**
     * @brief 创建作动器设备UI节点
     * @param serialNumber 序列号
     * @param groupName 组名称
     */
    void CreateActuatorDeviceUI(const QString& serialNumber, const QString& groupName);

    /**
     * @brief 查找作动器组节点
     * @param groupName 组名称
     * @return 作动器组节点，未找到返回nullptr
     */
    QTreeWidgetItem* FindActuatorGroupItem(const QString& groupName);

    /**
     * @brief 更新作动器设备显示
     * @param serialNumber 序列号
     */
    void UpdateActuatorDeviceDisplay(const QString& serialNumber);

    /**
     * @brief 导出工程为channel_config.json格式
     */
    void exportProjectAsChannelConfig();

    /**
     * @brief 统一的项目数据导出接口（带通道配置）
     * @param filePath 文件路径
     * @return 导出成功返回true
     */
    bool exportProjectData_1_2(const QString& filePath);

    bool integrateChannelConfigToProject_1_2();
    QJsonObject createControlChannelGroupsJson_1_2();

    // ============================================================================
    // 🆕 新增：数据同步修复方案方法声明
    // ============================================================================
    
    /**
     * @brief 同步所有数据管理器，确保数据一致性
     * @return 同步是否成功
     */
    bool SynchronizeAllDataManagers();
    
    /**
     * @brief 同步硬件节点数据（核心修复）
     * @return 同步是否成功
     */
    bool SynchronizeHardwareNodeData();
    
    /**
     * @brief 同步作动器数据
     * @return 同步是否成功
     */
    bool SynchronizeActuatorData();
    
    /**
     * @brief 同步传感器数据
     * @return 同步是否成功
     */
    bool SynchronizeSensorData();
    
    // ============================================================================
    // 🆕 新增：控制通道详细信息显示管理（公共接口）
    // ============================================================================
    
    /**
     * @brief 获取控制通道参数
     * @param channelName 通道名称
     * @return 控制通道参数，如果未找到返回默认参数
     */
    UI::ControlChannelParams GetControlChannelParams(const QString& channelName);

    /**
     * @brief 生成作动器设备详细信息（带组ID的精准版本）
     * @param deviceName 设备名称
     * @param groupId 组ID
     * @return 详细信息字符串
     */
    QString GenerateActuatorDeviceDetailedInfo(const QString& deviceName, int groupId);

    /**
     * @brief 传感器编辑后更新控制通道关联信息
     * @param serialNumber 传感器序列号
     * @param updatedParams 更新后的传感器参数
     */
    void UpdateControlChannelAssociationsAfterSensorEdit(const QString& serialNumber, const UI::SensorParams_1_2& updatedParams);

    /**
     * @brief 传感器删除后更新控制通道关联信息（增强版，支持组名验证）
     * @param serialNumber 已删除的传感器序列号
     * @param groupName 已删除的传感器所属组名（用于精确匹配，防止误删）
     */
    void UpdateControlChannelAssociationsAfterSensorDelete(const QString& serialNumber, const QString& groupName);
    
//    /**
//     * @brief 传感器删除后更新控制通道关联信息（兼容版，仅检查序列号）
//     * @param serialNumber 已删除的传感器序列号
//     * @note 建议使用带组名参数的版本以避免误删
//     */
//    void UpdateControlChannelAssociationsAfterSensorDelete(const QString& serialNumber);

    /**
     * @brief 作动器编辑后更新控制通道关联信息
     * @param serialNumber 作动器序列号
     * @param updatedParams 更新后的作动器参数
     */
    void UpdateControlChannelAssociationsAfterActuatorEdit(const QString& serialNumber, const UI::ActuatorParams_1_2& updatedParams);

    /**
     * @brief 作动器删除后更新控制通道关联信息（增强版，支持组名验证）
     * @param serialNumber 已删除的作动器序列号
     * @param groupName 作动器所属组名（用于精确匹配）
     */
    void UpdateControlChannelAssociationsAfterActuatorDelete(const QString& serialNumber, const QString& groupName);

    /**
     * @brief 作动器删除后更新控制通道关联信息（兼容版，仅检查序列号）
     * @param serialNumber 已删除的作动器序列号
     */
    void UpdateControlChannelAssociationsAfterActuatorDelete(const QString& serialNumber);

    /**
     * @brief 硬件节点编辑后更新控制通道关联信息
     * @param oldNodeName 旧硬件节点名称
     * @param newNodeName 新硬件节点名称
     * @param channels 更新后的通道信息
     */
    void UpdateControlChannelAssociationsAfterHardwareNodeEdit(const QString& oldNodeName, const QString& newNodeName, const QList<UI::ChannelInfo>& channels);

    /**
     * @brief 硬件节点删除后更新控制通道关联信息
     * @param nodeName 已删除的硬件节点名称
     */
    void UpdateControlChannelAssociationsAfterHardwareNodeDelete(const QString& nodeName);

    /**
     * @brief 同步控制通道关联信息从数据管理器到界面显示
     * @details 确保界面上的"关联信息"列与数据管理器中的数据一致
     */
    void SynchronizeControlChannelAssociationsToUI();

    /**
     * @brief 同步单个控制通道子节点的关联信息到界面
     * @param channelItem 通道界面节点
     * @param channelData 通道数据对象
     */
    void SynchronizeChannelChildAssociationsToUI(QTreeWidgetItem* channelItem, const UI::ControlChannelParams& channelData);

    /**
     * @brief 根据序列号获取传感器所属的所有组名
     * @param serialNumber 传感器序列号
     * @return 包含该序列号的所有组名列表
     */
    QStringList GetSensorGroupNamesBySerialNumber(const QString& serialNumber) const;
    
    /**
     * @brief 同步控制通道数据
     * @return 同步是否成功
     */
    bool SynchronizeControlChannelData();
    
    /**
     * @brief 同步JSON导出器与数据管理器
     */
    void SynchronizeJSONExporterWithDataManagers();
    
    /**
     * @brief 保存树形控件的展开状态
     * @param tree 目标树形控件
     * @return 节点路径到展开状态的映射
     */
    QMap<QString, bool> saveTreeExpandedStates(QTreeWidget* tree);
    
    /**
     * @brief 恢复树形控件的展开状态
     * @param tree 目标树形控件
     * @param states 节点路径到展开状态的映射
     */
    void restoreTreeExpandedStates(QTreeWidget* tree, const QMap<QString, bool>& states);
    
    /**
     * @brief 获取树形控件项的完整路径
     * @param item 树形控件项
     * @return 从根到该项的完整路径字符串
     */
    QString getItemPath(QTreeWidgetItem* item);
    
    /**
     * @brief 展开指定作动器组在树形控件中的显示
     * @param groupName 作动器组名称
     */
    void expandActuatorGroupInTree(const QString& groupName);
    
    /**
     * @brief 展开包含指定作动器设备的组在树形控件中的显示
     * @param serialNumber 作动器序列号
     */
    void expandActuatorDeviceInTree(const QString& serialNumber);
};

//} // namespace UI
