/**
 * @file test_control_channel_detail_info.cpp
 * @brief 控制通道详细信息显示功能测试
 * @details 测试当实验资源树形控件当前项是"控制通道"时，详细信息面板显示控制通道的详细信息，包括所有子节点（通道）的详细信息
 * <AUTHOR> Assistant
 * @date 2025-01-27
 * @version 1.0.0
 */

#include <QApplication>
#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QMessageBox>
#include <QDebug>
#include <QTreeWidget>
#include <QTreeWidgetItem>
#include <QDockWidget>
#include <QGroupBox>
#include <QLabel>

#include "DetailInfoPanel.h"
#include "DataModels_Fixed.h"

// 测试用的主窗口类
class TestControlChannelDetailInfoWindow : public QMainWindow
{
    Q_OBJECT
    
public:
    TestControlChannelDetailInfoWindow(QWidget *parent = nullptr) : QMainWindow(parent)
    {
        setupUI();
        setupTestData();
        setupConnections();
    }
    
private slots:
    void onTestCH1Clicked()
    {
        testControlChannel("CH1");
    }
    
    void onTestCH2Clicked()
    {
        testControlChannel("CH2");
    }
    
    void onTestControlChannelRootClicked()
    {
        testControlChannelRoot();
    }
    
    void onClearClicked()
    {
        m_detailInfoPanel->clearInfo();
        QMessageBox::information(this, "测试", "已清空详细信息");
    }
    
    void onTreeItemClicked(QTreeWidgetItem* item, int column)
    {
        Q_UNUSED(column)
        
        QString itemText = item->text(0);
        QString itemType = item->text(1);
        
        qDebug() << "树形控件项目被点击:" << itemText << "类型:" << itemType;
        
        if (itemType == "控制通道" || itemText == "控制通道") {
            testControlChannelRoot();
        } else if (itemText == "CH1" || itemText == "CH2") {
            testControlChannel(itemText);
        } else if (itemText == "载荷1" || itemText == "载荷2" || itemText == "位置" || itemText == "控制") {
            // 子节点点击，显示父通道的详细信息
            if (item->parent()) {
                QString parentText = item->parent()->text(0);
                if (parentText == "CH1" || parentText == "CH2") {
                    testControlChannel(parentText);
                }
            }
        }
    }
    
private:
    void setupUI()
    {
        setWindowTitle("控制通道详细信息显示功能测试");
        setMinimumSize(1400, 900);
        resize(1600, 1000);
        
        // 创建中央窗口
        QWidget* centralWidget = new QWidget(this);
        setCentralWidget(centralWidget);
        
        QVBoxLayout* centralLayout = new QVBoxLayout(centralWidget);
        
        // 创建说明标签
        QLabel* infoLabel = new QLabel("🎯 测试说明：点击树形控件中的不同节点，查看详细信息面板的显示效果", this);
        infoLabel->setStyleSheet("QLabel { font-size: 12pt; color: #2c3e50; padding: 10px; background-color: #ecf0f1; border-radius: 5px; }");
        centralLayout->addWidget(infoLabel);
        
        // 创建测试按钮
        QHBoxLayout* buttonLayout = new QHBoxLayout();
        
        QPushButton* testCH1Button = new QPushButton("测试CH1详细信息", this);
        QPushButton* testCH2Button = new QPushButton("测试CH2详细信息", this);
        QPushButton* testRootButton = new QPushButton("测试控制通道根节点", this);
        QPushButton* clearButton = new QPushButton("清空信息", this);
        
        buttonLayout->addWidget(testCH1Button);
        buttonLayout->addWidget(testCH2Button);
        buttonLayout->addWidget(testRootButton);
        buttonLayout->addWidget(clearButton);
        buttonLayout->addStretch();
        
        centralLayout->addLayout(buttonLayout);
        
        // 连接按钮信号
        connect(testCH1Button, &QPushButton::clicked, this, &TestControlChannelDetailInfoWindow::onTestCH1Clicked);
        connect(testCH2Button, &QPushButton::clicked, this, &TestControlChannelDetailInfoWindow::onTestCH2Clicked);
        connect(testRootButton, &QPushButton::clicked, this, &TestControlChannelDetailInfoWindow::onTestControlChannelRootClicked);
        connect(clearButton, &QPushButton::clicked, this, &TestControlChannelDetailInfoWindow::onClearClicked);
        
        // 创建详细信息面板
        m_detailInfoPanel = new DetailInfoPanel(this);
        
        // 创建停靠窗口
        QDockWidget* detailDock = new QDockWidget("控制通道详细信息面板", this);
        detailDock->setWidget(m_detailInfoPanel);
        detailDock->setAllowedAreas(Qt::RightDockWidgetArea | Qt::LeftDockWidgetArea);
        addDockWidget(Qt::RightDockWidgetArea, detailDock);
        
        // 创建模拟实验资源树形控件
        QGroupBox* treeGroup = new QGroupBox("🧪 实验资源树形控件", this);
        QVBoxLayout* treeLayout = new QVBoxLayout(treeGroup);
        
        QLabel* treeInfoLabel = new QLabel("点击树形控件中的节点，查看详细信息面板的显示效果", this);
        treeInfoLabel->setStyleSheet("QLabel { color: #7f8c8d; font-size: 10pt; }");
        treeLayout->addWidget(treeInfoLabel);
        
        m_testTree = new QTreeWidget(this);
        m_testTree->setHeaderLabels(QStringList() << "节点名称" << "类型" << "状态");
        m_testTree->setAlternatingRowColors(true);
        m_testTree->setMinimumHeight(300);
        
        // 添加测试节点
        setupTestTree();
        
        treeLayout->addWidget(m_testTree);
        centralLayout->addWidget(treeGroup);
        
        // 连接树形控件选择信号
        connect(m_testTree, &QTreeWidget::itemClicked, this, &TestControlChannelDetailInfoWindow::onTreeItemClicked);
    }
    
    void setupTestTree()
    {
        // 添加测试节点
        QTreeWidgetItem* rootItem = new QTreeWidgetItem(m_testTree);
        rootItem->setText(0, "实验");
        rootItem->setText(1, "实验根节点");
        rootItem->setText(2, "在线");
        rootItem->setExpanded(true);
        
        QTreeWidgetItem* controlChannelItem = new QTreeWidgetItem(rootItem);
        controlChannelItem->setText(0, "控制通道");
        controlChannelItem->setText(1, "控制通道组");
        controlChannelItem->setText(2, "在线");
        controlChannelItem->setExpanded(true);
        
        QTreeWidgetItem* ch1Item = new QTreeWidgetItem(controlChannelItem);
        ch1Item->setText(0, "CH1");
        ch1Item->setText(1, "控制通道");
        ch1Item->setText(2, "在线");
        ch1Item->setExpanded(true);
        
        // CH1的子节点
        QTreeWidgetItem* load1Item = new QTreeWidgetItem(ch1Item);
        load1Item->setText(0, "载荷1");
        load1Item->setText(1, "载荷传感器");
        load1Item->setText(2, "已关联");
        
        QTreeWidgetItem* load2Item = new QTreeWidgetItem(ch1Item);
        load2Item->setText(0, "载荷2");
        load2Item->setText(1, "载荷传感器");
        load2Item->setText(2, "已关联");
        
        QTreeWidgetItem* positionItem = new QTreeWidgetItem(ch1Item);
        positionItem->setText(0, "位置");
        positionItem->setText(1, "位置传感器");
        positionItem->setText(2, "已关联");
        
        QTreeWidgetItem* controlItem = new QTreeWidgetItem(ch1Item);
        controlItem->setText(0, "控制");
        controlItem->setText(1, "控制作动器");
        controlItem->setText(2, "已关联");
        
        QTreeWidgetItem* ch2Item = new QTreeWidgetItem(controlChannelItem);
        ch2Item->setText(0, "CH2");
        ch2Item->setText(1, "控制通道");
        ch2Item->setText(2, "在线");
        ch2Item->setExpanded(true);
        
        // CH2的子节点
        QTreeWidgetItem* load1Item2 = new QTreeWidgetItem(ch2Item);
        load1Item2->setText(0, "载荷1");
        load1Item2->setText(1, "载荷传感器");
        load1Item2->setText(2, "未关联");
        
        QTreeWidgetItem* load2Item2 = new QTreeWidgetItem(ch2Item);
        load2Item2->setText(0, "载荷2");
        load2Item2->setText(1, "载荷传感器");
        load2Item2->setText(2, "未关联");
        
        QTreeWidgetItem* positionItem2 = new QTreeWidgetItem(ch2Item);
        positionItem2->setText(0, "位置");
        positionItem2->setText(1, "位置传感器");
        positionItem2->setText(2, "未关联");
        
        QTreeWidgetItem* controlItem2 = new QTreeWidgetItem(ch2Item);
        controlItem2->setText(0, "控制");
        controlItem2->setText(1, "控制作动器");
        controlItem2->setText(2, "未关联");
    }
    
    void setupTestData()
    {
        // 初始状态为空
        m_detailInfoPanel->clearInfo();
    }
    
    void setupConnections()
    {
        // 连接详细信息面板信号
        connect(m_detailInfoPanel, &DetailInfoPanel::editConfigRequested,
                this, &TestControlChannelDetailInfoWindow::onEditConfigRequested);
        connect(m_detailInfoPanel, &DetailInfoPanel::viewHistoryRequested,
                this, &TestControlChannelDetailInfoWindow::onViewHistoryRequested);
        connect(m_detailInfoPanel, &DetailInfoPanel::exportInfoRequested,
                this, &TestControlChannelDetailInfoWindow::onExportInfoRequested);
    }
    
    void testControlChannel(const QString& channelName)
    {
        qDebug() << "测试控制通道详细信息显示:" << channelName;
        
        // 创建测试用的控制通道参数
        UI::ControlChannelParams testParams;
        testParams.channelId = channelName.toStdString();
        testParams.channelName = channelName.toStdString();
        testParams.hardwareAssociation = "LD-B1 - " + channelName.toStdString();
        testParams.load1Sensor = "载荷传感器组 - 载荷传感器_001";
        testParams.load2Sensor = "载荷传感器组 - 载荷传感器_002";
        testParams.positionSensor = "位置传感器组 - 位置传感器_001";
        testParams.controlActuator = "伺服作动器组 - 伺服作动器1_1";
        testParams.lc_id = 1;
        testParams.station_id = 1;
        testParams.enable = true;
        testParams.control_mode = 4; // 力控制
        testParams.servo_control_polarity = 1;
        testParams.payload_sensor1_polarity = 1;
        testParams.payload_sensor2_polarity = 1;
        testParams.position_sensor_polarity = 1;
        
        // 使用DetailInfoPanel的静态方法创建NodeInfo
        NodeInfo nodeInfo = DetailInfoPanel::createControlChannelNodeInfo(
            channelName, 
            QString::fromStdString(testParams.channelId), 
            testParams
        );
        
        // 设置详细信息到面板
        m_detailInfoPanel->setNodeInfo(nodeInfo);
        
        QMessageBox::information(this, "测试", 
            QString("已显示控制通道 %1 的详细信息，包含 %2 个子节点")
            .arg(channelName).arg(nodeInfo.subNodes.size()));
    }
    
    void testControlChannelRoot()
    {
        qDebug() << "测试控制通道根节点详细信息显示";
        
        // 创建包含多个通道的控制通道组信息
        NodeInfo rootNodeInfo;
        rootNodeInfo.nodeName = "控制通道";
        rootNodeInfo.nodeType = "控制通道组";
        rootNodeInfo.status = NodeStatus::Online;
        
        // 创建CH1和CH2的子节点信息
        QList<SubNodeInfo> subNodes;
        
        // CH1子节点
        SubNodeInfo ch1Node;
        ch1Node.name = "CH1";
        ch1Node.type = "控制通道";
        ch1Node.deviceName = "LD-B1 - CH1";
        ch1Node.deviceId = "CH1";
        ch1Node.isConnected = true;
        ch1Node.setProperty("状态", "已启用");
        ch1Node.setProperty("子节点数量", "4个");
        ch1Node.setProperty("关联设备", "3个");
        subNodes.append(ch1Node);
        
        // CH2子节点
        SubNodeInfo ch2Node;
        ch2Node.name = "CH2";
        ch2Node.type = "控制通道";
        ch2Node.deviceName = "LD-B1 - CH2";
        ch2Node.deviceId = "CH2";
        ch2Node.isConnected = true;
        ch2Node.setProperty("状态", "已启用");
        ch2Node.setProperty("子节点数量", "4个");
        ch2Node.setProperty("关联设备", "0个");
        subNodes.append(ch2Node);
        
        rootNodeInfo.subNodes = subNodes;
        
        // 设置详细信息到面板
        m_detailInfoPanel->setNodeInfo(rootNodeInfo);
        
        QMessageBox::information(this, "测试", 
            QString("已显示控制通道根节点的详细信息，包含 %1 个子通道")
            .arg(subNodes.size()));
    }
    
    void onEditConfigRequested(const NodeInfo& nodeInfo)
    {
        QMessageBox::information(this, "编辑配置", 
            QString("请求编辑节点配置: %1").arg(nodeInfo.nodeName));
    }
    
    void onViewHistoryRequested(const NodeInfo& nodeInfo)
    {
        QMessageBox::information(this, "查看历史", 
            QString("请求查看节点历史: %1").arg(nodeInfo.nodeName));
    }
    
    void onExportInfoRequested(const NodeInfo& nodeInfo)
    {
        QMessageBox::information(this, "导出信息", 
            QString("请求导出节点信息: %1").arg(nodeInfo.nodeName));
    }
    
private:
    DetailInfoPanel* m_detailInfoPanel;
    QTreeWidget* m_testTree;
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    TestControlChannelDetailInfoWindow window;
    window.show();
    
    qDebug() << "控制通道详细信息显示功能测试程序已启动";
    qDebug() << "功能说明:";
    qDebug() << "1. 点击树形控件中的'控制通道'节点，查看根节点详细信息";
    qDebug() << "2. 点击树形控件中的'CH1'或'CH2'节点，查看通道详细信息";
    qDebug() << "3. 点击子节点（载荷1、载荷2、位置、控制），查看父通道详细信息";
    qDebug() << "4. 使用测试按钮快速测试不同场景";
    qDebug() << "5. 在详细信息面板中查看完整的通道信息和子节点信息";
    
    return app.exec();
}

#include "test_control_channel_detail_info.moc" 