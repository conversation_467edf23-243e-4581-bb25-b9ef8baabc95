@echo off
chcp 65001 >nul
echo ========================================
echo 🎯 载荷1传感器选择列全面修复验证
echo ========================================
echo.

echo 📁 当前目录: %CD%
echo.

echo 🔍 检查修复状态...
echo.

REM 检查BasicInfoWidget.cpp中的修复
echo 📋 检查BasicInfoWidget.cpp修复状态...
findstr /n "载荷1传感器选择" src\BasicInfoWidget.cpp >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 载荷1传感器选择列属性键名已修复
) else (
    echo ❌ 载荷1传感器选择列属性键名未修复
)

findstr /n "已配置" src\BasicInfoWidget.cpp >nul 2>&1
if %errorlevel% equ 0 (
    echo ⚠️  发现硬编码的"已配置"文本，需要进一步检查
) else (
    echo ✅ 所有硬编码的"已配置"文本已修复
)

echo.

REM 检查Qt环境
echo 🔍 检查Qt环境...
where qmake >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: Qt环境未找到！
    echo 请确保Qt已安装并添加到PATH环境变量中
    echo.
    pause
    exit /b 1
)

echo ✅ Qt环境检查通过
echo.

REM 清理之前的构建
echo 🧹 清理之前的构建...
if exist debug rmdir /s /q debug
if exist release rmdir /s /q release
if exist *.o del *.o
if exist *.exe del *.exe
echo ✅ 清理完成
echo.

REM 生成Makefile
echo 🔧 生成Makefile...
qmake test_load1_sensor_comprehensive.pro
if %errorlevel% neq 0 (
    echo ❌ 错误: qmake失败！
    echo.
    pause
    exit /b 1
)
echo ✅ Makefile生成成功
echo.

REM 编译项目
echo 🚀 开始编译全面测试程序...
mingw32-make
if %errorlevel% neq 0 (
    echo ❌ 编译失败！
    echo.
    echo 📋 编译错误信息：
    echo.
    mingw32-make 2>&1
    echo.
    echo 🔍 请检查以下可能的问题：
    echo 1. 源代码语法错误
    echo 2. 头文件路径问题
    echo 3. 依赖库缺失
    echo.
    pause
    exit /b 1
)

echo ✅ 编译成功！
echo.

REM 检查生成的可执行文件
echo 🔍 检查生成的文件...
if exist debug\test_load1_sensor_comprehensive.exe (
    echo ✅ 全面测试程序生成成功: debug\test_load1_sensor_comprehensive.exe
    echo.
    echo 🎯 载荷1传感器选择列全面修复验证程序已准备就绪！
    echo.
    echo 📋 修复内容总结：
    echo ✅ 修复了updateBasicInfoTable函数中的属性键名不一致问题
    echo ✅ 修复了addControlChannelRow函数中的硬编码"已配置"问题
    echo ✅ 统一使用"载荷1传感器选择"作为属性键名
    echo ✅ 空值数据现在正确显示为空而不是"已配置"
    echo ✅ 有数据的行正确显示数据内容
    echo ✅ 所有相关列（载荷1、载荷2、位置、控制作动器）都得到修复
    echo.
    echo 🚀 运行全面测试程序...
    echo.
    start debug\test_load1_sensor_comprehensive.exe
) else (
    echo ❌ 错误: 可执行文件未生成！
    echo.
    pause
    exit /b 1
)

echo.
echo 🎉 载荷1传感器选择列全面修复验证完成！
echo.
echo 📋 测试说明：
echo 1. 程序启动后会显示详细信息面板和测试按钮组
echo 2. 点击不同的测试按钮验证各种修复场景：
echo    - 🧪 测试载荷1传感器选择列修复
echo    - 🔍 测试空值显示（应该显示为空）
echo    - 📊 测试有数据显示（应该显示数据）
echo    - 🔄 测试混合数据（空值+有数据）
echo    - 🌟 测试所有相关列修复
echo 3. 检查载荷1传感器选择列是否正确显示数据
echo 4. 确认空值显示为空而不是"已配置"
echo.
echo 🔍 修复验证要点：
echo - 有数据显示数据：正确显示传感器选择的具体内容
echo - 没有数据显示为空：空值不再显示为"已配置"等误导性文本
echo - 所有相关列都得到修复：载荷1、载荷2、位置、控制作动器
echo.
pause 