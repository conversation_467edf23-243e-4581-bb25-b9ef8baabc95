/**
 * @file HardwareNodeResDataManager.cpp
 * @brief 硬件节点资源数据管理器实现
 * @details 管理硬件节点配置的存储、检索和操作，使用原有的NodeConfigParams结构体
 * <AUTHOR> Agent
 * @date 2024-01-15
 * @version 1.0.0
 */

#include "HardwareNodeResDataManager.h"
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QFile>
#include <QTextStream>
#include <QStandardPaths>
#include <QDir>
#include <algorithm>

HardwareNodeResDataManager::HardwareNodeResDataManager(QObject* parent)
    : QObject(parent) {
    qDebug() << "HardwareNodeResDataManager 初始化完成";
}

HardwareNodeResDataManager::~HardwareNodeResDataManager() {
    QMutexLocker locker(&dataMutex_);
    nodeConfigStorage_.clear();
    qDebug() << "HardwareNodeResDataManager 析构完成";
}

// ============================================================================
// 硬件节点配置管理方法
// ============================================================================

bool HardwareNodeResDataManager::addHardwareNodeConfig(const UI::NodeConfigParams& nodeConfig) {
    QMutexLocker locker(&dataMutex_);
    
    if (!validateNodeConfig(nodeConfig)) {
        qDebug() << "硬件节点配置数据验证失败";
        return false;
    }
    
    // 检查节点名称是否已存在
    if (nodeConfigStorage_.contains(nodeConfig.nodeName)) {
        qDebug() << QString("硬件节点 %1 已存在").arg(nodeConfig.nodeName);
        return false;
    }

    nodeConfigStorage_[nodeConfig.nodeName] = nodeConfig;

    // 🆕 新增：维护添加顺序
    if (!nodeAdditionOrder_.contains(nodeConfig.nodeName)) {
        nodeAdditionOrder_.append(nodeConfig.nodeName);
    }

    qDebug() << QString("添加硬件节点配置成功: 名称=%1, 通道数=%2, 添加顺序=%3")
                .arg(nodeConfig.nodeName)
                .arg(nodeConfig.channelCount)
                .arg(nodeAdditionOrder_.size());

    emit hardwareNodeConfigAdded(nodeConfig.nodeName);
    return true;
}

bool HardwareNodeResDataManager::updateHardwareNodeConfig(const UI::NodeConfigParams& nodeConfig) {
    QMutexLocker locker(&dataMutex_);
    
    if (!validateNodeConfig(nodeConfig)) {
        qDebug() << "硬件节点配置数据验证失败";
        return false;
    }
    
    if (!nodeConfigStorage_.contains(nodeConfig.nodeName)) {
        qDebug() << QString("硬件节点 %1 不存在").arg(nodeConfig.nodeName);
        return false;
    }
    
    nodeConfigStorage_[nodeConfig.nodeName] = nodeConfig;
    
    qDebug() << QString("更新硬件节点配置成功: 名称=%1")
                .arg(nodeConfig.nodeName);
    
    emit hardwareNodeConfigUpdated(nodeConfig.nodeName);
    return true;
}

bool HardwareNodeResDataManager::addOrUpdateHardwareNodeConfig(const UI::NodeConfigParams& nodeConfig, const QString& oldNodeName) {
    QMutexLocker locker(&dataMutex_);
    
    if (!validateNodeConfig(nodeConfig)) {
        qDebug() << "硬件节点配置数据验证失败";
        return false;
    }
    
    bool isUpdate = nodeConfigStorage_.contains(nodeConfig.nodeName);
    bool isNameChange = !oldNodeName.isEmpty() && oldNodeName != nodeConfig.nodeName;
    
    // 更新或添加配置
    nodeConfigStorage_[nodeConfig.nodeName] = nodeConfig;
    
    // 处理添加顺序
    if (isNameChange && nodeAdditionOrder_.contains(oldNodeName)) {
        // 节点名称改变：先从存储中删除旧配置，然后替换列表中的旧名称为新名称，保持位置不变
        if (nodeConfigStorage_.contains(oldNodeName)) {
            nodeConfigStorage_.remove(oldNodeName);
        }
        int oldIndex = nodeAdditionOrder_.indexOf(oldNodeName);
        nodeAdditionOrder_[oldIndex] = nodeConfig.nodeName;
        qDebug() << QString("节点名称从 %1 更改为 %2，保持原位置 %3")
                    .arg(oldNodeName).arg(nodeConfig.nodeName).arg(oldIndex);
    } else if (!isUpdate && !nodeAdditionOrder_.contains(nodeConfig.nodeName)) {
        // 新增节点：添加到列表末尾
        nodeAdditionOrder_.append(nodeConfig.nodeName);
    }
    
    qDebug() << QString("%1硬件节点配置成功: 名称=%2, 通道数=%3")
                .arg(isUpdate || isNameChange ? "更新" : "添加")
                .arg(nodeConfig.nodeName)
                .arg(nodeConfig.channelCount);
    
    if (isUpdate || isNameChange) {
        emit hardwareNodeConfigUpdated(nodeConfig.nodeName);
    } else {
        emit hardwareNodeConfigAdded(nodeConfig.nodeName);
    }
    
    return true;
}

bool HardwareNodeResDataManager::removeHardwareNodeConfig(const QString& nodeName) {
    QMutexLocker locker(&dataMutex_);
    
    if (!nodeConfigStorage_.contains(nodeName)) {
        qDebug() << QString("硬件节点 %1 不存在").arg(nodeName);
        return false;
    }
    
    nodeConfigStorage_.remove(nodeName);

    // 🆕 新增：从添加顺序列表中移除
    nodeAdditionOrder_.removeAll(nodeName);

    qDebug() << QString("删除硬件节点配置成功: 名称=%1").arg(nodeName);

    emit hardwareNodeConfigRemoved(nodeName);
    return true;
}

QList<UI::NodeConfigParams> HardwareNodeResDataManager::getAllNodeConfigs() const {
    QMutexLocker locker(&dataMutex_);

    QList<UI::NodeConfigParams> configs;

    // 🆕 修改：按添加顺序返回硬件节点配置
    for (const QString& nodeName : nodeAdditionOrder_) {
        if (nodeConfigStorage_.contains(nodeName)) {
            configs.append(nodeConfigStorage_[nodeName]);
        }
    }

    qDebug() << QString("获取所有硬件节点配置: 共 %1 个节点 (按添加顺序)").arg(configs.size());
    return configs;
}

QList<UI::NodeConfigParams> HardwareNodeResDataManager::getAllHardwareNodeConfigs() const {
    return getAllNodeConfigs();
}

UI::NodeConfigParams HardwareNodeResDataManager::getHardwareNodeConfig(const QString& nodeId) const {
    QMutexLocker locker(&dataMutex_);
    
    if (nodeConfigStorage_.contains(nodeId)) {
        return nodeConfigStorage_[nodeId];
    }
    
    qDebug() << QString("硬件节点 %1 不存在，返回空配置").arg(nodeId);
    return UI::NodeConfigParams(); // 返回默认构造的空配置
}

// ============================================================================
// 数据统计和查询方法
// ============================================================================

int HardwareNodeResDataManager::getTotalNodeCount() const {
    QMutexLocker locker(&dataMutex_);
    return nodeConfigStorage_.size();
}

bool HardwareNodeResDataManager::hasNodeConfig(const QString& nodeName) const {
    QMutexLocker locker(&dataMutex_);
    return nodeConfigStorage_.contains(nodeName);
}

void HardwareNodeResDataManager::clearAllData() {
    QMutexLocker locker(&dataMutex_);
    
    int nodeCount = nodeConfigStorage_.size();
    nodeConfigStorage_.clear();
    
    // 🆕 修复：同时清空添加顺序列表
    nodeAdditionOrder_.clear();
    
    qDebug() << QString("清空所有硬件节点数据: 清除了 %1 个节点").arg(nodeCount);
}

void HardwareNodeResDataManager::clearAllHardwareNodeConfigs() {
    clearAllData();
}

// ============================================================================
// 数据导入导出方法
// ============================================================================

bool HardwareNodeResDataManager::exportToCSV(const QString& filePath) const {
    QMutexLocker locker(&dataMutex_);
    
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        qDebug() << QString("无法创建CSV文件: %1").arg(filePath);
        return false;
    }
    
    QTextStream out(&file);
    out.setCodec("UTF-8");
    out.setGenerateByteOrderMark(true);
    
    // 写入CSV头部
    out << "# 硬件节点配置文件\n";
    out << "# 导出时间," << QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss") << "\n";
    out << "\n";
    
    // 写入表头
    out << "节点名称,通道数量,通道详情\n";

    // 🆕 修复：按添加顺序写入数据，而不是按QMap的键值排序
    for (const QString& nodeName : nodeAdditionOrder_) {
        if (nodeConfigStorage_.contains(nodeName)) {
            const UI::NodeConfigParams& config = nodeConfigStorage_[nodeName];

            // 构建通道详情字符串
            QString channelDetails;
            for (int i = 0; i < config.channels.size(); ++i) {
                const UI::ChannelInfo& channel = config.channels[i];
                if (i > 0) channelDetails += ";";
                channelDetails += QString("CH%1:%2:%3").arg(channel.channelId).arg(channel.ipAddress).arg(channel.port);
            }

            out << QString("%1,%2,%3\n")
                   .arg(config.nodeName)
                   .arg(config.channelCount)
                   .arg(channelDetails);
        }
    }
    
    file.close();
    qDebug() << QString("硬件节点数据导出到CSV成功: %1").arg(filePath);
    return true;
}

// 🚫 已注释：独立JSON导出功能已废弃
//bool HardwareNodeResDataManager::exportToJSON(const QString& filePath) const {
//    QMutexLocker locker(&dataMutex_);
//    
//    QJsonObject rootObj;
//    rootObj["exportTime"] = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
//    rootObj["version"] = "1.0.0";
//    rootObj["description"] = "硬件节点配置数据";
//    
//    // 🆕 修复：按添加顺序导出JSON数据
//    QJsonArray nodesArray;
//    for (const QString& nodeName : nodeAdditionOrder_) {
//        if (nodeConfigStorage_.contains(nodeName)) {
//            const UI::NodeConfigParams& config = nodeConfigStorage_[nodeName];
//
//            QJsonObject nodeObj;
//            nodeObj["nodeName"] = config.nodeName;
//            nodeObj["channelCount"] = config.channelCount;
//
//            // 添加通道信息
//            QJsonArray channelsArray;
//            for (const UI::ChannelInfo& channel : config.channels) {
//                QJsonObject channelObj;
//                channelObj["channelId"] = channel.channelId;
//                channelObj["ipAddress"] = channel.ipAddress;
//                channelObj["port"] = channel.port;
//                channelsArray.append(channelObj);
//            }
//            nodeObj["channels"] = channelsArray;
//
//            nodesArray.append(nodeObj);
//        }
//    }
//    rootObj["hardwareNodes"] = nodesArray;
//    
//    QJsonDocument doc(rootObj);
//    
//    QFile file(filePath);
//    if (!file.open(QIODevice::WriteOnly)) {
//        qDebug() << QString("无法创建JSON文件: %1").arg(filePath);
//        return false;
//    }
//    
//    file.write(doc.toJson());
//    file.close();
//    
//    qDebug() << QString("硬件节点数据导出到JSON成功: %1").arg(filePath);
//    return true;
//}

bool HardwareNodeResDataManager::importFromJSON(const QString& filePath) {
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        qDebug() << QString("无法打开JSON文件: %1").arg(filePath);
        return false;
    }
    
    QByteArray data = file.readAll();
    file.close();
    
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(data, &error);
    if (error.error != QJsonParseError::NoError) {
        qDebug() << QString("JSON解析失败: %1").arg(error.errorString());
        return false;
    }
    
    QJsonObject rootObj = doc.object();
    QJsonArray nodesArray = rootObj["hardwareNodes"].toArray();
    
    QMutexLocker locker(&dataMutex_);
    
    // 清空现有数据
    nodeConfigStorage_.clear();
    
    // 导入数据
    for (const QJsonValue& nodeValue : nodesArray) {
        QJsonObject nodeObj = nodeValue.toObject();

        UI::NodeConfigParams config;
        config.nodeName = nodeObj["nodeName"].toString();
        config.channelCount = nodeObj["channelCount"].toInt();

        // 导入通道信息
        QJsonArray channelsArray = nodeObj["channels"].toArray();
        config.channels.clear();
        for (const QJsonValue& channelValue : channelsArray) {
            QJsonObject channelObj = channelValue.toObject();
            UI::ChannelInfo channel;
            channel.channelId = channelObj["channelId"].toInt();
            channel.ipAddress = channelObj["ipAddress"].toString();
            channel.port = channelObj["port"].toInt();
            config.channels.append(channel);
        }

        nodeConfigStorage_[config.nodeName] = config;
    }
    
    qDebug() << QString("从JSON导入硬件节点数据成功: %1 个节点")
                .arg(nodeConfigStorage_.size());
    return true;
}

// ============================================================================
// 私有辅助方法
// ============================================================================

bool HardwareNodeResDataManager::validateNodeConfig(const UI::NodeConfigParams& nodeConfig) const {
    // 检查节点名称是否为空
    if (nodeConfig.nodeName.isEmpty()) {
        qDebug() << "硬件节点名称不能为空";
        return false;
    }
    
    // 检查通道数量是否有效
    if (nodeConfig.channelCount <= 0) {
        qDebug() << "硬件节点通道数量必须大于0";
        return false;
    }
    
    return true;
}
