@echo off
echo ========================================
echo  测试传感器组ID分配修复
echo ========================================

REM 设置Qt环境变量
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目（测试传感器组ID分配修复）...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo 请检查上面的错误信息。
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！传感器组ID分配问题已修复
    echo ========================================
    
    echo.
    echo ✅ 修复的问题:
    echo - 解决了"传感器组ID必须大于0"的验证错误
    echo - 解决了"传感器组必须包含至少一个传感器"的验证错误
    echo - 实现了传感器组的延迟创建机制
    echo - 参考作动器流程优化了传感器组管理
    echo.
    echo 🔧 修复方案:
    echo 1. 传感器组UI创建：只创建UI节点，不创建数据
    echo 2. 传感器组数据创建：在第一个传感器添加时创建
    echo 3. 组ID生成：使用哈希算法生成唯一ID
    echo 4. 延迟验证：避免空组的验证问题
    echo.
    echo 🎯 修复逻辑:
    echo - CreateSensorGroup() → 只创建UI节点
    echo - OnCreateSensor() → 触发传感器组数据创建
    echo - createOrUpdateSensorGroup() → 智能创建或更新组
    echo - generateSensorGroupIdFromName() → 生成唯一组ID
    echo.
    echo 📝 参考作动器流程的改进:
    echo - 延迟创建：组数据在需要时才创建
    echo - ID生成：使用哈希算法而不是依赖组名格式
    echo - 验证优化：避免空组验证问题
    echo - 流程统一：与作动器流程保持一致
    echo.
    
    if exist "SiteResConfig.exe" (
        echo 启动程序验证修复...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 启动程序验证修复...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 启动程序验证修复...
        start release\SiteResConfig.exe
    )
)

echo.
echo 📖 详细测试指南:
echo.
echo 🎮 传感器组创建测试:
echo 1. 启动软件后，新建一个项目
echo 2. 在硬件树中右键"传感器"节点
echo 3. 选择"新建" → "传感器组"
echo 4. 选择"载荷"类型，创建"载荷_传感器组"
echo 5. 观察日志：应显示"创建传感器组: 载荷_传感器组"（不再有保存失败错误）
echo 6. 在"载荷_传感器组"上右键，选择"新建传感器"
echo 7. 填写传感器参数，保存
echo 8. 观察日志：应显示"传感器组保存成功: 载荷_传感器组 (组ID: XXXX, 传感器数量: 1)"
echo.
echo 🎮 组ID生成测试:
echo 1. 创建多个不同名称的传感器组
echo 2. 在每个组中添加传感器
echo 3. 验证每个组都分配了唯一的ID
echo 4. 验证ID在1000-9999范围内
echo 5. 验证相同组名生成相同ID（一致性）
echo.
echo 🎮 延迟创建测试:
echo 1. 创建传感器组但不添加传感器 → 应该成功（只有UI节点）
echo 2. 添加第一个传感器 → 应该触发组数据创建
echo 3. 添加第二个传感器 → 应该更新现有组数据
echo 4. 验证组数据的完整性和正确性
echo.
echo ✅ 预期结果:
echo - 传感器组创建成功，无ID验证错误
echo - 传感器添加成功，组数据正确创建
echo - 组ID自动生成且唯一
echo - 传感器组管理功能完整可用
echo - 与作动器流程保持一致
echo.
echo 🚨 如果测试失败:
echo - 检查是否还有"传感器组ID必须大于0"错误
echo - 验证组ID生成算法是否正常
echo - 确认延迟创建机制正确
echo - 检查传感器组数据完整性
echo.
pause
