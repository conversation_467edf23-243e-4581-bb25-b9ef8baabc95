/**
 * @file LogManager.h
 * @brief 日志管理模块 - 基于现有AddLogEntry方法
 * @details 封装现有的AddLogEntry方法，提供统一的日志管理接口
 * <AUTHOR> Assistant
 * @date 2025-01-27
 * @version 3.4.0
 */

#ifndef LOGMANAGER_H
#define LOGMANAGER_H

#include <QObject>
#include <QString>
#include <QStringList>

// 前向声明
class CMyMainWindow;

/**
 * @brief 日志管理器类 - 基于现有AddLogEntry方法
 * @details 封装现有的AddLogEntry方法，提供统一的日志管理接口
 */
class LogManager : public QObject {
    Q_OBJECT
    
public:
    explicit LogManager(QObject* parent = nullptr);
    ~LogManager();
    
    // 封装现有AddLogEntry方法
    void debug(const QString& message);
    void info(const QString& message);
    void warning(const QString& message);
    void error(const QString& message);
    void success(const QString& message);

    // 🆕 新增：日志管理功能
    bool saveLogs();
    bool saveLogsToFile(const QString& filePath);
    QStringList getLogsByLevel(const QString& level) const;
    QStringList searchLogs(const QString& keyword) const;
    
    // 🆕 新增：日志过滤功能
    void setLogLevelFilter(const QStringList& enabledLevels);
    bool isLogLevelEnabled(const QString& level) const;
    
    // 🆕 新增：日志统计结构体
    struct LogStats {
        int totalLogs = 0;
        int debugLogs = 0;
        int infoLogs = 0;
        int warningLogs = 0;
        int errorLogs = 0;
        int successLogs = 0;
    };
    
    // 🆕 新增：获取日志统计
    LogStats getLogStats() const;

    // 清理日志
    void clearLogs();
    
    // 获取所有日志
    QStringList getLogs() const;
    
    // 设置主窗口引用
    void setMainWindow(CMyMainWindow* mainWindow);

signals:
    void logAdded(const QString& level, const QString& message);
    void logsCleared();
    void logsSaved();
    void logsSavedToFile(const QString& filePath);
    void logError(const QString& error);
    void logLevelFilterChanged(const QStringList& enabledLevels);

private:
    CMyMainWindow* mainWindow_;
    QStringList logs_;
    QStringList enabledLogLevels_;  // 🆕 新增：启用的日志级别过滤
    
    QString formatLogEntry(const QString& level, const QString& message);
};

#endif // LOGMANAGER_H 