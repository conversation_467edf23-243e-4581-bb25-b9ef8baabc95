/* 树形控件 - 完全Windows原生风格（参考截图） */
QTreeWidget, QTreeView {
    font-family: "Microsoft YaHei", "微软雅黑", "Segoe UI", "Tahoma", sans-serif;
    font-size: 11pt; /* 加大字体 */
    color: #000000;
    background-color: #FFFFFF;
    border: 1px solid #C0C0C0; /* Windows经典细边框 */
    border-radius: 0px;
    outline: none;
    show-decoration-selected: 1;
    alternate-background-color: transparent; /* 不使用交替背景，保持纯白 */
    selection-background-color: #0078D4; /* Windows 10标准蓝色 */
    selection-color: #FFFFFF;
    padding: 0px;
    gridline-color: transparent; /* 隐藏网格线 */
}

/* 树形控件获得焦点时的边框 - Windows原生 */
QTreeWidget:focus, QTreeView:focus {
    border: 1px solid #0078D4;
}

/* 树形控件项目基础样式 - 完全Windows原生 */
QTreeWidget::item, QTreeView::item {
    height: 22px; /* 增加行高以适应更大字体 */
    border: none;
    padding: 2px 4px 2px 2px; /* 增加内边距 */
    margin: 0px;
    background-color: transparent;
    color: #000000;
}

/* 悬停效果 - Windows原生浅蓝色 */
QTreeWidget::item:hover, QTreeView::item:hover {
    background-color: #E1ECFF; /* Windows原生悬停色 */
    color: #000000;
}

/* 选中状态 - Windows原生蓝色（与截图一致） */
QTreeWidget::item:selected, QTreeView::item:selected {
    background-color: #0078D4; /* Windows 10标准选中蓝色 */
    color: #FFFFFF;
}

/* 选中且悬停 - Windows原生 */
QTreeWidget::item:selected:hover, QTreeView::item:hover:selected {
    background-color: #106EBE; /* 稍深的蓝色 */
    color: #FFFFFF;
}

/* 失去焦点时的选中状态 - Windows原生灰色 */
QTreeWidget::item:selected:!active, QTreeView::item:selected:!active {
    background-color: #CCCCCC; /* Windows原生非活动选中 */
    color: #000000;
}

/* 拖拽源状态 - 保持现代感但降低对比度 */
QTreeWidget::item:selected:active, QTreeView::item:selected:active {
    background-color: #FF8C42; /* 温和的橙色 */
    color: #FFFFFF;
}

/* 拖拽目标指示器 - Windows原生 */
QTreeWidget::drop-indicator, QTreeView::drop-indicator {
    background-color: #0078D4;
    height: 1px; /* 更细的指示线 */
    border: none;
}

/* 禁用状态 - Windows原生 */
QTreeWidget::item:disabled, QTreeView::item:disabled {
    color: #6D6D6D;
    background-color: transparent;
}

/* 不可用的树形控件整体样式 - Windows原生 */
QTreeWidget:disabled, QTreeView:disabled {
    background-color: #F0F0F0;
    color: #6D6D6D;
    border-color: #ADADAD;
}

/* 不可用状态下的项目 */
QTreeWidget:disabled::item, QTreeView:disabled::item {
    color: #8B949E;
    background-color: transparent;
}

/* 不可用状态下的项目悬停（无效果） */
QTreeWidget:disabled::item:hover, QTreeView:disabled::item:hover {
    background-color: transparent;
    color: #8B949E;
}

/* 分支线基础样式 - Windows原生简洁风格 */
QTreeWidget::branch, QTreeView::branch {
    background: transparent;
    border: none;
    margin: 0px;
    padding: 0px;
    width: 16px; /* 增加分支区域宽度 */
    height: 22px; /* 与新的行高一致 */
}

/* 垂直连接线 - Windows经典虚线 */
QTreeWidget::branch:has-siblings:!adjoins-item,
QTreeView::branch:has-siblings:!adjoins-item {
    border-right: 1px dotted #808080; /* Windows经典虚线 */
}

/* L型连接线 - Windows经典虚线 */
QTreeWidget::branch:has-siblings:adjoins-item,
QTreeView::branch:has-siblings:adjoins-item {
    border-right: 1px dotted #808080;
    border-bottom: 1px dotted #808080;
}

/* 末端连接线 - Windows经典虚线 */
QTreeWidget::branch:!has-children:!has-siblings:adjoins-item,
QTreeView::branch:!has-children:!has-siblings:adjoins-item {
    border-bottom: 1px dotted #808080;
}

/* 展开/折叠按钮 - 更大尺寸，虚线穿过中间 */
QTreeWidget::branch:has-children,
QTreeView::branch:has-children {
    border-image: none;
    background: transparent;
    border: none;
    width: 13px;  /* 增加按钮宽度 */
    height: 13px; /* 增加按钮高度 */
    margin: 4px;  /* 调整边距 */
}

/* 悬停效果 - 按钮背景变色 */
QTreeWidget::branch:has-children:!has-siblings:closed:hover,
QTreeView::branch:has-children:!has-siblings:closed:hover,
QTreeWidget::branch:closed:has-children:has-siblings:hover,
QTreeView::branch:closed:has-children:has-siblings:hover {
    background-color: #E1ECFF;
    border-color: #0078D4;
}

QTreeWidget::branch:open:has-children:!has-siblings:hover,
QTreeView::branch:open:has-children:!has-siblings:hover,
QTreeWidget::branch:open:has-children:has-siblings:hover,
QTreeView::branch:open:has-children:has-siblings:hover {
    background-color: #E1ECFF;
    border-color: #0078D4;
}

/* 禁用状态的连接线 - Windows经典虚线 */
QTreeWidget:disabled::branch, QTreeView:disabled::branch {
    border-color: #C0C0C0;
    border-style: dotted; /* 确保禁用状态也是虚线 */
}

/* 禁用状态的展开按钮 - Windows风格 */
QTreeWidget:disabled::branch:has-children:closed,
QTreeView:disabled::branch:has-children:closed,
QTreeWidget:disabled::branch:open:has-children,
QTreeView:disabled::branch:open:has-children {
    background-color: #F5F5F5;
    border-color: #C0C0C0;
}

/* 禁用状态的加号/减号 */
QTreeWidget:disabled::branch:has-children:before,
QTreeView:disabled::branch:has-children:before {
    color: #A0A0A0; /* 灰色的加号/减号 */
}

/* 禁用状态下的按钮不响应悬停 */
QTreeWidget:disabled::branch:hover, QTreeView:disabled::branch:hover {
    background-color: #F5F5F5;
    border-color: #C0C0C0;
}

/* 拖拽悬停目标高亮 - Windows原生风格 */
QTreeWidget::item[dragover="true"], QTreeView::item[dragover="true"] {
    background-color: #E1ECFF; /* Windows原生拖拽目标色 */
    border: 1px dashed #0078D4; /* 更细的虚线边框 */
    color: #000000;
}

/* 编辑状态 - Windows原生风格 */
QTreeWidget::item:editing, QTreeView::item:editing {
    background-color: #FFFFFF;
    border: 1px solid #0078D4;
    color: #000000;
    padding: 1px 4px 1px 2px; /* 与普通项目一致的内边距 */
}

/* 根节点和叶子节点 - Windows原生（无特殊样式） */
QTreeWidget::item:has-children:open, QTreeView::item:has-children:open,
QTreeWidget::item:!has-children, QTreeView::item:!has-children {
    font-weight: normal;
    color: #000000;
    margin-left: 0px; /* Windows原生：无额外缩进 */
}

/* 树形控件头部 - Windows原生风格 */
QTreeWidget::header, QTreeView::header {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #F8F8F8, stop:1 #E8E8E8); /* Windows原生表头渐变 */
    border-bottom: 1px solid #C0C0C0;
    border-right: 1px solid #C0C0C0;
    font-weight: normal;
    color: #000000;
    padding: 3px 6px; /* Windows原生内边距 */
    height: 18px; /* 与行高一致 */
}

/* 树形控件头部悬停 - Windows原生 */
QTreeWidget::header:hover, QTreeView::header:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #E1ECFF, stop:1 #D0E7FF);
}