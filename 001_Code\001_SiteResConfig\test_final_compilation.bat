@echo off
echo ========================================
echo  最终编译测试 - 所有错误修复验证
echo ========================================

REM 设置Qt环境变量
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目（最终验证）...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo 请检查上面的错误信息。
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！所有错误已修复
    echo ========================================
    
    echo.
    echo ✅ 已修复的编译错误总结:
    echo.
    echo 🔧 DataManager接口错误:
    echo - ActuatorDataManager.cpp:59 getAllActuatorDetailedParams 错误
    echo - ActuatorDataManager.cpp:342 second 成员错误
    echo - ActuatorDataManager.cpp:389 getAllActuatorDetailedParams 错误
    echo.
    echo 🔧 DataManager指针类型错误:
    echo - MainWindow_Qt_Simple.cpp:735 setSensorDataManager 参数类型错误
    echo - MainWindow_Qt_Simple.cpp:736 setActuatorDataManager 参数类型错误
    echo - MainWindow_Qt_Simple.cpp:1811 setSensorDataManager 参数类型错误
    echo - MainWindow_Qt_Simple.cpp:1812 setActuatorDataManager 参数类型错误
    echo.
    echo 🔧 UI控件不存在错误:
    echo - MainWindow_Qt_Simple.cpp:7822 systemOverviewLabel 控件不存在
    echo - MainWindow_Qt_Simple.cpp:7872 refreshHardwareButton 控件不存在
    echo - MainWindow_Qt_Simple.cpp:7881 dataTableWidget 控件不存在
    echo.
    echo 🔧 菜单项不存在错误:
    echo - MainWindow_Qt_Simple.cpp:7891 actionCreateData 菜单项不存在
    echo - MainWindow_Qt_Simple.cpp:7892 actionManualControl 菜单项不存在
    echo - MainWindow_Qt_Simple.cpp:7893 actionDataTemplate 菜单项不存在
    echo.
    echo 🎯 项目状态管理功能已完全集成:
    echo - 软件启动时操作区禁用
    echo - 新建/打开项目后操作区启用
    echo - 项目关闭后重新禁用操作区
    echo - 状态信息实时显示
    echo.
    
    if exist "SiteResConfig.exe" (
        echo 可执行文件: SiteResConfig.exe
        echo 启动程序验证功能...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
        echo 启动程序验证功能...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
        echo 启动程序验证功能...
        start release\SiteResConfig.exe
    )
)

echo.
echo 📖 功能验证指南:
echo.
echo 🎮 测试步骤:
echo 1. 启动软件 - 观察硬件树和试验配置树是否禁用
echo 2. 观察状态标签是否显示"没有项目"提示
echo 3. 新建项目 - 观察操作区是否启用
echo 4. 观察状态标签是否显示"项目已就绪"
echo 5. 清空界面 - 观察操作区是否重新禁用
echo.
echo 🎯 核心功能:
echo - DataManager接口完全集成
echo - 项目状态管理完全实现
echo - UI控件状态动态管理
echo - 菜单项状态智能控制
echo.
pause
