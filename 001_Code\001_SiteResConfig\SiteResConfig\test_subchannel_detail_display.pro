QT += core gui widgets

TARGET = test_subchannel_detail_display
TEMPLATE = app

# 源文件
SOURCES += test_subchannel_detail_display.cpp

# 头文件
HEADERS += 

# 包含路径
INCLUDEPATH += include
INCLUDEPATH += src

# 库文件路径
LIBS += -L$$PWD -lSiteResConfig

# 编译选项
CONFIG += debug
CONFIG += c++11

# 输出目录
DESTDIR = $$PWD/bin

# 依赖库
win32 {
    LIBS += -lQt5Core -lQt5Gui -lQt5Widgets
} else {
    LIBS += -lQtCore -lQtGui -lQtWidgets
} 