/**
 * @file main_qt.cpp
 * @brief SiteResConfig Qt版本主函数入口
 * <AUTHOR> Team
 * @date 2025-08-05
 * 
 * Qt版本的主函数，用于启动基于Qt框架的GUI应用程序
 */

#include <QApplication>
#include <QDir>
#include <QStandardPaths>
#include <QStyleFactory>
#include <QTranslator>
#include <QLocale>
#include <QMessageBox>
#include <QSplashScreen>
#include <QPixmap>
#include <QTimer>
#include <QDebug>
#include <QPainter>
#include <QFont>
#include <QPalette>
#include <QSysInfo>

#include "MainWindow_Qt_Simple.h"
#include "Common_Fixed.h"

// 前向声明
namespace UI {
    class MainWindow;
}

///**
// * @brief 设置应用程序样式
// */
//void SetupApplicationStyle(QApplication& app) {
//    // 设置应用程序样式
//    app.setStyle(QStyleFactory::create("Fusion"));
    
//    // 设置现代化的调色板
//    QPalette darkPalette;
//    darkPalette.setColor(QPalette::Window, QColor(53, 53, 53));
//    darkPalette.setColor(QPalette::WindowText, Qt::white);
//    darkPalette.setColor(QPalette::Base, QColor(25, 25, 25));
//    darkPalette.setColor(QPalette::AlternateBase, QColor(53, 53, 53));
//    darkPalette.setColor(QPalette::ToolTipBase, Qt::white);
//    darkPalette.setColor(QPalette::ToolTipText, Qt::white);
//    darkPalette.setColor(QPalette::Text, Qt::white);
//    darkPalette.setColor(QPalette::Button, QColor(53, 53, 53));
//    darkPalette.setColor(QPalette::ButtonText, Qt::white);
//    darkPalette.setColor(QPalette::BrightText, Qt::red);
//    darkPalette.setColor(QPalette::Link, QColor(42, 130, 218));
//    darkPalette.setColor(QPalette::Highlight, QColor(42, 130, 218));
//    darkPalette.setColor(QPalette::HighlightedText, Qt::black);
    
//    // 应用调色板
//    app.setPalette(darkPalette);
    
////    // 设置全局样式表
////    app.setStyleSheet(
////        "QToolTip { "
////        "color: #ffffff; "
////        "background-color: #2a82da; "
////        "border: 1px solid white; "
////        "}"
        
////        "QMenuBar { "
////        "background-color: #353535; "
////        "color: white; "
////        "}"
        
////        "QMenuBar::item { "
////        "background: transparent; "
////        "}"
        
////        "QMenuBar::item:selected { "
////        "background-color: #2a82da; "
////        "}"
        
////        "QMenu { "
////        "background-color: #353535; "
////        "color: white; "
////        "border: 1px solid #555555; "
////        "}"
        
////        "QMenu::item:selected { "
////        "background-color: #2a82da; "
////        "}"
        
////        "QStatusBar { "
////        "background-color: #353535; "
////        "color: white; "
////        "}"
        
////        "QPushButton { "
////        "background-color: #0078d4; "
////        "color: white; "
////        "border: none; "
////        "padding: 6px 12px; "
////        "border-radius: 4px; "
////        "font-weight: bold; "
////        "}"
        
////        "QPushButton:hover { "
////        "background-color: #106ebe; "
////        "}"
        
////        "QPushButton:pressed { "
////        "background-color: #005a9e; "
////        "}"
        
////        "QPushButton:disabled { "
////        "background-color: #cccccc; "
////        "color: #666666; "
////        "}"
////    );
//}

/**
 * @brief 设置国际化支持
 */
void SetupInternationalization(QApplication& app) {
    // 创建翻译器
    QTranslator* translator = new QTranslator(&app);
    
    // 获取系统语言
    QString locale = QLocale::system().name();
    
    // 加载翻译文件
    QString translationPath = QApplication::applicationDirPath() + "/translations";
    if (translator->load("siteresconfig_" + locale, translationPath)) {
        app.installTranslator(translator);
        qDebug() << "Loaded translation for locale:" << locale;
    } else {
        qDebug() << "No translation found for locale:" << locale;
    }
}

/**
 * @brief 显示启动画面
 */
QSplashScreen* ShowSplashScreen() {
    // 创建启动画面
    QPixmap pixmap(400, 300);
    pixmap.fill(QColor(53, 53, 53));
    
    QPainter painter(&pixmap);
    painter.setPen(Qt::white);
    painter.setFont(QFont("Arial", 16, QFont::Bold));
    
    // 绘制标题
    painter.drawText(pixmap.rect(), Qt::AlignCenter, 
        "SiteResConfig\n灵动加载上位机管理软件\n\n正在启动...");
    
    QSplashScreen* splash = new QSplashScreen(pixmap);
    splash->show();
    
    return splash;
}

/**
 * @brief 检查系统环境
 */
bool CheckSystemEnvironment() {
    // 检查Qt版本
    QString qtVersion = qVersion();
    qDebug() << "Qt Version:" << qtVersion;
    
    // 检查操作系统
    qDebug() << "OS:" << QSysInfo::prettyProductName();
    qDebug() << "Architecture:" << QSysInfo::currentCpuArchitecture();
    
    // 检查应用程序目录
    QString appDir = QApplication::applicationDirPath();
    qDebug() << "Application Directory:" << appDir;
    
    // 检查配置目录
    QString configDir = QStandardPaths::writableLocation(QStandardPaths::AppConfigLocation);
    QDir().mkpath(configDir);
    qDebug() << "Config Directory:" << configDir;
    
    return true;
}

/**
 * @brief 主函数
 */
int main(int argc, char *argv[])
{
    // 创建Qt应用程序
    QApplication app(argc, argv);
    
    // 设置应用程序信息
    app.setApplicationName("SiteResConfig");
    app.setApplicationDisplayName(u8"SiteResConfig - 灵动加载上位机管理软件");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("SiteResConfig Team");
    app.setOrganizationDomain("siteresconfig.com");
    
    // 检查系统环境
    if (!CheckSystemEnvironment()) {
        QMessageBox::critical(nullptr, u8"系统错误", 
            u8"系统环境检查失败，程序无法启动。");
        return -1;
    }
    
    // 显示启动画面
    QSplashScreen* splash = ShowSplashScreen();
    app.processEvents();
    
    // 设置应用程序样式
    splash->showMessage(u8"正在加载样式...", Qt::AlignBottom | Qt::AlignCenter, Qt::white);
    app.processEvents();
    //SetupApplicationStyle(app);
    
    // 设置国际化
    splash->showMessage(u8"正在加载语言包...", Qt::AlignBottom | Qt::AlignCenter, Qt::white);
    app.processEvents();
    SetupInternationalization(app);
    
    // 创建主窗口
    splash->showMessage(u8"正在初始化主窗口...", Qt::AlignBottom | Qt::AlignCenter, Qt::white);
    app.processEvents();
    
    /*MyDlg::*/CMyMainWindow* mainWindow = nullptr;
    try {
        mainWindow = new /*MyDlg::*/CMyMainWindow();
        
        // 初始化主窗口
        splash->showMessage(u8"正在加载配置...", Qt::AlignBottom | Qt::AlignCenter, Qt::white);
        app.processEvents();
        
        if (!mainWindow->Initialize()) {
            delete mainWindow;
            splash->close();
            delete splash;
            
            QMessageBox::critical(nullptr, u8"初始化错误", 
                u8"主窗口初始化失败，程序无法启动。");
            return -1;
        }
        
        // 显示主窗口
        splash->showMessage(u8"启动完成", Qt::AlignBottom | Qt::AlignCenter, Qt::white);
        app.processEvents();
        
        // 延迟一下让用户看到启动完成信息
        QTimer::singleShot(500, [=]() {
            splash->finish(mainWindow);
            mainWindow->show();
            delete splash;
        });
        
    } catch (const std::exception& e) {
        if (splash) {
            splash->close();
            delete splash;
        }
        
        QString errorMsg = QString(u8"程序启动时发生异常：\n%1").arg(e.what());
        QMessageBox::critical(nullptr, u8"启动错误", errorMsg);
        return -1;
    } catch (...) {
        if (splash) {
            splash->close();
            delete splash;
        }
        
        QMessageBox::critical(nullptr, u8"启动错误", 
            u8"程序启动时发生未知异常。");
        return -1;
    }
    
    // 运行应用程序事件循环
    int result = app.exec();
    
    // 清理资源
    if (mainWindow) {
        delete mainWindow;
    }
    
    qDebug() << "Application exited with code:" << result;
    return result;
}
