#include "TreeManager.h"
#include "CustomTreeWidgets.h"
#include "MainWindow_Qt_Simple.h"
#include <QDebug>
#include <QTreeWidget>

TreeManager::TreeManager(QObject* parent)
    : QObject(parent)
    , hardwareTreeWidget_(nullptr)
    , testConfigTreeWidget_(nullptr)
    , mainWindow_(nullptr)
{
    qDebug() << "TreeManager: 初始化树形控件管理器 (基于现有树形控件组件)";
}

TreeManager::~TreeManager()
{
    disconnectTreeSignals();
    qDebug() << "TreeManager: 树形控件管理器已销毁";
}

void TreeManager::setHardwareTreeWidget(CustomHardwareTreeWidget* treeWidget)
{
    if (hardwareTreeWidget_) {
        // 断开旧的信号连接
        disconnect(hardwareTreeWidget_, nullptr, this, nullptr);
    }

    hardwareTreeWidget_ = treeWidget;
    
    if (hardwareTreeWidget_) {
        connectHardwareTreeSignals();
        qDebug() << "TreeManager: 已设置硬件树形控件";
    }
}

void TreeManager::setTestConfigTreeWidget(CustomTestConfigTreeWidget* treeWidget)
{
    if (testConfigTreeWidget_) {
        // 断开旧的信号连接
        disconnect(testConfigTreeWidget_, nullptr, this, nullptr);
    }

    testConfigTreeWidget_ = treeWidget;
    
    if (testConfigTreeWidget_) {
        connectTestConfigTreeSignals();
        qDebug() << "TreeManager: 已设置测试配置树形控件";
    }
}

void TreeManager::setMainWindow(CMyMainWindow* mainWindow)
{
    mainWindow_ = mainWindow;
    if (mainWindow_) {
        qDebug() << "TreeManager: 已设置主窗口引用";
    }
}

void TreeManager::refreshHardwareTree()
{
    if (!hardwareTreeWidget_) {
        qWarning() << "TreeManager: 硬件树形控件未设置，无法刷新";
        return;
    }

    try {
        // 通过主窗口刷新硬件树
        if (mainWindow_) {
            mainWindow_->RefreshHardwareTreeFromDataManagers();
        }
        emit hardwareTreeRefreshed();
        qDebug() << "TreeManager: 硬件树形控件已刷新";
    }
    catch (const std::exception& e) {
        qCritical() << "TreeManager: 刷新硬件树时发生异常:" << e.what();
    }
}

void TreeManager::addHardwareNode(const QString& nodeName)
{
    if (!hardwareTreeWidget_) {
        qWarning() << "TreeManager: 硬件树形控件未设置，无法添加节点";
        return;
    }

    try {
        // 这里可以根据实际的CustomHardwareTreeWidget API调用相应方法
        // hardwareTreeWidget_->addNode(nodeName);
        qDebug() << "TreeManager: 尝试添加硬件节点:" << nodeName;
        
        // 刷新树形控件以确保显示最新状态
        refreshHardwareTree();
    }
    catch (const std::exception& e) {
        qCritical() << "TreeManager: 添加硬件节点时发生异常:" << e.what();
    }
}

void TreeManager::removeHardwareNode(const QString& nodeName)
{
    if (!hardwareTreeWidget_) {
        qWarning() << "TreeManager: 硬件树形控件未设置，无法移除节点";
        return;
    }

    try {
        QTreeWidgetItem* item = findHardwareNode(nodeName);
        if (item) {
            delete item;
            qDebug() << "TreeManager: 已移除硬件节点:" << nodeName;
        }
        else {
            qWarning() << "TreeManager: 未找到要移除的硬件节点:" << nodeName;
        }
    }
    catch (const std::exception& e) {
        qCritical() << "TreeManager: 移除硬件节点时发生异常:" << e.what();
    }
}

void TreeManager::updateHardwareNode(const QString& nodeName)
{
    if (!hardwareTreeWidget_) {
        qWarning() << "TreeManager: 硬件树形控件未设置，无法更新节点";
        return;
    }

    try {
        QTreeWidgetItem* item = findHardwareNode(nodeName);
        if (item) {
            // 这里可以更新节点的显示数据
            // item->setText(0, nodeName);
            qDebug() << "TreeManager: 已更新硬件节点:" << nodeName;
        }
        else {
            qWarning() << "TreeManager: 未找到要更新的硬件节点:" << nodeName;
        }
    }
    catch (const std::exception& e) {
        qCritical() << "TreeManager: 更新硬件节点时发生异常:" << e.what();
    }
}

void TreeManager::refreshTestConfigTree()
{
    if (!testConfigTreeWidget_) {
        qWarning() << "TreeManager: 测试配置树形控件未设置，无法刷新";
        return;
    }

    try {
        // 通过主窗口刷新测试配置树
        if (mainWindow_) {
            mainWindow_->RefreshTestConfigTreeFromDataManagers();
        }
        emit testConfigTreeRefreshed();
        qDebug() << "TreeManager: 测试配置树形控件已刷新";
    }
    catch (const std::exception& e) {
        qCritical() << "TreeManager: 刷新测试配置树时发生异常:" << e.what();
    }
}

void TreeManager::addTestConfigItem(const QString& itemName, const QVariant& data)
{
    if (!testConfigTreeWidget_) {
        qWarning() << "TreeManager: 测试配置树形控件未设置，无法添加项目";
        return;
    }

    try {
        // 这里可以根据实际的CustomTestConfigTreeWidget API调用相应方法
        // testConfigTreeWidget_->addItem(itemName, data);
        qDebug() << "TreeManager: 尝试添加测试配置项目:" << itemName;
        
        // 刷新树形控件以确保显示最新状态
        refreshTestConfigTree();
    }
    catch (const std::exception& e) {
        qCritical() << "TreeManager: 添加测试配置项目时发生异常:" << e.what();
    }
}

void TreeManager::removeTestConfigItem(const QString& itemName)
{
    if (!testConfigTreeWidget_) {
        qWarning() << "TreeManager: 测试配置树形控件未设置，无法移除项目";
        return;
    }

    try {
        QTreeWidgetItem* item = findTestConfigItem(itemName);
        if (item) {
            delete item;
            qDebug() << "TreeManager: 已移除测试配置项目:" << itemName;
        }
        else {
            qWarning() << "TreeManager: 未找到要移除的测试配置项目:" << itemName;
        }
    }
    catch (const std::exception& e) {
        qCritical() << "TreeManager: 移除测试配置项目时发生异常:" << e.what();
    }
}

void TreeManager::updateTestConfigItem(const QString& itemName, const QVariant& data)
{
    if (!testConfigTreeWidget_) {
        qWarning() << "TreeManager: 测试配置树形控件未设置，无法更新项目";
        return;
    }

    try {
        QTreeWidgetItem* item = findTestConfigItem(itemName);
        if (item) {
            // 这里可以更新项目的显示数据
            // item->setData(0, Qt::UserRole, data);
            qDebug() << "TreeManager: 已更新测试配置项目:" << itemName;
        }
        else {
            qWarning() << "TreeManager: 未找到要更新的测试配置项目:" << itemName;
        }
    }
    catch (const std::exception& e) {
        qCritical() << "TreeManager: 更新测试配置项目时发生异常:" << e.what();
    }
}

void TreeManager::handleTreeItemSelection(QTreeWidgetItem* item)
{
    if (!item) return;

    QString treeType;
    if (hardwareTreeWidget_ && hardwareTreeWidget_->indexOfTopLevelItem(item) != -1) {
        treeType = "HardwareTree";
    }
    else if (testConfigTreeWidget_ && testConfigTreeWidget_->indexOfTopLevelItem(item) != -1) {
        treeType = "TestConfigTree";
    }

    emit treeItemSelected(treeType, item);
}

void TreeManager::handleTreeItemDoubleClick(QTreeWidgetItem* item)
{
    if (!item) return;

    QString treeType;
    if (hardwareTreeWidget_ && hardwareTreeWidget_->indexOfTopLevelItem(item) != -1) {
        treeType = "HardwareTree";
    }
    else if (testConfigTreeWidget_ && testConfigTreeWidget_->indexOfTopLevelItem(item) != -1) {
        treeType = "TestConfigTree";
    }

    emit treeItemDoubleClicked(treeType, item);
}

QTreeWidgetItem* TreeManager::getCurrentHardwareItem() const
{
    if (!hardwareTreeWidget_) return nullptr;
    return hardwareTreeWidget_->currentItem();
}

QTreeWidgetItem* TreeManager::getCurrentTestConfigItem() const
{
    if (!testConfigTreeWidget_) return nullptr;
    return testConfigTreeWidget_->currentItem();
}

bool TreeManager::hasHardwareTreeWidget() const
{
    return hardwareTreeWidget_ != nullptr;
}

bool TreeManager::hasTestConfigTreeWidget() const
{
    return testConfigTreeWidget_ != nullptr;
}

void TreeManager::onHardwareTreeItemSelection()
{
    if (!hardwareTreeWidget_) return;
    
    QTreeWidgetItem* item = hardwareTreeWidget_->currentItem();
    emit treeItemSelected("HardwareTree", item);
}

void TreeManager::onHardwareTreeItemDoubleClick()
{
    if (!hardwareTreeWidget_) return;
    
    QTreeWidgetItem* item = hardwareTreeWidget_->currentItem();
    emit treeItemDoubleClicked("HardwareTree", item);
}

void TreeManager::onTestConfigTreeItemSelection()
{
    if (!testConfigTreeWidget_) return;
    
    QTreeWidgetItem* item = testConfigTreeWidget_->currentItem();
    emit treeItemSelected("TestConfigTree", item);
}

void TreeManager::onTestConfigTreeItemDoubleClick()
{
    if (!testConfigTreeWidget_) return;
    
    QTreeWidgetItem* item = testConfigTreeWidget_->currentItem();
    emit treeItemDoubleClicked("TestConfigTree", item);
}

void TreeManager::connectHardwareTreeSignals()
{
    if (!hardwareTreeWidget_) return;

    connect(hardwareTreeWidget_, &QTreeWidget::itemSelectionChanged,
            this, &TreeManager::onHardwareTreeItemSelection);
    connect(hardwareTreeWidget_, &QTreeWidget::itemDoubleClicked,
            this, &TreeManager::onHardwareTreeItemDoubleClick);
}

void TreeManager::connectTestConfigTreeSignals()
{
    if (!testConfigTreeWidget_) return;

    connect(testConfigTreeWidget_, &QTreeWidget::itemSelectionChanged,
            this, &TreeManager::onTestConfigTreeItemSelection);
    connect(testConfigTreeWidget_, &QTreeWidget::itemDoubleClicked,
            this, &TreeManager::onTestConfigTreeItemDoubleClick);
}

void TreeManager::disconnectTreeSignals()
{
    if (hardwareTreeWidget_) {
        disconnect(hardwareTreeWidget_, nullptr, this, nullptr);
    }
    if (testConfigTreeWidget_) {
        disconnect(testConfigTreeWidget_, nullptr, this, nullptr);
    }
}

QTreeWidgetItem* TreeManager::findHardwareNode(const QString& nodeName)
{
    if (!hardwareTreeWidget_) return nullptr;

    for (int i = 0; i < hardwareTreeWidget_->topLevelItemCount(); ++i) {
        QTreeWidgetItem* item = hardwareTreeWidget_->topLevelItem(i);
        if (item && item->text(0) == nodeName) {
            return item;
        }
        
        // 递归查找子项
        for (int j = 0; j < item->childCount(); ++j) {
            QTreeWidgetItem* childItem = item->child(j);
            if (childItem && childItem->text(0) == nodeName) {
                return childItem;
            }
        }
    }
    
    return nullptr;
}

QTreeWidgetItem* TreeManager::findTestConfigItem(const QString& itemName)
{
    if (!testConfigTreeWidget_) return nullptr;

    for (int i = 0; i < testConfigTreeWidget_->topLevelItemCount(); ++i) {
        QTreeWidgetItem* item = testConfigTreeWidget_->topLevelItem(i);
        if (item && item->text(0) == itemName) {
            return item;
        }
        
        // 递归查找子项
        for (int j = 0; j < item->childCount(); ++j) {
            QTreeWidgetItem* childItem = item->child(j);
            if (childItem && childItem->text(0) == itemName) {
                return childItem;
            }
        }
    }
    
    return nullptr;
} 

// 处理硬件树右键菜单
void TreeManager::handleHardwareTreeContextMenu(const QPoint& pos) {
    if (!hardwareTreeWidget_ || !mainWindow_) {
        qWarning() << "TreeManager: 硬件树或主界面指针为空";
        return;
    }
    
    // 委托给主界面处理（保持现有功能）
    mainWindow_->OnHardwareTreeContextMenu(pos);
    qDebug() << "TreeManager: 处理硬件树右键菜单";
}

// 处理测试配置树右键菜单
void TreeManager::handleTestConfigTreeContextMenu(const QPoint& pos) {
    if (!testConfigTreeWidget_ || !mainWindow_) {
        qWarning() << "TreeManager: 测试配置树或主界面指针为空";
        return;
    }
    
    // 委托给主界面处理（保持现有功能）
    mainWindow_->OnTestConfigTreeContextMenu(pos);
    qDebug() << "TreeManager: 处理测试配置树右键菜单";
}

// 处理测试配置树项目双击
void TreeManager::handleTestConfigTreeItemDoubleClicked(QTreeWidgetItem* item, int column) {
    if (!testConfigTreeWidget_ || !mainWindow_) {
        qWarning() << "TreeManager: 测试配置树或主界面指针为空";
        return;
    }
    
    // 委托给主界面处理（保持现有功能）
    mainWindow_->OnTestConfigTreeItemDoubleClicked(item, column);
    qDebug() << "TreeManager: 处理测试配置树项目双击";
}

// 处理测试配置树项目点击
void TreeManager::handleTestConfigTreeItemClicked(QTreeWidgetItem* item, int column) {
    if (!testConfigTreeWidget_ || !mainWindow_) {
        qWarning() << "TreeManager: 测试配置树或主界面指针为空";
        return;
    }
    
    // 委托给主界面处理（保持现有功能）
    mainWindow_->OnTestConfigTreeItemClicked(item, column);
    qDebug() << "TreeManager: 处理测试配置树项目点击";
}

// 强制恢复所有树形控件颜色
void TreeManager::forceRestoreAllTreeColors() {
    try {
        if (hardwareTreeWidget_) {
            CustomHardwareTreeWidget* customTree = 
                qobject_cast<CustomHardwareTreeWidget*>(hardwareTreeWidget_);
            if (customTree) {
                customTree->forceRestoreAllColors();
            }
        }
        
        if (testConfigTreeWidget_) {
            CustomTestConfigTreeWidget* configTree = 
                qobject_cast<CustomTestConfigTreeWidget*>(testConfigTreeWidget_);
            if (configTree) {
                configTree->forceRestoreAllColors();
            }
        }
        
        qDebug() << "TreeManager: 已强制恢复所有树形控件颜色";
        emit treeColorsRestored();
    }
    catch (const std::exception& e) {
        qCritical() << "TreeManager: 恢复树形控件颜色时发生异常:" << e.what();
    }
}

// 更新树形控件显示
void TreeManager::updateTreeDisplay() {
    if (!mainWindow_) {
        qWarning() << "TreeManager: 主界面指针为空，无法更新树形控件显示";
        return;
    }
    
    try {
        qDebug() << "TreeManager: 开始更新树控件显示...";
        
        // 刷新硬件树
        refreshHardwareTree();
        
        // 刷新测试配置树
        refreshTestConfigTree();
        
        // 更新硬件树显示
        if (hardwareTreeWidget_) {
            hardwareTreeWidget_->update();
            hardwareTreeWidget_->expandAll();
        }
        
        // 更新测试配置树显示
        if (testConfigTreeWidget_) {
            testConfigTreeWidget_->update();
            testConfigTreeWidget_->expandAll();
        }
        
        // 强制恢复树控件颜色
        forceRestoreAllTreeColors();
        
        qDebug() << "TreeManager: 树控件显示已更新";
        emit treeDisplayUpdated();
    }
    catch (const std::exception& e) {
        qCritical() << "TreeManager: 更新树形控件显示时发生异常:" << e.what();
    }
}

// 启用测试配置树拖放功能
void TreeManager::enableTestConfigTreeDragDrop() {
    if (!testConfigTreeWidget_) {
        qWarning() << "TreeManager: 测试配置树指针为空，无法启用拖放功能";
        return;
    }
    
    try {
        // 委托给主界面处理（保持现有功能）
        if (mainWindow_) {
            mainWindow_->EnableTestConfigTreeDragDrop();
        }
        qDebug() << "TreeManager: 已启用测试配置树拖放功能";
    }
    catch (const std::exception& e) {
        qCritical() << "TreeManager: 启用测试配置树拖放功能时发生异常:" << e.what();
    }
} 