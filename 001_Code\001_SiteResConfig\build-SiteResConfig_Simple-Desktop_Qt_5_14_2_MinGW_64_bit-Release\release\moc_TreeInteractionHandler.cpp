/****************************************************************************
** Meta object code from reading C++ file 'TreeInteractionHandler.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../001_SiteResConfig_OldStruct/SiteResConfig/include/TreeInteractionHandler.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'TreeInteractionHandler.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_TreeInteractionHandler_t {
    QByteArrayData data[17];
    char stringdata0[243];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_TreeInteractionHandler_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_TreeInteractionHandler_t qt_meta_stringdata_TreeInteractionHandler = {
    {
QT_MOC_LITERAL(0, 0, 22), // "TreeInteractionHandler"
QT_MOC_LITERAL(1, 23, 13), // "onItemEntered"
QT_MOC_LITERAL(2, 37, 0), // ""
QT_MOC_LITERAL(3, 38, 16), // "QTreeWidgetItem*"
QT_MOC_LITERAL(4, 55, 4), // "item"
QT_MOC_LITERAL(5, 60, 6), // "column"
QT_MOC_LITERAL(6, 67, 13), // "onItemClicked"
QT_MOC_LITERAL(7, 81, 22), // "onContextMenuRequested"
QT_MOC_LITERAL(8, 104, 3), // "pos"
QT_MOC_LITERAL(9, 108, 19), // "onItemDoubleClicked"
QT_MOC_LITERAL(10, 128, 18), // "hideTooltipDelayed"
QT_MOC_LITERAL(11, 147, 17), // "editChannelConfig"
QT_MOC_LITERAL(12, 165, 16), // "clearAssociation"
QT_MOC_LITERAL(13, 182, 19), // "clearAllAssociation"
QT_MOC_LITERAL(14, 202, 13), // "deleteChannel"
QT_MOC_LITERAL(15, 216, 13), // "createChannel"
QT_MOC_LITERAL(16, 230, 12) // "onMouseLeave"

    },
    "TreeInteractionHandler\0onItemEntered\0"
    "\0QTreeWidgetItem*\0item\0column\0"
    "onItemClicked\0onContextMenuRequested\0"
    "pos\0onItemDoubleClicked\0hideTooltipDelayed\0"
    "editChannelConfig\0clearAssociation\0"
    "clearAllAssociation\0deleteChannel\0"
    "createChannel\0onMouseLeave"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_TreeInteractionHandler[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      11,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    2,   69,    2, 0x08 /* Private */,
       6,    2,   74,    2, 0x08 /* Private */,
       7,    1,   79,    2, 0x08 /* Private */,
       9,    2,   82,    2, 0x08 /* Private */,
      10,    0,   87,    2, 0x08 /* Private */,
      11,    0,   88,    2, 0x08 /* Private */,
      12,    0,   89,    2, 0x08 /* Private */,
      13,    0,   90,    2, 0x08 /* Private */,
      14,    0,   91,    2, 0x08 /* Private */,
      15,    0,   92,    2, 0x08 /* Private */,
      16,    0,   93,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void, 0x80000000 | 3, QMetaType::Int,    4,    5,
    QMetaType::Void, 0x80000000 | 3, QMetaType::Int,    4,    5,
    QMetaType::Void, QMetaType::QPoint,    8,
    QMetaType::Void, 0x80000000 | 3, QMetaType::Int,    4,    5,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void TreeInteractionHandler::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<TreeInteractionHandler *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->onItemEntered((*reinterpret_cast< QTreeWidgetItem*(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 1: _t->onItemClicked((*reinterpret_cast< QTreeWidgetItem*(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 2: _t->onContextMenuRequested((*reinterpret_cast< const QPoint(*)>(_a[1]))); break;
        case 3: _t->onItemDoubleClicked((*reinterpret_cast< QTreeWidgetItem*(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 4: _t->hideTooltipDelayed(); break;
        case 5: _t->editChannelConfig(); break;
        case 6: _t->clearAssociation(); break;
        case 7: _t->clearAllAssociation(); break;
        case 8: _t->deleteChannel(); break;
        case 9: _t->createChannel(); break;
        case 10: _t->onMouseLeave(); break;
        default: ;
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject TreeInteractionHandler::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_TreeInteractionHandler.data,
    qt_meta_data_TreeInteractionHandler,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *TreeInteractionHandler::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *TreeInteractionHandler::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_TreeInteractionHandler.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int TreeInteractionHandler::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 11)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 11;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 11)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 11;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
