/********************************************************************************
** Form generated from reading UI file 'PIDParametersDialog.ui'
**
** Created by: Qt User Interface Compiler version 5.14.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_PIDPARAMETERSDIALOG_H
#define UI_PIDPARAMETERSDIALOG_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QDialog>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QVBoxLayout>

QT_BEGIN_NAMESPACE

class Ui_PIDParametersDialog
{
public:
    QVBoxLayout *verticalLayout;
    QHBoxLayout *kpLayout;
    QLabel *kpLabel;
    QLineEdit *kpLineEdit;
    QHBoxLayout *kiLayout;
    QLabel *kiLabel;
    QLineEdit *kiLineEdit;
    QHBoxLayout *kdLayout;
    QLabel *kdLabel;
    QLineEdit *kdLineEdit;
    QSpacerItem *verticalSpacer;
    QHBoxLayout *buttonLayout;
    QSpacerItem *horizontalSpacer;
    QPushButton *okButton;
    QPushButton *cancelButton;

    void setupUi(QDialog *PIDParametersDialog)
    {
        if (PIDParametersDialog->objectName().isEmpty())
            PIDParametersDialog->setObjectName(QString::fromUtf8("PIDParametersDialog"));
        PIDParametersDialog->resize(350, 200);
        PIDParametersDialog->setModal(true);
        verticalLayout = new QVBoxLayout(PIDParametersDialog);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        kpLayout = new QHBoxLayout();
        kpLayout->setObjectName(QString::fromUtf8("kpLayout"));
        kpLabel = new QLabel(PIDParametersDialog);
        kpLabel->setObjectName(QString::fromUtf8("kpLabel"));

        kpLayout->addWidget(kpLabel);

        kpLineEdit = new QLineEdit(PIDParametersDialog);
        kpLineEdit->setObjectName(QString::fromUtf8("kpLineEdit"));

        kpLayout->addWidget(kpLineEdit);


        verticalLayout->addLayout(kpLayout);

        kiLayout = new QHBoxLayout();
        kiLayout->setObjectName(QString::fromUtf8("kiLayout"));
        kiLabel = new QLabel(PIDParametersDialog);
        kiLabel->setObjectName(QString::fromUtf8("kiLabel"));

        kiLayout->addWidget(kiLabel);

        kiLineEdit = new QLineEdit(PIDParametersDialog);
        kiLineEdit->setObjectName(QString::fromUtf8("kiLineEdit"));

        kiLayout->addWidget(kiLineEdit);


        verticalLayout->addLayout(kiLayout);

        kdLayout = new QHBoxLayout();
        kdLayout->setObjectName(QString::fromUtf8("kdLayout"));
        kdLabel = new QLabel(PIDParametersDialog);
        kdLabel->setObjectName(QString::fromUtf8("kdLabel"));

        kdLayout->addWidget(kdLabel);

        kdLineEdit = new QLineEdit(PIDParametersDialog);
        kdLineEdit->setObjectName(QString::fromUtf8("kdLineEdit"));

        kdLayout->addWidget(kdLineEdit);


        verticalLayout->addLayout(kdLayout);

        verticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout->addItem(verticalSpacer);

        buttonLayout = new QHBoxLayout();
        buttonLayout->setObjectName(QString::fromUtf8("buttonLayout"));
        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        buttonLayout->addItem(horizontalSpacer);

        okButton = new QPushButton(PIDParametersDialog);
        okButton->setObjectName(QString::fromUtf8("okButton"));

        buttonLayout->addWidget(okButton);

        cancelButton = new QPushButton(PIDParametersDialog);
        cancelButton->setObjectName(QString::fromUtf8("cancelButton"));

        buttonLayout->addWidget(cancelButton);


        verticalLayout->addLayout(buttonLayout);


        retranslateUi(PIDParametersDialog);
        QObject::connect(cancelButton, SIGNAL(clicked()), PIDParametersDialog, SLOT(reject()));

        okButton->setDefault(true);


        QMetaObject::connectSlotsByName(PIDParametersDialog);
    } // setupUi

    void retranslateUi(QDialog *PIDParametersDialog)
    {
        PIDParametersDialog->setWindowTitle(QCoreApplication::translate("PIDParametersDialog", "PID\345\217\202\346\225\260\350\256\276\347\275\256", nullptr));
        kpLabel->setText(QCoreApplication::translate("PIDParametersDialog", "\346\257\224\344\276\213\347\263\273\346\225\260(P):", nullptr));
        kpLineEdit->setText(QCoreApplication::translate("PIDParametersDialog", "1.0", nullptr));
        kpLineEdit->setPlaceholderText(QCoreApplication::translate("PIDParametersDialog", "\350\257\267\350\276\223\345\205\245\346\257\224\344\276\213\347\263\273\346\225\260", nullptr));
        kiLabel->setText(QCoreApplication::translate("PIDParametersDialog", "\347\247\257\345\210\206\347\263\273\346\225\260(I):", nullptr));
        kiLineEdit->setText(QCoreApplication::translate("PIDParametersDialog", "0.1", nullptr));
        kiLineEdit->setPlaceholderText(QCoreApplication::translate("PIDParametersDialog", "\350\257\267\350\276\223\345\205\245\347\247\257\345\210\206\347\263\273\346\225\260", nullptr));
        kdLabel->setText(QCoreApplication::translate("PIDParametersDialog", "\345\276\256\345\210\206\347\263\273\346\225\260(D):", nullptr));
        kdLineEdit->setText(QCoreApplication::translate("PIDParametersDialog", "0.01", nullptr));
        kdLineEdit->setPlaceholderText(QCoreApplication::translate("PIDParametersDialog", "\350\257\267\350\276\223\345\205\245\345\276\256\345\210\206\347\263\273\346\225\260", nullptr));
        okButton->setText(QCoreApplication::translate("PIDParametersDialog", "\350\256\276\347\275\256", nullptr));
        cancelButton->setText(QCoreApplication::translate("PIDParametersDialog", "\345\217\226\346\266\210", nullptr));
    } // retranslateUi

};

namespace Ui {
    class PIDParametersDialog: public Ui_PIDParametersDialog {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_PIDPARAMETERSDIALOG_H
