# 📊 作动器XLSX存储格式 - Unit字段修改版

## 🔍 界面修改分析

基于您的修改：
- **Unit Length** → **Unit**
- **Unit数据需要存储两项数据** → **需要两列存储**

## 📋 新的XLSX存储格式设计

### 工作表：作动器配置表 (Actuator_Configuration)

#### 表头信息区域 (A1:O4)
```
A1: 作动器配置数据表                    | B1-O1: [合并单元格]
A2: 导出时间: 2025-08-14 15:30:00      | B2-O2: [合并单元格]
A3: 说明: 包含完整的作动器参数配置信息    | B3-O3: [合并单元格]
A4: [空行]                            | B4-O4: [空行]
```

#### 数据表头 (A5:O5)
```
A5: 序号
B5: 序列号
C5: 类型
D5: Unit类型
E5: Unit名称
F5: 行程(m)
G5: 位移(m)
H5: 拉伸面积(m²)
I5: 压缩面积(m²)
J5: 极性
K5: Deliver(V)
L5: 频率(Hz)
M5: 输出倍数
N5: 平衡(V)
O5: 备注
```

#### 数据行格式 (A6开始)
```
A6: 1
B6: ACT_001
C6: 单出杆
D6: m
E6: 米
F6: 0.30
G6: 0.30
H6: 0.60
I6: 0.50
J6: Positive
K6: 0.100
L6: 100.00
M6: 1.000
N6: 0.000
O6: 液压系统主作动器
```

## 📊 详细字段规格

### 基本信息字段
| 列 | 字段名 | 数据类型 | 宽度 | 说明 | 示例值 |
|---|--------|----------|------|------|--------|
| A | 序号 | Integer | 6 | 自动递增序号 | 1 |
| B | 序列号 | String | 15 | 作动器唯一标识 | "ACT_001" |
| C | 类型 | String | 10 | 单出杆/双出杆 | "单出杆" |
| O | 备注 | String | 25 | 附加说明信息 | "液压系统主作动器" |

### Unit字段 (两列存储)
| 列 | 字段名 | 数据类型 | 宽度 | 说明 | 示例值 |
|---|--------|----------|------|------|--------|
| D | Unit类型 | String | 10 | 单位类型 | "m", "mm", "cm", "inch" |
| E | Unit名称 | String | 10 | 单位中文名称 | "米", "毫米", "厘米", "英寸" |

### 截面数据字段
| 列 | 字段名 | 数据类型 | 单位 | 精度 | 范围 | 示例值 |
|---|--------|----------|------|------|------|--------|
| F | 行程 | Double | m | 2位小数 | 0.01-10.00 | 0.30 |
| G | 位移 | Double | m | 2位小数 | 0.01-10.00 | 0.30 |
| H | 拉伸面积 | Double | m² | 2位小数 | 0.01-100.00 | 0.60 |
| I | 压缩面积 | Double | m² | 2位小数 | 0.01-100.00 | 0.50 |

### 伺服控制器参数字段
| 列 | 字段名 | 数据类型 | 单位 | 精度 | 范围 | 示例值 |
|---|--------|----------|------|------|------|--------|
| J | 极性 | String | - | - | Positive/Negative | "Positive" |
| K | Deliver | Double | V | 3位小数 | -10.000~10.000 | 0.100 |
| L | 频率 | Double | Hz | 2位小数 | 1.00-10000.00 | 100.00 |
| M | 输出倍数 | Double | - | 3位小数 | 0.001-1000.000 | 1.000 |
| N | 平衡 | Double | V | 3位小数 | -10.000~10.000 | 0.000 |

## 🎨 格式样式

### 表头样式 (A5:O5)
- **背景色**: #4472C4 (深蓝色)
- **字体色**: 白色
- **字体**: 微软雅黑, 11pt, 粗体
- **对齐**: 居中对齐
- **边框**: 全边框, 白色, 1pt

### 数据行样式 (A6开始)
- **奇数行背景**: #F8F9FA (浅灰色)
- **偶数行背景**: 白色
- **字体**: 微软雅黑, 10pt, 常规
- **数值对齐**: 右对齐
- **文本对齐**: 左对齐
- **边框**: 全边框, 灰色, 0.5pt

### 列宽设置
```
A列(序号): 6
B列(序列号): 15
C列(类型): 10
D列(Unit类型): 10
E列(Unit名称): 10
F列(行程): 10
G列(位移): 10
H列(拉伸面积): 12
I列(压缩面积): 12
J列(极性): 10
K列(Deliver): 12
L列(频率): 10
M列(输出倍数): 12
N列(平衡): 10
O列(备注): 25
```

## 📤 Unit字段存储示例

### Unit数据组合示例
| Unit类型 | Unit名称 | 说明 | 换算关系 |
|----------|----------|------|----------|
| m | 米 | 米制单位 | 基准单位 |
| mm | 毫米 | 毫米制 | 1m = 1000mm |
| cm | 厘米 | 厘米制 | 1m = 100cm |
| inch | 英寸 | 英制单位 | 1m = 39.37inch |

### 完整数据行示例
```
序号 | 序列号      | 类型   | Unit类型 | Unit名称 | 行程  | 位移  | 拉伸面积 | 压缩面积 | 极性     | Deliver | 频率    | 输出倍数 | 平衡  | 备注
-----|------------|--------|----------|----------|-------|-------|----------|----------|----------|---------|---------|----------|-------|----------------
1    | ACT_HYD_01 | 单出杆 | m        | 米       | 0.30  | 0.30  | 0.60     | 0.50     | Positive | 0.100   | 100.00  | 1.000    | 0.000 | 液压系统主作动器
2    | ACT_HYD_02 | 双出杆 | mm       | 毫米     | 0.25  | 0.25  | 0.45     | 0.45     | Positive | 0.080   | 120.00  | 1.000    | 0.000 | 液压系统副作动器
3    | ACT_ELE_01 | 单出杆 | cm       | 厘米     | 0.20  | 0.20  | 0.30     | 0.25     | Positive | 0.050   | 1000.00 | 1.000    | 0.000 | 电动精密作动器
4    | ACT_PNE_01 | 单出杆 | inch     | 英寸     | 0.15  | 0.15  | 0.20     | 0.18     | Positive | 0.200   | 50.00   | 1.000    | 0.000 | 气动快速作动器
```

## 🔄 Unit字段数据验证规则

### Unit类型验证
- **允许值**: "m", "mm", "cm", "inch"
- **默认值**: "m"
- **大小写**: 严格匹配

### Unit名称验证
- **数据类型**: String
- **允许值**: "米", "毫米", "厘米", "英寸"
- **默认值**: "米"
- **编码**: UTF-8中文

### Unit字段组合验证
```cpp
// 验证Unit类型和名称的匹配关系
bool validateUnitData(QString unitType, QString unitName) {
    if (unitType == "m" && unitName == "米") return true;
    if (unitType == "mm" && unitName == "毫米") return true;
    if (unitType == "cm" && unitName == "厘米") return true;
    if (unitType == "inch" && unitName == "英寸") return true;
    return false;
}
```

## 📁 文件命名规范

```
SiteResConfig_Actuators_YYYYMMDD_HHMMSS.xlsx
```

示例: `SiteResConfig_Actuators_20250814_153000.xlsx`

## 🎯 新格式特点

### Unit字段增强
- ✅ **双列存储** - Unit类型 + Unit名称
- ✅ **完整单位信息** - 支持多种单位制
- ✅ **中文显示** - 直观的中文单位名称
- ✅ **数据验证** - 类型和名称的匹配验证

### 存储优势
- ✅ **15列完整布局** - 增加Unit名称列
- ✅ **单位制支持** - 米制、毫米制、厘米制、英制
- ✅ **中文友好** - 直观的中文单位显示
- ✅ **数据完整性** - 保留所有原有参数
- ✅ **向后兼容** - 支持原有数据迁移

### 实用性
- ✅ **标准XLSX格式** - Excel完全兼容
- ✅ **专业表格样式** - 清晰的视觉设计
- ✅ **数据验证** - 完整的逻辑检查
- ✅ **扩展性** - 支持新增单位类型

这个新的存储格式完全适应了您对Unit字段的修改，将Unit数据分为两列存储（类型+名称），提供了更直观和用户友好的单位管理方案。
