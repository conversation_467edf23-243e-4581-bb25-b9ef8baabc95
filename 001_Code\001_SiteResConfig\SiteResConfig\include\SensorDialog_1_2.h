#pragma once

/**
 * @file SensorDialog_1_2.h
 * @brief 传感器创建对话框类定义1_2版本
 * @details 使用Qt Designer设计的传感器参数输入对话框，基于MVVM架构
 * <AUTHOR> Assistant
 * @date 2025-08-23
 * @version 1.2.0
 */

#include <QtWidgets/QDialog>
#include <QtCore/QString>
#include <QtCore/QJsonObject>
#include <QtCore/QStringList>

QT_BEGIN_NAMESPACE
class QTreeWidgetItem;
QT_END_NAMESPACE

namespace Ui {
class SensorDialog_1_2;
}

namespace UI {

/**
 * @brief 传感器参数结构体1_2版本
 * @details 存储传感器的14字段新需求配置参数
 */
struct SensorParams_1_2 {
    // ================= 🆕 新需求核心字段 (14字段) =================
    
    // 1. 零点偏移
    double zero_offset;       // 零点偏移
    
    // 2. 使能状态
    bool enable;              // 启用状态
    
    // 3-14. params嵌套对象的12个字段
    QString params_model;     // 参数模型（如"AKD-8A"）
    QString params_sn;        // 参数序列号（如"2223"）
    double params_k;          // 线性系数k
    double params_b;          // 线性系数b  
    double params_precision;  // 精度
    int params_polarity;      // 极性 (Unknown=0, Positive=1, Negative=-1, Both=9)
    
    int meas_unit;            // 测量单位类型
    double meas_range_min;    // 测量范围最小值
    double meas_range_max;    // 测量范围最大值
    
    int output_signal_unit;          // 输出信号单位类型
    double output_signal_range_min;  // 输出信号范围最小值
    double output_signal_range_max;  // 输出信号范围最大值

    // ================= 🔄 兼容性字段 (保留用于向后兼容) =================
    
    // 基本标识信息（最小化保留）
    int sensorId;              // 传感器ID (用于内部标识)
    QString sensorType;        // 传感器类型（作为主要标识）

    // 默认构造函数
    SensorParams_1_2() : 
        // 新需求字段默认值
        zero_offset(0.0), enable(true),
        params_model(""), params_sn(""),
        params_k(20.0), params_b(0.0), params_precision(0.1), params_polarity(-1),
        meas_unit(1), meas_range_min(-100.0), meas_range_max(100.0),
        output_signal_unit(1), output_signal_range_min(-100.0), output_signal_range_max(100.0),
        // 兼容性字段默认值
        sensorId(0), sensorType("NewRequirement_1_2") {}
};

/**
 * @brief 传感器组结构体1_2版本
 * @details 存储传感器组及其包含的传感器列表
 */
struct SensorGroup_1_2 {
    int groupId;                          // 组序号
    QString groupName;                    // 传感器组名称
    QList<SensorParams_1_2> sensors;          // 传感器列表
    QString groupType;                    // 组类型 (载荷/位置/压力/温度/振动/应变/角度)
    QString createTime;                   // 创建时间
    QString groupNotes;                   // 组备注
};

/**
 * @brief 传感器创建对话框类1_2版本
 * @details 使用.ui文件设计的标准Qt对话框，基于MVVM架构设计
 */
class SensorDialog_1_2 : public QDialog {
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param groupName 传感器组名称
     * @param autoNumber 自动生成的编号
     * @param parent 父窗口
     */
    explicit SensorDialog_1_2(const QString& groupName, const QString& autoNumber, QWidget* parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    virtual ~SensorDialog_1_2();

    /**
     * @brief 获取传感器参数
     * @return 传感器参数结构体
     */
    SensorParams_1_2 getSensorParams() const;

    /**
     * @brief 设置传感器参数（用于编辑模式）
     * @param params 传感器参数结构体
     */
    void setSensorParams(const SensorParams_1_2& params);

    /**
     * @brief 导出传感器数据到CSV文件
     * @param filePath 文件路径
     * @return 成功返回true，失败返回false
     */
    bool exportToCSV(const QString& filePath) const;

    /**
     * @brief 导出传感器数据到JSON文件
     * @param filePath 文件路径
     * @return 成功返回true，失败返回false
     */
    // 🚫 已注释：独立JSON导出功能已废弃
    // bool exportToJSON(const QString& filePath) const;

    /**
     * @brief 导出传感器数据到Excel文件
     * @param filePath 文件路径
     * @return 成功返回true，失败返回false
     */
    bool exportToExcel(const QString& filePath) const;

    /**
     * @brief 从Excel文件导入传感器数据
     * @param filePath 文件路径
     * @return 成功返回true，失败返回false
     */
    bool importFromExcel(const QString& filePath);

    /**
     * @brief 设置传感器类型（预选且不可编辑）
     * @param sensorType 传感器类型
     */
    void setSensorType(const QString& sensorType);

    /**
     * @brief 验证输入参数
     * @return 验证是否通过
     */
    bool validateInput();

    // 🆕 新增：1_2版本特有方法
    /**
     * @brief 设置传感器ID（用于编辑模式）
     * @param sensorId 传感器ID
     */
    void setSensorId(int sensorId);

    /**
     * @brief 获取传感器ID
     * @return 传感器ID
     */
    int getSensorId() const;

    /**
     * @brief 重置表单为默认值
     */
    void resetForm();

    /**
     * @brief 启用/禁用编辑模式
     * @param enabled 是否启用编辑
     */
    void setEditMode(bool enabled);

private slots:
    /**
     * @brief Sensor组合框改变槽函数
     */
    void onSensorChanged();

    /**
     * @brief 传感器类型改变槽函数
     */
    void onSensorTypeChanged();

    /**
     * @brief 单位类型改变槽函数
     */
    void onUnitTypeChanged();

    /**
     * @brief 校准日期按钮点击槽函数
     */
    void onCalibrationDateButtonClicked();

    /**
     * @brief 登录按钮点击槽函数
     */
    void onLoginButtonClicked();

    /**
     * @brief 确定按钮点击前的验证
     */
    void onAcceptClicked();

    /**
     * @brief 取消按钮点击槽函数
     */
    void onRejectClicked();

    // 🆕 新增：1_2版本特有槽函数
    /**
     * @brief 重置按钮点击槽函数
     */
    void onResetButtonClicked();

    /**
     * @brief 预览按钮点击槽函数
     */
    void onPreviewButtonClicked();

signals:
    // 🆕 新增：1_2版本特有信号
    /**
     * @brief 传感器参数改变信号
     * @param params 新的传感器参数
     */
    void sensorParamsChanged(const SensorParams_1_2& params);

    /**
     * @brief 传感器类型改变信号
     * @param sensorType 新的传感器类型
     */
    void sensorTypeChanged(const QString& sensorType);

    /**
     * @brief 验证状态改变信号
     * @param isValid 是否有效
     */
    void validationStatusChanged(bool isValid);

private:
    /**
     * @brief 初始化UI组件
     */
    void initializeUI();

    /**
     * @brief 连接信号槽
     */
    void connectSignals();

    /**
     * @brief 设置默认值
     */
    void setDefaultValues();

    /**
     * @brief 更新UI状态
     */
    void updateUIState();

    /**
     * @brief 验证单个字段
     * @param fieldName 字段名称
     * @param value 字段值
     * @return 是否有效
     */
    bool validateField(const QString& fieldName, const QVariant& value);

private:
    Ui::SensorDialog_1_2* ui;
    QString groupName_;        // 传感器组名称
    QString autoNumber_;       // 自动生成的编号
    int sensorId_;            // 🆕 新增：传感器ID
    bool editMode_;           // 🆕 新增：编辑模式标志
    bool isValid_;            // 🆕 新增：验证状态标志
};

} // namespace UI
