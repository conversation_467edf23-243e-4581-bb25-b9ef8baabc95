@echo off
echo 正在查找Qt安装路径...

REM 常见的Qt安装路径
set QT_PATHS=C:\Qt\5.14.2\mingw73_32\bin;C:\Qt\Qt5.14.2\5.14.2\mingw73_32\bin;D:\Qt\5.14.2\mingw73_32\bin;C:\Qt\5.15.2\mingw81_32\bin

REM 查找qmake.exe
for %%p in (%QT_PATHS%) do (
    if exist "%%p\qmake.exe" (
        echo 找到Qt路径: %%p
        set QT_BIN=%%p
        goto :found_qt
    )
)

echo 未找到Qt安装路径，请手动设置PATH环境变量
echo 或者使用Qt Creator编译项目
pause
exit /b 1

:found_qt
echo 设置Qt环境变量...
set PATH=%QT_BIN%;%PATH%

echo 进入构建目录...
cd /d "%~dp0\..\build-SiteResConfig_Simple-Desktop_Qt_5_14_2_MinGW_32_bit-Debug"

echo 开始编译...
mingw32-make

if %ERRORLEVEL% EQU 0 (
    echo 编译成功！
    echo 运行程序...
    cd debug
    SiteResConfig.exe
) else (
    echo 编译失败！
    pause
)
