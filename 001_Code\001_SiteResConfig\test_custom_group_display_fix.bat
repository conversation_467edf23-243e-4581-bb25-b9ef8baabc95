@echo off
chcp 65001 > nul
echo ========================================
echo 自定义组显示错误修复测试
echo ========================================
echo.

echo 🔧 正在编译修复后的代码...
cd SiteResConfig
qmake SiteResConfig_Simple.pro
mingw32-make clean
mingw32-make

if %ERRORLEVEL% NEQ 0 (
    echo ❌ 编译失败！
    pause
    exit /b 1
)

echo ✅ 编译成功！
echo.

echo 🚀 启动应用程序进行测试...
echo.
echo 📋 测试步骤：
echo 1. 打开您的实验工程文件
echo 2. 观察硬件树中的自定义组显示
echo 3. 检查组内设备信息是否正确显示
echo 4. 查看控制台调试输出
echo.
echo 🔍 预期修复效果：
echo - 自定义_作动器组：正确显示组内作动器设备
echo - 自定义类型_传感器组：正确显示组内传感器设备
echo - 详细的调试信息输出到控制台
echo.

start "" "debug\SiteResConfig.exe"

echo 📊 应用程序已启动，请按照上述步骤进行测试
echo 如果问题仍然存在，请查看控制台输出的调试信息
pause
