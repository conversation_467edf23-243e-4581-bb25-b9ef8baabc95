@echo off
echo Starting SiteResConfig application...

REM Change to the directory containing the executable
cd /d "build-SiteResConfig_Simple-Desktop_Qt_5_14_2_MinGW_32_bit-Debug\debug"

if exist "SiteResConfig.exe" (
    echo Found SiteResConfig.exe
    echo Starting application...
    echo.
    echo Note: If you see debug messages in a separate console,
    echo that means the debug code is working.
    echo.
    
    REM Start the application
    SiteResConfig.exe
    
) else (
    echo SiteResConfig.exe not found in current directory!
    echo Current directory: %CD%
    dir
)

cd ..\..
pause
