# 🔧 Debug模式树形控件提示信息功能说明

## 📋 功能概述

实现了在debug模式下显示详细ID信息的树形控件提示功能：
- **Debug模式**：显示组ID、设备ID、序号等详细调试信息
- **Release模式**：显示正常的用户友好信息

## 🎯 核心功能

### 1. 自动模式检测
```cpp
bool CMyMainWindow::IsDebugMode() const {
#ifdef _DEBUG
    return true;
#else
    return false;
#endif
}
```

### 2. Debug信息增强
在debug模式下，所有树形控件节点的tooltip都会额外显示：

#### 🔧 DEBUG信息 🔧
```
═══════════════════
节点类型: 作动器组
组ID: 1
组内作动器数量: 3个
组类型: 液压
创建时间: 2025-08-20 15:30:45
作动器ID列表: [1, 2, 3]
树形位置: 第2层
子节点数: 3个
父节点: 作动器
```

## 🎨 不同节点类型的Debug信息

### 作动器组节点
- **组ID**: 从组名称中提取的数字ID
- **组内作动器数量**: 该组包含的作动器设备数量
- **组类型**: 液压/电动/气动等
- **创建时间**: 组创建的时间戳
- **作动器ID列表**: 组内所有作动器的ID数组

### 作动器设备节点
- **作动器ID**: 设备的唯一标识ID
- **序列号**: 设备序列号
- **作动器类型**: 单出杆/双出杆
- **所属组ID**: 设备所属组的ID
- **所属组名**: 设备所属组的名称

### 传感器组节点
- **组ID**: 从组名称中提取的数字ID
- **组内传感器数量**: 该组包含的传感器设备数量
- **组类型**: 载荷/位置/压力等
- **创建时间**: 组创建的时间戳
- **传感器ID列表**: 组内所有传感器的ID数组

### 传感器设备节点
- **传感器ID**: 设备的唯一标识ID
- **序列号**: 设备序列号
- **传感器类型**: 载荷/位置/压力/温度等
- **所属组ID**: 设备所属组的ID
- **所属组名**: 设备所属组的名称

### 硬件节点
- **硬件节点ID**: 从节点名称提取的ID (如LD-B1 -> 1)
- **节点名称**: 完整的节点名称
- **IP地址**: 硬件节点的网络地址
- **端口**: 通信端口号
- **通道数量**: 该节点包含的通道数
- **通道ID列表**: 所有通道的ID数组

### 硬件通道
- **通道ID**: 通道的数字ID (CH1 -> 1)
- **通道名称**: 完整的通道名称
- **所属硬件节点**: 父级硬件节点名称
- **通道类型**: 输入/输出/控制等
- **通道状态**: 激活/未激活/错误等
- **配置参数**: 通道的配置信息

## 🔄 使用方式

### 自动触发
tooltip信息会在以下情况自动更新：
1. **拖拽操作完成后**
2. **添加作动器组后**
3. **添加传感器组后**
4. **添加硬件节点后**
5. **打开工程文件后**

### 手动触发
```cpp
// 更新所有树形控件的tooltip
UpdateAllTreeWidgetTooltips();

// 更新单个节点的tooltip
UpdateSingleNodeTooltip(treeItem);
```

## 🎯 实际效果示例

### Release模式 (正常显示)
```
═══ 50kN_作动器组 详细信息 ═══
节点名称: 50kN_作动器组
节点类型: 作动器组
作动器数量: 2个
─────────────────────
功能: 管理50kN级别的液压作动器
组类型: 液压作动器组
创建时间: 2025-08-20 15:30:45
```

### Debug模式 (增强显示)
```
═══ 50kN_作动器组 详细信息 ═══
节点名称: 50kN_作动器组
节点类型: 作动器组
作动器数量: 2个
─────────────────────
功能: 管理50kN级别的液压作动器
组类型: 液压作动器组
创建时间: 2025-08-20 15:30:45

🔧 DEBUG信息 🔧
═══════════════════
节点类型: 作动器组
组ID: 1
组内作动器数量: 2个
组类型: 液压
创建时间: 2025-08-20 15:30:45
作动器ID列表: [1, 2]
树形位置: 第2层
子节点数: 2个
父节点: 作动器
```

## 🛠️ 技术实现

### 核心方法链
```
UpdateSingleNodeTooltip()
├── GenerateHardwareTreeNodeTooltip() / GenerateTestConfigTreeNodeTooltip()
├── AddDebugInfoToTooltip() [仅Debug模式]
│   ├── AddActuatorGroupDebugInfo()
│   ├── AddActuatorDeviceDebugInfo()
│   ├── AddSensorGroupDebugInfo()
│   ├── AddSensorDeviceDebugInfo()
│   ├── AddHardwareNodeDebugInfo()
│   └── AddHardwareChannelDebugInfo()
└── item->setToolTip()
```

### 数据来源
- **ActuatorDataManager**: 作动器组和设备信息
- **SensorDataManager**: 传感器组和设备信息
- **HardwareNodeResDataManager**: 硬件节点和通道信息

## 🎉 功能优势

1. **开发调试友好**: Debug模式下可以清楚看到所有ID和内部数据
2. **用户体验优化**: Release模式下显示简洁的用户信息
3. **自动化管理**: 无需手动切换，根据编译模式自动调整
4. **全面覆盖**: 支持所有类型的树形控件节点
5. **实时更新**: 数据变化时自动刷新tooltip信息

## 🔧 编译配置

确保项目配置正确：
- **Debug版本**: 定义 `_DEBUG` 宏，显示详细信息
- **Release版本**: 不定义 `_DEBUG` 宏，显示简洁信息

这样开发人员在调试时可以看到完整的ID信息，而最终用户使用时看到的是友好的界面提示。
