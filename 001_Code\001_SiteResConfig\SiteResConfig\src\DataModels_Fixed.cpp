/**
 * @file DataModels_Fixed.cpp
 * @brief Core Data Models Implementation (Fixed Encoding)
 * @details Implementation of data model serialization, deserialization and validation functionality
 * <AUTHOR> Assistant
 * @date 2025-08-05
 * @version 1.0.0
 */

#include "DataModels_Fixed.h"
#include <regex>

namespace DataModels {

// ============================================================================
// HardwareNode Implementation
// ============================================================================

json HardwareNode::ToJson() const {
    std::ostringstream oss;
    oss << "{"
        << "\"nodeId\":" << nodeId << ","
        << "\"nodeName\":\"" << nodeName << "\","
        << "\"nodeType\":\"" << nodeType << "\","
        << "\"ipAddress\":\"" << ipAddress << "\","
        << "\"port\":" << port << ","
        << "\"isConnected\":" << (isConnected ? "true" : "false") << ","
        << "\"firmwareVersion\":\"" << firmwareVersion << "\""
        << "}";
    return oss.str();
}

bool HardwareNode::FromJson(const json& jsonData) {
    // Simplified implementation - just set default values for now
    // TODO: Implement proper JSON parsing when JSON library is available
    nodeId = 0;
    nodeName = "Default Node";
    nodeType = "DefaultType";
    ipAddress = "*************";
    port = 8080;
    isConnected = false;
    firmwareVersion = "v1.0.0";
    return true;
}

bool HardwareNode::IsValid() const {
    // Validate node ID validity
    if (nodeId < 0) return false;
    
    // Validate node name is not empty
    if (nodeName.empty()) return false;
    
    // Validate IP address format (simple validation)
    if (!ipAddress.empty()) {
        std::regex ipRegex(R"(^(\d{1,3}\.){3}\d{1,3}$)");
        if (!std::regex_match(ipAddress, ipRegex)) return false;
    }
    
    // Validate port range
    if (port < 0 || port > 65535) return false;
    
    return true;
}

// ============================================================================
// SensorInfo Implementation
// ============================================================================

json SensorInfo::ToJson() const {
    std::ostringstream oss;
    oss << "{"
        << "\"sensorId\":\"" << sensorId << "\","
        << "\"sensorName\":\"" << sensorName << "\","
        << "\"sensorType\":" << static_cast<int>(sensorType) << ","
        << "\"fullScale\":" << fullScale << ","
        << "\"unit\":\"" << unit << "\""
        << "}";
    return oss.str();
}

bool SensorInfo::FromJson(const json& jsonData) {
    try {
        sensorId = jsonData.value("sensorId", StringType());
        sensorName = jsonData.value("sensorName", StringType());
        sensorType = static_cast<Enums::SensorType>(
            jsonData.value("sensorType", static_cast<int>(Enums::SensorType::Unknown)));
        serialNumber = jsonData.value("serialNumber", StringType());
        manufacturer = jsonData.value("manufacturer", StringType());
        fullScale = jsonData.value("fullScale", 0.0);
        unit = jsonData.value("unit", StringType());
        isPositive = jsonData.value("isPositive", true);
        calibrationDate = jsonData.value("calibrationDate", StringType());
        sensitivity = jsonData.value("sensitivity", 0.0);
        positiveFeedback = jsonData.value("positiveFeedback", 0.0);
        negativeFeedback = jsonData.value("negativeFeedback", 0.0);
        excitationVoltage = jsonData.value("excitationVoltage", 0.0);
        boundNodeId = jsonData.value("boundNodeId", -1);
        boundChannel = jsonData.value("boundChannel", -1);
        return true;
    } catch (const std::exception&) {
        return false;
    }
}

bool SensorInfo::IsValid() const {
    // Validate sensor ID is not empty
    if (sensorId.empty()) return false;
    
    // Validate sensor name is not empty
    if (sensorName.empty()) return false;
    
    // Validate sensor type
    if (sensorType == Enums::SensorType::Unknown) return false;
    
    // Validate full scale is positive
    if (fullScale <= 0.0) return false;
    
    // Validate unit is not empty
    if (unit.empty()) return false;
    
    // Validate bound channel validity
    if (boundNodeId >= 0 && boundChannel < 0) return false;
    
    return true;
}

// ============================================================================
// ActuatorInfo Implementation
// ============================================================================

json ActuatorInfo::ToJson() const {
    return json{
        {"actuatorId", actuatorId},
        {"actuatorName", actuatorName},
        {"actuatorType", static_cast<int>(actuatorType)},
        {"serialNumber", serialNumber},
        {"cylinderDiameter", cylinderDiameter},
        {"rodDiameter", rodDiameter},
        {"stroke", stroke},
        {"maxForce", maxForce},
        {"maxVelocity", maxVelocity},
        {"valveBalance", valveBalance},
        {"valveDither", valveDither},
        {"areaRatio", areaRatio},
        {"boundNodeId", boundNodeId},
        {"boundControlChannel", boundControlChannel},
        {"boundEnableChannel", boundEnableChannel}
    };
}

bool ActuatorInfo::FromJson(const json& jsonData) {
    try {
        actuatorId = jsonData.value("actuatorId", StringType());
        actuatorName = jsonData.value("actuatorName", StringType());
        actuatorType = static_cast<Enums::ActuatorType>(
            jsonData.value("actuatorType", static_cast<int>(Enums::ActuatorType::Unknown)));
        serialNumber = jsonData.value("serialNumber", StringType());
        cylinderDiameter = jsonData.value("cylinderDiameter", 0.0);
        rodDiameter = jsonData.value("rodDiameter", 0.0);
        stroke = jsonData.value("stroke", 0.0);
        maxForce = jsonData.value("maxForce", 0.0);
        maxVelocity = jsonData.value("maxVelocity", 0.0);
        valveBalance = jsonData.value("valveBalance", 0.0);
        valveDither = jsonData.value("valveDither", 0.0);
        areaRatio = jsonData.value("areaRatio", 1.0);
        boundNodeId = jsonData.value("boundNodeId", -1);
        boundControlChannel = jsonData.value("boundControlChannel", -1);
        boundEnableChannel = jsonData.value("boundEnableChannel", -1);
        return true;
    } catch (const std::exception&) {
        return false;
    }
}

bool ActuatorInfo::IsValid() const {
    // Validate actuator ID is not empty
    if (actuatorId.empty()) return false;
    
    // Validate actuator name is not empty
    if (actuatorName.empty()) return false;
    
    // Validate actuator type
    if (actuatorType == Enums::ActuatorType::Unknown) return false;
    
    // Validate physical parameters reasonableness
    if (cylinderDiameter <= 0.0 || rodDiameter <= 0.0 || stroke <= 0.0) return false;
    if (rodDiameter >= cylinderDiameter) return false; // Rod diameter cannot be greater than or equal to cylinder diameter
    
    // Validate area ratio reasonableness
    if (areaRatio <= 0.0) return false;
    
    return true;
}

// ============================================================================
// LoadControlChannel Implementation
// ============================================================================

json LoadControlChannel::ToJson() const {
    return json{
        {"channelId", channelId},
        {"channelName", channelName},
        {"channelIndex", channelIndex},
        {"actuatorId", actuatorId},
        {"sensorIds", sensorIds},
        {"designLoad", designLoad},
        {"controlMode", controlMode},
        {"kp", kp},
        {"ki", ki},
        {"kd", kd},
        {"ks", ks},
        {"safetyEnabled", safetyEnabled},
        {"positionLimitLow", positionLimitLow},
        {"positionLimitHigh", positionLimitHigh},
        {"loadLimitLow", loadLimitLow},
        {"loadLimitHigh", loadLimitHigh}
    };
}

bool LoadControlChannel::FromJson(const json& jsonData) {
    try {
        channelId = jsonData.value("channelId", StringType());
        channelName = jsonData.value("channelName", StringType());
        channelIndex = jsonData.value("channelIndex", 0);
        actuatorId = jsonData.value("actuatorId", StringType());
        
        // Handle sensor ID array
        if (jsonData.contains("sensorIds") && jsonData["sensorIds"].is_array()) {
            sensorIds.clear();
            for (const auto& sensorId : jsonData["sensorIds"]) {
                if (sensorId.is_string()) {
                    sensorIds.push_back(sensorId.get<StringType>());
                }
            }
        }
        
        designLoad = jsonData.value("designLoad", 0.0);
        controlMode = jsonData.value("controlMode", StringType());
        kp = jsonData.value("kp", 1.0);
        ki = jsonData.value("ki", 0.0);
        kd = jsonData.value("kd", 0.0);
        ks = jsonData.value("ks", 0.0);
        safetyEnabled = jsonData.value("safetyEnabled", true);
        positionLimitLow = jsonData.value("positionLimitLow", -1000.0);
        positionLimitHigh = jsonData.value("positionLimitHigh", 1000.0);
        loadLimitLow = jsonData.value("loadLimitLow", -10000.0);
        loadLimitHigh = jsonData.value("loadLimitHigh", 10000.0);
        return true;
    } catch (const std::exception&) {
        return false;
    }
}

bool LoadControlChannel::IsValid() const {
    // Validate channel ID is not empty
    if (channelId.empty()) return false;
    
    // Validate channel name is not empty
    if (channelName.empty()) return false;
    
    // Validate channel index validity
    if (channelIndex < 0) return false;
    
    // Validate associated actuator ID is not empty
    if (actuatorId.empty()) return false;
    
    // Validate at least one sensor
    if (sensorIds.empty()) return false;
    
    // Validate control mode is not empty
    if (controlMode.empty()) return false;
    
    // Validate PID parameters reasonableness
    if (kp < 0.0) return false; // Proportional coefficient cannot be negative
    
    // Validate safety limits reasonableness
    if (positionLimitLow >= positionLimitHigh) return false;
    if (loadLimitLow >= loadLimitHigh) return false;
    
    return true;
}

} // namespace DataModels
