@echo off
echo.
echo ========================================
echo Link Error Fix Test
echo ========================================
echo.

echo Fixed undefined reference errors:
echo 1. Added importFromJson1_1() implementation
echo 2. Added exportToExcel1_1() implementation  
echo 3. Added importFromExcel1_1() implementation
echo 4. Added writeActuatorToCsv1_1() helper method
echo.

echo All missing method implementations have been added:
echo - JSON import with full data structure support
echo - Excel export as CSV format (Excel compatible)
echo - Excel import from CSV format
echo - CSV writing helper for clean data output
echo.

if exist "SiteResConfig\SiteResConfig_Simple.pro" (
    echo Found project file, testing compilation and linking...
    echo.
    
    cd SiteResConfig
    
    echo Cleaning old files...
    if exist "Makefile" del /Q "Makefile" 2>nul
    if exist "debug" rmdir /S /Q "debug" 2>nul
    if exist "release" rmdir /S /Q "release" 2>nul
    if exist "ui_*.h" del /Q "ui_*.h" 2>nul
    
    echo.
    echo Running qmake...
    qmake SiteResConfig_Simple.pro -spec win32-g++ "CONFIG+=debug" 2>&1
    
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo qmake successful!
        echo.
        echo Starting compilation and linking...
        mingw32-make debug 2>&1
        
        if %ERRORLEVEL% EQU 0 (
            echo.
            echo *** COMPILATION AND LINKING SUCCESSFUL! ***
            echo.
            echo All undefined reference errors have been fixed!
            echo All Actuator1_1 features are now fully functional.
            echo.
            
            if exist "debug\SiteResConfig.exe" (
                echo Executable created successfully!
                echo.
                echo All Actuator1_1 features now available:
                echo 1. Create Actuator - Full 4-tab dialog
                echo 2. Edit/Delete Actuator - Context menu operations
                echo 3. JSON Import/Export - Complete data structure
                echo 4. Excel Import/Export - CSV format compatible
                echo 5. Statistics Display - Detailed analysis
                echo.
                
                set /p choice="Launch program to test all Actuator1_1 features? (y/n): "
                if /i "%choice%"=="y" (
                    echo Launching program...
                    start "" "debug\SiteResConfig.exe"
                    echo.
                    echo Complete Test Checklist:
                    echo [ ] Hardware -^> Actuator1_1 Version menu
                    echo [ ] Create Actuator (Ctrl+Alt+A)
                    echo [ ] Test all 4 dialog tabs
                    echo [ ] Export to JSON - verify file content
                    echo [ ] Import from JSON - verify data loading
                    echo [ ] Export to Excel/CSV - verify format
                    echo [ ] Import from Excel/CSV - verify parsing
                    echo [ ] View Statistics - verify calculations
                    echo [ ] Edit existing actuator
                    echo [ ] Delete actuator with confirmation
                )
            ) else (
                echo ERROR: Executable not found
            )
        ) else (
            echo.
            echo *** COMPILATION/LINKING STILL FAILED ***
            echo Please check the error messages above for remaining issues.
        )
    ) else (
        echo.
        echo *** QMAKE FAILED ***
        echo Please check Qt environment configuration.
    )
    
    cd ..
) else (
    echo ERROR: Project file not found
)

echo.
echo ========================================
echo Link Error Fix Test Completed
echo ========================================
echo.
pause
