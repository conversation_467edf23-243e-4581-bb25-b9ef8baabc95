@echo off
echo ========================================
echo  UI文件版本编译测试
echo ========================================

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o
if exist "ui_*.h" del ui_*.h

echo.
echo 生成UI头文件...
uic ui\MainWindow.ui -o ui_MainWindow.h
if errorlevel 1 (
    echo UI文件生成失败！
    pause
    exit /b 1
) else (
    echo UI头文件生成成功: ui_MainWindow.h
)

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo.
    echo 可能的问题：
    echo 1. UI文件中的控件名称与代码不匹配
    echo 2. 缺少必要的信号槽连接
    echo 3. UI控件访问错误
    echo.
    echo 请检查：
    echo - ui->dataTableWidget 是否在UI文件中定义
    echo - ui->actionXXX 动作是否在UI文件中定义
    echo - 所有UI控件的objectName是否正确
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  编译成功！UI文件版本已就绪
    echo ========================================
    
    if exist "SiteResConfig.exe" (
        echo 可执行文件: SiteResConfig.exe
        echo.
        echo ✅ UI文件集成成功！
        echo ✅ 标准Qt开发模式 (.h + .cpp + .ui)
        echo ✅ 可视化界面设计支持
        echo ✅ 代码与界面完全分离
        echo.
        echo 启动程序...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
        echo 启动程序...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
        echo 启动程序...
        start release\SiteResConfig.exe
    ) else (
        echo 警告: 找不到可执行文件！
    )
)

echo.
echo 📖 UI文件开发提示:
echo.
echo 🎨 界面设计:
echo - 使用Qt Designer编辑 ui\MainWindow.ui
echo - 设置控件的objectName属性
echo - 在代码中通过ui->controlName访问
echo.
echo 🔗 信号槽连接:
echo - 在代码中: connect(ui->button, SIGNAL, this, SLOT)
echo - 或在UI文件中直接定义连接关系
echo.
echo 🚀 开发流程:
echo 1. Qt Designer设计界面
echo 2. 设置控件名称和属性
echo 3. 在代码中实现业务逻辑
echo 4. 编译运行测试
echo.
pause
