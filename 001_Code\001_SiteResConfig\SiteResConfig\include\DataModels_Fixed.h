#pragma once

/**
 * @file DataModels_Fixed.h
 * @brief Core Data Models Definition (Fixed Encoding)
 * @details Define core data structures for test project, hardware resources, sensors, etc.
 * <AUTHOR> Assistant
 * @date 2025-08-05
 * @version 1.0.0
 */

#include "Common_Fixed.h"
#include <sstream>

// Simple JSON-like string for now (will be replaced with proper JSON library later)
using json = std::string;

// 包含传感器参数结构体定义
#include "SensorDialog_1_2.h"

// 包含作动器参数结构体定义
#include "ActuatorDialog_1_2.h"

// 🔄 修改：包含DataManager头文件
#include "SensorDataManager_1_2.h"
#include "ActuatorDataManager_1_2.h"

namespace DataModels {

/**
 * @brief Enumeration definitions
 */
namespace Enums {

/**
 * @brief Actuator type enumeration
 */
enum class ActuatorType {
    Unknown = 0,
    Hydraulic,      // Hydraulic actuator
    Electric,       // Electric actuator
    Pneumatic       // Pneumatic actuator
};

/**
 * @brief Sensor type enumeration
 */
enum class SensorType {
    Unknown = 0,
    Force,          // Force sensor
    Displacement,   // Displacement sensor
    Pressure,       // Pressure sensor
    Temperature,    // Temperature sensor
    Acceleration,   // Acceleration sensor
    Strain          // Strain sensor
};

/**
 * @brief Control mode enumeration
 */
enum class ControlMode {
    Unknown = 0,
    Force,          // Force control
    Position,       // Position control
    Velocity,       // Velocity control
    Hybrid          // Hybrid control
};

} // namespace Enums

/**
 * @brief Base data model interface
 * @details Base class for all data models, providing serialization and deserialization functionality
 */
class IDataModel {
public:
    virtual ~IDataModel() = default;
    
//    /**
//     * @brief Serialize to JSON
//     * @return JSON object
//     */
//    virtual json ToJson() const = 0;
    
//    /**
//     * @brief Deserialize from JSON
//     * @param jsonData JSON object
//     * @return true if successful
//     */
//    virtual bool FromJson(const json& jsonData) = 0;
    
//    /**
//     * @brief Validate data validity
//     * @return true if valid
//     */
//    virtual bool IsValid() const = 0;
};

// 注释掉HardwareNode结构体 - 已弃用
/**
 * @brief Hardware node information - 已弃用
 * @details Represents basic information of a hardware control node - 已弃用
 * @deprecated 硬件节点数据结构已弃用，使用NodeConfigParams和ChannelInfo替代
 */
// struct HardwareNode : public IDataModel {
//     int nodeId;                    // Node ID
//     StringType nodeName;           // Node name
//     StringType nodeType;           // Node type
//     //StringType ipAddress;          // IP address
//     //int port;                      // Port number
//     //bool isConnected;              // Connection status
//     //StringType firmwareVersion;    // Firmware version
//     int channelCount;              // Number of channels
//     //double maxSampleRate;          // Maximum sample rate (Hz)

//     HardwareNode()
//         : nodeId(0), /*port(0), isConnected(false),*/ channelCount(0),/* maxSampleRate(1000.0)*/ {}

//     json ToJson() const override;
//     bool FromJson(const json& jsonData) override;
//     bool IsValid() const override;
// };

// 🚫 注释掉未使用的SensorInfo结构体
/*
/**
 * @brief Sensor information
 * @details Represents complete information of a sensor
 */
/*
struct SensorInfo : public IDataModel {
    StringType sensorId;           // Sensor unique identifier
    StringType sensorName;         // Sensor name
    Enums::SensorType sensorType;  // Sensor type
    StringType serialNumber;       // Serial number
    StringType manufacturer;       // Manufacturer

    // Range information
    double fullScale;              // Full scale
    StringType unit;               // Unit
    bool isPositive;               // Polarity (true for positive, false for negative)

    // Calibration information
    StringType calibrationDate;    // Calibration date
    double sensitivity;            // Sensitivity
    double positiveFeedback;       // Positive feedback coefficient
    double negativeFeedback;       // Negative feedback coefficient
    double excitationVoltage;      // Excitation voltage

    // Hardware binding
    int boundNodeId;               // Bound node ID
    int boundChannel;              // Bound channel number

    SensorInfo()
        : sensorType(Enums::SensorType::Unknown)
        , fullScale(0.0), isPositive(true)
        , sensitivity(0.0), positiveFeedback(0.0), negativeFeedback(0.0)
        , excitationVoltage(0.0), boundNodeId(-1), boundChannel(-1) {}

//    json ToJson() const override;
//    bool FromJson(const json& jsonData) override;
//    bool IsValid() const override;
};
*/

// 🚫 注释掉未使用的ActuatorInfo结构体
/*
/**
 * @brief Actuator information
 * @details Represents complete information of an actuator
 */
/*
struct ActuatorInfo : public IDataModel {
    StringType actuatorId;         // Actuator unique identifier
    StringType actuatorName;       // Actuator name
    Enums::ActuatorType actuatorType; // Actuator type
    StringType serialNumber;       // Serial number

    // Physical parameters
    double cylinderDiameter;       // Cylinder diameter (mm)
    double rodDiameter;            // Rod diameter (mm)
    double stroke;                 // Stroke (mm)
    double maxForce;               // Maximum load (N)
    double maxVelocity;            // Maximum velocity (mm/s)

    // Servo valve parameters
    double valveBalance;           // Valve balance
    double valveDither;            // Valve dither
    double areaRatio;              // Area ratio of tension/compression chambers

    // Hardware binding
    int boundNodeId;               // Bound node ID
    int boundControlChannel;       // Bound control channel
    int boundEnableChannel;        // Bound enable channel

    ActuatorInfo()
        : actuatorType(Enums::ActuatorType::Unknown)
        , cylinderDiameter(0.0), rodDiameter(0.0), stroke(0.0)
        , maxForce(0.0), maxVelocity(0.0)
        , valveBalance(0.0), valveDither(0.0), areaRatio(1.0)
        , boundNodeId(-1), boundControlChannel(-1), boundEnableChannel(-1) {}

//    json ToJson() const override;
//    bool FromJson(const json& jsonData) override;
//    bool IsValid() const override;
};
*/

// 🚫 注释掉未使用的LoadControlChannel结构体
/*
/**
 * @brief Load control channel
 * @details Represents configuration information of a load control channel
 */
/*
struct LoadControlChannel : public IDataModel {
    StringType channelId;          // Channel unique identifier
    StringType channelName;        // Channel name
    int channelIndex;              // Channel index

    // Associated hardware
    StringType actuatorId;         // Associated actuator ID
    std::vector<StringType> sensorIds; // Associated sensor ID list

    // Control parameters
    double designLoad;             // Design load
    double maxForce;               // Maximum force
    double maxVelocity;            // Maximum velocity
    Enums::ControlMode controlMode; // Control mode

    // PID parameters
    double kp;                     // Proportional coefficient
    double ki;                     // Integral coefficient
    double kd;                     // Derivative coefficient
    double ks;                     // Feedforward coefficient

    // Safety parameters
    bool safetyEnabled;            // Safety monitoring enable
    double positionLimitLow;       // Position lower limit
    double positionLimitHigh;      // Position upper limit
    double loadLimitLow;           // Load lower limit
    double loadLimitHigh;          // Load upper limit

    LoadControlChannel()
        : channelIndex(0), designLoad(0.0), maxForce(100000.0), maxVelocity(500.0)
        , controlMode(Enums::ControlMode::Force)
        , kp(1.0), ki(0.0), kd(0.0), ks(0.0)
        , safetyEnabled(true)
        , positionLimitLow(-1000.0), positionLimitHigh(1000.0)
        , loadLimitLow(-10000.0), loadLimitHigh(10000.0) {}

//    json ToJson() const override;
//    bool FromJson(const json& jsonData) override;
//    bool IsValid() const override;
};
*/

// 🚫 注释掉未使用的LoadSpectrum结构体
/*
/**
 * @brief Load spectrum data structure
 * @details Represents a load spectrum for testing
 */
/*
struct LoadSpectrum : public IDataModel {
    StringType spectrumId;         // Spectrum unique identifier
    StringType spectrumName;       // Spectrum name
    StringType spectrumType;       // Spectrum type (sine, random, etc.)
    double duration;               // Duration in seconds
    double amplitude;              // Amplitude
    double frequency;              // Frequency (for sine wave)
    std::vector<double> dataPoints; // Data points for custom spectrum

    LoadSpectrum()
        : duration(0.0), amplitude(0.0), frequency(1.0) {}

//    json ToJson() const override;
//    bool FromJson(const json& jsonData) override;
//    bool IsValid() const override;
};
*/

/**
 * @brief Test project data structure
 * @details Represents a complete test project with all configurations
 */
struct TestProject : public IDataModel {
    // Project basic information
    StringType projectName;        // Project name
    StringType projectPath;        // Project file path
    StringType description;        // Project description
    StringType createdDate;        // Creation date
    StringType modifiedDate;       // Last modification date
    StringType version;            // Project version
    StringType author;             // Project author

    // Hardware configuration
    // 注释掉硬件节点向量 - HardwareNode已弃用
    // std::vector<HardwareNode> hardwareNodes;     // Hardware nodes
//    std::vector<ActuatorInfo> actuators;         // Actuators
//    std::vector<SensorInfo> sensors;             // Sensors

    // ❌ 已注释：传感器详细参数存储（改用内存存储）
    // std::map<StringType, UI::SensorParams_1_2> sensorDetailedParams;
    // Key: 传感器序列号, Value: 完整的传感器参数

    // ❌ 已注释：作动器详细参数存储（改用内存存储）
    // std::map<StringType, UI::ActuatorParams_1_2> actuatorDetailedParams;
    // Key: 作动器序列号, Value: 完整的作动器参数

    // ❌ 已注释：作动器组存储（改用内存存储）
    // std::map<int, UI::ActuatorGroup_1_2> actuatorGroups;
    // Key: 组ID, Value: 完整的作动器组

//    // Test configuration
//    std::vector<LoadControlChannel> loadChannels; // Load control channels
//    std::vector<LoadSpectrum> loadSpectrums;      // Load spectrums

//    // Test parameters
//    double sampleRate;             // Sample rate (Hz)
//    double testDuration;           // Test duration (seconds)
//    bool autoSave;                 // Auto save enable
//    StringType dataPath;           // Data save path

    TestProject()
//        : sampleRate(1000.0), testDuration(0.0), autoSave(true)
//        , sensorDataManager_(nullptr), actuatorDataManager_(nullptr), ownDataManagers_(false)
    {}

//    // 🔄 修改：传感器详细参数管理方法（调用SensorDataManager接口）
//    bool addSensorDetailedParams(const StringType& serialNumber, const UI::SensorParams_1_2& params);
//    UI::SensorParams_1_2 getSensorDetailedParams(const StringType& serialNumber) const;
//    bool hasSensorDetailedParams(const StringType& serialNumber) const;
//    bool updateSensorDetailedParams(const StringType& serialNumber, const UI::SensorParams_1_2& params);
//    bool removeSensorDetailedParams(const StringType& serialNumber);
//    std::vector<StringType> getAllSensorSerialNumbers() const;
//    int getSensorCount() const;
//    void clearAllSensors();

//    // 🔄 修改：作动器详细参数管理方法（调用ActuatorDataManager接口）
//    bool addActuatorDetailedParams(const StringType& serialNumber, const UI::ActuatorParams_1_2& params);
//    UI::ActuatorParams_1_2 getActuatorDetailedParams(const StringType& serialNumber) const;
//    bool hasActuatorDetailedParams(const StringType& serialNumber) const;
//    bool updateActuatorDetailedParams(const StringType& serialNumber, const UI::ActuatorParams_1_2& params);
//    bool removeActuatorDetailedParams(const StringType& serialNumber);
//    std::vector<StringType> getAllActuatorSerialNumbers() const;
//    int getActuatorCount() const;

//    // 🔄 修改：作动器组管理方法（调用ActuatorDataManager接口）
//    bool addActuatorGroup(int groupId, const UI::ActuatorGroup_1_2& group);
//    UI::ActuatorGroup_1_2 getActuatorGroup(int groupId) const;
//    bool hasActuatorGroup(int groupId) const;
//    bool updateActuatorGroup(int groupId, const UI::ActuatorGroup_1_2& group);
//    bool removeActuatorGroup(int groupId);
//    std::vector<UI::ActuatorGroup_1_2> getAllActuatorGroups() const;
//    int getActuatorGroupCount() const;
//    void clearAllActuators();
//    void clearAllActuatorGroups();

//    json ToJson() const override;
//    bool FromJson(const json& jsonData) override;
//    bool IsValid() const override;

//    /**
//     * @brief Save project to file
//     * @param filePath File path
//     * @return true if successful
//     */
//    bool SaveToFile(const StringType& filePath) const;

//    /**
//     * @brief Load project from file
//     * @param filePath File path
//     * @return true if successful
//     */
//    bool LoadFromFile(const StringType& filePath);

//    // 🔄 修改：DataManager管理方法
//    void setSensorDataManager(SensorDataManager_1_2* manager);
//    void setActuatorDataManager(ActuatorDataManager_1_2* manager);
//    SensorDataManager_1_2* getSensorDataManager() const;
//    ActuatorDataManager_1_2* getActuatorDataManager() const;

//    // 🆕 新增：DataManager初始化方法
//    void initializeDataManagers();
//    void cleanupDataManagers();

private:
//    // 🔄 修改：DataManager实例（用于委托调用）
//    SensorDataManager_1_2* sensorDataManager_;
//    ActuatorDataManager_1_2* actuatorDataManager_;
//    bool ownDataManagers_; // 标记是否拥有DataManager实例的所有权
};

} // namespace DataModels

// ============================================================================
// UI namespace for interface-specific data structures
// ============================================================================
namespace UI {

/**
 * @brief Control channel parameters for UI display
 * @details Simplified structure for control channel configuration
 */
struct ControlChannelParams {
    std::string channelId;           // 通道ID (如 "CH1", "CH2")
    std::string channelName;         // 通道名称 (如 "CH1", "CH2")
    std::string hardwareAssociation; // 硬件关联 (如 "LD-B1 - CH1")

    // 传感器关联信息
    std::string load1Sensor;         // 载荷1传感器
    std::string load2Sensor;         // 载荷2传感器
    std::string positionSensor;      // 位置传感器

    // 作动器关联信息
    std::string controlActuator;     // 控制作动器

    std::string notes;               // 备注

    // 🆕 新增：扩展配置字段
    int lc_id;                       // 下位机ID
    int station_id;                  // 站点ID
    bool enable;                     // 使能状态
    int control_mode;                // 控制模式

    // 🆕 新增：极性参数
    int servo_control_polarity;      // 控制作动器极性 (1=Positive, -1=Negative, 9=Both, 0=Unknown)
    int payload_sensor1_polarity;    // 载荷1传感器极性
    int payload_sensor2_polarity;    // 载荷2传感器极性
    int position_sensor_polarity;    // 位置传感器极性

    // 默认构造函数
    ControlChannelParams() : 
        lc_id(1), station_id(1), enable(true), control_mode(4),
        servo_control_polarity(1), payload_sensor1_polarity(1),
        payload_sensor2_polarity(1), position_sensor_polarity(1) {}
};

/**
 * @brief Control channel group for UI display
 * @details Container for multiple control channels
 */
struct ControlChannelGroup {
    int groupId;                     // 组ID
    std::string groupName;           // 组名称
    std::string groupType;           // 组类型
    std::string createTime;          // 创建时间
    std::string groupNotes;          // 组备注
    std::vector<ControlChannelParams> channels; // 通道列表

    // 默认构造函数
    ControlChannelGroup() : groupId(1) {
        groupName = "控制通道组";
        groupType = "控制通道";
        groupNotes = "控制通道配置组";
    }
};

} // namespace UI
