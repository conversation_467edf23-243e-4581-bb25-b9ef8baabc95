/*!
    \example extractdata
    \title Extract Data Example
    \brief This is a simplest Qt Xlsx example.
    \ingroup qtxlsx-examples

    This example demonstrates how to extract data form existing
     .xlsx file with Qt Xlsx Library. So lets see how this is achieved.

    This creates a new instance of the all important Document
    class which gives you access to the Excel workbook and worksheets.
    \snippet extractdata/main.cpp 0

    Extracts data from current worksheet.
    \snippet extractdata/main.cpp 1
*/
