@echo off
echo ========================================
echo  新菜单结构测试 - 类型化节点右键菜单
echo ========================================

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o
if exist "ui_*.h" del ui_*.h

echo.
echo 生成UI头文件...
uic ui\MainWindow.ui -o ui_MainWindow.h
if errorlevel 1 (
    echo UI文件生成失败！
    pause
    exit /b 1
) else (
    echo ✅ UI头文件生成成功: ui_MainWindow.h
)

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo.
    echo 新增功能：
    echo ✅ 节点类型标识（UserRole数据）
    echo ✅ 层级化右键菜单结构
    echo ✅ "新建"子菜单包含对应组类型
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！新菜单结构已实现
    echo ========================================
    
    if exist "SiteResConfig.exe" (
        echo 可执行文件: SiteResConfig.exe
        echo.
        echo ✅ 类型化节点右键菜单已实现！
        echo.
        echo 🏷️ 节点类型设置:
        echo ├─ 作动器节点 → 类型: "作动器"
        echo ├─ 传感器节点 → 类型: "传感器"  
        echo └─ 硬件节点资源 → 类型: "硬件节点资源"
        echo.
        echo 🖱️ 新菜单结构:
        echo.
        echo 📁 作动器右键菜单:
        echo ├─ 新建
        echo │  └─ 作动器组
        echo └─ 快速创建作动器组
        echo    ├─ 50kN_作动器
        echo    ├─ 100kN_作动器
        echo    ├─ 200kN_作动器
        echo    └─ 500kN_作动器
        echo.
        echo 📁 传感器右键菜单:
        echo └─ 新建
        echo    └─ 传感器组
        echo.
        echo 📁 硬件节点资源右键菜单:
        echo └─ 新建
        echo    └─ 硬件节点组
        echo.
        echo 🎯 菜单层级优势:
        echo - 统一的"新建"入口
        echo - 清晰的功能分类
        echo - 符合用户操作习惯
        echo - 易于扩展新功能
        echo.
        echo 🔧 技术实现:
        echo - 使用Qt::UserRole存储节点类型
        echo - 基于类型判断显示菜单
        echo - QMenu子菜单层级结构
        echo - Lambda表达式连接信号槽
        echo.
        echo 📋 测试要点:
        echo 1. 右键"作动器"节点
        echo 2. 验证"新建"子菜单存在
        echo 3. 验证"新建"下有"作动器组"选项
        echo 4. 验证"快速创建作动器组"子菜单
        echo 5. 测试各个量程组的创建
        echo 6. 验证传感器和硬件节点的菜单
        echo.
        echo 🎨 用户体验:
        echo - 菜单结构更加规范
        echo - 操作路径更加清晰
        echo - 功能分组更加合理
        echo - 扩展性更加良好
        echo.
        echo 启动程序测试新菜单结构...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
        echo 启动程序...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
        echo 启动程序...
        start release\SiteResConfig.exe
    ) else (
        echo 警告: 找不到可执行文件！
    )
)

echo.
echo 📖 新菜单结构详细测试指南:
echo.
echo 🎯 作动器节点测试:
echo 1. 右键点击"作动器"节点
echo 2. 验证出现"新建"和"快速创建作动器组"两个选项
echo 3. 点击"新建"，验证子菜单出现"作动器组"
echo 4. 点击"作动器组"，验证弹出输入对话框
echo 5. 输入组名，验证组成功创建
echo.
echo 🎯 传感器节点测试:
echo 1. 右键点击"传感器"节点
echo 2. 验证出现"新建"选项
echo 3. 点击"新建"，验证子菜单出现"传感器组"
echo 4. 测试传感器组创建功能
echo.
echo 🎯 硬件节点资源测试:
echo 1. 右键点击"硬件节点资源"节点
echo 2. 验证出现"新建"选项
echo 3. 点击"新建"，验证子菜单出现"硬件节点组"
echo 4. 测试硬件节点组创建功能
echo.
echo 🔍 验证要点:
echo - 节点类型正确识别
echo - 菜单结构层级清晰
echo - 子菜单正确显示
echo - 功能正常执行
echo - 日志正确记录
echo.
pause
