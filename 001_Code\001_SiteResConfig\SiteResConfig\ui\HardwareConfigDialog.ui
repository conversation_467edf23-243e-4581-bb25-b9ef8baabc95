<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>HardwareConfigDialog</class>
 <widget class="QDialog" name="HardwareConfigDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>500</width>
    <height>400</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>手动配置硬件</string>
  </property>
  <property name="modal">
   <bool>true</bool>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QGroupBox" name="nodeGroupBox">
     <property name="title">
      <string>硬件节点配置</string>
     </property>
     <layout class="QGridLayout" name="nodeGridLayout">
      <item row="0" column="0">
       <widget class="QLabel" name="nodeCountLabel">
        <property name="text">
         <string>节点数量:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QSpinBox" name="nodeCountSpinBox">
        <property name="minimum">
         <number>1</number>
        </property>
        <property name="maximum">
         <number>10</number>
        </property>
        <property name="value">
         <number>2</number>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="channelCountLabel">
        <property name="text">
         <string>每节点通道数:</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QSpinBox" name="channelCountSpinBox">
        <property name="minimum">
         <number>1</number>
        </property>
        <property name="maximum">
         <number>16</number>
        </property>
        <property name="value">
         <number>4</number>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="actuatorGroupBox">
     <property name="title">
      <string>作动器配置</string>
     </property>
     <layout class="QGridLayout" name="actuatorGridLayout">
      <item row="0" column="0">
       <widget class="QLabel" name="actuatorCountLabel">
        <property name="text">
         <string>作动器数量:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QSpinBox" name="actuatorCountSpinBox">
        <property name="minimum">
         <number>1</number>
        </property>
        <property name="maximum">
         <number>20</number>
        </property>
        <property name="value">
         <number>4</number>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="maxForceLabel">
        <property name="text">
         <string>最大力值 (kN):</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QDoubleSpinBox" name="maxForceSpinBox">
        <property name="decimals">
         <number>1</number>
        </property>
        <property name="minimum">
         <double>1.000000000000000</double>
        </property>
        <property name="maximum">
         <double>1000.000000000000000</double>
        </property>
        <property name="value">
         <double>100.000000000000000</double>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="sensorGroupBox">
     <property name="title">
      <string>传感器配置</string>
     </property>
     <layout class="QGridLayout" name="sensorGridLayout">
      <item row="0" column="0">
       <widget class="QLabel" name="sensorCountLabel">
        <property name="text">
         <string>传感器数量:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QSpinBox" name="sensorCountSpinBox">
        <property name="minimum">
         <number>1</number>
        </property>
        <property name="maximum">
         <number>50</number>
        </property>
        <property name="value">
         <number>8</number>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <layout class="QHBoxLayout" name="buttonLayout">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="okButton">
       <property name="text">
        <string>确定</string>
       </property>
       <property name="default">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="cancelButton">
       <property name="text">
        <string>取消</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>cancelButton</sender>
   <signal>clicked()</signal>
   <receiver>HardwareConfigDialog</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>369</x>
     <y>382</y>
    </hint>
    <hint type="destinationlabel">
     <x>286</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
