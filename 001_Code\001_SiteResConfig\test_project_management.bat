@echo off
echo ========================================
echo  实验工程管理功能测试
echo ========================================

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o
if exist "ui_*.h" del ui_*.h

echo.
echo 生成UI头文件...
uic ui\MainWindow.ui -o ui_MainWindow.h
if errorlevel 1 (
    echo UI文件生成失败！
    pause
    exit /b 1
) else (
    echo ✅ UI头文件生成成功: ui_MainWindow.h
)

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo.
    echo 请检查以下可能的问题：
    echo 1. QTextStream头文件包含
    echo 2. SaveTreeToCSV函数声明和定义
    echo 3. 文件编码问题
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！实验工程管理功能已实现
    echo ========================================
    
    if exist "SiteResConfig.exe" (
        echo 可执行文件: SiteResConfig.exe
        echo.
        echo ✅ 实验工程管理功能已实现！
        echo.
        echo 🗂️ 新建实验工程:
        echo ├─ 菜单: 文件 → 新建工程
        echo ├─ 默认名称: 20250807143025_实验工程
        echo ├─ 可自定义工程名称
        echo └─ 自动初始化界面
        echo.
        echo 💾 保存实验工程:
        echo ├─ 菜单: 文件 → 保存工程
        echo ├─ 默认格式: CSV
        echo ├─ 支持格式: CSV, JSON
        echo └─ 包含界面配置
        echo.
        echo 📂 打开实验工程:
        echo ├─ 菜单: 文件 → 打开工程
        echo ├─ 支持格式: CSV, JSON
        echo ├─ 自动识别格式
        echo └─ 重建界面配置
        echo.
        echo 📤 导出实验工程:
        echo ├─ 菜单: 文件 → 导出工程
        echo ├─ 导出格式: CSV, JSON
        echo ├─ 用户选择格式
        echo └─ 完整数据结构
        echo.
        echo 🎯 菜单优化:
        echo ├─ 隐藏无用菜单项
        echo ├─ 保留核心功能
        echo ├─ 简化操作流程
        echo └─ 突出工程管理
        echo.
        echo 📋 测试步骤:
        echo 1. 启动程序
        echo 2. 文件 → 新建工程
        echo 3. 输入工程名称（或使用默认）
        echo 4. 配置硬件和试验参数
        echo 5. 文件 → 保存工程（CSV格式）
        echo 6. 文件 → 导出工程（JSON格式）
        echo 7. 文件 → 打开工程（测试加载）
        echo.
        echo 🔧 文件格式:
        echo ├─ CSV格式: 表格式，易编辑
        echo │  ├─ UTF-8编码
        echo │  ├─ 注释行包含项目信息
        echo │  └─ 分段式结构
        echo └─ JSON格式: 完整数据，程序交换
        echo    ├─ 格式化输出
        echo    ├─ 4空格缩进
        echo    └─ 完整项目结构
        echo.
        echo 启动程序...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
        echo 启动程序...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
        echo 启动程序...
        start release\SiteResConfig.exe
    ) else (
        echo 警告: 找不到可执行文件！
    )
)

echo.
echo 📖 实验工程管理功能详细测试指南:
echo.
echo 🎯 新建工程测试:
echo 1. 点击菜单"文件" → "新建工程"
echo 2. 验证默认名称格式: YYYYMMDDHHMMSS_实验工程
echo 3. 可以修改工程名称
echo 4. 点击确定，验证界面重新初始化
echo 5. 验证窗口标题更新为工程名称
echo.
echo 🎯 保存工程测试:
echo 1. 在界面中配置一些硬件设备
echo 2. 点击菜单"文件" → "保存工程"
echo 3. 选择保存位置和格式（CSV/JSON）
echo 4. 验证文件保存成功
echo 5. 用文本编辑器打开验证内容
echo.
echo 🎯 打开工程测试:
echo 1. 点击菜单"文件" → "打开工程"
echo 2. 选择之前保存的工程文件
echo 3. 验证界面配置正确加载
echo 4. 验证设备信息正确显示
echo.
echo 🎯 导出工程测试:
echo 1. 点击菜单"文件" → "导出工程"
echo 2. 选择导出格式（CSV或JSON）
echo 3. 选择保存位置
echo 4. 验证导出文件内容完整
echo.
echo 🔍 验证要点:
echo - 工程名称时间戳格式正确
echo - CSV文件UTF-8编码可读
echo - JSON文件格式正确
echo - 界面配置保存完整
echo - 加载后界面正确恢复
echo - 菜单项正确隐藏/显示
echo.
pause
