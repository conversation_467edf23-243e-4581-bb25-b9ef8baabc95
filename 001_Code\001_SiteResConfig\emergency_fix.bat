@echo off
chcp 65001 > nul
echo ========================================
echo 紧急修复：跳过界面刷新，避免卡死
echo ========================================
echo.

echo [INFO] 创建紧急修复版本...

cd SiteResConfig\src

echo [INFO] 备份原文件...
copy MainWindow_Qt_Simple.cpp MainWindow_Qt_Simple.cpp.backup

echo [INFO] 应用紧急修复...
powershell -Command "(Get-Content MainWindow_Qt_Simple.cpp) -replace 'refreshAllDataFromManagers\(\);', '// refreshAllDataFromManagers(); // 紧急修复：跳过界面刷新' | Set-Content MainWindow_Qt_Simple.cpp"

echo [INFO] 编译修复版本...
cd ..
qmake SiteResConfig_Simple.pro
mingw32-make clean
mingw32-make

if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] 编译失败！
    echo [INFO] 恢复原文件...
    cd src
    copy MainWindow_Qt_Simple.cpp.backup MainWindow_Qt_Simple.cpp
    cd ..
    pause
    exit /b 1
)

echo.
echo [INFO] 紧急修复版本编译成功！
echo.
echo [修复说明]
echo   - 跳过了可能导致卡死的界面刷新操作
echo   - 数据导入功能保持正常
echo   - 避免了界面更新时的阻塞问题
echo.
echo [测试说明]
echo   - 导入后不会自动刷新界面显示
echo   - 但数据已经成功导入到内存中
echo   - 可以手动重启程序查看导入结果
echo.

start "" "debug\SiteResConfig.exe"

echo [INFO] 紧急修复版本已启动
echo [INFO] 请测试工程导入功能
echo.
pause
