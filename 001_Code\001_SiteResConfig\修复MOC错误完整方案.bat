@echo off
chcp 65001 >nul
echo MOC编译错误完整修复方案
echo ============================

cd /d "D:\Work\Project\004_SiteResConfig_ProjectAll\001_Code\001_SiteResConfig\SiteResConfig"

echo 步骤1: 完全清理编译环境...
if exist debug rmdir /s /q debug 2>nul
if exist release rmdir /s /q release 2>nul
if exist Makefile* del /q Makefile* 2>nul
if exist .qmake.stash del /q .qmake.stash 2>nul
if exist object_script*.* del /q object_script*.* 2>nul

echo 步骤2: 重新创建debug目录...
mkdir debug 2>nul
mkdir release 2>nul

echo 步骤3: 检查Qt环境...
qmake -v >nul 2>&1
if %errorlevel% neq 0 (
    echo   ✗ Qt环境未配置！请设置Qt环境变量
    echo   建议: 添加Qt的bin目录到PATH
    goto :error
) else (
    echo   ✓ Qt环境正常
    qmake -v
)

echo.
echo 步骤4: 手动生成MOC文件...
echo 正在生成 TreeInteractionHandler.moc...
moc include\TreeInteractionHandler.h -o debug\TreeInteractionHandler.moc
if %errorlevel% neq 0 (
    echo   ✗ MOC生成失败
    goto :error
) else (
    echo   ✓ MOC文件生成成功
)

echo.
echo 步骤5: 重新生成Makefile...
qmake SiteResConfig_Simple.pro
if %errorlevel% neq 0 (
    echo   ✗ qmake执行失败
    goto :error
) else (
    echo   ✓ Makefile生成成功
)

echo.
echo 步骤6: 检查生成的文件...
if exist debug\TreeInteractionHandler.moc (
    echo   ✓ MOC文件存在: debug\TreeInteractionHandler.moc
) else (
    echo   ✗ MOC文件不存在，尝试备用方案...
    goto :alternative
)

if exist Makefile (
    echo   ✓ Makefile存在
) else (
    echo   ✗ Makefile不存在
    goto :error
)

echo.
echo 步骤7: 开始编译...
echo 使用nmake编译...
nmake 2>nul
if %errorlevel% neq 0 (
    echo nmake失败，尝试mingw32-make...
    mingw32-make 2>nul
    if %errorlevel% neq 0 (
        echo mingw32-make失败，尝试make...
        make 2>nul
        if %errorlevel% neq 0 (
            echo   ✗ 所有编译方式都失败
            goto :error
        )
    )
)

echo   ✓ 编译成功！
goto :success

:alternative
echo.
echo === 备用方案 ===
echo 手动创建所有必需的MOC文件...

echo 生成其他可能需要的MOC文件...
for %%f in (include\*.h) do (
    findstr /c:"Q_OBJECT" "%%f" >nul 2>&1
    if not errorlevel 1 (
        set "header=%%f"
        set "mocfile=debug\%%~nf.moc"
        echo 生成 %%~nf.moc...
        moc "%%f" -o "debug\%%~nf.moc" >nul 2>&1
    )
)

echo 重新运行qmake...
qmake SiteResConfig_Simple.pro

echo 再次尝试编译...
nmake 2>nul || mingw32-make 2>nul || make 2>nul

if %errorlevel% equ 0 (
    echo   ✓ 备用方案编译成功！
    goto :success
) else (
    goto :error
)

:success
echo.
echo ============================
echo ✓ MOC错误修复成功！
echo ============================
pause
exit /b 0

:error
echo.
echo ============================
echo ✗ 修复失败，建议手动操作：
echo 1. 在Qt Creator中打开项目
echo 2. 构建 → 清理项目
echo 3. 构建 → 运行qmake
echo 4. 构建 → 重新构建项目
echo.
echo 或检查以下问题：
echo - Qt环境变量是否正确设置
echo - 编译工具链是否可用(nmake/mingw32-make)
echo - 项目文件权限是否正常
echo ============================
pause
exit /b 1 