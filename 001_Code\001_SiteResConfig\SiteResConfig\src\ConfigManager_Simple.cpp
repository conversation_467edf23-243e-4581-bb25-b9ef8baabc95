/**
 * @file ConfigManager_Simple.cpp
 * @brief Configuration Manager Implementation (Simplified)
 * @details Simplified implementation of system configuration and project file management
 * <AUTHOR> Assistant
 * @date 2025-08-05
 * @version 1.0.0
 */

#include "ConfigManager_Fixed.h"
#include <fstream>
#include <algorithm>
#include "ConfigManager.h"
#include <QStandardPaths>
#include <QDir>
#include <QSettings>
#include <QDebug>
#include <QFile>

#ifdef _WIN32
#include <direct.h>
#include <io.h>
#define ACCESS _access
#define MKDIR(path) _mkdir(path)
#else
#include <unistd.h>
#include <sys/stat.h>
#define ACCESS access
#define MKDIR(path) mkdir(path, 0755)
#endif

namespace Config {

// ============================================================================
// SystemConfig Implementation
// ============================================================================

SystemConfig::SystemConfig() 
    : language("en_US")
    , theme("dark")
    , autoSave(true)
    , autoSaveInterval(300)
    , dataDirectory("data")
    , logDirectory("logs")
    , tempDirectory("temp")
    , maxLogFiles(10)
    , maxLogFileSize(50)
    , defaultSampleRate(Constants::DEFAULT_SAMPLE_RATE)
    , defaultControlPeriod(Constants::MIN_CONTROL_PERIOD)
    , communicationTimeout(5000)
    , maxRetryCount(3)
    , enableSafetyMonitor(true)
    , emergencyStopTimeout(1000)
    , requireConfirmation(true)
{
}

//json SystemConfig::ToJson() const {
//    std::ostringstream oss;
//    oss << "{"
//        << "\"language\":\"" << language << "\","
//        << "\"theme\":\"" << theme << "\","
//        << "\"autoSave\":" << (autoSave ? "true" : "false") << ","
//        << "\"dataDirectory\":\"" << dataDirectory << "\","
//        << "\"defaultSampleRate\":" << defaultSampleRate
//        << "}";
//    return oss.str();
//}

//bool SystemConfig::FromJson(const json& jsonData) {
//    // Simplified implementation - use default values
//    language = "en_US";
//    theme = "dark";
//    autoSave = true;
//    autoSaveInterval = 300;
//    dataDirectory = "data";
//    logDirectory = "logs";
//    tempDirectory = "temp";
//    return true;
//}

//bool SystemConfig::IsValid() const {
//    // Validate basic configuration
//    if (language.empty() || theme.empty()) return false;
//    if (autoSaveInterval <= 0) return false;
    
//    // Validate directory configuration
//    if (dataDirectory.empty() || logDirectory.empty() || tempDirectory.empty()) return false;
    
//    // Validate log configuration
//    if (maxLogFiles <= 0 || maxLogFileSize <= 0) return false;
    
//    // Validate hardware configuration
//    if (defaultSampleRate <= 0.0) return false;
//    if (defaultControlPeriod < Constants::MIN_CONTROL_PERIOD ||
//        defaultControlPeriod > Constants::MAX_CONTROL_PERIOD) return false;
    
//    return true;
//}

// ============================================================================
// ConfigManager Static Members
// ============================================================================

UniquePtr<ConfigManager> ConfigManager::instance_ = nullptr;
std::mutex ConfigManager::instanceMutex_;

// ============================================================================
// ConfigManager Implementation
// ============================================================================

ConfigManager::ConfigManager() {
    // 设置配置文件路径
    QString configDir = QStandardPaths::writableLocation(QStandardPaths::AppConfigLocation);
    QDir().mkpath(configDir);
    QString configPath = configDir + "/SiteResConfig.ini";
    configFilePath_ = configPath.toStdString();
}

ConfigManager& ConfigManager::GetInstance() {
    std::lock_guard<std::mutex> lock(instanceMutex_);
    if (!instance_) {
        instance_ = UniquePtr<ConfigManager>(new ConfigManager());
    }
    return *instance_;
}

bool ConfigManager::Initialize(const StringType& configDir) {
    // Simplified initialization without file system operations
    configFilePath_ = configDir + "/system_config.json";
    recentProjectsFilePath_ = configDir + "/recent_projects.json";

    // Use default configurations
    systemConfig_ = SystemConfig();
    recentProjects_.clear();

    return true;
}

bool ConfigManager::SaveAllConfigs() {
    std::lock_guard<std::mutex> lock(configMutex_);
    
    try {
        QString configPath = QString::fromStdString(configFilePath_);
        QSettings settings(configPath, QSettings::IniFormat);
        settings.setIniCodec("UTF-8");
        
        // 保存系统配置
        settings.beginGroup("SystemConfig");
        settings.setValue("language", QString::fromStdString(systemConfig_.language));
        settings.setValue("theme", QString::fromStdString(systemConfig_.theme));
        settings.setValue("autoSave", systemConfig_.autoSave);
        settings.setValue("autoSaveInterval", static_cast<int>(systemConfig_.autoSaveInterval));
        settings.setValue("dataDirectory", QString::fromStdString(systemConfig_.dataDirectory));
        settings.setValue("logDirectory", QString::fromStdString(systemConfig_.logDirectory));
        settings.setValue("tempDirectory", QString::fromStdString(systemConfig_.tempDirectory));
        settings.setValue("maxLogFiles", systemConfig_.maxLogFiles);
        settings.setValue("maxLogFileSize", systemConfig_.maxLogFileSize);
        settings.setValue("defaultSampleRate", systemConfig_.defaultSampleRate);
        settings.setValue("defaultControlPeriod", systemConfig_.defaultControlPeriod);
        settings.setValue("communicationTimeout", systemConfig_.communicationTimeout);
        settings.setValue("maxRetryCount", systemConfig_.maxRetryCount);
        settings.setValue("enableSafetyMonitor", systemConfig_.enableSafetyMonitor);
        settings.setValue("emergencyStopTimeout", systemConfig_.emergencyStopTimeout);
        settings.setValue("requireConfirmation", systemConfig_.requireConfirmation);
        settings.endGroup();
        
        // 保存最近项目列表
        settings.beginWriteArray("RecentProjects");
        for (int i = 0; i < recentProjects_.size(); ++i) {
            settings.setArrayIndex(i);
            settings.setValue("name", QString::fromStdString(recentProjects_[i].projectName));
            settings.setValue("path", QString::fromStdString(recentProjects_[i].projectPath));
            settings.setValue("lastOpenTime", QString::fromStdString(recentProjects_[i].lastOpenTime));
        }
        settings.endArray();
        
        settings.sync();
        
        if (settings.status() != QSettings::NoError) {
            qDebug() << "配置保存失败:" << settings.status();
            return false;
        }
        
        qDebug() << "配置已保存到:" << configPath;
        return true;
        
    } catch (const std::exception& e) {
        qDebug() << "保存配置时发生异常:" << e.what();
        return false;
    }
}

bool ConfigManager::LoadAllConfigs() {
    std::lock_guard<std::mutex> lock(configMutex_);
    
    try {
        QString configPath = QString::fromStdString(configFilePath_);
        QSettings settings(configPath, QSettings::IniFormat);
        settings.setIniCodec("UTF-8");
        
        if (!QFile::exists(configPath)) {
            qDebug() << "配置文件不存在，使用默认配置:" << configPath;
            systemConfig_ = SystemConfig();
            recentProjects_.clear();
            return true;
        }
        
        // 加载系统配置
        settings.beginGroup("SystemConfig");
        systemConfig_.language = settings.value("language", "zh_CN").toString().toStdString();
        systemConfig_.theme = settings.value("theme", "default").toString().toStdString();
        systemConfig_.autoSave = settings.value("autoSave", true).toBool();
        systemConfig_.autoSaveInterval = settings.value("autoSaveInterval", 300).toInt();
        systemConfig_.dataDirectory = settings.value("dataDirectory", "data").toString().toStdString();
        systemConfig_.logDirectory = settings.value("logDirectory", "logs").toString().toStdString();
        systemConfig_.tempDirectory = settings.value("tempDirectory", "temp").toString().toStdString();
        systemConfig_.maxLogFiles = settings.value("maxLogFiles", 10).toInt();
        systemConfig_.maxLogFileSize = settings.value("maxLogFileSize", 50).toInt();
        systemConfig_.defaultSampleRate = settings.value("defaultSampleRate", 1000.0).toDouble();
        systemConfig_.defaultControlPeriod = settings.value("defaultControlPeriod", 10.0).toDouble();
        systemConfig_.communicationTimeout = settings.value("communicationTimeout", 5000).toInt();
        systemConfig_.maxRetryCount = settings.value("maxRetryCount", 3).toInt();
        systemConfig_.enableSafetyMonitor = settings.value("enableSafetyMonitor", true).toBool();
        systemConfig_.emergencyStopTimeout = settings.value("emergencyStopTimeout", 1000.0).toDouble();
        systemConfig_.requireConfirmation = settings.value("requireConfirmation", true).toBool();
        settings.endGroup();
        
        // 加载最近项目列表
        recentProjects_.clear();
        int size = settings.beginReadArray("RecentProjects");
        for (int i = 0; i < size; ++i) {
            settings.setArrayIndex(i);
            RecentProject project;
            project.projectName = settings.value("name").toString().toStdString();
            project.projectPath = settings.value("path").toString().toStdString();
            project.lastOpenTime = settings.value("lastOpenTime").toString().toStdString();
            
            // 验证项目文件是否仍然存在
            if (QFile::exists(QString::fromStdString(project.projectPath))) {
                recentProjects_.push_back(project);
            }
        }
        settings.endArray();
        
        qDebug() << "配置已加载:" << configPath;
        qDebug() << "最近项目数量:" << recentProjects_.size();
        return true;
        
    } catch (const std::exception& e) {
        qDebug() << "加载配置时发生异常:" << e.what();
        // 出错时使用默认配置
        systemConfig_ = SystemConfig();
        recentProjects_.clear();
        return false;
    }
}

const SystemConfig& ConfigManager::GetSystemConfig() const {
    std::lock_guard<std::mutex> lock(configMutex_);
    return systemConfig_;
}

bool ConfigManager::UpdateSystemConfig(const SystemConfig& config) {
//    if (!config.IsValid()) {
//        return false;
//    }
    
    std::lock_guard<std::mutex> lock(configMutex_);
    systemConfig_ = config;
    
    return true;
}

void ConfigManager::ResetSystemConfig() {
    std::lock_guard<std::mutex> lock(configMutex_);
    systemConfig_ = SystemConfig(); // Reset using default constructor
}

void ConfigManager::AddRecentProject(const StringType& projectName, const StringType& projectPath) {
    std::lock_guard<std::mutex> lock(configMutex_);
    
    // Remove existing project with same path
    recentProjects_.erase(
        std::remove_if(recentProjects_.begin(), recentProjects_.end(),
            [&projectPath](const RecentProject& project) {
                return project.projectPath == projectPath;
            }),
        recentProjects_.end()
    );
    
    // Add to beginning of list
    recentProjects_.insert(recentProjects_.begin(), RecentProject(projectName, projectPath));
    
    // Limit maximum count to 10
    if (recentProjects_.size() > 10) {
        recentProjects_.resize(10);
    }
}

void ConfigManager::RemoveRecentProject(const StringType& projectPath) {
    std::lock_guard<std::mutex> lock(configMutex_);
    
    recentProjects_.erase(
        std::remove_if(recentProjects_.begin(), recentProjects_.end(),
            [&projectPath](const RecentProject& project) {
                return project.projectPath == projectPath;
            }),
        recentProjects_.end()
    );
}

std::vector<RecentProject> ConfigManager::GetRecentProjects() const {
    std::lock_guard<std::mutex> lock(configMutex_);
    return recentProjects_;
}

void ConfigManager::ClearRecentProjects() {
    std::lock_guard<std::mutex> lock(configMutex_);
    recentProjects_.clear();
}

bool ConfigManager::EnsureDirectoryExists(const StringType& dirPath) {
    // Simplified implementation - just return true for now
    // TODO: Implement proper directory creation when needed
    return true;
}

StringType ConfigManager::GetConfigDirectory() const {
    return "config";
}

StringType ConfigManager::GetDataDirectory() const {
    std::lock_guard<std::mutex> lock(configMutex_);
    return systemConfig_.dataDirectory;
}

StringType ConfigManager::GetLogDirectory() const {
    std::lock_guard<std::mutex> lock(configMutex_);
    return systemConfig_.logDirectory;
}

StringType ConfigManager::GetTempDirectory() const {
    std::lock_guard<std::mutex> lock(configMutex_);
    return systemConfig_.tempDirectory;
}

void ConfigManager::CreateDefaultDirectories() {
    // Note: This method assumes lock is already held
    EnsureDirectoryExists(systemConfig_.dataDirectory);
    EnsureDirectoryExists(systemConfig_.logDirectory);
    EnsureDirectoryExists(systemConfig_.tempDirectory);
}

} // namespace Config
