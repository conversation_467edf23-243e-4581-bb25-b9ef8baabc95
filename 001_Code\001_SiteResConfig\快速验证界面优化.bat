@echo off
chcp 65001 >nul
echo ========================================
echo  传感器界面优化验证 v2.0
echo ========================================
echo.

echo 🔍 验证界面优化修改...
echo.

REM 检查UI文件中的GroupBox
echo [1/5] 检查Sensor区域GroupBox...
findstr /C:"sensorGroupBox" SiteResConfig\ui\SensorDialog.ui >nul
if errorlevel 1 (
    echo ❌ 未找到sensorGroupBox
) else (
    echo ✅ Sensor GroupBox已添加
)

REM 检查样式设置
echo [2/5] 检查样式设置...
findstr /C:"#2E86AB" SiteResConfig\ui\SensorDialog.ui >nul
if errorlevel 1 (
    echo ❌ 未找到蓝色主题样式
) else (
    echo ✅ 蓝色主题样式已应用
)

REM 检查配置区域标签
echo [3/5] 检查配置区域标签...
findstr /C:"configSectionLabel" SiteResConfig\ui\SensorDialog.ui >nul
if errorlevel 1 (
    echo ❌ 未找到配置区域标签
) else (
    echo ✅ 配置区域标签已添加
)

REM 检查智能配置功能
echo [4/5] 检查智能配置功能...
findstr /C:"力传感器" SiteResConfig\src\SensorDialog.cpp >nul
if errorlevel 1 (
    echo ❌ 未找到智能配置代码
) else (
    echo ✅ 智能配置功能已实现
)

REM 检查分组选项
echo [5/5] 检查分组选项...
findstr /C:"insertSeparator" SiteResConfig\src\SensorDialog.cpp >nul
if errorlevel 1 (
    echo ❌ 未找到分组分隔符
) else (
    echo ✅ 分组选项已实现
)

echo.
echo ========================================
echo  ✅ 界面优化验证完成！
echo ========================================
echo.
echo 🎨 主要改进:
echo   • Sensor区域独立显示，蓝色边框突出
echo   • 三层视觉结构：信息-Sensor-配置
echo   • 智能自动配置不同类型传感器
echo   • 分组显示传感器选项
echo   • 现代化的界面设计风格
echo.
echo 🚀 测试建议:
echo   1. 编译主项目查看实际效果
echo   2. 运行测试程序验证界面布局
echo   3. 测试传感器选择的智能配置
echo   4. 验证自定义传感器输入功能
echo.
echo 📖 详细信息: 传感器界面优化完成报告_v2.md
echo.
pause
