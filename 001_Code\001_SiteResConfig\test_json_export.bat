@echo off
echo ========================================
echo  JSON导出功能测试
echo ========================================

REM 设置测试目录
set TEST_DIR=%~dp0test_json_output
if not exist "%TEST_DIR%" mkdir "%TEST_DIR%"

echo 测试目录: %TEST_DIR%

REM 启动应用程序进行JSON导出测试
echo.
echo 启动应用程序...
cd /d "%~dp0build-SiteResConfig_Simple-Desktop_Qt_5_14_2_MinGW_32_bit-Debug\debug"

if exist "SiteResConfig.exe" (
    echo 找到可执行文件: SiteResConfig.exe
    echo.
    echo ========================================
    echo  测试说明：
    echo  1. 应用程序启动后，创建一些测试数据
    echo  2. 使用"导出工程"功能选择JSON格式
    echo  3. 验证先保存CSV再转换JSON的流程
    echo  4. 检查生成的JSON文件格式
    echo ========================================
    echo.
    
    start SiteResConfig.exe
    
    echo 应用程序已启动，请手动测试JSON导出功能
    echo 测试完成后，检查输出目录中的文件
    
) else (
    echo 错误: 找不到可执行文件！
    echo 请先编译项目
)

pause
