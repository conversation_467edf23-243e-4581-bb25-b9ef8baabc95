@echo off
echo ========================================
echo  测试XLSX文件列结构修复
echo ========================================

REM 设置Qt环境变量
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目（测试XLSX文件列结构修复）...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo 请检查上面的错误信息。
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！XLSX文件列结构修复完成
    echo ========================================
    
    echo.
    echo ✅ 修复完成的内容:
    echo.
    echo 📊 作动器详细配置修改:
    echo - 修改前: 17列（组序号, 作动器组名称, 作动器序号, 作动器序列号, ...）
    echo - 修改后: 16列（组序号, 作动器组名称, 作动器序列号, ...）
    echo - 变化: 去掉"作动器序号"列
    echo.
    echo 📊 传感器详细配置确认:
    echo - 格式: 34列（组序号, 传感器组名称, 传感器序号, 传感器序列号, ...）
    echo - 结构关系: 一个传感器组名称包括多个传感器信息
    echo - 组序号: 从1开始递增（1, 2, 3, 4...）
    echo - 状态: ✅ 已正确实现
    echo.
    echo 🔧 修改详情:
    echo.
    echo 🎯 作动器详细配置（16列）:
    echo 第1列: 组序号（从1开始递增）
    echo 第2列: 作动器组名称
    echo 第3列: 作动器序列号（去掉了作动器序号）
    echo 第4列: 作动器类型
    echo 第5列: Unit类型
    echo 第6列: Unit值
    echo 第7列: 行程(m)
    echo 第8列: 位移(m)
    echo 第9列: 拉伸面积(m²)
    echo 第10列: 压缩面积(m²)
    echo 第11列: 极性
    echo 第12列: Deliver(V)
    echo 第13列: 频率(Hz)
    echo 第14列: 输出倍数
    echo 第15列: 平衡(V)
    echo 第16列: 备注
    echo.
    echo 🎯 传感器详细配置（34列）:
    echo 第1列: 组序号（从1开始递增）
    echo 第2列: 传感器组名称
    echo 第3列: 传感器序号
    echo 第4列: 传感器序列号
    echo 第5列: 传感器类型
    echo 第6列: EDS标识
    echo 第7列: 尺寸
    echo 第8列: 型号
    echo 第9列: 量程
    echo 第10列: 精度
    echo 第11列: 单位
    echo 第12列: 灵敏度
    echo ... 继续到第34列: 编码器分辨率
    echo.
    echo 📝 结构关系说明:
    echo.
    echo 作动器详细配置:
    echo 组序号 ^| 作动器组名称 ^| 作动器序列号 ^| 作动器类型 ^| ...
    echo ------|------------|------------|----------|----
    echo 1     ^| 主作动器组   ^| ACT001     ^| 液压作动器 ^| ...
    echo 1     ^|            ^| ACT002     ^| 液压作动器 ^| ...
    echo 2     ^| 辅助作动器组 ^| ACT003     ^| 液压作动器 ^| ...
    echo.
    echo 传感器详细配置:
    echo 组序号 ^| 传感器组名称 ^| 传感器序号 ^| 传感器序列号 ^| ...
    echo ------|------------|----------|------------|----
    echo 1     ^| 载荷_传感器组 ^| 1        ^| SEN001     ^| ...
    echo 1     ^|            ^| 2        ^| SEN002     ^| ...
    echo 2     ^| 位置_传感器组 ^| 1        ^| SEN003     ^| ...
    echo.
    
    if exist "SiteResConfig.exe" (
        echo 启动程序验证修复...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 启动程序验证修复...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 启动程序验证修复...
        start release\SiteResConfig.exe
    )
)

echo.
echo 📖 详细验证步骤:
echo.
echo 🎮 作动器详细配置验证:
echo 1. 启动软件，新建项目
echo 2. 创建多个作动器组
echo 3. 在每个组中添加作动器
echo 4. 导出作动器详细信息到Excel
echo 5. 验证Excel文件：
echo    - 总共16列
echo    - 第1列：组序号（1, 2, 3...）
echo    - 第2列：作动器组名称
echo    - 第3列：作动器序列号（没有作动器序号列）
echo    - 第4列：作动器类型
echo    - 第16列：备注
echo.
echo 🎮 传感器详细配置验证:
echo 1. 创建多个传感器组
echo 2. 在每个组中添加传感器
echo 3. 导出传感器详细信息到Excel
echo 4. 验证Excel文件：
echo    - 总共34列
echo    - 第1列：组序号（1, 2, 3...）
echo    - 第2列：传感器组名称
echo    - 第3列：传感器序号
echo    - 第4列：传感器序列号
echo    - 第34列：编码器分辨率
echo.
echo 🎮 组序号递增验证:
echo 1. 创建5个组，只在第1、3、5个组中添加设备
echo 2. 导出Excel文件
echo 3. 验证组序号为：1, 2, 3（连续，无跳跃）
echo 4. 验证空组不分配序号
echo.
echo 🎮 结构关系验证:
echo 1. 验证组名称只在每组第一行显示
echo 2. 验证同组内的设备行组名称为空
echo 3. 验证组行使用特殊格式（浅蓝色背景，粗体）
echo 4. 验证一个组名称包括多个设备信息
echo.
echo ✅ 预期结果:
echo - 作动器详细配置：16列，无作动器序号列
echo - 传感器详细配置：34列，有组序号和传感器组名称列
echo - 组序号从1开始递增，连续无跳跃
echo - 结构关系正确，一个组名称包括多个设备信息
echo - Excel格式专业，数据完整
echo.
echo 🚨 如果测试失败:
echo - 检查作动器Excel是否为16列
echo - 验证传感器Excel是否为34列
echo - 确认组序号是否从1开始递增
echo - 检查结构关系是否正确
echo.
pause
