#include <QApplication>
#include <QMainWindow>
#include <QVBoxLayout>
#include <QPushButton>
#include <QDebug>
#include <QMessageBox>
#include <QGroupBox>
#include <QLabel>

// 包含必要的头文件
#include "src/BasicInfoWidget.h"
#include "src/DetailInfoPanel.h"

class ComprehensiveTestWindow : public QMainWindow
{
    Q_OBJECT

public:
    ComprehensiveTestWindow(QWidget *parent = nullptr) : QMainWindow(parent)
    {
        setWindowTitle("载荷1传感器选择列全面修复测试");
        setGeometry(100, 100, 1400, 900);

        // 创建中央部件
        QWidget *centralWidget = new QWidget(this);
        setCentralWidget(centralWidget);

        // 创建布局
        QVBoxLayout *layout = new QVBoxLayout(centralWidget);

        // 创建详细信息面板
        m_detailPanel = new DetailInfoPanel(this);
        layout->addWidget(m_detailPanel);

        // 创建测试按钮组
        QGroupBox *testGroup = new QGroupBox("测试功能", this);
        QVBoxLayout *testLayout = new QVBoxLayout(testGroup);
        
        // 测试按钮1：测试载荷1传感器选择列修复
        QPushButton *testLoad1Btn = new QPushButton("🧪 测试载荷1传感器选择列修复", this);
        testLayout->addWidget(testLoad1Btn);
        
        // 测试按钮2：测试空值显示
        QPushButton *testEmptyBtn = new QPushButton("🔍 测试空值显示（应该显示为空）", this);
        testLayout->addWidget(testEmptyBtn);
        
        // 测试按钮3：测试有数据显示
        QPushButton *testDataBtn = new QPushButton("📊 测试有数据显示（应该显示数据）", this);
        testLayout->addWidget(testDataBtn);
        
        // 测试按钮4：测试混合数据
        QPushButton *testMixedBtn = new QPushButton("🔄 测试混合数据（空值+有数据）", this);
        testLayout->addWidget(testMixedBtn);
        
        // 测试按钮5：测试所有相关列
        QPushButton *testAllBtn = new QPushButton("🌟 测试所有相关列修复", this);
        testLayout->addWidget(testAllBtn);
        
        layout->addWidget(testGroup);

        // 创建状态显示
        QGroupBox *statusGroup = new QGroupBox("测试状态", this);
        QVBoxLayout *statusLayout = new QVBoxLayout(statusGroup);
        m_statusLabel = new QLabel("等待测试...", this);
        m_statusLabel->setStyleSheet("QLabel { padding: 10px; background-color: #f0f0f0; border: 1px solid #ccc; }");
        statusLayout->addWidget(m_statusLabel);
        layout->addWidget(statusGroup);

        // 连接信号
        connect(testLoad1Btn, &QPushButton::clicked, this, &ComprehensiveTestWindow::testLoad1SensorFix);
        connect(testEmptyBtn, &QPushButton::clicked, this, &ComprehensiveTestWindow::testEmptyValues);
        connect(testDataBtn, &QPushButton::clicked, this, &ComprehensiveTestWindow::testDataValues);
        connect(testMixedBtn, &QPushButton::clicked, this, &ComprehensiveTestWindow::testMixedValues);
        connect(testAllBtn, &QPushButton::clicked, this, &ComprehensiveTestWindow::testAllColumns);
    }

private slots:
    void testLoad1SensorFix()
    {
        qDebug() << "\n🧪 测试载荷1传感器选择列修复";
        m_statusLabel->setText("🧪 测试载荷1传感器选择列修复...");
        
        // 创建测试用的控制通道根节点信息
        NodeInfo rootInfo;
        rootInfo.nodeName = "控制通道";
        rootInfo.nodeType = "控制通道组";
        rootInfo.status = NodeStatus::Online;
        
        // 添加子通道信息
        SubNodeInfo channel1;
        channel1.name = "CH1";
        channel1.type = "控制通道";
        channel1.deviceName = "LD-B1";
        channel1.deviceId = "CH1";
        channel1.isConnected = true;
        
        // 🎯 关键测试：设置载荷1传感器选择属性
        channel1.setProperty("载荷1传感器选择", "载荷传感器_001");
        channel1.setProperty("载荷2传感器选择", "载荷传感器_002");
        channel1.setProperty("位置传感器选择", "位置传感器_001");
        channel1.setProperty("控制作动器选择", "伺服作动器_001");
        channel1.setProperty("下位机ID", "1");
        channel1.setProperty("站点ID", "1");
        channel1.setProperty("使能状态", "✅");
        channel1.setProperty("控制作动器极性", "正向");
        channel1.setProperty("载荷1传感器极性", "正向");
        channel1.setProperty("载荷2传感器极性", "正向");
        channel1.setProperty("位置传感器极性", "正向");
        rootInfo.addSubNode(channel1);
        
        // 设置到详细信息面板
        m_detailPanel->setNodeInfo(rootInfo);
        
        qDebug() << "✅ 测试数据已设置，包含" << rootInfo.subNodes.size() << "个子通道";
        qDebug() << "  - CH1 载荷1传感器选择:" << channel1.getProperty("载荷1传感器选择").toString();
        
        m_statusLabel->setText("✅ 载荷1传感器选择列修复测试完成！\n请检查详细信息面板中的载荷1传感器选择列。");
        
        QMessageBox::information(this, "测试完成", 
            QString("载荷1传感器选择列修复测试完成！\n\n"
                   "CH1 载荷1传感器选择: %1\n\n"
                   "请检查详细信息面板中的载荷1传感器选择列是否正确显示数据。")
            .arg(channel1.getProperty("载荷1传感器选择").toString()));
    }
    
    void testEmptyValues()
    {
        qDebug() << "\n🔍 测试空值显示（应该显示为空）";
        m_statusLabel->setText("🔍 测试空值显示...");
        
        NodeInfo rootInfo;
        rootInfo.nodeName = "控制通道";
        rootInfo.nodeType = "控制通道组";
        rootInfo.status = NodeStatus::Online;
        
        SubNodeInfo channel;
        channel.name = "CH_EMPTY";
        channel.type = "控制通道";
        channel.deviceName = "LD-B-EMPTY";
        channel.deviceId = "CH_EMPTY";
        channel.isConnected = true;
        
        // 🎯 关键测试：所有传感器选择都设置为空
        channel.setProperty("载荷1传感器选择", "");
        channel.setProperty("载荷2传感器选择", "");
        channel.setProperty("位置传感器选择", "");
        channel.setProperty("控制作动器选择", "");
        channel.setProperty("下位机ID", "99");
        channel.setProperty("站点ID", "99");
        channel.setProperty("使能状态", "❌");
        channel.setProperty("控制作动器极性", "");
        channel.setProperty("载荷1传感器极性", "");
        channel.setProperty("载荷2传感器极性", "");
        channel.setProperty("位置传感器极性", "");
        rootInfo.addSubNode(channel);
        
        m_detailPanel->setNodeInfo(rootInfo);
        
        qDebug() << "✅ 空值测试数据已设置";
        qDebug() << "  - 载荷1传感器选择: [空值]";
        qDebug() << "  - 载荷2传感器选择: [空值]";
        qDebug() << "  - 位置传感器选择: [空值]";
        qDebug() << "  - 控制作动器选择: [空值]";
        
        m_statusLabel->setText("✅ 空值显示测试完成！\n所有传感器选择列应该显示为空（空白）。");
        
        QMessageBox::information(this, "空值测试完成", 
            "空值显示测试完成！\n\n"
            "所有传感器选择列现在应该显示为空（空白），而不是显示'已配置'。\n\n"
            "请检查详细信息面板验证修复效果。");
    }
    
    void testDataValues()
    {
        qDebug() << "\n📊 测试有数据显示（应该显示数据）";
        m_statusLabel->setText("📊 测试有数据显示...");
        
        NodeInfo rootInfo;
        rootInfo.nodeName = "控制通道";
        rootInfo.nodeType = "控制通道组";
        rootInfo.status = NodeStatus::Online;
        
        SubNodeInfo channel;
        channel.name = "CH_DATA";
        channel.type = "控制通道";
        channel.deviceName = "LD-B-DATA";
        channel.deviceId = "CH_DATA";
        channel.isConnected = true;
        
        // 🎯 关键测试：所有传感器选择都设置具体数据
        channel.setProperty("载荷1传感器选择", "高精度载荷传感器_A001");
        channel.setProperty("载荷2传感器选择", "高精度载荷传感器_A002");
        channel.setProperty("位置传感器选择", "激光位置传感器_L001");
        channel.setProperty("控制作动器选择", "伺服电机作动器_S001");
        channel.setProperty("下位机ID", "100");
        channel.setProperty("站点ID", "100");
        channel.setProperty("使能状态", "✅");
        channel.setProperty("控制作动器极性", "正向");
        channel.setProperty("载荷1传感器极性", "正向");
        channel.setProperty("载荷2传感器极性", "正向");
        channel.setProperty("位置传感器极性", "正向");
        rootInfo.addSubNode(channel);
        
        m_detailPanel->setNodeInfo(rootInfo);
        
        qDebug() << "✅ 有数据测试已设置";
        qDebug() << "  - 载荷1传感器选择:" << channel.getProperty("载荷1传感器选择").toString();
        qDebug() << "  - 载荷2传感器选择:" << channel.getProperty("载荷2传感器选择").toString();
        qDebug() << "  - 位置传感器选择:" << channel.getProperty("位置传感器选择").toString();
        qDebug() << "  - 控制作动器选择:" << channel.getProperty("控制作动器选择").toString();
        
        m_statusLabel->setText("✅ 有数据显示测试完成！\n所有传感器选择列应该显示具体数据。");
        
        QMessageBox::information(this, "有数据测试完成", 
            "有数据显示测试完成！\n\n"
            "所有传感器选择列现在应该显示具体的传感器名称，而不是显示'已配置'。\n\n"
            "请检查详细信息面板验证修复效果。");
    }
    
    void testMixedValues()
    {
        qDebug() << "\n🔄 测试混合数据（空值+有数据）";
        m_statusLabel->setText("🔄 测试混合数据...");
        
        NodeInfo rootInfo;
        rootInfo.nodeName = "控制通道";
        rootInfo.nodeType = "控制通道组";
        rootInfo.status = NodeStatus::Online;
        
        // 通道1：部分有数据，部分空值
        SubNodeInfo channel1;
        channel1.name = "CH_MIXED1";
        channel1.type = "控制通道";
        channel1.deviceName = "LD-B-MIXED1";
        channel1.deviceId = "CH_MIXED1";
        channel1.isConnected = true;
        
        channel1.setProperty("载荷1传感器选择", "载荷传感器_M001");  // 有数据
        channel1.setProperty("载荷2传感器选择", "");                  // 空值
        channel1.setProperty("位置传感器选择", "位置传感器_P001");    // 有数据
        channel1.setProperty("控制作动器选择", "");                   // 空值
        channel1.setProperty("下位机ID", "201");
        channel1.setProperty("站点ID", "201");
        channel1.setProperty("使能状态", "⚠️");
        channel1.setProperty("控制作动器极性", "正向");
        channel1.setProperty("载荷1传感器极性", "正向");
        channel1.setProperty("载荷2传感器极性", "正向");
        channel1.setProperty("位置传感器极性", "正向");
        rootInfo.addSubNode(channel1);
        
        // 通道2：全部空值
        SubNodeInfo channel2;
        channel2.name = "CH_MIXED2";
        channel2.type = "控制通道";
        channel2.deviceName = "LD-B-MIXED2";
        channel2.deviceId = "CH_MIXED2";
        channel2.isConnected = true;
        
        channel2.setProperty("载荷1传感器选择", "");
        channel2.setProperty("载荷2传感器选择", "");
        channel2.setProperty("位置传感器选择", "");
        channel2.setProperty("控制作动器选择", "");
        channel2.setProperty("下位机ID", "202");
        channel2.setProperty("站点ID", "202");
        channel2.setProperty("使能状态", "❌");
        channel2.setProperty("控制作动器极性", "");
        channel2.setProperty("载荷1传感器极性", "");
        channel2.setProperty("载荷2传感器极性", "");
        channel2.setProperty("位置传感器极性", "");
        rootInfo.addSubNode(channel2);
        
        m_detailPanel->setNodeInfo(rootInfo);
        
        qDebug() << "✅ 混合数据测试已设置";
        qDebug() << "  - CH_MIXED1: 载荷1[有数据], 载荷2[空值], 位置[有数据], 控制[空值]";
        qDebug() << "  - CH_MIXED2: 全部[空值]";
        
        m_statusLabel->setText("✅ 混合数据测试完成！\n有数据的显示数据，空值的显示为空。");
        
        QMessageBox::information(this, "混合数据测试完成", 
            "混合数据测试完成！\n\n"
            "CH_MIXED1: 载荷1[有数据], 载荷2[空值], 位置[有数据], 控制[空值]\n"
            "CH_MIXED2: 全部[空值]\n\n"
            "请检查详细信息面板验证修复效果。");
    }
    
    void testAllColumns()
    {
        qDebug() << "\n🌟 测试所有相关列修复";
        m_statusLabel->setText("🌟 测试所有相关列修复...");
        
        NodeInfo rootInfo;
        rootInfo.nodeName = "控制通道";
        rootInfo.nodeType = "控制通道组";
        rootInfo.status = NodeStatus::Online;
        
        // 创建多个测试通道，覆盖所有情况
        for (int i = 1; i <= 5; ++i) {
            SubNodeInfo channel;
            channel.name = QString("CH_ALL%1").arg(i);
            channel.type = "控制通道";
            channel.deviceName = QString("LD-B-ALL%1").arg(i);
            channel.deviceId = QString("CH_ALL%1").arg(i);
            channel.isConnected = true;
            
            // 根据索引设置不同的数据组合
            if (i == 1) {
                // 全部有数据
                channel.setProperty("载荷1传感器选择", "载荷传感器_ALL1");
                channel.setProperty("载荷2传感器选择", "载荷传感器_ALL2");
                channel.setProperty("位置传感器选择", "位置传感器_ALL1");
                channel.setProperty("控制作动器选择", "伺服作动器_ALL1");
            } else if (i == 2) {
                // 全部空值
                channel.setProperty("载荷1传感器选择", "");
                channel.setProperty("载荷2传感器选择", "");
                channel.setProperty("位置传感器选择", "");
                channel.setProperty("控制作动器选择", "");
            } else if (i == 3) {
                // 部分有数据
                channel.setProperty("载荷1传感器选择", "载荷传感器_ALL3");
                channel.setProperty("载荷2传感器选择", "");
                channel.setProperty("位置传感器选择", "位置传感器_ALL3");
                channel.setProperty("控制作动器选择", "");
            } else if (i == 4) {
                // 交替有数据
                channel.setProperty("载荷1传感器选择", "");
                channel.setProperty("载荷2传感器选择", "载荷传感器_ALL4");
                channel.setProperty("位置传感器选择", "");
                channel.setProperty("控制作动器选择", "伺服作动器_ALL4");
            } else {
                // 随机组合
                channel.setProperty("载荷1传感器选择", "载荷传感器_ALL5");
                channel.setProperty("载荷2传感器选择", "载荷传感器_ALL6");
                channel.setProperty("位置传感器选择", "");
                channel.setProperty("控制作动器选择", "");
            }
            
            channel.setProperty("下位机ID", QString::number(300 + i));
            channel.setProperty("站点ID", QString::number(300 + i));
            channel.setProperty("使能状态", i % 2 == 0 ? "✅" : "❌");
            channel.setProperty("控制作动器极性", "正向");
            channel.setProperty("载荷1传感器极性", "正向");
            channel.setProperty("载荷2传感器极性", "正向");
            channel.setProperty("位置传感器极性", "正向");
            
            rootInfo.addSubNode(channel);
        }
        
        m_detailPanel->setNodeInfo(rootInfo);
        
        qDebug() << "✅ 所有相关列测试已设置，包含" << rootInfo.subNodes.size() << "个测试通道";
        
        m_statusLabel->setText("✅ 所有相关列修复测试完成！\n请检查所有传感器选择列和作动器选择列。");
        
        QMessageBox::information(this, "全面测试完成", 
            "所有相关列修复测试完成！\n\n"
            "创建了5个测试通道，覆盖了各种数据组合情况：\n"
            "CH_ALL1: 全部有数据\n"
            "CH_ALL2: 全部空值\n"
            "CH_ALL3: 部分有数据\n"
            "CH_ALL4: 交替有数据\n"
            "CH_ALL5: 随机组合\n\n"
            "请检查详细信息面板验证所有列的修复效果。");
    }

private:
    DetailInfoPanel *m_detailPanel;
    QLabel *m_statusLabel;
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    ComprehensiveTestWindow window;
    window.show();
    
    return app.exec();
}

#include "test_load1_sensor_comprehensive.moc" 