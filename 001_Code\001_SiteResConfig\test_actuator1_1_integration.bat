@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🔧 作动器1_1版本集成测试
echo ========================================
echo.

echo 📋 集成状态检查:
echo.
echo ✅ 项目文件更新:
echo    ├─ 已添加 ActuatorStructs1_1.cpp 到 SOURCES
echo    ├─ 已添加 ActuatorDataManager1_1.cpp 到 SOURCES
echo    ├─ 已添加 ActuatorDialog1_1.cpp 到 SOURCES
echo    ├─ 已添加 ActuatorStructs1_1.h 到 HEADERS
echo    ├─ 已添加 ActuatorDataManager1_1.h 到 HEADERS
echo    ├─ 已添加 ActuatorDialog1_1.h 到 HEADERS
echo    └─ 已添加 ActuatorDialog1_1.ui 到 FORMS
echo.

echo ✅ 文件存在性检查:
echo.

set "missing_files="

if not exist "SiteResConfig\include\ActuatorStructs1_1.h" (
    echo ❌ 缺少文件: ActuatorStructs1_1.h
    set "missing_files=1"
) else (
    echo ✅ 存在: ActuatorStructs1_1.h
)

if not exist "SiteResConfig\src\ActuatorStructs1_1.cpp" (
    echo ❌ 缺少文件: ActuatorStructs1_1.cpp
    set "missing_files=1"
) else (
    echo ✅ 存在: ActuatorStructs1_1.cpp
)

if not exist "SiteResConfig\include\ActuatorDataManager1_1.h" (
    echo ❌ 缺少文件: ActuatorDataManager1_1.h
    set "missing_files=1"
) else (
    echo ✅ 存在: ActuatorDataManager1_1.h
)

if not exist "SiteResConfig\src\ActuatorDataManager1_1.cpp" (
    echo ❌ 缺少文件: ActuatorDataManager1_1.cpp
    set "missing_files=1"
) else (
    echo ✅ 存在: ActuatorDataManager1_1.cpp
)

if not exist "SiteResConfig\include\ActuatorDialog1_1.h" (
    echo ❌ 缺少文件: ActuatorDialog1_1.h
    set "missing_files=1"
) else (
    echo ✅ 存在: ActuatorDialog1_1.h
)

if not exist "SiteResConfig\src\ActuatorDialog1_1.cpp" (
    echo ❌ 缺少文件: ActuatorDialog1_1.cpp
    set "missing_files=1"
) else (
    echo ✅ 存在: ActuatorDialog1_1.cpp
)

if not exist "SiteResConfig\ui\ActuatorDialog1_1.ui" (
    echo ❌ 缺少文件: ActuatorDialog1_1.ui
    set "missing_files=1"
) else (
    echo ✅ 存在: ActuatorDialog1_1.ui
)

echo.

if defined missing_files (
    echo ❌ 发现缺少文件，请检查文件是否正确创建！
    echo.
    pause
    exit /b 1
)

echo ✅ 所有必需文件都存在！
echo.

echo 🔄 开始编译集成测试:
echo.

if exist "SiteResConfig\SiteResConfig_Simple.pro" (
    echo 找到项目文件，开始编译...
    echo.
    
    cd SiteResConfig
    
    echo 清理旧文件...
    if exist "Makefile" del /Q "Makefile" 2>nul
    if exist "debug" rmdir /S /Q "debug" 2>nul
    if exist "release" rmdir /S /Q "release" 2>nul
    if exist "ui_*.h" del /Q "ui_*.h" 2>nul
    
    echo.
    echo 生成Makefile...
    qmake SiteResConfig_Simple.pro -spec win32-g++ "CONFIG+=debug"
    
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo ✅ qmake成功！
        echo.
        echo 开始编译...
        mingw32-make debug
        
        if %ERRORLEVEL% EQU 0 (
            echo.
            echo ✅ 编译成功！
            echo.
            echo 🎉 作动器1_1版本集成成功！
            echo.
            echo 集成验证:
            echo ✅ 所有源文件编译通过
            echo ✅ UI文件生成成功
            echo ✅ 链接过程无错误
            echo ✅ 可执行文件生成完成
            echo.
            
            if exist "debug\SiteResConfig.exe" (
                echo 🚀 程序已准备就绪！
                echo.
                echo 可以在程序中测试以下功能:
                echo 1. 创建ActuatorDialog1_1对话框实例
                echo 2. 使用ActuatorDataManager1_1管理数据
                echo 3. 测试新的数据结构功能
                echo 4. 验证Excel和JSON导入导出
                echo.
                
                set /p choice="是否启动程序进行测试？ (y/n): "
                if /i "%choice%"=="y" (
                    echo 启动程序...
                    start "" "debug\SiteResConfig.exe"
                )
            ) else (
                echo ❌ 可执行文件未找到，可能编译有问题
            )
        ) else (
            echo ❌ 编译失败！
            echo.
            echo 可能的问题:
            echo 1. 头文件包含路径问题
            echo 2. 语法错误
            echo 3. 链接错误
            echo 4. Qt版本兼容性问题
            echo.
            echo 请检查编译错误信息并修复。
        )
    ) else (
        echo ❌ qmake失败！
        echo.
        echo 可能的问题:
        echo 1. 项目文件语法错误
        echo 2. Qt环境配置问题
        echo 3. 文件路径问题
        echo.
        echo 请检查项目文件和Qt环境配置。
    )
    
    cd ..
) else (
    echo ❌ 项目文件未找到
    echo 请确保在正确的目录中运行此脚本
)

echo.
echo ========================================
echo 🔧 作动器1_1版本集成测试完成
echo ========================================
echo.

if %ERRORLEVEL% EQU 0 (
    echo ✅ 集成状态: 成功
    echo.
    echo 已完成的集成内容:
    echo ✅ 项目文件更新 - 添加了所有新的源文件
    echo ✅ 编译验证 - 所有文件编译通过
    echo ✅ 链接验证 - 程序链接成功
    echo ✅ 功能就绪 - 可以开始使用新功能
    echo.
    echo 下一步建议:
    echo 1. 在主程序中添加调用新对话框的菜单项
    echo 2. 测试ActuatorDialog1_1的显示和功能
    echo 3. 测试ActuatorDataManager1_1的数据管理功能
    echo 4. 验证Excel和JSON导入导出功能
    echo 5. 进行完整的功能测试
) else (
    echo ❌ 集成状态: 失败
    echo.
    echo 需要解决的问题:
    echo 1. 检查所有文件是否正确创建
    echo 2. 检查项目文件配置
    echo 3. 检查Qt环境和编译器配置
    echo 4. 修复编译错误
)

echo.
pause
