#pragma once

/**
 * @file PIDParametersDialog.h
 * @brief PID参数设置对话框类定义
 * @details 使用Qt Designer设计的PID参数输入对话框
 * <AUTHOR> Assistant
 * @date 2025-08-06
 * @version 1.0.0
 */

#include <QtWidgets/QDialog>
#include <QtCore/QString>

QT_BEGIN_NAMESPACE
QT_END_NAMESPACE

namespace Ui {
class PIDParametersDialog;
}

namespace UI {

/**
 * @brief PID参数结构体
 * @details 存储PID控制器的所有参数
 */
struct PIDParameters {
    double kp;              // 比例系数
    double ki;              // 积分系数
    double kd;              // 微分系数
    
    PIDParameters() 
        : kp(1.0), ki(0.1), kd(0.01) {}
};

/**
 * @brief PID参数设置对话框类
 * @details 使用.ui文件设计的标准Qt对话框
 */
class PIDParametersDialog : public QDialog {
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口
     */
    explicit PIDParametersDialog(QWidget* parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    virtual ~PIDParametersDialog();

    /**
     * @brief 获取PID参数
     * @return PID参数结构体
     */
    PIDParameters getPIDParameters() const;

private slots:
    /**
     * @brief 确定按钮点击前的验证
     */
    void onAcceptClicked();

private:
    Ui::PIDParametersDialog* ui;

    /**
     * @brief 初始化界面
     */
    void initializeUI();

    /**
     * @brief 连接信号槽
     */
    void connectSignals();
};

} // namespace UI
