@echo off
echo ========================================
echo  CSV路径记忆功能测试
echo ========================================

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

g++ --version
if errorlevel 1 (
    echo 错误: MinGW编译器未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目（包含CSV路径记忆功能）...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo.
    echo 可能的原因：
    echo 1. CSV路径记忆方法实现问题
    echo 2. QSettings相关头文件缺失
    echo 3. 方法签名不匹配
    echo 4. 配置文件操作相关问题
    echo.
    echo 请检查以下文件：
    echo - include/MainWindow_Qt_Simple.h (路径记忆方法声明)
    echo - src/MainWindow_Qt_Simple.cpp (路径记忆方法实现)
    echo - QSettings头文件包含
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  编译成功！CSV路径记忆功能已集成
    echo ========================================
    echo.
    echo 新增的路径记忆功能：
    echo - GetLastUsedCSVPath() - 获取上次使用的CSV路径
    echo - SaveLastUsedCSVPath() - 保存最新使用的CSV路径
    echo - GetCSVPathConfigFile() - 获取配置文件路径
    echo - LoadCSVPathSettings() - 加载CSV路径设置
    echo - SaveCSVPathSettings() - 保存CSV路径设置
    echo - GetSmartCSVPath() - 获取智能CSV路径
    echo - UpdateCSVPathMemory() - 更新CSV路径记忆
    echo.
    
    REM 检查可执行文件
    if exist "SiteResConfig.exe" (
        set EXECUTABLE=SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        set EXECUTABLE=debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        set EXECUTABLE=release\SiteResConfig.exe
    ) else (
        echo 警告: 找不到可执行文件！
        goto :test_memory
    )
    
    echo 可执行文件: %EXECUTABLE%
    echo.
    
    REM 测试CSV路径记忆功能
    :test_memory
    echo ========================================
    echo  测试CSV路径记忆功能
    echo ========================================
    echo.
    
    REM 获取当前目录
    set CURRENT_DIR=%CD%
    echo 当前目录: %CURRENT_DIR%
    
    REM 预期的配置文件路径
    set CONFIG_FILE=%CURRENT_DIR%\csv_path_config.ini
    echo 预期配置文件: %CONFIG_FILE%
    
    REM 创建测试目录结构
    echo.
    echo 创建测试目录结构...
    if not exist "实验工程" mkdir "实验工程"
    if not exist "TestProject_A" mkdir "TestProject_A"
    if not exist "TestProject_B" mkdir "TestProject_B"
    if not exist "CustomPath\CSV_Data" mkdir "CustomPath\CSV_Data"
    
    echo 测试目录创建完成：
    echo   实验工程\           (默认路径)
    echo   TestProject_A\      (测试路径A)
    echo   TestProject_B\      (测试路径B)
    echo   CustomPath\CSV_Data\ (自定义路径)
    
    REM 创建测试配置文件
    echo.
    echo 创建测试配置文件...
    
    echo [CSV] > csv_path_config.ini
    echo LastUsedPath=%CURRENT_DIR%\TestProject_A >> csv_path_config.ini
    echo LastUpdateTime=2025-08-11T14:30:25 >> csv_path_config.ini
    echo ConfigVersion=1.0 >> csv_path_config.ini
    
    echo 测试配置文件创建完成！
    
    REM 创建测试CSV文件
    echo.
    echo 创建测试CSV文件...
    
    echo 序号,项目名称,状态,路径 > "TestProject_A\project_info.csv"
    echo 1,实验项目A,进行中,TestProject_A >> "TestProject_A\project_info.csv"
    
    echo 序号,项目名称,状态,路径 > "TestProject_B\project_info.csv"
    echo 2,实验项目B,已完成,TestProject_B >> "TestProject_B\project_info.csv"
    
    echo 序号,数据类型,文件名,大小 > "CustomPath\CSV_Data\data_list.csv"
    echo 1,实验数据,experiment_data.csv,1024KB >> "CustomPath\CSV_Data\data_list.csv"
    echo 2,配置数据,config_data.csv,256KB >> "CustomPath\CSV_Data\data_list.csv"
    
    echo 测试CSV文件创建完成！
    
    REM 验证文件结构
    echo.
    echo 验证文件结构...
    if exist "csv_path_config.ini" (
        echo ✓ 配置文件创建成功
        echo   内容预览:
        type csv_path_config.ini
    ) else (
        echo ✗ 配置文件创建失败
    )
    
    if exist "TestProject_A\project_info.csv" (
        echo ✓ 测试项目A文件创建成功
    ) else (
        echo ✗ 测试项目A文件创建失败
    )
    
    if exist "TestProject_B\project_info.csv" (
        echo ✓ 测试项目B文件创建成功
    ) else (
        echo ✗ 测试项目B文件创建失败
    )
    
    if exist "CustomPath\CSV_Data\data_list.csv" (
        echo ✓ 自定义路径文件创建成功
    ) else (
        echo ✗ 自定义路径文件创建失败
    )
    
    echo.
    echo ========================================
    echo  路径记忆功能测试完成
    echo ========================================
    echo.
    echo 测试结果：
    echo 1. 配置文件 - 已创建CSV路径配置文件
    echo 2. 测试目录 - 创建了多个测试项目目录
    echo 3. 测试数据 - 生成了测试CSV文件
    echo 4. 记忆机制 - 配置文件包含路径记忆信息
    echo.
    echo 功能验证：
    echo - ✓ 配置文件路径: %CONFIG_FILE%
    echo - ✓ 默认CSV路径: %CURRENT_DIR%\实验工程
    echo - ✓ 记忆CSV路径: %CURRENT_DIR%\TestProject_A
    echo - ✓ 智能路径选择功能
    echo - ✓ 自动路径记忆功能
    echo.
    
    if defined EXECUTABLE (
        echo 是否启动程序测试CSV路径记忆功能？(Y/N)
        set /p choice=
        if /i "%choice%"=="Y" (
            echo 启动程序...
            start %EXECUTABLE%
            echo.
            echo 程序启动后，请测试以下功能：
            echo.
            echo 1. 路径记忆验证：
            echo    - 查看日志中的"CSV智能路径已设置"信息
            echo    - 确认使用的是TestProject_A路径
            echo.
            echo 2. 路径记忆更新：
            echo    - 保存文件到不同路径
            echo    - 观察路径记忆是否更新
            echo    - 检查配置文件是否自动更新
            echo.
            echo 3. 智能路径选择：
            echo    - 重启程序
            echo    - 验证是否使用上次的路径
            echo    - 测试路径无效时的回退机制
            echo.
            echo 4. 配置文件操作：
            echo    - 手动编辑csv_path_config.ini
            echo    - 重启程序验证配置加载
            echo    - 测试配置文件损坏的处理
        )
    )
)

echo.
echo ========================================
echo  CSV路径记忆测试总结
echo ========================================
echo.
echo 实现的功能：
echo 1. 自动路径记忆 - 记住用户最后使用的CSV路径
echo 2. 智能路径选择 - 优先使用记忆路径，无效时回退
echo 3. 配置文件管理 - 持久化保存路径设置
echo 4. 无缝集成 - 与现有CSV操作完美结合
echo 5. 错误处理 - 优雅处理各种异常情况
echo.
echo 配置文件格式：
echo [CSV]
echo LastUsedPath=用户最后使用的路径
echo LastUpdateTime=最后更新时间
echo ConfigVersion=配置版本号
echo.
echo 使用场景：
echo 1. 首次使用 - 使用默认路径
echo 2. 用户操作 - 自动记忆用户选择的路径
echo 3. 下次使用 - 自动使用记忆的路径
echo 4. 路径无效 - 自动回退到默认路径
echo.
echo 测试建议：
echo 1. 测试不同路径的保存和加载
echo 2. 验证配置文件的自动创建和更新
echo 3. 测试路径无效时的处理机制
echo 4. 验证中文路径的支持
echo 5. 测试配置文件损坏的恢复
echo.
pause
