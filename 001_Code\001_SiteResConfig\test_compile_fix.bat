@echo off
echo.
echo ========================================
echo Actuator1_1 Compilation Fix Test
echo ========================================
echo.

echo Fixed compilation errors:
echo 1. const method calling non-const method
echo 2. undeclared member variables
echo.

if exist "SiteResConfig\SiteResConfig_Simple.pro" (
    echo Found project file, starting compilation...
    echo.
    
    cd SiteResConfig
    
    echo Cleaning old files...
    if exist "Makefile" del /Q "Makefile" 2>nul
    if exist "debug" rmdir /S /Q "debug" 2>nul
    if exist "release" rmdir /S /Q "release" 2>nul
    if exist "ui_*.h" del /Q "ui_*.h" 2>nul
    
    echo.
    echo Running qmake...
    qmake SiteResConfig_Simple.pro -spec win32-g++ "CONFIG+=debug"
    
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo qmake successful!
        echo.
        echo Starting compilation...
        mingw32-make debug
        
        if %ERRORLEVEL% EQU 0 (
            echo.
            echo *** COMPILATION SUCCESSFUL! ***
            echo.
            echo All ActuatorDialog1_1 files compiled successfully:
            echo - ActuatorStructs1_1.cpp
            echo - ActuatorDataManager1_1.cpp  
            echo - ActuatorDialog1_1.cpp
            echo - ActuatorDialog1_1.ui
            echo.
            
            if exist "debug\SiteResConfig.exe" (
                echo Executable created successfully!
                echo.
                echo You can now:
                echo 1. Test ActuatorDialog1_1 dialog
                echo 2. Test data management features
                echo 3. Test JSON import/export
                echo 4. Integrate into main application
                echo.
                
                set /p choice="Launch program for testing? (y/n): "
                if /i "%choice%"=="y" (
                    start "" "debug\SiteResConfig.exe"
                )
            ) else (
                echo ERROR: Executable not found
            )
        ) else (
            echo.
            echo *** COMPILATION FAILED ***
            echo Please check the error messages above.
        )
    ) else (
        echo.
        echo *** QMAKE FAILED ***
        echo Please check Qt environment and project file.
    )
    
    cd ..
) else (
    echo ERROR: Project file not found
    echo Please run this script from the correct directory
)

echo.
echo ========================================
echo Test completed
echo ========================================
echo.
pause
