#ifndef DATAEXPORTERFACTORY_H
#define DATAEXPORTERFACTORY_H

#include "IDataExporter.h"
#include <memory>
#include <QString>

class SensorDataManager_1_2;

/**
 * @brief 数据导出器工厂
 * @details 负责创建不同类型的数据导出器
 */
class DataExporterFactory {
public:
    enum class ExportFormat {
        JSON,
        XLS,
        Unknown
    };
    
    /**
     * @brief 创建数据导出器
     * @param format 导出格式
     * @param sensorManager 传感器数据管理器
     * @return 导出器智能指针
     */
    static std::unique_ptr<IDataExporter> createExporter(
        ExportFormat format, 
        SensorDataManager_1_2* sensorManager = nullptr);
    
    /**
     * @brief 根据文件扩展名创建导出器
     * @param extension 文件扩展名
     * @param sensorManager 传感器数据管理器
     * @return 导出器智能指针
     */
    static std::unique_ptr<IDataExporter> createExporterByExtension(
        const QString& extension, 
        SensorDataManager_1_2* sensorManager = nullptr);
    
    /**
     * @brief 根据文件路径创建导出器
     * @param filePath 文件路径
     * @param sensorManager 传感器数据管理器
     * @return 导出器智能指针
     */
    static std::unique_ptr<IDataExporter> createExporterByFilePath(
        const QString& filePath, 
        SensorDataManager_1_2* sensorManager = nullptr);
    
    /**
     * @brief 获取支持的格式列表
     * @return 格式描述列表
     */
    static QStringList getSupportedFormats();
    
    /**
     * @brief 获取文件过滤器字符串
     * @return 文件对话框过滤器字符串
     */
    static QString getFileFilter();
    
    /**
     * @brief 根据扩展名获取格式枚举
     * @param extension 文件扩展名
     * @return 格式枚举
     */
    static ExportFormat getFormatByExtension(const QString& extension);
    
    /**
     * @brief 获取格式的扩展名
     * @param format 格式枚举
     * @return 扩展名
     */
    static QString getExtensionByFormat(ExportFormat format);
    
    /**
     * @brief 获取格式的描述
     * @param format 格式枚举
     * @return 格式描述
     */
    static QString getFormatDescription(ExportFormat format);

private:
    DataExporterFactory() = default; // 禁止实例化
    
    /**
     * @brief 从文件路径中提取扩展名
     * @param filePath 文件路径
     * @return 扩展名（小写，不含点）
     */
    static QString extractExtension(const QString& filePath);
};

#endif // DATAEXPORTERFACTORY_H
