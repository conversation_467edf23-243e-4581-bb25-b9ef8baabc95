/**
 * @file ActuatorViewModel1_2.h
 * @brief 作动器视图模型1_2版本 - 主界面解耦合专用
 * @details 基于MVVM模式设计，封装MainWindow中与作动器数据交互的所有逻辑
 *          实现UI层和数据层的完全分离，提供统一的业务逻辑接口
 * <AUTHOR> Assistant
 * @date 2025-08-22
 * @version 1.2.0
 */

#ifndef ACTUATOR_VIEW_MODEL_1_2_H
#define ACTUATOR_VIEW_MODEL_1_2_H

#include <QObject>
#include <QString>
#include <QStringList>
#include <QList>
#include <QMap>
#include <QMutex>
#include <QDateTime>
#include <memory>
#include <functional>

// 前向声明
class ActuatorDataManager_1_2;
class QTreeWidgetItem;

namespace DataModels {
    class TestProject;
}

namespace UI {
    struct ActuatorParams_1_2;
    struct ActuatorGroup_1_2;
}

/**
 * @brief 作动器视图模型1_2版本类
 * @details 专为MainWindow解耦合设计的ViewModel层
 *          封装所有作动器相关的业务逻辑和数据操作
 *          
 * 设计原则：
 * - 单一职责：只处理作动器相关的业务逻辑
 * - 接口统一：提供一致的错误处理和返回值
 * - 线程安全：支持多线程环境下的安全访问
 * - 可测试性：支持单元测试和集成测试
 */
class ActuatorViewModel1_2 : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 错误类型枚举
     */
    enum class ErrorType {
        NoError = 0,
        ValidationError,    // 数据验证错误
        DuplicateError,     // 重复数据错误
        NotFoundError,      // 数据不存在错误
        DatabaseError,      // 数据库操作错误
        SystemError         // 系统级错误
    };

    /**
     * @brief 配置参数结构体
     */
    struct Config {
        bool enableCache = true;        // 启用缓存
        int cacheTimeout = 300;         // 缓存超时时间(秒)
        bool enableValidation = true;   // 启用数据验证
        bool enableLogging = true;      // 启用日志记录
        QString logLevel = "INFO";      // 日志级别
        bool threadSafe = true;         // 线程安全模式
    };

public:
    /**
     * @brief 构造函数 - 自动创建数据管理器
     * @param parent 父对象指针
     */
    explicit ActuatorViewModel1_2(QObject* parent = nullptr);

    /**
     * @brief 构造函数 - 使用外部数据管理器
     * @param dataManager 外部数据管理器指针
     * @param parent 父对象指针
     */
    explicit ActuatorViewModel1_2(ActuatorDataManager_1_2* dataManager, QObject* parent = nullptr);

    /**
     * @brief 析构函数
     */
    virtual ~ActuatorViewModel1_2();

    // ==================== 配置管理接口 ====================
    
    /**
     * @brief 设置配置参数
     * @param config 配置参数
     */
    void setConfig(const Config& config);

    /**
     * @brief 获取配置参数
     * @return 当前配置参数
     */
    Config getConfig() const;

    // ==================== 基础CRUD操作接口 ====================
    
    /**
     * @brief 保存作动器参数
     * @param params 作动器参数
     * @return 成功返回true，失败返回false
     */
    //bool saveActuator(const UI::ActuatorParams_1_2& params); // ❌ 注释：不再使用无组ID版本

    /**
     * @brief 保存作动器参数到指定组
     * @param params 作动器参数
     * @param groupId 组ID
     * @return 成功返回true，失败返回false
     */
    bool saveActuator(const UI::ActuatorParams_1_2& params, int groupId);

    /**
     * @brief 获取作动器参数
     * @param serialNumber 序列号
     * @return 作动器参数，失败返回空对象
     */
    //UI::ActuatorParams_1_2 getActuator(const QString& serialNumber) const; // ❌ 注释：不再使用无组ID版本

    /**
     * @brief 获取指定组中的作动器参数
     * @param serialNumber 序列号
     * @param groupId 组ID
     * @return 作动器参数，失败返回空对象
     */
    UI::ActuatorParams_1_2 getActuator(const QString& serialNumber, int groupId) const;

    /**
     * @brief 更新作动器参数
     * @param serialNumber 序列号
     * @param params 新的作动器参数
     * @return 成功返回true，失败返回false
     */
    //bool updateActuator(const QString& serialNumber, const UI::ActuatorParams_1_2& params); // ❌ 注释：不再使用无组ID版本

    /**
     * @brief 更新指定组中的作动器参数
     * @param serialNumber 序列号
     * @param params 新的作动器参数
     * @param groupId 组ID
     * @return 成功返回true，失败返回false
     */
    bool updateActuator(const QString& serialNumber, const UI::ActuatorParams_1_2& params, int groupId);

    /**
     * @brief 删除作动器
     * @param serialNumber 序列号
     * @return 成功返回true，失败返回false
     */
    //bool removeActuator(const QString& serialNumber); // ❌ 注释：不再使用无组ID版本

    /**
     * @brief 删除指定组中的作动器
     * @param serialNumber 序列号
     * @param groupId 组ID
     * @return 成功返回true，失败返回false
     */
    bool removeActuator(const QString& serialNumber, int groupId);

    /**
     * @brief 检查作动器是否存在
     * @param serialNumber 序列号
     * @return 存在返回true，不存在返回false
     */
    //bool hasActuator(const QString& serialNumber) const; // ❌ 注释：不再使用无组ID版本

    /**
     * @brief 检查指定组中作动器是否存在
     * @param serialNumber 序列号
     * @param groupId 组ID
     * @return 存在返回true，不存在返回false
     */
    bool hasActuator(const QString& serialNumber, int groupId) const;

    // ==================== 作动器组管理接口 ====================
    
    /**
     * @brief 保存作动器组
     * @param group 作动器组
     * @return 成功返回true，失败返回false
     */
    bool saveActuatorGroup(const UI::ActuatorGroup_1_2& group);

    /**
     * @brief 获取作动器组
     * @param groupId 组ID
     * @return 作动器组，失败返回空对象
     */
    UI::ActuatorGroup_1_2 getActuatorGroup(int groupId) const;

    /**
     * @brief 更新作动器组
     * @param groupId 组ID
     * @param group 新的作动器组
     * @return 成功返回true，失败返回false
     */
    bool updateActuatorGroup(int groupId, const UI::ActuatorGroup_1_2& group);

    /**
     * @brief 删除作动器组
     * @param groupId 组ID
     * @return 成功返回true，失败返回false
     */
    bool removeActuatorGroup(int groupId);

    /**
     * @brief 获取所有作动器组
     * @return 作动器组列表
     */
    QList<UI::ActuatorGroup_1_2> getAllActuatorGroups() const;

    /**
     * @brief 检查作动器组是否存在
     * @param groupId 组ID
     * @return 存在返回true，不存在返回false
     */
    bool hasActuatorGroup(int groupId) const;

    // ==================== 批量操作接口 ====================
    
    /**
     * @brief 获取所有作动器
     * @return 作动器列表
     */
    QList<UI::ActuatorParams_1_2> getAllActuators() const;

    /**
     * @brief 根据类型获取作动器
     * @param actuatorType 作动器类型
     * @return 作动器列表
     */
    QList<UI::ActuatorParams_1_2> getActuatorsByType(const QString& actuatorType) const;

    /**
     * @brief 根据组ID获取作动器
     * @param groupId 组ID
     * @return 作动器列表
     */
    QList<UI::ActuatorParams_1_2> getActuatorsByGroup(int groupId) const;

    /**
     * @brief 获取所有作动器序列号
     * @return 序列号列表
     */
    QStringList getAllActuatorSerialNumbers() const;

    // ==================== 数据验证接口 ====================
    
    /**
     * @brief 验证作动器参数
     * @param params 作动器参数
     * @return 验证通过返回true，失败返回false
     */
    bool validateActuatorParams(const UI::ActuatorParams_1_2& params) const;

    /**
     * @brief 验证作动器组
     * @param group 作动器组
     * @return 验证通过返回true，失败返回false
     */
    bool validateActuatorGroup(const UI::ActuatorGroup_1_2& group) const;

    /**
     * @brief 验证作动器在组内是否冲突
     * @param actuator 作动器参数
     * @param group 作动器组
     * @return 无冲突返回true，有冲突返回false
     */
    bool validateActuatorInGroup(const UI::ActuatorParams_1_2& actuator, const UI::ActuatorGroup_1_2& group) const;

    /**
     * @brief 验证所有作动器
     * @return 错误信息列表，空列表表示无错误
     */
    QStringList validateAllActuators() const;

    /**
     * @brief 验证所有作动器组
     * @return 错误信息列表，空列表表示无错误
     */
    QStringList validateAllActuatorGroups() const;

    // ==================== 序列号管理接口 ====================
    
//    /**
//     * @brief 生成下一个序列号
//     * @param prefix 前缀，默认为"ACT"
//     * @return 生成的序列号
//     */
//    QString generateNextSerialNumber(const QString& prefix = "ACT") const;

//    /**
//     * @brief 检查序列号是否唯一
//     * @param serialNumber 序列号
//     * @return 唯一返回true，重复返回false
//     */
//    bool isSerialNumberUnique(const QString& serialNumber) const;

    /**
     * @brief 检查序列号在组内是否唯一
     * @param serialNumber 序列号
     * @param groupId 组ID
     * @return 唯一返回true，重复返回false
     */
    bool isSerialNumberUniqueInGroup(const QString& serialNumber, int groupId) const;

    /**
     * @brief 检查序列号在组内是否唯一（排除指定ID）
     * @param serialNumber 序列号
     * @param groupId 组ID
     * @param excludeActuatorId 排除的作动器ID
     * @return 唯一返回true，重复返回false
     */
    bool isSerialNumberUniqueInGroup(const QString& serialNumber, int groupId, int excludeActuatorId) const;

    /**
     * @brief 查找重复的序列号
     * @return 重复序列号列表
     */
    QStringList findDuplicateSerialNumbers() const;

    // ==================== 数据统计接口 ====================

    /**
     * @brief 获取作动器类型统计
     * @return 类型统计映射表
     */
    QMap<QString, int> getActuatorTypeStatistics() const;

    /**
     * @brief 获取单位类型统计
     * @return 单位类型统计映射表
     */
    QMap<QString, int> getUnitTypeStatistics() const;

    /**
     * @brief 获取极性统计
     * @return 极性统计映射表
     */
    QMap<QString, int> getPolarityStatistics() const;

    /**
     * @brief 获取作动器数量
     * @return 作动器总数
     */
    int getActuatorCount() const;

    /**
     * @brief 获取作动器组数量
     * @return 作动器组总数
     */
    int getActuatorGroupCount() const;

    /**
     * @brief 获取已使用的作动器类型
     * @return 类型列表
     */
    QStringList getUsedActuatorTypes() const;

    /**
     * @brief 获取已使用的单位类型
     * @return 单位类型列表
     */
    QStringList getUsedUnitTypes() const;

    // ==================== 数据同步接口 ====================

    /**
     * @brief 同步内存数据（准备数据以供保存）
     * @param project 项目指针（保留兼容性，但不使用TestProject方法）
     * @note 由于TestProject方法已不存在，此方法主要确保数据在DataManager中是最新的
     */
    void syncMemoryDataToProject(DataModels::TestProject* project = nullptr);

    /**
     * @brief 同步项目数据到内存（从外部数据源加载）
     * @param project 项目指针（保留兼容性，但不使用TestProject方法）
     * @note 由于TestProject方法已不存在，此方法主要是清空现有数据
     */
    void syncProjectDataToMemory(DataModels::TestProject* project = nullptr);

    // ==================== 界面扩展支持接口 ====================

    /**
     * @brief 注册界面扩展处理器
     * @param extensionName 扩展名称
     * @param handler 处理器函数
     */
    void registerUIExtension(const QString& extensionName, std::function<QWidget*()> handler);

    /**
     * @brief 获取支持的界面扩展列表
     * @return 扩展名称列表
     */
    QStringList getSupportedUIExtensions() const;

    /**
     * @brief 创建扩展界面
     * @param extensionName 扩展名称
     * @return 界面指针，失败返回nullptr
     */
    QWidget* createExtensionUI(const QString& extensionName);

    /**
     * @brief 注册数据字段扩展
     * @param fieldName 字段名称
     * @param fieldType 字段类型
     * @param defaultValue 默认值
     */
    void registerDataFieldExtension(const QString& fieldName, const QString& fieldType, const QVariant& defaultValue);

    /**
     * @brief 获取扩展数据字段
     * @param serialNumber 序列号
     * @param fieldName 字段名称
     * @return 字段值
     */
    QVariant getExtensionFieldValue(const QString& serialNumber, const QString& fieldName) const;

    /**
     * @brief 设置扩展数据字段
     * @param serialNumber 序列号
     * @param fieldName 字段名称
     * @param value 字段值
     * @return 成功返回true，失败返回false
     */
    bool setExtensionFieldValue(const QString& serialNumber, const QString& fieldName, const QVariant& value);

    // ==================== 格式兼容性保证接口 ====================

    /**
     * @brief 验证JSON格式兼容性
     * @param jsonData JSON数据
     * @return 兼容返回true，不兼容返回false
     */
    bool validateJSONCompatibility(const QJsonObject& jsonData) const;

    /**
     * @brief 转换为兼容的JSON格式
     * @return 兼容的JSON对象
     */
    QJsonObject toCompatibleJSON() const;

//    /**
//     * @brief 从兼容的JSON格式加载
//     * @param jsonData JSON数据
//     * @return 成功返回true，失败返回false
//     */
//    bool fromCompatibleJSON(const QJsonObject& jsonData);

    /**
     * @brief 获取当前数据格式版本
     * @return 版本字符串
     */
    QString getDataFormatVersion() const;

    /**
     * @brief 设置数据格式版本
     * @param version 版本字符串
     */
    void setDataFormatVersion(const QString& version);

    /**
     * @brief 清空内存数据
     */
    void clearMemoryData();

    // ==================== 文件操作接口 ====================

//    /**
//     * @brief 从JSON文件加载数据
//     * @param filePath JSON文件路径
//     * @return 成功返回true，失败返回false
//     */
//    bool loadFromJSONFile(const QString& filePath);

//    /**
//     * @brief 保存数据到JSON文件
//     * @param filePath JSON文件路径
//     * @return 成功返回true，失败返回false
//     */
//    bool saveToJSONFile(const QString& filePath) const;

//    /**
//     * @brief 从JSON字符串加载数据
//     * @param jsonString JSON字符串
//     * @return 成功返回true，失败返回false
//     */
//    bool loadFromJSONString(const QString& jsonString);

    /**
     * @brief 导出为JSON字符串
     * @return JSON字符串
     */
    // 🚫 已注释：独立JSON导出功能已废弃
    // QString exportToJSONString() const;

    // ==================== 兼容性接口 ====================

    /**
     * @brief 获取内部的ActuatorDataManager指针（用于兼容性）
     * @return ActuatorDataManager指针
     * @note 此方法主要用于与需要ActuatorDataManager*参数的旧接口兼容
     */
    ActuatorDataManager_1_2* getDataManager() const;

    // ==================== 业务逻辑接口 ====================

    /**
     * @brief 创建作动器组（业务逻辑）
     * @param groupName 组名称
     * @return 成功返回组ID，失败返回-1
     */
    int createActuatorGroupBusiness(const QString& groupName);

    /**
     * @brief 删除作动器组（业务逻辑）
     * @param groupId 组ID
     * @return 成功返回true
     */
    bool deleteActuatorGroupBusiness(int groupId);

    /**
     * @brief 检查组名是否存在（业务逻辑）
     * @param groupName 组名称
     * @return 存在返回true
     */
    bool isActuatorGroupNameExistsBusiness(const QString& groupName) const;

    /**
     * @brief 创建作动器设备（业务逻辑）
     * @param groupId 组ID
     * @param params 作动器参数
     * @return 成功返回true
     */
    bool createActuatorDeviceBusiness(int groupId, const UI::ActuatorParams_1_2& params);

    /**
     * @brief 编辑作动器设备（业务逻辑）
     * @param serialNumber 序列号
     * @param newParams 新参数
     * @return 成功返回true
     */
    bool editActuatorDeviceBusiness(const QString& serialNumber, const UI::ActuatorParams_1_2& newParams);

    /**
     * @brief 编辑作动器设备（业务逻辑，带组ID的精准版本）
     * @param serialNumber 序列号
     * @param newParams 新参数
     * @param groupId 组ID
     * @return 成功返回true
     */
    bool editActuatorDeviceBusinessWithGroupId(const QString& serialNumber, const UI::ActuatorParams_1_2& newParams, int groupId);

    /**
     * @brief 删除作动器设备（业务逻辑）
     * @param serialNumber 序列号
     * @return 成功返回true
     */
    bool deleteActuatorDeviceBusiness(const QString& serialNumber);

    /**
     * @brief 检查序列号在组内是否唯一（业务逻辑）
     * @param serialNumber 序列号
     * @param groupId 组ID
     * @param excludeId 排除的作动器ID（编辑时使用）
     * @return 唯一返回true
     */
    bool isSerialNumberUniqueInGroupBusiness(const QString& serialNumber, int groupId, int excludeId = -1) const;

    /**
     * @brief 从组名提取组ID（业务逻辑）
     * @param groupName 组名称
     * @return 组ID，失败返回-1
     */
    int extractGroupIdFromNameBusiness(const QString& groupName) const;

    /**
     * @brief 生成作动器详细信息（业务逻辑）
     * @param serialNumber 序列号
     * @return 详细信息字符串
     */
    QString generateActuatorDetailedInfoBusiness(const QString& serialNumber) const;

    /**
     * @brief 获取所有组名列表（业务逻辑）
     * @return 组名列表
     */
    QStringList getActuatorGroupNamesBusiness() const;

    /**
     * @brief 验证作动器参数（业务逻辑）
     * @param params 作动器参数
     * @return 验证通过返回true
     */
    bool validateActuatorParamsBusiness(const UI::ActuatorParams_1_2& params) const;

    /**
     * @brief 根据组ID获取组名
     * @param groupId 组ID
     * @return 组名，失败返回空字符串
     */
    QString getActuatorGroupNameBusiness(int groupId) const;

    /**
     * @brief 生成作动器组调试信息（业务逻辑）
     * @param groupName 组名称
     * @return 调试信息字符串
     */
    QString generateActuatorGroupDebugInfoBusiness(const QString& groupName) const;

    /**
     * @brief 生成作动器设备调试信息（业务逻辑）
     * @param serialNumber 序列号
     * @return 调试信息字符串
     */
    QString generateActuatorDeviceDebugInfoBusiness(const QString& serialNumber) const;

    /**
     * @brief 清空所有作动器
     */
    void clearAllActuators();

    /**
     * @brief 清空所有作动器组
     */
    void clearAllActuatorGroups();

    /**
     * @brief 清空所有数据
     */
    void clearAll();

    // ==================== UI交互接口 ====================

//    /**
//     * @brief 在组中创建作动器
//     * @param groupName 组名称
//     * @param params 作动器参数
//     * @return 成功返回true，失败返回false
//     */
//    bool createActuatorInGroup(const QString& groupName, const UI::ActuatorParams_1_2& params);

    /**
     * @brief 编辑作动器设备
     * @param serialNumber 序列号
     * @param newParams 新的作动器参数
     * @return 成功返回true，失败返回false
     */
    bool editActuatorDevice(const QString& serialNumber, const UI::ActuatorParams_1_2& newParams);

    /**
     * @brief 删除作动器设备
     * @param serialNumber 序列号
     * @return 成功返回true，失败返回false
     */
    bool deleteActuatorDevice(const QString& serialNumber);

    /**
     * @brief 获取作动器所属的组ID
     * @param serialNumber 序列号
     * @return 组ID，如果未找到返回-1
     */
    int getActuatorGroupId(const QString& serialNumber) const;

    /**
     * @brief 创建或更新作动器组
     * @param groupName 组名称
     * @param params 作动器参数
     * @return 成功返回true，失败返回false
     */
    bool createOrUpdateActuatorGroup(const QString& groupName, const UI::ActuatorParams_1_2& params);

    /**
     * @brief 从组名称提取组ID
     * @param groupName 组名称
     * @return 组ID，失败返回-1
     */
    int extractActuatorGroupId(const QString& groupName) const;

    /**
     * @brief 获取作动器统计信息
     * @return 统计信息字符串
     */
    QString getActuatorStatistics() const;

//    /**
//     * @brief 根据名称获取作动器详细信息
//     * @param actuatorName 作动器名称
//     * @return 详细信息字符串
//     */
//    QString getActuatorDetailsByName(const QString& actuatorName) const;

    /**
     * @brief 获取作动器设备详细信息
     * @param serialNumber 序列号
     * @return 详细信息字符串
     */
    QString getActuatorDeviceDetails(const QString& serialNumber) const;

    // ==================== 数据导出接口 ====================

    /**
     * @brief 导出到CSV数据
     * @return CSV数据行列表
     */
    QVector<QStringList> exportToCSVData() const;

    /**
     * @brief 导出组数据到CSV
     * @return CSV数据行列表
     */
    QVector<QStringList> exportGroupsToCSVData() const;

    /**
     * @brief 导出到JSON数组
     * @return JSON数组
     */
    // 🚫 已注释：独立JSON导出功能已废弃
    // QJsonArray exportToJSONArray() const;

    // ==================== 错误处理接口 ====================

    /**
     * @brief 获取最后的错误信息
     * @return 错误信息字符串
     */
    QString getLastError() const;

    /**
     * @brief 检查是否有错误
     * @return 有错误返回true，无错误返回false
     */
    bool hasError() const;

    /**
     * @brief 获取错误类型
     * @return 错误类型枚举
     */
    ErrorType getErrorType() const;

    /**
     * @brief 清除最后的错误
     */
    void clearLastError();

    // ==================== 调试和诊断接口 ====================

    /**
     * @brief 添加作动器设备调试信息
     * @param debugInfo 调试信息字符串引用
     * @param serialNumber 序列号
     */
    void addActuatorDeviceDebugInfo(QString& debugInfo, const QString& serialNumber) const;

    /**
     * @brief 添加作动器组调试信息
     * @param debugInfo 调试信息字符串引用
     * @param groupName 组名称
     */
    void addActuatorGroupDebugInfo(QString& debugInfo, const QString& groupName) const;

    /**
     * @brief 验证数据完整性
     * @return 验证通过返回true，失败返回false
     */
    bool validateDataIntegrity() const;

signals:
    // ==================== 数据变更信号 ====================

    /**
     * @brief 作动器数据变更信号
     * @param serialNumber 序列号
     * @param operation 操作类型（create/update/delete）
     */
    void actuatorDataChanged(const QString& serialNumber, const QString& operation);

    /**
     * @brief 作动器组数据变更信号
     * @param groupId 组ID
     * @param operation 操作类型（create/update/delete）
     */
    void actuatorGroupDataChanged(int groupId, const QString& operation);

    /**
     * @brief 统计信息变更信号
     */
    void statisticsChanged();

    /**
     * @brief 错误发生信号
     * @param error 错误信息
     */
    void errorOccurred(const QString& error);

    /**
     * @brief 操作完成信号
     * @param operation 操作名称
     * @param success 是否成功
     */
    void operationCompleted(const QString& operation, bool success);

    // ==================== 进度通知信号 ====================

    /**
     * @brief 批量操作进度信号
     * @param current 当前进度
     * @param total 总数
     */
    void batchOperationProgress(int current, int total);

    /**
     * @brief 批量操作完成信号
     * @param success 是否成功
     * @param summary 摘要信息
     */
    void batchOperationCompleted(bool success, const QString& summary);

    /**
     * @brief 同步进度信号
     * @param stage 当前阶段
     * @param percentage 进度百分比
     */
    void syncProgress(const QString& stage, int percentage);

    /**
     * @brief 同步完成信号
     * @param success 是否成功
     */
    void syncCompleted(bool success);

    // ==================== 业务事件信号 ====================

    /**
     * @brief 作动器组已创建（业务事件）
     * @param groupName 组名称
     * @param groupId 组ID
     */
    void actuatorGroupCreatedBusiness(const QString& groupName, int groupId);

    /**
     * @brief 作动器设备已创建（业务事件）
     * @param serialNumber 序列号
     * @param groupId 组ID
     */
    void actuatorDeviceCreatedBusiness(const QString& serialNumber, int groupId);

    /**
     * @brief 作动器设备已编辑（业务事件）
     * @param serialNumber 序列号
     */
    void actuatorDeviceEditedBusiness(const QString& serialNumber, int groupId);

    /**
     * @brief 作动器设备已删除（业务事件）
     * @param serialNumber 序列号
     */
    void actuatorDeviceDeletedBusiness(const QString& serialNumber);

    /**
     * @brief 业务验证错误（业务事件）
     * @param error 错误信息
     */
    void businessValidationError(const QString& error);

private:
    // ==================== 私有成员变量 ====================

    // 数据管理器
    std::unique_ptr<ActuatorDataManager_1_2> dataManager_;
    bool ownsDataManager_;  // 是否拥有数据管理器的所有权

    // 配置参数
    Config config_;

    // 错误处理
    mutable QString lastError_;
    mutable ErrorType errorType_;
    mutable bool hasError_;

    // 线程安全
    mutable QMutex dataMutex_;
    mutable QMutex cacheMutex_;
    mutable QMutex errorMutex_;

    // 缓存机制
    mutable QMap<QString, UI::ActuatorParams_1_2> actuatorCache_;
    mutable QMap<int, UI::ActuatorGroup_1_2> groupCache_;
    mutable QDateTime cacheTimestamp_;
    mutable bool cacheValid_;

    // 统计缓存
    mutable QMap<QString, int> typeStatisticsCache_;
    mutable QMap<QString, int> unitStatisticsCache_;
    mutable QMap<QString, int> polarityStatisticsCache_;
    mutable bool statisticsLoaded_;

    // 延迟加载标志
    mutable bool groupsLoaded_;
    mutable bool actuatorsLoaded_;

    // 日志记录
    mutable QStringList logEntries_;
    static const int MAX_LOG_ENTRIES = 1000;

    // 界面扩展支持
    QMap<QString, std::function<QWidget*()>> uiExtensionHandlers_;
    QMap<QString, QString> extensionFieldTypes_;
    QMap<QString, QVariant> extensionFieldDefaults_;
    QMap<QString, QMap<QString, QVariant>> extensionFieldValues_; // serialNumber -> fieldName -> value

    // 格式兼容性
    QString dataFormatVersion_;
    static const QString CURRENT_FORMAT_VERSION;

    // ==================== 私有辅助方法 ====================

    /**
     * @brief 初始化ViewModel
     */
    void initialize();

    // ==================== 私有实现方法 ====================

//    /**
//     * @brief 保存作动器实现方法
//     * @param params 作动器参数
//     * @return 成功返回true，失败返回false
//     */
//    bool saveActuatorImpl(const UI::ActuatorParams_1_2& params);

    /**
     * @brief 保存作动器到指定组实现方法
     * @param params 作动器参数
     * @param groupId 组ID
     * @return 成功返回true，失败返回false
     */
    bool saveActuatorImpl(const UI::ActuatorParams_1_2& params, int groupId);

    /**
     * @brief 获取作动器实现方法
     * @param serialNumber 序列号
     * @return 作动器参数
     */
    UI::ActuatorParams_1_2 getActuatorImpl(const QString& serialNumber) const;

    /**
     * @brief 获取指定组中作动器实现方法
     * @param serialNumber 序列号
     * @param groupId 组ID
     * @return 作动器参数
     */
    UI::ActuatorParams_1_2 getActuatorImpl(const QString& serialNumber, int groupId) const;

    /**
     * @brief 更新作动器实现方法
     * @param serialNumber 序列号
     * @param params 新的作动器参数
     * @return 成功返回true，失败返回false
     */
    //bool updateActuatorImpl(const QString& serialNumber, const UI::ActuatorParams_1_2& params); // ❌ 注释：不再使用无组ID版本

    /**
     * @brief 更新指定组中作动器实现方法
     * @param serialNumber 序列号
     * @param params 新的作动器参数
     * @param groupId 组ID
     * @return 成功返回true，失败返回false
     */
    bool updateActuatorImpl(const QString& serialNumber, const UI::ActuatorParams_1_2& params, int groupId);

    /**
     * @brief 删除作动器实现方法
     * @param serialNumber 序列号
     * @return 成功返回true，失败返回false
     */
    //bool removeActuatorImpl(const QString& serialNumber); // ❌ 注释：不再使用无组ID版本

    /**
     * @brief 删除指定组中作动器实现方法
     * @param serialNumber 序列号
     * @param groupId 组ID
     * @return 成功返回true，失败返回false
     */
    bool removeActuatorImpl(const QString& serialNumber, int groupId);

    /**
     * @brief 检查作动器是否存在实现方法
     * @param serialNumber 序列号
     * @return 存在返回true，不存在返回false
     */
    //bool hasActuatorImpl(const QString& serialNumber) const; // ❌ 注释：不再使用无组ID版本

    /**
     * @brief 检查指定组中作动器是否存在实现方法
     * @param serialNumber 序列号
     * @param groupId 组ID
     * @return 存在返回true，不存在返回false
     */
    bool hasActuatorImpl(const QString& serialNumber, int groupId) const;

    /**
     * @brief 保存作动器组实现方法
     * @param group 作动器组
     * @return 成功返回true，失败返回false
     */
    bool saveActuatorGroupImpl(const UI::ActuatorGroup_1_2& group);

    /**
     * @brief 获取作动器组实现方法
     * @param groupId 组ID
     * @return 作动器组
     */
    UI::ActuatorGroup_1_2 getActuatorGroupImpl(int groupId) const;

    /**
     * @brief 获取所有作动器组实现方法
     * @return 作动器组列表
     */
    QList<UI::ActuatorGroup_1_2> getAllActuatorGroupsImpl() const;

    /**
     * @brief 获取所有作动器实现方法
     * @return 作动器列表
     */
    QList<UI::ActuatorParams_1_2> getAllActuatorsImpl() const;

    /**
     * @brief 获取作动器类型统计实现方法
     * @return 类型统计映射表
     */
    QMap<QString, int> getActuatorTypeStatisticsImpl() const;

    /**
     * @brief 清理资源
     */
    void cleanup();

    /**
     * @brief 释放资源
     */
    void releaseResources();

    // ==================== 错误处理方法 ====================

    /**
     * @brief 清除错误
     */
    void clearError() const;

    /**
     * @brief 设置错误信息
     * @param error 错误信息
     * @param type 错误类型
     */
    void setError(const QString& error, ErrorType type = ErrorType::SystemError) const;

    /**
     * @brief 记录错误日志
     * @param operation 操作名称
     * @param error 错误信息
     */
    void logError(const QString& operation, const QString& error) const;

    /**
     * @brief 添加日志条目
     * @param level 日志级别
     * @param message 日志消息
     */
    void addLogEntry(const QString& level, const QString& message) const;

    // ==================== 缓存管理方法 ====================

    /**
     * @brief 使缓存失效
     */
    void invalidateCache();

    /**
     * @brief 更新缓存
     */
    void updateCache();

    /**
     * @brief 检查缓存是否有效
     * @return 有效返回true，无效返回false
     */
    bool isCacheValid() const;

    /**
     * @brief 清空缓存
     */
    void clearCache();

    /**
     * @brief 更新统计缓存
     */
    void updateStatisticsCache() const;

    // ==================== 延迟加载方法 ====================

    /**
     * @brief 确保统计信息已加载
     */
    void ensureStatisticsLoaded() const;

    /**
     * @brief 确保组数据已加载
     */
    void ensureGroupsLoaded() const;

    /**
     * @brief 确保作动器数据已加载
     */
    void ensureActuatorsLoaded() const;

    // ==================== 线程安全方法 ====================

    /**
     * @brief 线程安全的数据访问
     * @param accessor 访问器函数
     * @return 访问结果
     */
    template<typename T>
    T safeDataAccess(std::function<T()> accessor) const
    {
        if (config_.threadSafe) {
            QMutexLocker locker(&dataMutex_);
            return accessor();
        } else {
            return accessor();
        }
    }

    /**
     * @brief 线程安全的数据修改
     * @param modifier 修改器函数
     * @return 修改结果
     */
    template<typename T>
    T safeDataModify(std::function<T()> modifier)
    {
        if (config_.threadSafe) {
            QMutexLocker locker(&dataMutex_);
            T result = modifier();
            // 修改操作后使缓存失效
            invalidateCache();
            return result;
        } else {
            T result = modifier();
            invalidateCache();
            return result;
        }
    }

    // ==================== 数据验证辅助方法 ====================

    /**
     * @brief 验证序列号格式
     * @param serialNumber 序列号
     * @return 有效返回true，无效返回false
     */
    bool isValidSerialNumber(const QString& serialNumber) const;

    /**
     * @brief 验证组ID
     * @param groupId 组ID
     * @return 有效返回true，无效返回false
     */
    bool isValidGroupId(int groupId) const;

    /**
     * @brief 验证作动器ID
     * @param actuatorId 作动器ID
     * @return 有效返回true，无效返回false
     */
    bool isValidActuatorId(int actuatorId) const;

    // ==================== 业务逻辑辅助方法 ====================

    /**
     * @brief 分配组内作动器ID
     * @param group 作动器组
     * @return 新的作动器ID
     */
    int assignActuatorIdInGroup(const UI::ActuatorGroup_1_2& group) const;

    /**
     * @brief 查找组中的最大作动器ID
     * @param group 作动器组
     * @return 最大ID
     */
    int findMaxActuatorIdInGroup(const UI::ActuatorGroup_1_2& group) const;

    /**
     * @brief 从组名称解析组ID
     * @param groupName 组名称
     * @return 组ID，失败返回-1
     */
    int parseGroupIdFromName(const QString& groupName) const;

    // ==================== 扩展功能辅助方法 ====================

    /**
     * @brief 验证扩展字段类型
     * @param value 字段值
     * @param expectedType 期望类型
     * @return 类型匹配返回true，不匹配返回false
     */
    bool validateExtensionFieldType(const QVariant& value, const QString& expectedType) const;

    /**
     * @brief 检查版本兼容性
     * @param version 版本字符串
     * @return 兼容返回true，不兼容返回false
     */
    bool isVersionCompatible(const QString& version) const;

    /**
     * @brief 验证作动器JSON格式
     * @param actuatorObj 作动器JSON对象
     * @return 格式正确返回true，错误返回false
     */
    bool validateActuatorJSONFormat(const QJsonObject& actuatorObj) const;

    /**
     * @brief 作动器转换为兼容JSON格式
     * @param params 作动器参数
     * @return JSON对象
     */
    QJsonObject actuatorToCompatibleJSON(const UI::ActuatorParams_1_2& params) const;

    /**
     * @brief 从兼容JSON格式创建作动器
     * @param actuatorObj JSON对象
     * @return 作动器参数
     */
    UI::ActuatorParams_1_2 actuatorFromCompatibleJSON(const QJsonObject& actuatorObj) const;

    /**
     * @brief 作动器组转换为兼容JSON格式
     * @param group 作动器组
     * @return JSON对象
     */
    QJsonObject actuatorGroupToCompatibleJSON(const UI::ActuatorGroup_1_2& group) const;

    /**
     * @brief 从兼容JSON格式创建作动器组
     * @param groupObj JSON对象
     * @return 作动器组
     */
    UI::ActuatorGroup_1_2 actuatorGroupFromCompatibleJSON(const QJsonObject& groupObj) const;

    /**
     * @brief 生成组创建时间戳
     * @return 时间戳字符串
     */
    QString generateTimestamp() const;

    // ==================== 数据转换方法 ====================

    /**
     * @brief 将作动器参数转换为调试字符串
     * @param params 作动器参数
     * @return 调试字符串
     */
    QString actuatorParamsToDebugString(const UI::ActuatorParams_1_2& params) const;

    /**
     * @brief 将作动器组转换为调试字符串
     * @param group 作动器组
     * @return 调试字符串
     */
    QString actuatorGroupToDebugString(const UI::ActuatorGroup_1_2& group) const;

#ifdef QT_TESTLIB_LIB
public:
    // ==================== 测试专用接口 ====================

    /**
     * @brief 设置测试模式
     * @param enabled 是否启用测试模式
     */
    void setTestMode(bool enabled);

    /**
     * @brief 注入测试数据
     * @param testActuators 测试作动器列表
     */
    void injectTestData(const QList<UI::ActuatorParams_1_2>& testActuators);

    /**
     * @brief 清空测试数据
     */
    void clearTestData();

    /**
     * @brief 获取测试日志
     * @return 测试日志列表
     */
    QStringList getTestLog() const;

    /**
     * @brief 获取缓存状态
     * @return 缓存状态信息
     */
    QString getCacheStatus() const;

private:
    bool testMode_;
    QList<UI::ActuatorParams_1_2> testData_;
#endif
};

#endif // ACTUATOR_VIEW_MODEL_1_2_H
