@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🔧 组数据导入修复验证
echo ========================================
echo.

echo 📋 修复内容：
echo   ✅ 添加通用的组名称映射构建函数
echo   ✅ 修复作动器导入逻辑（支持组内多个设备）
echo   ✅ 修复传感器导入逻辑（支持组内多个设备）
echo   ✅ 硬件节点和控制通道无需修改（无组概念）
echo.

echo 🔍 修复原理：
echo   1. 第一遍扫描：建立组ID到组名称的完整映射
echo   2. 第二遍处理：使用映射填充空的组名称
echo   3. 只检查组ID，不再检查组名称是否为空
echo   4. 支持Excel保存格式：组内第一行有组名称，其他行组名称为空
echo.

echo 🚀 开始验证：
echo.

echo 步骤1: 编译应用程序
cd SiteResConfig
echo   正在编译...
qmake SiteResConfig_Simple.pro >nul 2>&1
make >nul 2>&1
if exist "debug\SiteResConfig.exe" (
    echo   ✅ 编译成功
) else (
    echo   ❌ 编译失败，请检查编译错误
    cd ..
    pause
    exit /b 1
)
cd ..

echo.
echo 步骤2: 启动应用程序并测试
echo   请按以下步骤操作：
echo.
echo   1. 启动应用程序：SiteResConfig\debug\SiteResConfig.exe
echo   2. 点击"打开工程"
echo   3. 选择桌面的"20250819171946_实验工程.xls"
echo   4. 观察控制台日志输出
echo   5. 检查硬件树中各组的显示
echo.

echo 🎯 预期的正确日志输出：
echo.
echo === 组名称映射构建阶段 ===
echo 🔍 构建组名称映射：起始行=2, 组ID列=1, 组名称列=2
echo 📝 映射记录：组ID=1 -> 组名称='50kN_作动器组'
echo 📝 映射记录：组ID=2 -> 组名称='自定义_作动器组'
echo ✅ 组名称映射构建完成：共2个映射关系
echo.
echo === 作动器数据处理阶段 ===
echo 📝 处理作动器：行4, 组ID=1, 组名称='50kN_作动器组', 序列号='作动器_0000013213'
echo ✅ 作动器已添加到组：组ID=1, 组名称='50kN_作动器组', 当前组内作动器数量=1
echo 📝 处理作动器：行5, 组ID=2, 组名称='自定义_作动器组', 序列号='作动器_000001'
echo ✅ 作动器已添加到组：组ID=2, 组名称='自定义_作动器组', 当前组内作动器数量=1
echo 🔄 使用映射组名称：组ID=2 -> 组名称='自定义_作动器组'
echo 📝 处理作动器：行6, 组ID=2, 组名称='自定义_作动器组', 序列号='作动器_000002'
echo ✅ 作动器已添加到组：组ID=2, 组名称='自定义_作动器组', 当前组内作动器数量=2
echo 📊 组2 (自定义_作动器组)：2个作动器
echo.
echo === 传感器数据处理阶段 ===
echo 🔍 构建组名称映射：起始行=2, 组ID列=1, 组名称列=2
echo 📝 映射记录：组ID=1 -> 组名称='载荷_传感器组'
echo 📝 映射记录：组ID=2 -> 组名称='自定义类型_传感器组'
echo ✅ 组名称映射构建完成：共2个映射关系
echo （如果传感器组也有多个设备，会看到类似的映射使用日志）
echo.
echo === 界面显示阶段 ===
echo 📊 处理作动器组：ID=2, 名称='自定义_作动器组', 作动器数量=2
echo   ✅ 添加作动器到界面：序列号='作动器_000001', 类型='单出杆'
echo   ✅ 添加作动器到界面：序列号='作动器_000002', 类型='单出杆'
echo 📈 组'自定义_作动器组'界面显示完成：2个作动器已添加到树控件
echo.

echo 🚨 异常情况分析：
echo.
echo 1. 如果看不到"🔄 使用映射组名称"日志：
echo    ➤ Excel文件中可能所有行都有组名称
echo    ➤ 这是正常情况，说明Excel格式不同
echo.
echo 2. 如果看到"警告：组ID X 没有对应的组名称"：
echo    ➤ Excel文件中存在孤立的组ID
echo    ➤ 检查Excel文件数据完整性
echo.
echo 3. 如果组内设备数量仍然不对：
echo    ➤ 检查Excel文件中的实际数据行数
echo    ➤ 确认组ID是否正确
echo.
echo 4. 如果编译失败：
echo    ➤ 检查新增的函数声明和实现
echo    ➤ 确认头文件包含正确
echo.

echo ========================================
echo 🎯 开始验证
echo ========================================
echo.
echo 现在请：
echo 1. 启动应用程序
echo 2. 打开工程文件
echo 3. 仔细观察控制台日志
echo 4. 检查界面显示结果
echo 5. 特别关注"自定义_作动器组"是否显示2个作动器
echo.
echo 如果修复成功，您应该看到：
echo   - 组名称映射构建日志
echo   - 映射使用日志（对于组名称为空的行）
echo   - 正确的组内设备数量
echo   - 完整的界面显示
echo.
pause
