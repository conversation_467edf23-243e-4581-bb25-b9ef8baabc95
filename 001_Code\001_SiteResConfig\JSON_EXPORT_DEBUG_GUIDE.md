# 🔍 JSON导出调试指南

## 📋 **问题描述**

从您提供的截图可以看到：
- ✅ 试验配置树结构正确：CH1和CH2下面都有载荷1、载荷2、位置、控制子节点
- ✅ 关联信息正确：每个子节点都显示了正确的关联传感器/作动器
- ❌ JSON导出问题：只导出了CH1和CH2节点，缺少了它们的子节点

## 🔍 **调试方案**

我已经在`CollectTreeItemsInSpecificFormat`方法中添加了调试输出：

```cpp
void CMyMainWindow::CollectTreeItemsInSpecificFormat(QTreeWidgetItem* item, QJsonArray& jsonArray) {
    if (!item) return;

    QString itemType = item->data(0, Qt::UserRole).toString();
    QString itemName = item->text(0);
    QString tooltip = item->toolTip(0);
    
    // 调试输出：记录每个处理的节点
    AddLogEntry("DEBUG", QString("处理节点: %1, 类型: %2, 子节点数: %3")
                         .arg(itemName).arg(itemType).arg(item->childCount()));
    
    // ... 其他处理逻辑
    
    // 递归处理子项目
    for (int i = 0; i < item->childCount(); ++i) {
        QTreeWidgetItem* child = item->child(i);
        if (child) {
            CollectTreeItemsInSpecificFormat(child, jsonArray);
        }
    }
}
```

## 🚀 **测试步骤**

### **1. 启动应用程序并查看调试信息**

```bash
cd build-SiteResConfig_Simple-Desktop_Qt_5_14_2_MinGW_32_bit-Debug/debug
./SiteResConfig.exe
```

### **2. 导出JSON并查看日志**

1. 在应用程序中点击"导出为JSON格式"
2. 查看应用程序的日志输出
3. 寻找以"处理节点:"开头的调试信息

### **3. 分析调试输出**

期望看到的调试输出应该类似：
```
DEBUG: 处理节点: 实验, 类型: 试验节点, 子节点数: 4
DEBUG: 处理节点: 指令, 类型: 试验节点, 子节点数: 0
DEBUG: 处理节点: DI, 类型: 试验节点, 子节点数: 0
DEBUG: 处理节点: DO, 类型: 试验节点, 子节点数: 0
DEBUG: 处理节点: 控制通道, 类型: 试验节点, 子节点数: 2
DEBUG: 处理节点: CH1, 类型: 试验节点, 子节点数: 4
DEBUG: 处理节点: 载荷1, 类型: 试验节点, 子节点数: 0
DEBUG: 处理节点: 载荷2, 类型: 试验节点, 子节点数: 0
DEBUG: 处理节点: 位置, 类型: 试验节点, 子节点数: 0
DEBUG: 处理节点: 控制, 类型: 试验节点, 子节点数: 0
DEBUG: 处理节点: CH2, 类型: 试验节点, 子节点数: 4
DEBUG: 处理节点: 载荷1, 类型: 试验节点, 子节点数: 0
DEBUG: 处理节点: 载荷2, 类型: 试验节点, 子节点数: 0
DEBUG: 处理节点: 位置, 类型: 试验节点, 子节点数: 0
DEBUG: 处理节点: 控制, 类型: 试验节点, 子节点数: 0
```

## 🔍 **可能的问题原因**

### **原因1：子节点类型设置错误**
如果调试输出显示载荷1、载荷2等节点的类型不是"试验节点"，那么它们可能不会被正确处理。

**解决方案**：检查`InitializeTestConfigTree`方法中子节点的类型设置：
```cpp
load1Item->setData(0, Qt::UserRole, "试验节点");
```

### **原因2：递归没有执行**
如果调试输出中没有显示CH1、CH2的子节点，说明递归没有正确执行。

**解决方案**：检查递归逻辑：
```cpp
// 递归处理子项目
for (int i = 0; i < item->childCount(); ++i) {
    QTreeWidgetItem* child = item->child(i);
    if (child) {
        CollectTreeItemsInSpecificFormat(child, jsonArray);
    }
}
```

### **原因3：条件判断问题**
如果子节点被处理了但没有添加到JSON数组中，可能是条件判断有问题。

**解决方案**：检查试验节点的处理逻辑：
```cpp
} else if (itemType == "试验节点") {
    // 试验节点处理逻辑
    QJsonObject nodeObj;
    nodeObj["# 实验工程配置文件"] = "试验节点";
    nodeObj["field2"] = itemName;
    
    // 从树形控件的第二列获取关联信息
    QString associatedInfo = "";
    if (item->columnCount() > 1) {
        associatedInfo = item->text(1); // 第二列是关联信息
    }
    
    nodeObj["field3"] = associatedInfo;
    nodeObj["field4"] = "";
    jsonArray.append(nodeObj);  // 确保这行被执行
}
```

## 📊 **预期的JSON输出格式**

修复后应该看到完整的JSON输出：

```json
{
    "# 实验工程配置文件": "试验节点",
    "field2": "CH1",
    "field3": "",
    "field4": ""
},
{
    "# 实验工程配置文件": "试验节点",
    "field2": "载荷1",
    "field3": "传感器_000001",
    "field4": ""
},
{
    "# 实验工程配置文件": "试验节点",
    "field2": "载荷2",
    "field3": "传感器_000002",
    "field4": ""
},
{
    "# 实验工程配置文件": "试验节点",
    "field2": "位置",
    "field3": "传感器_000003",
    "field4": ""
},
{
    "# 实验工程配置文件": "试验节点",
    "field2": "控制",
    "field3": "作动器_000001",
    "field4": ""
},
{
    "# 实验工程配置文件": "试验节点",
    "field2": "CH2",
    "field3": "",
    "field4": ""
},
{
    "# 实验工程配置文件": "试验节点",
    "field2": "载荷1",
    "field3": "传感器_000001",
    "field4": ""
},
{
    "# 实验工程配置文件": "试验节点",
    "field2": "载荷2",
    "field3": "传感器_000002",
    "field4": ""
},
{
    "# 实验工程配置文件": "试验节点",
    "field2": "位置",
    "field3": "传感器_000003",
    "field4": ""
},
{
    "# 实验工程配置文件": "试验节点",
    "field2": "控制",
    "field3": "作动器_000002",
    "field4": ""
}
```

## 🔧 **下一步行动**

1. **重新编译项目**（包含调试输出）
2. **启动应用程序**
3. **导出JSON并查看调试日志**
4. **根据调试输出确定具体问题**
5. **针对性修复问题**

通过调试输出，我们可以准确定位问题是在递归遍历、节点类型设置还是条件判断上，然后进行针对性的修复。
