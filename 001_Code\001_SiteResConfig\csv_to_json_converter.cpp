#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <map>
#include <sstream>

// 简单的JSON生成器
class SimpleJSON {
private:
    std::ostringstream json;
    int indent_level;
    bool first_item;
    
public:
    SimpleJSON() : indent_level(0), first_item(true) {}
    
    void startObject() {
        if (!first_item) json << ",\n";
        json << std::string(indent_level * 2, ' ') << "{\n";
        indent_level++;
        first_item = true;
    }
    
    void endObject() {
        json << "\n";
        indent_level--;
        json << std::string(indent_level * 2, ' ') << "}";
        first_item = false;
    }
    
    void startArray(const std::string& key) {
        if (!first_item) json << ",\n";
        json << std::string(indent_level * 2, ' ') << "\"" << key << "\": [\n";
        indent_level++;
        first_item = true;
    }
    
    void endArray() {
        json << "\n";
        indent_level--;
        json << std::string(indent_level * 2, ' ') << "]";
        first_item = false;
    }
    
    void addString(const std::string& key, const std::string& value) {
        if (!first_item) json << ",\n";
        json << std::string(indent_level * 2, ' ') << "\"" << key << "\": \"" << escapeString(value) << "\"";
        first_item = false;
    }
    
    void addNumber(const std::string& key, const std::string& value) {
        if (!first_item) json << ",\n";
        json << std::string(indent_level * 2, ' ') << "\"" << key << "\": " << value;
        first_item = false;
    }
    
    std::string toString() {
        return json.str();
    }
    
private:
    std::string escapeString(const std::string& str) {
        std::string escaped;
        for (char c : str) {
            switch (c) {
                case '"': escaped += "\\\""; break;
                case '\\': escaped += "\\\\"; break;
                case '\n': escaped += "\\n"; break;
                case '\r': escaped += "\\r"; break;
                case '\t': escaped += "\\t"; break;
                default: escaped += c; break;
            }
        }
        return escaped;
    }
};

// 设备信息结构
struct DeviceInfo {
    std::string deviceType;
    std::map<std::string, std::string> parameters;
};

// CSV解析器
class CSVToJSONConverter {
private:
    std::string csvFilePath;
    std::string jsonFilePath;
    
public:
    CSVToJSONConverter(const std::string& csvPath, const std::string& jsonPath) 
        : csvFilePath(csvPath), jsonFilePath(jsonPath) {}
    
    bool convert() {
        std::cout << "🔄 开始转换CSV到JSON..." << std::endl;
        std::cout << "📁 输入文件: " << csvFilePath << std::endl;
        std::cout << "📁 输出文件: " << jsonFilePath << std::endl;
        
        // 读取CSV文件
        std::ifstream csvFile(csvFilePath);
        if (!csvFile.is_open()) {
            std::cout << "❌ 无法打开CSV文件: " << csvFilePath << std::endl;
            return false;
        }
        
        std::vector<std::string> lines;
        std::string line;
        while (std::getline(csvFile, line)) {
            lines.push_back(line);
        }
        csvFile.close();
        
        std::cout << "✅ 成功读取CSV文件，共 " << lines.size() << " 行" << std::endl;
        
        // 解析CSV内容
        SimpleJSON json;
        json.startObject();
        
        // 解析项目基本信息
        std::map<std::string, std::string> projectInfo;
        std::string currentSection;
        std::vector<DeviceInfo> currentDevices;
        
        for (size_t i = 0; i < lines.size(); ++i) {
            std::string& currentLine = lines[i];
            
            // 移除行尾的回车换行
            while (!currentLine.empty() && (currentLine.back() == '\r' || currentLine.back() == '\n')) {
                currentLine.pop_back();
            }
            
            if (currentLine.empty()) continue;
            
            std::cout << "处理第 " << (i + 1) << " 行: " << currentLine.substr(0, 50) << "..." << std::endl;
            
            if (currentLine[0] == '#') {
                // 处理头部注释信息
                size_t commaPos = currentLine.find(',');
                if (commaPos != std::string::npos) {
                    std::string key = currentLine.substr(2, commaPos - 2); // 跳过"# "
                    std::string value = currentLine.substr(commaPos + 1);
                    
                    if (key == "工程名称") projectInfo["projectName"] = value;
                    else if (key == "创建日期") projectInfo["createdDate"] = value;
                    else if (key == "版本") projectInfo["version"] = value;
                    else if (key == "描述") projectInfo["description"] = value;
                }
            }
            else if (currentLine[0] == '[' && currentLine.back() == ']') {
                // 处理节标题
                if (!currentSection.empty() && !currentDevices.empty()) {
                    addSectionToJSON(json, currentSection, currentDevices);
                }
                
                currentSection = currentLine.substr(1, currentLine.length() - 2);
                currentDevices.clear();
                std::cout << "开始处理节: " << currentSection << std::endl;
            }
            else if (currentLine.find("类型,名称,参数1,参数2,参数3") != std::string::npos) {
                // 跳过表头
                continue;
            }
            else if (!currentLine.empty() && currentLine[0] != '#') {
                // 处理数据行
                std::vector<std::string> fields = splitCSVLine(currentLine);
                
                if (!fields.empty() && !fields[0].empty()) {
                    // 这是一个设备类型行
                    DeviceInfo device;
                    device.deviceType = fields[0];
                    currentDevices.push_back(device);
                }
                else if (fields.size() >= 3 && !fields[1].empty()) {
                    // 这是一个参数行
                    if (!currentDevices.empty()) {
                        std::string paramName = fields[1];
                        
                        // 清理参数名称
                        size_t pos = paramName.find("├─ ");
                        if (pos != std::string::npos) {
                            paramName = paramName.substr(pos + 6); // 6 = strlen("├─ ")
                        }
                        pos = paramName.find("└─ ");
                        if (pos != std::string::npos) {
                            paramName = paramName.substr(pos + 6);
                        }
                        
                        // 移除分隔线
                        if (paramName.find("─────────") != std::string::npos) {
                            continue;
                        }
                        
                        if (!paramName.empty() && fields.size() >= 3) {
                            std::string paramValue = fields[2];
                            std::string unit = (fields.size() > 3) ? fields[3] : "";
                            
                            if (!unit.empty()) {
                                paramValue += " " + unit;
                            }
                            
                            currentDevices.back().parameters[paramName] = paramValue;
                        }
                    }
                }
            }
        }
        
        // 添加最后一个节的数据
        if (!currentSection.empty() && !currentDevices.empty()) {
            addSectionToJSON(json, currentSection, currentDevices);
        }
        
        // 添加项目信息
        if (!projectInfo.empty()) {
            json.startObject();
            for (const auto& pair : projectInfo) {
                json.addString(pair.first, pair.second);
            }
            json.endObject();
        }
        
        json.endObject();
        
        // 写入JSON文件
        std::ofstream jsonFile(jsonFilePath);
        if (!jsonFile.is_open()) {
            std::cout << "❌ 无法创建JSON文件: " << jsonFilePath << std::endl;
            return false;
        }
        
        jsonFile << json.toString();
        jsonFile.close();
        
        std::cout << "✅ 成功转换CSV为JSON" << std::endl;
        std::cout << "📁 JSON文件已保存到: " << jsonFilePath << std::endl;
        
        return true;
    }
    
private:
    std::vector<std::string> splitCSVLine(const std::string& line) {
        std::vector<std::string> fields;
        std::string field;
        bool inQuotes = false;
        
        for (char c : line) {
            if (c == '"') {
                inQuotes = !inQuotes;
            } else if (c == ',' && !inQuotes) {
                fields.push_back(field);
                field.clear();
            } else {
                field += c;
            }
        }
        fields.push_back(field);
        
        return fields;
    }
    
    void addSectionToJSON(SimpleJSON& json, const std::string& sectionName, const std::vector<DeviceInfo>& devices) {
        json.startArray(sectionName);
        
        for (const auto& device : devices) {
            json.startObject();
            json.addString("deviceType", device.deviceType);
            
            if (!device.parameters.empty()) {
                json.startObject();
                for (const auto& param : device.parameters) {
                    json.addString(param.first, param.second);
                }
                json.endObject();
            }
            
            json.endObject();
        }
        
        json.endArray();
    }
};

int main(int argc, char* argv[]) {
    std::cout << "🔄 CSV到JSON转换工具" << std::endl;
    std::cout << "=" << std::string(50, '=') << std::endl;
    
    std::string csvPath, jsonPath;
    
    if (argc != 3) {
        // 使用默认路径
        csvPath = R"(C:\Users\<USER>\Desktop\20250812095644_实验工程.csv)";
        jsonPath = R"(C:\Users\<USER>\Desktop\20250812095644_实验工程.json)";
        
        std::cout << "使用默认路径:" << std::endl;
        std::cout << "📁 CSV文件: " << csvPath << std::endl;
        std::cout << "📁 JSON文件: " << jsonPath << std::endl;
    } else {
        csvPath = argv[1];
        jsonPath = argv[2];
    }
    
    CSVToJSONConverter converter(csvPath, jsonPath);
    bool success = converter.convert();
    
    if (success) {
        std::cout << "\n🎉 转换完成!" << std::endl;
    } else {
        std::cout << "\n❌ 转换失败!" << std::endl;
        return 1;
    }
    
    return 0;
}
