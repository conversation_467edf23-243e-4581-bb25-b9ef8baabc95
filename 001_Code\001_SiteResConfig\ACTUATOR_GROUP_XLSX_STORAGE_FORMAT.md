# 📊 作动器组XLSX存储格式设计方案

## 🎯 设计概述

基于作动器分组管理的需求，设计支持"作动器组"和"作动器"层级结构的XLSX存储格式。每个作动器组包含多个作动器信息。

## 📋 工作表结构设计

### 主工作表：作动器组配置表 (ActuatorGroup_Configuration)

#### 表头信息区域 (A1:P4)
```
A1: 作动器组配置数据表                  | B1-P1: [合并单元格]
A2: 导出时间: 2025-08-14 15:30:00      | B2-P2: [合并单元格]
A3: 说明: 包含作动器组及其作动器的完整配置信息 | B3-P3: [合并单元格]
A4: [空行]                            | B4-P4: [空行]
```

#### 数据表头 (A5:P5)
```
A5: 组序号
B5: 作动器组名称
C5: 作动器序号
D5: 作动器序列号
E5: 作动器类型
F5: Unit类型
G5: Unit名称
H5: 行程(m)
I5: 位移(m)
J5: 拉伸面积(m²)
K5: 压缩面积(m²)
L5: 极性
M5: Deliver(V)
N5: 频率(Hz)
O5: 输出倍数
P5: 平衡(V)
Q5: 备注
```

#### 数据行格式 (A6开始)
```
A6: 1
B6: 作动器组1
C6: 1
D6: ACT_G1_001
E6: 单出杆
F6: m
G6: 米
H6: 0.30
I6: 0.30
J6: 0.60
K6: 0.50
L6: Positive
M6: 0.100
N6: 100.00
O6: 1.000
P6: 0.000
Q6: 液压系统主作动器

A7: 1
B7: [空] (同组不重复显示)
C7: 2
D7: ACT_G1_002
E7: 单出杆
F7: m
G7: 米
H7: 0.25
I7: 0.25
J7: 0.45
K7: 0.40
L7: Positive
M7: 0.080
N7: 120.00
O7: 1.000
P7: 0.000
Q7: 液压系统副作动器

A8: 2
B8: 作动器组2
C8: 1
D8: ACT_G2_001
E8: 双出杆
F8: mm
G8: 毫米
H8: 0.20
I8: 0.20
J8: 0.30
K8: 0.30
L8: Positive
M8: 0.050
N8: 1000.00
O8: 1.000
P8: 0.000
Q8: 电动精密作动器
```

## 📊 详细字段规格

### 分组信息字段
| 列 | 字段名 | 数据类型 | 宽度 | 说明 | 示例值 |
|---|--------|----------|------|------|--------|
| A | 组序号 | Integer | 8 | 作动器组序号 | 1, 2, 3... |
| B | 作动器组名称 | String | 20 | 作动器组名称 | "作动器组1" |

### 作动器基本信息字段
| 列 | 字段名 | 数据类型 | 宽度 | 说明 | 示例值 |
|---|--------|----------|------|------|--------|
| C | 作动器序号 | Integer | 10 | 组内作动器序号 | 1, 2, 3... |
| D | 作动器序列号 | String | 15 | 作动器唯一标识 | "ACT_G1_001" |
| E | 作动器类型 | String | 12 | 单出杆/双出杆 | "单出杆" |
| Q | 备注 | String | 25 | 附加说明信息 | "液压系统主作动器" |

### Unit字段 (两列存储)
| 列 | 字段名 | 数据类型 | 宽度 | 说明 | 示例值 |
|---|--------|----------|------|------|--------|
| F | Unit类型 | String | 10 | 单位类型 | "m", "mm", "cm", "inch" |
| G | Unit名称 | String | 10 | 单位中文名称 | "米", "毫米", "厘米", "英寸" |

### 截面数据字段
| 列 | 字段名 | 数据类型 | 单位 | 精度 | 范围 | 示例值 |
|---|--------|----------|------|------|------|--------|
| H | 行程 | Double | m | 2位小数 | 0.01-10.00 | 0.30 |
| I | 位移 | Double | m | 2位小数 | 0.01-10.00 | 0.30 |
| J | 拉伸面积 | Double | m² | 2位小数 | 0.01-100.00 | 0.60 |
| K | 压缩面积 | Double | m² | 2位小数 | 0.01-100.00 | 0.50 |

### 伺服控制器参数字段
| 列 | 字段名 | 数据类型 | 单位 | 精度 | 范围 | 示例值 |
|---|--------|----------|------|------|------|--------|
| L | 极性 | String | - | - | Positive/Negative | "Positive" |
| M | Deliver | Double | V | 3位小数 | -10.000~10.000 | 0.100 |
| N | 频率 | Double | Hz | 2位小数 | 1.00-10000.00 | 100.00 |
| O | 输出倍数 | Double | - | 3位小数 | 0.001-1000.000 | 1.000 |
| P | 平衡 | Double | V | 3位小数 | -10.000~10.000 | 0.000 |

## 🎨 格式样式设计

### 表头样式 (A5:Q5)
- **背景色**: #4472C4 (深蓝色)
- **字体色**: 白色
- **字体**: 微软雅黑, 11pt, 粗体
- **对齐**: 居中对齐
- **边框**: 全边框, 白色, 1pt

### 作动器组行样式
- **组名称行背景**: #E7F3FF (浅蓝色)
- **字体**: 微软雅黑, 10pt, 粗体
- **边框**: 全边框, 蓝色, 1pt

### 作动器数据行样式
- **奇数组背景**: #F8F9FA (浅灰色)
- **偶数组背景**: 白色
- **字体**: 微软雅黑, 10pt, 常规
- **数值对齐**: 右对齐
- **文本对齐**: 左对齐
- **边框**: 全边框, 灰色, 0.5pt

### 列宽设置
```
A列(组序号): 8
B列(作动器组名称): 20
C列(作动器序号): 10
D列(作动器序列号): 15
E列(作动器类型): 12
F列(Unit类型): 10
G列(Unit名称): 10
H列(行程): 10
I列(位移): 10
J列(拉伸面积): 12
K列(压缩面积): 12
L列(极性): 10
M列(Deliver): 12
N列(频率): 10
O列(输出倍数): 12
P列(平衡): 10
Q列(备注): 25
```

## 📤 完整数据示例

### 作动器组结构示例
```
组序号 | 作动器组名称 | 作动器序号 | 作动器序列号 | 作动器类型 | Unit类型 | Unit名称 | 行程  | 位移  | 拉伸面积 | 压缩面积 | 极性     | Deliver | 频率    | 输出倍数 | 平衡  | 备注
-------|-------------|-----------|-------------|-----------|----------|----------|-------|-------|----------|----------|----------|---------|---------|----------|-------|------------------
1      | 液压作动器组 | 1         | ACT_HYD_001 | 单出杆    | m        | 米       | 0.30  | 0.30  | 0.60     | 0.50     | Positive | 0.100   | 100.00  | 1.000    | 0.000 | 主液压缸
1      |             | 2         | ACT_HYD_002 | 单出杆    | m        | 米       | 0.25  | 0.25  | 0.45     | 0.40     | Positive | 0.080   | 120.00  | 1.000    | 0.000 | 副液压缸
1      |             | 3         | ACT_HYD_003 | 双出杆    | m        | 米       | 0.20  | 0.20  | 0.40     | 0.40     | Positive | 0.090   | 110.00  | 1.000    | 0.000 | 双向液压缸
-------|-------------|-----------|-------------|-----------|----------|----------|-------|-------|----------|----------|----------|---------|---------|----------|-------|------------------
2      | 电动作动器组 | 1         | ACT_ELE_001 | 单出杆    | mm       | 毫米     | 0.20  | 0.20  | 0.30     | 0.25     | Positive | 0.050   | 1000.00 | 1.000    | 0.000 | 精密电动缸
2      |             | 2         | ACT_ELE_002 | 双出杆    | mm       | 毫米     | 0.15  | 0.15  | 0.25     | 0.25     | Positive | 0.040   | 1200.00 | 1.000    | 0.000 | 高速电动缸
-------|-------------|-----------|-------------|-----------|----------|----------|-------|-------|----------|----------|----------|---------|---------|----------|-------|------------------
3      | 气动作动器组 | 1         | ACT_PNE_001 | 单出杆    | cm       | 厘米     | 0.15  | 0.15  | 0.20     | 0.18     | Positive | 0.200   | 50.00   | 1.000    | 0.000 | 快速气缸
3      |             | 2         | ACT_PNE_002 | 单出杆    | cm       | 厘米     | 0.12  | 0.12  | 0.18     | 0.16     | Positive | 0.180   | 60.00   | 1.000    | 0.000 | 夹紧气缸
3      |             | 3         | ACT_PNE_003 | 双出杆    | cm       | 厘米     | 0.10  | 0.10  | 0.15     | 0.15     | Positive | 0.150   | 70.00   | 1.000    | 0.000 | 双向气缸
-------|-------------|-----------|-------------|-----------|----------|----------|-------|-------|----------|----------|----------|---------|---------|----------|-------|------------------
4      | 伺服作动器组 | 1         | ACT_SRV_001 | 单出杆    | inch     | 英寸     | 0.10  | 0.10  | 0.12     | 0.10     | Positive | 0.000   | 528.00  | 1.000    | 0.000 | 高精度伺服缸
```

## 🔄 数据验证规则

### 分组验证
- **组序号**: 必须连续，从1开始
- **作动器组名称**: 不能为空，同一组内只在第一行显示
- **作动器序号**: 组内必须连续，从1开始

### 作动器验证
- **作动器序列号**: 全局唯一，建议格式 "ACT_G{组号}_{序号}"
- **作动器类型**: 必须为"单出杆"或"双出杆"

### Unit字段验证
- **Unit类型和名称**: 必须匹配
- **允许组合**: (m,米), (mm,毫米), (cm,厘米), (inch,英寸)

### 逻辑关系验证
- 压缩面积 ≤ 拉伸面积 (单出杆)
- 压缩面积 = 拉伸面积 (双出杆)
- 位移 ≤ 行程

## 📋 辅助工作表设计

### 工作表2：作动器组汇总 (ActuatorGroup_Summary)

#### 汇总表结构
```
A1: 组序号 | B1: 作动器组名称 | C1: 作动器数量 | D1: 主要类型 | E1: 创建时间 | F1: 备注

A2: 1 | B2: 液压作动器组 | C2: 3 | D2: 液压系统 | E2: 2025-08-14 | F2: 主要液压执行机构
A3: 2 | B3: 电动作动器组 | C3: 2 | D3: 电动系统 | E3: 2025-08-14 | F3: 精密定位机构
A4: 3 | B4: 气动作动器组 | C4: 3 | D4: 气动系统 | E4: 2025-08-14 | F4: 快速动作机构
A5: 4 | B5: 伺服作动器组 | C5: 1 | D5: 伺服系统 | E5: 2025-08-14 | F5: 高精度控制机构
```

## 📁 文件命名规范

```
SiteResConfig_ActuatorGroups_YYYYMMDD_HHMMSS.xlsx
```

示例: `SiteResConfig_ActuatorGroups_20250814_153000.xlsx`

## 🎯 分组存储格式特点

### 层级结构
- ✅ **清晰的分组结构** - 作动器组 → 作动器
- ✅ **组内序号管理** - 每组内作动器独立编号
- ✅ **全局唯一标识** - 作动器序列号全局唯一

### 数据组织
- ✅ **分组显示优化** - 同组名称不重复显示
- ✅ **17列完整布局** - 包含分组和作动器信息
- ✅ **多工作表支持** - 主表+汇总表

### 管理优势
- ✅ **批量管理** - 按组批量操作作动器
- ✅ **分类清晰** - 不同类型作动器分组管理
- ✅ **扩展性强** - 支持任意数量的组和作动器
- ✅ **查询便利** - 支持按组查询和统计

### 实用性
- ✅ **标准XLSX格式** - Excel完全兼容
- ✅ **专业表格样式** - 分组视觉效果
- ✅ **完整数据验证** - 分组和作动器双重验证
- ✅ **汇总统计** - 提供组级汇总信息

这个分组存储格式完全支持"作动器组"和"作动器"的层级结构，提供了专业、完整、易管理的XLSX存储方案。
