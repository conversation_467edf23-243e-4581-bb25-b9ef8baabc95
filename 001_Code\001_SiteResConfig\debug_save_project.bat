@echo off
echo ========================================
echo  诊断保存工程问题
echo ========================================

REM 设置Qt环境变量
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 🔍 检查项目文件状态...
echo.

echo 📁 检查关键源文件:
if exist "src\MainWindow_Qt_Simple.cpp" (
    echo ✅ MainWindow_Qt_Simple.cpp 存在
) else (
    echo ❌ MainWindow_Qt_Simple.cpp 不存在
)

if exist "src\XLSDataExporter.cpp" (
    echo ✅ XLSDataExporter.cpp 存在
) else (
    echo ❌ XLSDataExporter.cpp 不存在
)

if exist "include\MainWindow_Qt_Simple.h" (
    echo ✅ MainWindow_Qt_Simple.h 存在
) else (
    echo ❌ MainWindow_Qt_Simple.h 不存在
)

if exist "include\XLSDataExporter.h" (
    echo ✅ XLSDataExporter.h 存在
) else (
    echo ❌ XLSDataExporter.h 不存在
)

echo.
echo 📋 检查项目配置文件:
if exist "SiteResConfig_Simple.pro" (
    echo ✅ SiteResConfig_Simple.pro 存在
) else (
    echo ❌ SiteResConfig_Simple.pro 不存在
)

echo.
echo 🔧 强制清理并重新编译...
if exist "Makefile*" del /f /q Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del /f /q *.o
if exist "moc_*.cpp" del /f /q moc_*.cpp
if exist "ui_*.h" del /f /q ui_*.h

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++ CONFIG+=debug
if errorlevel 1 (
    echo ❌ qmake失败！
    echo.
    echo 🔍 可能的问题:
    echo 1. 项目文件(.pro)有语法错误
    echo 2. 缺少必要的源文件
    echo 3. Qt环境配置问题
    echo.
    pause
    exit /b 1
)

echo.
echo 编译项目...
mingw32-make -j4 2>&1 | tee compile_log.txt
if errorlevel 1 (
    echo.
    echo ========================================
    echo  ❌ 编译失败！
    echo ========================================
    echo.
    echo 🔍 编译错误分析:
    echo.
    echo 📄 编译日志已保存到: compile_log.txt
    echo.
    echo 🔍 常见编译错误及解决方案:
    echo.
    echo 1. "use of undeclared identifier" 错误:
    echo    - 检查方法声明是否在头文件中
    echo    - 检查是否有方法被注释但仍在调用
    echo    - 检查include路径是否正确
    echo.
    echo 2. "out-of-line definition does not match" 错误:
    echo    - 检查头文件中的方法声明与实现是否匹配
    echo    - 检查方法参数类型是否一致
    echo    - 检查const修饰符是否匹配
    echo.
    echo 3. "undefined reference" 错误:
    echo    - 检查链接库是否正确
    echo    - 检查.pro文件中的LIBS配置
    echo    - 检查方法实现是否存在
    echo.
    echo 4. Qt相关错误:
    echo    - 检查moc文件是否正确生成
    echo    - 检查Q_OBJECT宏是否正确使用
    echo    - 检查信号槽连接是否正确
    echo.
    echo 📖 查看详细错误信息:
    echo type compile_log.txt
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  ✅ 编译成功！
    echo ========================================
    
    echo.
    echo 🎯 保存工程功能诊断:
    echo.
    echo 📋 保存工程调用链:
    echo 1. 菜单: 文件 ^> 保存工程
    echo 2. 方法: OnSaveProject()
    echo 3. 检查: currentProject_ 是否存在
    echo 4. 获取: 文件路径（已有或用户选择）
    echo 5. 调用: SaveProjectToXLS(fileName)
    echo 6. 检查: xlsDataExporter_ 是否初始化
    echo 7. 检查: hardwareTreeWidget 是否存在
    echo 8. 调用: xlsDataExporter_-^>exportCompleteProject()
    echo 9. 保存: Excel文件到指定路径
    echo.
    echo 🔍 可能的保存问题:
    echo.
    echo 1. 项目未初始化:
    echo    - 错误: "没有可保存的工程！"
    echo    - 解决: 先创建新项目或打开现有项目
    echo.
    echo 2. 文件路径问题:
    echo    - 错误: 路径不存在或无写入权限
    echo    - 解决: 选择有权限的目录，避免系统目录
    echo.
    echo 3. XLS导出器未初始化:
    echo    - 错误: "XLS导出器未初始化"
    echo    - 解决: 检查程序启动时的初始化代码
    echo.
    echo 4. 硬件树控件为空:
    echo    - 错误: "硬件树控件为空"
    echo    - 解决: 检查UI初始化是否正确
    echo.
    echo 5. Excel文件保存失败:
    echo    - 错误: "保存Excel文件失败"
    echo    - 解决: 检查文件是否被其他程序占用
    echo.
    echo 6. 数据导出异常:
    echo    - 错误: 导出过程中发生异常
    echo    - 解决: 检查数据完整性和格式
    echo.
    echo 🎮 测试步骤:
    echo.
    echo 1. 启动程序
    echo 2. 创建新项目或打开现有项目
    echo 3. 添加一些硬件设备（传感器/作动器）
    echo 4. 尝试保存工程
    echo 5. 观察错误信息和日志
    echo.
    echo 📝 日志查看:
    echo - 程序运行时会在日志窗口显示详细信息
    echo - 关注 "ERROR" 和 "INFO" 级别的日志
    echo - 保存失败时会显示具体错误原因
    echo.
    
    if exist "SiteResConfig.exe" (
        echo 🚀 启动程序进行测试...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 🚀 启动程序进行测试...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 🚀 启动程序进行测试...
        start release\SiteResConfig.exe
    )
)

echo.
echo 📖 故障排除指南:
echo.
echo 🔧 如果保存失败，请按以下步骤排查:
echo.
echo 1. 检查项目状态:
echo    - 确认已创建或打开项目
echo    - 检查项目名称是否正确显示
echo.
echo 2. 检查文件路径:
echo    - 避免保存到系统目录（如C:\Windows）
echo    - 确保目标目录存在且有写入权限
echo    - 文件名不要包含特殊字符
echo.
echo 3. 检查程序状态:
echo    - 重启程序再试
echo    - 检查是否有其他Excel文件打开
echo    - 确保有足够的磁盘空间
echo.
echo 4. 查看详细错误:
echo    - 注意程序日志窗口的错误信息
echo    - 记录具体的错误消息
echo    - 截图保存错误对话框
echo.
echo 5. 数据检查:
echo    - 确认硬件树中有设备
echo    - 检查传感器和作动器数据是否完整
echo    - 验证配置参数是否正确
echo.
echo ✅ 如果问题仍然存在，请提供:
echo - 具体的错误消息
echo - 保存的文件路径
echo - 项目中的设备数量
echo - 程序日志中的ERROR信息
echo.
pause
