@echo off
echo ========================================
echo  完整组管理系统演示
echo ========================================

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o
if exist "ui_*.h" del ui_*.h

echo.
echo 生成UI头文件...
uic ui\MainWindow.ui -o ui_MainWindow.h
if errorlevel 1 (
    echo UI文件生成失败！
    pause
    exit /b 1
) else (
    echo ✅ UI头文件生成成功: ui_MainWindow.h
)

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！完整组管理系统已实现
    echo ========================================
    
    if exist "SiteResConfig.exe" (
        echo 可执行文件: SiteResConfig.exe
        echo.
        echo ✅ 完整组管理系统功能总览！
        echo.
        echo 🌳 树形结构设计:
        echo 任务1
        echo ├─ 作动器 (类型: "作动器")
        echo │  ├─ 50kN_作动器 (类型: "作动器组", 无图标)
        echo │  ├─ 100kN_作动器 (类型: "作动器组", 无图标)
        echo │  └─ 自定义_作动器 (类型: "作动器组", 无图标)
        echo ├─ 传感器 (类型: "传感器")
        echo │  ├─ 力_传感器 (类型: "传感器组", 无图标)
        echo │  ├─ 位移_传感器 (类型: "传感器组", 无图标)
        echo │  └─ 自定义_传感器 (类型: "传感器组", 无图标)
        echo └─ 硬件节点资源 (类型: "硬件节点资源")
        echo    ├─ 控制器_节点 (类型: "硬件节点组", 无图标)
        echo    ├─ 采集卡_节点 (类型: "硬件节点组", 无图标)
        echo    └─ 自定义_节点 (类型: "硬件节点组", 无图标)
        echo.
        echo 🖱️ 右键菜单系统:
        echo.
        echo 📁 作动器右键菜单:
        echo ├─ 新建
        echo │  └─ 作动器组 (下拉框选择)
        echo │     ├─ 50kN_作动器
        echo │     ├─ 100kN_作动器
        echo │     ├─ 200kN_作动器
        echo │     ├─ 500kN_作动器
        echo │     └─ 自定义...
        echo └─ 快速创建作动器组
        echo    ├─ 50kN_作动器
        echo    ├─ 100kN_作动器
        echo    ├─ 200kN_作动器
        echo    └─ 500kN_作动器
        echo.
        echo 📁 传感器右键菜单:
        echo └─ 新建
        echo    └─ 传感器组 (下拉框选择)
        echo       ├─ 力_传感器
        echo       ├─ 位移_传感器
        echo       ├─ 压力_传感器
        echo       ├─ 温度_传感器
        echo       └─ 自定义...
        echo.
        echo 📁 硬件节点资源右键菜单:
        echo └─ 新建
        echo    └─ 硬件节点组 (下拉框选择)
        echo       ├─ 控制器_节点
        echo       ├─ 采集卡_节点
        echo       ├─ 通信_节点
        echo       ├─ 电源_节点
        echo       └─ 自定义...
        echo.
        echo 🎯 核心功能特色:
        echo.
        echo ✅ 双重创建方式:
        echo - 新建菜单: 下拉框选择，支持预定义和自定义
        echo - 快速创建: 直接选择常用容量规格
        echo.
        echo ✅ 标准化命名:
        echo - 作动器组: "容量_作动器" (如: 50kN_作动器)
        echo - 传感器组: "类型_传感器" (如: 力_传感器)
        echo - 硬件节点组: "功能_节点" (如: 控制器_节点)
        echo.
        echo ✅ 类型化管理:
        echo - 每个节点都有明确的类型标识
        echo - 基于类型的右键菜单显示
        echo - 支持类型扩展和功能增强
        echo.
        echo ✅ 简洁视觉:
        echo - 所有组节点无图标显示
        echo - 统一的视觉风格
        echo - 清晰的层次结构
        echo.
        echo 🔧 技术架构:
        echo - Qt::UserRole 存储节点类型
        echo - QInputDialog::getItem 下拉框选择
        echo - QInputDialog::getText 自定义输入
        echo - QMenu 层级右键菜单
        echo - Lambda 表达式信号连接
        echo.
        echo 📊 应用场景:
        echo.
        echo 🔩 作动器组应用:
        echo - 50kN_作动器: 材料拉伸试验
        echo - 100kN_作动器: 构件弯曲试验
        echo - 200kN_作动器: 梁柱连接试验
        echo - 500kN_作动器: 大型结构试验
        echo.
        echo 📡 传感器组应用:
        echo - 力_传感器: 力值测量监控
        echo - 位移_传感器: 变形测量监控
        echo - 压力_传感器: 压力状态监控
        echo - 温度_传感器: 温度环境监控
        echo.
        echo 💻 硬件节点组应用:
        echo - 控制器_节点: 主控制系统
        echo - 采集卡_节点: 数据采集系统
        echo - 通信_节点: 网络通信系统
        echo - 电源_节点: 电源管理系统
        echo.
        echo 启动程序体验完整组管理系统...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
        echo 启动程序...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
        echo 启动程序...
        start release\SiteResConfig.exe
    ) else (
        echo 警告: 找不到可执行文件！
    )
)

echo.
echo 📖 完整组管理系统使用指南:
echo.
echo 🎯 推荐操作流程:
echo.
echo 1️⃣ 作动器组管理:
echo   - 右键"作动器" → "新建" → "作动器组"
echo   - 选择"100kN_作动器"创建标准组
echo   - 或选择"自定义..."创建特殊组
echo   - 使用"快速创建"直接创建常用规格
echo.
echo 2️⃣ 传感器组管理:
echo   - 右键"传感器" → "新建" → "传感器组"
echo   - 选择"力_传感器"创建力测量组
echo   - 根据试验需求选择不同传感器类型
echo.
echo 3️⃣ 硬件节点组管理:
echo   - 右键"硬件节点资源" → "新建" → "硬件节点组"
echo   - 选择"控制器_节点"创建控制系统组
echo   - 按功能分类管理硬件设备
echo.
echo 🏆 系统优势总结:
echo - 操作简便: 下拉选择比手动输入更快更准确
echo - 标准统一: 预定义选项确保命名规范一致
echo - 灵活扩展: 自定义选项满足特殊需求
echo - 视觉清晰: 无图标设计保持界面简洁
echo - 类型明确: 完整的类型体系支持功能扩展
echo.
pause
