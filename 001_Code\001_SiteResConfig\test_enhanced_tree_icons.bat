@echo off
chcp 65001 > nul
echo ========================================
echo 增强版树形控件图标测试
echo ========================================
echo.

echo 🔧 正在编译增强版图标代码...
cd SiteResConfig
qmake SiteResConfig_Simple.pro
mingw32-make clean
mingw32-make

if %ERRORLEVEL% NEQ 0 (
    echo ❌ 编译失败！
    echo.
    echo 🔍 可能的问题：
    echo 1. 缺少QDir或QApplication头文件包含
    echo 2. 图标生成代码有误
    echo 3. 文件路径处理问题
    echo.
    pause
    exit /b 1
)

echo ✅ 编译成功！
echo.

echo 🚀 启动应用程序进行增强版图标测试...
echo.
echo 📋 **增强版C++图标生成方案**：
echo.
echo 🎯 **改进特点**：
echo - 生成11x11像素的PNG图标文件
echo - 保存到temp/目录下
echo - 在CSS中通过url()引用图标文件
echo - 像素级精确绘制，无抗锯齿
echo.
echo 🎨 **图标规格**：
echo.
echo 1️⃣ **加号图标 (plus.png)**
echo    - 尺寸：11x11像素
echo    - 背景：纯白色
echo    - 边框：1px黑色边框
echo    - 符号：黑色十字形加号
echo    - 水平线：从(3,5)到(7,5)
echo    - 垂直线：从(5,3)到(5,7)
echo.
echo 2️⃣ **减号图标 (minus.png)**
echo    - 尺寸：11x11像素
echo    - 背景：纯白色
echo    - 边框：1px黑色边框
echo    - 符号：黑色水平线
echo    - 水平线：从(3,5)到(7,5)
echo.
echo 3️⃣ **CSS集成**
echo    - 使用image: url(path)引用图标
echo    - 设置width: 11px, height: 11px
echo    - margin: 3px居中对齐
echo    - background: transparent透明背景
echo.
echo 4️⃣ **文件管理**
echo    - 图标保存到debug/temp/目录
echo    - 自动创建目录结构
echo    - PNG格式，支持透明度
echo    - 运行时动态生成
echo.

start "" "debug\SiteResConfig.exe"

echo 📊 应用程序已启动！
echo.
echo 🔍 **详细验证步骤**：
echo.
echo ☐ 1. **文件生成验证**
echo      - 检查debug/temp/目录是否创建
echo      - 确认plus.png文件是否存在
echo      - 确认minus.png文件是否存在
echo      - 验证图标文件大小（应该很小，几百字节）
echo.
echo ☐ 2. **日志信息验证**
echo      - 查看是否显示"样式表已从资源加载并应用到整个应用程序"
echo      - 查看是否显示"树形控件加号/减号图标已设置"
echo      - 确认日志中包含图标文件路径
echo      - 无错误或警告信息
echo.
echo ☐ 3. **图标显示验证**
echo      - 有子节点的折叠节点显示加号图标
echo      - 有子节点的展开节点显示减号图标
echo      - 图标清晰可见，边缘锐利
echo      - 图标大小适中，不会过大或过小
echo.
echo ☐ 4. **交互功能验证**
echo      - 点击加号图标可展开节点
echo      - 点击减号图标可折叠节点
echo      - 图标响应鼠标悬停（如果有悬停效果）
echo      - 展开/折叠动画流畅
echo.
echo ☐ 5. **视觉效果验证**
echo      - 图标与连接线对齐良好
echo      - 图标与文字间距合适
echo      - 整体视觉效果协调
echo      - 符合Windows原生风格
echo.
echo 💡 **故障排除**：
echo.
echo 🔧 **如果图标不显示**：
echo 1. 检查debug/temp/目录和图标文件
echo 2. 查看日志中的文件路径是否正确
echo 3. 确认CSS样式是否正确应用
echo 4. 检查图标文件是否可以正常打开
echo.
echo 🔧 **如果图标显示异常**：
echo 1. 检查图标文件内容是否正确
echo 2. 验证QPainter绘制逻辑
echo 3. 调整图标尺寸或边距
echo 4. 检查CSS中的尺寸设置
echo.
echo 🎉 **成功标志**：
echo - ✅ 编译无错误
echo - ✅ 图标文件成功生成
echo - ✅ 日志显示设置成功
echo - ✅ 树形控件显示清晰的加号/减号图标
echo - ✅ 点击图标可正常展开/折叠节点
echo - ✅ 整体视觉效果符合Windows风格
echo.
echo 🎉 **方案优势**：
echo - 完全自定义的图标外观
echo - 像素级精确控制
echo - 运行时动态生成
echo - 易于调整和修改
echo - 跨平台兼容性好
echo.
pause

echo.
echo 📁 检查生成的图标文件...
if exist "debug\temp\plus.png" (
    echo ✅ 加号图标文件存在: debug\temp\plus.png
    for %%A in (debug\temp\plus.png) do echo    文件大小: %%~zA 字节
) else (
    echo ❌ 加号图标文件不存在
)

if exist "debug\temp\minus.png" (
    echo ✅ 减号图标文件存在: debug\temp\minus.png
    for %%A in (debug\temp\minus.png) do echo    文件大小: %%~zA 字节
) else (
    echo ❌ 减号图标文件不存在
)
echo.
pause
