@echo off
echo ========================================
echo  启动 SiteResConfig 应用程序
echo ========================================

echo 当前目录: %CD%
echo.

REM 检查是否在正确的目录
if not exist "build-SiteResConfig_Simple-Desktop_Qt_5_14_2_MinGW_32_bit-Debug" (
    echo 错误: 找不到构建目录
    echo 请确保您在正确的项目目录中运行此脚本
    echo 当前目录应该包含: build-SiteResConfig_Simple-Desktop_Qt_5_14_2_MinGW_32_bit-Debug
    pause
    exit /b 1
)

echo 找到构建目录，检查可执行文件...

set APP_PATH=build-SiteResConfig_Simple-Desktop_Qt_5_14_2_MinGW_32_bit-Debug\debug\SiteResConfig.exe

if exist "%APP_PATH%" (
    echo ✅ 找到应用程序: %APP_PATH%
    echo.
    echo 🔍 调试说明:
    echo - 如果添加了调试代码，控制台会显示调试信息
    echo - 拖拽硬件节点时，观察控制台输出
    echo - 查找以 "=== dragMoveEvent START ===" 开头的消息
    echo.
    echo 启动应用程序...
    
    REM 启动应用程序并保持控制台打开以查看调试输出
    "%APP_PATH%"
    
    echo.
    echo 应用程序已关闭
    
) else (
    echo ❌ 找不到应用程序文件
    echo 预期位置: %APP_PATH%
    echo.
    echo 请检查:
    echo 1. 项目是否已成功编译
    echo 2. 构建配置是否正确 (Debug)
    echo 3. 可执行文件是否在预期位置
    echo.
    echo 当前构建目录内容:
    if exist "build-SiteResConfig_Simple-Desktop_Qt_5_14_2_MinGW_32_bit-Debug\debug" (
        dir "build-SiteResConfig_Simple-Desktop_Qt_5_14_2_MinGW_32_bit-Debug\debug" /b
    ) else (
        echo debug 目录不存在
    )
)

echo.
pause
