/**
 * @file EventManager.h
 * @brief 事件管理模块 - 简化版
 * @details 提供简化的事件管理接口
 * <AUTHOR> Assistant
 * @date 2025-01-27
 * @version 3.4.0
 */

#ifndef EVENTMANAGER_H
#define EVENTMANAGER_H

#include <QObject>
#include <QString>
#include <QVariant>
#include <QMap>
#include <functional>

/**
 * @brief 事件管理器类 - 简化版
 * @details 提供简化的事件管理接口
 */
class EventManager : public QObject {
    Q_OBJECT
    
public:
    explicit EventManager(QObject* parent = nullptr);
    ~EventManager();
    
    // 简单的事件发送
    void sendEvent(const QString& eventType, const QVariant& data = QVariant());
    
    // 事件监听
    void addEventListener(const QString& eventType, std::function<void(const QVariant&)> callback);
    void removeEventListener(const QString& eventType);
    
signals:
    void eventProcessed(const QString& eventType, const QVariant& data);
    
private:
    QMap<QString, std::function<void(const QVariant&)>> eventListeners_;
};

#endif // EVENTMANAGER_H 