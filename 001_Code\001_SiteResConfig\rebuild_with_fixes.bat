@echo off
echo ========================================
echo  重新编译修复版本
echo ========================================

echo 正在查找Qt工具...

REM 尝试常见的Qt安装路径
set QT_PATHS=C:\Qt\5.14.2\mingw73_32\bin;C:\Qt\5.14.2\mingw73_64\bin;C:\Qt\Tools\mingw730_32\bin;C:\Qt\Tools\mingw730_64\bin

for %%p in (%QT_PATHS%) do (
    if exist "%%p\qmake.exe" (
        echo 找到Qt工具: %%p
        set PATH=%%p;%PATH%
        goto :found_qt
    )
)

echo 未找到Qt工具，尝试使用系统PATH中的工具...

:found_qt

echo 检查编译工具...
where qmake >nul 2>&1
if errorlevel 1 (
    echo 错误: 找不到qmake
    echo 请确保Qt开发环境已正确安装
    pause
    exit /b 1
)

REM 查找make工具
set MAKE_CMD=
for %%m in (mingw32-make.exe make.exe nmake.exe) do (
    where %%m >nul 2>&1
    if not errorlevel 1 (
        set MAKE_CMD=%%m
        echo 使用编译工具: %%m
        goto :found_make
    )
)

echo 未找到make工具，尝试直接使用现有Makefile...

:found_make

echo.
echo 进入编译目录...
cd /d "%~dp0\build-SiteResConfig_Simple-Desktop_Qt_5_14_2_MinGW_32_bit-Debug"

if not exist "Makefile" (
    echo 错误: 找不到Makefile
    echo 请先运行qmake生成Makefile
    pause
    exit /b 1
)

echo.
echo 清理旧的目标文件...
if exist "debug\MainWindow_Qt_Simple.o" del "debug\MainWindow_Qt_Simple.o"

echo.
echo 开始编译...
if defined MAKE_CMD (
    %MAKE_CMD%
) else (
    echo 尝试使用nmake...
    nmake
    if errorlevel 1 (
        echo nmake失败，尝试使用make...
        make
        if errorlevel 1 (
            echo 所有编译工具都失败了
            pause
            exit /b 1
        )
    )
)

if errorlevel 1 (
    echo 编译失败！
    pause
    exit /b 1
)

echo.
echo ========================================
echo  编译成功！
echo ========================================

if exist "debug\SiteResConfig.exe" (
    echo 可执行文件已生成: debug\SiteResConfig.exe
    echo.
    echo 启动应用程序测试修复效果...
    start debug\SiteResConfig.exe
    echo.
    echo 测试说明：
    echo 1. 观察控制台输出是否有中文乱码
    echo 2. 创建新工程并保存为CSV格式
    echo 3. 检查CSV文件内容是否完整
    echo 4. 多次保存验证数据一致性
) else (
    echo 警告: 找不到可执行文件
)

pause
