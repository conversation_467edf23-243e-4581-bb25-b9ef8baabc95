#pragma once

/**
 * @file HardwareNodeStructs.h
 * @brief 硬件节点相关数据结构定义
 * @details 独立的硬件节点数据结构，避免循环依赖
 * <AUTHOR> Agent
 * @date 2025-08-16
 * @version 1.0.0
 */

#include <QString>
#include <QList>

namespace UI {

/**
 * @brief 通道信息结构体
 * @details 存储单个通道的配置信息
 */
struct ChannelInfo {
    int channelId;          // 通道ID (CH1, CH2)
    QString ipAddress;      // IP地址
    int port;              // 端口号
    bool enabled;          // 是否启用
    
    ChannelInfo() 
        : channelId(1), ipAddress("*************"), port(8080), enabled(true) {}
        
    ChannelInfo(int id, const QString& ip, int p) 
        : channelId(id), ipAddress(ip), port(p), enabled(true) {}
};

/**
 * @brief 节点配置参数结构体
 * @details 存储硬件节点的所有配置参数
 */
struct NodeConfigParams {
    QString nodeName;           // 节点名称 (如 LD-B1, LD-B2)
    int channelCount;          // 通道数量 (1-2)
    QList<ChannelInfo> channels; // 通道列表
    
    NodeConfigParams() 
        : nodeName("LD-B1"), channelCount(2) {
        // 默认添加2个通道
        channels.append(ChannelInfo(1, "*************", 8080));
        channels.append(ChannelInfo(2, "*************", 8081));
    }
};

} // namespace UI
