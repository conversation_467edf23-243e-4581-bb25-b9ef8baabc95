/**
 * @file XLSDataExporter_1_2.cpp
 * @brief XLS数据导出器实现
 * @details 实现Excel格式的数据导出和导入功能
 * <AUTHOR> Assistant
 * @date 2025-08-13
 * @version 1.0.0
 */

#include "XLSDataExporter_1_2.h"
#include "SensorDataManager_1_2.h"
#include "ActuatorDataManager_1_2.h"  // 🆕 新增：作动器数据管理器
#include "CtrlChanDataManager.h"  // 🆕 新增：控制通道数据管理器
#include "HardwareNodeResDataManager.h"  // 🆕 新增：硬件节点资源数据管理器
#include "SensorExcelExtensions_1_2.h"  // 🆕 新增：传感器Excel扩展功能
#include "DataModels_Fixed.h"  // 🆕 新增：包含所有UI数据结构定义
#include "HardwareNodeStructs.h"  // 🔄 修正：使用独立的硬件节点结构体定义
#include "QtXlsxWriter-master/src/xlsx/xlsxdocument.h"
#include "ActuatorDialog_1_2.h"  // 🆕 新增：包含作动器相关结构体
#include "QtXlsxWriter-master/src/xlsx/xlsxworksheet.h"
#include "QtXlsxWriter-master/src/xlsx/xlsxformat.h"
#include "QtXlsxWriter-master/src/xlsx/xlsxcellrange.h"
#include <cmath>  // 🆕 新增：数学函数库，包含sqrt函数
#include <QTreeWidget>
#include <QTreeWidgetItem>
#include <QFile>
#include <QFileInfo>
#include <QDir>
#include <QDateTime>
#include <QDebug>
#include <QRegExp>
#include <QRegularExpression>  // 🆕 新增：正则表达式支持
#include <QColor>
#include <QVariant>

XLSDataExporter_1_2::XLSDataExporter_1_2(SensorDataManager_1_2* sensorManager, ActuatorDataManager_1_2* actuatorManager, CtrlChanDataManager* ctrlChanManager)
    : sensorDataManager_(sensorManager)
    , actuatorDataManager_(actuatorManager)  // 🆕 新增：初始化作动器数据管理器
    , ctrlChanDataManager_(ctrlChanManager)  // 🆕 新增：初始化控制通道数据管理器
    , hardwareNodeResDataManager_(nullptr)  // 🆕 新增：初始化硬件节点资源数据管理器
    , worksheetName_(u8"硬件配置")
    , includeHeader_(true)
    , autoFitColumns_(true)
    , useTableStyle_(true)
{
}

bool XLSDataExporter_1_2::exportCompleteProject(const QString& filePath) {
    clearError();

    try {
        auto document = createDocument();
        if (!document) {
            setError(QString(u8"无法创建Excel文档"));
            return false;
        }

//        // ============================================================================
//        // 1. 创建硬件配置工作表
//        // ============================================================================
//        document->addSheet(worksheetName_);
//        document->selectSheet(worksheetName_);
//        auto hardwareWorksheet = dynamic_cast<QXlsx::Worksheet*>(document->currentSheet());

//        if (!hardwareWorksheet) {
//            setError(QString(u8"无法创建硬件配置工作表"));
//            return false;
//        }

//        int currentRow = 1;

//        // 写入项目信息
//        if (includeHeader_) {
//            document->write(currentRow, 1, QString(u8"# 实验工程配置文件"));
//            document->write(currentRow, 2, QString(u8"完整项目配置"));
//            currentRow++;

//            document->write(currentRow, 1, QString(u8"# 导出时间"));
//            document->write(currentRow, 2, QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"));
//            currentRow++;

//            document->write(currentRow, 1, QString(u8"# 格式"));
//            document->write(currentRow, 2, QString(u8"Excel"));
//            currentRow += 2;

//            // 设置表头
//            QStringList headers;
//            headers << u8"类型" << u8"名称" << u8"参数1" << u8"参数2" << u8"参数3";
//            setupHeaderStyle(hardwareWorksheet, currentRow, 1, headers);
//            currentRow++;
//        }

//        // 导出硬件树结构
//        QTreeWidgetItem* rootItem = treeWidget->invisibleRootItem();
//        if (rootItem) {
//            for (int i = 0; i < rootItem->childCount(); ++i) {
//                currentRow = exportTreeItemToExcel(hardwareWorksheet, rootItem->child(i), currentRow, 0);
//            }
//        }

//        // 自动调整列宽
//        if (autoFitColumns_) {
//            autoFitColumnWidths(hardwareWorksheet, 5);
//        }

        // ============================================================================
        // 2. 创建作动器详细配置工作表
        // ============================================================================
        if (actuatorDataManager_) {
            QList<UI::ActuatorGroup_1_2> actuatorGroups = actuatorDataManager_->getAllActuatorGroups();
            qDebug() << QString(u8"🔍 ActuatorDataManager获取到的作动器组数量: %1").arg(actuatorGroups.size());

            if (!actuatorGroups.isEmpty()) {
                QString actuatorSheetName = u8"作动器详细配置";
                document->addSheet(actuatorSheetName);
                document->selectSheet(actuatorSheetName);
                auto actuatorWorksheet = dynamic_cast<QXlsx::Worksheet*>(document->currentSheet());

                if (actuatorWorksheet) {
                    int actuatorRow = 1;

                    // 写入作动器工作表头信息
                    if (includeHeader_) {
                        document->write(actuatorRow, 1, QString(u8"# 作动器详细配置文件"));
                        document->write(actuatorRow, 2, QString(u8"导出时间: %1").arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss")));
                        actuatorRow += 2;

                        // 设置作动器详细信息表头 - 使用最新需求格式
                        QStringList actuatorHeaders;
                        actuatorHeaders << u8"组序号" << u8"作动器组名称" << u8"控制量名称" << u8"作动器类型" << u8"零偏"
                                       << u8"下位机ID" << u8"站点ID" << u8"AO板卡ID" << u8"AO板卡类型" << u8"AO端口ID"
                                       << u8"DO板卡ID" << u8"DO板卡类型" << u8"DO端口ID" << u8"型号" << u8"序列号"
                                       << u8"K系数" << u8"B系数" << u8"精度" << u8"极性" << u8"测量单位"
                                       << u8"测量下限" << u8"测量上限" << u8"输出信号单位" << u8"输出信号下限" << u8"输出信号上限";
                        setupHeaderStyle(actuatorWorksheet, actuatorRow, 1, actuatorHeaders);
                        actuatorRow++;
                    }

                    // 🔄 修复：使用连续的显示序号，确保组序号从1开始连续递增（与传感器保持一致）
                    for (int groupIndex = 0; groupIndex < actuatorGroups.size(); ++groupIndex) {
                        const UI::ActuatorGroup_1_2& group = actuatorGroups[groupIndex];
                        int displayGroupId = groupIndex + 1; // 显示序号从1开始连续递增
                        actuatorRow = addActuatorGroupDetailToExcel(actuatorWorksheet, group, actuatorRow, displayGroupId);
                    }

                    // 🆕 更新：自动调整列宽（新需求格式，25列）
                    if (autoFitColumns_) {
                        autoFitColumnWidths(actuatorWorksheet, 25); // 作动器新需求格式有25列
                    }
                }
            }
        }

        // ============================================================================
        // 3. 创建传感器详细配置工作表（按组导出）
        // ============================================================================
        if (sensorDataManager_) {
            QList<UI::SensorGroup_1_2> sensorGroups = sensorDataManager_->getAllSensorGroups();

            // 🔍 调试信息：输出传感器组详细信息
            qDebug() << QString("=== 传感器组导出调试信息 ===");
            qDebug() << QString("传感器组总数: %1").arg(sensorGroups.size());
            for (int i = 0; i < sensorGroups.size(); ++i) {
                const UI::SensorGroup_1_2& group = sensorGroups[i];
                qDebug() << QString("组%1: 原始ID=%2, 组名称='%3', 传感器数量=%4")
                            .arg(i+1).arg(group.groupId).arg(group.groupName).arg(group.sensors.size());
                for (int j = 0; j < group.sensors.size(); ++j) {
                    const UI::SensorParams_1_2& sensor = group.sensors[j];
                    qDebug() << QString("  传感器%1: 序列号='%2', 类型='%3'")
                                .arg(j+1).arg(sensor.params_sn).arg(sensor.params_model);
                }
            }

            if (!sensorGroups.isEmpty()) {
                // 🆕 使用新的SensorExcelExtensions_1_2的createSensorDetailWorksheet方法
                if (!SensorExcelExtensions_1_2::createSensorDetailWorksheet(document.get(), sensorGroups)) {
                    QString error = SensorExcelExtensions_1_2::getLastError();
                    setError(QString(u8"创建传感器详细配置工作表失败: %1").arg(error));
                    qDebug() << "🚫 传感器工作表创建失败:" << error;
                    // 不返回false，继续创建其他工作表
                } else {
                    qDebug() << "✅ 传感器详细配置工作表创建成功！";
                }
            }
        }

        // ============================================================================
        // 4. 🆕 新增：创建硬件节点详细配置工作表
        // ============================================================================
        // 🔄 修正：使用内存中的硬件节点配置数据，不从界面获取
        if (!hardwareNodeConfigs_.isEmpty()) {
            if (!createHardwareNodeWorksheet(document.get(), hardwareNodeConfigs_)) {
                setError(QString(u8"创建硬件节点工作表失败"));
                return false;
            }
        }

        // ============================================================================
        // 5. 🆕 新增：创建控制通道详细配置工作表
        // ============================================================================
        if (ctrlChanDataManager_) {
            QList<UI::ControlChannelGroup> channelGroups = ctrlChanDataManager_->getAllControlChannelGroups();
            qDebug() << QString(u8"🔍 CtrlChanDataManager获取到的控制通道组数量: %1").arg(channelGroups.size());
            
            // 🆕 增强调试：输出控制通道组详细信息
            for (int i = 0; i < channelGroups.size(); ++i) {
                const UI::ControlChannelGroup& group = channelGroups[i];
                qDebug() << QString(u8"  控制通道组%1: ID=%2, 名称='%3', 通道数量=%4")
                            .arg(i+1).arg(group.groupId).arg(QString::fromStdString(group.groupName)).arg(group.channels.size());
                for (int j = 0; j < group.channels.size(); ++j) {
                    const UI::ControlChannelParams& channel = group.channels[j];
                    qDebug() << QString(u8"    通道%1: ID='%2', 名称='%3', 硬件关联='%4'")
                                .arg(j+1).arg(QString::fromStdString(channel.channelId))
                                .arg(QString::fromStdString(channel.channelName))
                                .arg(QString::fromStdString(channel.hardwareAssociation));
                }
            }

            if (!channelGroups.isEmpty()) {
                QString channelSheetName = u8"控制通道详细配置";
                document->addSheet(channelSheetName);
                document->selectSheet(channelSheetName);
                auto channelWorksheet = dynamic_cast<QXlsx::Worksheet*>(document->currentSheet());

                if (channelWorksheet) {
                    int channelRow = 1;

                    // 写入控制通道工作表头信息
                    if (includeHeader_) {
                        document->write(channelRow, 1, QString(u8"# 控制通道详细配置文件"));
                        document->write(channelRow, 2, QString(u8"导出时间: %1").arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss")));
                        channelRow += 2;

                        document->write(channelRow, 1, QString(u8"说明: 包含控制通道的关联信息与配置参数"));
                        channelRow += 2;

                        // 🆕 扩展：设置控制通道详细信息表头（包含扩展字段）
                        QStringList channelHeaders;
                        channelHeaders << u8"通道序号" << u8"通道ID" << u8"通道名称" 
                                      << u8"下位机ID" << u8"站点ID" << u8"使能状态" << u8"控制模式"
                                      << u8"硬件关联" << u8"载荷1传感器" << u8"载荷2传感器" 
                                      << u8"位置传感器" << u8"控制作动器"
                                      << u8"控制作动器极性" << u8"载荷1传感器极性" 
                                      << u8"载荷2传感器极性" << u8"位置传感器极性"
                                      << u8"备注";
                        setupHeaderStyle(channelWorksheet, channelRow, 1, channelHeaders);
                        channelRow++;
                    }

                    // 导出所有控制通道的详细信息（简化格式，不显示组信息）
                    int channelIndex = 1; // 通道序号从1开始
                    for (const UI::ControlChannelGroup& group : channelGroups) {
                        for (const UI::ControlChannelParams& channel : group.channels) {
                            channelRow = addControlChannelDetailToExcel(channelWorksheet, channel, channelRow, channelIndex);
                            channelIndex++;
                        }
                    }

                    // 自动调整列宽（8列）
                    if (autoFitColumns_) {
                        autoFitColumnWidths(channelWorksheet, 8);
                    }
                }
            }
        }

        // ============================================================================
        // 6. 保存文件
        // ============================================================================
        // 切换回硬件配置工作表作为默认显示
        document->selectSheet(worksheetName_);

        if (!document->saveAs(filePath)) {
            setError(QString(u8"保存Excel文件失败"));
            return false;
        }

        return true;
        
    } catch (const std::exception& e) {
        setError(QString(u8"导出过程中发生异常: %1").arg(e.what()));
        return false;
    } catch (...) {
        setError(QString(u8"导出过程中发生未知异常"));
        return false;
    }
}

bool XLSDataExporter_1_2::importToHardwareTree(const QString& filePath, QTreeWidget* treeWidget) {
    clearError();
    
    if (!treeWidget) {
        setError(QString(u8"硬件树控件为空"));
        return false;
    }
    
    if (!validateExcelFile(filePath)) {
        setError(QString(u8"无效的Excel文件"));
        return false;
    }
    
    try {
        QXlsx::Document document(filePath);
        // QtXlsx会自动加载文件，不需要调用load()方法
        
        // 读取硬件配置数据
        QList<QStringList> hardwareData = readHardwareDataFromExcel(filePath);
        if (hardwareData.isEmpty()) {
            setError(QString(u8"Excel文件中没有找到有效的硬件配置数据"));
            return false;
        }
        
        // 清空现有的硬件树
        treeWidget->clear();
        
        // 根据读取的数据重建硬件树
        // 这里需要根据您的具体数据格式来实现
        // 暂时提供基础框架
        
        return true;
        
    } catch (const std::exception& e) {
        setError(QString(u8"导入过程中发生异常: %1").arg(e.what()));
        return false;
    } catch (...) {
        setError(QString(u8"导入过程中发生未知异常"));
        return false;
    }
}

QString XLSDataExporter_1_2::getSupportedExtension() const {
    return "xlsx";
}

QString XLSDataExporter_1_2::getFormatDescription() const {
    return "Microsoft Excel (*.xlsx)";
}

QString XLSDataExporter_1_2::getLastError() const {
    return lastError_;
}

// 设置工作表名称
void XLSDataExporter_1_2::setWorksheetName(const QString& sheetName) {
    worksheetName_ = sheetName;
}

// 设置是否包含表头
void XLSDataExporter_1_2::setIncludeHeader(bool includeHeader) {
    includeHeader_ = includeHeader;
}

// 设置是否自动调整列宽
void XLSDataExporter_1_2::setAutoFitColumns(bool autoFitColumns) {
    autoFitColumns_ = autoFitColumns;
}

// 设置是否使用表格样式
void XLSDataExporter_1_2::setUseTableStyle(bool useTableStyle) {
    useTableStyle_ = useTableStyle;
}

// 设置作动器数据管理器
void XLSDataExporter_1_2::setActuatorDataManager(ActuatorDataManager_1_2* actuatorManager) {
    actuatorDataManager_ = actuatorManager;
}

// 设置控制通道数据管理器
void XLSDataExporter_1_2::setCtrlChanDataManager(CtrlChanDataManager* ctrlChanManager) {
    ctrlChanDataManager_ = ctrlChanManager;
}

// 🆕 新增：设置硬件节点资源数据管理器
void XLSDataExporter_1_2::setHardwareNodeResDataManager(HardwareNodeResDataManager* hardwareNodeManager) {
    hardwareNodeResDataManager_ = hardwareNodeManager;
}

// 清除错误信息
void XLSDataExporter_1_2::clearError() {
    lastError_.clear();
}

// 设置错误信息
void XLSDataExporter_1_2::setError(const QString& error) {
    lastError_ = error;
    qDebug() << u8"XLSDataExporter错误:" << error;
}

std::unique_ptr<QXlsx::Document> XLSDataExporter_1_2::createDocument() {
    return std::make_unique<QXlsx::Document>();
}

void XLSDataExporter_1_2::setupHeaderStyle(QXlsx::Worksheet* worksheet, int row, int startCol, const QStringList& headers) {
    if (!worksheet) return;

    // 创建表头格式
    QXlsx::Format headerFormat;
    headerFormat.setFontBold(true);
    headerFormat.setFontSize(12);
    headerFormat.setFontColor(QColor(Qt::white));
    headerFormat.setPatternBackgroundColor(QColor(68, 114, 196)); // 蓝色背景
    headerFormat.setHorizontalAlignment(QXlsx::Format::AlignHCenter);
    headerFormat.setVerticalAlignment(QXlsx::Format::AlignVCenter);
    headerFormat.setBorderStyle(QXlsx::Format::BorderThin);

    // 写入表头
    for (int i = 0; i < headers.size(); ++i) {
        worksheet->write(row, startCol + i, headers[i], headerFormat);
    }
}

int XLSDataExporter_1_2::exportTreeItemToExcel(QXlsx::Worksheet* worksheet, QTreeWidgetItem* item, int row, int level) {
    if (!worksheet || !item) return row;

    QString itemType = getItemType(item);
    QString itemName = item->text(0);
    QString tooltip = item->toolTip(0);

    // 创建数据行格式
    QXlsx::Format dataFormat;
    dataFormat.setBorderStyle(QXlsx::Format::BorderThin);

    // 根据层级设置缩进
    QString indentedName = QString("  ").repeated(level) + itemName;

    // 写入基本信息
    worksheet->write(row, 1, itemType, dataFormat);
    worksheet->write(row, 2, indentedName, dataFormat);

    // 解析tooltip信息并填入参数列
    if (!tooltip.isEmpty()) {
        QStringList params = parseTooltipToParams(tooltip);
        for (int i = 0; i < qMin(params.size(), 3); ++i) {
            worksheet->write(row, 3 + i, params[i], dataFormat);
        }
    }

    row++;

    // 递归处理子节点
    for (int i = 0; i < item->childCount(); ++i) {
        row = exportTreeItemToExcel(worksheet, item->child(i), row, level + 1);
    }

    return row;
}

int XLSDataExporter_1_2::addSensorDetailToExcel(QXlsx::Worksheet* worksheet, const UI::SensorParams_1_2& params, int row) {
    if (!worksheet) return row;

    // 创建数据格式
    QXlsx::Format dataFormat;
    dataFormat.setBorderStyle(QXlsx::Format::BorderThin);

    // 🔄 修改：写入传感器简化详细信息（14字段新需求版本）
    worksheet->write(row, 1, params.sensorId, dataFormat);                    // 传感器ID
    worksheet->write(row, 2, params.params_sn, dataFormat);                   // 序列号（使用新字段）
    worksheet->write(row, 3, params.params_model, dataFormat);                // 传感器类型（使用型号字段）
    // 注意：14字段新需求不包含edsId、model等旧字段，已删除
    worksheet->write(row, 4, params.zero_offset, dataFormat);                 // 零点偏移
    worksheet->write(row, 5, params.enable ? u8"是" : u8"否", dataFormat);   // 使能状态
    worksheet->write(row, 6, params.params_k, dataFormat);                    // 系数K
    worksheet->write(row, 7, params.params_b, dataFormat);                    // 系数B
    worksheet->write(row, 8, params.params_precision, dataFormat);            // 精度
    worksheet->write(row, 9, params.params_polarity, dataFormat);             // 极性（存储数字值）
    worksheet->write(row, 10, params.meas_unit, dataFormat);                  // 测量单位
    worksheet->write(row, 11, params.meas_range_min, dataFormat);             // 测量范围最小值
    worksheet->write(row, 12, params.meas_range_max, dataFormat);             // 测量范围最大值
    worksheet->write(row, 13, params.output_signal_unit, dataFormat);         // 输出信号单位
    worksheet->write(row, 14, params.output_signal_range_min, dataFormat);    // 输出信号范围最小值
    worksheet->write(row, 15, params.output_signal_range_max, dataFormat);    // 输出信号范围最大值


    return row + 1;
}

int XLSDataExporter_1_2::addActuatorGroupDetailToExcel(QXlsx::Worksheet* worksheet, const UI::ActuatorGroup_1_2& group, int row, int displayGroupId) {
    if (!worksheet) return row;

    // 如果没有提供显示组ID，使用原来的组ID
    int actualGroupId = (displayGroupId == -1) ? group.groupId : displayGroupId;

    // 创建数据格式
    QXlsx::Format dataFormat;
    dataFormat.setBorderStyle(QXlsx::Format::BorderThin);

    // 创建组名称格式（浅蓝色背景，粗体）
    QXlsx::Format groupFormat;
    groupFormat.setBorderStyle(QXlsx::Format::BorderThin);
    groupFormat.setPatternBackgroundColor(QColor(231, 243, 255));
    groupFormat.setFontBold(true);

    // 导出组内每个作动器的详细信息
    for (int i = 0; i < group.actuators.size(); ++i) {
        const UI::ActuatorParams_1_2& actuator = group.actuators[i];

        // 选择格式（第一个作动器使用组格式）
        QXlsx::Format currentFormat = (i == 0) ? groupFormat : dataFormat;

        // 🆕 更新：写入作动器详细信息（新需求格式，25列）
        int col = 1;
        worksheet->write(row, col++, actualGroupId, currentFormat);                    // 组序号
        worksheet->write(row, col++, (i == 0) ? group.groupName : QString(), currentFormat); // 作动器组名称
        worksheet->write(row, col++, actuator.name, currentFormat);                    // 控制量名称
        worksheet->write(row, col++, ActuatorDataManager_1_2::actuatorTypeToString(actuator.type), currentFormat); // 作动器类型

        // 🔍 调试信息：输出导出的关键字段
        if (row <= 8) { // 只输出前几行避免日志过多
            qDebug() << QString(u8"🔍 Excel导出第%1行 - 控制量名称: '%2', 序列号: '%3', 型号: '%4'")
                        .arg(row).arg(actuator.name).arg(actuator.params.sn).arg(actuator.params.model);
        }
        worksheet->write(row, col++, actuator.zero_offset, currentFormat);             // 零偏
        worksheet->write(row, col++, actuator.lc_id, currentFormat);                   // 下位机ID
        worksheet->write(row, col++, actuator.station_id, currentFormat);             // 站点ID
        worksheet->write(row, col++, actuator.board_id_ao, currentFormat);            // AO板卡ID
        worksheet->write(row, col++, actuator.board_type_ao, currentFormat);          // AO板卡类型
        worksheet->write(row, col++, actuator.port_id_ao, currentFormat);             // AO端口ID
        worksheet->write(row, col++, actuator.board_id_do, currentFormat);            // DO板卡ID
        worksheet->write(row, col++, actuator.board_type_do, currentFormat);          // DO板卡类型
        worksheet->write(row, col++, actuator.port_id_do, currentFormat);             // DO端口ID
        worksheet->write(row, col++, actuator.params.model, currentFormat);           // 型号
        worksheet->write(row, col++, actuator.params.sn, currentFormat);              // 序列号
        worksheet->write(row, col++, actuator.params.k, currentFormat);               // K系数
        worksheet->write(row, col++, actuator.params.b, currentFormat);               // B系数
        worksheet->write(row, col++, actuator.params.precision, currentFormat);       // 精度
        worksheet->write(row, col++, static_cast<int>(actuator.params.polarity), currentFormat); // 极性
        worksheet->write(row, col++, ActuatorDataManager_1_2::measurementUnitToString(actuator.params.meas_unit), currentFormat); // 测量单位
        worksheet->write(row, col++, actuator.params.meas_range_min, currentFormat);  // 测量下限
        worksheet->write(row, col++, actuator.params.meas_range_max, currentFormat);  // 测量上限
        worksheet->write(row, col++, actuator.params.output_signal_unit, currentFormat); // 输出信号单位
        worksheet->write(row, col++, actuator.params.output_signal_range_min, currentFormat); // 输出信号下限
        worksheet->write(row, col++, actuator.params.output_signal_range_max, currentFormat); // 输出信号上限

        row++;
    }

    return row;
}

//// 🆕 新增：传感器组详细信息添加方法（参考作动器组实现）
//int XLSDataExporter_1_2::addSensorGroupDetailToExcel(QXlsx::Worksheet* worksheet, const UI::SensorGroup_1_2& group, int row, int displayGroupId) {
//    if (!worksheet) return row;

//    // 如果没有提供显示组ID，使用原来的组ID
//    int actualGroupId = (displayGroupId == -1) ? group.groupId : displayGroupId;

//    // 创建数据格式
//    QXlsx::Format dataFormat;
//    dataFormat.setBorderStyle(QXlsx::Format::BorderThin);

//    // 创建组名称格式（浅蓝色背景，粗体）
//    QXlsx::Format groupFormat;
//    groupFormat.setBorderStyle(QXlsx::Format::BorderThin);
//    groupFormat.setPatternBackgroundColor(QColor(231, 243, 255));
//    groupFormat.setFontBold(true);

//    // 导出组内每个传感器的详细信息
//    for (int i = 0; i < group.sensors.size(); ++i) {
//        const UI::SensorParams_1_2& sensor = group.sensors[i];

//        // 选择格式（第一个传感器使用组格式）
//        QXlsx::Format currentFormat = (i == 0) ? groupFormat : dataFormat;

//        // 🔍 调试信息：记录组名称显示逻辑
//        qDebug() << QString("=== 传感器组名称显示逻辑 ===");
//        qDebug() << QString("组ID: %1, 传感器索引: %2, 组名称: %3").arg(group.groupId).arg(i).arg(group.groupName);
//        qDebug() << QString("传感器序列号: %1, 显示组名称: %2").arg(sensor.params_sn).arg(i == 0 ? "是" : "否");

//        // 🔄 修改：写入传感器详细信息（33列：组信息2列 + 传感器信息31列，去掉传感器序号）
//        worksheet->write(row, 1, actualGroupId, currentFormat);                    // 组序号（使用显示序号）

//        // 🔄 修复：传感器组名称只在每组第一行显示，其他行为空字符串
//        if (i == 0) {
//            worksheet->write(row, 2, group.groupName, currentFormat);              // 传感器组名称（只在第一行显示）
//            qDebug() << QString("写入组名称到行%1列2: '%2'").arg(row).arg(group.groupName);
//        } else {
//            worksheet->write(row, 2, QVariant(), currentFormat);                   // 其他行为空值（使用QVariant()确保真正为空）
//            qDebug() << QString("写入空值到行%1列2").arg(row);
//        }
//        // 🔄 修改：写入传感器简化详细信息（14字段新需求版本）
//        worksheet->write(row, 3, sensor.params_sn, currentFormat);             // 传感器序列号（使用新字段）
//        worksheet->write(row, 4, sensor.params_model, currentFormat);           // 传感器类型（使用型号字段）
//        worksheet->write(row, 5, sensor.zero_offset, currentFormat);           // 零点偏移
//        worksheet->write(row, 6, sensor.enable ? u8"是" : u8"否", currentFormat); // 使能状态
//        worksheet->write(row, 7, sensor.params_k, currentFormat);              // 系数K
//        worksheet->write(row, 8, sensor.params_b, currentFormat);              // 系数B
//        worksheet->write(row, 9, sensor.params_precision, currentFormat);      // 精度
//        worksheet->write(row, 10, sensor.params_polarity, currentFormat);      // 极性
//        worksheet->write(row, 11, sensor.meas_unit, currentFormat);            // 测量单位
//        worksheet->write(row, 12, sensor.meas_range_min, currentFormat);       // 测量范围最小值
//        worksheet->write(row, 13, sensor.meas_range_max, currentFormat);       // 测量范围最大值
//        worksheet->write(row, 14, sensor.output_signal_unit, currentFormat);   // 输出信号单位
//        worksheet->write(row, 15, sensor.output_signal_range_min, currentFormat); // 输出信号范围最小值
//        worksheet->write(row, 16, sensor.output_signal_range_max, currentFormat); // 输出信号范围最大值

//        row++;
//    }

//    return row;
//}

QString XLSDataExporter_1_2::getItemType(QTreeWidgetItem* item) const {
    if (!item) return QString();

    QString itemName = item->text(0);
    QString tooltip = item->toolTip(0);

    // 根据节点特征判断类型
    if (itemName.contains(u8"作动器") && !itemName.contains(u8"组")) {
        return u8"作动器设备";
    } else if (itemName.contains(u8"传感器") && !itemName.contains(u8"组")) {
        return u8"传感器设备";
    } else if (itemName.contains(u8"作动器组") || itemName.endsWith(u8"_作动器组")) {
        return u8"作动器组";
    } else if (itemName.contains(u8"传感器组") || itemName.endsWith(u8"_传感器组")) {
        return u8"传感器组";
    } else if (itemName.startsWith("LD-") || tooltip.contains("CH")) {
        return u8"硬件节点";
    } else if (itemName == u8"作动器") {
        return u8"作动器";
    } else if (itemName == u8"传感器") {
        return u8"传感器";
    } else if (itemName == u8"硬件节点资源") {
        return u8"硬件节点资源";
    } else if (itemName == u8"试验配置") {
        return u8"试验配置";
    } else {
        return u8"其他";
    }
}



QList<QStringList> XLSDataExporter_1_2::readHardwareDataFromExcel(const QString& filePath) {
    QList<QStringList> data;
    clearError();

    try {
        QXlsx::Document document(filePath);
        // QtXlsx会自动加载文件，不需要调用load()方法

        // 获取第一个工作表
        auto worksheet = dynamic_cast<QXlsx::Worksheet*>(document.currentSheet());
        if (!worksheet) {
            setError(QString(u8"无法获取工作表"));
            return data;
        }

        // 获取使用的范围
        QXlsx::CellRange usedRange = worksheet->dimension();
        if (!usedRange.isValid()) {
            setError(QString(u8"工作表为空"));
            return data;
        }

        // 读取数据（跳过表头行）
        int startRow = includeHeader_ ? 6 : 1; // 跳过文件头和表头
        for (int row = startRow; row <= usedRange.lastRow(); ++row) {
            QStringList rowData;
            bool hasData = false;

            for (int col = 1; col <= 5; ++col) { // 读取5列数据
                QVariant cellValue = document.read(row, col);
                QString value = cellValue.toString().trimmed();
                rowData.append(value);

                if (!value.isEmpty()) {
                    hasData = true;
                }
            }

            if (hasData) {
                data.append(rowData);
            }
        }

        return data;

    } catch (const std::exception& e) {
        setError(QString(u8"读取过程中发生异常: %1").arg(e.what()));
        return data;
    } catch (...) {
        setError(QString(u8"读取过程中发生未知异常"));
        return data;
    }
}

// 注释掉重复函数：此功能已在SensorExcelExtensions_1_2中实现，请使用SensorExcelExtensions_1_2::importEnhancedSensorDetails
/*
QList<UI::SensorParams_1_2> XLSDataExporter_1_2::readSensorDataFromExcel(const QString& filePath) {
    QList<UI::SensorParams_1_2> sensorList;
    clearError();

    try {
        QXlsx::Document document(filePath);
        // QtXlsx会自动加载文件，不需要调用load()方法

        // 查找传感器详细配置工作表
        QStringList sheetNames = document.sheetNames();
        QString sensorSheetName;

        for (const QString& name : sheetNames) {
            if (name.contains(u8"传感器") || name.contains("sensor")) {
                sensorSheetName = name;
                break;
            }
        }

        if (sensorSheetName.isEmpty()) {
            // 如果没有专门的传感器工作表，使用第一个工作表
            sensorSheetName = sheetNames.first();
        }

        document.selectSheet(sensorSheetName);
        auto worksheet = dynamic_cast<QXlsx::Worksheet*>(document.currentSheet());
        if (!worksheet) {
            setError(QString(u8"无法获取传感器工作表"));
            return sensorList;
        }

        // 获取使用的范围
        QXlsx::CellRange usedRange = worksheet->dimension();
        if (!usedRange.isValid()) {
            setError(QString(u8"传感器工作表为空"));
            return sensorList;
        }

        // 读取传感器数据（跳过表头行）
        int startRow = includeHeader_ ? 4 : 1; // 跳过文件头和表头
        for (int row = startRow; row <= usedRange.lastRow(); ++row) {
            QStringList rowData;

            for (int col = 1; col <= 6; ++col) { // 读取6列传感器数据
                QVariant cellValue = document.read(row, col);
                rowData.append(cellValue.toString().trimmed());
            }

            // 如果第一列（序列号）不为空，则解析为传感器参数
            if (!rowData[0].isEmpty()) {
                UI::SensorParams_1_2 params = parseRowToSensorParams(rowData);
                sensorList.append(params);
            }
        }

        return sensorList;

    } catch (const std::exception& e) {
        setError(QString(u8"读取传感器数据过程中发生异常: %1").arg(e.what()));
        return sensorList;
    } catch (...) {
        setError(QString(u8"读取传感器数据过程中发生未知异常"));
        return sensorList;
    }
}
*/

UI::SensorParams_1_2 XLSDataExporter_1_2::parseRowToSensorParams(const QStringList& rowData) {
    UI::SensorParams_1_2 params;

    if (rowData.size() >= 6) {
        params.params_sn = rowData[0];          // 使用新字段
        params.params_model = rowData[1];        // 使用新字段
        params.params_precision = rowData[2].toDouble();  // 精度是double类型
        params.meas_range_max = rowData[3].toDouble(); // 范围使用测量范围最大值
        params.sensorType = rowData[4];           // 保留兼容性字段
        params.params_model = rowData[5];         // 型号字段
    }

    return params;
}

bool XLSDataExporter_1_2::validateExcelFile(const QString& filePath) {
    QFileInfo fileInfo(filePath);

    // 检查文件是否存在
    if (!fileInfo.exists()) {
        setError(QString(u8"文件不存在: %1").arg(filePath));
        return false;
    }

    // 检查文件扩展名
    QString suffix = fileInfo.suffix().toLower();
    if (suffix != "xlsx" && suffix != "xls") {
        setError(QString(u8"不支持的文件格式: %1").arg(suffix));
        return false;
    }

    // 检查文件是否可读
    if (!fileInfo.isReadable()) {
        setError(QString(u8"文件不可读: %1").arg(filePath));
        return false;
    }

    return true;
}

void XLSDataExporter_1_2::autoFitColumnWidths(QXlsx::Worksheet* worksheet, int maxCol) {
    if (!worksheet) return;

    // 设置默认列宽
    QMap<int, double> columnWidths;
    columnWidths[1] = 15.0; // 类型列
    columnWidths[2] = 25.0; // 名称列
    columnWidths[3] = 20.0; // 参数1列
    columnWidths[4] = 20.0; // 参数2列
    columnWidths[5] = 20.0; // 参数3列

    // 应用列宽设置 - 使用正确的API
    for (auto it = columnWidths.begin(); it != columnWidths.end(); ++it) {
        if (it.key() <= maxCol) {
            // 使用列号和列号的范围来设置列宽
            worksheet->setColumnWidth(it.key(), it.key(), it.value());
        }
    }
}

QStringList XLSDataExporter_1_2::parseTooltipToParams(const QString& tooltip) {
    QStringList params;

    if (tooltip.isEmpty()) {
        params << "" << "" << "";
        return params;
    }

    // 解析tooltip中的参数信息
    QStringList lines = tooltip.split('\n', QString::SkipEmptyParts);

    for (const QString& line : lines) {
        QString trimmedLine = line.trimmed();

        // 解析IP地址
        if (trimmedLine.contains("IP=") || trimmedLine.contains("IP:")) {
            QString ip = extractValueFromLine(trimmedLine, "IP");
            if (!ip.isEmpty() && params.size() < 3) {
                params.append(ip);
            }
        }
        // 解析端口
        else if (trimmedLine.contains("Port=") || trimmedLine.contains("Port:")) {
            QString port = extractValueFromLine(trimmedLine, "Port");
            if (!port.isEmpty() && params.size() < 3) {
                params.append(port);
            }
        }
        // 解析其他参数
        else if (trimmedLine.contains("=") || trimmedLine.contains(":")) {
            QStringList parts = trimmedLine.split(QRegExp("[=:]"), QString::SkipEmptyParts);
            if (parts.size() >= 2 && params.size() < 3) {
                params.append(parts[1].trimmed());
            }
        }
    }

    // 确保至少有3个参数
    while (params.size() < 3) {
        params.append("");
    }

    return params.mid(0, 3);
}

QString XLSDataExporter_1_2::extractValueFromLine(const QString& line, const QString& key) {
    QRegExp regex(key + "[=:]\\s*([^,\\s]+)");
    if (regex.indexIn(line) != -1) {
        return regex.cap(1).trimmed();
    }
    return QString();
}

// 🆕 新增：作动器组导出方法实现
bool XLSDataExporter_1_2::exportActuatorGroups(const QList<UI::ActuatorGroup_1_2>& actuatorGroups, const QString& filePath) {
    clearError();

    if (actuatorGroups.isEmpty()) {
        setError(QString(u8"作动器组列表为空"));
        return false;
    }

    try {
        auto document = createDocument();
        if (!document) {
            setError(QString(u8"无法创建Excel文档"));
            return false;
        }

        // 创建主工作表：作动器组配置表
        document->selectSheet("Sheet1");
        document->renameSheet("Sheet1", u8"作动器组配置表");
        auto worksheet = dynamic_cast<QXlsx::Worksheet*>(document->currentSheet());

        if (!worksheet) {
            setError(QString(u8"无法创建工作表"));
            return false;
        }

        // 写入表头信息
        writeActuatorGroupHeader(worksheet);

        // 写入数据
        int currentRow = 6; // 从第6行开始写入数据
        for (const auto& group : actuatorGroups) {
            currentRow = writeActuatorGroupData(worksheet, group, currentRow);
        }

        // 应用样式和格式
        applyActuatorGroupStyles(worksheet, currentRow - 1);

        // 创建汇总工作表
        createActuatorGroupSummarySheet(document.get(), actuatorGroups);

        // 保存文件
        if (!document->saveAs(filePath)) {
            setError(QString(u8"无法保存Excel文件: %1").arg(filePath));
            return false;
        }

        return true;

    } catch (const std::exception& e) {
        setError(QString(u8"导出作动器组时发生异常: %1").arg(e.what()));
        return false;
    }
}

void XLSDataExporter_1_2::writeActuatorGroupHeader(QXlsx::Worksheet* worksheet) {
    if (!worksheet) return;

    // 写入表头信息区域
    worksheet->write(1, 1, u8"作动器组配置数据表");
    worksheet->write(2, 1, QString(u8"导出时间: %1").arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss")));
    worksheet->write(3, 1, u8"说明: 包含作动器组及其作动器的完整配置信息");

    // 写入数据表头 - 使用最新需求格式
    QStringList headers;
    headers << u8"组序号" << u8"作动器组名称" << u8"作动器序号" << u8"序列号" << u8"作动器类型"
            << u8"控制量名称" << u8"零偏" << u8"下位机ID" << u8"站点ID" << u8"AO板卡ID"
            << u8"AO板卡类型" << u8"AO端口ID" << u8"DO板卡ID" << u8"DO板卡类型" << u8"DO端口ID"
            << u8"型号" << u8"K系数" << u8"B系数" << u8"精度" << u8"极性"
            << u8"测量单位" << u8"测量下限" << u8"测量上限" << u8"输出信号单位" << u8"输出信号下限" << u8"输出信号上限";

    setupHeaderStyle(worksheet, 5, 1, headers);
}

int XLSDataExporter_1_2::writeActuatorGroupData(QXlsx::Worksheet* worksheet, const UI::ActuatorGroup_1_2& group, int startRow) {
    if (!worksheet) return startRow;

    int currentRow = startRow;

    // 创建数据格式
    QXlsx::Format dataFormat;
    dataFormat.setBorderStyle(QXlsx::Format::BorderThin);

    // 创建组名称格式（第一行显示组名称）
    QXlsx::Format groupFormat;
    groupFormat.setBorderStyle(QXlsx::Format::BorderThin);
    groupFormat.setFontBold(true);
    groupFormat.setPatternBackgroundColor(QColor(231, 243, 255)); // 浅蓝色背景

    for (int i = 0; i < group.actuators.size(); ++i) {
        const UI::ActuatorParams_1_2& actuator = group.actuators[i];

        // 选择格式（第一行使用组格式）
        QXlsx::Format& currentFormat = (i == 0) ? groupFormat : dataFormat;

        // 写入组信息（只在第一行显示）
        worksheet->write(currentRow, 1, group.groupId, currentFormat);
        if (i == 0) {
            worksheet->write(currentRow, 2, group.groupName, currentFormat);
        } else {
            worksheet->write(currentRow, 2, "", currentFormat); // 空白
        }

        // 写入作动器信息 - 使用最新需求字段
        worksheet->write(currentRow, 3, i + 1, currentFormat); // 作动器序号
        worksheet->write(currentRow, 4, actuator.params.sn, currentFormat); // 序列号
        worksheet->write(currentRow, 5, ActuatorDataManager_1_2::actuatorTypeToString(actuator.type), currentFormat); // 作动器类型
        worksheet->write(currentRow, 6, actuator.name, currentFormat); // 控制量名称
        worksheet->write(currentRow, 7, actuator.zero_offset, currentFormat); // 零偏
        worksheet->write(currentRow, 8, actuator.lc_id, currentFormat); // 下位机ID
        worksheet->write(currentRow, 9, actuator.station_id, currentFormat); // 站点ID
        worksheet->write(currentRow, 10, actuator.board_id_ao, currentFormat); // AO板卡ID
        worksheet->write(currentRow, 11, actuator.board_type_ao, currentFormat); // AO板卡类型
        worksheet->write(currentRow, 12, actuator.port_id_ao, currentFormat); // AO端口ID
        worksheet->write(currentRow, 13, actuator.board_id_do, currentFormat); // DO板卡ID
        worksheet->write(currentRow, 14, actuator.board_type_do, currentFormat); // DO板卡类型
        worksheet->write(currentRow, 15, actuator.port_id_do, currentFormat); // DO端口ID
        worksheet->write(currentRow, 16, actuator.params.model, currentFormat); // 型号
        worksheet->write(currentRow, 17, actuator.params.sn, currentFormat); // K系数
        worksheet->write(currentRow, 18, actuator.params.b, currentFormat); // B系数
        worksheet->write(currentRow, 19, actuator.params.precision, currentFormat); // 精度
        worksheet->write(currentRow, 20, static_cast<int>(actuator.params.polarity), currentFormat); // 极性
        worksheet->write(currentRow, 21, ActuatorDataManager_1_2::measurementUnitToString(actuator.params.meas_unit), currentFormat); // 测量单位
        worksheet->write(currentRow, 22, actuator.params.meas_range_min, currentFormat); // 测量下限
        worksheet->write(currentRow, 23, actuator.params.meas_range_max, currentFormat); // 测量上限
        worksheet->write(currentRow, 24, actuator.params.output_signal_unit, currentFormat); // 输出信号单位
        worksheet->write(currentRow, 25, actuator.params.output_signal_range_min, currentFormat); // 输出信号下限
        worksheet->write(currentRow, 26, actuator.params.output_signal_range_max, currentFormat); // 输出信号上限

        currentRow++;
    }

    return currentRow;
}

void XLSDataExporter_1_2::applyActuatorGroupStyles(QXlsx::Worksheet* worksheet, int lastRow) {
    if (!worksheet) return;

    // 设置列宽
    QMap<int, double> columnWidths;
    columnWidths[1] = 8;   // 组序号
    columnWidths[2] = 20;  // 作动器组名称
    columnWidths[3] = 10;  // 作动器序号
    columnWidths[4] = 15;  // 作动器序列号
    columnWidths[5] = 12;  // 作动器类型
    columnWidths[6] = 10;  // Unit类型
    columnWidths[7] = 10;  // Unit值
    columnWidths[8] = 10;  // 行程
    columnWidths[9] = 10;  // 位移
    columnWidths[10] = 12; // 拉伸面积
    columnWidths[11] = 12; // 压缩面积
    columnWidths[12] = 10; // 极性
    columnWidths[13] = 12; // Deliver
    columnWidths[14] = 10; // 频率
    columnWidths[15] = 12; // 输出倍数
    columnWidths[16] = 10; // 平衡
    columnWidths[17] = 25; // 备注

    // 应用列宽设置
    for (auto it = columnWidths.begin(); it != columnWidths.end(); ++it) {
        worksheet->setColumnWidth(it.key(), it.key(), it.value());
    }
}

void XLSDataExporter_1_2::createActuatorGroupSummarySheet(QXlsx::Document* document, const QList<UI::ActuatorGroup_1_2>& actuatorGroups) {
    if (!document) return;

    // 创建汇总工作表
    document->addSheet(u8"作动器组汇总");
    document->selectSheet(u8"作动器组汇总");
    auto worksheet = dynamic_cast<QXlsx::Worksheet*>(document->currentSheet());

    if (!worksheet) return;

    // 写入汇总表头
    QStringList summaryHeaders;
    summaryHeaders << u8"组序号" << u8"作动器组名称" << u8"作动器数量" << u8"主要类型" << u8"创建时间" << u8"备注";
    setupHeaderStyle(worksheet, 1, 1, summaryHeaders);

    // 写入汇总数据
    QXlsx::Format dataFormat;
    dataFormat.setBorderStyle(QXlsx::Format::BorderThin);

    int row = 2;
    for (const auto& group : actuatorGroups) {
        worksheet->write(row, 1, group.groupId, dataFormat);
        worksheet->write(row, 2, group.groupName, dataFormat);
        worksheet->write(row, 3, group.actuators.size(), dataFormat);
        worksheet->write(row, 4, group.groupType, dataFormat);
        worksheet->write(row, 5, group.createTime, dataFormat);
        worksheet->write(row, 6, group.groupNotes, dataFormat);
        row++;
    }

    // 设置汇总表列宽
    worksheet->setColumnWidth(1, 1, 8);   // 组序号
    worksheet->setColumnWidth(2, 2, 20);  // 作动器组名称
    worksheet->setColumnWidth(3, 3, 12);  // 作动器数量
    worksheet->setColumnWidth(4, 4, 15);  // 主要类型
    worksheet->setColumnWidth(5, 5, 15);  // 创建时间
    worksheet->setColumnWidth(6, 6, 25);  // 备注
}

// 🆕 新增：作动器专用工作表方法
bool XLSDataExporter_1_2::createActuatorWorksheet(QXlsx::Document* document, const QList<UI::ActuatorGroup_1_2>& actuatorGroups) {
    if (!document) {
        setError(QString(u8"Excel文档指针为空"));
        return false;
    }

    if (actuatorGroups.isEmpty()) {
        setError(QString(u8"作动器组列表为空"));
        return false;
    }

    try {
        // 创建"作动器"工作表
        document->addSheet(u8"作动器");
        document->selectSheet(u8"作动器");
        auto worksheet = dynamic_cast<QXlsx::Worksheet*>(document->currentSheet());

        if (!worksheet) {
            setError(QString(u8"无法创建作动器工作表"));
            return false;
        }

        // 写入作动器工作表表头
        writeActuatorWorksheetHeader(worksheet);

        // 写入作动器数据
        int currentRow = 6; // 从第6行开始写入数据
        for (const auto& group : actuatorGroups) {
            currentRow = writeActuatorGroupData(worksheet, group, currentRow);
        }

        // 应用作动器工作表样式
        applyActuatorWorksheetStyles(worksheet, currentRow - 1);

        return true;

    } catch (const std::exception& e) {
        setError(QString(u8"创建作动器工作表时发生异常: %1").arg(e.what()));
        return false;
    } catch (...) {
        setError(QString(u8"创建作动器工作表时发生未知异常"));
        return false;
    }
}

bool XLSDataExporter_1_2::addActuatorWorksheetToExcel(const QString& filePath, const QList<UI::ActuatorGroup_1_2>& actuatorGroups) {
    clearError();

    if (actuatorGroups.isEmpty()) {
        setError(QString(u8"作动器组列表为空"));
        return false;
    }

    try {
        // 打开现有的Excel文件或创建新文件
        std::unique_ptr<QXlsx::Document> document;

        QFileInfo fileInfo(filePath);
        if (fileInfo.exists()) {
            // 文件存在，打开现有文件
            document = std::make_unique<QXlsx::Document>(filePath);
        } else {
            // 文件不存在，创建新文件
            document = createDocument();
        }

        if (!document) {
            setError(QString(u8"无法打开或创建Excel文档"));
            return false;
        }

        // 检查是否已存在"作动器"工作表
        QStringList sheetNames = document->sheetNames();
        if (sheetNames.contains(u8"作动器")) {
            // 删除现有的作动器工作表
            document->deleteSheet(u8"作动器");
        }

        // 创建新的作动器工作表
        if (!createActuatorWorksheet(document.get(), actuatorGroups)) {
            return false;
        }

        // 保存文件
        if (!document->saveAs(filePath)) {
            setError(QString(u8"无法保存Excel文件: %1").arg(filePath));
            return false;
        }

        return true;

    } catch (const std::exception& e) {
        setError(QString(u8"添加作动器工作表时发生异常: %1").arg(e.what()));
        return false;
    }
}

void XLSDataExporter_1_2::writeActuatorWorksheetHeader(QXlsx::Worksheet* worksheet) {
    if (!worksheet) return;

    // 写入作动器工作表专用表头信息
    worksheet->write(1, 1, u8"作动器配置数据表");
    worksheet->write(2, 1, QString(u8"导出时间: %1").arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss")));
    worksheet->write(3, 1, u8"说明: 包含作动器组及其作动器的完整配置信息");

    // 写入作动器专用数据表头（详细配置格式，25列）
    QStringList headers;
    headers << u8"组序号" << u8"作动器组名称" << u8"控制量名称" << u8"作动器类型" << u8"零偏"
            << u8"下位机ID" << u8"站点ID" << u8"AO板卡ID" << u8"AO板卡类型" << u8"AO端口ID"
            << u8"DO板卡ID" << u8"DO板卡类型" << u8"DO端口ID" << u8"型号" << u8"序列号"
            << u8"K系数" << u8"B系数" << u8"精度" << u8"极性" << u8"测量单位"
            << u8"测量下限" << u8"测量上限" << u8"输出信号单位" << u8"输出信号下限" << u8"输出信号上限";

    // 创建作动器专用表头格式
    QXlsx::Format headerFormat;
    headerFormat.setFontBold(true);
    headerFormat.setFontSize(11);
    headerFormat.setFontColor(QColor(Qt::white));
    headerFormat.setPatternBackgroundColor(QColor(68, 114, 196)); // 深蓝色背景
    headerFormat.setHorizontalAlignment(QXlsx::Format::AlignHCenter);
    headerFormat.setVerticalAlignment(QXlsx::Format::AlignVCenter);
    headerFormat.setBorderStyle(QXlsx::Format::BorderThin);

    // 写入表头
    for (int i = 0; i < headers.size(); ++i) {
        worksheet->write(5, i + 1, headers[i], headerFormat);
    }
}

void XLSDataExporter_1_2::applyActuatorWorksheetStyles(QXlsx::Worksheet* worksheet, int lastRow) {
    if (!worksheet) return;

    // 设置作动器工作表专用列宽（新需求格式，25列）
    QMap<int, double> columnWidths;
    columnWidths[1] = 8;   // 组序号
    columnWidths[2] = 20;  // 作动器组名称
    columnWidths[3] = 15;  // 控制量名称
    columnWidths[4] = 12;  // 作动器类型
    columnWidths[5] = 10;  // 零偏
    columnWidths[6] = 10;  // 下位机ID
    columnWidths[7] = 10;  // 站点ID
    columnWidths[8] = 10;  // AO板卡ID
    columnWidths[9] = 12;  // AO板卡类型
    columnWidths[10] = 10; // AO端口ID
    columnWidths[11] = 10; // DO板卡ID
    columnWidths[12] = 12; // DO板卡类型
    columnWidths[13] = 10; // DO端口ID
    columnWidths[14] = 12; // 型号
    columnWidths[15] = 15; // 序列号
    columnWidths[16] = 10; // K系数
    columnWidths[17] = 10; // B系数
    columnWidths[18] = 10; // 精度
    columnWidths[19] = 10; // 极性
    columnWidths[20] = 12; // 测量单位
    columnWidths[21] = 12; // 测量下限
    columnWidths[22] = 12; // 测量上限
    columnWidths[23] = 12; // 输出信号单位
    columnWidths[24] = 12; // 输出信号下限
    columnWidths[25] = 12; // 输出信号上限

    // 应用列宽设置
    for (auto it = columnWidths.begin(); it != columnWidths.end(); ++it) {
        worksheet->setColumnWidth(it.key(), it.key(), it.value());
    }

    // 设置表头信息区域样式
    QXlsx::Format titleFormat;
    titleFormat.setFontBold(true);
    titleFormat.setFontSize(14);
    titleFormat.setFontColor(QColor(68, 114, 196));
    worksheet->write(1, 1, u8"作动器配置数据表", titleFormat);

    QXlsx::Format infoFormat;
    infoFormat.setFontSize(10);
    infoFormat.setFontColor(QColor(128, 128, 128));
    worksheet->write(2, 1, QString(u8"导出时间: %1").arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss")), infoFormat);
    worksheet->write(3, 1, u8"说明: 包含作动器组及其作动器的完整配置信息", infoFormat);
}

bool XLSDataExporter_1_2::exportCompleteProjectWithActuators(QTreeWidget* treeWidget, const QList<UI::ActuatorGroup_1_2>& actuatorGroups, const QString& filePath) {
    clearError();

    if (!treeWidget) {
        setError(QString(u8"硬件树控件为空"));
        return false;
    }

    try {
        auto document = createDocument();
        if (!document) {
            setError(QString(u8"无法创建Excel文档"));
            return false;
        }

        // 1. 创建硬件配置工作表
        document->addSheet(worksheetName_);
        document->selectSheet(worksheetName_);
        auto worksheet = dynamic_cast<QXlsx::Worksheet*>(document->currentSheet());

        if (!worksheet) {
            setError(QString(u8"无法创建硬件配置工作表"));
            return false;
        }

        int currentRow = 1;

        // 写入项目信息
        if (includeHeader_) {
            document->write(currentRow, 1, QString(u8"# 实验工程配置文件"));
            document->write(currentRow, 2, QString(u8"完整项目配置"));
            currentRow++;

            document->write(currentRow, 1, QString(u8"# 导出时间"));
            document->write(currentRow, 2, QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"));
            currentRow++;

            document->write(currentRow, 1, QString(u8"# 格式"));
            document->write(currentRow, 2, QString(u8"Excel"));
            currentRow += 2;

            // 设置表头
            QStringList headers;
            headers << u8"类型" << u8"名称" << u8"参数1" << u8"参数2" << u8"参数3";
            setupHeaderStyle(worksheet, currentRow, 1, headers);
            currentRow++;
        }

        // 导出硬件树结构
        QTreeWidgetItem* rootItem = treeWidget->invisibleRootItem();
        if (rootItem) {
            for (int i = 0; i < rootItem->childCount(); ++i) {
                currentRow = exportTreeItemToExcel(worksheet, rootItem->child(i), currentRow, 0);
            }
        }

        // 自动调整列宽
        if (autoFitColumns_) {
            autoFitColumnWidths(worksheet, 5);
        }

        // 2. 创建作动器工作表（如果有作动器数据）
        if (!actuatorGroups.isEmpty()) {
            if (!createActuatorWorksheet(document.get(), actuatorGroups)) {
                setError(QString(u8"创建作动器工作表失败"));
                return false;
            }
        }

        // 3. 保存文件
        if (!document->saveAs(filePath)) {
            setError(QString(u8"保存Excel文件失败"));
            return false;
        }

        return true;

    } catch (const std::exception& e) {
        setError(QString(u8"导出完整项目时发生异常: %1").arg(e.what()));
        return false;
    } catch (...) {
        setError(QString(u8"导出完整项目时发生未知异常"));
        return false;
    }
}

QList<UI::ActuatorGroup_1_2> XLSDataExporter_1_2::readActuatorGroupsFromExcel(const QString& filePath) {
    QList<UI::ActuatorGroup_1_2> actuatorGroups;
    clearError();

    try {
        QXlsx::Document document(filePath);

        // 查找作动器工作表
        QStringList sheetNames = document.sheetNames();
        QString actuatorSheetName;

        for (const QString& name : sheetNames) {
            if (name.contains(u8"作动器") || name.contains("actuator")) {
                actuatorSheetName = name;
                break;
            }
        }

        if (actuatorSheetName.isEmpty()) {
            setError(QString(u8"Excel文件中没有找到作动器工作表"));
            return actuatorGroups;
        }

        document.selectSheet(actuatorSheetName);
        auto worksheet = dynamic_cast<QXlsx::Worksheet*>(document.currentSheet());
        if (!worksheet) {
            setError(QString(u8"无法获取作动器工作表"));
            return actuatorGroups;
        }

        // 获取使用的范围
        QXlsx::CellRange usedRange = worksheet->dimension();
        if (!usedRange.isValid()) {
            setError(QString(u8"作动器工作表为空"));
            return actuatorGroups;
        }

        // 读取作动器数据（从第6行开始，跳过表头）
        QMap<int, UI::ActuatorGroup_1_2> groupMap;

        for (int row = 2; row <= usedRange.lastRow(); ++row) {
            QStringList rowData;

            // 读取25列数据（新格式）
            for (int col = 1; col <= 25; ++col) {
                QVariant cellValue = document.read(row, col);
                rowData.append(cellValue.toString().trimmed());
            }

            // 如果组序号不为空，则解析数据
            if (!rowData[0].isEmpty()) {
                int groupId = rowData[0].toInt();

                // 创建或获取作动器组
                if (!groupMap.contains(groupId)) {
                    UI::ActuatorGroup_1_2 group;
                    group.groupId = groupId;
                    group.groupName = rowData[1]; // 组名称
                    group.groupType = u8"未知类型";
                    group.createTime = QDateTime::currentDateTime().toString("yyyy-MM-dd");
                    group.groupNotes = u8"从Excel导入";
                    groupMap[groupId] = group;
                }

                // 解析作动器参数
                UI::ActuatorParams_1_2 actuator = parseRowToActuatorParams(rowData);

                // 🔍 调试信息：输出解析后的作动器参数
                qDebug() << QString(u8"🔍 Excel导入第%1行解析完成 - 控制量名称: '%2', 序列号: '%3'")
                            .arg(row).arg(actuator.name).arg(actuator.params.sn);

                groupMap[groupId].actuators.append(actuator);
            }
        }

        // 转换为列表
        for (auto it = groupMap.begin(); it != groupMap.end(); ++it) {
            actuatorGroups.append(it.value());
        }

        return actuatorGroups;

    } catch (const std::exception& e) {
        setError(QString(u8"读取作动器组数据时发生异常: %1").arg(e.what()));
        return actuatorGroups;
    } catch (...) {
        setError(QString(u8"读取作动器组数据时发生未知异常"));
        return actuatorGroups;
    }
}

bool XLSDataExporter_1_2::exportSingleActuatorGroup(const UI::ActuatorGroup_1_2& actuatorGroup, const QString& filePath) {
    clearError();

    QList<UI::ActuatorGroup_1_2> singleGroupList;
    singleGroupList.append(actuatorGroup);

    return exportActuatorGroups(singleGroupList, filePath);
}

UI::ActuatorParams_1_2 XLSDataExporter_1_2::parseRowToActuatorParams(const QStringList& rowData) {
    UI::ActuatorParams_1_2 params;

    if (rowData.size() >= 25) {  // 🆕 只支持新的25列格式
        // 按照新的Excel表头格式解析数据
        // 表头：组序号(1) | 作动器组名称(2) | 控制量名称(3) | 作动器类型(4) | 零偏(5) | ...
        // 按照新的25列格式解析（索引从0开始）
        // 组序号(0) | 作动器组名称(1) | 控制量名称(2) | 作动器类型(3) | 零偏(4) | ...
        QString controlName = rowData[2];       // 控制量名称
        QString typeText = rowData[3];           // 作动器类型
        double zeroOffset = rowData[4].toDouble(); // 零偏
        int lcId = rowData[5].toInt();           // 下位机ID
        int stationId = rowData[6].toInt();      // 站点ID
        int boardIdAo = rowData[7].toInt();      // AO板卡ID
        int boardTypeAo = rowData[8].toInt();    // AO板卡类型
        int portIdAo = rowData[9].toInt();       // AO端口ID
        int boardIdDo = rowData[10].toInt();     // DO板卡ID
        int boardTypeDo = rowData[11].toInt();   // DO板卡类型
        int portIdDo = rowData[12].toInt();      // DO端口ID
        QString model = rowData[13];             // 型号
        QString serialNumber = rowData[14];      // 序列号
        double k = rowData[15].toDouble();       // K系数
        double b = rowData[16].toDouble();       // B系数
        double precision = rowData[17].toDouble(); // 精度
        QString polarityText = rowData[18];      // 极性
        QString measUnitText = rowData[19];      // 测量单位
        double measRangeMin = rowData[20].toDouble(); // 测量下限
        double measRangeMax = rowData[21].toDouble(); // 测量上限
        int outputSignalUnit = rowData[22].toInt();  // 输出信号单位
        double outputSignalRangeMin = rowData[23].toDouble(); // 输出信号下限
        double outputSignalRangeMax = rowData[24].toDouble(); // 输出信号上限

        // 🆕 填充新的数据结构（使用新格式的数据）
        params.name = controlName;
        params.zero_offset = zeroOffset;
        params.lc_id = lcId;
        params.station_id = stationId;
        params.board_id_ao = boardIdAo;
        params.board_type_ao = boardTypeAo;
        params.port_id_ao = portIdAo;
        params.board_id_do = boardIdDo;
        params.board_type_do = boardTypeDo;
        params.port_id_do = portIdDo;

        // 作动器类型转换
        if (typeText == "双出杆") {
            params.type = UI::ActuatorType_1_2::DoubleRod;
        } else {
            params.type = UI::ActuatorType_1_2::SingleRod;
        }

        // 详细参数
        params.params.model = model;
        params.params.sn = serialNumber;
        params.params.k = k;
        params.params.b = b;
        params.params.precision = precision;

        // 🔍 调试信息：输出解析的关键字段和原始数据
        qDebug() << QString(u8"🔍 Excel导入解析 - 控制量名称: '%1', 序列号: '%2', 型号: '%3'")
                    .arg(controlName).arg(serialNumber).arg(model);
        qDebug() << QString(u8"🔍 原始Excel数据 - 第3列: '%1', 第15列: '%2', 第14列: '%3'")
                    .arg(rowData[2]).arg(rowData[14]).arg(rowData[13]);

        // 极性转换 - 使用统一的极性解析函数
        int polarityValue = parsePolarityFromString(polarityText);
        switch (polarityValue) {
            case -1: params.params.polarity = UI::Polarity_1_2::Negative; break;
            case 9:  params.params.polarity = UI::Polarity_1_2::Both; break;
            case 0:  params.params.polarity = UI::Polarity_1_2::Unknown; break;
            default: params.params.polarity = UI::Polarity_1_2::Positive; break;
        }

        // 测量单位转换
        if (measUnitText == "m") {
            params.params.meas_unit = UI::MeasurementUnit_1_2::Meter;
        } else if (measUnitText == "cm") {
            params.params.meas_unit = UI::MeasurementUnit_1_2::Centimeter;
        } else if (measUnitText == "inch") {
            params.params.meas_unit = UI::MeasurementUnit_1_2::Inch;
        } else {
            params.params.meas_unit = UI::MeasurementUnit_1_2::Millimeter;
        }

        // 测量范围和输出信号配置
        params.params.meas_range_min = measRangeMin;
        params.params.meas_range_max = measRangeMax;
        params.params.output_signal_unit = outputSignalUnit;
        params.params.output_signal_range_min = outputSignalRangeMin;
        params.params.output_signal_range_max = outputSignalRangeMax;
    } else {
        // 🔧 修复：处理列数不足的情况
        qDebug() << QString(u8"⚠️ Excel行数据列数不足，期望25列，实际%1列").arg(rowData.size());
        // 设置默认值
        params.name = u8"未知控制量";
        params.type = UI::ActuatorType_1_2::SingleRod;
        params.params.sn = u8"未知序列号";
        params.params.model = u8"未知型号";
        params.params.k = 1.0;
        params.params.b = 0.0;
        params.params.precision = 0.01;
        params.params.polarity = UI::Polarity_1_2::Positive;
        params.params.meas_unit = UI::MeasurementUnit_1_2::Millimeter;
        params.params.meas_range_min = 0.0;
        params.params.meas_range_max = 100.0;
        params.params.output_signal_range_min = 0.0;
        params.params.output_signal_range_max = 10.0;
    }

    return params;
}

// ============================================================================
// 🆕 新增：控制通道详细配置导出方法
// ============================================================================

bool XLSDataExporter_1_2::exportControlChannelDetails(const QList<UI::ControlChannelGroup>& channelGroups, const QString& filePath) {
    clearError();

    if (channelGroups.isEmpty()) {
        setError(QString(u8"控制通道组列表为空"));
        return false;
    }

    try {
        auto document = createDocument();
        if (!document) {
            setError(QString(u8"无法创建Excel文档"));
            return false;
        }

        // 创建控制通道详细信息工作表
        document->addSheet(u8"控制通道详细配置");
        document->selectSheet(u8"控制通道详细配置");
        auto worksheet = dynamic_cast<QXlsx::Worksheet*>(document->currentSheet());

        if (!worksheet) {
            setError(QString(u8"无法创建控制通道详细信息工作表"));
            return false;
        }

        int currentRow = 1;

        // 写入文件头信息
        if (includeHeader_) {
            document->write(currentRow, 1, QString(u8"# 控制通道详细配置文件"));
            document->write(currentRow, 2, QString(u8"导出时间: %1").arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss")));
            currentRow += 2;

            document->write(currentRow, 1, QString(u8"说明: 包含控制通道的关联信息与配置参数"));
            currentRow += 2;

            // 设置控制通道详细信息表头（9列）
            QStringList headers;
            headers << u8"组序号" << u8"通道组名称" << u8"通道名称" << u8"硬件关联"
                    << u8"载荷1传感器" << u8"载荷2传感器" << u8"位置传感器"
                    << u8"控制作动器" << u8"备注";
            setupHeaderStyle(worksheet, currentRow, 1, headers);
            currentRow++;
        }

        // 使用连续的显示序号，确保组序号从1开始连续递增
        for (int groupIndex = 0; groupIndex < channelGroups.size(); ++groupIndex) {
            const UI::ControlChannelGroup& group = channelGroups[groupIndex];
            int displayGroupId = groupIndex + 1; // 显示序号从1开始连续递增
            currentRow = addControlChannelGroupDetailToExcel(worksheet, group, currentRow, displayGroupId);
        }

        // 自动调整列宽（9列）
        if (autoFitColumns_) {
            autoFitColumnWidths(worksheet, 9);
        }

        // 保存文件
        if (!document->saveAs(filePath)) {
            setError(QString(u8"保存Excel文件失败"));
            return false;
        }

        return true;

    } catch (const std::exception& e) {
        setError(QString(u8"导出控制通道详细信息时发生异常: %1").arg(e.what()));
        return false;
    } catch (...) {
        setError(QString(u8"导出控制通道详细信息时发生未知异常"));
        return false;
    }
}

int XLSDataExporter_1_2::addControlChannelGroupDetailToExcel(QXlsx::Worksheet* worksheet, const UI::ControlChannelGroup& group, int row, int displayGroupId) {
    if (!worksheet) return row;

    // 如果没有提供显示组ID，使用原来的组ID
    int actualGroupId = (displayGroupId == -1) ? group.groupId : displayGroupId;

    // 创建数据格式
    QXlsx::Format dataFormat;
    dataFormat.setBorderStyle(QXlsx::Format::BorderThin);

    // 创建组名称格式（浅绿色背景，粗体）
    QXlsx::Format groupFormat;
    groupFormat.setBorderStyle(QXlsx::Format::BorderThin);
    groupFormat.setPatternBackgroundColor(QColor(231, 255, 231)); // 浅绿色区分作动器
    groupFormat.setFontBold(true);

    // 导出组内每个控制通道的详细信息
    for (int i = 0; i < group.channels.size(); ++i) {
        const UI::ControlChannelParams& channel = group.channels[i];

        // 选择格式（第一个通道使用组格式）
        QXlsx::Format currentFormat = (i == 0) ? groupFormat : dataFormat;

        // 写入控制通道详细信息（9列）
        worksheet->write(row, 1, actualGroupId, currentFormat);                    // 组序号
        worksheet->write(row, 2, (i == 0) ? QString::fromStdString(group.groupName) : QString(), currentFormat); // 通道组名称（只在第一行显示）
        worksheet->write(row, 3, QString::fromStdString(channel.channelName), currentFormat);     // 通道名称
        worksheet->write(row, 4, QString::fromStdString(channel.hardwareAssociation), currentFormat); // 硬件关联
        worksheet->write(row, 5, QString::fromStdString(channel.load1Sensor), currentFormat);     // 载荷1传感器
        worksheet->write(row, 6, QString::fromStdString(channel.load2Sensor), currentFormat);     // 载荷2传感器
        worksheet->write(row, 7, QString::fromStdString(channel.positionSensor), currentFormat);  // 位置传感器
        worksheet->write(row, 8, QString::fromStdString(channel.controlActuator), currentFormat); // 控制作动器
        worksheet->write(row, 9, QString::fromStdString(channel.notes), currentFormat);           // 备注

        row++;
    }

    return row;
}

int XLSDataExporter_1_2::addControlChannelDetailToExcel(QXlsx::Worksheet* worksheet, const UI::ControlChannelParams& channel, int row, int channelIndex) {
    if (!worksheet) return row;

    // 创建数据格式
    QXlsx::Format dataFormat;
    dataFormat.setBorderStyle(QXlsx::Format::BorderThin);

    // 🆕 扩展：写入控制通道详细信息（包含所有扩展字段）
    int col = 1;
    worksheet->write(row, col++, channelIndex, dataFormat);                                    // 通道序号
    
    // 🔧 修复：通道ID转换为数字格式（递增）
    QString channelIdStr = QString::fromStdString(channel.channelId);
    int numericChannelId = channelIndex; // 默认使用通道序号
    
    // 尝试从channelId字符串中提取数字（如"CH1" -> 1, "CH002" -> 2）
    QRegExp regex("\\d+");  // 匹配数字
    if (regex.indexIn(channelIdStr) != -1) {
        bool ok;
        int extracted = regex.cap(0).toInt(&ok);
        if (ok) {
            numericChannelId = extracted;
        }
    }
    
    worksheet->write(row, col++, numericChannelId, dataFormat);                               // 通道ID（数字格式）
    worksheet->write(row, col++, QString::fromStdString(channel.channelName), dataFormat);    // 通道名称
    
    // 🆕 新增：扩展配置字段
    worksheet->write(row, col++, channel.lc_id, dataFormat);                                  // 下位机ID
    worksheet->write(row, col++, channel.station_id, dataFormat);                             // 站点ID
    worksheet->write(row, col++, channel.enable ? u8"是" : u8"否", dataFormat);               // 使能状态
    worksheet->write(row, col++, channel.control_mode, dataFormat);                           // 控制模式
    
    // 关联配置
    worksheet->write(row, col++, QString::fromStdString(channel.hardwareAssociation), dataFormat); // 硬件关联
    worksheet->write(row, col++, QString::fromStdString(channel.load1Sensor), dataFormat);    // 载荷1传感器
    worksheet->write(row, col++, QString::fromStdString(channel.load2Sensor), dataFormat);    // 载荷2传感器
    worksheet->write(row, col++, QString::fromStdString(channel.positionSensor), dataFormat); // 位置传感器
    worksheet->write(row, col++, QString::fromStdString(channel.controlActuator), dataFormat); // 控制作动器
    
    // 🆕 新增：极性参数（存储数字值）
    worksheet->write(row, col++, channel.servo_control_polarity, dataFormat);    // 控制作动器极性
    worksheet->write(row, col++, channel.payload_sensor1_polarity, dataFormat);  // 载荷1传感器极性
    worksheet->write(row, col++, channel.payload_sensor2_polarity, dataFormat);  // 载荷2传感器极性
    worksheet->write(row, col++, channel.position_sensor_polarity, dataFormat);  // 位置传感器极性
    
    worksheet->write(row, col++, QString::fromStdString(channel.notes), dataFormat);          // 备注

    return row + 1;
}

void XLSDataExporter_1_2::writeControlChannelHeader(QXlsx::Worksheet* worksheet) {
    if (!worksheet) return;

    // 写入表头信息区域
    worksheet->write(1, 1, u8"控制通道详细配置数据表");
    worksheet->write(2, 1, QString(u8"导出时间: %1").arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss")));
    worksheet->write(3, 1, u8"说明: 包含控制通道的关联信息与配置参数");

    // 写入数据表头
    QStringList headers;
    headers << u8"组序号" << u8"通道组名称" << u8"通道名称" << u8"硬件关联"
            << u8"载荷1传感器" << u8"载荷2传感器" << u8"位置传感器"
            << u8"控制作动器" << u8"备注";

    setupHeaderStyle(worksheet, 5, 1, headers);
}

QString XLSDataExporter_1_2::controlModeToString(const std::string& mode) const {
    if (mode == "Force") return u8"力控制";
    if (mode == "Position") return u8"位移控制";
    if (mode == "Velocity") return u8"速度控制";
    if (mode == "Hybrid") return u8"混合控制";
    return u8"未知控制";
}

QString XLSDataExporter_1_2::boolToYesNo(bool value) const {
    return value ? u8"是" : u8"否";
}

QString XLSDataExporter_1_2::polarityToString(int polarity) const {
    // 极性存储必须使用数字格式，不再使用文本描述
    return QString::number(polarity);
}

int XLSDataExporter_1_2::parsePolarityFromString(const QString& polarityStr) const {
    QString str = polarityStr.trimmed();
    
    // 只支持新格式（完整描述格式），移除旧英文格式兼容
    if (str.contains("正极性")) return 1;
    if (str.contains("负极性")) return -1;
    if (str.contains("双极性")) return 9;
    if (str.contains("未知")) return 0;
    
    // 数字格式支持
    if (str == "1") return 1;
    if (str == "-1") return -1;
    if (str == "9") return 9;
    if (str == "0") return 0;
    
    // 移除旧格式支持 - 不再兼容简单的英文格式
    
    // 数值格式直接转换
    bool ok;
    int value = str.toInt(&ok);
    if (ok) {
        if (value == 1 || value == -1 || value == 9 || value == 0) {
            return value;
        }
    }
    
    return 1; // 默认正极性
}

// ============================================================================
// 🆕 新增：硬件节点详细配置导出方法
// ============================================================================

bool XLSDataExporter_1_2::exportHardwareNodeDetails(const QList<UI::NodeConfigParams>& nodeConfigs, const QString& filePath) {
    clearError();

    if (nodeConfigs.isEmpty()) {
        setError(QString(u8"硬件节点配置列表为空"));
        return false;
    }

    try {
        auto document = createDocument();
        if (!document) {
            setError(QString(u8"无法创建Excel文档"));
            return false;
        }

        // 🔄 使用原有的正确实现：创建硬件节点工作表
        if (!createHardwareNodeWorksheet(document.get(), nodeConfigs)) {
            return false; // 错误信息已在createHardwareNodeWorksheet中设置
        }

        // 保存文件
        if (!document->saveAs(filePath)) {
            setError(QString(u8"保存Excel文件失败"));
            return false;
        }

        return true;

    } catch (const std::exception& e) {
        setError(QString(u8"导出硬件节点详细信息时发生异常: %1").arg(e.what()));
        return false;
    } catch (...) {
        setError(QString(u8"导出硬件节点详细信息时发生未知异常"));
        return false;
    }
}

// 注意：addNodeConfigDetailToExcel 方法已经在原有实现中存在，不需要重复实现

// 🆕 新增：设置硬件节点配置数据
void XLSDataExporter_1_2::setHardwareNodeConfigs(const QList<UI::NodeConfigParams>& nodeConfigs) {
    hardwareNodeConfigs_ = nodeConfigs;
}



// ============================================================================
// 🆕 新增：作动器组工作表导出方法
// ============================================================================

bool XLSDataExporter_1_2::exportActuatorGroupsToWorksheet(QXlsx::Worksheet* worksheet, const QList<UI::ActuatorGroup_1_2>& actuatorGroups) {
    if (!worksheet || actuatorGroups.isEmpty()) {
        return false;
    }

    int currentRow = 1;

    // 写入作动器工作表头信息
    if (includeHeader_) {
        worksheet->write(currentRow, 1, QString(u8"# 作动器配置文件"));
        worksheet->write(currentRow, 2, QString(u8"导出时间: %1").arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss")));
        currentRow += 2;

        worksheet->write(currentRow, 1, u8"说明: 包含作动器组及其作动器的完整配置信息");
        currentRow += 2;
    }

    // 写入表头
    writeActuatorGroupHeader(worksheet);
    currentRow = 6; // 表头占用5行

    // 导出每个作动器组
    for (const UI::ActuatorGroup_1_2& group : actuatorGroups) {
        currentRow = writeActuatorGroupData(worksheet, group, currentRow);
    }

    // 应用样式
    applyActuatorGroupStyles(worksheet, currentRow - 1);

    // 自动调整列宽
    if (autoFitColumns_) {
        autoFitColumnWidths(worksheet, 17);
    }

    return true;
}

// ============================================================================
// 🆕 新增：硬件节点详细配置导出功能实现
// ============================================================================

bool XLSDataExporter_1_2::createHardwareNodeWorksheet(QXlsx::Document* document, const QList<UI::NodeConfigParams>& nodeConfigs) {
    if (!document || nodeConfigs.isEmpty()) {
        setError(QString(u8"文档为空或硬件节点配置列表为空"));
        return false;
    }

    try {
        // 创建硬件节点详细信息工作表
        document->addSheet(u8"硬件节点详细配置");
        document->selectSheet(u8"硬件节点详细配置");
        auto worksheet = document->currentWorksheet();

        int currentRow = 1;

        // 写入文件头信息
        if (includeHeader_) {
            document->write(currentRow, 1, QString(u8"硬件节点详细配置文件"));
            document->write(currentRow, 2, QString(u8"导出时间: %1").arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss")));
            currentRow += 2;

            // 🆕 修改：设置硬件节点详细信息表头（7列，去掉节点类型）
            QStringList headers;
            headers << u8"节点ID" << u8"节点名称" << u8"通道数量"
                    << u8"通道ID" << u8"通道IP地址" << u8"通道端口" << u8"通道状态";
            setupHeaderStyle(worksheet, currentRow, 1, headers);
            currentRow++;
        }

        // 导出每个硬件节点配置的详细信息
        int nodeId = 1;
        for (const UI::NodeConfigParams& nodeConfig : nodeConfigs) {
            currentRow = addNodeConfigDetailToExcel(worksheet, nodeConfig, currentRow, nodeId);
            nodeId++;
        }

        // 🆕 修改：自动调整列宽（7列，去掉节点类型列）
        if (autoFitColumns_) {
            autoFitColumnWidths(worksheet, 7);
        }

        return true;

    } catch (const std::exception& e) {
        setError(QString(u8"创建硬件节点工作表时发生异常: %1").arg(e.what()));
        return false;
    }
}

//QList<UI::NodeConfigParams> XLSDataExporter_1_2::getHardwareNodeConfigsFromTree(QTreeWidget* treeWidget) const {
//    QList<UI::NodeConfigParams> nodeConfigs;

//    if (!treeWidget) {
//        return nodeConfigs;
//    }

//    // 获取任务1根节点
//    QTreeWidgetItem* taskRoot = treeWidget->topLevelItem(0);
//    if (!taskRoot) {
//        return nodeConfigs;
//    }

//    // 查找硬件节点资源根节点
//    QTreeWidgetItem* hardwareRoot = nullptr;
//    for (int i = 0; i < taskRoot->childCount(); ++i) {
//        QTreeWidgetItem* child = taskRoot->child(i);
//        if (child && child->text(0) == u8"硬件节点资源") {
//            hardwareRoot = child;
//            break;
//        }
//    }

//    if (!hardwareRoot) {
//        return nodeConfigs;
//    }

//    // 遍历硬件节点资源下的所有节点
//    for (int i = 0; i < hardwareRoot->childCount(); ++i) {
//        QTreeWidgetItem* nodeItem = hardwareRoot->child(i);
//        if (!nodeItem) continue;

//        UI::NodeConfigParams nodeConfig;
//        nodeConfig.nodeName = nodeItem->text(0);
//        nodeConfig.channelCount = nodeItem->childCount();
//        nodeConfig.channels.clear();

//        // 遍历节点下的所有通道
//        for (int j = 0; j < nodeItem->childCount(); ++j) {
//            QTreeWidgetItem* channelItem = nodeItem->child(j);
//            if (!channelItem) continue;

//            UI::ChannelInfo channel;
//            channel.channelId = j + 1;
//            channel.enabled = true;

//            // 从通道项的工具提示中解析IP和端口信息
//            QString tooltip = channelItem->toolTip(0);

//            // 解析IP地址（假设格式包含 "IP地址: *************"）
//            QRegularExpression ipRegex(R"(IP地址:\s*(\d+\.\d+\.\d+\.\d+))");
//            QRegularExpressionMatch ipMatch = ipRegex.match(tooltip);
//            if (ipMatch.hasMatch()) {
//                channel.ipAddress = ipMatch.captured(1);
//            } else {
//                // 默认IP地址
//                channel.ipAddress = QString("192.168.1.%1").arg(100 + i);
//            }

//            // 解析端口（假设格式包含 "端口: 8080"）
//            QRegularExpression portRegex(R"(端口:\s*(\d+))");
//            QRegularExpressionMatch portMatch = portRegex.match(tooltip);
//            if (portMatch.hasMatch()) {
//                channel.port = portMatch.captured(1).toInt();
//            } else {
//                // 默认端口
//                channel.port = 8080 + j;
//            }

//            nodeConfig.channels.append(channel);
//        }

//        nodeConfigs.append(nodeConfig);
//    }

//    return nodeConfigs;
//}

int XLSDataExporter_1_2::addNodeConfigDetailToExcel(QXlsx::Worksheet* worksheet, const UI::NodeConfigParams& nodeConfig, int row, int nodeId) {
    if (!worksheet) return row;

    // 为每个通道创建一行
    for (int i = 0; i < nodeConfig.channels.size(); ++i) {
        const UI::ChannelInfo& channel = nodeConfig.channels[i];
        bool isFirstChannel = (i == 0);
        row = addHardwareChannelDetailToExcel(worksheet, nodeConfig, channel, row, nodeId, isFirstChannel);
    }

    return row;
}

int XLSDataExporter_1_2::addHardwareChannelDetailToExcel(QXlsx::Worksheet* worksheet, const UI::NodeConfigParams& nodeConfig, const UI::ChannelInfo& channel, int row, int nodeId, bool isFirstChannel) {
    if (!worksheet) return row;

    // 创建数据格式
    QXlsx::Format dataFormat;
    dataFormat.setBorderStyle(QXlsx::Format::BorderThin);

    // 创建节点名称格式（浅绿色背景，粗体）
    QXlsx::Format nodeFormat;
    nodeFormat.setBorderStyle(QXlsx::Format::BorderThin);
    nodeFormat.setPatternBackgroundColor(QColor(231, 255, 231));
    nodeFormat.setFontBold(true);

    // 选择格式（第一个通道使用节点格式）
    QXlsx::Format currentFormat = isFirstChannel ? nodeFormat : dataFormat;

    // 🆕 修改：写入硬件节点详细信息（7列，去掉节点类型）
    worksheet->write(row, 1, nodeId, currentFormat);                                                 // 节点ID（每行都显示）
    worksheet->write(row, 2, isFirstChannel ? nodeConfig.nodeName : QString(), currentFormat);       // 节点名称（只在第一行显示）
    worksheet->write(row, 3, isFirstChannel ? nodeConfig.channelCount : QVariant(), currentFormat);  // 通道数量（只在第一行显示）
    worksheet->write(row, 4, channel.channelId, currentFormat);                                      // 通道ID
    worksheet->write(row, 5, channel.ipAddress, currentFormat);                                      // 通道IP地址
    worksheet->write(row, 6, channel.port, currentFormat);                                           // 通道端口
    worksheet->write(row, 7, channel.enabled ? u8"启用" : u8"禁用", currentFormat);                  // 通道状态

    return row + 1;
}

// ============================================================================
// 🆕 新增：工程导入功能实现
// ============================================================================

QString XLSDataExporter_1_2::getImportError() const {
    return importError_;
}

void XLSDataExporter_1_2::clearImportError() {
    importError_.clear();
}

bool XLSDataExporter_1_2::importProject(const QString& filePath) {
    clearImportError();

    if (filePath.isEmpty()) {
        importError_ = QString(u8"文件路径为空");
        return false;
    }

    if (!QFile::exists(filePath)) {
        importError_ = QString(u8"文件不存在: %1").arg(filePath);
        return false;
    }

    try {
        // 1. 打开XLSX文件
        QXlsx::Document doc(filePath);
        if (doc.sheetNames().isEmpty()) {
            importError_ = QString(u8"无法打开Excel文件或文件为空: %1").arg(filePath);
            return false;
        }

        // 2. 验证文件结构
        if (!validateWorksheetStructure(doc)) {
            return false; // 错误信息已在validateWorksheetStructure中设置
        }

        // 3. 按顺序导入各工作表数据
        bool success = true;

        // 导入作动器详细配置
        if (success && doc.selectSheet(QString(u8"作动器详细配置"))) {
            success = importActuatorDetails(doc);
        }

        // 导入传感器详细配置
        // 注释：传感器导入功能已迁移到SensorExcelExtensions_1_2，请使用MainWindow的传感器工程导入功能
        if (success && doc.selectSheet(QString(u8"传感器详细配置"))) {
            qDebug() << "传感器导入功能已迁移到SensorExcelExtensions_1_2，请使用MainWindow的传感器导入功能";
            success = importSensorDetails(doc); // 已注释，改用SensorExcelExtensions_1_2
            success = true; // 暂时设为成功，避免影响其他工作表的导入
        }

        // 导入硬件节点详细配置
        QString hardwareSheetName = QString(u8"硬件节点详细配置");
        qDebug() << QString(u8"🔍 调试：尝试选择硬件节点工作表: %1").arg(hardwareSheetName);
        if (success && doc.selectSheet(hardwareSheetName)) {
            qDebug() << QString(u8"🔍 调试：成功选择硬件节点工作表，开始导入");
            success = importHardwareNodeDetails(doc);
        } else {
            qDebug() << QString(u8"🔍 调试：未找到硬件节点工作表或导入失败");
            // 列出所有可用的工作表
            QStringList sheetNames = doc.sheetNames();
            qDebug() << QString(u8"🔍 调试：Excel文件中的所有工作表: %1").arg(sheetNames.join(", "));
        }

        // 导入控制通道详细配置
        if (success && doc.selectSheet(QString(u8"控制通道详细配置"))) {
            success = importControlChannelDetails(doc);
        }

        if (success) {
            qDebug() << QString(u8"工程导入成功: %1").arg(filePath);
        }

        return success;

    } catch (const std::exception& e) {
        importError_ = QString(u8"导入过程中发生异常: %1").arg(e.what());
        return false;
    } catch (...) {
        importError_ = QString(u8"导入过程中发生未知异常");
        return false;
    }
}

bool XLSDataExporter_1_2::validateWorksheetStructure(QXlsx::Document& doc) {
    // 检查必需的工作表是否存在
    QStringList requiredSheets = {
        QString(u8"作动器详细配置"),
        QString(u8"传感器详细配置"),
        QString(u8"硬件节点详细配置"),
        QString(u8"控制通道详细配置")
    };

    QStringList availableSheets = doc.sheetNames();

//    for (const QString& sheetName : requiredSheets) {
//        if (!availableSheets.contains(sheetName)) {
//            importError_ = QString(u8"缺少必需的工作表: %1").arg(sheetName);
//            return false;
//        }
//    }

    return true;
}

bool XLSDataExporter_1_2::importActuatorDetails(QXlsx::Document& doc) {
    if (!actuatorDataManager_) {
        importError_ = QString(u8"作动器数据管理器未设置");
        return false;
    }

    try {
        // 清空现有作动器数据
        actuatorDataManager_->clearAllActuators();
        actuatorDataManager_->clearAllActuatorGroups();

        // 读取数据范围
        QXlsx::CellRange range = doc.dimension();
        qDebug() << QString(u8"作动器工作表数据范围: 行数=%1, 列数=%2, 起始行=%3, 结束行=%4")
                    .arg(range.rowCount()).arg(range.columnCount()).arg(range.firstRow()).arg(range.lastRow());

        if (range.rowCount() <= 1) {
            // 没有数据行，只有表头
            qDebug() << QString(u8"作动器工作表没有数据行");
            return true;
        }

        // 🔧 修复：读取作动器数据并按组序号组织
        QMap<int, UI::ActuatorGroup_1_2> groupMap;
        int nextActuatorId = 1;

        // 🔍 调试：显示前几行的内容
        qDebug() << QString(u8"=== 作动器工作表前3行内容 ===");
        for (int debugRow = 1; debugRow <= qMin(3, range.lastRow()); ++debugRow) {
            QString rowContent;
            for (int col = 1; col <= qMin(5, range.lastColumn()); ++col) {
                QVariant cellValue = doc.read(debugRow, col);
                rowContent += QString("列%1='%2' ").arg(col).arg(cellValue.toString());
            }
            qDebug() << QString(u8"第%1行: %2").arg(debugRow).arg(rowContent);
        }

        // 🆕 第一遍扫描：建立组ID到组名称的映射
        QMap<int, QString> groupNameMapping = buildGroupNameMapping(doc, 2, 1, 2);

        // 🆕 新增：序号重映射逻辑，确保组序号连续
        QMap<int, int> groupIdMapping; // Excel序号 -> 连续序号的映射
        QSet<QString> processedGroupNames; // 已处理的组名称
        int nextSequentialGroupId = 1;

        for (int row = 2; row <= range.lastRow(); ++row) {
            // 🔧 修复：正确读取作动器数据（对应保存时的16列格式）
            int excelGroupId = doc.read(row, 1).toInt();                    // 第1列：Excel中的组序号
            QString groupName = doc.read(row, 2).toString().trimmed(); // 第2列：作动器组名称

            // 🔧 修复：只检查组ID，不检查组名称
            if (excelGroupId <= 0) {
                qDebug() << QString(u8"跳过无效行 %1: excelGroupId=%2").arg(row).arg(excelGroupId);
                continue; // 跳过无效组ID的行
            }

            // 🆕 使用映射获取组名称
            groupName = getGroupNameFromMapping(groupNameMapping, excelGroupId, groupName);
            if (groupName.isEmpty()) {
                qDebug() << QString(u8"警告：作动器组ID %1 没有对应的组名称，跳过行 %2").arg(excelGroupId).arg(row);
                continue;
            }

            // 🆕 新增：序号重映射 - 确保组序号从1开始连续递增
            int actualGroupId;
            if (!groupIdMapping.contains(excelGroupId)) {
                // 新组：分配连续的组序号
                actualGroupId = nextSequentialGroupId++;
                groupIdMapping[excelGroupId] = actualGroupId;
                qDebug() << QString(u8"🔢 序号映射：Excel组序号 %1 -> 实际组序号 %2 (组名称: %3)")
                            .arg(excelGroupId).arg(actualGroupId).arg(groupName);
            } else {
                // 已存在的组：使用映射的序号
                actualGroupId = groupIdMapping[excelGroupId];
            }

            // 🔧 修复：读取25列数据并使用parseRowToActuatorParams解析
            QStringList rowData;
            for (int col = 1; col <= 25; ++col) {
                QVariant cellValue = doc.read(row, col);
                rowData.append(cellValue.toString().trimmed());
            }

            // 使用parseRowToActuatorParams方法正确解析数据
            UI::ActuatorParams_1_2 actuator = parseRowToActuatorParams(rowData);
            actuator.actuatorId = nextActuatorId++;

            // 🔧 修复：检查组是否已存在（使用重映射后的组序号作为键）
            if (!groupMap.contains(actualGroupId)) {
                // 创建新组
                UI::ActuatorGroup_1_2 newGroup;
                newGroup.groupId = actualGroupId; // 使用重映射后的连续序号
                newGroup.groupName = groupName;
                newGroup.groupType = QString(u8"液压"); // 默认类型
                newGroup.createTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
                newGroup.groupNotes = QString(u8"从Excel导入，原序号: %1").arg(excelGroupId);
                groupMap[actualGroupId] = newGroup;

                qDebug() << QString(u8"✅ 创建新作动器组：ID=%1, 名称='%2', 原Excel序号=%3")
                            .arg(actualGroupId).arg(groupName).arg(excelGroupId);
            }

            // 🆕 新增：详细的作动器信息日志
            qDebug() << QString(u8"📝 处理作动器：行%1, 组ID=%2, 组名称='%3', 序列号='%4', 类型='%5'")
                        .arg(row).arg(actualGroupId).arg(groupName).arg(actuator.params.sn).arg(ActuatorDataManager_1_2::actuatorTypeToString(actuator.type));

            // 🔄 修改：验证作动器序列号在当前组内的唯一性（不再全局检查）
            QString serialNumber = actuator.params.sn;
            bool serialNumberExistsInGroup = false;

            // 只在当前组内检查序列号重复
            if (groupMap.contains(actualGroupId)) {
                for (const auto& existingActuator : groupMap[actualGroupId].actuators) {
                    if (existingActuator.params.sn == serialNumber) {
                        serialNumberExistsInGroup = true;
                        break;
                    }
                }
            }

            if (serialNumberExistsInGroup) {
                qDebug() << QString(u8"⚠️  警告：作动器序列号在组内重复: %1（组ID=%2），将自动生成新序列号").arg(serialNumber).arg(actualGroupId);
                actuator.params.sn = QString("%1_组内重复_%2").arg(serialNumber).arg(QTime::currentTime().toString("hhmmss"));
            }

            // 将作动器添加到组中
            groupMap[actualGroupId].actuators.append(actuator);

            // 🆕 新增：确认作动器添加成功的日志
            qDebug() << QString(u8"✅ 作动器已添加到组：组ID=%1, 组名称='%2', 当前组内作动器数量=%3")
                        .arg(actualGroupId).arg(groupName).arg(groupMap[actualGroupId].actuators.size());
        }

        // 🆕 新增：数据验证和序号连续性检查
        QStringList validationReport;
        validationReport << QString(u8"=== 作动器数据序号验证报告 ===");

        // 验证组序号连续性
        QList<int> groupIds = groupMap.keys();
        std::sort(groupIds.begin(), groupIds.end());

        bool sequenceValid = true;
        for (int i = 0; i < groupIds.size(); ++i) {
            int expectedId = i + 1;
            int actualId = groupIds[i];
            if (actualId != expectedId) {
                validationReport << QString(u8"❌ 组序号不连续：期望 %1，实际 %2").arg(expectedId).arg(actualId);
                sequenceValid = false;
            }
        }

        if (sequenceValid) {
            validationReport << QString(u8"✅ 组序号连续性检查通过：1-%1").arg(groupIds.size());
        }

        // 统计信息
        int totalActuators = 0;
        for (auto it = groupMap.begin(); it != groupMap.end(); ++it) {
            totalActuators += it.value().actuators.size();
            validationReport << QString(u8"📊 组%1 (%2)：%3个作动器")
                               .arg(it.value().groupId)
                               .arg(it.value().groupName)
                               .arg(it.value().actuators.size());
        }

        validationReport << QString(u8"📈 总计：%1个组，%2个作动器").arg(groupMap.size()).arg(totalActuators);
        validationReport << QString(u8"🔢 序号映射：%1个映射关系").arg(groupIdMapping.size());

        // 输出验证报告
        for (const QString& line : validationReport) {
            qDebug() << line;
        }

        // 🆕 新增：重新分配组内ID，确保每个组内的设备ID从1开始按序排列
        for (auto it = groupMap.begin(); it != groupMap.end(); ++it) {
            UI::ActuatorGroup_1_2 group = it.value();

            // 重新分配组内作动器ID，从1开始按序排列
            for (int i = 0; i < group.actuators.size(); ++i) {
                group.actuators[i].actuatorId = i + 1; // 组内ID从1开始
            }

            if (!actuatorDataManager_->saveActuatorGroup(group)) {
                QString detailedError = actuatorDataManager_->getLastError();
                importError_ = QString(u8"保存作动器组失败: %1 - 详细错误: %2").arg(group.groupName).arg(detailedError);
                qDebug() << QString(u8"❌ 保存作动器组失败详情：");
                qDebug() << QString(u8"   组名称: %1").arg(group.groupName);
                qDebug() << QString(u8"   组ID: %1").arg(group.groupId);
                qDebug() << QString(u8"   作动器数量: %1").arg(group.actuators.size());
                qDebug() << QString(u8"   错误信息: %1").arg(detailedError);
                return false;
            }

            qDebug() << QString(u8"✅ 作动器组 %1 (组ID: %2) 重新分配组内ID完成，设备数量: %3")
                        .arg(group.groupName).arg(group.groupId).arg(group.actuators.size());
        }

        qDebug() << QString(u8"✅ 作动器详细配置导入完成，共导入 %1 个组，序号验证：%2")
                    .arg(groupMap.size()).arg(sequenceValid ? u8"通过" : u8"有问题");
        return true;

    } catch (const std::exception& e) {
        importError_ = QString(u8"导入作动器详细配置时发生异常: %1").arg(e.what());
        return false;
    }
}

// ============================================================================
// 🔄 重新实现：importSensorDetails方法 - 严格按照UI::SensorParams_1_2字段对应
// ============================================================================
// 注释说明：按照用户要求，严格按照17列格式和UI::SensorParams_1_2字段对应
// 注释掉序列号自动转换和智能数据验证功能
// Excel格式对应：A到Q列（17列）与传感器写Excel时的格式、顺序对应
// ============================================================================
bool XLSDataExporter_1_2::importSensorDetails(QXlsx::Document& doc) {
    if (!sensorDataManager_) {
        importError_ = QString(u8"传感器数据管理器未设置");
        return false;
    }

    try {
        // 清空现有传感器数据
        sensorDataManager_->clearAllSensors();
        sensorDataManager_->clearAllSensorGroups();

        // 检查工作表是否存在
        QXlsx::CellRange range = doc.dimension();
        if (!range.isValid() || range.rowCount() <= 2) {
            qDebug() << QString(u8"⚠️ 传感器工作表为空或只有表头，跳过导入");
            return true;
        }

        qDebug() << QString(u8"📊 按照17列格式导入传感器数据，Excel范围：行%1-%2，列%3-%4")
                    .arg(range.firstRow()).arg(range.lastRow())
                    .arg(range.firstColumn()).arg(range.lastColumn());

        // 🎯 严格按照17列标准结构解析传感器数据
        QMap<int, UI::SensorGroup_1_2> groupMap;
        QMap<int, QString> groupNameMap;
        int nextSensorId = 1;

        // 从第3行开始读取数据（跳过标题行第1行和表头行第2行）
        for (int row = 3; row <= range.lastRow(); ++row) {
            // A列：组序号 - 对应Excel第1列
            QVariant groupIdVariant = doc.read(row, 1);
            if (!groupIdVariant.isValid() || groupIdVariant.toString().isEmpty()) {
                continue;
            }
            
            int groupId = groupIdVariant.toInt();
            if (groupId <= 0) continue;

            // B列：传感器组名称 - 对应Excel第2列（可能为空，使用缓存）
            QString groupName = doc.read(row, 2).toString().trimmed();
            if (!groupName.isEmpty()) {
                groupNameMap[groupId] = groupName; // 缓存组名称
            } else if (groupNameMap.contains(groupId)) {
                groupName = groupNameMap[groupId]; // 使用缓存的组名称
            } else {
                continue; // 没有组名称，跳过该行
            }

            // 创建传感器参数 - 严格按照UI::SensorParams_1_2字段对应
            UI::SensorParams_1_2 sensor;
            
            // C列：传感器ID - 对应sensor.sensorId
            sensor.sensorId = doc.read(row, 3).toInt();
            if (sensor.sensorId <= 0) sensor.sensorId = nextSensorId++;
            
            // D列：传感器序列号 - 对应sensor.params_sn
            sensor.params_sn = doc.read(row, 4).toString().trimmed();
            
            // E列：传感器型号 - 对应sensor.params_model
            sensor.params_model = doc.read(row, 5).toString().trimmed();
            
            // F列：零点偏移 - 对应sensor.zero_offset
            sensor.zero_offset = doc.read(row, 6).toDouble();
            
            // G列：启用状态 - 对应sensor.enable
            QString enableText = doc.read(row, 7).toString().trimmed();
            sensor.enable = (enableText == u8"是" || enableText.toLower() == "true" || enableText == "1");
            
            // H列：线性系数K - 对应sensor.params_k
            sensor.params_k = doc.read(row, 8).toDouble();
            
            // I列：线性系数B - 对应sensor.params_b
            sensor.params_b = doc.read(row, 9).toDouble();
            
            // J列：精度 - 对应sensor.params_precision
            sensor.params_precision = doc.read(row, 10).toDouble();
            
            // K列：极性 - 对应sensor.params_polarity（支持新格式数字和旧格式字符串）
            QVariant polarityValue = doc.read(row, 11);
            if (polarityValue.type() == QVariant::Int) {
                sensor.params_polarity = polarityValue.toInt();
            } else {
                // 处理旧格式字符串
                QString polarityStr = polarityValue.toString().trimmed();
                if (polarityStr == "1" || polarityStr.contains(u8"正极性")) {
                    sensor.params_polarity = 1;
                } else if (polarityStr == "-1" || polarityStr.contains(u8"负极性")) {
                    sensor.params_polarity = -1;
                } else if (polarityStr == "9" || polarityStr.contains(u8"双极性")) {
                    sensor.params_polarity = 9;
                } else if (polarityStr == "0" || polarityStr.contains(u8"未知")) {
                    sensor.params_polarity = 0;
                } else {
                    sensor.params_polarity = 1; // 默认正极性
                }
            }
            
            // L列：测量单位类型 - 对应sensor.meas_unit
            sensor.meas_unit = doc.read(row, 12).toInt();
            
            // M列：测量范围最小值 - 对应sensor.meas_range_min
            sensor.meas_range_min = doc.read(row, 13).toDouble();
            
            // N列：测量范围最大值 - 对应sensor.meas_range_max
            sensor.meas_range_max = doc.read(row, 14).toDouble();
            
            // O列：输出信号单位类型 - 对应sensor.output_signal_unit
            sensor.output_signal_unit = doc.read(row, 15).toInt();
            
            // P列：输出信号范围最小值 - 对应sensor.output_signal_range_min
            sensor.output_signal_range_min = doc.read(row, 16).toDouble();
            
            // Q列：输出信号范围最大值 - 对应sensor.output_signal_range_max
            sensor.output_signal_range_max = doc.read(row, 17).toDouble();

            // 注释掉自动转换和验证功能（按用户要求）
            /*
            // 🔧 智能默认值设置
            if (sensor.params_k <= 0.0) sensor.params_k = 1.0;
            if (sensor.params_precision <= 0.0) sensor.params_precision = 0.1;
            if (sensor.params_polarity == 0) sensor.params_polarity = -1;
            if (sensor.meas_unit <= 0) sensor.meas_unit = 1;
            if (sensor.output_signal_unit <= 0) sensor.output_signal_unit = 1;

            // 范围验证
            if (sensor.meas_range_max <= sensor.meas_range_min) {
                sensor.meas_range_max = sensor.meas_range_min + 100.0;
            }
            if (sensor.output_signal_range_max <= sensor.output_signal_range_min) {
                sensor.output_signal_range_max = sensor.output_signal_range_min + 10.0;
            }

            // 🆕 验证传感器序列号格式并自动转换
            if (!sensor.params_sn.isEmpty()) {
                bool isNumericOnly = true;
                for (const QChar& ch : sensor.params_sn) {
                    if (!ch.isDigit()) {
                        isNumericOnly = false;
                        break;
                    }
                }
                
                if (isNumericOnly) {
                    QString originalSn = sensor.params_sn;
                    sensor.params_sn = QString("SEN%1").arg(sensor.params_sn.rightJustified(3, '0'));
                    qWarning() << QString(u8"⚠️ 自动转换传感器序列号：'%1' → '%2'")
                                  .arg(originalSn).arg(sensor.params_sn);
                }
            } else {
                sensor.params_sn = QString("SEN%1").arg(QString::number(sensor.sensorId).rightJustified(3, '0'));
            }
            */

            qDebug() << QString(u8"📋 解析传感器 - 行%1: 组%2(%3), ID=%4, 序列号='%5', 型号='%6'")
                        .arg(row).arg(groupId).arg(groupName)
                        .arg(sensor.sensorId).arg(sensor.params_sn).arg(sensor.params_model);

            // 创建或获取传感器组
            if (!groupMap.contains(groupId)) {
                UI::SensorGroup_1_2 newGroup;
                newGroup.groupId = groupId;
                newGroup.groupName = groupName;
                newGroup.groupType = QString(u8"载荷");
                newGroup.createTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
                newGroup.groupNotes = QString(u8"Excel导入");
                groupMap[groupId] = newGroup;
                
                qDebug() << QString(u8"✅ 创建传感器组：ID=%1, 名称='%2'").arg(groupId).arg(groupName);
            }

            // 注释掉序列号唯一性检查（按用户要求）
            /*
            // 检查组内序列号唯一性
            bool serialNumberExists = false;
            for (const auto& existingSensor : groupMap[groupId].sensors) {
                if (existingSensor.params_sn == sensor.params_sn) {
                    serialNumberExists = true;
                    break;
                }
            }
            
            if (serialNumberExists) {
                QString originalSn = sensor.params_sn;
                sensor.params_sn += QString("_%1").arg(QTime::currentTime().toString("hhmmss"));
                qWarning() << QString(u8"⚠️ 序列号重复，自动重命名：'%1' → '%2'")
                              .arg(originalSn).arg(sensor.params_sn);
            }
            */

            groupMap[groupId].sensors.append(sensor);
        }

        // 保存传感器组到数据管理器
        int totalSensors = 0;
        qDebug() << QString(u8"🔄 开始重新分配传感器组内ID...");
        qDebug() << QString(u8"📊 待处理组数量: %1").arg(groupMap.size());
        
        for (auto it = groupMap.begin(); it != groupMap.end(); ++it) {
            UI::SensorGroup_1_2& group = it.value();
            
            qDebug() << QString(u8"📋 处理传感器组: ID=%1, 名称='%2', 传感器数量=%3")
                        .arg(group.groupId).arg(group.groupName).arg(group.sensors.size());
            
            // 🔍 显示重新分配前的传感器ID状态
            for (int i = 0; i < group.sensors.size(); ++i) {
                qDebug() << QString(u8"  📄 传感器[%1]: 序列号=%2, 旧ID=%3")
                            .arg(i).arg(group.sensors[i].params_sn).arg(group.sensors[i].sensorId);
            }
            
            // 重新分配组内传感器ID（从1开始）
            for (int i = 0; i < group.sensors.size(); ++i) {
                int oldId = group.sensors[i].sensorId;
                group.sensors[i].sensorId = i + 1;
                qDebug() << QString(u8"  ✅ 传感器[%1]: %2 → ID从%3改为%4")
                            .arg(i).arg(group.sensors[i].params_sn).arg(oldId).arg(i + 1);
            }
            
            if (!sensorDataManager_->saveSensorGroup(group)) {
                importError_ = QString(u8"保存传感器组失败: %1").arg(group.groupName);
                qDebug() << QString(u8"❌ 保存传感器组失败: %1").arg(group.groupName);
                qDebug() << QString(u8"❌ 错误详情: %1").arg(sensorDataManager_->getLastError());
                return false;
            }
            
            totalSensors += group.sensors.size();
            qDebug() << QString(u8"✅ 保存传感器组：ID=%1, 名称='%2', 传感器数量=%3")
                        .arg(group.groupId).arg(group.groupName).arg(group.sensors.size());
        }
        
        qDebug() << QString(u8"🎉 所有传感器组的组内ID重新分配完成！");

        qDebug() << QString(u8"🎉 传感器数据导入完成：%1个组，共%2个传感器（严格按照17列格式）")
                    .arg(groupMap.size()).arg(totalSensors);
        return true;

    } catch (const std::exception& e) {
        importError_ = QString(u8"导入传感器详细配置时发生异常: %1").arg(e.what());
        qCritical() << importError_;
        return false;
    } catch (...) {
        importError_ = QString(u8"导入传感器详细配置时发生未知异常");
        qCritical() << importError_;
        return false;
    }
}



bool XLSDataExporter_1_2::importHardwareNodeDetails(QXlsx::Document& doc) {
    // 🆕 修改：参照传感器导入流程，检查并使用硬件节点数据管理器
    if (!hardwareNodeResDataManager_) {
        importError_ = QString(u8"硬件节点数据管理器未设置");
        return false;
    }

    try {
        // 🆕 修改：清空数据管理器中的现有数据（参照传感器流程）
        hardwareNodeResDataManager_->clearAllData();

        // 同时清空内部配置（保持向后兼容）
        hardwareNodeConfigs_.clear();

        // 读取数据范围
        QXlsx::CellRange range = doc.dimension();
        if (range.rowCount() <= 1) {
            // 没有数据行，只有表头
            return true;
        }

        // 🆕 新增：自动检测Excel格式（7列新格式 vs 8列旧格式）
        int columnCount = range.columnCount();
        bool isOldFormat = (columnCount >= 8); // 8列或更多为旧格式
        bool isNewFormat = (columnCount == 7);  // 7列为新格式

        qDebug() << QString(u8"🔍 调试：检测到Excel格式 - 列数: %1, 旧格式: %2, 新格式: %3")
                    .arg(columnCount).arg(isOldFormat).arg(isNewFormat);

        // 读取硬件节点数据
        QMap<QString, UI::NodeConfigParams> nodeMap;

        for (int row = 4; row <= range.lastRow(); ++row) {
            // 🆕 修改：根据格式自动选择正确的列索引
            QString nodeName = doc.read(row, 2).toString().trimmed();
            if (nodeName.isEmpty()) {
                continue; // 跳过空行
            }

            // 检查节点是否已存在
            if (!nodeMap.contains(nodeName)) {
                // 创建新节点
                UI::NodeConfigParams newNode;
                newNode.nodeName = nodeName;

                // 根据格式读取通道数量
                if (isOldFormat) {
                    newNode.channelCount = doc.read(row, 4).toInt(); // 旧格式：第4列
                } else {
                    newNode.channelCount = doc.read(row, 3).toInt(); // 新格式：第3列
                }

                nodeMap[nodeName] = newNode;
                qDebug() << QString(u8"🔍 调试：创建新节点: %1，通道数: %2").arg(nodeName).arg(newNode.channelCount);
            }

            // 读取通道信息
            UI::ChannelInfo channel;

            // 根据格式读取通道数据
            if (isOldFormat) {
                // 旧格式（8列）：跳过第3列的节点类型
                channel.channelId = doc.read(row, 5).toInt();
                channel.ipAddress = doc.read(row, 6).toString().trimmed();
                channel.port = doc.read(row, 7).toInt();
                QString enabledStr = doc.read(row, 8).toString().trimmed();
                channel.enabled = (enabledStr == QString(u8"启用"));
            } else {
                // 新格式（7列）：无节点类型列
                channel.channelId = doc.read(row, 4).toInt();
                channel.ipAddress = doc.read(row, 5).toString().trimmed();
                channel.port = doc.read(row, 6).toInt();
                QString enabledStr = doc.read(row, 7).toString().trimmed();
                channel.enabled = (enabledStr == QString(u8"启用"));
            }

            // 🆕 新增：检查通道是否已存在，避免重复添加
            bool channelExists = false;
            for (const auto& existingChannel : nodeMap[nodeName].channels) {
                if (existingChannel.channelId == channel.channelId) {
                    channelExists = true;
                    qDebug() << QString(u8"🔍 调试：跳过重复通道: %1 - CH%2").arg(nodeName).arg(channel.channelId);
                    break;
                }
            }

            if (!channelExists) {
                // 将通道添加到节点
                nodeMap[nodeName].channels.append(channel);
                qDebug() << QString(u8"🔍 调试：添加通道: %1 - CH%2 (IP: %3)").arg(nodeName).arg(channel.channelId).arg(channel.ipAddress);
            }
        }

        // 🆕 修改：将所有节点保存到数据管理器（参照传感器流程）
        int successCount = 0;
        int skipCount = 0;

        for (auto it = nodeMap.begin(); it != nodeMap.end(); ++it) {
            qDebug() << QString(u8"🔍 调试：正在保存硬件节点到数据管理器: %1，通道数: %2").arg(it.value().nodeName).arg(it.value().channels.size());

            // 🆕 新增：跳过无效的节点记录，而不是整个导入失败
            if (it.value().nodeName.isEmpty() ||
                it.value().nodeName == QString(u8"节点名称") ||
                it.value().channels.isEmpty()) {
                qDebug() << QString(u8"🔍 调试：跳过无效节点: %1 (通道数: %2)").arg(it.value().nodeName).arg(it.value().channels.size());
                skipCount++;
                continue;
            }

            // 保存到数据管理器
            if (!hardwareNodeResDataManager_->addHardwareNodeConfig(it.value())) {
                qDebug() << QString(u8"🔍 调试：保存失败: %1，但继续处理其他节点").arg(it.value().nodeName);
                skipCount++;
                continue; // 🆕 修改：继续处理其他节点，而不是整个失败
            }

            qDebug() << QString(u8"🔍 调试：保存成功: %1").arg(it.value().nodeName);
            successCount++;

            // 同时保存到内部配置（保持向后兼容）
            hardwareNodeConfigs_.append(it.value());
        }

        qDebug() << QString(u8"🔍 调试：硬件节点导入统计 - 成功: %1个，跳过: %2个").arg(successCount).arg(skipCount);

        qDebug() << QString(u8"硬件节点详细配置导入完成，共导入 %1 个节点").arg(nodeMap.size());
        return true;

    } catch (const std::exception& e) {
        importError_ = QString(u8"导入硬件节点详细配置时发生异常: %1").arg(e.what());
        return false;
    }
}

bool XLSDataExporter_1_2::importControlChannelDetails(QXlsx::Document& doc) {
    if (!ctrlChanDataManager_) {
        importError_ = QString(u8"控制通道数据管理器未设置");
        return false;
    }

    try {
        // 清空现有控制通道数据
        ctrlChanDataManager_->clearAllData();

        // 读取数据范围
        QXlsx::CellRange range = doc.dimension();
        qDebug() << QString(u8"🔍 Excel数据范围: 行数=%1, 列数=%2, 最后行=%3, 最后列=%4")
                    .arg(range.rowCount()).arg(range.columnCount())
                    .arg(range.lastRow()).arg(range.lastColumn());

        if (range.rowCount() <= 5) {
            // 没有数据行，只有表头（前5行是表头信息）
            qDebug() << QString(u8"⚠️ 控制通道数据表没有有效数据行");
            return true;
        }

        // 创建控制通道组
        UI::ControlChannelGroup channelGroup;
        channelGroup.groupId = 1;
        channelGroup.groupName = QString(u8"导入控制通道组").toStdString();  // 🆕 修复：避免与默认组重名
        channelGroup.groupType = QString(u8"控制通道").toStdString();  // 🆕 修复：设置组类型
        channelGroup.createTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss").toStdString();
        channelGroup.groupNotes = QString(u8"从Excel文件导入的控制通道组").toStdString();

        int channelIndex = 1;
        // 🔄 修复：跳过表头行，从第6行开始读取数据（对应导出时的表头结构）
        int dataStartRow = 6; // 表头信息占用前5行

        // 🆕 扩展：支持扩展格式（包含所有扩展字段）
        qDebug() << QString(u8"🔍 控制通道Excel格式 - 列数: %1, 使用扩展格式")
                    .arg(range.columnCount());

        for (int row = dataStartRow; row <= range.lastRow(); ++row) {
            // 🆕 扩展：支持扩展格式（包含所有扩展字段）
            // 列1: 通道序号, 列2: 通道ID, 列3: 通道名称, 列4: 下位机ID, 列5: 站点ID,
            // 列6: 使能状态, 列7: 控制模式, 列8: 硬件关联, 列9: 载荷1传感器,
            // 列10: 载荷2传感器, 列11: 位置传感器, 列12: 控制作动器,
            // 列13: 控制作动器极性, 列14: 载荷1传感器极性, 列15: 载荷2传感器极性,
            // 列16: 位置传感器极性, 列17: 备注
            QString channelName = doc.read(row, 3).toString().trimmed(); // 第3列：通道名称
            if (channelName.isEmpty()) {
                continue; // 跳过空行
            }

            // 创建控制通道
            UI::ControlChannelParams channel;
            
            // 🔧 修复：确保channelId与Excel中的行号正确对应
            QVariant channelIdValue = doc.read(row, 2);
            QString channelIdStr;
            
            // 计算期望的通道序号（第6行=CH1，第7行=CH2，以此类推）
            int expectedChannelNum = row - dataStartRow + 1;
            
            if (channelIdValue.type() == QVariant::Int || channelIdValue.type() == QVariant::Double) {
                // 数字格式：转换为"CH{数字}"格式
                int numericId = channelIdValue.toInt();
                channelIdStr = QString("CH%1").arg(numericId);
                
                // 🔧 验证：检查Excel中的channelId是否与行号匹配
                if (numericId != expectedChannelNum) {
                    qDebug() << QString(u8"⚠️ 警告：Excel第%1行的通道ID(%2)与期望的通道序号(%3)不匹配")
                                .arg(row).arg(numericId).arg(expectedChannelNum);
                }
            } else {
                // 字符串格式：直接使用或生成
                channelIdStr = channelIdValue.toString().trimmed();
                if (channelIdStr.isEmpty()) {
                    // 如果为空，使用期望的通道序号生成
                    channelIdStr = QString("CH%1").arg(expectedChannelNum);
                    qDebug() << QString(u8"🔧 自动生成通道ID：第%1行 -> %2").arg(row).arg(channelIdStr);
                }
            }
            
            channel.channelId = channelIdStr.toStdString();                                    // 第2列：通道ID
            channel.channelName = channelName.toStdString();
            
            // 🔧 调试：记录导入的通道信息
            qDebug() << QString(u8"📊 导入通道：行号=%1, ID=%2, 名称=%3")
                        .arg(row).arg(channelIdStr).arg(channelName);                                   // 第3列：通道名称
            
            // 🆕 新增：扩展配置字段
            channel.lc_id = doc.read(row, 4).toInt();                                          // 第4列：下位机ID
            channel.station_id = doc.read(row, 5).toInt();                                     // 第5列：站点ID
            channel.enable = (doc.read(row, 6).toString().trimmed() == u8"是");                // 第6列：使能状态
            channel.control_mode = doc.read(row, 7).toInt();                                   // 第7列：控制模式
            
            // 关联配置
            channel.hardwareAssociation = doc.read(row, 8).toString().trimmed().toStdString(); // 第8列：硬件关联
            channel.load1Sensor = doc.read(row, 9).toString().trimmed().toStdString();         // 第9列：载荷1传感器
            channel.load2Sensor = doc.read(row, 10).toString().trimmed().toStdString();        // 第10列：载荷2传感器
            channel.positionSensor = doc.read(row, 11).toString().trimmed().toStdString();     // 第11列：位置传感器
            channel.controlActuator = doc.read(row, 12).toString().trimmed().toStdString();    // 第12列：控制作动器
            
            // 🆕 新增：极性参数（直接读取数字值）
            channel.servo_control_polarity = doc.read(row, 13).toInt();   // 第13列：控制作动器极性
            channel.payload_sensor1_polarity = doc.read(row, 14).toInt(); // 第14列：载荷1传感器极性
            channel.payload_sensor2_polarity = doc.read(row, 15).toInt(); // 第15列：载荷2传感器极性
            channel.position_sensor_polarity = doc.read(row, 16).toInt(); // 第16列：位置传感器极性
            
            channel.notes = doc.read(row, 17).toString().trimmed().toStdString();              // 第17列：备注

            // 将通道添加到组中
            channelGroup.channels.push_back(channel);
            channelIndex++;

            qDebug() << QString(u8"🔍 读取控制通道[行%1]: %2 (ID: %3)")
                        .arg(row)
                        .arg(QString::fromStdString(channel.channelName))
                        .arg(QString::fromStdString(channel.channelId));
            qDebug() << QString(u8"   硬件关联: %1, 载荷1: %2, 载荷2: %3")
                        .arg(QString::fromStdString(channel.hardwareAssociation))
                        .arg(QString::fromStdString(channel.load1Sensor))
                        .arg(QString::fromStdString(channel.load2Sensor));
            qDebug() << QString(u8"   位置传感器: %1, 控制作动器: %2, 备注: %3")
                        .arg(QString::fromStdString(channel.positionSensor))
                        .arg(QString::fromStdString(channel.controlActuator))
                        .arg(QString::fromStdString(channel.notes));
        }

        // 保存控制通道组
        if (!channelGroup.channels.empty()) {
            qDebug() << QString(u8"准备保存控制通道组: %1 个通道")
                        .arg(channelGroup.channels.size());
            qDebug() << QString(u8"组信息 - ID: %1, 名称: %2, 类型: %3")
                        .arg(channelGroup.groupId)
                        .arg(QString::fromStdString(channelGroup.groupName))
                        .arg(QString::fromStdString(channelGroup.groupType));

            if (!ctrlChanDataManager_->createControlChannelGroup(channelGroup)) {
                importError_ = QString(u8"保存控制通道组失败");
                qDebug() << QString(u8"控制通道组保存失败！");
                return false;
            }
            qDebug() << QString(u8"控制通道组保存成功");
        } else {
            qDebug() << QString(u8"没有有效的控制通道数据");
        }

        qDebug() << QString(u8"控制通道详细配置导入完成，共导入 %1 个通道").arg(channelGroup.channels.size());
        return true;

    } catch (const std::exception& e) {
        importError_ = QString(u8"导入控制通道详细配置时发生异常: %1").arg(e.what());
        return false;
    }
}

// ============================================================================
// 🆕 新增：通用的组名称映射构建和处理函数
// ============================================================================

QMap<int, QString> XLSDataExporter_1_2::buildGroupNameMapping(QXlsx::Document& doc, int startRow, int groupIdCol, int groupNameCol) {
    QMap<int, QString> mapping;
    QXlsx::CellRange range = doc.dimension();

    qDebug() << QString(u8"🔍 构建组名称映射：起始行=%1, 组ID列=%2, 组名称列=%3").arg(startRow).arg(groupIdCol).arg(groupNameCol);

    for (int row = startRow; row <= range.lastRow(); ++row) {
        int groupId = doc.read(row, groupIdCol).toInt();
        QString groupName = doc.read(row, groupNameCol).toString().trimmed();

        if (groupId > 0 && !groupName.isEmpty()) {
            mapping[groupId] = groupName;
            qDebug() << QString(u8"📝 映射记录：组ID=%1 -> 组名称='%2'").arg(groupId).arg(groupName);
        }
    }

    qDebug() << QString(u8"✅ 组名称映射构建完成：共%1个映射关系").arg(mapping.size());
    return mapping;
}

QString XLSDataExporter_1_2::getGroupNameFromMapping(const QMap<int, QString>& mapping, int groupId, const QString& currentGroupName) {
    if (!currentGroupName.isEmpty()) {
        return currentGroupName; // 如果当前行有组名称，直接使用
    }

    QString mappedName = mapping.value(groupId, "");
    if (!mappedName.isEmpty()) {
        qDebug() << QString(u8"🔄 使用映射组名称：组ID=%1 -> 组名称='%2'").arg(groupId).arg(mappedName);
    }

    return mappedName; // 从映射中获取
}


