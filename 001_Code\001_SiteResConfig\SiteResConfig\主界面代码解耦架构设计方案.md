﻿#  **主界面代码解耦架构设计方案**

##  **项目现状分析**

### **当前问题**
- **主界面文件**: MainWindow_Qt_Simple.cpp (10,903行)
- **方法数量**: 约100+个方法
- **功能模块**: 15+个不同功能域
- **依赖关系**: 高度耦合，难以维护

### **现有格式支持**
-  **Excel格式**: 支持导入导出 (.xlsx)
-  **JSON格式**: 支持导入导出 (.json)

##  **解耦架构设计**

### **1. 分层架构设计**

`

                    Presentation Layer                       
               
   MainWindow    TreeManager   MenuManager            
   (简化版)                                            
               

─
                    Business Logic Layer                     
    ┌           
  ProjectMgr    DeviceMgr     ImportExport            
                              Manager                 
    ─           
─
─
                      Data Layer                             
               
  SensorMgr     ActuatorMgr   ConfigMgr               
                                                      
               
─
`

##  **模块化分解方案**

### **A. 项目管理模块 (ProjectManager)**

`cpp
//  include/ProjectManager.h
class ProjectManager : public QObject {
    Q_OBJECT
public:
    // 项目生命周期管理
    bool createNewProject(const QString& projectName);
    bool openProject(const QString& filePath);
    bool saveProject();
    bool saveAsProject(const QString& filePath);
    void closeProject();
    
    // 项目状态管理
    bool hasActiveProject() const;
    QString getCurrentProjectPath() const;
    QString getCurrentProjectName() const;
    
    // 数据保存检查
    bool promptSaveIfNeeded();
    bool hasUnsavedChanges() const;
    
signals:
    void projectOpened(const QString& path, const QString& name);
    void projectClosed();
    void projectSaved();
    void projectError(const QString& error);
};
`

### **B. 设备管理模块 (DeviceManager)**

`cpp
//  include/DeviceManager.h
class DeviceManager : public QObject {
    Q_OBJECT
public:
    // 传感器管理
    bool createSensorGroup(const QString& groupName);
    bool createSensorDevice(int groupId, const UI::SensorParams_1_2& params);
    bool editSensorDevice(const QString& serialNumber, const UI::SensorParams_1_2& params);
    bool deleteSensorDevice(const QString& serialNumber);
    
    // 作动器管理
    bool createActuatorGroup(const QString& groupName);
    bool createActuatorDevice(int groupId, const UI::ActuatorParams_1_2& params);
    bool editActuatorDevice(const QString& serialNumber, const UI::ActuatorParams_1_2& params);
    bool deleteActuatorDevice(const QString& serialNumber);
    
    // 硬件节点管理
    bool createHardwareNode(const UI::CreateHardwareNodeParams& params);
    bool editHardwareNode(const QString& nodeName, const UI::CreateHardwareNodeParams& params);
    bool deleteHardwareNode(const QString& nodeName);
    
    // 数据管理器访问
    SensorDataManager_1_2* getSensorDataManager() const;
    ActuatorDataManager_1_2* getActuatorDataManager() const;
    CtrlChanDataManager_1_2* getCtrlChanDataManager() const;
    HardwareNodeResDataManager_1_2* getHardwareNodeResDataManager() const;
    
    // 数据清理
    void clearAllData();
    void clearSensorData();
    void clearActuatorData();
    void clearHardwareNodeData();
    
signals:
    void deviceCreated(const QString& type, const QString& serialNumber);
    void deviceEdited(const QString& type, const QString& serialNumber);
    void deviceDeleted(const QString& type, const QString& serialNumber);
    void deviceError(const QString& error);
};
`

### **C. 导入导出管理模块 (ImportExportManager)**

`cpp
//  include/ImportExportManager.h
class ImportExportManager : public QObject {
    Q_OBJECT
    
public:
    // 构造函数
    explicit ImportExportManager(QObject* parent = nullptr);
    ~ImportExportManager();
    
    // 数据管理器设置
    void setActuatorDataManager(ActuatorDataManager_1_2* manager);
    void setSensorDataManager(SensorDataManager_1_2* manager);
    void setCtrlChanDataManager(CtrlChanDataManager_1_2* manager);
    void setHardwareNodeResDataManager(HardwareNodeResDataManager_1_2* manager);
    
    // 项目级导入导出 (保留现有格式)
    bool importProject(const QString& filePath, QProgressDialog* progressDialog = nullptr);
    bool exportProject(const QString& filePath, QProgressDialog* progressDialog = nullptr);
    
    // Excel格式导入导出 (保留现有格式)
    bool importProjectFromExcel(const QString& filePath);
    bool exportProjectToExcel(const QString& filePath);
    
    // JSON格式导入导出 (保留现有格式)
    bool importProjectFromJSON(const QString& filePath);
    bool exportProjectToJSON(const QString& filePath);
    bool exportChannelConfigToJSON(const QString& filePath);
    
    // 传感器专用导入导出
    bool importSensorProject(const QString& filePath);
    bool exportSensorProject(const QString& filePath);
    bool createSensorTemplate(const QString& filePath);
    
    // 作动器专用导入导出
    bool importActuatorProject(const QString& filePath);
    bool exportActuatorProject(const QString& filePath);
    
    // 硬件节点专用导入导出
    bool importHardwareNodeProject(const QString& filePath);
    bool exportHardwareNodeProject(const QString& filePath);
    
    // 控制通道专用导入导出
    bool importControlChannelProject(const QString& filePath);
    bool exportControlChannelProject(const QString& filePath);
    
    // 数据验证
    bool validateImportedData();
    QStringList getValidationErrors() const;
    
    // 错误处理
    QString getLastError() const;
    void clearError();
    
signals:
    void importStarted(const QString& filePath);
    void importProgress(int percentage, const QString& message);
    void importCompleted(const QString& filePath, bool success);
    void exportStarted(const QString& filePath);
    void exportProgress(int percentage, const QString& message);
    void exportCompleted(const QString& filePath, bool success);
    void dataValidationCompleted(bool valid, const QStringList& errors);
    void errorOccurred(const QString& error);
    
private:
    // 内部组件 (保留现有实现)
    std::unique_ptr<XLSDataExporter_1_2> xlsExporter_;
    std::unique_ptr<JSONDataExporter_1_2> jsonExporter_;
    std::unique_ptr<SensorExcelExtensions_1_2> sensorExcelExtensions_;
    
    // 数据管理器引用
    ActuatorDataManager_1_2* actuatorDataManager_;
    SensorDataManager_1_2* sensorDataManager_;
    CtrlChanDataManager_1_2* ctrlChanDataManager_;
    HardwareNodeResDataManager_1_2* hardwareNodeResDataManager_;
    
    // 错误处理
    QString lastError_;
    QStringList validationErrors_;
    
    // 内部方法
    bool validateFile(const QString& filePath, const QString& expectedExtension);
    bool synchronizeDataManagers();
    void setupProgressDialog(QProgressDialog* dialog, const QString& title);
    QString getFileExtension(const QString& filePath) const;
};
`

##  **代码行数对比**

| 模块 | 重构前 | 重构后 | 减少比例 | 功能说明 |
|------|--------|--------|----------|----------|
| **主界面** | 10,903行 | 800行 | 93%  | 简化为主界面协调器 |
| **项目管理** | 分散 | 800行 | 集中化 | 项目生命周期管理 |
| **设备管理** | 分散 | 1,200行 | 集中化 | 传感器、作动器、硬件节点管理 |
| **导入导出管理** | 分散 | 1,500行 | 集中化 | **Excel/JSON格式统一管理** |
| **树形控件** | 分散 | 1,500行 | 集中化 | 树形控件操作和显示 |
| **菜单管理** | 分散 | 400行 | 集中化 | 菜单和工具栏管理 |
| **日志管理** | 分散 | 300行 | 集中化 | 日志记录和管理 |

##  **格式支持保留**

### **Excel格式支持**
-  **XLSDataExporter_1_2**: 通用Excel导入导出器
-  **SensorExcelExtensions_1_2**: 传感器专用Excel扩展
-  **17列标准格式**: 传感器参数完整支持
-  **多工作表支持**: 作动器、传感器、硬件节点、控制通道

### **JSON格式支持**
-  **JSONDataExporter_1_2**: JSON导入导出器
-  **通道配置导出**: channel_config.json格式
-  **完整项目导出**: 包含所有设备数据
-  **servo_control格式**: 作动器专用格式

##  **迁移策略**

### **阶段1: 创建ImportExportManager (高优先级)**
1. 创建ImportExportManager类
2. 集成现有的XLSDataExporter_1_2和JSONDataExporter_1_2
3. 保留所有现有格式支持
4. 实现统一的导入导出接口

### **阶段2: 重构主界面**
1. 简化MainWindow，只保留UI协调功能
2. 通过ImportExportManager处理所有导入导出
3. 建立信号槽连接
4. 保持现有用户界面不变

### **阶段3: 创建其他管理器**
1. 创建ProjectManager、DeviceManager等
2. 逐步迁移相关功能
3. 保持向后兼容性

### **阶段4: 测试和优化**
1. 单元测试各个管理器
2. 集成测试整体功能
3. 性能优化和代码清理

##  **架构优势**

### **1. 格式兼容性**
- 完全保留现有Excel、JSON格式支持
- 用户无需改变现有工作流程
- 向后兼容所有现有功能

### **2. 模块化设计**
- 每个管理器负责特定功能域
- 清晰的职责分离
- 易于维护和扩展

### **3. 统一接口**
- 所有导入导出功能通过ImportExportManager统一管理
- 一致的错误处理和进度反馈
- 统一的用户体验

### **4. 可测试性**
- 每个管理器可以独立测试
- 模拟数据管理器进行单元测试
- 集成测试覆盖完整流程

### **5. 可扩展性**
- 新格式支持只需扩展ImportExportManager
- 新设备类型只需扩展DeviceManager
- 新功能模块可以独立添加

##  **实施建议**

### **优先级排序**
1. **最高优先级**: ImportExportManager (保留现有格式支持)
2. **高优先级**: ProjectManager, DeviceManager (核心功能)
3. **中优先级**: TreeManager, MenuManager (重要功能)
4. **低优先级**: LogManager (辅助功能)

### **风险控制**
1. **渐进式重构**: 一次只重构一个模块
2. **保持兼容**: 重构过程中保持原有功能
3. **充分测试**: 每个阶段都要进行充分测试
4. **文档更新**: 及时更新相关文档

### **成功标准**
1. 主界面代码从10,903行减少到800行以下
2. 所有现有格式支持完全保留
3. 用户界面和操作流程保持不变
4. 所有功能测试通过
5. 代码可维护性显著提升

这个架构方案将主界面的代码从10,903行减少到约800行，同时完全保留现有的Excel、JSON格式支持，实现了更好的模块化和可维护性。
