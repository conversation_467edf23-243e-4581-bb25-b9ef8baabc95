@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🔧 传感器组ID异常值修复测试
echo ========================================
echo.

echo 📋 问题描述:
echo.
echo ❌ 修复前:
echo    载荷_传感器组 → 组ID: 3640 (异常哈希值)
echo.
echo ✅ 修复后:
echo    载荷_传感器组 → 组ID: 1 (简单递增)
echo.

echo 🔧 修复内容:
echo.
echo 1. 修改 generateSensorGroupIdFromName() 函数
echo 2. 从哈希算法改为递增算法
echo 3. 使组ID更直观易懂
echo.

echo 💡 测试步骤:
echo.
echo 1. 编译Debug版本
echo 2. 启动程序
echo 3. 创建 "载荷_传感器组"
echo 4. 添加传感器设备
echo 5. 鼠标悬停查看DEBUG信息
echo 6. 确认组ID为 1 而不是 3640
echo.

echo 🎯 预期DEBUG信息:
echo.
echo 🔧 DEBUG信息 🔧
echo ═══════════════════
echo 🔍 节点: 载荷_传感器组, 类型: 传感器组
echo 组ID: 1 ✅正常值
echo 🔍 组名: 载荷_传感器组, 传感器数: 2
echo ID: 1, 序号: 1
echo ID: 2, 序号: 2
echo.

echo ========================================
echo 🔧 传感器组ID生成算法已修复！
echo ========================================
echo.
pause
