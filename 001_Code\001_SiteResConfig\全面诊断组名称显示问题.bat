@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🔍 全面诊断传感器组名称显示问题
echo ========================================
echo.

echo 📋 问题现状:
echo 从截图可以看出，传感器组名称仍然在每一行都显示：
echo 1 载荷_传感器组 传感器_000001 Axial Gage
echo 2 载荷_传感器组 传感器_000002 Axial Gage  ← 应该为空
echo 3 位置_传感器组 传感器_000001-位 Axial Gage
echo 4 位置_传感器组 传感器_000002523 Axial Gage  ← 应该为空
echo.

echo 🔍 可能的问题源:
echo.
echo 1. 数据源问题:
echo    - SensorDataManager中的数据本身就有问题
echo    - 每个传感器都被分配了组名称
echo.
echo 2. 导出路径问题:
echo    - 可能调用了错误的getAllSensorGroups()方法
echo    - 主窗口的方法 vs SensorDataManager的方法
echo.
echo 3. 代码执行问题:
echo    - 我们的修复代码可能没有被执行
echo    - 可能有其他地方覆盖了我们的逻辑
echo.
echo 4. Excel写入问题:
echo    - QVariant()可能不够"空"
echo    - QXlsx库的处理方式问题
echo.

echo 🚀 诊断步骤:
echo.
echo 步骤1: 重新编译应用程序
echo - 确保所有修复代码生效
echo - 特别是addSensorGroupDetailToExcel方法中的调试信息
echo.
echo 步骤2: 启动应用程序并导出
echo - 使用任何传感器导出功能
echo - 重点观察控制台调试输出
echo.
echo 步骤3: 检查调试输出
echo 应该看到以下调试信息:
echo   "=== 传感器组名称显示逻辑 ==="
echo   "组ID: 1, 传感器索引: 0, 组名称: 载荷_传感器组"
echo   "传感器序列号: 传感器_000001, 显示组名称: 是"
echo   "写入组名称到行X列2: '载荷_传感器组'"
echo   
echo   "=== 传感器组名称显示逻辑 ==="
echo   "组ID: 1, 传感器索引: 1, 组名称: 载荷_传感器组"
echo   "传感器序列号: 传感器_000002, 显示组名称: 否"
echo   "写入空值到行X列2"
echo.
echo 步骤4: 分析调试结果
echo.
echo 情况A: 没有看到调试信息
echo - 说明addSensorGroupDetailToExcel方法没有被调用
echo - 可能使用了其他导出路径
echo - 需要检查实际调用的导出方法
echo.
echo 情况B: 看到调试信息，但逻辑错误
echo - 所有传感器的"显示组名称"都是"是"
echo - 说明传感器索引i始终为0
echo - 数据结构有问题
echo.
echo 情况C: 看到正确的调试信息，但Excel仍有问题
echo - 逻辑正确，但Excel写入有问题
echo - 可能是QVariant()的问题
echo - 需要尝试其他空值写入方式
echo.

echo 🔧 根据诊断结果的修复方案:
echo.
echo 方案A: 如果没有调用addSensorGroupDetailToExcel
echo 1. 检查实际使用的导出方法
echo 2. 确认数据来源（SensorDataManager vs 硬件树）
echo 3. 修复导出路径调用
echo.
echo 方案B: 如果数据结构有问题
echo 1. 检查SensorDataManager中的传感器组数据
echo 2. 确认每个组中传感器的索引是否正确
echo 3. 修复传感器组构建逻辑
echo.
echo 方案C: 如果Excel写入有问题
echo 1. 尝试使用QString("")而不是QVariant()
echo 2. 尝试使用nullptr
echo 3. 检查QXlsx库的文档
echo.

echo 💡 额外的诊断技巧:
echo.
echo 1. 对比作动器组导出:
echo    - 检查作动器组是否有相同问题
echo    - 如果作动器组正常，对比两者的差异
echo.
echo 2. 检查Excel文件的实际内容:
echo    - 使用Excel打开文件
echo    - 选中"应该为空"的单元格
echo    - 查看单元格的实际内容（不是显示）
echo.
echo 3. 简化测试:
echo    - 创建只有一个传感器组的测试数据
echo    - 该组包含2-3个传感器
echo    - 导出并检查结果
echo.

echo 🎯 预期的正确调试输出:
echo.
echo 组1（载荷_传感器组）:
echo   传感器1: "显示组名称: 是" + "写入组名称到行X列2"
echo   传感器2: "显示组名称: 否" + "写入空值到行X列2"
echo   传感器3: "显示组名称: 否" + "写入空值到行X列2"
echo.
echo 组2（位置_传感器组）:
echo   传感器1: "显示组名称: 是" + "写入组名称到行X列2"
echo   传感器2: "显示组名称: 否" + "写入空值到行X列2"
echo.

echo ⚠️ 关键检查点:
echo.
echo 1. 确认调试信息出现
echo 2. 确认传感器索引i的值正确（0,1,2...）
echo 3. 确认"显示组名称"的判断正确
echo 4. 确认Excel写入的行号和内容
echo.

echo 📝 记录诊断结果:
echo 请将控制台的调试输出复制保存，以便进一步分析。
echo 特别注意：
echo - 每个传感器的索引值
echo - 组名称显示的判断结果
echo - Excel写入的具体行列位置
echo.

pause
