@echo off
chcp 65001 >nul
echo ========================================
echo 拖拽颜色恢复修复测试
echo ========================================
echo.

echo 正在编译项目...
cd SiteResConfig

if exist "Makefile" (
    echo 清理旧的构建文件...
    del /Q Makefile >nul 2>&1
)

echo 生成Makefile...
qmake SiteResConfig_Simple.pro

if not exist "Makefile" (
    echo 错误: qmake 失败，无法生成 Makefile
    pause
    exit /b 1
)

echo 开始编译...
mingw32-make clean >nul 2>&1
mingw32-make

if exist "debug\SiteResConfig.exe" (
    echo.
    echo ✅ 编译成功！
    echo 可执行文件: debug\SiteResConfig.exe
    echo.
    echo 🎨 拖拽颜色恢复修复完成:
    echo ├─ 拖拽源颜色恢复: 增强的startDrag方法
    echo ├─ 拖拽目标颜色恢复: 完善的事件处理
    echo ├─ 多重保护机制: 定时器+事件+析构函数
    echo ├─ 全局强制恢复: ForceRestoreAllTreeColors方法
    echo └─ 自动颜色恢复: 拖拽完成后自动调用
    echo.
    echo 🔧 修复的问题:
    echo ├─ 拖拽开始时设置颜色后无法恢复
    echo ├─ 拖拽过程中颜色状态管理混乱
    echo ├─ 拖拽完成后节点背景颜色残留
    echo └─ 缺少强制恢复机制
    echo.
    echo 🎯 测试建议:
    echo 1. 启动程序并创建硬件节点
    echo 2. 拖拽硬件节点到测试配置树
    echo 3. 观察拖拽过程中的颜色变化
    echo 4. 验证拖拽完成后所有颜色都恢复正常
    echo 5. 测试拖拽取消时的颜色恢复
    echo.
) else (
    echo.
    echo ❌ 编译失败！
    echo 请检查编译错误信息
)

echo.
pause
