# 📊 作动器示例Excel文件创建脚本

## 🎯 目标
创建一个包含作动器数据的Excel文件，用于测试导入功能

## 📋 文件结构

### 工作表1: 硬件配置数据表
- 包含现有的硬件树结构

### 工作表2: 作动器
- 17列完整格式的作动器数据
- 包含2个作动器组，共4个作动器

## 📊 作动器数据内容

### 表头信息 (第1-5行)
```
A1: 作动器配置数据表
A2: 导出时间: 2025-08-14 15:30:00
A3: 说明: 包含作动器组及其作动器的完整配置信息
A4: (空行)
A5: 组序号 | 作动器组名称 | 作动器序号 | 作动器序列号 | 作动器类型 | Unit类型 | Unit名称 | 行程(m) | 位移(m) | 拉伸面积(m²) | 压缩面积(m²) | 极性 | Deliver(V) | 频率(Hz) | 输出倍数 | 平衡(V) | 备注
```

### 数据行 (第6-9行)
```
A6: 1 | 主作动器组 | 1 | ACT001 | 单出杆 | m | 米 | 0.15 | 0.05 | 0.0314 | 0.0254 | Positive | 5.0 | 50.0 | 1.0 | 2.5 | 主控制作动器
A7: 1 |  | 2 | ACT002 | 单出杆 | m | 米 | 0.15 | 0.05 | 0.0314 | 0.0254 | Positive | 5.0 | 50.0 | 1.0 | 2.5 | 备用作动器
A8: 2 | 辅助作动器组 | 1 | ACT003 | 双出杆 | mm | 毫米 | 100.0 | 30.0 | 0.0201 | 0.0154 | Negative | 3.5 | 25.0 | 0.8 | 1.8 | 辅助控制
A9: 2 |  | 2 | ACT004 | 单出杆 | cm | 厘米 | 20.0 | 8.0 | 0.0283 | 0.0226 | Positive | 4.2 | 40.0 | 1.2 | 2.1 | 精密控制
```

## 🔧 创建方法

由于无法直接创建二进制Excel文件，我们需要：
1. 使用程序代码创建Excel文件
2. 或者提供CSV格式的数据，然后转换为Excel

## 📝 CSV格式数据 (用于转换)

```csv
组序号,作动器组名称,作动器序号,作动器序列号,作动器类型,Unit类型,Unit名称,行程(m),位移(m),拉伸面积(m²),压缩面积(m²),极性,Deliver(V),频率(Hz),输出倍数,平衡(V),备注
1,主作动器组,1,ACT001,单出杆,m,米,0.15,0.05,0.0314,0.0254,Positive,5.0,50.0,1.0,2.5,主控制作动器
1,,2,ACT002,单出杆,m,米,0.15,0.05,0.0314,0.0254,Positive,5.0,50.0,1.0,2.5,备用作动器
2,辅助作动器组,1,ACT003,双出杆,mm,毫米,100.0,30.0,0.0201,0.0154,Negative,3.5,25.0,0.8,1.8,辅助控制
2,,2,ACT004,单出杆,cm,厘米,20.0,8.0,0.0283,0.0226,Positive,4.2,40.0,1.2,2.1,精密控制
```

## 🚀 下一步操作

1. 使用XLSDataExporter创建包含作动器数据的Excel文件
2. 测试从Excel文件读取作动器数据
3. 验证数据的完整性和正确性
