@echo off
echo.
echo ========================================
echo Method Name Fix Test
echo ========================================
echo.

echo Fixed method name error:
echo - Changed RefreshTreeDisplay() to UpdateTreeDisplay()
echo - Fixed 5 occurrences in MainWindow_Qt_Simple.cpp
echo.

echo All compilation errors should now be resolved:
echo 1. const method calling non-const method - FIXED
echo 2. undeclared member variables - FIXED  
echo 3. QList template instantiation - FIXED
echo 4. undefined method RefreshTreeDisplay - FIXED
echo.

if exist "SiteResConfig\SiteResConfig_Simple.pro" (
    echo Found project file, testing compilation...
    echo.
    
    cd SiteResConfig
    
    echo Cleaning old files...
    if exist "Makefile" del /Q "Makefile" 2>nul
    if exist "debug" rmdir /S /Q "debug" 2>nul
    if exist "release" rmdir /S /Q "release" 2>nul
    if exist "ui_*.h" del /Q "ui_*.h" 2>nul
    
    echo.
    echo Running qmake...
    qmake SiteResConfig_Simple.pro -spec win32-g++ "CONFIG+=debug" 2>&1
    
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo qmake successful!
        echo.
        echo Starting compilation...
        mingw32-make debug 2>&1
        
        if %ERRORLEVEL% EQU 0 (
            echo.
            echo *** COMPILATION SUCCESSFUL! ***
            echo.
            echo All Actuator1_1 integration errors have been fixed!
            echo The program is ready to use with new features.
            echo.
            
            if exist "debug\SiteResConfig.exe" (
                echo Executable created successfully!
                echo.
                echo Ready to test Actuator1_1 features:
                echo 1. Hardware menu -^> Actuator1_1 Version
                echo 2. Create Actuator dialog with 4 tabs
                echo 3. JSON/Excel import/export functions
                echo 4. Statistics and data management
                echo.
                
                set /p choice="Launch program to test all features? (y/n): "
                if /i "%choice%"=="y" (
                    echo Launching program...
                    start "" "debug\SiteResConfig.exe"
                    echo.
                    echo Test checklist:
                    echo [ ] Open Hardware -^> Actuator1_1 Version menu
                    echo [ ] Create new actuator using Ctrl+Alt+A
                    echo [ ] Test all 4 tabs in dialog
                    echo [ ] Test data validation
                    echo [ ] Test preview function
                    echo [ ] Export data to JSON
                    echo [ ] View statistics
                )
            ) else (
                echo ERROR: Executable not found
            )
        ) else (
            echo.
            echo *** COMPILATION STILL FAILED ***
            echo Please check the error messages above for remaining issues.
        )
    ) else (
        echo.
        echo *** QMAKE FAILED ***
        echo Please check Qt environment configuration.
    )
    
    cd ..
) else (
    echo ERROR: Project file not found
)

echo.
echo ========================================
echo Method Name Fix Test Completed
echo ========================================
echo.
pause
