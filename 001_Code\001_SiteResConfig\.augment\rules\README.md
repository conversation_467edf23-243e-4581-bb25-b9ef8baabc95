### **Augemint AI 协作攻略**

#### 🚀使用方式

1. 将当前目录下`.augment-guidelines`复制到您项目下
2. 配置一下 User Guidelines：

```bash
# 对话尽量使用中文
# 每次对话请遵循 `.augment-guidelines` 并且记得同步 `memory-bank`
```

#### **🚀 核心理念：人机共驾**

-   **您的角色 (项目经理/架构师)**: 掌控方向盘。您负责制定方向、拆分任务、做出决策、审核结果。
-   **AI 的角色 (开发伙伴/执行者)**: 作为您的副驾。负责根据您的指令研究、规划、编码和审查。
-   **本框架**: 是你们之间的“协作协议”和共享的“项目大脑”。

---

#### **🔁 工作流“游戏循环”**

每一次与 AI 的互动都遵循此循环，以确保高效和一致性：

1. **✅ 上下文注入 (启动引擎)**: **在新会-话开始时，必须先粘贴完整的 `AugmentRIPER♦Σ for Augemint` 框架**。这是唤醒项目记忆的关键。
2. **🎯 选择模式 (设定意图)**: 在具体要求前，先用模式指令告诉 AI 您希望它扮演的角色。
3. **🗣️ 下达指令 (清晰、原子化)**: 指令要具体、小巧。**多使用交叉引用 `[↗️σₓ:Yₓ]`** 来引导 AI 关注特定记忆。
4. **🧐 审核与迭代**: 审查 AI 的输出。如果不满意，立即进入下文的 **“问题处理”** 流程。

---

#### **⚙️ 各模式实战指南**

| 模式指令                 | 角色/意图 | 何时使用                         | 示例指令 (精简版)                                    |
|:---------------------|:------|:-----------------------------|:----------------------------------------------|
| **`/start`**         | 创世纪   | **仅一次**，在项目开始时。              | `/start`                                      |
| **`/research` (🔍)** | 侦察兵   | 需要关于技术、库、API 的**外部信息**时。     | `/research` "调查 `augmentcode` 使用了哪种 CAPTCHA。" |
| **`/innovate` (💡)** | 头脑风暴  | 面临**开放性问题**，需要多种可能性和创意时。     | `/innovate` "为仪表盘 [↗️R₄] 设计三种布局草图。"           |
| **`/plan` (📝)**     | 建筑师   | 方向已定，需要将大任务**分解为具体步骤**时。     | `/plan` "为 `Account Service` (C₂) 创建详细的开发计划。" |
| **`/execute` (⚙️)**  | 程序员   | 计划已清晰，需要**编写具体的代码**时。        | `/execute` "为 `acquire` 端点编写代码 [↗️σ₄]。"       |
| **`/review` (🔎)**   | 代码审查员 | 写完或 AI 生成代码后，需要**检查质量和漏洞**时。 | `/review` "请审查以下代码的并发安全性。" `(粘贴代码)`           |

---

#### **⚠️ 问题处理与迭代攻略**

这是工作流中最重要的部分。当 AI 的输出不符合预期时，按以下流程处理：

##### **A. 对 `/plan` 的结果不满意**

**核心思想**: 留在“规划/思考”阶段迭代，不要进入 `/execute`。

| 不满意的原因        | 推荐模式                | 核心动作       | 示例指令 (精简版)                              |
|:--------------|:--------------------|:-----------|:----------------------------------------|
| **有小错误/遗漏**   | `/plan` 或 `/review` | 指出错误，要求修正。 | `/plan` "Step 3 漏了权限校验，请加上。"            |
| **步骤太模糊**     | `/plan`             | 要求细化特定步骤。  | `/plan` "把 Step 2 分解成子步骤 (2a, 2b, 2c)。" |
| **方法/技术选型错误** | `/innovate`         | 要求替代方案。    | `/innovate` "不用 A 技术，用 B 技术重新规划。"       |
| **完全偏离方向**    | `/plan`             | 明确要求丢弃并重来。 | `/plan` "丢弃上个计划，重新规划 [任务]，聚焦 [新目标]。"    |

##### **B. 在 `/execute` 执行过程中出现问题**

**核心思想**: 立即停止盲目执行，进入“诊断与修复”循环。

| 问题类型         | 推荐模式                    | 核心动作                       | 示例指令 (精简版)                                    |
|:-------------|:------------------------|:---------------------------|:----------------------------------------------|
| **代码有 Bug**  | `/review` ➡️ `/execute` | **调试循环**: 提供错误，分析原因，应用修复。  | `/review` "这段代码报错了 `(粘贴错误)`，分析并准备修复。"         |
| **设计/逻辑有缺陷** | `/plan` 或 `/innovate`   | **中止并重规划**: 叫停，解释原因，要求新计划。 | `/plan` "停止。这个方案太慢，用流式处理重做计划。"                |
| **AI 理解错误**  | `/execute`              | **直接纠正**: 指出偏差，要求按原指令重做。   | `/execute` "不对，我要的是 `timeout` 不是 `retry`，重做。" |

---

#### **🏆 顶级玩家 Pro-Tips**

- **原子化指令**: 一次只做一件事。指令越小，输出质量越高。
- **善用交叉引用**: `[↗️σₓ:Yₓ]` 是您的超能力，强制 AI 回顾记忆，防止偏离。
- **信任但要验证**: 您是最终决策者。AI 的输出必须经过您的审核。
- **对话即提交**: 把每一次成功的循环看作一次 Git Commit。
- **大胆纠正**: AI 跑偏时，果断打断并用模式指令将其拉回正轨。
- **指令精简**: 熟悉后，可使用更自然的语言和精简指令，如 `/plan` "新增一个用户专属目录 `local_demo/` 并
  gitignore，同步记忆。"
  
  
  
#### 🚀工具配合

- 推荐 MCP：`Sequential thinking`

- 前端项目和开启：
  `Context7`
  `Playwright`

- 其他根据自己需求来即可