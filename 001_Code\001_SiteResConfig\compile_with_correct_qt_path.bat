@echo off
echo ========================================
echo Compile with Correct Qt Path
echo ========================================
echo.

echo [INFO] Using Qt installation at: D:\Qt\Qt5.14.2
echo [INFO] Encoding regression fix applied

REM Set Qt paths for D:\Qt\Qt5.14.2
set QTDIR=D:\Qt\Qt5.14.2\5.14.2\mingw73_32
set MINGW_PATH=D:\Qt\Qt5.14.2\Tools\mingw730_32\bin
set PATH=%QTDIR%\bin;%MINGW_PATH%;%PATH%

echo Qt environment set:
echo   QTDIR: %QTDIR%
echo   MINGW: %MINGW_PATH%
echo.

REM Verify tools are available
echo Verifying Qt tools...
qmake -v
if errorlevel 1 (
    echo ERROR: qmake not found! Please check Qt installation.
    echo Expected path: %QTDIR%\bin\qmake.exe
    pause
    exit /b 1
)

mingw32-make --version > nul 2>&1
if errorlevel 1 (
    echo ERROR: mingw32-make not found! Please check MinGW installation.
    echo Expected path: %MINGW_PATH%\mingw32-make.exe
    pause
    exit /b 1
)

echo Tools verified successfully.
echo.

cd /d "%~dp0\SiteResConfig"

echo Cleaning previous build...
mingw32-make clean > nul 2>&1

echo Generating Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo ERROR: qmake failed!
    pause
    exit /b 1
)

echo Compiling project...
mingw32-make -j4
if errorlevel 1 (
    echo ERROR: Compilation failed!
    pause
    exit /b 1
)

echo.
echo SUCCESS: Encoding regression fix compiled successfully!
echo.
echo [FIXES APPLIED]
echo   ✅ Removed qDebug console output causing encoding issues
echo   ✅ Restored original AddLogEntry behavior
echo   ✅ Kept all functional fixes (deadlock, validation, UI refresh)
echo   ✅ Used correct Qt path: D:\Qt\Qt5.14.2
echo.

echo Starting application...
if exist "debug\SiteResConfig.exe" (
    start "" "debug\SiteResConfig.exe"
    echo.
    echo Application started successfully!
    echo.
    echo [VERIFICATION STEPS]
    echo 1. Check UI log panel for clear Chinese characters
    echo 2. Look for: [INFO] 系统初始化完成
    echo 3. Import test file and verify readable log messages
    echo 4. Confirm no garbled text like: ÍÏ×§¹¦ÄÜÒÑÆôÓÃ
) else (
    echo ERROR: Executable not found at debug\SiteResConfig.exe
)

echo.
pause
