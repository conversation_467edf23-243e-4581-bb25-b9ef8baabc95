@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🧪 传感器详细配置 - DataManager数据源测试
echo ========================================
echo.

echo 📋 测试目标:
echo 1. 验证传感器详细配置完全从SensorDataManager获取数据
echo 2. 验证组序号和传感器组名称列的正确显示
echo 3. 验证测试数据的自动创建和导出
echo 4. 验证数据不依赖界面硬件树
echo.

echo 🔧 实现要点:
echo.
echo 📊 数据源变更:
echo ❌ 旧方式: 从界面硬件树获取数据
echo   - getAllSensorGroups() 从UI树形控件解析
echo   - 数据不完整，依赖界面状态
echo.
echo ✅ 新方式: 从SensorDataManager获取数据
echo   - sensorDataManager_-^>getAllSensorGroups()
echo   - 数据完整，独立于界面状态
echo   - 支持完整的33列传感器参数
echo.

echo 🏗️ 架构改进:
echo 1. XLSDataExporter.cpp:
echo    - 直接调用 sensorDataManager_-^>getAllSensorGroups()
echo    - 不再依赖界面数据
echo.
echo 2. MainWindow_Qt_Simple.cpp:
echo    - OnExportSensorDetailsToExcel() 改为从DataManager获取
echo    - initializeSensorDataManager() 自动创建测试数据
echo.
echo 3. SensorDataManager.cpp:
echo    - 新增 createTestSensorGroups() 方法
echo    - 自动创建3个测试传感器组，共6个传感器
echo.

echo 📋 测试数据结构:
echo.
echo 组1: 载荷_传感器组 (groupId=1)
echo   - SEN001: 载荷传感器, LC-1, 100kN
echo   - SEN002: 载荷传感器, LC-2, 200kN  
echo   - SEN003: 载荷传感器, LC-3, 300kN
echo.
echo 组2: 位置_传感器组 (groupId=2)
echo   - SEN004: 位置传感器, LVDT-4, ±50mm
echo   - SEN005: 位置传感器, LVDT-5, ±100mm
echo.
echo 组3: 温度_传感器组 (groupId=3)
echo   - SEN006: 温度传感器, TC-K, 0-1000°C
echo.

echo 📊 Excel导出格式:
echo 组序号 ^| 传感器组名称    ^| 传感器序列号 ^| 传感器类型 ^| ...
echo ------|---------------|------------|----------|----
echo 1     ^| 载荷_传感器组   ^| SEN001     ^| 载荷传感器 ^| ...
echo 1     ^|               ^| SEN002     ^| 载荷传感器 ^| ...
echo 1     ^|               ^| SEN003     ^| 载荷传感器 ^| ...
echo 2     ^| 位置_传感器组   ^| SEN004     ^| 位置传感器 ^| ...
echo 2     ^|               ^| SEN005     ^| 位置传感器 ^| ...
echo 3     ^| 温度_传感器组   ^| SEN006     ^| 温度传感器 ^| ...
echo.

echo 🚀 测试步骤:
echo 1. 启动应用程序
echo 2. 程序自动创建测试传感器组数据
echo 3. 点击"导出传感器详细配置"
echo 4. 检查Excel文件内容
echo 5. 验证数据完整性和格式正确性
echo.

echo ✅ 预期结果:
echo - Excel包含3个传感器组，共6个传感器
echo - 表头包含"组序号"和"传感器组名称"列
echo - 组序号为1,2,3递增
echo - 组名称只在每组第一行显示
echo - 所有33列传感器参数完整导出
echo - 数据不依赖界面硬件树状态
echo.

echo 🔍 验证要点:
echo 1. 数据完整性: 检查所有传感器参数是否完整
echo 2. 格式正确性: 检查组序号和组名称显示规则
echo 3. 独立性: 清空硬件树后仍能正常导出
echo 4. 一致性: 多次导出结果应完全一致
echo.

echo 📝 关键文件:
echo - SensorDataManager.h/.cpp: 数据管理和测试数据创建
echo - XLSDataExporter.cpp: Excel导出逻辑
echo - MainWindow_Qt_Simple.cpp: 主窗口集成
echo.

pause
