@echo off
echo ========================================
echo  作动器创建功能测试
echo ========================================

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o
if exist "ui_*.h" del ui_*.h

echo.
echo 生成UI头文件...
uic ui\MainWindow.ui -o ui_MainWindow.h
if errorlevel 1 (
    echo UI文件生成失败！
    pause
    exit /b 1
) else (
    echo ✅ UI头文件生成成功: ui_MainWindow.h
)

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo.
    echo 新增功能：
    echo ✅ 隐藏快速创建作动器组菜单
    echo ✅ 作动器组右键菜单
    echo ✅ 作动器详细参数输入对话框
    echo ✅ 参数精度和单位设置
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！作动器创建功能已实现
    echo ========================================
    
    if exist "SiteResConfig.exe" (
        echo 可执行文件: SiteResConfig.exe
        echo.
        echo ✅ 作动器创建功能已实现！
        echo.
        echo 🖱️ 菜单变更:
        echo ❌ 隐藏了"快速创建作动器组"菜单
        echo ✅ 作动器组右键显示"新建" → "作动器"
        echo.
        echo 📋 作动器参数输入对话框:
        echo ├─ 显示信息: "作动器组\作动器_000001" (自动编号)
        echo ├─ 序列号: 文本输入框 (默认值: 作动器_000001)
        echo ├─ 类型: 下拉框 (单出杆/双出杆)
        echo ├─ 缸径: 数值输入框 (单位: m, 精度: 0.00, 最小值: 0)
        echo ├─ 杆径: 数值输入框 (单位: m, 精度: 0.00, 最小值: 0)
        echo └─ 行程: 数值输入框 (单位: m, 精度: 0.00, 最小值: 0)
        echo.
        echo 🎯 操作流程:
        echo 1. 右键"作动器"节点 → "新建" → "作动器组"
        echo 2. 选择作动器组类型 (如: 100kN_作动器)
        echo 3. 右键创建的作动器组 → "新建" → "作动器"
        echo 4. 填写作动器详细参数:
        echo    - 序列号: ACT001
        echo    - 类型: 单出杆
        echo    - 缸径: 0.10 m
        echo    - 杆径: 0.05 m
        echo    - 行程: 0.20 m
        echo 5. 确定创建作动器设备
        echo.
        echo 🌳 创建后的树形结构:
        echo 任务1
        echo ├─ 作动器
        echo │  └─ 100kN_作动器 (类型: "作动器组")
        echo │     ├─ ACT001 [单出杆] (类型: "作动器设备")
        echo │     └─ ACT002 [双出杆] (类型: "作动器设备")
        echo ├─ 传感器
        echo └─ 硬件节点资源
        echo.
        echo 📊 参数设置详情:
        echo.
        echo 🔧 缸径参数:
        echo - 单位: 米 (m)
        echo - 精度: 0.00 (小数点后2位)
        echo - 最小值: 0.00 m
        echo - 最大值: 10.00 m
        echo - 步长: 0.01 m
        echo - 默认值: 0.10 m
        echo.
        echo 🔧 杆径参数:
        echo - 单位: 米 (m)
        echo - 精度: 0.00 (小数点后2位)
        echo - 最小值: 0.00 m
        echo - 最大值: 5.00 m
        echo - 步长: 0.01 m
        echo - 默认值: 0.05 m
        echo.
        echo 🔧 行程参数:
        echo - 单位: 米 (m)
        echo - 精度: 0.00 (小数点后2位)
        echo - 最小值: 0.00 m
        echo - 最大值: 2.00 m
        echo - 步长: 0.01 m
        echo - 默认值: 0.20 m
        echo.
        echo 💡 工具提示信息:
        echo 鼠标悬停在作动器节点上显示完整参数:
        echo - 序列号: ACT001
        echo - 类型: 单出杆
        echo - 缸径: 0.10 m
        echo - 杆径: 0.05 m
        echo - 行程: 0.20 m
        echo.
        echo 📝 日志记录:
        echo 系统自动记录作动器创建信息:
        echo "创建作动器设备: ACT001, 类型: 单出杆, 缸径: 0.10m, 杆径: 0.05m, 行程: 0.20m"
        echo.
        echo 启动程序测试作动器创建功能...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
        echo 启动程序...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
        echo 启动程序...
        start release\SiteResConfig.exe
    ) else (
        echo 警告: 找不到可执行文件！
    )
)

echo.
echo 📖 作动器创建功能测试指南:
echo.
echo 🎯 完整测试流程:
echo.
echo 1️⃣ 创建作动器组:
echo   - 右键"作动器"节点
echo   - 选择"新建" → "作动器组"
echo   - 选择"100kN_作动器"
echo   - 验证组创建成功
echo.
echo 2️⃣ 创建作动器设备:
echo   - 右键"100kN_作动器"组
echo   - 选择"新建" → "作动器"
echo   - 填写参数:
echo     * 序列号: ACT001
echo     * 类型: 单出杆
echo     * 缸径: 0.10 m
echo     * 杆径: 0.05 m
echo     * 行程: 0.20 m
echo   - 点击确定
echo   - 验证作动器创建成功
echo.
echo 3️⃣ 验证功能:
echo   - 验证作动器显示为"ACT001 [单出杆]"
echo   - 验证工具提示显示完整参数
echo   - 验证日志记录创建信息
echo   - 验证节点类型为"作动器设备"
echo.
echo 🔍 验证要点:
echo - 快速创建菜单已隐藏
echo - 作动器组右键菜单正确显示
echo - 参数输入对话框正常工作
echo - 数值输入框精度和范围正确
echo - 作动器节点正确创建和显示
echo - 工具提示信息完整准确
echo - 日志记录详细信息
echo.
pause
