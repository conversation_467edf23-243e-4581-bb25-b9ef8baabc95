/****************************************************************************
** Resource object code
**
** Created by: The Resource Compiler for Qt version 5.14.2
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

static const unsigned char qt_resource_data[] = {
  // D:/Work/Project/004_SiteResConfig_ProjectAll/001_Code/001_SiteResConfig_OldStruct/SiteResConfig/Res/plus.png
  0x0,0x0,0x1,0x2d,
  0x69,
  0x56,0x42,0x4f,0x52,0x77,0x30,0x4b,0x47,0x67,0x6f,0x41,0x41,0x41,0x41,0x4e,0x53,
  0x55,0x68,0x45,0x55,0x67,0x41,0x41,0x41,0x41,0x6b,0x41,0x41,0x41,0x41,0x4a,0x43,
  0x41,0x59,0x41,0x41,0x41,0x44,0x67,0x6b,0x51,0x59,0x51,0x41,0x41,0x41,0x41,0x42,
  0x48,0x4e,0x43,0x53,0x56,0x51,0x49,0x43,0x41,0x67,0x49,0x66,0x41,0x68,0x6b,0x69,
  0x41,0x41,0x41,0x41,0x41,0x6c,0x77,0x53,0x46,0x6c,0x7a,0x41,0x41,0x41,0x41,0x64,
  0x67,0x41,0x41,0x41,0x48,0x59,0x42,0x54,0x6e,0x73,0x6d,0x43,0x41,0x41,0x41,0x41,
  0x42,0x6c,0x30,0x52,0x56,0x68,0x30,0x55,0x32,0x39,0x6d,0x64,0x48,0x64,0x68,0x63,
  0x6d,0x55,0x41,0x64,0x33,0x64,0x33,0x4c,0x6d,0x6c,0x75,0x61,0x33,0x4e,0x6a,0x59,
  0x58,0x42,0x6c,0x4c,0x6d,0x39,0x79,0x5a,0x35,0x76,0x75,0x50,0x42,0x6f,0x41,0x41,
  0x41,0x42,0x59,0x53,0x55,0x52,0x42,0x56,0x42,0x69,0x56,0x70,0x59,0x34,0x78,0x44,
  0x67,0x41,0x67,0x43,0x41,0x50,0x62,0x2f,0x33,0x2b,0x61,0x4f,0x4c,0x67,0x34,0x6b,
  0x51,0x51,0x53,0x61,0x41,0x63,0x58,0x53,0x69,0x6d,0x6c,0x56,0x45,0x72,0x4a,0x7a,
  0x4d,0x7a,0x4d,0x7a,0x4d,0x79,0x63,0x63,0x38,0x34,0x35,0x46,0x32,0x50,0x4d,0x47,
  0x47,0x50,0x47,0x47,0x44,0x50,0x47,0x6a,0x44,0x46,0x6d,0x6a,0x42,0x6c,0x6a,0x2f,
  0x68,0x56,0x6a,0x7a,0x42,0x68,0x6a,0x78,0x68,0x67,0x7a,0x78,0x6f,0x77,0x78,0x5a,
  0x6f,0x77,0x5a,0x59,0x38,0x77,0x59,0x4d,0x38,0x61,0x59,0x4d,0x57,0x61,0x4d,0x4d,
  0x57,0x4f,0x4d,0x47,0x57,0x50,0x4d,0x47,0x47,0x50,0x47,0x6d,0x48,0x38,0x41,0x58,
  0x67,0x41,0x4a,0x41,0x45,0x6c,0x45,0x51,0x56,0x51,0x41,0x41,0x41,0x41,0x41,0x53,
  0x55,0x56,0x4f,0x52,0x4b,0x35,0x43,0x59,0x49,0x49,0x3d,0xa,
  
};

static const unsigned char qt_resource_name[] = {
  // images
  0x0,0x6,
  0x7,0x3,0x7d,0xc3,
  0x0,0x69,
  0x0,0x6d,0x0,0x61,0x0,0x67,0x0,0x65,0x0,0x73,
    // Res
  0x0,0x3,
  0x0,0x0,0x58,0xc3,
  0x0,0x52,
  0x0,0x65,0x0,0x73,
    // plus.png
  0x0,0x8,
  0x3,0xc6,0x59,0xa7,
  0x0,0x70,
  0x0,0x6c,0x0,0x75,0x0,0x73,0x0,0x2e,0x0,0x70,0x0,0x6e,0x0,0x67,
  
};

static const unsigned char qt_resource_struct[] = {
  // :
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x1,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/images
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x2,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/images/Res
  0x0,0x0,0x0,0x12,0x0,0x2,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x3,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/images/Res/plus.png
  0x0,0x0,0x0,0x1e,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x0,
0x0,0x0,0x1,0x98,0xc6,0x7a,0xb7,0xe0,

};

#ifdef QT_NAMESPACE
#  define QT_RCC_PREPEND_NAMESPACE(name) ::QT_NAMESPACE::name
#  define QT_RCC_MANGLE_NAMESPACE0(x) x
#  define QT_RCC_MANGLE_NAMESPACE1(a, b) a##_##b
#  define QT_RCC_MANGLE_NAMESPACE2(a, b) QT_RCC_MANGLE_NAMESPACE1(a,b)
#  define QT_RCC_MANGLE_NAMESPACE(name) QT_RCC_MANGLE_NAMESPACE2( \
        QT_RCC_MANGLE_NAMESPACE0(name), QT_RCC_MANGLE_NAMESPACE0(QT_NAMESPACE))
#else
#   define QT_RCC_PREPEND_NAMESPACE(name) name
#   define QT_RCC_MANGLE_NAMESPACE(name) name
#endif

#ifdef QT_NAMESPACE
namespace QT_NAMESPACE {
#endif

bool qRegisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);
bool qUnregisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);

#ifdef QT_NAMESPACE
}
#endif

int QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)();
int QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)()
{
    int version = 3;
    QT_RCC_PREPEND_NAMESPACE(qRegisterResourceData)
        (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)();
int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)()
{
    int version = 3;
    QT_RCC_PREPEND_NAMESPACE(qUnregisterResourceData)
       (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

namespace {
   struct initializer {
       initializer() { QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)(); }
       ~initializer() { QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)(); }
   } dummy;
}
