#ifndef INFOPANELMANAGER_H
#define INFOPANELMANAGER_H

#include <QObject>
#include <QString>
#include <QList>
#include "BasicInfoWidget.h"

// 引用现有信息面板类
class DetailInfoPanel;
class QTreeWidgetItem;

/**
 * @brief 信息面板管理器 - 基于现有信息面板组件
 * 
 * InfoPanelManager基于现有的BasicInfoWidget和DetailInfoPanel
 * 提供统一的信息面板管理接口。
 * 
 * 设计特点：
 * - 100%基于现有信息面板 (BasicInfoWidget, DetailInfoPanel)
 * - 使用现有数据结构体 (NodeInfo, SubNodeInfo, NodeStatus)
 * - 不创建任何新的信息面板组件
 * - 不改变任何现有显示格式
 */
class InfoPanelManager : public QObject
{
    Q_OBJECT

public:
    explicit InfoPanelManager(QObject* parent = nullptr);
    ~InfoPanelManager();

    // 设置现有信息面板 (不创建新的)
    void setBasicInfoWidget(BasicInfoWidget* widget);
    void setDetailInfoPanel(DetailInfoPanel* panel);

    // 使用现有NodeInfo结构体更新信息面板
    void updateBasicInfo(const NodeInfo& nodeInfo);
    void updateDetailInfo(const NodeInfo& nodeInfo);
    void clearAllInfo();

    // 使用现有NodeStatus枚举更新状态
    void updateNodeStatus(NodeStatus status);

    // 控制通道信息管理 (使用现有SubNodeInfo)
    void setControlChannelInfo(const QString& channelName, const QList<SubNodeInfo>& subNodes);
    void setControlChannelRootInfo(const QString& rootName, const QList<QTreeWidgetItem*>& childChannels);

    // 信息面板状态
    bool hasBasicInfoWidget() const;
    bool hasDetailInfoPanel() const;

    // 获取当前信息
    NodeInfo getCurrentNodeInfo() const;

signals:
    void infoUpdated(const QString& infoType);
    void infoCleared();
    void nodeStatusChanged(NodeStatus status);

private:
    // 引用现有信息面板 (不创建新的)
    BasicInfoWidget* basicInfoWidget_;
    DetailInfoPanel* detailInfoPanel_;

    // 当前信息缓存
    NodeInfo currentNodeInfo_;
    NodeStatus currentNodeStatus_;

    void initializeInfoPanels();
    void connectInfoPanelSignals();
    void disconnectInfoPanelSignals();
};

#endif // INFOPANELMANAGER_H 