/**
 * @file DeviceManager.cpp
 * @brief 设备管理模块实现 - 基于现有DataManager_1_2系列
 * @details 封装现有的数据管理器，提供统一的设备管理接口
 * <AUTHOR> Assistant
 * @date 2025-01-27
 * @version 3.4.0
 */

#include "DeviceManager.h"
#include "MainWindow_Qt_Simple.h"
#include "SensorDataManager_1_2.h"
#include "ActuatorDataManager_1_2.h"
#include <QDebug>
#include <QInputDialog>
#include <QMessageBox>

DeviceManager::DeviceManager(QObject* parent)
    : QObject(parent), 
      mainWindow_(nullptr),
      sensorDataManager_(nullptr),
      actuatorDataManager_(nullptr),
      ctrlChanDataManager_(nullptr),
      hardwareNodeResDataManager_(nullptr) {
}

DeviceManager::~DeviceManager() {
}

void DeviceManager::setMainWindow(CMyMainWindow* mainWindow) {
    mainWindow_ = mainWindow;
}

void DeviceManager::setSensorDataManager(SensorDataManager_1_2* manager) {
    sensorDataManager_ = manager;
}

void DeviceManager::setActuatorDataManager(ActuatorDataManager_1_2* manager) {
    actuatorDataManager_ = manager;
}

void DeviceManager::setCtrlChanDataManager(CtrlChanDataManager* manager) {
    ctrlChanDataManager_ = manager;
}

void DeviceManager::setHardwareNodeResDataManager(HardwareNodeResDataManager* manager) {
    hardwareNodeResDataManager_ = manager;
}

// 作动器组管理
bool DeviceManager::createActuatorGroup() {
    if (!mainWindow_) {
        emit deviceError("主窗口引用未设置");
        return false;
    }
    
    // 委托给主窗口的现有方法
    mainWindow_->OnCreateActuatorGroup();
    emit deviceGroupCreated("Actuator", "ActuatorGroup");
    return true;
}

bool DeviceManager::createActuatorGroup(const QString& groupName) {
    if (!mainWindow_) {
        emit deviceError("主窗口引用未设置");
        return false;
    }
    
    // 对于指定名称的组创建，我们可以通过ViewModel直接创建
    if (mainWindow_->actuatorViewModel1_2_) {
        int groupId = mainWindow_->actuatorViewModel1_2_->createActuatorGroupBusiness(groupName);
        if (groupId > 0) {
            emit deviceGroupCreated("Actuator", groupName);
            return true;
        }
    }
    
    emit deviceError("创建作动器组失败");
    return false;
}

bool DeviceManager::editActuatorGroup(QTreeWidgetItem* item) {
    if (!actuatorDataManager_ || !item) {
        emit deviceError("作动器数据管理器未初始化或节点为空");
        return false;
    }
    
    QString groupName = item->text(0);
    emit deviceStatusChanged(QString("编辑作动器组：%1").arg(groupName));
    
    // 获取组ID
    int groupId = extractActuatorGroupIdFromItem(item);
    if (groupId <= 0) {
        emit deviceError("编辑作动器组失败：无效的组ID");
        return false;
    }
    
    // 获取现有组信息
    UI::ActuatorGroup_1_2 existingGroup = mainWindow_->actuatorViewModel1_2_->getDataManager()->getActuatorGroup(groupId);
    if (existingGroup.groupId <= 0) {
        emit deviceError(QString("未找到作动器组：%1").arg(groupName));
        return false;
    }
    
    // 弹出编辑对话框
    bool ok;
    QString newGroupName = QInputDialog::getText(mainWindow_, 
                                               QObject::tr("编辑作动器组"),
                                               QObject::tr("请输入新的作动器组名称:"),
                                               QLineEdit::Normal,
                                               groupName, &ok);
    
    if (ok && !newGroupName.isEmpty()) {
        // 检查名称是否重复
        if (newGroupName != groupName && mainWindow_->actuatorViewModel1_2_->isActuatorGroupNameExistsBusiness(newGroupName)) {
            QMessageBox::warning(mainWindow_, QObject::tr("名称重复"),
                QString("作动器组名称 '%1' 已存在！\n请输入不同的名称。").arg(newGroupName));
            return false;
        }
        
        // 更新组名称
        existingGroup.groupName = newGroupName;
        
        // 使用业务方法更新组
        if (mainWindow_->actuatorViewModel1_2_->getDataManager()->saveActuatorGroup(existingGroup)) {
            emit deviceStatusChanged(QString("作动器组编辑成功：%1 → %2").arg(groupName).arg(newGroupName));
            emit deviceGroupEdited("Actuator", newGroupName);
            
            // 更新树形控件显示
            item->setText(0, newGroupName);
            
            // 通知主窗口更新界面
            if (mainWindow_) {
                mainWindow_->UpdateAllTreeWidgetTooltips();
            }
            
            return true;
        } else {
            QString error = mainWindow_->actuatorViewModel1_2_->getDataManager()->getLastError();
            emit deviceError(QString("作动器组编辑失败：%1").arg(error));
            QMessageBox::warning(mainWindow_, QObject::tr("编辑失败"),
                QString("无法编辑作动器组: %1").arg(error));
            return false;
        }
    } else {
        emit deviceStatusChanged("作动器组编辑已取消");
        return false;
    }
}

// 传感器组管理
bool DeviceManager::createSensorGroup() {
    if (!mainWindow_) {
        emit deviceError("主窗口引用未设置");
        return false;
    }
    
    // 委托给主窗口的现有方法
    mainWindow_->OnCreateSensorGroup();
    emit deviceGroupCreated("Sensor", "SensorGroup");
    return true;
}

bool DeviceManager::createSensorGroup(const QString& groupName) {
    if (!mainWindow_) {
        emit deviceError("主窗口引用未设置");
        return false;
    }
    
    // 委托给主窗口的现有方法
    mainWindow_->CreateSensorGroup(groupName);
    emit deviceGroupCreated("Sensor", groupName);
    return true;
}

// 🆕 新增：设备创建管理
bool DeviceManager::createActuator(QTreeWidgetItem* groupItem) {
    if (!mainWindow_) {
        emit deviceError("主窗口引用未设置");
        return false;
    }
    
    if (!groupItem) {
        emit deviceError("作动器组项目为空");
        return false;
    }
    
    // 委托给主窗口的现有方法
    mainWindow_->OnCreateActuator(groupItem);
    emit deviceCreated("Actuator", groupItem->text(0));
    return true;
}

bool DeviceManager::createSensor(QTreeWidgetItem* groupItem) {
    if (!mainWindow_) {
        emit deviceError("主窗口引用未设置");
        return false;
    }
    
    if (!groupItem) {
        emit deviceError("传感器组项目为空");
        return false;
    }
    
    // 委托给主窗口的现有方法
    mainWindow_->OnCreateSensor(groupItem);
    emit deviceCreated("Sensor", groupItem->text(0));
    return true;
}

// 🆕 新增：设备编辑管理
bool DeviceManager::editActuatorDevice(QTreeWidgetItem* item) {
    if (!mainWindow_) {
        emit deviceError("主窗口引用未设置");
        return false;
    }
    
    if (!item) {
        emit deviceError("作动器设备项目为空");
        return false;
    }
    
    // 委托给主窗口的现有方法
    mainWindow_->OnEditActuatorDevice(item);
    emit deviceEdited("Actuator", item->text(0));
    return true;
}

bool DeviceManager::editSensorDevice(QTreeWidgetItem* item) {
    if (!mainWindow_) {
        emit deviceError("主窗口引用未设置");
        return false;
    }
    
    if (!item) {
        emit deviceError("传感器设备项目为空");
        return false;
    }
    
    // 委托给主窗口的现有方法
    mainWindow_->OnEditSensorDevice(item);
    emit deviceEdited("Sensor", item->text(0));
    return true;
}

// 🆕 新增：设备删除管理
bool DeviceManager::deleteActuatorDevice(QTreeWidgetItem* item) {
    if (!mainWindow_) {
        emit deviceError("主窗口引用未设置");
        return false;
    }
    
    if (!item) {
        emit deviceError("作动器设备项目为空");
        return false;
    }
    
    // 委托给主窗口的现有方法
    mainWindow_->OnDeleteActuatorDevice(item);
    emit deviceDeleted("Actuator", item->text(0));
    return true;
}

bool DeviceManager::deleteSensorDevice(QTreeWidgetItem* item) {
    if (!mainWindow_) {
        emit deviceError("主窗口引用未设置");
        return false;
    }
    
    if (!item) {
        emit deviceError("传感器设备项目为空");
        return false;
    }
    
    // 委托给主窗口的现有方法
    mainWindow_->OnDeleteSensorDevice(item);
    emit deviceDeleted("Sensor", item->text(0));
    return true;
}

// 🆕 新增：硬件节点管理
bool DeviceManager::createHardwareNode() {
    if (!mainWindow_) {
        emit deviceError("主窗口引用未设置");
        return false;
    }
    
    // 委托给主窗口的现有方法
    mainWindow_->OnCreateHardwareNode();
    emit hardwareNodeCreated("HardwareNode");
    return true;
}

bool DeviceManager::deleteHardwareNode(QTreeWidgetItem* item) {
    if (!mainWindow_) {
        emit deviceError("主窗口引用未设置");
        return false;
    }
    
    if (!item) {
        emit deviceError("硬件节点项目为空");
        return false;
    }
    
    // 委托给主窗口的现有方法
    mainWindow_->OnDeleteHardwareNode(item);
    emit hardwareNodeDeleted(item->text(0));
    return true;
}

bool DeviceManager::editHardwareNode(QTreeWidgetItem* item) {
    if (!mainWindow_) {
        emit deviceError("主窗口引用未设置");
        return false;
    }
    
    if (!item) {
        emit deviceError("硬件节点项目为空");
        return false;
    }
    
    // 委托给主窗口的现有方法
    mainWindow_->OnEditHardwareNode(item);
    emit hardwareNodeEdited(item->text(0));
    return true;
}

// 🆕 新增：控制通道管理
bool DeviceManager::createControlChannel(QTreeWidgetItem* parentItem) {
    if (!mainWindow_) {
        emit deviceError("主窗口引用未设置");
        return false;
    }
    
    if (!parentItem) {
        emit deviceError("父项目为空");
        return false;
    }
    
    // 委托给主窗口的现有方法
    mainWindow_->OnCreateControlChannel(parentItem);
    emit controlChannelCreated(parentItem->text(0));
    return true;
}

bool DeviceManager::deleteControlChannel(QTreeWidgetItem* item) {
    if (!mainWindow_) {
        emit deviceError("主窗口引用未设置");
        return false;
    }
    
    if (!item) {
        emit deviceError("控制通道项目为空");
        return false;
    }
    
    // 委托给主窗口的现有方法
    mainWindow_->OnDeleteControlChannel(item);
    emit controlChannelDeleted(item->text(0));
    return true;
}

// 🆕 新增：设备关联管理
bool DeviceManager::clearAssociation(QTreeWidgetItem* item) {
    if (!mainWindow_) {
        emit deviceError("主窗口引用未设置");
        return false;
    }
    
    if (!item) {
        emit deviceError("项目为空");
        return false;
    }
    
    // 委托给主窗口的现有方法
    mainWindow_->OnClearAssociation(item);
    emit associationCleared(item->text(0));
    return true;
}

bool DeviceManager::clearSingleAssociation(QTreeWidgetItem* item) {
    if (!mainWindow_) {
        emit deviceError("主窗口引用未设置");
        return false;
    }
    
    if (!item) {
        emit deviceError("项目为空");
        return false;
    }
    
    // 委托给主窗口的现有方法
    mainWindow_->OnClearSingleAssociation(item);
    emit singleAssociationCleared(item->text(0));
    return true;
}

bool DeviceManager::clearAllAssociation(QTreeWidgetItem* item) {
    if (!mainWindow_) {
        emit deviceError("主窗口引用未设置");
        return false;
    }
    
    if (!item) {
        emit deviceError("项目为空");
        return false;
    }
    
    // 委托给主窗口的现有方法
    mainWindow_->OnClearAllAssociation(item);
    emit allAssociationCleared(item->text(0));
    return true;
}

// 数据同步
bool DeviceManager::synchronizeAllDataManagers() {
    if (!mainWindow_) {
        emit deviceError("主窗口引用未设置");
        return false;
    }
    
    // 委托给主窗口的现有方法
    mainWindow_->SynchronizeAllDataManagers();
    emit dataManagersSynchronized();
    return true;
}

// 获取现有数据管理器 (不创建新的)
SensorDataManager_1_2* DeviceManager::getSensorDataManager() const {
    return sensorDataManager_;
}

ActuatorDataManager_1_2* DeviceManager::getActuatorDataManager() const {
    return actuatorDataManager_;
}

CtrlChanDataManager* DeviceManager::getCtrlChanDataManager() const {
    return ctrlChanDataManager_;
}

HardwareNodeResDataManager* DeviceManager::getHardwareNodeResDataManager() const {
    return hardwareNodeResDataManager_;
}

// 辅助方法实现
int DeviceManager::extractActuatorGroupIdFromItem(QTreeWidgetItem* item) const {
    if (!item) return -1;
    
    // 从树形项目的UserRole数据中获取组ID
    QVariant data = item->data(1, Qt::UserRole);
    if (data.isValid()) {
        return data.toInt();
    }
    
    // 如果没有存储ID，尝试从主窗口获取
    if (mainWindow_) {
        return mainWindow_->extractActuatorGroupIdFromItem(item);
    }
    
    return -1;
}

int DeviceManager::extractSensorGroupIdFromItem(QTreeWidgetItem* item) const {
    if (!item) return -1;
    
    // 从树形项目的UserRole数据中获取组ID
    QVariant data = item->data(1, Qt::UserRole);
    if (data.isValid()) {
        return data.toInt();
    }
    
    // 如果没有存储ID，尝试从主窗口获取
    if (mainWindow_) {
        return mainWindow_->extractSensorGroupIdFromItem(item);
    }
    
    return -1;
} 