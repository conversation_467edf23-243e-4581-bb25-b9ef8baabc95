@echo off
chcp 65001 >nul
echo ========================================
echo  XLS导出器快速编译测试
echo ========================================
echo.

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile* >nul 2>&1
if exist "debug" rmdir /s /q debug >nul 2>&1
if exist "release" rmdir /s /q release >nul 2>&1
if exist "*.o" del *.o >nul 2>&1
if exist "ui_*.h" del ui_*.h >nul 2>&1

echo.
echo 快速检查关键修复...
echo ├─ 检查QtXlsx API修复...
findstr /C:"document.load()" "src\XLSDataExporter.cpp" >nul
if not errorlevel 1 (
    echo │  ❌ 仍有document.load()调用
    goto :error
) else (
    echo │  ✅ QtXlsx API已修复
)

echo ├─ 检查SensorParams字段修复...
findstr /C:"params.precision" "src\XLSDataExporter.cpp" >nul
if not errorlevel 1 (
    echo │  ❌ 仍使用precision字段
    goto :error
) else (
    echo │  ✅ SensorParams字段已修复
)

echo └─ 检查重复定义修复...
findstr /N /C:"parseTooltipToParams" "src\XLSDataExporter.cpp" | find /C "parseTooltipToParams" >nul
if errorlevel 1 (
    echo    ❌ parseTooltipToParams方法丢失
    goto :error
) else (
    echo    ✅ 重复定义已修复
)

echo.
echo 生成UI头文件...
uic ui\MainWindow.ui -o ui_MainWindow.h >nul 2>&1
if errorlevel 1 (
    echo ❌ UI文件生成失败
    goto :error
) else (
    echo ✅ UI头文件生成成功
)

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++ >nul 2>&1
if errorlevel 1 (
    echo ❌ qmake失败
    goto :error
) else (
    echo ✅ Makefile生成成功
)

echo.
echo 开始编译测试...
echo 这可能需要几分钟时间，请耐心等待...
echo.

mingw32-make clean >nul 2>&1
mingw32-make -j4 2>compile_errors.txt
if errorlevel 1 (
    echo ❌ 编译失败！
    echo.
    echo 编译错误信息：
    type compile_errors.txt
    echo.
    echo 请检查上述错误信息并进行修复。
    pause
    exit /b 1
) else (
    echo ✅ 编译成功！
    
    if exist compile_errors.txt del compile_errors.txt >nul 2>&1
    
    echo.
    echo ========================================
    echo  🎉 XLS导出器编译测试通过！
    echo ========================================
    
    if exist "SiteResConfig.exe" (
        echo.
        echo 📊 编译结果:
        echo ├─ 可执行文件: SiteResConfig.exe
        echo ├─ 文件大小: 
        for %%F in (SiteResConfig.exe) do echo │  └─ %%~zF 字节
        echo └─ 修改时间: 
        for %%F in (SiteResConfig.exe) do echo    └─ %%~tF
        echo.
        echo 🎯 修复完成的问题:
        echo ├─ ✅ QtXlsx API兼容性
        echo ├─ ✅ SensorParams字段匹配
        echo ├─ ✅ 数据类型转换
        echo ├─ ✅ setColumnWidth API
        echo └─ ✅ 重复定义清理
        echo.
        echo 🚀 现在可以使用的XLS功能:
        echo ├─ 导出硬件树到Excel
        echo ├─ 导出传感器详细信息到Excel
        echo ├─ 导出完整项目到Excel
        echo ├─ 从Excel导入硬件配置
        echo └─ 批量导出多种格式
        echo.
        
        set /p choice="是否启动程序测试XLS功能？(Y/N): "
        if /i "%choice%"=="Y" (
            echo 启动程序...
            start SiteResConfig.exe
        )
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
        set /p choice="是否启动程序测试？(Y/N): "
        if /i "%choice%"=="Y" start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
        set /p choice="是否启动程序测试？(Y/N): "
        if /i "%choice%"=="Y" start release\SiteResConfig.exe
    ) else (
        echo ⚠️ 警告: 找不到可执行文件
    )
)

echo.
echo 测试完成！
pause
exit /b 0

:error
echo.
echo ❌ XLS导出器编译测试失败！
echo.
echo 可能的问题：
echo 1. 代码修复不完整
echo 2. 环境配置问题
echo 3. 依赖库问题
echo.
echo 请检查错误信息并重新修复。
pause
exit /b 1
