@echo off
echo ========================================
echo  测试DataManager接口编译错误修复
echo ========================================

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    echo 请确保Qt 5.14.2 MinGW版本已正确安装
    pause
    exit /b 1
)

g++ --version
if errorlevel 1 (
    echo 错误: MinGW编译器未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目（测试DataManager接口修复）...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo 请检查上面的错误信息。
    echo.
    echo 已修复的错误:
    echo ✅ ActuatorDataManager.cpp:59 getAllActuatorDetailedParams 错误
    echo ✅ ActuatorDataManager.cpp:342 second 成员错误  
    echo ✅ ActuatorDataManager.cpp:389 getAllActuatorDetailedParams 错误
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  编译成功！DataManager接口错误已修复
    echo ========================================
    
    if exist "SiteResConfig.exe" (
        echo 可执行文件: SiteResConfig.exe
        echo.
        echo ✅ 修复的编译错误:
        echo - ActuatorDataManager.cpp:59 getAllActuatorDetailedParams 错误
        echo - ActuatorDataManager.cpp:342 second 成员错误  
        echo - ActuatorDataManager.cpp:389 getAllActuatorDetailedParams 错误
        echo.
        echo 🔧 修复方案:
        echo - 使用 getAllActuatorSerialNumbers() 替代 getAllActuatorDetailedParams()
        echo - 使用 vector 迭代替代 map pair.second 访问
        echo - 通过序列号逐个获取作动器详细信息
        echo.
        echo 🎯 DataManager接口更新完成:
        echo - TestProject 接口已统一使用 DataManager
        echo - 返回值类型从 map 改为 vector 和单独获取
        echo - 所有操作返回 bool 类型表示成功/失败
        echo - 项目状态管理功能已集成
        echo.
        echo 启动程序验证功能...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
        echo 启动程序验证功能...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
        echo 启动程序验证功能...
        start release\SiteResConfig.exe
    ) else (
        echo 警告: 找不到可执行文件！
    )
)

echo.
echo 📖 修复详情:
echo.
echo 🔧 ActuatorDataManager 接口适配:
echo - getAllActuatorSerialNumbers() 获取序列号列表
echo - getActuatorDetailedParams(sn) 逐个获取详细信息
echo - getAllActuatorGroups() 返回 vector 而非 map
echo.
echo 🎯 DataModels 接口统一:
echo - 所有操作方法返回 bool 类型
echo - 使用 DataManager 委托模式
echo - 保持接口一致性和错误处理
echo.
echo 🚀 项目状态管理功能:
echo - 软件启动时操作区禁用
echo - 新建/打开项目后操作区启用
echo - 项目关闭后重新禁用操作区
echo.
pause
