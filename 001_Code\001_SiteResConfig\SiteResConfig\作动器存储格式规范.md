# 📊 作动器存储格式规范

## 🎯 概述

本文档定义了作动器数据在XLSX文件中的完整存储格式，包括工作表结构、数据字段和示例数据。

## 📋 作动器工作表格式

### 工作表名称
- **中文名称**: `作动器`
- **英文名称**: `Actuators`

### 表头信息区域 (第1-4行)

| 行号 | A列内容 | 说明 |
|------|---------|------|
| 1 | 作动器配置数据表 | 工作表标题 |
| 2 | 导出时间: 2025-08-14 15:30:00 | 导出时间戳 |
| 3 | 说明: 包含作动器组及其作动器的完整配置信息 | 工作表说明 |
| 4 | (空行) | 分隔行 |

### 数据表头 (第5行) - 17列完整格式

| 列号 | 列名 | 字段名 | 数据类型 | 说明 |
|------|------|--------|----------|------|
| A | 组序号 | groupId | int | 作动器组的唯一标识 |
| B | 作动器组名称 | groupName | QString | 作动器组的名称 |
| C | 作动器序号 | actuatorId | int | 组内作动器序号 |
| D | 作动器序列号 | serialNumber | QString | 作动器的唯一序列号 |
| E | 作动器类型 | type | QString | 单出杆/双出杆 |
| F | Unit类型 | unitType | QString | m/mm/cm/inch |
| G | Unit名称 | unitName | QString | 米/毫米/厘米/英寸 |
| H | 行程(m) | stroke | double | 作动器行程，单位米 |
| I | 位移(m) | displacement | double | 作动器位移，单位米 |
| J | 拉伸面积(m²) | tensionArea | double | 拉伸有效面积 |
| K | 压缩面积(m²) | compressionArea | double | 压缩有效面积 |
| L | 极性 | polarity | QString | Positive/Negative |
| M | Deliver(V) | dither | double | Dither/Deliver值，单位伏特 |
| N | 频率(Hz) | frequency | double | 工作频率，单位赫兹 |
| O | 输出倍数 | outputMultiplier | double | 输出倍数系数 |
| P | 平衡(V) | balance | double | 平衡值，单位伏特 |
| Q | 备注 | notes | QString | 备注信息 |

## 📊 示例数据格式

### 完整示例表格

```
A1: 作动器配置数据表
A2: 导出时间: 2025-08-14 15:30:00
A3: 说明: 包含作动器组及其作动器的完整配置信息
A4: (空行)

A5: 组序号 | B5: 作动器组名称 | C5: 作动器序号 | D5: 作动器序列号 | E5: 作动器类型 | F5: Unit类型 | G5: Unit名称 | H5: 行程(m) | I5: 位移(m) | J5: 拉伸面积(m²) | K5: 压缩面积(m²) | L5: 极性 | M5: Deliver(V) | N5: 频率(Hz) | O5: 输出倍数 | P5: 平衡(V) | Q5: 备注

A6: 1 | B6: 主作动器组 | C6: 1 | D6: ACT001 | E6: 单出杆 | F6: m | G6: 米 | H6: 0.15 | I6: 0.05 | J6: 0.0314 | K6: 0.0254 | L6: Positive | M6: 5.0 | N6: 50.0 | O6: 1.0 | P6: 2.5 | Q6: 主控制作动器
A7: 1 | B7: | C7: 2 | D7: ACT002 | E7: 单出杆 | F7: m | G7: 米 | H7: 0.15 | I7: 0.05 | J7: 0.0314 | K7: 0.0254 | L7: Positive | M7: 5.0 | N7: 50.0 | O7: 1.0 | P7: 2.5 | Q7: 备用作动器
A8: 2 | B8: 辅助作动器组 | C8: 1 | D8: ACT003 | E8: 双出杆 | F8: mm | G8: 毫米 | H8: 100.0 | I8: 30.0 | J8: 0.0201 | K8: 0.0154 | L8: Negative | M8: 3.5 | N8: 25.0 | O8: 0.8 | P8: 1.8 | Q8: 辅助控制
```

## 🔧 数据结构定义

### ActuatorParams 结构体
```cpp
struct ActuatorParams {
    // 基本信息
    int actuatorId;           // 作动器ID (用于内部标识)
    QString serialNumber;      // 序列号
    QString type;             // 类型（单出杆/双出杆）

    // Unit字段 (两列存储)
    QString unitType;         // Unit类型 (m/mm/cm/inch)
    QString unitName;         // Unit名称 (米/毫米/厘米/英寸)

    // 截面数据 (Actuator基本参数)
    double stroke;            // 行程 (m)
    double displacement;      // 位移 (m)
    double tensionArea;       // 拉伸面积 (m²)
    double compressionArea;   // 压缩面积 (m²)

    // 伺服控制器参数 (Value)
    QString polarity;         // 极性（Positive/Negative）
    double dither;            // Dither/Deliver值 (V)
    double frequency;         // 频率 (Hz)
    double outputMultiplier;  // 输出倍数
    double balance;           // 平衡值 (V)

    // 物理参数 (用于计算)
    double cylinderDiameter;  // 缸径 (m) - 从tensionArea计算
    double rodDiameter;       // 杆径 (m) - 从compressionArea计算

    // 备注信息
    QString notes;            // 备注
};
```

### ActuatorGroup 结构体
```cpp
struct ActuatorGroup {
    int groupId;                          // 组序号
    QString groupName;                    // 作动器组名称
    QList<ActuatorParams> actuators;      // 作动器列表
    QString groupType;                    // 组类型 (液压/电动/气动/伺服)
    QString createTime;                   // 创建时间
    QString groupNotes;                   // 组备注
};
```

## 📝 数据填写规范

### 必填字段
- ✅ **组序号**: 必须为正整数，同组内相同
- ✅ **作动器组名称**: 只在组内第一个作动器行显示
- ✅ **作动器序号**: 组内唯一，从1开始递增
- ✅ **作动器序列号**: 全局唯一标识符

### 可选字段
- **Unit类型/名称**: 默认为 "m"/"米"
- **物理参数**: 可以为0或空值
- **控制参数**: 有默认值
- **备注**: 可以为空

### 数据验证规则
1. **数值范围**:
   - 行程、位移: > 0
   - 面积: > 0
   - 频率: > 0
   - 输出倍数: > 0

2. **枚举值**:
   - 作动器类型: "单出杆", "双出杆"
   - Unit类型: "m", "mm", "cm", "inch"
   - 极性: "Positive", "Negative"

3. **字符串格式**:
   - 序列号: 建议使用字母+数字格式 (如: ACT001)
   - 组名称: 不超过50字符

## 🎨 样式格式

### 表头样式
- **字体**: 粗体，11号字
- **颜色**: 白色字体，深蓝色背景 (RGB: 68, 114, 196)
- **对齐**: 水平居中，垂直居中
- **边框**: 细线边框

### 数据样式
- **组名称行**: 浅蓝色背景 (RGB: 231, 243, 255)，粗体
- **普通数据行**: 白色背景，细线边框
- **数值格式**: 保留适当小数位数

## 🔄 导入导出支持

### 导出功能
- ✅ 支持单独作动器工作表导出
- ✅ 支持完整项目导出（硬件树+作动器）
- ✅ 支持多种导出格式 (XLSX, CSV, JSON)

### 导入功能
- ✅ 支持从XLSX文件读取作动器数据
- ✅ 数据验证和错误提示
- ✅ 自动类型转换和格式检查

## 📊 使用示例

### 创建作动器数据
```cpp
// 创建作动器参数
UI::ActuatorParams actuator;
actuator.actuatorId = 1;
actuator.serialNumber = "ACT001";
actuator.type = "单出杆";
actuator.unitType = "m";
actuator.unitName = "米";
actuator.stroke = 0.15;
actuator.displacement = 0.05;
actuator.tensionArea = 0.0314;
actuator.compressionArea = 0.0254;
actuator.polarity = "Positive";
actuator.dither = 5.0;
actuator.frequency = 50.0;
actuator.outputMultiplier = 1.0;
actuator.balance = 2.5;
actuator.notes = "主控制作动器";

// 创建作动器组
UI::ActuatorGroup group;
group.groupId = 1;
group.groupName = "主作动器组";
group.actuators.append(actuator);
```

### 导出到Excel
```cpp
// 导出作动器数据
QList<UI::ActuatorGroup> actuatorGroups;
actuatorGroups.append(group);

XLSDataExporter exporter;
bool success = exporter.exportCompleteProjectWithActuators(
    ui->hardwareTreeWidget, 
    actuatorGroups, 
    "complete_project.xlsx"
);
```

这个格式规范确保了作动器数据的完整性和一致性，支持复杂的层级结构和详细的参数配置。
