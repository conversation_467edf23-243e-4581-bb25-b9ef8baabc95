# 作动器新需求改动_1_2.md

## 📋 **需求变更分析**

### **🔍 新需求字段结构**

**新需求JSON格式：**
```json
{
    "servo_control": {
        "name": "控制量",
        "type": 1,
        "zero_offset": 0,
        "lc_id": 1,
        "station_id": 1,
        "board_id_ao": 1,
        "board_type_ao": 1,
        "port_id_ao": 1,
        "board_id_do": 1,
        "board_type_do": 1,
        "port_id_do": 1,
        "params": {
            "model": "MD500",
            "sn": "123",
            "k": 1.0,
            "b": 0.0,
            "precision": 0.1,
            "polarity": 1,
            "meas_unit": 1,
            "meas_range_min": -100.0,
            "meas_range_max": 100.0,
            "output_signal_unit": 1,
            "output_signal_range_min": -100.0,
            "output_signal_range_max": 100.0
        }
    }
}
```

**界面数据说明：**
- 作动器类型：单出杆(1)、双出杆(2)
- 序列号：只能是字母数字
- 测量单位：m(1)、mm(2)、cm(3)、inch(4)
- 极性：Positive=1, Negative=-1, Both=9, Unknown=0

## 🛠️ **按现有工程流程的修改方案**

### **1. 数据结构重构**

#### **A. 新增枚举定义**
```cpp
// 作动器类型枚举
enum class ActuatorType_1_2 {
    SingleRod = 1,    // 单出杆
    DoubleRod = 2     // 双出杆
};

// 极性枚举
enum class Polarity_1_2 {
    Unknown = 0,      // 未知
    Positive = 1,     // 正极性
    Negative = -1,    // 负极性
    Both = 9          // 双极性
};

// 测量单位枚举
enum class MeasurementUnit_1_2 {
    Meter = 1,        // 米
    Millimeter = 2,   // 毫米
    Centimeter = 3,   // 厘米
    Inch = 4          // 英寸
};
```

#### **B. 重构 ActuatorParams_1_2 结构体**
```cpp
struct ActuatorDetailParams_1_2 {
    QString model;                    // 型号
    QString sn;                      // 序列号
    double k;                        // k系数
    double b;                        // b系数
    double precision;                // 精度
    Polarity_1_2 polarity;          // 极性
    MeasurementUnit_1_2 meas_unit;   // 测量单位
    double meas_range_min;           // 测量下限
    double meas_range_max;           // 测量上限
    int output_signal_unit;          // 输出信号单位
    double output_signal_range_min;  // 输出信号下限
    double output_signal_range_max;  // 输出信号上限
};

struct ActuatorParams_1_2 {
    // 基本信息
    int actuatorId;                  // 内部ID（保持兼容）
    QString name;                    // 控制量名称
    ActuatorType_1_2 type;          // 作动器类型
    double zero_offset;              // 零偏

    // 硬件配置
    int lc_id;                      // 下位机ID
    int station_id;                 // 站点ID
    int board_id_ao;                // 板卡AO ID
    int board_type_ao;              // 板卡AO类型
    int port_id_ao;                 // 端口AO ID
    int board_id_do;                // 板卡DO ID
    int board_type_do;              // 板卡DO类型
    int port_id_do;                 // 端口DO ID

    // 详细参数
    ActuatorDetailParams_1_2 params; // 作动器详细参数

    // 兼容性字段
    QString notes;                   // 备注
};
```

### **2. 界面修改方案**

#### **A. ActuatorDialog_1_2.ui 界面重构**
```
新界面布局：
┌─────────────────────────────────────────┐
│ 基本信息                                  │
│ ├─ 控制量名称: [文本框]                    │
│ ├─ 作动器类型: [下拉框: 单出杆/双出杆]      │
│ └─ 零偏: [数值框]                         │
├─────────────────────────────────────────┤
│ 硬件配置                                  │
│ ├─ 下位机ID: [数值框]  站点ID: [数值框]     │
│ ├─ AO配置: 板卡ID[数值] 类型[数值] 端口[数值] │
│ └─ DO配置: 板卡ID[数值] 类型[数值] 端口[数值] │
├─────────────────────────────────────────┤
│ 作动器参数                                │
│ ├─ 型号: [文本框]  序列号: [文本框]        │
│ ├─ K系数: [数值框]  B系数: [数值框]        │
│ ├─ 精度: [数值框]                         │
│ ├─ 极性: [下拉框: Positive/Negative/Both] │
│ ├─ 测量单位: [下拉框: m/mm/cm/inch]       │
│ ├─ 测量范围: [数值框] ~ [数值框]           │
│ ├─ 输出信号单位: [数值框]                  │
│ └─ 输出信号范围: [数值框] ~ [数值框]       │
└─────────────────────────────────────────┘
```

#### **B. 验证规则更新**
```cpp
// 序列号验证：只能包含字母和数字
bool ActuatorDialog_1_2::validateSerialNumber(const QString& sn) {
    QRegularExpression regex("^[A-Za-z0-9]+$");
    return regex.match(sn).hasMatch();
}

// 作动器类型验证
bool ActuatorDialog_1_2::validateActuatorType(int type) {
    return (type == 1 || type == 2); // 单出杆或双出杆
}

// 极性验证
bool ActuatorDialog_1_2::validatePolarity(int polarity) {
    return (polarity == 0 || polarity == 1 ||
            polarity == -1 || polarity == 9);
}
```

## 🔄 **按现有工程流程的功能修改**

### **3. 流程1：打开工程（Excel导入）**

#### **A. Excel格式更新**
```
新Excel列结构：
| 控制量名称 | 类型 | 零偏 | 下位机ID | 站点ID | AO板卡ID | AO板卡类型 | AO端口ID |
| DO板卡ID | DO板卡类型 | DO端口ID | 型号 | 序列号 | K系数 | B系数 | 精度 |
| 极性 | 测量单位 | 测量下限 | 测量上限 | 输出信号单位 | 输出信号下限 | 输出信号上限 |
```

#### **B. ExcelDataImporter_1_2 更新**
```cpp
class ExcelDataImporter_1_2 {
public:
    // 现有方法保持不变，内部适配新结构
    bool importActuatorDataFromExcel(const QString& filePath);

private:
    // 新增：解析新格式Excel行
    UI::ActuatorParams_1_2 parseActuatorFromExcelRow(
        const QStringList& rowData,
        int rowIndex
    );

    // 新增：验证Excel列格式
    bool validateExcelColumns(const QStringList& headers);

    // 新增：类型转换辅助方法
    ActuatorType_1_2 parseActuatorTypeFromExcel(const QString& typeStr);
    Polarity_1_2 parsePolarityFromExcel(const QString& polarityStr);
    MeasurementUnit_1_2 parseMeasurementUnitFromExcel(const QString& unitStr);
};
```

### **4. 流程2：新建工程**

#### **A. 保持现有新建流程不变**
```cpp
// MainWindow_Qt_Simple::onNewProject() 保持不变
// 只需要确保新建的作动器使用新的数据结构
void MainWindow_Qt_Simple::onNewProject() {
    // 现有逻辑保持不变
    // 内部创建新作动器时使用 ActuatorParams_1_2 新结构
}
```

### **5. 流程3：保存工程（Excel导出）**

#### **A. ExcelDataExporter_1_2 更新**
```cpp
class ExcelDataExporter_1_2 {
public:
    // 现有方法保持不变，内部适配新结构
    bool exportActuatorDataToExcel(const QString& filePath);

private:
    // 新增：将作动器转换为Excel行
    QStringList convertActuatorToExcelRow(const UI::ActuatorParams_1_2& actuator);

    // 新增：生成新格式Excel表头
    QStringList generateExcelHeaders();

    // 新增：类型转换辅助方法
    QString actuatorTypeToExcelString(ActuatorType_1_2 type);
    QString polarityToExcelString(Polarity_1_2 polarity);
    QString measurementUnitToExcelString(MeasurementUnit_1_2 unit);
};
```

### **6. 流程4：导出工程（导出JSON）**

#### **A. JSONDataExporter_1_2 更新**
```cpp
class JSONDataExporter_1_2 {
public:
    // 现有方法保持不变，内部适配新结构
    bool exportProjectToJson(const QString& filePath);

private:
    // 新增：将作动器转换为servo_control格式
    QJsonObject convertActuatorToServoControlJson(
        const UI::ActuatorParams_1_2& actuator
    );

    // 新增：创建params子对象
    QJsonObject createActuatorParamsJson(
        const UI::ActuatorDetailParams_1_2& params
    );

    // 新增：生成完整的工程JSON
    QJsonObject generateProjectJson();
};
```

#### **B. 导出JSON格式（servo_control）**
```cpp
QJsonObject JSONDataExporter_1_2::convertActuatorToServoControlJson(
    const UI::ActuatorParams_1_2& actuator) {

    QJsonObject servoControl;

    // 基本信息
    servoControl["name"] = actuator.name;
    servoControl["type"] = static_cast<int>(actuator.type);
    servoControl["zero_offset"] = actuator.zero_offset;

    // 硬件配置
    servoControl["lc_id"] = actuator.lc_id;
    servoControl["station_id"] = actuator.station_id;
    servoControl["board_id_ao"] = actuator.board_id_ao;
    servoControl["board_type_ao"] = actuator.board_type_ao;
    servoControl["port_id_ao"] = actuator.port_id_ao;
    servoControl["board_id_do"] = actuator.board_id_do;
    servoControl["board_type_do"] = actuator.board_type_do;
    servoControl["port_id_do"] = actuator.port_id_do;

    // 详细参数
    servoControl["params"] = createActuatorParamsJson(actuator.params);

    return servoControl;
}

QJsonObject JSONDataExporter_1_2::createActuatorParamsJson(
    const UI::ActuatorDetailParams_1_2& params) {

    QJsonObject paramsObj;

    paramsObj["model"] = params.model;
    paramsObj["sn"] = params.sn;
    paramsObj["k"] = params.k;
    paramsObj["b"] = params.b;
    paramsObj["precision"] = params.precision;
    paramsObj["polarity"] = static_cast<int>(params.polarity);
    paramsObj["meas_unit"] = static_cast<int>(params.meas_unit);
    paramsObj["meas_range_min"] = params.meas_range_min;
    paramsObj["meas_range_max"] = params.meas_range_max;
    paramsObj["output_signal_unit"] = params.output_signal_unit;
    paramsObj["output_signal_range_min"] = params.output_signal_range_min;
    paramsObj["output_signal_range_max"] = params.output_signal_range_max;

    return paramsObj;
}
```

## 🔧 **数据管理层适配**

### **7. ActuatorDataManager_1_2 更新**

#### **A. 类型转换辅助方法**
```cpp
class ActuatorDataManager_1_2 {
public:
    // 现有接口保持不变，确保功能单一性

    // 新增类型转换辅助方法
    static QString actuatorTypeToString(ActuatorType_1_2 type) {
        switch (type) {
            case ActuatorType_1_2::SingleRod: return "单出杆";
            case ActuatorType_1_2::DoubleRod: return "双出杆";
            default: return "未知";
        }
    }

    static ActuatorType_1_2 stringToActuatorType(const QString& str) {
        if (str == "单出杆" || str == "1") return ActuatorType_1_2::SingleRod;
        if (str == "双出杆" || str == "2") return ActuatorType_1_2::DoubleRod;
        return ActuatorType_1_2::SingleRod; // 默认值
    }

    static QString polarityToString(Polarity_1_2 polarity) {
        switch (polarity) {
            case Polarity_1_2::Positive: return "Positive";
            case Polarity_1_2::Negative: return "Negative";
            case Polarity_1_2::Both: return "Both";
            default: return "Unknown";
        }
    }

    static Polarity_1_2 stringToPolarity(const QString& str) {
        if (str == "Positive" || str == "1") return Polarity_1_2::Positive;
        if (str == "Negative" || str == "-1") return Polarity_1_2::Negative;
        if (str == "Both" || str == "9") return Polarity_1_2::Both;
        return Polarity_1_2::Unknown;
    }

    static QString measurementUnitToString(MeasurementUnit_1_2 unit) {
        switch (unit) {
            case MeasurementUnit_1_2::Meter: return "m";
            case MeasurementUnit_1_2::Millimeter: return "mm";
            case MeasurementUnit_1_2::Centimeter: return "cm";
            case MeasurementUnit_1_2::Inch: return "inch";
            default: return "mm";
        }
    }

    static MeasurementUnit_1_2 stringToMeasurementUnit(const QString& str) {
        if (str == "m" || str == "1") return MeasurementUnit_1_2::Meter;
        if (str == "mm" || str == "2") return MeasurementUnit_1_2::Millimeter;
        if (str == "cm" || str == "3") return MeasurementUnit_1_2::Centimeter;
        if (str == "inch" || str == "4") return MeasurementUnit_1_2::Inch;
        return MeasurementUnit_1_2::Millimeter; // 默认值
    }

    // 验证方法
    bool validateActuatorParams(const ActuatorParams_1_2& params) {
        // 验证序列号格式
        QRegularExpression regex("^[A-Za-z0-9]+$");
        if (!regex.match(params.params.sn).hasMatch()) {
            return false;
        }

        // 验证作动器类型
        if (static_cast<int>(params.type) != 1 && static_cast<int>(params.type) != 2) {
            return false;
        }

        // 验证极性
        int polarity = static_cast<int>(params.params.polarity);
        if (polarity != 0 && polarity != 1 && polarity != -1 && polarity != 9) {
            return false;
        }

        return true;
    }
};
```

## 📊 **兼容性和迁移方案**

### **8. 数据迁移策略**

#### **A. 从旧格式迁移到新格式**
```cpp
class ActuatorDataMigrator_1_2 {
public:
    // 从旧格式转换到新格式
    static ActuatorParams_1_2 migrateFromOldFormat(const OldActuatorParams& oldParams) {
        ActuatorParams_1_2 newParams;

        // 基本信息映射
        newParams.actuatorId = oldParams.actuatorId;
        newParams.name = oldParams.actuatorName.isEmpty() ? "控制量" : oldParams.actuatorName;
        newParams.type = oldParams.type == "双出杆" ? ActuatorType_1_2::DoubleRod : ActuatorType_1_2::SingleRod;
        newParams.zero_offset = 0.0; // 新字段，设置默认值

        // 硬件配置（新字段，设置默认值）
        newParams.lc_id = 1;
        newParams.station_id = 1;
        newParams.board_id_ao = 1;
        newParams.board_type_ao = 1;
        newParams.port_id_ao = 1;
        newParams.board_id_do = 1;
        newParams.board_type_do = 1;
        newParams.port_id_do = 1;

        // 详细参数映射
        newParams.params.model = "MD500"; // 默认型号
        newParams.params.sn = oldParams.serialNumber.isEmpty() ? "123" : oldParams.serialNumber;
        newParams.params.k = 1.0; // 默认值
        newParams.params.b = 0.0; // 默认值
        newParams.params.precision = 0.1; // 默认精度
        newParams.params.polarity = stringToPolarity(oldParams.polarity);
        newParams.params.meas_unit = MeasurementUnit_1_2::Millimeter; // 默认单位
        newParams.params.meas_range_min = -100.0; // 默认范围
        newParams.params.meas_range_max = 100.0;
        newParams.params.output_signal_unit = 1; // 默认输出信号单位
        newParams.params.output_signal_range_min = -100.0; // 默认输出范围
        newParams.params.output_signal_range_max = 100.0;

        // 兼容性字段
        newParams.notes = oldParams.notes;

        return newParams;
    }

    // 批量迁移
    static QList<ActuatorParams_1_2> migrateBatch(const QList<OldActuatorParams>& oldList) {
        QList<ActuatorParams_1_2> newList;
        for (const auto& oldParams : oldList) {
            newList.append(migrateFromOldFormat(oldParams));
        }
        return newList;
    }
};
```

## 🎯 **实施步骤**

### **9. 分阶段实施计划**

#### **阶段1：数据结构更新（不影响现有流程）**
1. 更新 `ActuatorParams_1_2` 结构体定义
2. 添加枚举类型定义
3. 更新 `ActuatorDataManager_1_2` 类型转换方法
4. 实现数据迁移功能

#### **阶段2：界面适配（保持现有操作流程）**
1. 更新 `ActuatorDialog_1_2.ui` 界面布局
2. 更新 `ActuatorDialog_1_2.cpp` 逻辑处理
3. 添加新的验证规则
4. 确保添加/编辑作动器功能正常

#### **阶段3：Excel导入导出适配（保持现有流程）**
1. 更新 `ExcelDataImporter_1_2` 导入逻辑
2. 更新 `ExcelDataExporter_1_2` 导出逻辑
3. 确保"打开工程"和"保存工程"功能正常
4. 测试Excel格式兼容性

#### **阶段4：JSON导出适配（保持现有流程）**
1. 更新 `JSONDataExporter_1_2` 导出逻辑
2. 实现servo_control格式转换
3. 确保"导出工程"功能正常
4. 测试JSON格式正确性

#### **阶段5：全面测试**
1. 测试完整的工程流程：新建→添加作动器→保存→打开→导出
2. 测试数据迁移功能
3. 验证所有功能的单一性和一致性
4. 确保向后兼容性

## 📋 **总结**

### **核心原则：**
- ✅ **保持现有工程流程不变**：打开工程(Excel导入)→新建工程→保存工程(Excel导出)→导出工程(JSON导出)
- ✅ **功能单一性**：每个功能只负责一个职责，不产生多分流程代码
- ✅ **向后兼容**：支持旧数据格式的自动迁移
- ✅ **数据完整性**：满足新需求的所有字段要求
- ✅ **用户体验一致**：界面操作逻辑保持一致

### **关键修改点：**
1. **数据结构**：扩展ActuatorParams_1_2以支持新字段
2. **Excel格式**：更新导入导出的列结构
3. **JSON格式**：输出servo_control格式
4. **界面布局**：重新设计以适应新字段
5. **数据验证**：添加新的验证规则

这个方案确保了在满足新需求的同时，完全保持现有工程流程的完整性和功能的单一性。

## 🎯 **方案1：单个servo_control对象 - 详细实施方案**

### **1. JSON导出格式确定**

#### **A. 标准导出格式**
```json
{
    "servo_control": {
        "name": "控制量",
        "type": 1,
        "zero_offset": 0,
        "lc_id": 1,
        "station_id": 1,
        "board_id_ao": 1,
        "board_type_ao": 1,
        "port_id_ao": 1,
        "board_id_do": 1,
        "board_type_do": 1,
        "port_id_do": 1,
        "params": {
            "model": "MD500",
            "sn": "123",
            "k": 1.0,
            "b": 0.0,
            "precision": 0.1,
            "polarity": 1,
            "meas_unit": 1,
            "meas_range_min": -100.0,
            "meas_range_max": 100.0,
            "output_signal_unit": 1,
            "output_signal_range_min": -100.0,
            "output_signal_range_max": 100.0
        }
    }
}
```

### **2. JSONDataExporter_1_2 类核心方法**

#### **A. 主要导出方法**
```cpp
class JSONDataExporter_1_2 {
public:
    /**
     * @brief 导出单个作动器为servo_control格式
     * @param actuator 作动器参数
     * @param filePath 导出文件路径
     * @return 导出是否成功
     */
    bool exportActuatorAsServoControl(
        const UI::ActuatorParams_1_2& actuator, 
        const QString& filePath
    );
    
    /**
     * @brief 将作动器转换为servo_control JSON对象
     * @param actuator 作动器参数
     * @return servo_control JSON对象
     */
    QJsonObject convertActuatorToServoControlJson(
        const UI::ActuatorParams_1_2& actuator
    );
    
    /**
     * @brief 从servo_control格式导入作动器
     * @param filePath JSON文件路径
     * @return 作动器参数
     */
    UI::ActuatorParams_1_2 importActuatorFromServoControl(
        const QString& filePath
    );
    
    /**
     * @brief 从JSON对象解析servo_control
     * @param rootObj 根JSON对象
     * @return 作动器参数
     */
    UI::ActuatorParams_1_2 parseActuatorFromServoControlJson(
        const QJsonObject& rootObj
    );

private:
    /**
     * @brief 创建params子对象
     * @param params 作动器详细参数
     * @return params JSON对象
     */
    QJsonObject createActuatorParamsJson(
        const UI::ActuatorDetailParams_1_2& params
    );
    
    /**
     * @brief 解析params子对象
     * @param paramsObj params JSON对象
     * @return 作动器详细参数
     */
    UI::ActuatorDetailParams_1_2 parseActuatorParamsFromJson(
        const QJsonObject& paramsObj
    );
    
    /**
     * @brief 验证servo_control JSON格式
     * @param rootObj 根JSON对象
     * @return 验证是否通过
     */
    bool validateServoControlJson(const QJsonObject& rootObj);
};
```

#### **B. 核心实现逻辑**
```cpp
bool JSONDataExporter_1_2::exportActuatorAsServoControl(
    const UI::ActuatorParams_1_2& actuator,
    const QString& filePath) {

    clearError();

    try {
        // 创建根对象
        QJsonObject rootObj;
        rootObj["servo_control"] = convertActuatorToServoControlJson(actuator);

        // 创建JSON文档
        QJsonDocument doc(rootObj);

        // 写入文件
        QFile file(filePath);
        if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
            setError(QString("无法创建文件: %1").arg(filePath));
            return false;
        }

        // 使用缩进格式写入
        QByteArray jsonData = doc.toJson(QJsonDocument::Indented);
        qint64 bytesWritten = file.write(jsonData);
        file.close();

        if (bytesWritten == -1) {
            setError(QString("写入文件失败: %1").arg(filePath));
            return false;
        }

        return true;

    } catch (const std::exception& e) {
        setError(QString("导出过程中发生异常: %1").arg(e.what()));
        return false;
    }
}

QJsonObject JSONDataExporter_1_2::convertActuatorToServoControlJson(
    const UI::ActuatorParams_1_2& actuator) {

    QJsonObject servoControl;

    // 基本信息
    servoControl["name"] = actuator.name;
    servoControl["type"] = static_cast<int>(actuator.type);
    servoControl["zero_offset"] = actuator.zero_offset;

    // 硬件配置
    servoControl["lc_id"] = actuator.lc_id;
    servoControl["station_id"] = actuator.station_id;
    servoControl["board_id_ao"] = actuator.board_id_ao;
    servoControl["board_type_ao"] = actuator.board_type_ao;
    servoControl["port_id_ao"] = actuator.port_id_ao;
    servoControl["board_id_do"] = actuator.board_id_do;
    servoControl["board_type_do"] = actuator.board_type_do;
    servoControl["port_id_do"] = actuator.port_id_do;

    // 详细参数
    servoControl["params"] = createActuatorParamsJson(actuator.params);

    return servoControl;
}

QJsonObject JSONDataExporter_1_2::createActuatorParamsJson(
    const UI::ActuatorDetailParams_1_2& params) {

    QJsonObject paramsObj;

    paramsObj["model"] = params.model;
    paramsObj["sn"] = params.sn;
    paramsObj["k"] = params.k;
    paramsObj["b"] = params.b;
    paramsObj["precision"] = params.precision;
    paramsObj["polarity"] = static_cast<int>(params.polarity);
    paramsObj["meas_unit"] = static_cast<int>(params.meas_unit);
    paramsObj["meas_range_min"] = params.meas_range_min;
    paramsObj["meas_range_max"] = params.meas_range_max;
    paramsObj["output_signal_unit"] = params.output_signal_unit;
    paramsObj["output_signal_range_min"] = params.output_signal_range_min;
    paramsObj["output_signal_range_max"] = params.output_signal_range_max;

    return paramsObj;
}
```

### **3. 导入功能实现**

#### **A. 导入核心方法**
```cpp
UI::ActuatorParams_1_2 JSONDataExporter_1_2::importActuatorFromServoControl(
    const QString& filePath) {

    clearError();
    UI::ActuatorParams_1_2 actuator;

    try {
        // 读取文件
        QFile file(filePath);
        if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
            setError(QString("无法打开文件: %1").arg(filePath));
            return actuator;
        }

        QByteArray jsonData = file.readAll();
        file.close();

        // 解析JSON
        QJsonParseError parseError;
        QJsonDocument doc = QJsonDocument::fromJson(jsonData, &parseError);

        if (parseError.error != QJsonParseError::NoError) {
            setError(QString("JSON解析错误: %1").arg(parseError.errorString()));
            return actuator;
        }

        if (!doc.isObject()) {
            setError("JSON根节点不是对象");
            return actuator;
        }

        QJsonObject rootObj = doc.object();

        // 验证格式
        if (!validateServoControlJson(rootObj)) {
            return actuator; // 错误信息已在validateServoControlJson中设置
        }

        // 解析数据
        actuator = parseActuatorFromServoControlJson(rootObj);

        return actuator;

    } catch (const std::exception& e) {
        setError(QString("导入过程中发生异常: %1").arg(e.what()));
        return actuator;
    }
}

UI::ActuatorParams_1_2 JSONDataExporter_1_2::parseActuatorFromServoControlJson(
    const QJsonObject& rootObj) {

    UI::ActuatorParams_1_2 actuator;

    if (!rootObj.contains("servo_control")) {
        setError("JSON中缺少servo_control节点");
        return actuator;
    }

    QJsonObject servoControlObj = rootObj["servo_control"].toObject();

    // 基本信息
    actuator.name = servoControlObj["name"].toString();
    actuator.type = static_cast<ActuatorType_1_2>(
        servoControlObj["type"].toInt()
    );
    actuator.zero_offset = servoControlObj["zero_offset"].toDouble();

    // 硬件配置
    actuator.lc_id = servoControlObj["lc_id"].toInt();
    actuator.station_id = servoControlObj["station_id"].toInt();
    actuator.board_id_ao = servoControlObj["board_id_ao"].toInt();
    actuator.board_type_ao = servoControlObj["board_type_ao"].toInt();
    actuator.port_id_ao = servoControlObj["port_id_ao"].toInt();
    actuator.board_id_do = servoControlObj["board_id_do"].toInt();
    actuator.board_type_do = servoControlObj["board_type_do"].toInt();
    actuator.port_id_do = servoControlObj["port_id_do"].toInt();

    // 详细参数
    if (servoControlObj.contains("params") && servoControlObj["params"].isObject()) {
        actuator.params = parseActuatorParamsFromJson(
            servoControlObj["params"].toObject()
        );
    }

    return actuator;
}
```

### **4. 验证和错误处理**

#### **A. JSON格式验证**
```cpp
bool JSONDataExporter_1_2::validateServoControlJson(const QJsonObject& rootObj) {
    // 检查是否包含servo_control节点
    if (!rootObj.contains("servo_control")) {
        setError("JSON中缺少servo_control节点");
        return false;
    }

    if (!rootObj["servo_control"].isObject()) {
        setError("servo_control节点不是对象类型");
        return false;
    }

    QJsonObject servoControlObj = rootObj["servo_control"].toObject();

    // 必需字段检查
    QStringList requiredFields = {
        "name", "type", "zero_offset", "lc_id", "station_id",
        "board_id_ao", "board_type_ao", "port_id_ao",
        "board_id_do", "board_type_do", "port_id_do", "params"
    };

    for (const QString& field : requiredFields) {
        if (!servoControlObj.contains(field)) {
            setError(QString("servo_control中缺少必需字段: %1").arg(field));
            return false;
        }
    }

    // 验证params子对象
    if (!servoControlObj["params"].isObject()) {
        setError("params字段不是对象类型");
        return false;
    }

    QJsonObject paramsObj = servoControlObj["params"].toObject();
    QStringList requiredParamsFields = {
        "model", "sn", "k", "b", "precision", "polarity", "meas_unit",
        "meas_range_min", "meas_range_max", "output_signal_unit",
        "output_signal_range_min", "output_signal_range_max"
    };

    for (const QString& field : requiredParamsFields) {
        if (!paramsObj.contains(field)) {
            setError(QString("params中缺少必需字段: %1").arg(field));
            return false;
        }
    }

    // 数据类型验证
    if (!servoControlObj["type"].isDouble() ||
        (servoControlObj["type"].toInt() != 1 && servoControlObj["type"].toInt() != 2)) {
        setError("type字段值无效，必须为1（单出杆）或2（双出杆）");
        return false;
    }

    // 极性验证
    int polarity = paramsObj["polarity"].toInt();
    if (polarity != 0 && polarity != 1 && polarity != -1 && polarity != 9) {
        setError("polarity字段值无效，必须为0、1、-1或9");
        return false;
    }

    return true;
}
```

### **5. 主窗口集成**

#### **A. MainWindow_Qt_Simple 集成方法**
```cpp
class MainWindow_Qt_Simple {
private slots:
    /**
     * @brief 导出选中作动器为servo_control格式
     */
    void onExportActuatorAsServoControl();

    /**
     * @brief 导入servo_control格式作动器
     */
    void onImportActuatorFromServoControl();

private:
    /**
     * @brief 获取当前选中的作动器
     * @return 作动器参数，如果没有选中则返回空对象
     */
    UI::ActuatorParams_1_2 getCurrentSelectedActuator();

    /**
     * @brief 生成servo_control导出文件名
     * @param actuator 作动器参数
     * @return 文件名
     */
    QString generateServoControlFileName(const UI::ActuatorParams_1_2& actuator);
};

void MainWindow_Qt_Simple::onExportActuatorAsServoControl() {
    // 获取当前选中的作动器
    UI::ActuatorParams_1_2 actuator = getCurrentSelectedActuator();
    if (actuator.name.isEmpty()) {
        QMessageBox::warning(this, "导出错误", "请先选择要导出的作动器");
        return;
    }

    // 生成默认文件名
    QString defaultFileName = generateServoControlFileName(actuator);

    // 选择保存路径
    QString filePath = QFileDialog::getSaveFileName(
        this,
        "导出作动器为ServoControl格式",
        defaultFileName,
        "JSON文件 (*.json);;所有文件 (*.*)"
    );

    if (filePath.isEmpty()) {
        return;
    }

    // 执行导出
    JSONDataExporter_1_2 exporter;
    if (exporter.exportActuatorAsServoControl(actuator, filePath)) {
        QMessageBox::information(this, "导出成功",
            QString("作动器已成功导出到:\n%1").arg(filePath));
    } else {
        QMessageBox::critical(this, "导出失败",
            QString("导出失败:\n%1").arg(exporter.getLastError()));
    }
}

QString MainWindow_Qt_Simple::generateServoControlFileName(
    const UI::ActuatorParams_1_2& actuator) {

    QString baseName = QString("servo_control_%1_%2")
        .arg(actuator.name)
        .arg(QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss"));

    // 清理文件名中的非法字符
    baseName = baseName.replace(QRegularExpression("[^a-zA-Z0-9_\\-\\u4e00-\\u9fa5]"), "_");

    return baseName + ".json";
}
```

### **6. 用户界面集成**

#### **A. 右键菜单添加**
```cpp
// 在作动器树形控件的右键菜单中添加
void MainWindow_Qt_Simple::createActuatorContextMenu() {
    QMenu* contextMenu = new QMenu(this);

    // 现有菜单项...

    contextMenu->addSeparator();

    // 新增导出选项
    QAction* exportServoControlAction = new QAction("导出为ServoControl JSON", this);
    exportServoControlAction->setIcon(QIcon(":/icons/export_json.png"));
    connect(exportServoControlAction, &QAction::triggered,
            this, &MainWindow_Qt_Simple::onExportActuatorAsServoControl);
    contextMenu->addAction(exportServoControlAction);

    // 显示菜单
    contextMenu->exec(QCursor::pos());
}
```

#### **B. 主菜单添加**
```cpp
// 在主菜单栏添加导入导出选项
void MainWindow_Qt_Simple::createMenuBar() {
    // 现有菜单...

    // 文件菜单
    QMenu* fileMenu = menuBar()->addMenu("文件");

    // 导入子菜单
    QMenu* importMenu = fileMenu->addMenu("导入");
    QAction* importServoControlAction = new QAction("导入ServoControl JSON", this);
    connect(importServoControlAction, &QAction::triggered,
            this, &MainWindow_Qt_Simple::onImportActuatorFromServoControl);
    importMenu->addAction(importServoControlAction);

    // 导出子菜单
    QMenu* exportMenu = fileMenu->addMenu("导出");
    QAction* exportServoControlAction = new QAction("导出选中作动器为ServoControl", this);
    connect(exportServoControlAction, &QAction::triggered,
            this, &MainWindow_Qt_Simple::onExportActuatorAsServoControl);
    exportMenu->addAction(exportServoControlAction);
}
```

### **7. 实施步骤**

#### **阶段1：核心功能实现**
1. 更新 `JSONDataExporter_1_2` 类，添加servo_control相关方法
2. 实现导出功能：`exportActuatorAsServoControl()`
3. 实现导入功能：`importActuatorFromServoControl()`
4. 添加JSON格式验证

#### **阶段2：界面集成**
1. 在主窗口添加导出/导入菜单项
2. 在作动器右键菜单添加导出选项
3. 实现文件选择对话框
4. 添加成功/失败提示

#### **阶段3：测试验证**
1. 测试单个作动器导出功能
2. 测试导入功能和格式验证
3. 测试错误处理机制
4. 验证JSON格式的正确性

### **8. 兼容性处理方案**

#### **A. 数据迁移策略**
```cpp
class ActuatorDataMigrator_1_2 {
public:
    // 从旧格式转换到新格式
    static ActuatorParams_1_2 migrateFromOldFormat(const OldActuatorParams& oldParams);

    // 从新格式转换到旧格式（向后兼容）
    static OldActuatorParams migrateToOldFormat(const ActuatorParams_1_2& newParams);

    // 批量迁移
    static QList<ActuatorParams_1_2> migrateBatch(const QList<OldActuatorParams>& oldList);
};
```

#### **B. 配置文件版本管理**
```cpp
// 在配置文件中添加版本标识
{
    "version": "1.2",
    "actuator_format": "new",
    "actuators": [...]
}
```

## 📋 **总结**

这个方案确保了：
- ✅ 严格按照要求的servo_control格式导出
- ✅ 支持完整的导入功能
- ✅ 包含完善的验证和错误处理
- ✅ 用户界面友好易用
- ✅ 代码结构清晰可维护
- ✅ 满足新需求的所有字段要求
- ✅ 保持现有代码的兼容性
- ✅ 提供平滑的数据迁移路径
