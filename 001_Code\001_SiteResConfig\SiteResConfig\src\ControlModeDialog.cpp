/**
 * @file ControlModeDialog.cpp
 * @brief 控制模式设置对话框类实现
 * @details 使用Qt Designer设计的控制模式选择对话框实现
 * <AUTHOR> Assistant
 * @date 2025-08-06
 * @version 1.0.0
 */

#include "ControlModeDialog.h"
#include "ui_ControlModeDialog.h"
#include <QtWidgets/QMessageBox>

namespace UI {

ControlModeDialog::ControlModeDialog(QWidget* parent)
    : QDialog(parent)
    , ui(new Ui::ControlModeDialog) {
    
    ui->setupUi(this);
    initializeUI();
    connectSignals();
}

ControlModeDialog::~ControlModeDialog() {
    delete ui;
}

void ControlModeDialog::initializeUI() {
    // 设置窗口大小
    resize(300, 150);
    
    // 添加控制模式选项
    ui->modeComboBox->addItem("力控制");
    ui->modeComboBox->addItem("位移控制");
    ui->modeComboBox->addItem("速度控制");
    
    // 设置默认选择
    ui->modeComboBox->setCurrentText("力控制");
}

void ControlModeDialog::connectSignals() {
    // 重新连接确定按钮，添加验证
    disconnect(ui->okButton, &QPushButton::clicked, this, &QDialog::accept);
    connect(ui->okButton, &QPushButton::clicked, this, &ControlModeDialog::onAcceptClicked);
}

void ControlModeDialog::onAcceptClicked() {
    // 直接接受，不进行验证（根据项目要求）
    accept();
}

ControlModeParams ControlModeDialog::getControlModeParams() const {
    ControlModeParams params;
    
    params.controlMode = ui->modeComboBox->currentText();
    
    return params;
}

} // namespace UI
