@echo off
chcp 65001 >nul
echo ========================================
echo  XLS导出功能编译测试
echo ========================================
echo.

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o
if exist "ui_*.h" del ui_*.h

echo.
echo 检查XLS导出器文件...
if exist "include\XLSDataExporter.h" (
    echo ✅ XLSDataExporter.h 存在
) else (
    echo ❌ XLSDataExporter.h 不存在
    goto :error
)

if exist "src\XLSDataExporter.cpp" (
    echo ✅ XLSDataExporter.cpp 存在
) else (
    echo ❌ XLSDataExporter.cpp 不存在
    goto :error
)

echo.
echo 检查QtXlsxWriter库...
if exist "src\QtXlsxWriter-master\src\xlsx\qtxlsx.pri" (
    echo ✅ QtXlsxWriter库文件存在
) else (
    echo ❌ QtXlsxWriter库文件不存在
    goto :error
)

echo.
echo 生成UI头文件...
uic ui\MainWindow.ui -o ui_MainWindow.h
if errorlevel 1 (
    echo UI文件生成失败！
    pause
    exit /b 1
) else (
    echo ✅ UI头文件生成成功: ui_MainWindow.h
)

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目（包含XLS导出功能）...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo.
    echo 可能的原因：
    echo 1. QtXlsxWriter库编译问题
    echo 2. XLS导出器实现问题
    echo 3. 头文件包含路径问题
    echo 4. 链接器问题
    echo.
    echo 请检查以下文件：
    echo - include/XLSDataExporter.h (XLS导出器头文件)
    echo - src/XLSDataExporter.cpp (XLS导出器实现)
    echo - src/QtXlsxWriter-master/src/xlsx/qtxlsx.pri (QtXlsx库配置)
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！XLS导出功能已集成
    echo ========================================
    
    if exist "SiteResConfig.exe" (
        echo 可执行文件: SiteResConfig.exe
        echo.
        echo ✅ XLS导出功能已成功集成！
        echo.
        echo 📊 新增的Excel导出功能:
        echo ├─ XLSDataExporter类: 完整的Excel读写功能
        echo ├─ 导出硬件树: exportHardwareTree()
        echo ├─ 导出传感器详细信息: exportSensorDetails()
        echo ├─ 导出完整项目: exportCompleteProject()
        echo └─ 从Excel导入: importToHardwareTree()
        echo.
        echo 🎨 Excel格式特性:
        echo ├─ 表头样式: 蓝色背景，白色粗体文字
        echo ├─ 数据格式: 边框线，自动对齐
        echo ├─ 自动列宽: 根据内容自动调整
        echo ├─ 多工作表: 支持硬件配置和传感器详细信息分表
        echo └─ 中文支持: 完整的UTF-8中文支持
        echo.
        echo 🔧 工厂模式集成:
        echo ├─ DataExporterFactory支持XLS格式
        echo ├─ 文件过滤器: "Excel文件 (*.xlsx)"
        echo ├─ 自动格式识别: 根据文件扩展名创建导出器
        echo └─ 统一接口: 与CSV、JSON使用相同的接口
        echo.
        echo 📁 支持的Excel功能:
        echo ├─ 读取功能: 从Excel文件导入硬件配置
        echo ├─ 写入功能: 导出到Excel文件
        echo ├─ 格式验证: 自动验证Excel文件格式
        echo ├─ 错误处理: 完整的异常处理机制
        echo └─ 数据解析: 智能解析tooltip和传感器参数
        echo.
        echo 🚀 使用方法:
        echo.
        echo 1. 直接使用XLSDataExporter:
        echo    auto xlsExporter = std::make_unique^<XLSDataExporter^>();
        echo    xlsExporter-^>exportCompleteProject(treeWidget, "project.xlsx");
        echo.
        echo 2. 使用工厂模式:
        echo    auto exporter = DataExporterFactory::createExporter(
        echo        DataExporterFactory::ExportFormat::XLS);
        echo    exporter-^>exportCompleteProject(treeWidget, "project.xlsx");
        echo.
        echo 3. 根据文件路径自动创建:
        echo    auto exporter = DataExporterFactory::createExporterByFilePath(
        echo        "project.xlsx");
        echo    exporter-^>exportCompleteProject(treeWidget, "project.xlsx");
        echo.
        echo 启动程序测试XLS导出功能...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
        echo 启动程序...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
        echo 启动程序...
        start release\SiteResConfig.exe
    ) else (
        echo 警告: 找不到可执行文件！
    )
)

echo.
echo 📖 XLS导出功能说明:
echo.
echo 🎯 核心特性:
echo - 完整的Excel读写支持
echo - 与现有CSV、JSON导出器架构一致
echo - 支持硬件树结构导出
echo - 支持传感器详细信息导出
echo - 支持从Excel文件导入配置
echo - 自动格式化和样式设置
echo.
echo 📊 Excel文件结构:
echo - 工作表1: 硬件配置（硬件树结构）
echo - 工作表2: 传感器详细配置（可选）
echo - 表头信息: 项目信息、导出时间等
echo - 数据区域: 结构化的配置数据
echo.
echo 🔧 技术实现:
echo - 基于QtXlsxWriter库
echo - 实现IDataExporter接口
echo - 集成到DataExporterFactory
echo - 支持异常处理和错误报告
echo.
pause

:error
echo.
echo ❌ 编译测试失败！
echo 请检查XLS导出器文件是否正确创建。
pause
exit /b 1
