# 📁 CSV文件存储路径管理指南

## 🎯 **概述**

CSV路径管理系统为SiteResConfig项目提供了统一、便捷的CSV文件存储路径管理功能，默认存储路径为exe同目录下的`实验工程`文件夹。

## 📂 **默认路径结构**

```
SiteResConfig.exe所在目录/
└── 实验工程/                    # 默认CSV存储根目录
    ├── 项目文件.csv             # 项目配置文件
    ├── 配置/                    # 配置文件子目录
    │   ├── 系统配置.csv
    │   └── 用户设置.csv
    ├── 数据/                    # 实验数据子目录
    │   ├── 实验数据_2025-08-11_14-30-25.csv
    │   └── 测试结果_2025-08-11.csv
    ├── 日志/                    # 日志文件子目录
    │   └── 操作日志.csv
    └── 报告/                    # 报告文件子目录
        └── 项目状态.csv
```

## 🔧 **核心功能**

### **1. 路径获取方法**

#### **GetDefaultCSVPath()**
```cpp
QString csvPath = mainWindow->GetDefaultCSVPath();
// 返回: "D:\MyApp\实验工程"
```

#### **GetExperimentProjectPath()**
```cpp
QString projectPath = mainWindow->GetExperimentProjectPath();
// 返回: "D:\MyApp\实验工程" (与GetDefaultCSVPath相同)
```

### **2. 目录管理方法**

#### **EnsureCSVDirectoryExists()**
```cpp
bool success = mainWindow->EnsureCSVDirectoryExists();
if (success) {
    // 目录已存在或创建成功
}
```

### **3. 文件路径生成方法**

#### **GenerateCSVFilePath()**
```cpp
// 基础用法
QString path1 = mainWindow->GenerateCSVFilePath("test.csv");
// 返回: "D:\MyApp\实验工程\test.csv"

// 带子目录
QString path2 = mainWindow->GenerateCSVFilePath("config.csv", "配置");
// 返回: "D:\MyApp\实验工程\配置\config.csv"
```

#### **GenerateTimestampedFileName()**
```cpp
// 包含日期和时间
QString name1 = mainWindow->GenerateTimestampedFileName("实验数据", true);
// 返回: "实验数据_2025-08-11_14-30-25.csv"

// 只包含日期
QString name2 = mainWindow->GenerateTimestampedFileName("配置备份", false);
// 返回: "配置备份_2025-08-11.csv"
```

## 🚀 **便捷方法**

### **1. 快速保存项目**

#### **QuickSaveProjectToCSV()**
```cpp
// 使用默认设置
QString savedPath;
bool success = mainWindow->QuickSaveProjectToCSV(&savedPath);

// 自定义文件名
bool success = mainWindow->QuickSaveProjectToCSV(&savedPath, "我的实验", true);

// 不使用时间戳
bool success = mainWindow->QuickSaveProjectToCSV(&savedPath, "标准配置", false);
```

### **2. 导出数据到CSV**

#### **ExportDataToCSV()**
```cpp
// 准备数据
QVector<QStringList> data;
data.append(QStringList() << "时间" << "位移" << "载荷");
data.append(QStringList() << "0.0" << "0.0" << "0.0");
data.append(QStringList() << "0.1" << "1.5" << "150.2");

// 导出到主目录
bool success1 = mainWindow->ExportDataToCSV(data, "实验数据.csv");

// 导出到子目录
bool success2 = mainWindow->ExportDataToCSV(data, "测试数据.csv", "测试结果");
```

## 📋 **使用场景**

### **场景1：项目配置保存**
```cpp
// 快速保存当前项目配置
QString savedPath;
if (mainWindow->QuickSaveProjectToCSV(&savedPath, "项目配置", true)) {
    qDebug() << "项目保存成功:" << savedPath;
}
```

### **场景2：实验数据导出**
```cpp
// 收集实验数据
QVector<QStringList> experimentData;
experimentData.append(QStringList() << "序号" << "时间" << "位移" << "载荷" << "应变");

// 添加数据行...
for (int i = 0; i < dataPoints.size(); ++i) {
    experimentData.append(QStringList() 
                         << QString::number(i+1)
                         << QString::number(dataPoints[i].time)
                         << QString::number(dataPoints[i].displacement)
                         << QString::number(dataPoints[i].force)
                         << QString::number(dataPoints[i].strain));
}

// 导出到数据目录
QString fileName = mainWindow->GenerateTimestampedFileName("实验数据", true);
bool success = mainWindow->ExportDataToCSV(experimentData, fileName, "数据");
```

### **场景3：配置文件管理**
```cpp
// 系统配置导出
QVector<QStringList> configData;
configData.append(QStringList() << "参数名" << "参数值" << "描述");
configData.append(QStringList() << "采样频率" << "1000" << "Hz");
configData.append(QStringList() << "最大载荷" << "10000" << "N");

// 保存到配置目录
bool success = mainWindow->ExportDataToCSV(configData, "系统配置.csv", "配置");
```

### **场景4：报告生成**
```cpp
// 生成项目状态报告
QVector<QStringList> reportData;
reportData.append(QStringList() << "项目名称" << "状态" << "进度" << "完成时间");
reportData.append(QStringList() << "实验A" << "已完成" << "100%" << "2025-08-11");
reportData.append(QStringList() << "实验B" << "进行中" << "75%" << "预计2025-08-15");

// 生成带时间戳的报告文件
QString reportName = mainWindow->GenerateTimestampedFileName("项目状态报告", false);
bool success = mainWindow->ExportDataToCSV(reportData, reportName, "报告");
```

## ⚙️ **高级用法**

### **1. 自定义子目录结构**
```cpp
// 创建多级子目录
QString deepPath = mainWindow->GenerateCSVFilePath("data.csv", "实验/2025年/8月");
// 自动创建: 实验工程/实验/2025年/8月/data.csv
```

### **2. 批量文件操作**
```cpp
// 批量导出多个数据集
QStringList dataTypes = {"位移数据", "载荷数据", "应变数据"};
for (const QString& type : dataTypes) {
    QString fileName = mainWindow->GenerateTimestampedFileName(type, true);
    QString filePath = mainWindow->GenerateCSVFilePath(fileName, "批量导出");
    
    // 导出对应的数据...
    mainWindow->ExportDataToCSV(getDataByType(type), fileName, "批量导出");
}
```

### **3. 备份和版本管理**
```cpp
// 创建配置备份
QString backupName = mainWindow->GenerateTimestampedFileName("配置备份", true);
QString backupPath = mainWindow->GenerateCSVFilePath(backupName, "备份");

// 保存当前配置
mainWindow->QuickSaveProjectToCSV(&backupPath, "配置备份", true);
```

## 🛡️ **错误处理**

### **目录创建失败**
```cpp
if (!mainWindow->EnsureCSVDirectoryExists()) {
    QMessageBox::warning(this, "警告", "无法创建CSV存储目录，请检查磁盘权限");
    return;
}
```

### **文件保存失败**
```cpp
QString savedPath;
if (!mainWindow->QuickSaveProjectToCSV(&savedPath)) {
    QMessageBox::critical(this, "错误", "项目保存失败，请检查磁盘空间和权限");
    return;
}
```

### **数据导出失败**
```cpp
if (!mainWindow->ExportDataToCSV(data, fileName)) {
    // 检查CSV管理器的错误信息
    QString error = csvManager->getErrorString();
    QMessageBox::critical(this, "导出失败", QString("数据导出失败：%1").arg(error));
}
```

## 📊 **最佳实践**

### **1. 文件命名规范**
- 使用有意义的文件名
- 重要文件使用时间戳
- 按类型分类到不同子目录

### **2. 目录组织**
- 配置文件 → `配置/` 目录
- 实验数据 → `数据/` 目录  
- 日志文件 → `日志/` 目录
- 报告文件 → `报告/` 目录

### **3. 性能优化**
- 大量文件操作前先调用`EnsureCSVDirectoryExists()`
- 使用批量操作减少磁盘I/O
- 定期清理过期的临时文件

## ✅ **总结**

CSV路径管理系统提供了：

1. **统一的存储路径** - 所有CSV文件集中管理
2. **自动目录创建** - 无需手动创建目录结构
3. **灵活的文件命名** - 支持时间戳和自定义命名
4. **便捷的操作接口** - 简化常见的CSV操作
5. **完善的错误处理** - 提供详细的错误信息

这个系统让CSV文件管理变得简单、高效、可靠！
