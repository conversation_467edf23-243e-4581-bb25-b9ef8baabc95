#############################################################################
# Makefile for building: SiteResConfig
# Generated by qmake (3.1) (Qt 5.14.2)
# Project:  SiteResConfig_Simple.pro
# Template: app
# Command: D:\Qt\Qt5.14.2\5.14.2\mingw73_32\bin\qmake.exe -o Makefile SiteResConfig_Simple.pro
#############################################################################

MAKEFILE      = Makefile

EQ            = =

first: release
install: release-install
uninstall: release-uninstall
QMAKE         = D:\Qt\Qt5.14.2\5.14.2\mingw73_32\bin\qmake.exe
DEL_FILE      = del
CHK_DIR_EXISTS= if not exist
MKDIR         = mkdir
COPY          = copy /y
COPY_FILE     = copy /y
COPY_DIR      = xcopy /s /q /y /i
INSTALL_FILE  = copy /y
INSTALL_PROGRAM = copy /y
INSTALL_DIR   = xcopy /s /q /y /i
QINSTALL      = D:\Qt\Qt5.14.2\5.14.2\mingw73_32\bin\qmake.exe -install qinstall
QINSTALL_PROGRAM = D:\Qt\Qt5.14.2\5.14.2\mingw73_32\bin\qmake.exe -install qinstall -exe
DEL_FILE      = del
SYMLINK       = $(QMAKE) -install ln -f -s
DEL_DIR       = rmdir
MOVE          = move
IDC           = idc
IDL           = midl
ZIP           = zip -r -9
DEF_FILE      = 
RES_FILE      = 
SED           = $(QMAKE) -install sed
MOVE          = move
SUBTARGETS    =  \
		release \
		debug


release: FORCE
	$(MAKE) -f $(MAKEFILE).Release
release-make_first: FORCE
	$(MAKE) -f $(MAKEFILE).Release 
release-all: FORCE
	$(MAKE) -f $(MAKEFILE).Release all
release-clean: FORCE
	$(MAKE) -f $(MAKEFILE).Release clean
release-distclean: FORCE
	$(MAKE) -f $(MAKEFILE).Release distclean
release-install: FORCE
	$(MAKE) -f $(MAKEFILE).Release install
release-uninstall: FORCE
	$(MAKE) -f $(MAKEFILE).Release uninstall
debug: FORCE
	$(MAKE) -f $(MAKEFILE).Debug
debug-make_first: FORCE
	$(MAKE) -f $(MAKEFILE).Debug 
debug-all: FORCE
	$(MAKE) -f $(MAKEFILE).Debug all
debug-clean: FORCE
	$(MAKE) -f $(MAKEFILE).Debug clean
debug-distclean: FORCE
	$(MAKE) -f $(MAKEFILE).Debug distclean
debug-install: FORCE
	$(MAKE) -f $(MAKEFILE).Debug install
debug-uninstall: FORCE
	$(MAKE) -f $(MAKEFILE).Debug uninstall

Makefile: SiteResConfig_Simple.pro D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/win32-g++/qmake.conf D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/spec_pre.prf \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/qdevice.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/device_config.prf \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/common/sanitize.conf \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/common/gcc-base.conf \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/common/g++-base.conf \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/common/angle.conf \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/win32/windows_vulkan_sdk.prf \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/common/windows-vulkan.conf \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/common/g++-win32.conf \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/common/windows-desktop.conf \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/qconfig.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3danimation.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3danimation_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dcore.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dcore_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dextras.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dextras_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dinput.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dinput_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dlogic.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dlogic_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dquick.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dquick_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dquickanimation.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dquickanimation_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dquickextras.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dquickextras_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dquickinput.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dquickinput_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dquickrender.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dquickrender_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dquickscene2d.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dquickscene2d_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3drender.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3drender_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_accessibility_support_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_axbase.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_axbase_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_axcontainer.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_axcontainer_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_axserver.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_axserver_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_bluetooth.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_bluetooth_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_bodymovin_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_bootstrap_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_charts.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_charts_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_concurrent.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_concurrent_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_core.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_core_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_datavisualization.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_datavisualization_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_dbus.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_dbus_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_designer.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_designer_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_designercomponents_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_devicediscovery_support_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_edid_support_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_egl_support_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_fb_support_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_fontdatabase_support_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_gamepad.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_gamepad_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_gui.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_gui_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_help.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_help_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_location.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_location_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_multimedia.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_multimedia_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_multimediawidgets.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_multimediawidgets_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_network.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_network_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_networkauth.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_networkauth_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_nfc.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_nfc_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_opengl.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_opengl_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_openglextensions.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_openglextensions_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_packetprotocol_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_platformcompositor_support_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_positioning.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_positioning_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_positioningquick.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_positioningquick_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_printsupport.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_printsupport_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_purchasing.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_purchasing_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_qml.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_qml_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_qmldebug_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_qmldevtools_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_qmlmodels.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_qmlmodels_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_qmltest.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_qmltest_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_qmlworkerscript.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_qmlworkerscript_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_qtmultimediaquicktools_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quick.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quick3d.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quick3d_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quick3dassetimport.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quick3dassetimport_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quick3drender.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quick3drender_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quick3druntimerender.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quick3druntimerender_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quick3dutils.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quick3dutils_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quick_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quickcontrols2.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quickcontrols2_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quickparticles_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quickshapes_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quicktemplates2.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quicktemplates2_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quickwidgets.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quickwidgets_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_remoteobjects.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_remoteobjects_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_repparser.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_repparser_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_script.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_script_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_scripttools.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_scripttools_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_scxml.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_scxml_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_sensors.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_sensors_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_serialbus.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_serialbus_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_serialport.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_serialport_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_sql.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_sql_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_svg.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_svg_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_testlib.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_testlib_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_texttospeech.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_texttospeech_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_theme_support_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_uiplugin.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_uitools.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_uitools_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_virtualkeyboard.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_virtualkeyboard_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_webchannel.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_webchannel_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_websockets.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_websockets_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_widgets.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_widgets_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_windowsuiautomation_support_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_winextras.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_winextras_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_xml.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_xml_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_xmlpatterns.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_xmlpatterns_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_zlib_private.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/qt_functions.prf \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/qt_config.prf \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/win32-g++/qmake.conf \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/spec_post.prf \
		../.qmake.stash \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/exclusive_builds.prf \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/toolchain.prf \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/default_pre.prf \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/win32/default_pre.prf \
		src/QtXlsxWriter-master/src/xlsx/qtxlsx.pri \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/resolve_config.prf \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/exclusive_builds_post.prf \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/default_post.prf \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/win32/windows.prf \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/precompile_header.prf \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/warn_on.prf \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/qt.prf \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/resources_functions.prf \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/resources.prf \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/moc.prf \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/win32/opengl.prf \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/uic.prf \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/qmake_use.prf \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/file_copies.prf \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/testcase_targets.prf \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/exceptions.prf \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/yacc.prf \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/lex.prf \
		SiteResConfig_Simple.pro \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/lib/Qt5Widgets.prl \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/lib/Qt5Gui.prl \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/lib/Qt5Core.prl \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/lib/qtmain.prl \
		D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/build_pass.prf \
		resources.qrc
	$(QMAKE) -o Makefile SiteResConfig_Simple.pro
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/spec_pre.prf:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/qdevice.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/device_config.prf:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/common/sanitize.conf:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/common/gcc-base.conf:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/common/g++-base.conf:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/common/angle.conf:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/win32/windows_vulkan_sdk.prf:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/common/windows-vulkan.conf:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/common/g++-win32.conf:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/common/windows-desktop.conf:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/qconfig.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3danimation.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3danimation_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dcore.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dcore_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dextras.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dextras_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dinput.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dinput_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dlogic.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dlogic_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dquick.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dquick_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dquickanimation.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dquickanimation_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dquickextras.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dquickextras_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dquickinput.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dquickinput_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dquickrender.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dquickrender_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dquickscene2d.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3dquickscene2d_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3drender.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_3drender_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_accessibility_support_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_axbase.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_axbase_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_axcontainer.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_axcontainer_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_axserver.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_axserver_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_bluetooth.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_bluetooth_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_bodymovin_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_bootstrap_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_charts.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_charts_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_concurrent.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_concurrent_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_core.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_core_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_datavisualization.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_datavisualization_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_dbus.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_dbus_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_designer.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_designer_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_designercomponents_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_devicediscovery_support_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_edid_support_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_egl_support_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_fb_support_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_fontdatabase_support_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_gamepad.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_gamepad_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_gui.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_gui_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_help.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_help_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_location.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_location_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_multimedia.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_multimedia_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_multimediawidgets.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_multimediawidgets_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_network.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_network_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_networkauth.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_networkauth_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_nfc.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_nfc_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_opengl.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_opengl_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_openglextensions.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_openglextensions_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_packetprotocol_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_platformcompositor_support_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_positioning.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_positioning_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_positioningquick.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_positioningquick_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_printsupport.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_printsupport_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_purchasing.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_purchasing_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_qml.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_qml_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_qmldebug_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_qmldevtools_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_qmlmodels.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_qmlmodels_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_qmltest.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_qmltest_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_qmlworkerscript.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_qmlworkerscript_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_qtmultimediaquicktools_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quick.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quick3d.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quick3d_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quick3dassetimport.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quick3dassetimport_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quick3drender.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quick3drender_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quick3druntimerender.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quick3druntimerender_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quick3dutils.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quick3dutils_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quick_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quickcontrols2.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quickcontrols2_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quickparticles_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quickshapes_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quicktemplates2.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quicktemplates2_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quickwidgets.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_quickwidgets_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_remoteobjects.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_remoteobjects_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_repparser.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_repparser_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_script.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_script_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_scripttools.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_scripttools_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_scxml.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_scxml_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_sensors.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_sensors_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_serialbus.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_serialbus_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_serialport.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_serialport_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_sql.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_sql_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_svg.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_svg_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_testlib.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_testlib_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_texttospeech.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_texttospeech_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_theme_support_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_uiplugin.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_uitools.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_uitools_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_virtualkeyboard.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_virtualkeyboard_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_webchannel.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_webchannel_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_websockets.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_websockets_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_widgets.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_widgets_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_windowsuiautomation_support_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_winextras.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_winextras_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_xml.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_xml_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_xmlpatterns.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_xmlpatterns_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/modules/qt_lib_zlib_private.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/qt_functions.prf:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/qt_config.prf:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/win32-g++/qmake.conf:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/spec_post.prf:
../.qmake.stash:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/exclusive_builds.prf:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/toolchain.prf:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/default_pre.prf:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/win32/default_pre.prf:
src/QtXlsxWriter-master/src/xlsx/qtxlsx.pri:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/resolve_config.prf:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/exclusive_builds_post.prf:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/default_post.prf:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/win32/windows.prf:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/precompile_header.prf:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/warn_on.prf:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/qt.prf:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/resources_functions.prf:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/resources.prf:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/moc.prf:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/win32/opengl.prf:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/uic.prf:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/qmake_use.prf:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/file_copies.prf:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/testcase_targets.prf:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/exceptions.prf:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/yacc.prf:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/lex.prf:
SiteResConfig_Simple.pro:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/lib/Qt5Widgets.prl:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/lib/Qt5Gui.prl:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/lib/Qt5Core.prl:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/lib/qtmain.prl:
D:/Qt/Qt5.14.2/5.14.2/mingw73_32/mkspecs/features/build_pass.prf:
resources.qrc:
qmake: FORCE
	@$(QMAKE) -o Makefile SiteResConfig_Simple.pro

qmake_all: FORCE

make_first: release-make_first debug-make_first  FORCE
all: release-all debug-all  FORCE
clean: release-clean debug-clean  FORCE
distclean: release-distclean debug-distclean  FORCE
	-$(DEL_FILE) Makefile

release-mocclean:
	$(MAKE) -f $(MAKEFILE).Release mocclean
debug-mocclean:
	$(MAKE) -f $(MAKEFILE).Debug mocclean
mocclean: release-mocclean debug-mocclean

release-mocables:
	$(MAKE) -f $(MAKEFILE).Release mocables
debug-mocables:
	$(MAKE) -f $(MAKEFILE).Debug mocables
mocables: release-mocables debug-mocables

check: first

benchmark: first
FORCE:

$(MAKEFILE).Release: Makefile
$(MAKEFILE).Debug: Makefile
