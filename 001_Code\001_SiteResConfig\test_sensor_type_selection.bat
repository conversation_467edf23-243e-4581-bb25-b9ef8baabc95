@echo off
echo ========================================
echo  传感器类型选择功能测试
echo ========================================

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o
if exist "ui_*.h" del ui_*.h

echo.
echo 生成UI头文件...
uic ui\MainWindow.ui -o ui_MainWindow.h
if errorlevel 1 (
    echo UI文件生成失败！
    pause
    exit /b 1
) else (
    echo ✅ UI头文件生成成功: ui_MainWindow.h
)

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo.
    echo 新增功能：
    echo ✅ 传感器类型选择对话框
    echo ✅ 智能型号匹配
    echo ✅ 专业传感器类型列表
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！传感器类型选择功能已实现
    echo ========================================
    
    if exist "SiteResConfig.exe" (
        echo 可执行文件: SiteResConfig.exe
        echo.
        echo ✅ 传感器类型选择功能已实现！
        echo.
        echo 📋 传感器类型选择对话框:
        echo ┌─────────────────────────────────────────┐
        echo │                Force                    │
        echo ├─────────────────────────────────────────┤
        echo │ Please select an item from the          │
        echo │ following list.                         │
        echo │                                         │
        echo │ ┌─────────────────────────────────────┐ │
        echo │ │ Axial Gage                          │ │
        echo │ │ Biaxial Gage                        │ │
        echo │ │ Displacement Transducer             │ │
        echo │ │ Load Cell                           │ │
        echo │ │ Strain                              │ │
        echo │ │ Pressure Cell                       │ │
        echo │ │ Rectangular Rosette                 │ │
        echo │ │ Rotational Transducer               │ │
        echo │ │ Strain Gage                         │ │
        echo │ │ Thermocouple                        │ │
        echo │ └─────────────────────────────────────┘ │
        echo │                                         │
        echo │              [OK] [Cancel]              │
        echo └─────────────────────────────────────────┘
        echo.
        echo 🎯 不同传感器组的类型选项:
        echo.
        echo 📊 载荷_传感器组:
        echo ├─ Axial Gage (轴向应变片)
        echo ├─ Biaxial Gage (双轴应变片)
        echo ├─ Displacement Transducer (位移传感器)
        echo ├─ Load Cell (载荷传感器)
        echo ├─ Strain (应变)
        echo ├─ Pressure Cell (压力传感器)
        echo ├─ Rectangular Rosette (矩形应变花)
        echo ├─ Rotational Transducer (旋转传感器)
        echo ├─ Strain Gage (应变片)
        echo └─ Thermocouple (热电偶)
        echo.
        echo 📏 位置_传感器组:
        echo ├─ Displacement Transducer (位移传感器)
        echo ├─ Rotational Transducer (旋转传感器)
        echo ├─ Linear Encoder (线性编码器)
        echo ├─ LVDT (线性可变差动变压器)
        echo └─ Potentiometer (电位器)
        echo.
        echo 🔧 压力_传感器组:
        echo ├─ Pressure Cell (压力传感器)
        echo ├─ Pressure Transducer (压力变送器)
        echo └─ Vacuum Sensor (真空传感器)
        echo.
        echo 🌡️ 温度_传感器组:
        echo ├─ Thermocouple (热电偶)
        echo ├─ RTD (热电阻)
        echo └─ Thermistor (热敏电阻)
        echo.
        echo 🔧 智能型号匹配功能:
        echo.
        echo 📊 Load Cell 型号:
        echo ├─ LCF-500N (500牛顿载荷传感器)
        echo ├─ LCF-1kN (1千牛载荷传感器)
        echo ├─ LCF-5kN (5千牛载荷传感器)
        echo ├─ LCF-10kN (10千牛载荷传感器)
        echo ├─ LCF-50kN (50千牛载荷传感器)
        echo └─ LCF-100kN (100千牛载荷传感器)
        echo.
        echo 📏 Displacement Transducer 型号:
        echo ├─ LVDT-10mm (10毫米位移传感器)
        echo ├─ LVDT-25mm (25毫米位移传感器)
        echo ├─ LVDT-50mm (50毫米位移传感器)
        echo ├─ LVDT-100mm (100毫米位移传感器)
        echo └─ LVDT-200mm (200毫米位移传感器)
        echo.
        echo 📊 Strain Gage 型号:
        echo ├─ SG-120Ω (120欧姆应变片)
        echo ├─ SG-350Ω (350欧姆应变片)
        echo └─ SG-1000Ω (1000欧姆应变片)
        echo.
        echo 🔧 Pressure Cell 型号:
        echo ├─ PC-100bar (100巴压力传感器)
        echo ├─ PC-300bar (300巴压力传感器)
        echo └─ PC-500bar (500巴压力传感器)
        echo.
        echo 🌡️ Thermocouple 型号:
        echo ├─ TC-K型 (K型热电偶)
        echo ├─ TC-J型 (J型热电偶)
        echo └─ TC-T型 (T型热电偶)
        echo.
        echo 🎯 完整的操作流程:
        echo.
        echo 1️⃣ 创建传感器组:
        echo   - 右键"传感器" → "新建" → "传感器组"
        echo   - 选择"载荷"，创建"载荷_传感器组"
        echo.
        echo 2️⃣ 选择传感器类型:
        echo   - 右键"载荷_传感器组" → "新建" → "传感器"
        echo   - 弹出"Force"对话框
        echo   - 从列表中选择"Load Cell"
        echo   - 点击OK确认
        echo.
        echo 3️⃣ 填写传感器参数:
        echo   - 序列号: 传感器_000001 (自动生成)
        echo   - 类型: Load Cell (自动设置)
        echo   - 型号: LCF-100kN (智能推荐)
        echo   - 量程: ±100kN
        echo   - 精度: ±0.1%FS
        echo   - 点击确定创建
        echo.
        echo 4️⃣ 验证创建结果:
        echo   - 传感器节点显示: "传感器_000001 [Load Cell]"
        echo   - 工具提示显示完整参数信息
        echo   - 系统日志记录创建信息
        echo.
        echo 💡 功能特色:
        echo.
        echo ✅ 专业术语:
        echo - 使用国际标准的传感器类型名称
        echo - 符合工程测试领域的专业术语
        echo - 便于与国际设备厂商对接
        echo.
        echo ✅ 智能匹配:
        echo - 根据选择的传感器类型智能推荐型号
        echo - 不同类型显示对应的专业型号
        echo - 避免型号选择错误
        echo.
        echo ✅ 分类清晰:
        echo - 不同传感器组显示对应的类型选项
        echo - 载荷组主要显示力学相关传感器
        echo - 位置组主要显示位移相关传感器
        echo.
        echo 启动程序测试传感器类型选择功能...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
        echo 启动程序...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
        echo 启动程序...
        start release\SiteResConfig.exe
    ) else (
        echo 警告: 找不到可执行文件！
    )
)

echo.
echo 📖 传感器类型选择功能测试指南:
echo.
echo 🎯 完整测试流程:
echo.
echo 1️⃣ 测试载荷传感器组:
echo   - 创建"载荷_传感器组"
echo   - 右键组 → "新建" → "传感器"
echo   - 验证弹出"Force"对话框
echo   - 验证显示载荷相关传感器类型
echo   - 选择"Load Cell"
echo   - 验证型号选项为载荷传感器型号
echo.
echo 2️⃣ 测试位置传感器组:
echo   - 创建"位置_传感器组"
echo   - 验证显示位置相关传感器类型
echo   - 选择"Displacement Transducer"
echo   - 验证型号选项为位移传感器型号
echo.
echo 3️⃣ 测试其他传感器组:
echo   - 测试压力、温度传感器组
echo   - 验证每种组显示对应的传感器类型
echo   - 验证型号匹配功能正常
echo.
echo 4️⃣ 验证取消操作:
echo   - 在类型选择对话框点击Cancel
echo   - 验证操作正确取消，不创建传感器
echo.
echo 🔍 验证要点:
echo - 类型选择对话框正确弹出
echo - 不同组显示对应的传感器类型
echo - 选择的类型正确传递到参数界面
echo - 型号选择根据类型智能匹配
echo - 传感器创建信息正确记录
echo - 取消操作正常工作
echo.
pause
