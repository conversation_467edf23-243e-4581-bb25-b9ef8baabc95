#ifndef DATACHANGELISTENER_H
#define DATACHANGELISTENER_H

#include <QObject>
#include <QString>
#include "SensorDataManager_1_2.h"
#include "ActuatorDataManager_1_2.h"
#include "DetailInfoPanel.h"

// 前向声明
class ActuatorViewModel1_2;

/**
 * @brief 数据变化监听器类
 * @details 监听数据管理器的信号，自动更新详细信息面板显示
 */
class DataChangeListener : public QObject
{
    Q_OBJECT

public:
    explicit DataChangeListener(QObject *parent = nullptr);
    ~DataChangeListener();

    // 设置详细信息面板
    void setDetailInfoPanel(DetailInfoPanel* panel);
    
    // 连接数据管理器信号
    void connectSensorDataManager(SensorDataManager_1_2* manager);
    void connectActuatorDataManager(ActuatorDataManager_1_2* manager);
    void connectActuatorViewModel(ActuatorViewModel1_2* viewModel);
    
    // 断开所有连接
    void disconnectAll();

private slots:
    // 传感器数据变化处理
    void onSensorDataChanged(const QString& serialNumber, const QString& operation);
    void onSensorGroupDataChanged(int groupId, const QString& operation);
    void onSensorAssociationChanged(const QString& serialNumber, const QString& channelName);
    
    // 作动器数据变化处理
    void onActuatorDataChanged(const QString& serialNumber, const QString& operation);
    void onActuatorGroupDataChanged(int groupId, const QString& operation);
    void onActuatorAssociationChanged(const QString& serialNumber, const QString& channelName);
    
    // 错误处理
    void onSensorError(const QString& error);
    void onActuatorError(const QString& error);

private:
    DetailInfoPanel* detailInfoPanel_;
    // 数据管理器指针
    SensorDataManager_1_2* sensorDataManager_;
    ActuatorDataManager_1_2* actuatorDataManager_;
    ActuatorViewModel1_2* actuatorViewModel_; // 🆕 新增：作动器视图模型
    bool isConnected_; // 🆕 新增：连接状态标志
    
    // 🆕 新增：统一的详细信息面板检查函数
    bool isDetailInfoPanelValid();
    
    // 🆕 新增：专门用于lambda表达式的检查函数
    bool isDetailInfoPanelValidInLambda() const;
    
    // 刷新详细信息面板
    void refreshDetailInfoPanel();
    
    // 检查是否需要更新当前信息
    bool shouldUpdateCurrentInfo(const QString& serialNumber, const QString& operation);
    
    // 🆕 新增：断开所有信号连接
    void disconnectAllSignals();
};

#endif // DATACHANGELISTENER_H 
