@echo off
echo ========================================
echo  增强新建工程功能测试
echo ========================================

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o
if exist "ui_*.h" del ui_*.h

echo.
echo 生成UI头文件...
uic ui\MainWindow.ui -o ui_MainWindow.h
if errorlevel 1 (
    echo UI文件生成失败！
    pause
    exit /b 1
) else (
    echo ✅ UI头文件生成成功: ui_MainWindow.h
)

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo.
    echo 请检查以下可能的问题：
    echo 1. OnNewProject函数实现
    echo 2. 信号连接重复问题
    echo 3. QInputDialog和QFileDialog使用
    echo 4. 字符串转换问题
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！增强新建工程功能已实现
    echo ========================================
    
    if exist "SiteResConfig.exe" (
        echo 可执行文件: SiteResConfig.exe
        echo.
        echo ✅ 增强新建工程功能已实现！
        echo.
        echo 🗂️ 新建工程增强功能:
        echo ├─ 工程名称设置: 用户可以自定义工程名称
        echo ├─ 默认名称提供: 自动生成时间戳格式的默认名称
        echo ├─ 保存路径选择: 用户选择完整的文件保存路径
        echo ├─ 文件格式支持: 支持CSV和JSON格式选择
        echo └─ 单次执行保证: 修复了重复执行的问题
        echo.
        echo 🔧 修复的重复执行问题:
        echo ├─ 问题原因: OnNewProject信号被连接了两次
        echo │  ├─ ConnectUISignals(): 第一次连接
        echo │  └─ ConnectSignals(): 第二次连接（重复）
        echo ├─ 问题表现: 每次点击菜单执行两次新建工程
        echo ├─ 修复方案: 移除ConnectSignals()中的重复连接
        echo └─ 修复结果: ✅ 现在只执行一次
        echo.
        echo 📝 工程名称设置功能:
        echo ├─ 默认名称: 20250807143025_实验工程（时间戳格式）
        echo ├─ 输入对话框: QInputDialog::getText()
        echo ├─ 用户选择: 可以使用默认名称或自定义
        echo ├─ 验证机制: 不允许空名称
        echo └─ 取消支持: 用户可以取消操作
        echo.
        echo 📁 保存路径选择功能:
        echo ├─ 文件对话框: QFileDialog::getSaveFileName()
        echo ├─ 默认路径: 用户文档目录 + 工程名称.csv
        echo ├─ 格式选择: CSV文件、JSON文件、所有文件
        echo ├─ 路径验证: 确保用户选择了有效路径
        echo └─ 取消支持: 用户可以取消选择
        echo.
        echo 🔄 完整的新建工程流程:
        echo 1️⃣ 数据检查: 检查当前界面是否有未保存数据
        echo 2️⃣ 保存提示: 有数据时提示用户保存选择
        echo 3️⃣ 名称输入: 用户输入或确认工程名称
        echo 4️⃣ 路径选择: 用户选择保存文件路径
        echo 5️⃣ 工程创建: 创建新的TestProject对象
        echo 6️⃣ 界面清空: 清空旧数据并重置界面
        echo 7️⃣ 状态更新: 更新窗口标题和日志
        echo 8️⃣ 成功提示: 显示创建成功消息
        echo.
        echo 📋 测试步骤:
        echo.
        echo 🎯 基本新建工程测试:
        echo 1. 点击"文件" → "新建工程"
        echo 2. 在名称输入框中确认或修改工程名称
        echo 3. 在文件保存对话框中选择保存位置
        echo 4. 验证工程创建成功
        echo 5. 检查窗口标题是否更新
        echo.
        echo 🎯 重复执行测试:
        echo 1. 点击"文件" → "新建工程"
        echo 2. 观察是否只弹出一次名称输入对话框
        echo 3. 观察是否只弹出一次文件保存对话框
        echo 4. 验证只创建一个工程（不重复）
        echo.
        echo 🎯 取消操作测试:
        echo 1. 点击"文件" → "新建工程"
        echo 2. 在名称输入框中点击"取消"
        echo 3. 验证操作被正确取消
        echo 4. 在文件保存对话框中点击"取消"
        echo 5. 验证操作被正确取消
        echo.
        echo 🎯 数据保存提示测试:
        echo 1. 创建一些测试数据（作动器组、传感器组等）
        echo 2. 点击"文件" → "新建工程"
        echo 3. 验证弹出保存提示对话框
        echo 4. 测试"是"、"否"、"取消"三个选项
        echo.
        echo 🔍 验证要点:
        echo ├─ ✅ 新建工程只执行一次（不重复）
        echo ├─ ✅ 用户可以设置工程名称
        echo ├─ ✅ 提供默认的时间戳格式名称
        echo ├─ ✅ 用户可以选择保存文件路径
        echo ├─ ✅ 支持多种文件格式选择
        echo ├─ ✅ 取消操作正确处理
        echo └─ ✅ 界面状态正确更新
        echo.
        echo 启动程序测试增强新建工程功能...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
        echo 启动程序...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
        echo 启动程序...
        start release\SiteResConfig.exe
    ) else (
        echo 警告: 找不到可执行文件！
    )
)

echo.
echo 📖 增强新建工程功能详细测试指南:
echo.
echo 🎯 工程名称设置测试:
echo 1. 点击菜单"文件" → "新建工程"
echo 2. 观察名称输入对话框:
echo    - 标题: "新建实验工程"
echo    - 提示: "请输入实验工程名称:"
echo    - 默认值: 时间戳格式（如：20250807143025_实验工程）
echo 3. 测试不同的输入:
echo    - 使用默认名称: 直接点击"确定"
echo    - 自定义名称: 修改为"我的测试工程"
echo    - 空名称: 清空输入框，验证被拒绝
echo    - 取消操作: 点击"取消"，验证操作终止
echo.
echo 🎯 保存路径选择测试:
echo 1. 输入工程名称后，观察文件保存对话框:
echo    - 标题: "选择实验工程保存位置"
echo    - 默认文件名: 工程名称.csv
echo    - 默认位置: 用户文档目录
echo    - 文件类型: CSV文件、JSON文件、所有文件
echo 2. 测试不同的选择:
echo    - 使用默认位置和名称
echo    - 修改保存位置
echo    - 修改文件名
echo    - 选择不同的文件格式
echo    - 取消选择，验证操作终止
echo.
echo 🎯 重复执行验证:
echo 1. 点击"文件" → "新建工程"
echo 2. 仔细观察对话框出现次数:
echo    - 名称输入对话框应该只出现1次
echo    - 文件保存对话框应该只出现1次
echo    - 成功提示对话框应该只出现1次
echo 3. 检查日志输出:
echo    - "新建实验工程"日志应该只出现1次
echo    - "工程保存路径"日志应该只出现1次
echo.
echo 🔍 关键验证点:
echo ✓ 信号连接不重复（修复了ConnectSignals重复连接）
echo ✓ 工程名称可以自定义设置
echo ✓ 默认名称使用时间戳格式
echo ✓ 保存路径由用户完全控制
echo ✓ 支持多种文件格式选择
echo ✓ 取消操作在任何阶段都正确处理
echo ✓ 界面状态和窗口标题正确更新
echo ✓ 所有操作只执行一次，无重复
echo.
pause
