@echo off
echo.
echo ========================================
echo Dynamic Channel Configuration Test
echo ========================================
echo.

echo Testing NodeConfigDialog and CreateHardwareNodeDialog
echo Both dialogs now support 1-32 dynamic channels
echo.

echo Features implemented:
echo - Dynamic channel generation based on channelCountSpinBox value
echo - Scroll area support for many channels
echo - Automatic IP and port assignment
echo - Complete data validation and saving
echo.

echo Test steps:
echo 1. Right-click on hardware node -^> Configure Node
echo 2. Change channel count (1-32)
echo 3. Observe dynamic channel generation
echo 4. Right-click on Hardware Node Resource -^> Create Hardware Node
echo 5. Change channel count (1-32)
echo 6. Observe dynamic channel generation
echo.

if exist "SiteResConfig\SiteResConfig_Simple.pro" (
    echo Project file found. Ready for compilation.
    echo.
    echo To compile:
    echo 1. Open Qt Command Prompt
    echo 2. Navigate to SiteResConfig folder
    echo 3. Run: qmake SiteResConfig_Simple.pro -spec win32-g++ "CONFIG+=debug"
    echo 4. Run: mingw32-make debug
    echo.
) else (
    echo Project file not found!
)

echo ========================================
echo Dynamic Channel Configuration Ready!
echo ========================================
echo.
pause
