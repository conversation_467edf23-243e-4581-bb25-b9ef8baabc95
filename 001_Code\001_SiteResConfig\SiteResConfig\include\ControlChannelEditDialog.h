#pragma once

/**
 * @file ControlChannelEditDialog.h
 * @brief 控制通道编辑对话框类定义
 * @details 用于编辑控制通道的完整配置信息，使用UI文件定义界面
 * <AUTHOR> Assistant
 * @date 2025-01-27
 * @version 1.1.0
 */

#include <QtWidgets/QDialog>
#include <QtCore/QString>
#include <QtCore/QStringList>
#include "DataModels_Fixed.h"

QT_BEGIN_NAMESPACE
class QTabWidget;
class QLineEdit;
class QSpinBox;
class QCheckBox;
class QComboBox;
class QTextEdit;
class QPushButton;
QT_END_NAMESPACE

// 前向声明
class CMyMainWindow;

namespace Ui {
    class ControlChannelEditDialog;
}

namespace UI {

/**
 * @brief 控制通道编辑对话框类
 * @details 提供完整的控制通道配置编辑功能，使用UI文件定义界面
 */
class ControlChannelEditDialog : public QDialog {
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param params 控制通道参数
     * @param mainWindow MainWindow实例引用（用于获取真实数据）
     * @param parent 父窗口
     */
    explicit ControlChannelEditDialog(const ControlChannelParams& params, CMyMainWindow* mainWindow, QWidget* parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    virtual ~ControlChannelEditDialog();

    /**
     * @brief 获取更新后的控制通道参数
     * @return 控制通道参数结构体
     */
    ControlChannelParams getUpdatedParams() const;

    /**
     * @brief 设置控制通道参数
     * @param params 控制通道参数
     */
    void setControlChannelParams(const ControlChannelParams& params);
    
    /**
     * @brief 设置硬件节点列表
     * @param nodes 硬件节点列表
     */
    void setHardwareNodes(const QStringList& nodes);
    
    /**
     * @brief 设置传感器列表
     * @param sensors 传感器列表
     */
    void setSensorList(const QStringList& sensors);
    
    /**
     * @brief 设置作动器列表
     * @param actuators 作动器列表
     */
    void setActuatorList(const QStringList& actuators);
    
    /**
     * @brief 设置硬件组列表
     * @param groups 硬件组列表
     */
    void setHardwareGroups(const QStringList& groups);
    
    /**
     * @brief 设置传感器组列表
     * @param groups 传感器组列表
     */
    void setSensorGroups(const QStringList& groups);
    
    /**
     * @brief 设置作动器组列表
     * @param groups 作动器组列表
     */
    void setActuatorGroups(const QStringList& groups);
    
    /**
     * @brief 设置硬件组成员映射
     * @param members 硬件组成员映射
     */
    void setHardwareGroupMembers(const QMap<QString, QStringList>& members);
    
    /**
     * @brief 设置传感器组成员映射
     * @param members 传感器组成员映射
     */
    void setSensorGroupMembers(const QMap<QString, QStringList>& members);
    
    /**
     * @brief 设置作动器组成员映射
     * @param members 作动器组成员映射
     */
    void setActuatorGroupMembers(const QMap<QString, QStringList>& members);

private slots:
    /**
     * @brief 极性参数变更处理
     */
    void onPolarityChanged();
    
    /**
     * @brief 关联配置变更处理
     */
    void onAssociationChanged();
    
    /**
     * @brief 硬件组选择变更
     */
    void onHardwareGroupChanged();
    
    /**
     * @brief 载荷1传感器组选择变更
     */
    void onLoad1SensorGroupChanged();
    
    /**
     * @brief 载荷2传感器组选择变更
     */
    void onLoad2SensorGroupChanged();
    
    /**
     * @brief 位置传感器组选择变更
     */
    void onPositionSensorGroupChanged();
    
    /**
     * @brief 控制作动器组选择变更
     */
    void onControlActuatorGroupChanged();
    
    /**
     * @brief 输入验证
     */
    void validateInput();
    
    /**
     * @brief 确定按钮点击处理
     */
    void onAcceptClicked();
    
    /**
     * @brief 详细校验所有输入字段
     * @return 错误信息列表，如果为空则表示校验通过
     */
    QStringList validateAllFields() const;
    
    /**
     * @brief 详细校验所有输入字段，并返回第一个有错误的控件
     * @param firstErrorWidget 输出参数，第一个有错误的控件
     * @return 错误信息列表，如果为空则表示校验通过
     */
    QStringList validateAllFieldsWithFocus(QWidget*& firstErrorWidget) const;
    
    /**
     * @brief 加载所有组数据到下拉框
     */
    void loadGroupData();
    
    /**
     * @brief 根据当前选择的数据加载对应的设备信息
     */
    void loadDeviceDataForCurrentChannels();
    
    /**
     * @brief 为指定的组下拉框加载设备列表
     */
    void loadDevicesForGroup(const QString& groupName, QComboBox* deviceCombo);
    
    /**
     * @brief 获取模拟的组数据（实际项目中应该从数据源获取）
     */
    QStringList getAvailableGroups() const;
    
    /**
     * @brief 获取指定组的设备列表（实际项目中应该从数据源获取）
     */
    QStringList getDevicesForGroup(const QString& groupName) const;
    
    /**
     * @brief 从关联字符串中提取组名（格式："组名 - 设备名"）
     */
    QString extractGroupFromAssociation(const QString& association) const;
    
    /**
     * @brief 从关联字符串中提取设备名（格式："组名 - 设备名"）
     */
    QString extractDeviceFromAssociation(const QString& association) const;
    
    /**
     * @brief 获取控件所在的标签页索引
     */
    int getTabIndexForWidget(QWidget* widget) const;
    
    /**
     * @brief 切换到指定控件所在的标签页
     */
    void switchToWidgetTab(QWidget* widget);
    
    /**
     * @brief 从MainWindow获取真实的硬件组数据
     */
    void loadRealHardwareGroupData();
    
    /**
     * @brief 从MainWindow获取真实的传感器组数据  
     */
    void loadRealSensorGroupData();
    
    /**
     * @brief 从MainWindow获取真实的作动器组数据
     */
    void loadRealActuatorGroupData();
    
    /**
     * @brief 获取真实的组设备数据（替代模拟数据）
     */
    QStringList getRealDevicesForGroup(const QString& groupName) const;

private:
    // UI文件生成的界面
    Ui::ControlChannelEditDialog *ui;
    
    // MainWindow引用（用于获取真实数据）
    CMyMainWindow* mainWindow_;
    
    // 数据
    ControlChannelParams originalParams_;
    QStringList hardwareNodes_;
    QStringList sensorList_;
    QStringList actuatorList_;
    
    // 🆕 新增：组织结构数据
    QStringList hardwareGroups_;
    QStringList sensorGroups_;
    QStringList actuatorGroups_;
    QMap<QString, QStringList> hardwareGroupMembers_;  // 硬件组 -> 通道列表
    QMap<QString, QStringList> sensorGroupMembers_;    // 传感器组 -> 设备列表  
    QMap<QString, QStringList> actuatorGroupMembers_;  // 作动器组 -> 设备列表

    /**
     * @brief 初始化界面
     */
    void initializeUI();
    
    /**
     * @brief 连接信号槽
     */
    void connectSignals();
    
    /**
     * @brief 更新极性显示
     */
    void updatePolarityDisplay();
    
    /**
     * @brief 初始化下拉框选项
     */
    void initializeComboBoxes();
    
    /**
     * @brief 极性值转显示字符串
     */
    QString polarityToString(int polarity) const;
    
    /**
     * @brief 显示字符串转极性值
     */
    int stringToPolarity(const QString& str) const;
    
    /**
     * @brief 根据数据值设置下拉框选中项
     */
    void setComboBoxByValue(QComboBox* combo, int value);
    
    /**
     * @brief 更新硬件通道下拉框
     * @param groupName 选中的硬件组名称
     */
    void updateHardwareChannelCombo(const QString& groupName);
    
    /**
     * @brief 更新传感器设备下拉框
     * @param groupName 选中的传感器组名称
     * @param deviceCombo 目标设备下拉框
     */
    void updateSensorDeviceCombo(const QString& groupName, QComboBox* deviceCombo);
    
    /**
     * @brief 更新作动器设备下拉框
     * @param groupName 选中的作动器组名称
     */
    void updateActuatorDeviceCombo(const QString& groupName);
    
    /**
     * @brief 解析组合的关联信息 (如 "组名 - 设备名")
     * @param fullName 完整名称
     * @return QPair<组名, 设备名>
     */
    QPair<QString, QString> parseAssociationName(const QString& fullName) const;
    
    /**
     * @brief 组合关联信息为完整名称
     * @param groupName 组名
     * @param deviceName 设备名
     * @return 完整名称 "组名 - 设备名"
     */
    QString combineAssociationName(const QString& groupName, const QString& deviceName) const;
    
    /**
     * @brief 验证UI控件是否正确创建
     * @return true如果所有必需控件都存在，否则false
     */
    bool validateUIControls();
    
    /**
     * @brief 显示错误消息对话框
     * @param message 错误消息
     */
    void showErrorMessage(const QString& message);
    
    /**
     * @brief 验证UI控件状态以进行参数设置
     * @return true如果控件状态正常，否则false
     */
    bool validateUIControlsForParameterSetting();
    
    /**
     * @brief 确保组数据已加载
     */
    void ensureGroupDataLoaded();
    
    /**
     * @brief 安全设置下拉框文本（支持精确和包含匹配）
     * @param combo 目标下拉框
     * @param text 要设置的文本
     * @return true如果设置成功，否则false
     */
    bool setComboBoxText(QComboBox* combo, const QString& text);
    
    /**
     * @brief 获取下拉框所有选项
     * @param combo 目标下拉框
     * @return 所有选项的字符串列表
     */
    QStringList getComboBoxItems(QComboBox* combo);
};

} // namespace UI 