@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🔧 组内ID按序分配功能测试
echo ========================================
echo.

cd /d "%~dp0"

echo 📁 当前目录: %CD%
echo.

echo 🎯 功能说明:
echo.
echo 在打开工程时，所有组内ID将按照组内排序重新分配：
echo.
echo 📋 预期效果:
echo.
echo 组1
echo ├─ 设备1
echo ├─ 设备ID = 1
echo ├─ 设备2
echo ├─ 设备ID = 2
echo.
echo 组2
echo ├─ 设备1
echo ├─ 设备ID = 1
echo ├─ 设备2
echo ├─ 设备ID = 2
echo.

echo 🔧 实现原理:
echo.
echo 1. 在XLS导入过程中，重新分配组内设备ID
echo 2. 作动器组：每个组内的作动器ID从1开始递增
echo 3. 传感器组：每个组内的传感器ID从1开始递增
echo 4. 保持组ID不变，只修改组内设备ID
echo.

echo 📝 修改的代码位置:
echo.
echo 文件: XLSDataExporter.cpp
echo.
echo 作动器导入 (importActuatorDetails):
echo ├─ 重新分配组内作动器ID
echo ├─ for (int i = 0; i ^< group.actuators.size(); ++i)
echo └─     group.actuators[i].actuatorId = i + 1;
echo.
echo 传感器导入 (importSensorDetails):
echo ├─ 重新分配组内传感器ID
echo ├─ for (int i = 0; i ^< group.sensors.size(); ++i)
echo └─     group.sensors[i].sensorId = i + 1;
echo.

echo 🧪 测试步骤:
echo.
echo 1. 编译项目
echo 2. 启动程序
echo 3. 打开一个包含多个组的Excel工程文件
echo 4. 查看DEBUG信息，验证组内ID是否从1开始
echo 5. 检查tooltip显示的组ID、ID、序号信息
echo.

echo 📊 验证方法:
echo.
echo 方法1 - DEBUG信息验证:
echo ├─ 鼠标悬停在作动器组节点上
echo ├─ 查看DEBUG信息中的ID分配
echo └─ 确认每个组内ID都从1开始
echo.
echo 方法2 - 日志验证:
echo ├─ 查看导入日志中的重新分配信息
echo ├─ 搜索 "重新分配组内ID完成" 关键字
echo └─ 确认每个组都执行了ID重新分配
echo.

echo 🔍 预期日志输出:
echo.
echo ✅ 作动器组 液压_作动器组 (组ID: 1) 重新分配组内ID完成，设备数量: 2
echo ✅ 传感器组 载荷_传感器组 (组ID: 1) 重新分配组内ID完成，设备数量: 2
echo.

echo 💡 注意事项:
echo.
echo 1. 此功能只在打开工程时执行
echo 2. 不影响组ID，只重新分配组内设备ID
echo 3. 确保每个组内的设备ID都是连续的1、2、3...
echo 4. 原有的全局唯一ID被替换为组内序号ID
echo.

echo 🎉 功能优势:
echo.
echo ✅ 组内ID清晰明了，从1开始
echo ✅ 便于理解设备在组内的位置
echo ✅ DEBUG信息更加直观
echo ✅ 符合用户的使用习惯
echo.

echo 按任意键退出...
pause >nul
