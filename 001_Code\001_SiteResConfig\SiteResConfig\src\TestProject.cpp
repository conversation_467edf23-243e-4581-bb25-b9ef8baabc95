/**
 * @file TestProject.cpp
 * @brief 试验工程数据模型实现
 * @details 实现试验工程的数据管理和文件操作功能
 * <AUTHOR> Assistant
 * @date 2025-08-05
 * @version 1.0.0
 */

#include "TestProject.h"
#include <fstream>
#include <sstream>
#include <algorithm>
#include <random>

namespace DataModels {

// ============================================================================
// LoadSpectrum 实现
// ============================================================================

json LoadSpectrum::ToJson() const {
    json dataPointsJson = json::array();
    for (const auto& point : dataPoints) {
        dataPointsJson.push_back({
            {u8"time", point.time},
            {u8"position", point.position},
            {u8"velocity", point.velocity},
            {u8"force", point.force}
        });
    }
    
    return json{
        {u8"spectrumId", spectrumId},
        {u8"spectrumName", spectrumName},
        {u8"spectrumType", spectrumType},
        {u8"controlVariable", controlVariable},
        {u8"dataPoints", dataPointsJson},
        {u8"duration", duration},
        {u8"maxValue", maxValue},
        {u8"minValue", minValue},
        {u8"cycleCount", cycleCount}
    };
}

bool LoadSpectrum::FromJson(const json& jsonData) {
    try {
        spectrumId = jsonData.value(u8"spectrumId", StringType());
        spectrumName = jsonData.value(u8"spectrumName", StringType());
        spectrumType = jsonData.value(u8"spectrumType", StringType());
        controlVariable = jsonData.value(u8"controlVariable", StringType());
        duration = jsonData.value(u8"duration", 0.0);
        maxValue = jsonData.value(u8"maxValue", 0.0);
        minValue = jsonData.value(u8"minValue", 0.0);
        cycleCount = jsonData.value(u8"cycleCount", 1);
        
        // 解析数据点
        dataPoints.clear();
        if (jsonData.contains(u8"dataPoints") && jsonData[u8"dataPoints"].is_array()) {
            for (const auto& pointJson : jsonData[u8"dataPoints"]) {
                LoadSpectrumPoint point;
                point.time = pointJson.value(u8"time", 0.0);
                point.position = pointJson.value(u8"position", 0.0);
                point.velocity = pointJson.value(u8"velocity", 0.0);
                point.force = pointJson.value(u8"force", 0.0);
                dataPoints.push_back(point);
            }
        }
        
        return true;
    } catch (const std::exception&) {
        return false;
    }
}

bool LoadSpectrum::IsValid() const {
    // 验证基本信息
    if (spectrumId.empty() || spectrumName.empty()) return false;
    if (spectrumType.empty() || controlVariable.empty()) return false;
    
    // 验证数据点
    if (dataPoints.empty()) return false;
    
    // 验证持续时间
    if (duration <= 0.0) return false;
    
    // 验证循环次数
    if (cycleCount <= 0) return false;
    
    return true;
}

void LoadSpectrum::AddDataPoint(const LoadSpectrumPoint& point) {
    dataPoints.push_back(point);
    
    // 更新统计信息
    if (dataPoints.size() == 1) {
        maxValue = minValue = point.force;
        duration = point.time;
    } else {
        maxValue = std::max(maxValue, point.force);
        minValue = std::min(minValue, point.force);
        duration = std::max(duration, point.time);
    }
}

void LoadSpectrum::ClearDataPoints() {
    dataPoints.clear();
    duration = maxValue = minValue = 0.0;
}

LoadSpectrumPoint LoadSpectrum::GetInterpolatedPoint(double time) const {
    if (dataPoints.empty()) {
        return LoadSpectrumPoint();
    }
    
    // 如果时间超出范围，返回边界值
    if (time <= dataPoints.front().time) {
        return dataPoints.front();
    }
    if (time >= dataPoints.back().time) {
        return dataPoints.back();
    }
    
    // 查找插值区间
    for (size_t i = 0; i < dataPoints.size() - 1; ++i) {
        if (time >= dataPoints[i].time && time <= dataPoints[i + 1].time) {
            const auto& p1 = dataPoints[i];
            const auto& p2 = dataPoints[i + 1];
            
            // 线性插值
            double ratio = (time - p1.time) / (p2.time - p1.time);
            
            LoadSpectrumPoint result;
            result.time = time;
            result.position = p1.position + ratio * (p2.position - p1.position);
            result.velocity = p1.velocity + ratio * (p2.velocity - p1.velocity);
            result.force = p1.force + ratio * (p2.force - p1.force);
            
            return result;
        }
    }
    
    return LoadSpectrumPoint();
}

// ============================================================================
// SafetyLimitConfig 实现
// ============================================================================

json SafetyLimitConfig::ToJson() const {
    auto limitToJson = [](const LoadLimit& limit) {
        return json{
            {u8"enabled", limit.enabled},
            {u8"lowerLimit", limit.lowerLimit},
            {u8"upperLimit", limit.upperLimit},
            {u8"duration", limit.duration},
            {u8"action", limit.action}
        };
    };

    auto pacingToJson = [](const PacingConfig& pacing) {
        return json{
            {u8"enabled", pacing.enabled},
            {u8"threshold", pacing.threshold},
            {u8"duration", pacing.duration},
            {u8"timeout", pacing.timeout},
            {u8"action", pacing.action}
        };
    };

    return json{
        {u8"channelId", channelId},
        {u8"loadLimit1", limitToJson(loadLimit1)},
        {u8"loadLimit2", limitToJson(loadLimit2)},
        {u8"loadLimit3", limitToJson(loadLimit3)},
        {u8"positionLimit1", limitToJson(positionLimit1)},
        {u8"positionLimit2", limitToJson(positionLimit2)},
        {u8"positionLimit3", limitToJson(positionLimit3)},
        {u8"errorLimit1", limitToJson(errorLimit1)},
        {u8"errorLimit2", limitToJson(errorLimit2)},
        {u8"errorLimit3", limitToJson(errorLimit3)},
        {u8"staticPacing", pacingToJson(staticPacing)},
        {u8"dynamicPacing", pacingToJson(dynamicPacing)}
    };
}

bool SafetyLimitConfig::FromJson(const json& jsonData) {
    try {
        channelId = jsonData.value(u8"channelId", StringType());

        auto limitFromJson = [](const json& limitJson, LoadLimit& limit) {
            limit.enabled = limitJson.value(u8"enabled", false);
            limit.lowerLimit = limitJson.value(u8"lowerLimit", 0.0);
            limit.upperLimit = limitJson.value(u8"upperLimit", 0.0);
            limit.duration = limitJson.value(u8"duration", 0.0);
            limit.action = limitJson.value(u8"action", StringType());
        };

        auto pacingFromJson = [](const json& pacingJson, PacingConfig& pacing) {
            pacing.enabled = pacingJson.value(u8"enabled", false);
            pacing.threshold = pacingJson.value(u8"threshold", 0.0);
            pacing.duration = pacingJson.value(u8"duration", 0.0);
            pacing.timeout = pacingJson.value(u8"timeout", 0.0);
            pacing.action = pacingJson.value(u8"action", StringType());
        };

        if (jsonData.contains(u8"loadLimit1")) limitFromJson(jsonData[u8"loadLimit1"], loadLimit1);
        if (jsonData.contains(u8"loadLimit2")) limitFromJson(jsonData[u8"loadLimit2"], loadLimit2);
        if (jsonData.contains(u8"loadLimit3")) limitFromJson(jsonData[u8"loadLimit3"], loadLimit3);
        if (jsonData.contains(u8"positionLimit1")) limitFromJson(jsonData[u8"positionLimit1"], positionLimit1);
        if (jsonData.contains(u8"positionLimit2")) limitFromJson(jsonData[u8"positionLimit2"], positionLimit2);
        if (jsonData.contains(u8"positionLimit3")) limitFromJson(jsonData[u8"positionLimit3"], positionLimit3);
        if (jsonData.contains(u8"errorLimit1")) limitFromJson(jsonData[u8"errorLimit1"], errorLimit1);
        if (jsonData.contains(u8"errorLimit2")) limitFromJson(jsonData[u8"errorLimit2"], errorLimit2);
        if (jsonData.contains(u8"errorLimit3")) limitFromJson(jsonData[u8"errorLimit3"], errorLimit3);
        if (jsonData.contains(u8"staticPacing")) pacingFromJson(jsonData[u8"staticPacing"], staticPacing);
        if (jsonData.contains(u8"dynamicPacing")) pacingFromJson(jsonData[u8"dynamicPacing"], dynamicPacing);

        return true;
    } catch (const std::exception&) {
        return false;
    }
}

bool SafetyLimitConfig::IsValid() const {
    // 验证通道ID不为空
    if (channelId.empty()) return false;

    // 验证门限配置的合理性
    auto validateLimit = [](const LoadLimit& limit) {
        if (limit.enabled) {
            if (limit.lowerLimit >= limit.upperLimit) return false;
            if (limit.duration < 0.0) return false;
        }
        return true;
    };

    auto validatePacing = [](const PacingConfig& pacing) {
        if (pacing.enabled) {
            if (pacing.threshold < 0.0) return false;
            if (pacing.duration < 0.0) return false;
            if (pacing.timeout < 0.0) return false;
        }
        return true;
    };

    return validateLimit(loadLimit1) && validateLimit(loadLimit2) && validateLimit(loadLimit3) &&
           validateLimit(positionLimit1) && validateLimit(positionLimit2) && validateLimit(positionLimit3) &&
           validateLimit(errorLimit1) && validateLimit(errorLimit2) && validateLimit(errorLimit3) &&
           validatePacing(staticPacing) && validatePacing(dynamicPacing);
}

// ============================================================================
// TestProject 实现
// ============================================================================

TestProject::TestProject() 
    : sampleRate(Constants::DEFAULT_SAMPLE_RATE)
    , controlPeriod(Constants::MIN_CONTROL_PERIOD)
    , autoSave(true)
    , autoSaveInterval(300) // 5分钟
{
    // 生成项目ID
    projectId = GenerateUniqueId(u8"PRJ");
    
    // 设置创建时间
    createdDate = modifiedDate = Utils::FormatTimestamp(Utils::GetCurrentTimestamp());
    
    // 设置默认版本
    version = u8"1.0.0";
}

json TestProject::ToJson() const {
    // 转换硬件节点
    json hardwareNodesJson = json::object();
    for (const auto& pair : hardwareNodes) {
        hardwareNodesJson[std::to_string(pair.first)] = pair.second.ToJson();
    }
    
    // 转换作动器
    json actuatorsJson = json::object();
    for (const auto& pair : actuators) {
        actuatorsJson[pair.first] = pair.second.ToJson();
    }
    
    // 转换传感器
    json sensorsJson = json::object();
    for (const auto& pair : sensors) {
        sensorsJson[pair.first] = pair.second.ToJson();
    }
    
    // 转换通道
    json channelsJson = json::object();
    for (const auto& pair : channels) {
        channelsJson[pair.first] = pair.second.ToJson();
    }
    
    // 转换载荷谱
    json loadSpectrumsJson = json::object();
    for (const auto& pair : loadSpectrums) {
        loadSpectrumsJson[pair.first] = pair.second.ToJson();
    }
    
    return json{
        {u8"projectId", projectId},
        {u8"projectName", projectName},
        {u8"projectPath", projectPath},
        {u8"description", description},
        {u8"createdDate", createdDate},
        {u8"modifiedDate", modifiedDate},
        {u8"version", version},
        {u8"hardwareNodes", hardwareNodesJson},
        {u8"actuators", actuatorsJson},
        {u8"sensors", sensorsJson},
        {u8"channels", channelsJson},
        {u8"loadSpectrums", loadSpectrumsJson},
        {u8"sampleRate", sampleRate},
        {u8"controlPeriod", controlPeriod},
        {u8"autoSave", autoSave},
        {u8"autoSaveInterval", autoSaveInterval}
    };
}

bool TestProject::FromJson(const json& jsonData) {
    try {
        // 基本信息
        projectId = jsonData.value(u8"projectId", StringType());
        projectName = jsonData.value(u8"projectName", StringType());
        projectPath = jsonData.value(u8"projectPath", StringType());
        description = jsonData.value(u8"description", StringType());
        createdDate = jsonData.value(u8"createdDate", StringType());
        modifiedDate = jsonData.value(u8"modifiedDate", StringType());
        version = jsonData.value(u8"version", StringType());
        
        // 系统配置
        sampleRate = jsonData.value(u8"sampleRate", Constants::DEFAULT_SAMPLE_RATE);
        controlPeriod = jsonData.value(u8"controlPeriod", Constants::MIN_CONTROL_PERIOD);
        autoSave = jsonData.value(u8"autoSave", true);
        autoSaveInterval = jsonData.value(u8"autoSaveInterval", 300);
        
        // 清空现有数据
        hardwareNodes.clear();
        actuators.clear();
        sensors.clear();
        channels.clear();
        loadSpectrums.clear();
        
        // 解析硬件节点
        if (jsonData.contains(u8"hardwareNodes")) {
            for (const auto& item : jsonData[u8"hardwareNodes"].items()) {
                HardwareNode node;
                if (node.FromJson(item.value())) {
                    hardwareNodes[std::stoi(item.key())] = node;
                }
            }
        }
        
        // 解析作动器
        if (jsonData.contains(u8"actuators")) {
            for (const auto& item : jsonData[u8"actuators"].items()) {
                ActuatorInfo actuator;
                if (actuator.FromJson(item.value())) {
                    actuators[item.key()] = actuator;
                }
            }
        }
        
        // 解析传感器
        if (jsonData.contains(u8"sensors")) {
            for (const auto& item : jsonData[u8"sensors"].items()) {
                SensorInfo sensor;
                if (sensor.FromJson(item.value())) {
                    sensors[item.key()] = sensor;
                }
            }
        }
        
        // 解析通道
        if (jsonData.contains(u8"channels")) {
            for (const auto& item : jsonData[u8"channels"].items()) {
                LoadControlChannel channel;
                if (channel.FromJson(item.value())) {
                    channels[item.key()] = channel;
                }
            }
        }
        
        // 解析载荷谱
        if (jsonData.contains(u8"loadSpectrums")) {
            for (const auto& item : jsonData[u8"loadSpectrums"].items()) {
                LoadSpectrum spectrum;
                if (spectrum.FromJson(item.value())) {
                    loadSpectrums[item.key()] = spectrum;
                }
            }
        }
        
        return true;
    } catch (const std::exception&) {
        return false;
    }
}

bool TestProject::IsValid() const {
    // 验证基本信息
    if (projectId.empty() || projectName.empty()) return false;
    
    // 验证系统配置
    if (sampleRate <= 0.0) return false;
    if (controlPeriod < Constants::MIN_CONTROL_PERIOD || 
        controlPeriod > Constants::MAX_CONTROL_PERIOD) return false;
    
    // 验证所有数据模型
    for (const auto& pair : hardwareNodes) {
        if (!pair.second.IsValid()) return false;
    }
    
    for (const auto& pair : actuators) {
        if (!pair.second.IsValid()) return false;
    }
    
    for (const auto& pair : sensors) {
        if (!pair.second.IsValid()) return false;
    }
    
    for (const auto& pair : channels) {
        if (!pair.second.IsValid()) return false;
    }
    
    for (const auto& pair : loadSpectrums) {
        if (!pair.second.IsValid()) return false;
    }
    
    return true;
}

// ============================================================================
// TestProject 硬件资源管理方法
// ============================================================================

bool TestProject::AddHardwareNode(const HardwareNode& node) {
    if (!node.IsValid()) return false;
    hardwareNodes[node.nodeId] = node;
    modifiedDate = Utils::FormatTimestamp(Utils::GetCurrentTimestamp());
    return true;
}

bool TestProject::RemoveHardwareNode(int nodeId) {
    auto it = hardwareNodes.find(nodeId);
    if (it == hardwareNodes.end()) return false;
    hardwareNodes.erase(it);
    modifiedDate = Utils::FormatTimestamp(Utils::GetCurrentTimestamp());
    return true;
}

HardwareNode* TestProject::GetHardwareNode(int nodeId) {
    auto it = hardwareNodes.find(nodeId);
    return (it != hardwareNodes.end()) ? &it->second : nullptr;
}

bool TestProject::AddActuator(const ActuatorInfo& actuator) {
    if (!actuator.IsValid()) return false;
    actuators[actuator.actuatorId] = actuator;
    modifiedDate = Utils::FormatTimestamp(Utils::GetCurrentTimestamp());
    return true;
}

bool TestProject::RemoveActuator(const StringType& actuatorId) {
    auto it = actuators.find(actuatorId);
    if (it == actuators.end()) return false;
    actuators.erase(it);
    modifiedDate = Utils::FormatTimestamp(Utils::GetCurrentTimestamp());
    return true;
}

ActuatorInfo* TestProject::GetActuator(const StringType& actuatorId) {
    auto it = actuators.find(actuatorId);
    return (it != actuators.end()) ? &it->second : nullptr;
}

bool TestProject::AddSensor(const SensorInfo& sensor) {
    if (!sensor.IsValid()) return false;
    sensors[sensor.sensorId] = sensor;
    modifiedDate = Utils::FormatTimestamp(Utils::GetCurrentTimestamp());
    return true;
}

bool TestProject::RemoveSensor(const StringType& sensorId) {
    auto it = sensors.find(sensorId);
    if (it == sensors.end()) return false;
    sensors.erase(it);
    modifiedDate = Utils::FormatTimestamp(Utils::GetCurrentTimestamp());
    return true;
}

SensorInfo* TestProject::GetSensor(const StringType& sensorId) {
    auto it = sensors.find(sensorId);
    return (it != sensors.end()) ? &it->second : nullptr;
}

// ============================================================================
// TestProject 试验配置管理方法
// ============================================================================

bool TestProject::AddChannel(const LoadControlChannel& channel) {
    if (!channel.IsValid()) return false;
    channels[channel.channelId] = channel;
    modifiedDate = Utils::FormatTimestamp(Utils::GetCurrentTimestamp());
    return true;
}

bool TestProject::RemoveChannel(const StringType& channelId) {
    auto it = channels.find(channelId);
    if (it == channels.end()) return false;
    channels.erase(it);
    modifiedDate = Utils::FormatTimestamp(Utils::GetCurrentTimestamp());
    return true;
}

LoadControlChannel* TestProject::GetChannel(const StringType& channelId) {
    auto it = channels.find(channelId);
    return (it != channels.end()) ? &it->second : nullptr;
}

bool TestProject::AddLoadSpectrum(const LoadSpectrum& spectrum) {
    if (!spectrum.IsValid()) return false;
    loadSpectrums[spectrum.spectrumId] = spectrum;
    modifiedDate = Utils::FormatTimestamp(Utils::GetCurrentTimestamp());
    return true;
}

bool TestProject::RemoveLoadSpectrum(const StringType& spectrumId) {
    auto it = loadSpectrums.find(spectrumId);
    if (it == loadSpectrums.end()) return false;
    loadSpectrums.erase(it);
    modifiedDate = Utils::FormatTimestamp(Utils::GetCurrentTimestamp());
    return true;
}

LoadSpectrum* TestProject::GetLoadSpectrum(const StringType& spectrumId) {
    auto it = loadSpectrums.find(spectrumId);
    return (it != loadSpectrums.end()) ? &it->second : nullptr;
}

bool TestProject::AddSafetyConfig(const SafetyLimitConfig& config) {
    if (!config.IsValid()) return false;
    safetyConfigs[config.channelId] = config;
    modifiedDate = Utils::FormatTimestamp(Utils::GetCurrentTimestamp());
    return true;
}

bool TestProject::RemoveSafetyConfig(const StringType& channelId) {
    auto it = safetyConfigs.find(channelId);
    if (it == safetyConfigs.end()) return false;
    safetyConfigs.erase(it);
    modifiedDate = Utils::FormatTimestamp(Utils::GetCurrentTimestamp());
    return true;
}

SafetyLimitConfig* TestProject::GetSafetyConfig(const StringType& channelId) {
    auto it = safetyConfigs.find(channelId);
    return (it != safetyConfigs.end()) ? &it->second : nullptr;
}

// ============================================================================
// TestProject 文件操作方法
// ============================================================================

bool TestProject::SaveToFile(const StringType& filePath) const {
    try {
        json projectJson = ToJson();
        std::ofstream file(filePath);
        if (!file.is_open()) return false;

        file << projectJson.dump(4); // 格式化输出，缩进4个空格
        file.close();
        return true;
    } catch (const std::exception&) {
        return false;
    }
}

bool TestProject::LoadFromFile(const StringType& filePath) {
    try {
        std::ifstream file(filePath);
        if (!file.is_open()) return false;

        json projectJson;
        file >> projectJson;
        file.close();

        return FromJson(projectJson);
    } catch (const std::exception&) {
        return false;
    }
}

std::vector<StringType> TestProject::ValidateProject() const {
    std::vector<StringType> errors;

    // 验证基本信息
    if (projectName.empty()) {
        errors.push_back(u8"工程名称不能为空");
    }

    // 验证通道配置
    for (const auto& channelPair : channels) {
        const auto& channel = channelPair.second;

        // 检查关联的作动器是否存在
        if (actuators.find(channel.actuatorId) == actuators.end()) {
            errors.push_back(u8"通道 " + channel.channelName + u8" 关联的作动器不存在");
        }

        // 检查关联的传感器是否存在
        for (const auto& sensorId : channel.sensorIds) {
            if (sensors.find(sensorId) == sensors.end()) {
                errors.push_back(u8"通道 " + channel.channelName + u8" 关联的传感器不存在");
            }
        }
    }

    return errors;
}

StringType TestProject::GenerateUniqueId(const StringType& prefix) const {
    // 使用时间戳和随机数生成唯一ID
    auto timestamp = Utils::GetCurrentTimestamp();

    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(1000, 9999);

    std::ostringstream oss;
    oss << prefix << "_" << timestamp << "_" << dis(gen);
    return oss.str();
}

} // namespace DataModels
