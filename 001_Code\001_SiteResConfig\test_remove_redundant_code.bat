@echo off
echo ========================================
echo  测试注释多余代码后的编译
echo ========================================

REM 设置Qt环境变量
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目（测试注释多余代码后）...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo 请检查上面的错误信息。
    echo.
    echo 🔍 可能的问题:
    echo 1. 还有代码在调用已注释的方法
    echo 2. 头文件和实现文件不一致
    echo 3. 其他编译错误
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！多余代码已成功注释
    echo ========================================
    
    echo.
    echo ✅ 注释完成的内容:
    echo.
    echo 🚫 已注释的多余方法:
    echo 1. createActuatorWorksheet() - 包含旧的17列格式
    echo 2. addActuatorWorksheetToExcel() - 调用已注释的方法
    echo 3. writeActuatorWorksheetHeader() - 包含旧的17列表头
    echo 4. writeActuatorGroupData() - 包含作动器序号写入
    echo 5. applyActuatorWorksheetStyles() - 包含旧的17列样式
    echo.
    echo ✅ 保留的正确方法:
    echo 1. exportActuatorDetails() - 16列格式，正确的单独导出
    echo 2. addActuatorGroupDetailToExcel() - 16列格式，正确的数据写入
    echo 3. exportCompleteProject() - 标准的完整项目导出
    echo.
    echo 🎯 当前作动器导出格式（16列）:
    echo 第1列: 组序号
    echo 第2列: 作动器组名称
    echo 第3列: 作动器序列号（没有作动器序号）
    echo 第4列: 作动器类型
    echo 第5列: Unit类型
    echo 第6列: Unit值
    echo 第7列: 行程(m)
    echo 第8列: 位移(m)
    echo 第9列: 拉伸面积(m²)
    echo 第10列: 压缩面积(m²)
    echo 第11列: 极性
    echo 第12列: Deliver(V)
    echo 第13列: 频率(Hz)
    echo 第14列: 输出倍数
    echo 第15列: 平衡(V)
    echo 第16列: 备注
    echo.
    echo 🔧 注释详情:
    echo.
    echo 📋 XLSDataExporter.cpp:
    echo - createActuatorWorksheet(): 完全注释
    echo - addActuatorWorksheetToExcel(): 完全注释
    echo - writeActuatorWorksheetHeader(): 完全注释
    echo - writeActuatorGroupData(): 完全注释
    echo - applyActuatorWorksheetStyles(): 完全注释
    echo - exportCompleteProjectWithActuators(): 注释调用部分
    echo.
    echo 📋 XLSDataExporter.h:
    echo - createActuatorWorksheet(): 声明已注释
    echo - addActuatorWorksheetToExcel(): 声明已注释
    echo - writeActuatorWorksheetHeader(): 声明已注释
    echo - writeActuatorGroupData(): 声明已注释
    echo - applyActuatorWorksheetStyles(): 声明已注释
    echo.
    echo 💡 注释原因:
    echo - 这些方法包含旧的17列格式
    echo - 包含作动器序号的写入
    echo - 与当前需求（16列，无作动器序号）不符
    echo - 避免混乱和错误调用
    echo.
    
    if exist "SiteResConfig.exe" (
        echo 启动程序验证修复...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 启动程序验证修复...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 启动程序验证修复...
        start release\SiteResConfig.exe
    )
)

echo.
echo 📖 验证步骤:
echo.
echo 🎮 作动器详细配置验证:
echo 1. 启动软件，新建项目
echo 2. 创建多个作动器组，添加作动器
echo 3. 导出作动器详细信息到Excel
echo 4. 验证Excel文件：
echo    - 总共16列（不是17列）
echo    - 第3列是作动器序列号（没有作动器序号列）
echo    - 所有数据正确导出
echo.
echo 🎮 编译验证:
echo 1. 确认编译成功，无错误
echo 2. 验证没有调用已注释方法的编译错误
echo 3. 确认程序正常启动
echo.
echo 🎮 功能验证:
echo 1. 测试作动器详细配置导出功能
echo 2. 验证16列格式正确
echo 3. 确认没有作动器序号列
echo 4. 检查数据完整性
echo.
echo ✅ 预期结果:
echo - 编译成功，无多余代码相关错误
echo - 作动器详细配置为16列格式
echo - 没有作动器序号列
echo - 所有功能正常，数据完整
echo.
echo 🚨 如果测试失败:
echo - 检查是否还有代码调用已注释的方法
echo - 验证头文件和实现文件的一致性
echo - 确认Excel导出格式是否正确
echo.
pause
