@echo off
echo ========================================
echo  SiteResConfig Qt MinGW 编译脚本
echo ========================================

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

REM 检查Qt是否可用
echo 检查Qt环境...
qmake --version
if errorlevel 1 (
    echo 错误: 找不到Qt环境！
    echo 请确保Qt 5.14.2 MinGW版本已正确安装
    echo 或修改此脚本中的QTDIR路径
    pause
    exit /b 1
)

REM 检查MinGW编译器
echo 检查MinGW编译器...
g++ --version
if errorlevel 1 (
    echo 错误: 找不到MinGW编译器！
    echo 请确保MinGW已正确安装并添加到PATH
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

REM 清理之前的构建
echo 清理之前的构建文件...
if exist "Makefile" del Makefile
if exist "Makefile.Debug" del Makefile.Debug
if exist "Makefile.Release" del Makefile.Release
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o

echo.
echo 使用简化项目文件生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo 错误: qmake失败！
    echo 请检查项目文件是否正确
    pause
    exit /b 1
)

echo.
echo 编译项目...
mingw32-make clean
mingw32-make
if errorlevel 1 (
    echo 错误: 编译失败！
    echo 请检查编译错误信息
    pause
    exit /b 1
)

echo.
echo ========================================
echo  编译成功！
echo ========================================

REM 查找并运行可执行文件
echo 查找可执行文件...
if exist "SiteResConfig.exe" (
    echo 找到可执行文件: SiteResConfig.exe
    echo 启动程序...
    SiteResConfig.exe
) else if exist "debug\SiteResConfig.exe" (
    echo 找到可执行文件: debug\SiteResConfig.exe
    echo 启动程序...
    debug\SiteResConfig.exe
) else if exist "release\SiteResConfig.exe" (
    echo 找到可执行文件: release\SiteResConfig.exe
    echo 启动程序...
    release\SiteResConfig.exe
) else (
    echo 警告: 找不到可执行文件！
    echo 查找所有exe文件:
    dir *.exe /s
    echo.
    echo 可能的原因:
    echo 1. 编译过程中有错误但未被检测到
    echo 2. 可执行文件生成在其他目录
    echo 3. 链接器配置问题
)

echo.
pause
