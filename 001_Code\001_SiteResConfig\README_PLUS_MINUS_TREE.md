# Windows经典加号/减号树形控件

## 🎯 设计概述

实现了传统Windows风格的树形控件，使用经典的加号(+)和减号(-)按钮作为展开/折叠指示器，完全符合Windows用户的操作习惯。

## 🎨 设计特点

### 1. 加号/减号按钮

#### **按钮外观**
- **尺寸**: 9x9像素方形按钮
- **背景**: 白色 (#FFFFFF)
- **边框**: 1px灰色边框 (#808080)
- **形状**: 方形，无圆角（Windows经典风格）

#### **符号设计**
- **折叠状态**: 黑色加号 (+)
- **展开状态**: 黑色减号 (-)
- **字体**: Courier New等宽字体
- **大小**: 7pt，粗体
- **对齐**: 居中显示

### 2. 交互效果

#### **悬停状态**
- **背景色**: 浅蓝色 (#E1ECFF)
- **边框色**: Windows蓝色 (#0078D4)
- **响应**: 平滑的视觉反馈

#### **点击功能**
- **加号按钮**: 展开子节点
- **减号按钮**: 折叠子节点
- **响应速度**: 即时响应

### 3. 禁用状态

#### **外观变化**
- **背景色**: 浅灰色 (#F5F5F5)
- **边框色**: 灰色 (#C0C0C0)
- **符号色**: 浅灰色 (#A0A0A0)
- **交互**: 不响应悬停和点击

## 🔧 技术实现

### CSS核心代码

```css
/* 折叠状态 - 加号按钮 */
QTreeWidget::branch:closed:has-children {
    background-color: #FFFFFF;
    border: 1px solid #808080;
    border-radius: 0px;
    width: 9px;
    height: 9px;
    margin: 4px;
}

/* 加号符号 */
QTreeWidget::branch:closed:has-children:before {
    content: "+";
    color: #000000;
    font-size: 7pt;
    font-weight: bold;
    font-family: "Courier New", monospace;
    text-align: center;
    line-height: 9px;
}

/* 展开状态 - 减号按钮 */
QTreeWidget::branch:open:has-children {
    background-color: #FFFFFF;
    border: 1px solid #808080;
    border-radius: 0px;
    width: 9px;
    height: 9px;
    margin: 4px;
}

/* 减号符号 */
QTreeWidget::branch:open:has-children:before {
    content: "-";
    color: #000000;
    font-size: 7pt;
    font-weight: bold;
    font-family: "Courier New", monospace;
    text-align: center;
    line-height: 9px;
}

/* 悬停效果 */
QTreeWidget::branch:has-children:hover {
    background-color: #E1ECFF;
    border-color: #0078D4;
}
```

## 🎯 使用场景

### 适用环境
- **企业级应用**: 专业、稳重的界面风格
- **系统工具**: 与Windows系统风格一致
- **数据管理**: 清晰的层次结构显示
- **配置界面**: 熟悉的操作体验

### 用户体验
- **熟悉感**: Windows用户零学习成本
- **直观性**: 加号/减号含义明确
- **可访问性**: 符合无障碍设计标准
- **一致性**: 与Windows系统控件一致

## 🔍 设计优势

### 1. **经典Windows体验**
- ✅ 传统的加号/减号指示器
- ✅ 方形按钮设计
- ✅ 标准的Windows颜色方案
- ✅ 熟悉的交互模式

### 2. **清晰的视觉层次**
- ✅ 明确的展开/折叠状态指示
- ✅ 一致的按钮大小和样式
- ✅ 清晰的连接线系统
- ✅ 良好的对比度

### 3. **优秀的可用性**
- ✅ 直观的操作逻辑
- ✅ 即时的视觉反馈
- ✅ 标准的键盘导航支持
- ✅ 完整的禁用状态处理

### 4. **技术稳定性**
- ✅ 纯CSS实现，无依赖
- ✅ 跨平台兼容性
- ✅ 高性能渲染
- ✅ 易于维护和修改

## 📊 对比分析

| 特性 | 加号/减号 | 三角形 | 自定义图标 |
|------|-----------|--------|------------|
| 熟悉度 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ |
| 清晰度 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| Windows兼容 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐ |
| 实现复杂度 | ⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ |

## 🚀 测试验证

### 运行测试
```bash
test_plus_minus_tree.bat
```

### 验证要点
1. **按钮显示**: 所有有子节点的节点显示方形按钮
2. **符号正确**: 折叠显示+，展开显示-
3. **交互正常**: 点击按钮可正确展开/折叠
4. **样式一致**: 按钮大小、颜色、字体一致
5. **悬停效果**: 悬停时显示蓝色高亮

## 📝 总结

Windows经典加号/减号树形控件成功实现了：

- **✅ 经典体验**: 传统Windows用户界面风格
- **✅ 直观操作**: 加号/减号含义明确
- **✅ 视觉清晰**: 方形按钮，黑色符号
- **✅ 交互流畅**: 悬停和点击效果自然
- **✅ 状态完整**: 正常、悬停、禁用状态齐全

这种设计既保持了Windows用户的熟悉感，又提供了清晰的视觉层次和直观的操作体验，是企业级应用的理想选择。
