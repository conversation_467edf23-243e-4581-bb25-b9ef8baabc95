# 基于完整SensorParams结构体的传感器信息集成报告

## 📋 需求确认

用户要求：
> "将传感器详细信息直接集成到硬件树结构中，作为传感器设备的子项显示，CSV，JSON一样处理，传感器详细信息保存结构体如下：SensorParams"

## 🎯 实施方案

### **基于完整SensorParams结构体的设计**

根据 `SensorDialog.h` 中定义的完整 `SensorParams` 结构体，包含58个字段，分为4个主要组：

#### **1. 基本信息组 (sensorGroupBox) - 8个字段**
```cpp
QString serialNumber;      // 序列号
QString sensorType;        // 传感器类型（作为主要标识）
QString edsId;            // EDS标识
QString dimension;        // 尺寸
QString model;            // 型号
QString range;            // 量程
QString unit;             // 单位
double sensitivity;       // 灵敏度
```

#### **2. 校准和范围信息组 (rangeGroupBox) - 17个字段**
```cpp
bool calibrationEnabled;   // 校准日期启用状态
QString calibrationDate;   // 校准日期
QString performedBy;       // 校准执行人
QString unitType;         // 单位类型
QString unitValue;        // 具体单位值
QString inputRange;       // 输入范围
double fullScaleMax;      // 满量程最大值
QString fullScaleMaxUnit; // 满量程最大值单位
int fullScaleMaxValue2;   // 满量程最大值第二个数值
QString fullScaleMaxCombo; // 满量程最大值组合框
bool allowSeparateMinMax; // 允许分别设置最小和最大满量程
double fullScaleMin;      // 满量程最小值
QString fullScaleMinUnit; // 满量程最小值单位
int fullScaleMinValue2;   // 满量程最小值第二个数值
QString fullScaleMinCombo; // 满量程最小值组合框
// ... 其他校准字段
```

#### **3. 信号调理参数组 (conditioningGroupBox) - 30个字段**
```cpp
QString polarity;         // 极性 (Positive/Negative)
QString preAmpGain;       // 前置放大增益
double postAmpGain;       // 后置放大增益
int postAmpGainValue2;    // 后置放大增益第二个数值
QString postAmpGainCombo; // 后置放大增益组合框
double totalGain;         // 总增益
int totalGainValue2;      // 总增益第二个数值
QString totalGainCombo;   // 总增益组合框
double deltaKGain;        // Delta K增益
int deltaKGainValue2;     // Delta K增益第二个数值
QString deltaKGainCombo;  // Delta K增益组合框
QString scaleFactor;      // 比例因子
int scaleFactorValue;     // 比例因子数值
QString scaleFactorCombo; // 比例因子组合框
bool enableExcitation;    // 启用激励
double excitationVoltage; // 激励电压
int excitationValue2;     // 激励第二个数值
QString excitationCombo;  // 激励组合框
QString excitationBalance; // 激励平衡
int excitationBalanceValue; // 激励平衡数值
QString excitationBalanceCombo; // 激励平衡组合框
QString excitationFrequency; // 激励频率
QString phase;            // 相位
int phaseValue;           // 相位数值
QString phaseCombo;       // 相位组合框
QString encoderResolution; // 编码器分辨率
int encoderResolutionValue; // 编码器分辨率数值
QString encoderResolutionCombo; // 编码器分辨率组合框
// ... 其他信号调理字段
```

#### **4. 其他配置参数 (兼容性保留) - 3个字段**
```cpp
double positiveFeedback;  // 正向反馈系数 (兼容性保留)
double negativeFeedback;  // 负向反馈系数 (兼容性保留)
bool isPositive;          // 极性布尔值 (兼容性保留)
```

## 🔧 实施细节

### **CSV实现：AddSensorDetailToCSV()**

**设计原则**：
- 按结构体分组组织字段
- 智能显示：只显示有值的字段
- 格式化数值：使用适当的精度
- 组合字段：将相关的数值和单位组合显示

**实现示例**：
```cpp
void CMyMainWindow::AddSensorDetailToCSV(QTextStream& out, const UI::SensorParams& params) {
    // === 基本信息组 (sensorGroupBox) ===
    if (!params.edsId.isEmpty()) {
        out << "," << FormatCSVField(QStringLiteral("  ├─ EDS标识")) << "," << FormatCSVField(params.edsId) << "," << "" << "," << "" << "\n";
    }
    if (!params.dimension.isEmpty()) {
        out << "," << FormatCSVField(QStringLiteral("  ├─ 尺寸")) << "," << FormatCSVField(params.dimension) << "," << "" << "," << "" << "\n";
    }
    if (!params.unit.isEmpty()) {
        out << "," << FormatCSVField(QStringLiteral("  ├─ 单位")) << "," << FormatCSVField(params.unit) << "," << "" << "," << "" << "\n";
    }
    if (params.sensitivity != 0.0) {
        out << "," << FormatCSVField(QStringLiteral("  ├─ 灵敏度")) << "," << FormatCSVField(QString::number(params.sensitivity, 'f', 3)) << "," << "" << "," << "" << "\n";
    }

    // === 校准和范围信息组 (rangeGroupBox) ===
    if (params.calibrationEnabled) {
        out << "," << FormatCSVField(QStringLiteral("  ├─ 校准启用")) << "," << FormatCSVField("是") << "," << "" << "," << "" << "\n";
        // ... 校准相关字段
        
        // 满量程最大值（组合显示）
        if (params.fullScaleMax != 0.0) {
            QString maxValueStr = QString::number(params.fullScaleMax, 'f', 3);
            if (params.fullScaleMaxValue2 != 0) {
                maxValueStr += QString(" / %1").arg(params.fullScaleMaxValue2);
            }
            out << "," << FormatCSVField(QStringLiteral("  ├─ 满量程最大值")) << "," << FormatCSVField(maxValueStr) << "," << FormatCSVField(params.fullScaleMaxUnit) << "," << FormatCSVField(params.fullScaleMaxCombo) << "\n";
        }
    }

    // === 信号调理参数组 (conditioningGroupBox) ===
    // 后置放大增益（组合显示）
    if (params.postAmpGain != 0.0) {
        QString postAmpStr = QString::number(params.postAmpGain, 'f', 4);
        if (params.postAmpGainValue2 != 0) {
            postAmpStr += QString(" / %1").arg(params.postAmpGainValue2);
        }
        out << "," << FormatCSVField(QStringLiteral("  ├─ 后置放大增益")) << "," << FormatCSVField(postAmpStr) << "," << FormatCSVField(params.postAmpGainCombo) << "," << "" << "\n";
    }
    
    // 激励设置
    if (params.enableExcitation) {
        out << "," << FormatCSVField(QStringLiteral("  ├─ 激励启用")) << "," << FormatCSVField("是") << "," << "" << "," << "" << "\n";
        // ... 激励相关字段
    }

    // === 其他配置参数（兼容性保留） ===
    if (params.positiveFeedback != 0.0) {
        out << "," << FormatCSVField(QStringLiteral("  ├─ 正向反馈系数")) << "," << FormatCSVField(QString::number(params.positiveFeedback, 'f', 6)) << "," << "" << "," << "" << "\n";
    }
}
```

### **JSON实现：CreateSensorDetailedConfigJSON()**

**设计原则**：
- 与CSV保持一致的字段组织
- 使用标准的JSON对象格式
- field2-field5字段分别存储：主值、单位、组合框、扩展信息

**实现示例**：
```cpp
QJsonArray CMyMainWindow::CreateSensorDetailedConfigJSON(const UI::SensorParams& params) {
    QJsonArray detailArray;

    // === 基本信息组 (sensorGroupBox) ===
    if (!params.edsId.isEmpty()) {
        QJsonObject edsObj;
        edsObj["# 实验工程配置文件"] = QString(u8"  ├─ EDS标识");
        edsObj["field2"] = params.edsId;
        edsObj["field3"] = "";
        edsObj["field4"] = "";
        edsObj["field5"] = "";
        detailArray.append(edsObj);
    }

    // === 校准和范围信息组 (rangeGroupBox) ===
    if (params.calibrationEnabled) {
        // 满量程最大值（组合显示）
        if (params.fullScaleMax != 0.0) {
            QJsonObject fullScaleMaxObj;
            fullScaleMaxObj["# 实验工程配置文件"] = QString(u8"  ├─ 满量程最大值");
            QString maxValueStr = QString::number(params.fullScaleMax, 'f', 3);
            if (params.fullScaleMaxValue2 != 0) {
                maxValueStr += QString(" / %1").arg(params.fullScaleMaxValue2);
            }
            fullScaleMaxObj["field2"] = maxValueStr;
            fullScaleMaxObj["field3"] = params.fullScaleMaxUnit;
            fullScaleMaxObj["field4"] = params.fullScaleMaxCombo;
            fullScaleMaxObj["field5"] = "";
            detailArray.append(fullScaleMaxObj);
        }
    }

    return detailArray;
}
```

## 📊 完整字段覆盖

### **智能显示策略**

| 字段类型 | 显示条件 | 格式化规则 |
|---------|---------|-----------|
| **字符串字段** | `!field.isEmpty()` | 直接显示 |
| **数值字段** | `field != 0.0` | 指定精度格式化 |
| **布尔字段** | `field == true` | 显示"是"/"否" |
| **组合字段** | 主值有效时 | 主值 + 辅助值 + 单位 + 组合框 |

### **数值精度规则**

| 字段类型 | 精度 | 示例 |
|---------|------|------|
| **灵敏度** | 3位小数 | 2.000 |
| **满量程** | 3位小数 | 100.000 |
| **后置增益** | 4位小数 | 1.7500 |
| **总增益** | 2位小数 | 500.18 |
| **激励电压** | 1位小数 | 5.0 |
| **反馈系数** | 6位小数 | 1.000000 |

## 🎯 预期输出格式

### **CSV格式**
```csv
传感器设备,,,
,├─ 序列号,传感器_000001s'da'd,
,├─ 类型,Axial Gage,
,├─ 型号,标准型号,
,├─ 精度,1,
,├─ EDS标识,EDS001,
,├─ 尺寸,标准尺寸,
,├─ 单位,kN,
,├─ 灵敏度,2.000,
,├─ 校准启用,是,
,├─ 校准日期,2025/08/13 10:30:00.0,
,├─ 校准执行人,Admin,
,├─ 单位类型,Force,
,├─ 单位值,kN,
,├─ 输入范围,±10V,
,├─ 满量程最大值,100.000 / 100,kN,kN
,├─ 满量程最小值,0.000 / 0,kN,kN
,├─ 极性,Positive,
,├─ 前置放大增益,285.9600,
,├─ 后置放大增益,1.7500 / 1,V/V,
,├─ 总增益,500.18 / 500,V/V,
,├─ Delta K增益,1.0000 / 1,V/V,
,├─ 比例因子,1.0 (1),V/V,
,├─ 激励启用,是,
,├─ 激励电压,5.0 / 5,V,
,├─ 激励平衡,0.0 (0),V,
,├─ 激励频率,DC,
,├─ 相位,0° (0),°,
,├─ 编码器分辨率,1024 (1024),pulses/rev,
,├─ 正向反馈系数,1.000000,
,├─ 负向反馈系数,0.000000,
,├─ 极性标志,正向,
,└─────────────────────────,
```

### **JSON格式**
```json
[
  {
    "# 实验工程配置文件": "传感器设备",
    "field2": "", "field3": "", "field4": "", "field5": ""
  },
  {
    "# 实验工程配置文件": "",
    "field2": "├─ EDS标识",
    "field3": "EDS001",
    "field4": "", "field5": ""
  },
  {
    "# 实验工程配置文件": "",
    "field2": "├─ 满量程最大值",
    "field3": "100.000 / 100",
    "field4": "kN",
    "field5": "kN"
  }
]
```

## 🎉 总结

本次实现基于完整的 `SensorParams` 结构体，确保：

1. **完整性**：覆盖所有58个字段
2. **智能性**：只显示有值的字段
3. **一致性**：CSV和JSON格式保持一致
4. **可读性**：合理的分组和格式化
5. **扩展性**：易于添加新字段

传感器详细信息现在完全集成到硬件树结构中，作为传感器设备的子项显示，符合用户的要求！
