/**
 * @file DeviceManager.h
 * @brief 设备管理模块 - 基于现有DataManager_1_2系列
 * @details 封装现有的数据管理器，提供统一的设备管理接口
 * <AUTHOR> Assistant
 * @date 2025-01-27
 * @version 3.4.0
 */

#ifndef DEVICEMANAGER_H
#define DEVICEMANAGER_H

#include <QObject>
#include <QString>
#include <QTreeWidgetItem>
#include "SensorDialog_1_2.h"
#include "ActuatorDialog_1_2.h"

// 前向声明
class SensorDataManager_1_2;
class ActuatorDataManager_1_2;
class CtrlChanDataManager;
class HardwareNodeResDataManager;
class CMyMainWindow;

/**
 * @brief 设备管理器类 - 基于现有DataManager_1_2系列
 * @details 封装现有的数据管理器，提供统一的设备管理接口
 */
class DeviceManager : public QObject {
    Q_OBJECT
    
public:
    explicit DeviceManager(QObject* parent = nullptr);
    ~DeviceManager();
    
    // 设置主窗口引用和现有数据管理器 (不创建新的)
    void setMainWindow(CMyMainWindow* mainWindow);
    void setSensorDataManager(SensorDataManager_1_2* manager);
    void setActuatorDataManager(ActuatorDataManager_1_2* manager);
    void setCtrlChanDataManager(CtrlChanDataManager* manager);
    void setHardwareNodeResDataManager(HardwareNodeResDataManager* manager);
    
    // 作动器组管理
    bool createActuatorGroup();
    bool createActuatorGroup(const QString& groupName);
    bool editActuatorGroup(QTreeWidgetItem* item);

    // 🆕 新增：传感器组管理
    bool createSensorGroup();
    bool createSensorGroup(const QString& groupName);

    // 🆕 新增：设备创建管理
    bool createActuator(QTreeWidgetItem* groupItem);
    bool createSensor(QTreeWidgetItem* groupItem);

    // 🆕 新增：设备编辑管理
    bool editActuatorDevice(QTreeWidgetItem* item);
    bool editSensorDevice(QTreeWidgetItem* item);

    // 🆕 新增：设备删除管理
    bool deleteActuatorDevice(QTreeWidgetItem* item);
    bool deleteSensorDevice(QTreeWidgetItem* item);

    // 🆕 新增：硬件节点管理
    bool createHardwareNode();
    bool deleteHardwareNode(QTreeWidgetItem* item);
    bool editHardwareNode(QTreeWidgetItem* item);

    // 🆕 新增：控制通道管理
    bool createControlChannel(QTreeWidgetItem* parentItem);
    bool deleteControlChannel(QTreeWidgetItem* item);

    // 🆕 新增：设备关联管理
    bool clearAssociation(QTreeWidgetItem* item);
    bool clearSingleAssociation(QTreeWidgetItem* item);
    bool clearAllAssociation(QTreeWidgetItem* item);

    // 数据同步
    bool synchronizeAllDataManagers();
    
    // 获取现有数据管理器 (不创建新的)
    SensorDataManager_1_2* getSensorDataManager() const;
    ActuatorDataManager_1_2* getActuatorDataManager() const;
    CtrlChanDataManager* getCtrlChanDataManager() const;
    HardwareNodeResDataManager* getHardwareNodeResDataManager() const;
    
signals:
    // 设备状态信号
    void deviceStatusChanged(const QString& status);
    void deviceError(const QString& error);
    
    // 设备组相关信号
    void deviceGroupCreated(const QString& deviceType, const QString& groupName);
    void deviceGroupEdited(const QString& deviceType, const QString& groupName);
    void deviceGroupDeleted(const QString& deviceType, const QString& groupName);
    
    // 🆕 新增：设备管理信号
    void deviceCreated(const QString& deviceType, const QString& deviceName);
    void deviceEdited(const QString& deviceType, const QString& deviceName);
    void deviceDeleted(const QString& deviceType, const QString& deviceName);
    
    // 🆕 新增：硬件节点信号
    void hardwareNodeCreated(const QString& nodeName);
    void hardwareNodeEdited(const QString& nodeName);
    void hardwareNodeDeleted(const QString& nodeName);
    
    // 🆕 新增：控制通道信号
    void controlChannelCreated(const QString& channelName);
    void controlChannelDeleted(const QString& channelName);
    
    // 🆕 新增：关联管理信号
    void associationCleared(const QString& itemName);
    void singleAssociationCleared(const QString& itemName);
    void allAssociationCleared(const QString& itemName);
    void dataManagersSynchronized();
    
private:
    // 引用现有组件 (不创建新的)
    CMyMainWindow* mainWindow_;
    SensorDataManager_1_2* sensorDataManager_;
    ActuatorDataManager_1_2* actuatorDataManager_;
    CtrlChanDataManager* ctrlChanDataManager_;
    HardwareNodeResDataManager* hardwareNodeResDataManager_;
    
    // 辅助方法
    int extractActuatorGroupIdFromItem(QTreeWidgetItem* item) const;
    int extractSensorGroupIdFromItem(QTreeWidgetItem* item) const;
};

#endif // DEVICEMANAGER_H 