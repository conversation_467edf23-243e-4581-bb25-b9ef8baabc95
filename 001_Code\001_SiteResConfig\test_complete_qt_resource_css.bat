@echo off
chcp 65001 > nul
echo ========================================
echo Qt资源系统CSS完整功能测试
echo ========================================
echo.

echo 🔍 预编译检查...
echo.

echo 📁 检查必要文件是否存在：
if exist "SiteResConfig\resources.qrc" (
    echo ✅ 资源配置文件: resources.qrc
) else (
    echo ❌ 缺少资源配置文件: resources.qrc
    goto :error
)

if exist "SiteResConfig\Res\style.css" (
    echo ✅ 样式表文件: Res\style.css
    for %%A in (SiteResConfig\Res\style.css) do echo    文件大小: %%~zA 字节
) else (
    echo ❌ 缺少样式表文件: Res\style.css
    goto :error
)

echo.
echo 📄 检查资源配置文件内容：
type SiteResConfig\resources.qrc
echo.

echo 📄 检查项目文件中的资源配置：
findstr /i "resources.qrc" SiteResConfig\SiteResConfig_Simple.pro
if %ERRORLEVEL% EQU 0 (
    echo ✅ 项目文件包含资源配置
) else (
    echo ❌ 项目文件缺少资源配置
    goto :error
)
echo.

echo 🔧 开始编译...
cd SiteResConfig
qmake SiteResConfig_Simple.pro
if %ERRORLEVEL% NEQ 0 (
    echo ❌ qmake 失败！
    goto :error
)

mingw32-make clean
mingw32-make
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 编译失败！
    echo.
    echo 🔍 可能的问题：
    echo 1. 资源文件路径不正确
    echo 2. .qrc文件格式错误
    echo 3. CSS文件编码问题
    echo 4. Qt资源系统配置错误
    goto :error
)

echo ✅ 编译成功！
echo.

echo 📊 检查生成的文件：
if exist "debug\SiteResConfig.exe" (
    echo ✅ 可执行文件生成成功
    for %%A in (debug\SiteResConfig.exe) do echo    文件大小: %%~zA 字节
) else (
    echo ❌ 可执行文件未生成
    goto :error
)
echo.

echo 🚀 启动应用程序进行功能测试...
echo.
echo 📋 **Qt资源系统CSS功能验证**：
echo.
echo 🎯 **实现总结**：
echo - ✅ 创建了resources.qrc资源配置文件
echo - ✅ 将Res/style.css嵌入到应用程序资源中
echo - ✅ 修改代码从 ":/styles/Res/style.css" 读取样式
echo - ✅ 样式表编译到可执行文件，无需外部依赖
echo.
echo 🧪 **测试项目**：
echo.
echo 1️⃣ **资源加载验证**
echo    - 检查日志是否显示 "样式表已从资源加载"
echo    - 验证资源路径 ":/styles/Res/style.css" 正确
echo.
echo 2️⃣ **样式效果验证**
echo    - 树形控件使用自定义样式（非系统默认）
echo    - 悬停效果：浅灰色背景 (#F3F4F6)
echo    - 选中效果：蓝色背景 (#0969DA)
echo    - 拖拽效果：橙色高亮 (#FB8500)
echo.
echo 3️⃣ **独立性验证**
echo    - 应用程序可独立运行，无需外部CSS文件
echo    - 删除Res文件夹后程序仍正常运行
echo    - 样式表已嵌入到exe文件中
echo.
echo 4️⃣ **功能完整性验证**
echo    - 硬件节点资源树样式正确
echo    - 实验配置树样式正确
echo    - 拖拽功能正常工作
echo    - 右键菜单功能正常
echo.

start "" "debug\SiteResConfig.exe"

echo 📊 应用程序已启动！
echo.
echo 🔍 **验证清单**：
echo.
echo ☐ 1. 查看日志输出，确认显示 "样式表已从资源加载: :/styles/Res/style.css"
echo ☐ 2. 检查树形控件外观，确认使用了自定义样式（非系统默认）
echo ☐ 3. 测试鼠标悬停效果，项目应显示浅灰色背景
echo ☐ 4. 测试选中效果，选中项应显示蓝色背景
echo ☐ 5. 测试拖拽功能，拖拽时应显示橙色高亮
echo ☐ 6. 验证分支线和展开按钮样式是否正确
echo ☐ 7. 确认应用程序可以独立运行（无外部CSS依赖）
echo.
echo 💡 **成功标志**：
echo - 日志显示资源加载成功
echo - 树形控件显示美观的自定义样式
echo - 所有交互效果正常工作
echo - 应用程序完全独立，无外部文件依赖
echo.
echo 🎉 如果以上验证都通过，说明Qt资源系统CSS功能实现成功！
echo.
pause
goto :end

:error
echo.
echo ❌ 测试失败！请检查上述错误信息。
echo.
pause

:end
