#pragma once

/**
 * @file NodeConfigDialog.h
 * @brief 硬件节点配置对话框类定义
 * @details 使用Qt Designer设计的硬件节点配置对话框
 * <AUTHOR> Assistant
 * @date 2025-08-06
 * @version 1.0.0
 */

#include <QtWidgets/QDialog>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QCheckBox>
#include <QtWidgets/QScrollArea>
#include <QtCore/QString>
#include <QtCore/QList>
#include "HardwareNodeStructs.h"  // 🔄 修正：包含独立的硬件节点结构体定义

QT_BEGIN_NAMESPACE
QT_END_NAMESPACE

namespace Ui {
class NodeConfigDialog;
}

// 注意：ChannelInfo 和 NodeConfigParams 结构体现在在 HardwareNodeStructs.h 中定义

namespace UI {

/**
 * @brief 通道配置控件结构体
 * @details 包含单个通道的所有配置控件
 */
struct ChannelConfigWidget {
    QGroupBox* groupBox;
    QGridLayout* layout;
    QLabel* ipLabel;
    QLineEdit* ipEdit;
    QLabel* portLabel;
    QSpinBox* portSpinBox;
    QCheckBox* enabledCheckBox;

    ChannelConfigWidget() : groupBox(nullptr), layout(nullptr),
                           ipLabel(nullptr), ipEdit(nullptr),
                           portLabel(nullptr), portSpinBox(nullptr),
                           enabledCheckBox(nullptr) {}
};

/**
 * @brief 硬件节点配置对话框类
 * @details 支持动态生成任意数量的通道配置
 */
class NodeConfigDialog : public QDialog {
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param nodeName 节点名称
     * @param parent 父窗口
     */
    explicit NodeConfigDialog(const QString& nodeName = "LD-B1", QWidget* parent = nullptr);

    /**
     * @brief 析构函数
     */
    virtual ~NodeConfigDialog();

    /**
     * @brief 获取节点配置参数
     * @return 节点配置参数结构体
     */
    NodeConfigParams getNodeConfigParams() const;

    /**
     * @brief 设置节点配置参数
     * @param params 节点配置参数
     */
    void setNodeConfigParams(const NodeConfigParams& params);

private slots:
    /**
     * @brief 通道数量改变槽函数
     */
    void onChannelCountChanged();

    /**
     * @brief 确定按钮点击前的验证
     */
    void onAcceptClicked();

private:
    Ui::NodeConfigDialog* ui;
    QString nodeName_;
    QList<ChannelConfigWidget> channelWidgets_; // 动态通道控件列表
    QScrollArea* scrollArea_; // 滚动区域
    QWidget* scrollWidget_; // 滚动内容区域
    QVBoxLayout* scrollLayout_; // 滚动区域布局

    /**
     * @brief 初始化界面
     */
    void initializeUI();

    /**
     * @brief 连接信号槽
     */
    void connectSignals();

    /**
     * @brief 更新通道界面
     */
    void updateChannelUI();

    /**
     * @brief 设置滚动区域
     */
    void setupScrollArea();

    /**
     * @brief 创建单个通道配置控件
     * @param channelId 通道ID
     * @return 通道配置控件结构体
     */
    ChannelConfigWidget createChannelWidget(int channelId);

    /**
     * @brief 清理所有通道控件
     */
    void clearChannelWidgets();
};

} // namespace UI
