#include <QApplication>
#include <QMainWindow>
#include <QVBoxLayout>
#include <QPushButton>
#include <QHBoxLayout>
#include <QGroupBox>
#include "DetailInfoPanel.h"
#include <QDebug> // Added for qDebug

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // 创建主窗口
    QMainWindow mainWindow;
    mainWindow.setWindowTitle("DetailInfoPanel 测试程序");
    mainWindow.resize(800, 600);
    
    // 创建中央部件
    QWidget* centralWidget = new QWidget(&mainWindow);
    mainWindow.setCentralWidget(centralWidget);
    
    // 创建主布局
    QVBoxLayout* mainLayout = new QVBoxLayout(centralWidget);
    
    // 创建按钮组
    QGroupBox* buttonGroup = new QGroupBox("测试功能", centralWidget);
    QHBoxLayout* buttonLayout = new QHBoxLayout(buttonGroup);
    
    // 测试按钮1：设置测试数据
    QPushButton* testDataBtn = new QPushButton("设置测试数据", buttonGroup);
    buttonLayout->addWidget(testDataBtn);
    
    // 测试按钮2：设置控制通道信息
    QPushButton* controlChannelBtn = new QPushButton("设置控制通道信息", buttonGroup);
    buttonLayout->addWidget(controlChannelBtn);
    
    // 测试按钮3：清空信息
    QPushButton* clearBtn = new QPushButton("清空信息", buttonGroup);
    buttonLayout->addWidget(clearBtn);
    
    // 🚫 已移除：HTML内容测试按钮
    
    buttonLayout->addStretch();
    mainLayout->addWidget(buttonGroup);
    
    // 创建DetailInfoPanel
    DetailInfoPanel* detailPanel = new DetailInfoPanel(centralWidget);
    mainLayout->addWidget(detailPanel);
    
    // 连接信号槽
    QObject::connect(testDataBtn, &QPushButton::clicked, [detailPanel]() {
        qDebug() << "点击：设置测试数据";
        detailPanel->setTestData();
    });
    
    QObject::connect(controlChannelBtn, &QPushButton::clicked, [detailPanel]() {
        qDebug() << "点击：设置控制通道信息";
        
        // 创建测试用的子节点信息
        QList<SubNodeInfo> subNodes;
        
        // 子节点1：载荷1传感器
        SubNodeInfo load1Node;
        load1Node.name = "载荷1传感器";
        load1Node.type = "载荷传感器";
        load1Node.deviceName = "LS-001";
        load1Node.deviceId = "LS1_1";
        load1Node.isConnected = true;
        load1Node.setProperty("量程", "0-1000N");
        load1Node.setProperty("精度", "±0.1%");
        load1Node.setProperty("极性", "正极性");
        load1Node.setProperty("测量单位", "N");
        subNodes.append(load1Node);
        
        // 子节点2：位置传感器
        SubNodeInfo posNode;
        posNode.name = "位置传感器";
        posNode.type = "位置传感器";
        posNode.deviceName = "PS-001";
        posNode.deviceId = "PS1_1";
        posNode.isConnected = true;
        posNode.setProperty("量程", "±100mm");
        posNode.setProperty("精度", "±0.01mm");
        posNode.setProperty("极性", "正极性");
        posNode.setProperty("测量单位", "mm");
        subNodes.append(posNode);
        
        // 子节点3：控制作动器
        SubNodeInfo actuatorNode;
        actuatorNode.name = "控制作动器";
        actuatorNode.type = "伺服作动器";
        actuatorNode.deviceName = "SA-001";
        actuatorNode.deviceId = "SA1_1";
        actuatorNode.isConnected = true;
        actuatorNode.setProperty("控制模式", "力控制");
        actuatorNode.setProperty("极性", "正极性");
        actuatorNode.setProperty("最大行程", "±100mm");
        actuatorNode.setProperty("最大力", "1000N");
        subNodes.append(actuatorNode);
        
        // 设置控制通道信息
        detailPanel->setControlChannelInfo("CH1", subNodes);
    });
    
    QObject::connect(clearBtn, &QPushButton::clicked, [detailPanel]() {
        qDebug() << "点击：清空信息";
        detailPanel->clearInfo();
    });
    
    // 🚫 已移除：HTML内容测试连接
    
    // 显示窗口
    mainWindow.show();
    
    return app.exec();
} 