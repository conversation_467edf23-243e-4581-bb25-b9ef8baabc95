/**
 * @file ActuatorViewModel1_2.cpp
 * @brief 作动器视图模型1_2版本实现 - 主界面解耦合专用
 * @details 基于MVVM模式实现，封装MainWindow中与作动器数据交互的所有逻辑
 *          实现UI层和数据层的完全分离，提供统一的业务逻辑接口
 * <AUTHOR> Assistant
 * @date 2025-08-22
 * @version 1.2.0
 */

#include "ActuatorViewModel_1_2.h"
#include "ActuatorDataManager_1_2.h"
#include "ActuatorDialog_1_2.h"  // 包含UI::ActuatorParams和UI::ActuatorGroup定义
#include "DataModels_Fixed.h"
#include <QDebug>
#include <QDateTime>
#include <QMutexLocker>
#include <QJsonArray>
#include <QJsonObject>
#include <QRegularExpression>
#include <algorithm>
#include <QFile>
#include <QJsonParseError>

// ==================== 构造函数和析构函数 ====================

ActuatorViewModel1_2::ActuatorViewModel1_2(QObject* parent)
    : QObject(parent)
    , dataManager_(std::make_unique<ActuatorDataManager_1_2>())
    , ownsDataManager_(true)
    , errorType_(ErrorType::NoError)
    , hasError_(false)
    , cacheValid_(false)
    , statisticsLoaded_(false)
    , groupsLoaded_(false)
    , actuatorsLoaded_(false)
#ifdef QT_TESTLIB_LIB
    , testMode_(false)
#endif
{
    initialize();
    addLogEntry("INFO", u8"ActuatorViewModel1_2 初始化完成 (自动创建数据管理器)");
}

ActuatorViewModel1_2::ActuatorViewModel1_2(ActuatorDataManager_1_2* dataManager, QObject* parent)
    : QObject(parent)
    , dataManager_(dataManager ? std::unique_ptr<ActuatorDataManager_1_2>(dataManager) : std::make_unique<ActuatorDataManager_1_2>())
    , ownsDataManager_(dataManager == nullptr)
    , errorType_(ErrorType::NoError)
    , hasError_(false)
    , cacheValid_(false)
    , statisticsLoaded_(false)
    , groupsLoaded_(false)
    , actuatorsLoaded_(false)
#ifdef QT_TESTLIB_LIB
    , testMode_(false)
#endif
{
    initialize();
    addLogEntry("INFO", QString(u8"ActuatorViewModel1_2 初始化完成 (使用%1数据管理器)")
                .arg(ownsDataManager_ ? u8"自创建" : u8"外部"));
}

ActuatorViewModel1_2::~ActuatorViewModel1_2()
{
    addLogEntry("INFO", u8"ActuatorViewModel1_2 开始析构");
    cleanup();
    addLogEntry("INFO", u8"ActuatorViewModel1_2 析构完成");
}

// ==================== 初始化和清理方法 ====================

void ActuatorViewModel1_2::initialize()
{
    // 设置默认配置
    config_.enableCache = true;
    config_.cacheTimeout = 300;
    config_.enableValidation = true;
    config_.enableLogging = true;
    config_.logLevel = "INFO";
    config_.threadSafe = true;

    // 初始化缓存时间戳
    cacheTimestamp_ = QDateTime::currentDateTime();

    // 初始化数据格式版本
    dataFormatVersion_ = CURRENT_FORMAT_VERSION;

    clearError();
    clearCache();
}

void ActuatorViewModel1_2::cleanup()
{
    try {
        // 清理缓存
        clearCache();
        
        // 清理日志
        logEntries_.clear();
        
        // 释放数据管理器（如果拥有所有权）
        if (ownsDataManager_ && dataManager_) {
            addLogEntry("INFO", u8"释放数据管理器资源");
            dataManager_.reset();
        }
        
        addLogEntry("INFO", u8"ViewModel资源清理完成");
    } catch (const std::exception& e) {
        qDebug() << u8"ActuatorViewModel1_2析构时发生异常:" << e.what();
    } catch (...) {
        qDebug() << u8"ActuatorViewModel1_2析构时发生未知异常";
    }
}

void ActuatorViewModel1_2::releaseResources()
{
    QMutexLocker locker(&dataMutex_);
    
    // 清空所有缓存
    actuatorCache_.clear();
    groupCache_.clear();
    typeStatisticsCache_.clear();
    unitStatisticsCache_.clear();
    polarityStatisticsCache_.clear();
    
    // 重置状态标志
    cacheValid_ = false;
    statisticsLoaded_ = false;
    groupsLoaded_ = false;
    actuatorsLoaded_ = false;
}

// ==================== 配置管理接口 ====================

void ActuatorViewModel1_2::setConfig(const Config& config)
{
    QMutexLocker locker(&dataMutex_);
    
    config_ = config;
    
    // 如果禁用缓存，清空现有缓存
    if (!config_.enableCache) {
        clearCache();
    }
    
    addLogEntry("INFO", QString(u8"配置已更新: 缓存=%1, 验证=%2, 日志=%3")
                .arg(config_.enableCache ? u8"启用" : u8"禁用")
                .arg(config_.enableValidation ? u8"启用" : u8"禁用")
                .arg(config_.enableLogging ? u8"启用" : u8"禁用"));
}

ActuatorViewModel1_2::Config ActuatorViewModel1_2::getConfig() const
{
    QMutexLocker locker(&dataMutex_);
    return config_;
}

// ==================== 基础CRUD操作接口 ====================

//bool ActuatorViewModel1_2::saveActuator(const UI::ActuatorParams_1_2& params)
//{
//    if (config_.threadSafe) {
//        return safeDataModify<bool>([this, &params]() {
//            return saveActuatorImpl(params);
//        });
//    } else {
//        return saveActuatorImpl(params);
//    }
//} // ❌ 注释：不再使用无组ID版本

//bool ActuatorViewModel1_2::saveActuatorImpl(const UI::ActuatorParams_1_2& params)
//{
//    clearError();
//    
//    try {
//        // 数据验证
//        if (config_.enableValidation && !validateActuatorParams(params)) {
//            return false; // 错误信息已在validateActuatorParams中设置
//        }
//        
//        // 检查数据管理器
//        if (!dataManager_) {
//            setError(u8"数据管理器未初始化", ErrorType::SystemError);
//            return false;
//        }
//        
//        // 尝试获取作动器的现有信息，以确定其真实的组ID
//        int targetGroupId = 1; // 默认组ID
//        try {
//            // 使用新添加的方法获取作动器的真实组ID
//            int existingGroupId = dataManager_->getActuatorGroupId(params.params.sn);
//            if (existingGroupId != -1) {
//                // 作动器已存在，使用其真实的组ID
//                targetGroupId = existingGroupId;
//                addLogEntry("INFO", QString(u8"作动器 %1 已存在，使用真实组ID: %2").arg(params.params.sn).arg(targetGroupId));
//            } else {
//                // 作动器不存在，使用默认组ID
//                addLogEntry("INFO", QString(u8"作动器 %1 不存在，使用默认组ID: %2").arg(params.params.sn).arg(targetGroupId));
//            }
//        } catch (...) {
//            // 发生异常，使用默认组ID
//            addLogEntry("WARNING", QString(u8"获取作动器组ID时发生异常，使用默认组ID: %1").arg(targetGroupId));
//        }
//        
//        // 保存到数据管理器，使用确定的组ID
//        bool success = dataManager_->saveActuatorDetailedParams(params, targetGroupId);
//        if (!success) {
//            setError(QString(u8"保存作动器失败: %1").arg(dataManager_->getLastError()), ErrorType::DatabaseError);
//            return false;
//        }
//        
//        // 更新缓存
//        if (config_.enableCache) {
//            actuatorCache_[params.params.sn] = params;
//            invalidateCache(); // 使统计缓存失效
//        }
//
//        // 发送信号
//        emit actuatorDataChanged(params.params.sn, "create");
//        emit statisticsChanged();
//        emit operationCompleted("saveActuator", true);
//
//        addLogEntry("INFO", QString(u8"作动器保存成功: %1，组ID: %2").arg(params.params.sn).arg(targetGroupId));
//        return true;
//        
//    } catch (const std::exception& e) {
//        setError(QString(u8"保存作动器时发生异常: %1").arg(e.what()), ErrorType::SystemError);
//        emit operationCompleted("saveActuator", false);
//        return false;
//    }
//} // ❌ 注释：不再使用无组ID版本

bool ActuatorViewModel1_2::saveActuator(const UI::ActuatorParams_1_2& params, int groupId)
{
    if (config_.threadSafe) {
        return safeDataModify<bool>([this, &params, groupId]() {
            return saveActuatorImpl(params, groupId);
        });
    } else {
        return saveActuatorImpl(params, groupId);
    }
}

bool ActuatorViewModel1_2::saveActuatorImpl(const UI::ActuatorParams_1_2& params, int groupId)
{
    clearError();
    
    try {
        // 数据验证
        if (config_.enableValidation && !validateActuatorParams(params)) {
            return false; // 错误信息已在validateActuatorParams中设置
        }
        
        // 检查数据管理器
        if (!dataManager_) {
            setError(u8"数据管理器未初始化", ErrorType::SystemError);
            return false;
        }
        
        // 保存到数据管理器
        bool success = dataManager_->saveActuatorDetailedParams(params, groupId);
        if (!success) {
            setError(QString(u8"保存作动器失败: %1").arg(dataManager_->getLastError()), ErrorType::DatabaseError);
            return false;
        }
        
        // 🔧 修复：保存时也使用组ID复合Key更新缓存
        if (config_.enableCache) {
            QString cacheKey = QString("%1_%2").arg(params.params.sn).arg(groupId);
            actuatorCache_[cacheKey] = params;
            invalidateCache(); // 使统计缓存失效
        }

        // 发送信号
        emit actuatorDataChanged(params.params.sn, "create");
        emit statisticsChanged();
        emit operationCompleted("saveActuator", true);

        addLogEntry("INFO", QString(u8"作动器保存成功: %1").arg(params.params.sn));
        return true;
        
    } catch (const std::exception& e) {
        setError(QString(u8"保存作动器时发生异常: %1").arg(e.what()), ErrorType::SystemError);
        emit operationCompleted("saveActuator", false);
        return false;
    }
}

// 🆕 新增：带组ID的updateActuatorImpl函数
bool ActuatorViewModel1_2::updateActuatorImpl(const QString& serialNumber, const UI::ActuatorParams_1_2& params, int groupId)
{
    clearError();
    
    try {
        // 数据验证
        if (config_.enableValidation && !validateActuatorParams(params)) {
            return false; // 错误信息已在validateActuatorParams中设置
        }
        
        // 检查数据管理器
        if (!dataManager_) {
            setError(u8"数据管理器未初始化", ErrorType::SystemError);
            return false;
        }
        
        // 检查作动器是否存在于指定组中
        if (!dataManager_->hasActuator(serialNumber, groupId)) {
            setError(QString(u8"作动器不存在于组 %1 中: %2").arg(groupId).arg(serialNumber), ErrorType::NotFoundError);
            return false;
        }
        
        // 🔒 验证：不允许修改序列号
        if (serialNumber != params.params.sn) {
            setError(QString(u8"不允许修改作动器序列号：原序列号='%1'，新序列号='%2'").arg(serialNumber).arg(params.params.sn), ErrorType::ValidationError);
            return false;
        }
        
        // 更新数据管理器
        bool success = dataManager_->updateActuatorDetailedParams(serialNumber, params, groupId);
        if (!success) {
            setError(QString(u8"更新作动器失败: %1").arg(dataManager_->getLastError()), ErrorType::DatabaseError);
            return false;
        }
        
        // 🔧 修复：更新缓存时使用组ID复合Key
        if (config_.enableCache) {
            QString cacheKey = QString("%1_%2").arg(params.params.sn).arg(groupId);
            actuatorCache_[cacheKey] = params;
            // 注意：序列号不允许修改，所以不需要删除旧的缓存条目
            invalidateCache(); // 使统计缓存失效
        }
        
        // 发送信号
        emit actuatorDataChanged(params.params.sn, "update");
        emit statisticsChanged();
        emit operationCompleted("updateActuator", true);

        addLogEntry("INFO", QString(u8"作动器更新成功: %1 (组ID: %2)").arg(params.params.sn).arg(groupId));
        return true;
        
    } catch (const std::exception& e) {
        setError(QString(u8"更新作动器时发生异常: %1").arg(e.what()), ErrorType::SystemError);
        emit operationCompleted("updateActuator", false);
        return false;
    }
}

// 🆕 新增：带组ID的removeActuatorImpl函数
bool ActuatorViewModel1_2::removeActuatorImpl(const QString& serialNumber, int groupId)
{
    clearError();

    try {
        // 检查数据管理器
        if (!dataManager_) {
            setError(u8"数据管理器未初始化", ErrorType::SystemError);
            return false;
        }

        // 检查作动器是否存在于指定组中
        if (!dataManager_->hasActuator(serialNumber, groupId)) {
            setError(QString(u8"作动器不存在于组 %1 中: %2").arg(groupId).arg(serialNumber), ErrorType::NotFoundError);
            return false;
        }

        // 从数据管理器删除
        bool success = dataManager_->removeActuatorDetailedParams(serialNumber, groupId);
        if (!success) {
            setError(QString(u8"删除作动器失败: %1").arg(dataManager_->getLastError()), ErrorType::DatabaseError);
            return false;
        }

        // 🔧 修复：删除缓存时使用组ID复合Key
        if (config_.enableCache) {
            QString cacheKey = QString("%1_%2").arg(serialNumber).arg(groupId);
            actuatorCache_.remove(cacheKey);
            invalidateCache(); // 使统计缓存失效
        }

        // 发送信号
        emit actuatorDataChanged(serialNumber, "delete");
        emit statisticsChanged();
        emit operationCompleted("removeActuator", true);

        addLogEntry("INFO", QString(u8"作动器删除成功: %1 (组ID: %2)").arg(serialNumber).arg(groupId));
        return true;

    } catch (const std::exception& e) {
        setError(QString(u8"删除作动器时发生异常: %1").arg(e.what()), ErrorType::SystemError);
        emit operationCompleted("removeActuator", false);
        return false;
    }
}

// 🆕 新增：带组ID的hasActuatorImpl函数
bool ActuatorViewModel1_2::hasActuatorImpl(const QString& serialNumber, int groupId) const
{
    try {
        // 检查缓存
        if (config_.enableCache && actuatorCache_.contains(serialNumber)) {
            // 从缓存获取作动器，验证是否属于指定组
            // 这里简化处理，如果需要更精确的组验证，可以调用数据管理器
            return true;
        }

        // 检查数据管理器
        if (!dataManager_) {
            return false;
        }

        return dataManager_->hasActuator(serialNumber, groupId);

    } catch (const std::exception& e) {
        addLogEntry("ERROR", QString(u8"检查作动器存在性时发生异常: %1").arg(e.what()));
        return false;
    }
}

//UI::ActuatorParams_1_2 ActuatorViewModel1_2::getActuator(const QString& serialNumber) const
//{
//    if (config_.threadSafe) {
//        return safeDataAccess<UI::ActuatorParams_1_2>([this, &serialNumber]() {
//            return getActuatorImpl(serialNumber);
//        });
//    } else {
//        return getActuatorImpl(serialNumber);
//    }
//} // ❌ 注释：不再使用无组ID版本

//UI::ActuatorParams_1_2 ActuatorViewModel1_2::getActuatorImpl(const QString& serialNumber) const
//{
//    clearError();
//    
//    try {
//        // 检查缓存
//        if (config_.enableCache && actuatorCache_.contains(serialNumber)) {
//            addLogEntry("DEBUG", QString(u8"从缓存获取作动器: %1").arg(serialNumber));
//            return actuatorCache_[serialNumber];
//        }
//        
//        // 检查数据管理器
//        if (!dataManager_) {
//            setError(u8"数据管理器未初始化", ErrorType::SystemError);
//            return UI::ActuatorParams_1_2();
//        }
//        
//        // 首先获取作动器的组ID
//        int groupId = dataManager_->getActuatorGroupId(serialNumber);
//        if (groupId == -1) {
//            setError(QString(u8"未找到作动器所属组: %1").arg(serialNumber), ErrorType::NotFoundError);
//            return UI::ActuatorParams_1_2();
//        }
//        
//        // 从数据管理器获取指定组中的作动器
//        UI::ActuatorParams_1_2 params = dataManager_->getActuatorDetailedParams(serialNumber, groupId);
//        if (params.params.sn.isEmpty()) {
//            setError(QString(u8"作动器不存在: %1").arg(serialNumber), ErrorType::NotFoundError);
//            return UI::ActuatorParams_1_2();
//        }
//        
//        // 更新缓存
//        if (config_.enableCache) {
//            actuatorCache_[serialNumber] = params;
//        }
//        
//        addLogEntry("DEBUG", QString(u8"获取作动器成功: %1，组ID: %2").arg(serialNumber).arg(groupId));
//        return params;
//        
//    } catch (const std::exception& e) {
//        setError(QString(u8"获取作动器时发生异常: %1").arg(e.what()), ErrorType::SystemError);
//        return UI::ActuatorParams_1_2();
//    }
//} // ❌ 注释：不再使用无组ID版本

UI::ActuatorParams_1_2 ActuatorViewModel1_2::getActuator(const QString& serialNumber, int groupId) const
{
    if (config_.threadSafe) {
        return safeDataAccess<UI::ActuatorParams_1_2>([this, &serialNumber, groupId]() {
            return getActuatorImpl(serialNumber, groupId);
        });
    } else {
        return getActuatorImpl(serialNumber, groupId);
    }
}

UI::ActuatorParams_1_2 ActuatorViewModel1_2::getActuatorImpl(const QString& serialNumber, int groupId) const
{
    clearError();
    
    try {
        // 🔧 修复：检查缓存时考虑组ID，避免返回错误组的数据
        QString cacheKey = QString("%1_%2").arg(serialNumber).arg(groupId);
        if (config_.enableCache && actuatorCache_.contains(cacheKey)) {
            addLogEntry("DEBUG", QString(u8"从缓存获取作动器: %1 (组ID: %2)").arg(serialNumber).arg(groupId));
            return actuatorCache_[cacheKey];
        }
        
        // 检查数据管理器
        if (!dataManager_) {
            setError(u8"数据管理器未初始化", ErrorType::SystemError);
            return UI::ActuatorParams_1_2();
        }
        
        // 从数据管理器获取
        UI::ActuatorParams_1_2 params = dataManager_->getActuatorDetailedParams(serialNumber, groupId);
        if (params.params.sn.isEmpty()) {
            setError(QString(u8"作动器不存在: %1").arg(serialNumber), ErrorType::NotFoundError);
            return UI::ActuatorParams_1_2();
        }
        
        // 🔧 修复：更新缓存时使用组ID复合Key
        if (config_.enableCache) {
            QString cacheKey = QString("%1_%2").arg(serialNumber).arg(groupId);
            actuatorCache_[cacheKey] = params;
        }
        
        addLogEntry("DEBUG", QString(u8"获取作动器成功: %1").arg(serialNumber));
        return params;
        
    } catch (const std::exception& e) {
        setError(QString(u8"获取作动器时发生异常: %1").arg(e.what()), ErrorType::SystemError);
        return UI::ActuatorParams_1_2();
    }
}

//bool ActuatorViewModel1_2::updateActuator(const QString& serialNumber, const UI::ActuatorParams_1_2& params)
//{
//    if (config_.threadSafe) {
//        return safeDataModify<bool>([this, &serialNumber, &params]() {
//            return updateActuatorImpl(serialNumber, params);
//        });
//    } else {
//        return updateActuatorImpl(serialNumber, params);
//    }
//} // ❌ 注释：不再使用无组ID版本

bool ActuatorViewModel1_2::updateActuator(const QString& serialNumber, const UI::ActuatorParams_1_2& params, int groupId)
{
    if (config_.threadSafe) {
        return safeDataModify<bool>([this, &serialNumber, &params, groupId]() {
            return updateActuatorImpl(serialNumber, params, groupId);
        });
    } else {
        return updateActuatorImpl(serialNumber, params, groupId);
    }
}

//bool ActuatorViewModel1_2::updateActuatorImpl(const QString& serialNumber, const UI::ActuatorParams_1_2& params)
//{
//    clearError();
//    
//    try {
//        // 数据验证
//        if (config_.enableValidation && !validateActuatorParams(params)) {
//            return false; // 错误信息已在validateActuatorParams中设置
//        }
//        
//        // 检查数据管理器
//        if (!dataManager_) {
//            setError(u8"数据管理器未初始化", ErrorType::SystemError);
//            return false;
//        }
//        
//        // 检查作动器是否存在
//        if (!dataManager_->hasActuator(serialNumber)) {
//            setError(QString(u8"作动器不存在: %1").arg(serialNumber), ErrorType::NotFoundError);
//            return false;
//        }
//        
//        // 获取作动器所属的组ID
//        int groupId = dataManager_->getActuatorGroupId(serialNumber);
//        if (groupId == -1) {
//            setError(QString(u8"未找到作动器所属组: %1").arg(serialNumber), ErrorType::NotFoundError);
//            return false;
//        }
//        
//        // 更新数据管理器
//        bool success = dataManager_->updateActuatorDetailedParams(serialNumber, params, groupId);
//        if (!success) {
//            setError(QString(u8"更新作动器失败: %1").arg(dataManager_->getLastError()), ErrorType::DatabaseError);
//            return false;
//        }
//        
//        // 更新缓存
//        if (config_.enableCache) {
//            actuatorCache_[params.params.sn] = params;
//            // 如果序列号发生变化，删除旧的缓存条目
//            if (serialNumber != params.params.sn && actuatorCache_.contains(serialNumber)) {
//                actuatorCache_.remove(serialNumber);
//            }
//            invalidateCache(); // 使统计缓存失效
//        }
//        
//        // 发送信号
//        emit actuatorDataChanged(params.params.sn, "update");
//        emit statisticsChanged();
//        emit operationCompleted("updateActuator", true);
//
//        addLogEntry("INFO", QString(u8"作动器更新成功: %1").arg(params.params.sn));
//        return true;
//        
//    } catch (const std::exception& e) {
//        setError(QString(u8"更新作动器时发生异常: %1").arg(e.what()), ErrorType::SystemError);
//        emit operationCompleted("updateActuator", false);
//        return false;
//    }
//} // ❌ 注释：不再使用无组ID版本

//bool ActuatorViewModel1_2::removeActuator(const QString& serialNumber)
//{
//    if (config_.threadSafe) {
//        return safeDataModify<bool>([this, &serialNumber]() {
//            return removeActuatorImpl(serialNumber);
//        });
//    } else {
//        return removeActuatorImpl(serialNumber);
//    }
//} // ❌ 注释：不再使用无组ID版本

bool ActuatorViewModel1_2::removeActuator(const QString& serialNumber, int groupId)
{
    if (config_.threadSafe) {
        return safeDataModify<bool>([this, &serialNumber, groupId]() {
            return removeActuatorImpl(serialNumber, groupId);
        });
    } else {
        return removeActuatorImpl(serialNumber, groupId);
    }
}

//bool ActuatorViewModel1_2::removeActuatorImpl(const QString& serialNumber)
//{
//    clearError();
//
//    try {
//        // 检查数据管理器
//        if (!dataManager_) {
//            setError(u8"数据管理器未初始化", ErrorType::SystemError);
//            return false;
//        }
//
//        // 检查作动器是否存在
//        if (!dataManager_->hasActuator(serialNumber)) {
//            setError(QString(u8"作动器不存在: %1").arg(serialNumber), ErrorType::NotFoundError);
//            return false;
//        }
//
//        // 获取作动器所属的组ID
//        int groupId = dataManager_->getActuatorGroupId(serialNumber);
//        if (groupId == -1) {
//            setError(QString(u8"未找到作动器所属组: %1").arg(serialNumber), ErrorType::NotFoundError);
//            return false;
//        }
//
//        // 从数据管理器删除
//        bool success = dataManager_->removeActuatorDetailedParams(serialNumber, groupId);
//        if (!success) {
//            setError(QString(u8"删除作动器失败: %1").arg(dataManager_->getLastError()), ErrorType::DatabaseError);
//            return false;
//        }
//
//        // 更新缓存
//        if (config_.enableCache) {
//            actuatorCache_.remove(serialNumber);
//            invalidateCache(); // 使统计缓存失效
//        }
//
//        // 发送信号
//        emit actuatorDataChanged(serialNumber, "delete");
//        emit statisticsChanged();
//        emit operationCompleted("removeActuator", true);
//
//        addLogEntry("INFO", QString(u8"作动器删除成功: %1").arg(serialNumber));
//        return true;
//
//    } catch (const std::exception& e) {
//        setError(QString(u8"删除作动器时发生异常: %1").arg(e.what()), ErrorType::SystemError);
//        emit operationCompleted("removeActuator", false);
//        return false;
//    }
//} // ❌ 注释：不再使用无组ID版本

//bool ActuatorViewModel1_2::hasActuator(const QString& serialNumber) const
//{
//    if (config_.threadSafe) {
//        return safeDataAccess<bool>([this, &serialNumber]() {
//            return hasActuatorImpl(serialNumber);
//        });
//    } else {
//        return hasActuatorImpl(serialNumber);
//    }
//} // ❌ 注释：不再使用无组ID版本

//bool ActuatorViewModel1_2::hasActuatorImpl(const QString& serialNumber) const
//{
//    try {
//        // 检查缓存
//        if (config_.enableCache && actuatorCache_.contains(serialNumber)) {
//            return true;
//        }
//
//        // 检查数据管理器
//        if (!dataManager_) {
//            return false;
//        }
//
//        return dataManager_->hasActuator(serialNumber);
//
//    } catch (const std::exception& e) {
//        addLogEntry("ERROR", QString(u8"检查作动器存在性时发生异常: %1").arg(e.what()));
//        return false;
//    }
//} // ❌ 注释：不再使用无组ID版本

// ==================== 作动器组管理接口 ====================

bool ActuatorViewModel1_2::saveActuatorGroup(const UI::ActuatorGroup_1_2& group)
{
    if (config_.threadSafe) {
        return safeDataModify<bool>([this, &group]() {
            return saveActuatorGroupImpl(group);
        });
    } else {
        return saveActuatorGroupImpl(group);
    }
}

bool ActuatorViewModel1_2::saveActuatorGroupImpl(const UI::ActuatorGroup_1_2& group)
{
    clearError();

    try {
        // 数据验证
        if (config_.enableValidation && !validateActuatorGroup(group)) {
            return false; // 错误信息已在validateActuatorGroup中设置
        }

        // 检查数据管理器
        if (!dataManager_) {
            setError(u8"数据管理器未初始化", ErrorType::SystemError);
            return false;
        }

        // 保存到数据管理器
        bool success = dataManager_->saveActuatorGroup(group);
        if (!success) {
            setError(QString(u8"保存作动器组失败: %1").arg(dataManager_->getLastError()), ErrorType::DatabaseError);
            return false;
        }

        // 🔧 修复：更新缓存时使用组ID复合Key
        if (config_.enableCache) {
            groupCache_[group.groupId] = group;
            // 更新作动器缓存，使用组ID复合Key
            for (const auto& actuator : group.actuators) {
                QString cacheKey = QString("%1_%2").arg(actuator.params.sn).arg(group.groupId);
                actuatorCache_[cacheKey] = actuator;
            }
            invalidateCache(); // 使统计缓存失效
        }

        // 发送信号
        emit actuatorGroupDataChanged(group.groupId, "create");

        qDebug() << QString(u8"作动器组保存成功: %1 (ID: %2)\r\n\r\n\r\n").arg(group.groupName).arg(group.groupId);
        qDebug() << QString(u8"作动器组保存成功: %1 (ID: %2)\r\n\r\n\r\n").arg(group.groupName).arg(group.groupId);
        qDebug() << QString(u8"作动器组保存成功: %1 (ID: %2)\r\n\r\n\r\n").arg(group.groupName).arg(group.groupId);
        qDebug() << QString(u8"作动器组保存成功: %1 (ID: %2)\r\n\r\n\r\n").arg(group.groupName).arg(group.groupId);
        qDebug() << QString(u8"作动器组保存成功: %1 (ID: %2)\r\n\r\n\r\n").arg(group.groupName).arg(group.groupId);
        qDebug() << QString(u8"作动器组保存成功: %1 (ID: %2)\r\n\r\n\r\n").arg(group.groupName).arg(group.groupId);

        emit statisticsChanged();
        emit operationCompleted("saveActuatorGroup", true);

        addLogEntry("INFO", QString(u8"作动器组保存成功: %1 (ID: %2)").arg(group.groupName).arg(group.groupId));
        return true;

    } catch (const std::exception& e) {
        setError(QString(u8"保存作动器组时发生异常: %1").arg(e.what()), ErrorType::SystemError);
        emit operationCompleted("saveActuatorGroup", false);
        return false;
    }
}

UI::ActuatorGroup_1_2 ActuatorViewModel1_2::getActuatorGroup(int groupId) const
{
    if (config_.threadSafe) {
        return safeDataAccess<UI::ActuatorGroup_1_2>([this, groupId]() {
            return getActuatorGroupImpl(groupId);
        });
    } else {
        return getActuatorGroupImpl(groupId);
    }
}

UI::ActuatorGroup_1_2 ActuatorViewModel1_2::getActuatorGroupImpl(int groupId) const
{
    clearError();

    try {
        // 检查缓存
        if (config_.enableCache && groupCache_.contains(groupId)) {
            addLogEntry("DEBUG", QString(u8"从缓存获取作动器组: %1").arg(groupId));
            return groupCache_[groupId];
        }

        // 检查数据管理器
        if (!dataManager_) {
            setError(u8"数据管理器未初始化", ErrorType::SystemError);
            return UI::ActuatorGroup_1_2();
        }

        // 从数据管理器获取
        UI::ActuatorGroup_1_2 group = dataManager_->getActuatorGroup(groupId);
        if (group.groupName.isEmpty()) {
            setError(QString(u8"作动器组不存在: %1").arg(groupId), ErrorType::NotFoundError);
            return UI::ActuatorGroup_1_2();
        }

        // 更新缓存
        if (config_.enableCache) {
            groupCache_[groupId] = group;
        }

        addLogEntry("DEBUG", QString(u8"获取作动器组成功: %1").arg(groupId));
        return group;

    } catch (const std::exception& e) {
        setError(QString(u8"获取作动器组时发生异常: %1").arg(e.what()), ErrorType::SystemError);
        return UI::ActuatorGroup_1_2();
    }
}

QList<UI::ActuatorGroup_1_2> ActuatorViewModel1_2::getAllActuatorGroups() const
{
    if (config_.threadSafe) {
        return safeDataAccess<QList<UI::ActuatorGroup_1_2>>([this]() {
            return getAllActuatorGroupsImpl();
        });
    } else {
        return getAllActuatorGroupsImpl();
    }
}

QList<UI::ActuatorGroup_1_2> ActuatorViewModel1_2::getAllActuatorGroupsImpl() const
{
    clearError();

    try {
        // 检查数据管理器
        if (!dataManager_) {
            setError(u8"数据管理器未初始化", ErrorType::SystemError);
            return QList<UI::ActuatorGroup_1_2>();
        }

        // 从数据管理器获取
        QList<UI::ActuatorGroup_1_2> groups = dataManager_->getAllActuatorGroups();

        // 🔧 修复：更新缓存时使用组ID复合Key
        if (config_.enableCache) {
            for (const auto& group : groups) {
                groupCache_[group.groupId] = group;
                // 同时缓存组内的作动器，使用组ID复合Key
                for (const auto& actuator : group.actuators) {
                    QString cacheKey = QString("%1_%2").arg(actuator.params.sn).arg(group.groupId);
                    actuatorCache_[cacheKey] = actuator;
                }
            }
        }

        addLogEntry("DEBUG", QString(u8"获取所有作动器组成功: %1个组").arg(groups.size()));
        return groups;

    } catch (const std::exception& e) {
        setError(QString(u8"获取所有作动器组时发生异常: %1").arg(e.what()), ErrorType::SystemError);
        return QList<UI::ActuatorGroup_1_2>();
    }
}

// ==================== 数据验证接口 ====================

bool ActuatorViewModel1_2::validateActuatorParams(const UI::ActuatorParams_1_2& params) const
{
    clearError();

    try {
        // 检查数据管理器
        if (!dataManager_) {
            setError(u8"数据管理器未初始化", ErrorType::SystemError);
            return false;
        }

        // 使用数据管理器的验证方法
        bool isValid = dataManager_->validateActuatorParams(params);
        if (!isValid) {
            setError(dataManager_->getLastError(), ErrorType::ValidationError);
            return false;
        }

        return true;

    } catch (const std::exception& e) {
        setError(QString(u8"验证作动器参数时发生异常: %1").arg(e.what()), ErrorType::SystemError);
        return false;
    }
}

bool ActuatorViewModel1_2::validateActuatorGroup(const UI::ActuatorGroup_1_2& group) const
{
    clearError();

    try {
        // 检查数据管理器
        if (!dataManager_) {
            setError(u8"数据管理器未初始化", ErrorType::SystemError);
            return false;
        }

        // 使用数据管理器的验证方法
        bool isValid = dataManager_->validateActuatorGroup(group);
        if (!isValid) {
            setError(dataManager_->getLastError(), ErrorType::ValidationError);
            return false;
        }

        return true;

    } catch (const std::exception& e) {
        setError(QString(u8"验证作动器组时发生异常: %1").arg(e.what()), ErrorType::SystemError);
        return false;
    }
}

bool ActuatorViewModel1_2::validateActuatorInGroup(const UI::ActuatorParams_1_2& actuator, const UI::ActuatorGroup_1_2& group) const
{
    clearError();

    try {
        // 检查数据管理器
        if (!dataManager_) {
            setError(u8"数据管理器未初始化", ErrorType::SystemError);
            return false;
        }

        // 使用数据管理器的验证方法
        bool isValid = dataManager_->validateActuatorInGroup(actuator, group);
        if (!isValid) {
            setError(dataManager_->getLastError(), ErrorType::ValidationError);
            return false;
        }

        return true;

    } catch (const std::exception& e) {
        setError(QString(u8"验证作动器在组内时发生异常: %1").arg(e.what()), ErrorType::SystemError);
        return false;
    }
}

// ==================== 序列号管理接口 ====================

//QString ActuatorViewModel1_2::generateNextSerialNumber(const QString& prefix) const
//{
//    try {
//        // 检查数据管理器
//        if (!dataManager_) {
//            setError(u8"数据管理器未初始化", ErrorType::SystemError);
//            return QString();
//        }

//        QString serialNumber = dataManager_->generateNextSerialNumber(prefix);
//        addLogEntry("DEBUG", QString(u8"生成序列号: %1").arg(serialNumber));
//        return serialNumber;

//    } catch (const std::exception& e) {
//        setError(QString(u8"生成序列号时发生异常: %1").arg(e.what()), ErrorType::SystemError);
//        return QString();
//    }
//}

//bool ActuatorViewModel1_2::isSerialNumberUnique(const QString& serialNumber) const
//{
//    try {
//        // 检查数据管理器
//        if (!dataManager_) {
//            return false;
//        }

//        return dataManager_->isSerialNumberUnique(serialNumber);

//    } catch (const std::exception& e) {
//        addLogEntry("ERROR", QString(u8"检查序列号唯一性时发生异常: %1").arg(e.what()));
//        return false;
//    }
//}

bool ActuatorViewModel1_2::isSerialNumberUniqueInGroup(const QString& serialNumber, int groupId) const
{
    try {
        // 检查数据管理器
        if (!dataManager_) {
            return false;
        }

        return dataManager_->isSerialNumberUniqueInGroup(serialNumber, groupId);

    } catch (const std::exception& e) {
        addLogEntry("ERROR", QString(u8"检查组内序列号唯一性时发生异常: %1").arg(e.what()));
        return false;
    }
}

bool ActuatorViewModel1_2::isSerialNumberUniqueInGroup(const QString& serialNumber, int groupId, int excludeActuatorId) const
{
    try {
        // 检查数据管理器
        if (!dataManager_) {
            return false;
        }

        return dataManager_->isSerialNumberUniqueInGroup(serialNumber, groupId, excludeActuatorId);

    } catch (const std::exception& e) {
        addLogEntry("ERROR", QString(u8"检查组内序列号唯一性(排除)时发生异常: %1").arg(e.what()));
        return false;
    }
}

// ==================== 数据统计接口 ====================

QMap<QString, int> ActuatorViewModel1_2::getActuatorTypeStatistics() const
{
    if (config_.threadSafe) {
        return safeDataAccess<QMap<QString, int>>([this]() {
            return getActuatorTypeStatisticsImpl();
        });
    } else {
        return getActuatorTypeStatisticsImpl();
    }
}

QMap<QString, int> ActuatorViewModel1_2::getActuatorTypeStatisticsImpl() const
{
    try {
        // 检查缓存
        if (config_.enableCache && statisticsLoaded_ && !typeStatisticsCache_.isEmpty()) {
            return typeStatisticsCache_;
        }

        // 检查数据管理器
        if (!dataManager_) {
            setError(u8"数据管理器未初始化", ErrorType::SystemError);
            return QMap<QString, int>();
        }

        // 从数据管理器获取
        QMap<QString, int> statistics = dataManager_->getActuatorTypeStatistics();

        // 更新缓存
        if (config_.enableCache) {
            typeStatisticsCache_ = statistics;
        }

        return statistics;

    } catch (const std::exception& e) {
        setError(QString(u8"获取作动器类型统计时发生异常: %1").arg(e.what()), ErrorType::SystemError);
        return QMap<QString, int>();
    }
}

int ActuatorViewModel1_2::getActuatorCount() const
{
    try {
        // 检查数据管理器
        if (!dataManager_) {
            return 0;
        }

        return dataManager_->getActuatorCount();

    } catch (const std::exception& e) {
        addLogEntry("ERROR", QString(u8"获取作动器数量时发生异常: %1").arg(e.what()));
        return 0;
    }
}

int ActuatorViewModel1_2::getActuatorGroupCount() const
{
    try {
        // 检查数据管理器
        if (!dataManager_) {
            return 0;
        }

        return dataManager_->getActuatorGroupCount();

    } catch (const std::exception& e) {
        addLogEntry("ERROR", QString(u8"获取作动器组数量时发生异常: %1").arg(e.what()));
        return 0;
    }
}

// ==================== 项目同步接口 ====================

void ActuatorViewModel1_2::syncMemoryDataToProject(DataModels::TestProject* project)
{
    // 注意：TestProject的方法已不存在，这个方法现在只是一个占位符
    // 实际的数据同步应该通过DataManager直接进行
    Q_UNUSED(project);

    try {
        emit syncProgress(u8"开始同步作动器数据", 0);

        // 获取所有作动器数据
        QList<UI::ActuatorParams_1_2> actuators = getAllActuators();
        QList<UI::ActuatorGroup_1_2> groups = getAllActuatorGroups();

        emit syncProgress(u8"作动器数据已准备就绪", 100);
        emit syncCompleted(true);

        addLogEntry("INFO", QString(u8"作动器数据同步完成: %1个作动器, %2个组")
                    .arg(actuators.size()).arg(groups.size()));

        // 注意：实际的项目保存应该在MainWindow层面通过其他方式进行
        // 这里只是确保数据在DataManager中是最新的

    } catch (const std::exception& e) {
        setError(QString(u8"同步数据时发生异常: %1").arg(e.what()), ErrorType::SystemError);
        emit syncCompleted(false);
    }
}

void ActuatorViewModel1_2::syncProjectDataToMemory(DataModels::TestProject* project)
{
    // 注意：TestProject的方法已不存在，这个方法现在只是一个占位符
    // 实际的数据加载应该通过其他方式进行
    Q_UNUSED(project);

    try {
        emit syncProgress(u8"开始同步项目数据到内存", 0);

        // 清空现有数据
        clearMemoryData();

        emit syncProgress(u8"项目数据同步完成", 100);
        emit syncCompleted(true);

        addLogEntry("INFO", u8"项目数据同步到内存完成");

        // 注意：实际的项目加载应该在MainWindow层面通过其他方式进行
        // 比如直接从文件加载或通过其他数据源

    } catch (const std::exception& e) {
        setError(QString(u8"同步项目数据到内存时发生异常: %1").arg(e.what()), ErrorType::SystemError);
        emit syncCompleted(false);
    }
}

void ActuatorViewModel1_2::clearMemoryData()
{
    try {
        // 检查数据管理器
        if (!dataManager_) {
            setError(u8"数据管理器未初始化", ErrorType::SystemError);
            return;
        }

        // 清空数据管理器
        dataManager_->clearAll();

        // 清空缓存
        clearCache();

        // 发送信号
        emit statisticsChanged();

        addLogEntry("INFO", u8"内存数据清空完成");

    } catch (const std::exception& e) {
        setError(QString(u8"清空内存数据时发生异常: %1").arg(e.what()), ErrorType::SystemError);
    }
}

// ==================== 文件操作接口实现 ====================

//bool ActuatorViewModel1_2::loadFromJSONFile(const QString& filePath)
//{
//    try {
//        QFile file(filePath);
//        if (!file.open(QIODevice::ReadOnly)) {
//            setError(QString(u8"无法打开文件: %1").arg(filePath), ErrorType::SystemError);
//            return false;
//        }

//        QByteArray data = file.readAll();
//        file.close();

//        QJsonParseError error;
//        QJsonDocument doc = QJsonDocument::fromJson(data, &error);
//        if (error.error != QJsonParseError::NoError) {
//            setError(QString(u8"JSON解析错误: %1").arg(error.errorString()), ErrorType::ValidationError);
//            return false;
//        }

//        return fromCompatibleJSON(doc.object());

//    } catch (const std::exception& e) {
//        setError(QString(u8"从JSON文件加载时发生异常: %1").arg(e.what()), ErrorType::SystemError);
//        return false;
//    }
//}

//bool ActuatorViewModel1_2::saveToJSONFile(const QString& filePath) const
//{
//    try {
//        QJsonObject jsonData = toCompatibleJSON();
//        if (jsonData.isEmpty()) {
//            return false; // 错误信息已在toCompatibleJSON中设置
//        }

//        QJsonDocument doc(jsonData);
//        QByteArray data = doc.toJson(QJsonDocument::Indented);

//        QFile file(filePath);
//        if (!file.open(QIODevice::WriteOnly)) {
//            setError(QString(u8"无法创建文件: %1").arg(filePath), ErrorType::SystemError);
//            return false;
//        }

//        qint64 written = file.write(data);
//        file.close();

//        if (written != data.size()) {
//            setError(QString(u8"文件写入不完整: %1").arg(filePath), ErrorType::SystemError);
//            return false;
//        }

//        addLogEntry("INFO", QString(u8"数据已保存到JSON文件: %1").arg(filePath));
//        return true;

//    } catch (const std::exception& e) {
//        setError(QString(u8"保存到JSON文件时发生异常: %1").arg(e.what()), ErrorType::SystemError);
//        return false;
//    }
//}

//bool ActuatorViewModel1_2::loadFromJSONString(const QString& jsonString)
//{
//    try {
//        QJsonParseError error;
//        QJsonDocument doc = QJsonDocument::fromJson(jsonString.toUtf8(), &error);
//        if (error.error != QJsonParseError::NoError) {
//            setError(QString(u8"JSON字符串解析错误: %1").arg(error.errorString()), ErrorType::ValidationError);
//            return false;
//        }

//        return fromCompatibleJSON(doc.object());

//    } catch (const std::exception& e) {
//        setError(QString(u8"从JSON字符串加载时发生异常: %1").arg(e.what()), ErrorType::SystemError);
//        return false;
//    }
//}

// 🚫 已注释：独立JSON导出功能已废弃
//QString ActuatorViewModel1_2::exportToJSONString() const
//{
//    try {
//        QJsonObject jsonData = toCompatibleJSON();
//        if (jsonData.isEmpty()) {
//            return QString(); // 错误信息已在toCompatibleJSON中设置
//        }
//
//        QJsonDocument doc(jsonData);
//        return QString::fromUtf8(doc.toJson(QJsonDocument::Indented));
//
//    } catch (const std::exception& e) {
//        setError(QString(u8"导出JSON字符串时发生异常: %1").arg(e.what()), ErrorType::SystemError);
//        return QString();
//    }
//}

// ==================== 兼容性接口实现 ====================

ActuatorDataManager_1_2* ActuatorViewModel1_2::getDataManager() const
{
    return dataManager_.get();
}

// ==================== 错误处理接口 ====================

QString ActuatorViewModel1_2::getLastError() const
{
    QMutexLocker locker(&errorMutex_);
    return lastError_;
}

bool ActuatorViewModel1_2::hasError() const
{
    QMutexLocker locker(&errorMutex_);
    return hasError_;
}

ActuatorViewModel1_2::ErrorType ActuatorViewModel1_2::getErrorType() const
{
    QMutexLocker locker(&errorMutex_);
    return errorType_;
}

void ActuatorViewModel1_2::clearLastError()
{
    QMutexLocker locker(&errorMutex_);
    clearError();
}

// ==================== 私有辅助方法 ====================

void ActuatorViewModel1_2::clearError() const
{
    lastError_.clear();
    errorType_ = ErrorType::NoError;
    hasError_ = false;
}

void ActuatorViewModel1_2::setError(const QString& error, ErrorType type) const
{
    QMutexLocker locker(&errorMutex_);
    lastError_ = error;
    errorType_ = type;
    hasError_ = true;

    // 记录错误日志
    if (config_.enableLogging) {
        addLogEntry("ERROR", error);
    }

    // 发送错误信号
    emit const_cast<ActuatorViewModel1_2*>(this)->errorOccurred(error);
}

void ActuatorViewModel1_2::logError(const QString& operation, const QString& error) const
{
    QString logMessage = QString(u8"操作[%1]失败: %2").arg(operation).arg(error);
    addLogEntry("ERROR", logMessage);
}

void ActuatorViewModel1_2::addLogEntry(const QString& level, const QString& message) const
{
    if (!config_.enableLogging) {
        return;
    }

    // 检查日志级别
    if (level == "DEBUG" && config_.logLevel != "DEBUG") {
        return;
    }

    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    QString logEntry = QString("[%1] [%2] %3").arg(timestamp).arg(level).arg(message);

    // 添加到日志列表
    logEntries_.append(logEntry);

    // 限制日志条目数量
    if (logEntries_.size() > MAX_LOG_ENTRIES) {
        logEntries_.removeFirst();
    }

    // 输出到调试控制台
    qDebug() << logEntry;
}

// ==================== 缓存管理方法 ====================

void ActuatorViewModel1_2::invalidateCache()
{
    QMutexLocker locker(&cacheMutex_);
    cacheValid_ = false;
    statisticsLoaded_ = false;
    typeStatisticsCache_.clear();
    unitStatisticsCache_.clear();
    polarityStatisticsCache_.clear();
}

void ActuatorViewModel1_2::updateCache()
{
    QMutexLocker locker(&cacheMutex_);

    try {
        // 更新作动器缓存
        if (!actuatorsLoaded_) {
            ensureActuatorsLoaded();
        }

        // 更新组缓存
        if (!groupsLoaded_) {
            ensureGroupsLoaded();
        }

        // 更新统计缓存
        if (!statisticsLoaded_) {
            ensureStatisticsLoaded();
        }

        cacheValid_ = true;
        cacheTimestamp_ = QDateTime::currentDateTime();

        addLogEntry("DEBUG", u8"缓存更新完成");

    } catch (const std::exception& e) {
        addLogEntry("ERROR", QString(u8"更新缓存时发生异常: %1").arg(e.what()));
        cacheValid_ = false;
    }
}

void ActuatorViewModel1_2::clearCache()
{
    QMutexLocker locker(&cacheMutex_);
    actuatorCache_.clear();
    groupCache_.clear();
    typeStatisticsCache_.clear();
    unitStatisticsCache_.clear();
    polarityStatisticsCache_.clear();
    cacheValid_ = false;
    statisticsLoaded_ = false;
    groupsLoaded_ = false;
    actuatorsLoaded_ = false;
    cacheTimestamp_ = QDateTime::currentDateTime();
}

bool ActuatorViewModel1_2::isCacheValid() const
{
    if (!config_.enableCache) {
        return false;
    }

    QMutexLocker locker(&cacheMutex_);

    // 检查缓存是否过期
    QDateTime now = QDateTime::currentDateTime();
    qint64 elapsed = cacheTimestamp_.secsTo(now);

    return cacheValid_ && (elapsed < config_.cacheTimeout);
}

// ==================== 线程安全模板方法已在头文件中定义 ====================

// ==================== UI交互接口实现 ====================

//bool ActuatorViewModel1_2::createActuatorInGroup(const QString& groupName, const UI::ActuatorParams_1_2& params)
//{
//    clearError();

//    try {
//        // 首先保存作动器
//        if (!saveActuator(params)) {
//            return false; // 错误信息已在saveActuator中设置
//        }

//        // 然后创建或更新组
//        return createOrUpdateActuatorGroup(groupName, params);

//    } catch (const std::exception& e) {
//        setError(QString(u8"在组中创建作动器时发生异常: %1").arg(e.what()), ErrorType::SystemError);
//        return false;
//    }
//}

bool ActuatorViewModel1_2::createOrUpdateActuatorGroup(const QString& groupName, const UI::ActuatorParams_1_2& params)
{
    clearError();

    try {
        // 查找现有组
        QList<UI::ActuatorGroup_1_2> allGroups = getAllActuatorGroups();
        UI::ActuatorGroup_1_2 targetGroup;
        bool groupExists = false;

        for (const auto& group : allGroups) {
            if (group.groupName == groupName) {
                targetGroup = group;
                groupExists = true;
                break;
            }
        }

        if (groupExists) {
            // 更新现有组
            // 检查作动器是否已在组中
            bool actuatorExists = false;
            for (auto& actuator : targetGroup.actuators) {
                if (actuator.params.sn == params.params.sn) {
                    // 更新现有作动器
                    actuator = params;
                    actuatorExists = true;
                    break;
                }
            }

            if (!actuatorExists) {
                // 添加新作动器到组
                UI::ActuatorParams_1_2 actuatorWithId = params;
                actuatorWithId.actuatorId = assignActuatorIdInGroup(targetGroup);
                targetGroup.actuators.append(actuatorWithId);
            }

            return saveActuatorGroup(targetGroup);
        } else {
            // 创建新组
            targetGroup.groupId = getActuatorGroupCount() + 1;
            targetGroup.groupName = groupName;
            targetGroup.groupType = u8"作动器组";
            targetGroup.createTime = generateTimestamp();
            targetGroup.groupNotes = QString(u8"自动创建的作动器组: %1").arg(groupName);

            // 添加作动器到新组
            UI::ActuatorParams_1_2 actuatorWithId = params;
            actuatorWithId.actuatorId = 1; // 新组中第一个作动器ID为1
            targetGroup.actuators.append(actuatorWithId);

            return saveActuatorGroup(targetGroup);
        }

    } catch (const std::exception& e) {
        setError(QString(u8"创建或更新作动器组时发生异常: %1").arg(e.what()), ErrorType::SystemError);
        return false;
    }
}

QString ActuatorViewModel1_2::getActuatorStatistics() const
{
    try {
        int actuatorCount = getActuatorCount();
        int groupCount = getActuatorGroupCount();
        QMap<QString, int> typeStats = getActuatorTypeStatistics();

        QString statistics = QString(u8"作动器统计信息：\n");
        statistics += QString(u8"总作动器数量：%1\n").arg(actuatorCount);
        statistics += QString(u8"总组数量：%2\n").arg(groupCount);

        if (!typeStats.isEmpty()) {
            statistics += u8"类型分布：\n";
            for (auto it = typeStats.begin(); it != typeStats.end(); ++it) {
                statistics += QString(u8"  %1: %2个\n").arg(it.key()).arg(it.value());
            }
        }

        return statistics;

    } catch (const std::exception& e) {
        return QString(u8"获取统计信息时发生异常: %1").arg(e.what());
    }
}

// ==================== 业务逻辑辅助方法 ====================

int ActuatorViewModel1_2::assignActuatorIdInGroup(const UI::ActuatorGroup_1_2& group) const
{
    return findMaxActuatorIdInGroup(group) + 1;
}

int ActuatorViewModel1_2::findMaxActuatorIdInGroup(const UI::ActuatorGroup_1_2& group) const
{
    int maxId = 0;
    for (const auto& actuator : group.actuators) {
        if (actuator.actuatorId > maxId) {
            maxId = actuator.actuatorId;
        }
    }
    return maxId;
}

int ActuatorViewModel1_2::extractActuatorGroupId(const QString& groupName) const
{
    // 尝试从组名称中解析组ID
    QList<UI::ActuatorGroup_1_2> allGroups = getAllActuatorGroups();
    for (const auto& group : allGroups) {
        if (group.groupName == groupName) {
            return group.groupId;
        }
    }
    return -1; // 未找到
}

QString ActuatorViewModel1_2::generateTimestamp() const
{
    return QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
}

// ==================== 数据验证辅助方法 ====================

bool ActuatorViewModel1_2::isValidSerialNumber(const QString& serialNumber) const
{
    if (serialNumber.isEmpty() || serialNumber.length() > 50) {
        return false;
    }

    // 检查是否包含非法字符
    QRegularExpression regex("^[\\w\\u4e00-\\u9fa5\\s\\-_]+$");
    return regex.match(serialNumber).hasMatch();
}

bool ActuatorViewModel1_2::isValidGroupId(int groupId) const
{
    return groupId > 0;
}

bool ActuatorViewModel1_2::isValidActuatorId(int actuatorId) const
{
    return actuatorId > 0;
}

// ==================== 数据转换方法 ====================

QString ActuatorViewModel1_2::actuatorParamsToDebugString(const UI::ActuatorParams_1_2& params) const
{
    QString debug = QString(u8"作动器参数:\n");
    debug += QString(u8"  控制量名称: %1\n").arg(params.name);
    debug += QString(u8"  序列号: %1\n").arg(params.params.sn);
    debug += QString(u8"  型号: %1\n").arg(params.params.model);
    debug += QString(u8"  类型: %1\n").arg(ActuatorDataManager_1_2::actuatorTypeToString(params.type));
    debug += QString(u8"  零偏: %1\n").arg(params.zero_offset, 0, 'f', 3);
    debug += QString(u8"  下位机ID: %1\n").arg(params.lc_id);
    debug += QString(u8"  站点ID: %1\n").arg(params.station_id);
    debug += QString(u8"  K系数: %1\n").arg(params.params.k, 0, 'f', 6);
    debug += QString(u8"  B系数: %1\n").arg(params.params.b, 0, 'f', 6);
    debug += QString(u8"  精度: %1\n").arg(params.params.precision, 0, 'f', 6);
    debug += QString(u8"  极性: %1\n").arg(ActuatorDataManager_1_2::polarityToString(params.params.polarity));
    debug += QString(u8"  测量单位: %1\n").arg(ActuatorDataManager_1_2::measurementUnitToString(params.params.meas_unit));
    debug += QString(u8"  测量范围: %1 ~ %2\n").arg(params.params.meas_range_min, 0, 'f', 3).arg(params.params.meas_range_max, 0, 'f', 3);
    debug += QString(u8"  输出信号范围: %1 ~ %2\n").arg(params.params.output_signal_range_min, 0, 'f', 3).arg(params.params.output_signal_range_max, 0, 'f', 3);
    return debug;
}

QString ActuatorViewModel1_2::actuatorGroupToDebugString(const UI::ActuatorGroup_1_2& group) const
{
    QString debug = QString(u8"作动器组信息:\n");
    debug += QString(u8"  组ID: %1\n").arg(group.groupId);
    debug += QString(u8"  组名称: %2\n").arg(group.groupName);
    debug += QString(u8"  组类型: %3\n").arg(group.groupType);
    debug += QString(u8"  创建时间: %4\n").arg(group.createTime);
    debug += QString(u8"  作动器数量: %5\n").arg(group.actuators.size());

    for (int i = 0; i < group.actuators.size(); ++i) {
        const auto& actuator = group.actuators[i];
        debug += QString(u8"  [%1] %2 (ID: %3)\n")
                 .arg(i + 1)
                 .arg(actuator.params.sn)
                 .arg(actuator.actuatorId);
    }

    return debug;
}

#ifdef QT_TESTLIB_LIB
// ==================== 测试专用接口实现 ====================

void ActuatorViewModel1_2::setTestMode(bool enabled)
{
    testMode_ = enabled;
    addLogEntry("INFO", QString(u8"测试模式: %1").arg(enabled ? u8"启用" : u8"禁用"));
}

void ActuatorViewModel1_2::injectTestData(const QList<UI::ActuatorParams_1_2>& testActuators)
{
    if (!testMode_) {
        addLogEntry("WARNING", u8"非测试模式下无法注入测试数据");
        return;
    }

    testData_ = testActuators;
    addLogEntry("INFO", QString(u8"注入测试数据: %1个作动器").arg(testActuators.size()));
}

void ActuatorViewModel1_2::clearTestData()
{
    testData_.clear();
    addLogEntry("INFO", u8"清空测试数据");
}

QStringList ActuatorViewModel1_2::getTestLog() const
{
    return logEntries_;
}

QString ActuatorViewModel1_2::getCacheStatus() const
{
    QString status = QString(u8"缓存状态:\n");
    status += QString(u8"  启用: %1\n").arg(config_.enableCache ? u8"是" : u8"否");
    status += QString(u8"  有效: %2\n").arg(isCacheValid() ? u8"是" : u8"否");
    status += QString(u8"  作动器缓存: %3个\n").arg(actuatorCache_.size());
    status += QString(u8"  组缓存: %4个\n").arg(groupCache_.size());
    status += QString(u8"  统计缓存: %5\n").arg(statisticsLoaded_ ? u8"已加载" : u8"未加载");
    status += QString(u8"  更新时间: %6\n").arg(cacheTimestamp_.toString("yyyy-MM-dd hh:mm:ss"));
    return status;
}
#endif

// ==================== 模板方法已在头文件中定义，无需显式实例化 ====================

// ==================== 静态常量定义 ====================

const QString ActuatorViewModel1_2::CURRENT_FORMAT_VERSION = "1.2.0";

// ==================== 界面扩展支持接口实现 ====================

void ActuatorViewModel1_2::registerUIExtension(const QString& extensionName, std::function<QWidget*()> handler)
{
    if (extensionName.isEmpty() || !handler) {
        setError(u8"注册界面扩展失败：扩展名称为空或处理器无效", ErrorType::ValidationError);
        return;
    }

    uiExtensionHandlers_[extensionName] = handler;
    addLogEntry("INFO", QString(u8"注册界面扩展成功: %1").arg(extensionName));
}

QStringList ActuatorViewModel1_2::getSupportedUIExtensions() const
{
    return uiExtensionHandlers_.keys();
}

QWidget* ActuatorViewModel1_2::createExtensionUI(const QString& extensionName)
{
    if (!uiExtensionHandlers_.contains(extensionName)) {
        setError(QString(u8"未找到界面扩展: %1").arg(extensionName), ErrorType::NotFoundError);
        return nullptr;
    }

    try {
        QWidget* widget = uiExtensionHandlers_[extensionName]();
        if (widget) {
            addLogEntry("INFO", QString(u8"创建界面扩展成功: %1").arg(extensionName));
        } else {
            setError(QString(u8"创建界面扩展失败: %1").arg(extensionName), ErrorType::SystemError);
        }
        return widget;
    } catch (const std::exception& e) {
        setError(QString(u8"创建界面扩展时发生异常: %1 - %2").arg(extensionName).arg(e.what()), ErrorType::SystemError);
        return nullptr;
    }
}

void ActuatorViewModel1_2::registerDataFieldExtension(const QString& fieldName, const QString& fieldType, const QVariant& defaultValue)
{
    if (fieldName.isEmpty() || fieldType.isEmpty()) {
        setError(u8"注册数据字段扩展失败：字段名称或类型为空", ErrorType::ValidationError);
        return;
    }

    extensionFieldTypes_[fieldName] = fieldType;
    extensionFieldDefaults_[fieldName] = defaultValue;

    addLogEntry("INFO", QString(u8"注册数据字段扩展成功: %1 (%2)").arg(fieldName).arg(fieldType));
}

QVariant ActuatorViewModel1_2::getExtensionFieldValue(const QString& serialNumber, const QString& fieldName) const
{
    if (serialNumber.isEmpty() || fieldName.isEmpty()) {
        return QVariant();
    }

    if (!extensionFieldValues_.contains(serialNumber)) {
        // 返回默认值
        return extensionFieldDefaults_.value(fieldName, QVariant());
    }

    const auto& fieldMap = extensionFieldValues_[serialNumber];
    return fieldMap.value(fieldName, extensionFieldDefaults_.value(fieldName, QVariant()));
}

bool ActuatorViewModel1_2::setExtensionFieldValue(const QString& serialNumber, const QString& fieldName, const QVariant& value)
{
    if (serialNumber.isEmpty() || fieldName.isEmpty()) {
        setError(u8"设置扩展字段值失败：序列号或字段名称为空", ErrorType::ValidationError);
        return false;
    }

    // 检查字段是否已注册
    if (!extensionFieldTypes_.contains(fieldName)) {
        setError(QString(u8"未注册的扩展字段: %1").arg(fieldName), ErrorType::ValidationError);
        return false;
    }

    // 验证数据类型
    QString expectedType = extensionFieldTypes_[fieldName];
    if (!validateExtensionFieldType(value, expectedType)) {
        setError(QString(u8"扩展字段类型不匹配: %1 期望 %2").arg(fieldName).arg(expectedType), ErrorType::ValidationError);
        return false;
    }

    extensionFieldValues_[serialNumber][fieldName] = value;

    addLogEntry("DEBUG", QString(u8"设置扩展字段值: %1.%2 = %3").arg(serialNumber).arg(fieldName).arg(value.toString()));
    return true;
}

// ==================== 格式兼容性保证接口实现 ====================

bool ActuatorViewModel1_2::validateJSONCompatibility(const QJsonObject& jsonData) const
{
    try {
        // 检查必需的字段
        QStringList requiredFields = {"version", "actuators", "actuatorGroups"};
        for (const QString& field : requiredFields) {
            if (!jsonData.contains(field)) {
                setError(QString(u8"JSON格式验证失败：缺少必需字段 %1").arg(field), ErrorType::ValidationError);
                return false;
            }
        }

        // 检查版本兼容性
        QString version = jsonData["version"].toString();
        if (!isVersionCompatible(version)) {
            setError(QString(u8"JSON格式版本不兼容: %1，当前支持版本: %2").arg(version).arg(CURRENT_FORMAT_VERSION), ErrorType::ValidationError);
            return false;
        }

        // 检查作动器数据格式
        QJsonArray actuators = jsonData["actuators"].toArray();
        for (const auto& value : actuators) {
            QJsonObject actuator = value.toObject();
            if (!validateActuatorJSONFormat(actuator)) {
                return false; // 错误信息已在validateActuatorJSONFormat中设置
            }
        }

        addLogEntry("INFO", u8"JSON格式兼容性验证通过");
        return true;

    } catch (const std::exception& e) {
        setError(QString(u8"JSON格式验证时发生异常: %1").arg(e.what()), ErrorType::SystemError);
        return false;
    }
}

QJsonObject ActuatorViewModel1_2::toCompatibleJSON() const
{
    try {
        QJsonObject jsonData;

        // 设置版本信息
        jsonData["version"] = dataFormatVersion_.isEmpty() ? CURRENT_FORMAT_VERSION : dataFormatVersion_;
        jsonData["exportTime"] = QDateTime::currentDateTime().toString(Qt::ISODate);
        jsonData["exportedBy"] = "ActuatorViewModel1_2";

        // 导出作动器数据
        QJsonArray actuatorsArray;
        QList<UI::ActuatorParams_1_2> allActuators = getAllActuators();
        for (const auto& actuator : allActuators) {
            QJsonObject actuatorObj = actuatorToCompatibleJSON(actuator);
            actuatorsArray.append(actuatorObj);
        }
        jsonData["actuators"] = actuatorsArray;

        // 导出作动器组数据
        QJsonArray groupsArray;
        QList<UI::ActuatorGroup_1_2> allGroups = getAllActuatorGroups();
        for (const auto& group : allGroups) {
            QJsonObject groupObj = actuatorGroupToCompatibleJSON(group);
            groupsArray.append(groupObj);
        }
        jsonData["actuatorGroups"] = groupsArray;

        // 导出扩展字段定义
        if (!extensionFieldTypes_.isEmpty()) {
            QJsonObject extensionFields;
            for (auto it = extensionFieldTypes_.begin(); it != extensionFieldTypes_.end(); ++it) {
                QJsonObject fieldDef;
                fieldDef["type"] = it.value();
                fieldDef["defaultValue"] = QJsonValue::fromVariant(extensionFieldDefaults_.value(it.key()));
                extensionFields[it.key()] = fieldDef;
            }
            jsonData["extensionFields"] = extensionFields;
        }

        // 导出扩展字段值
        if (!extensionFieldValues_.isEmpty()) {
            QJsonObject extensionValues;
            for (auto it = extensionFieldValues_.begin(); it != extensionFieldValues_.end(); ++it) {
                QJsonObject valueMap;
                for (auto valueIt = it.value().begin(); valueIt != it.value().end(); ++valueIt) {
                    valueMap[valueIt.key()] = QJsonValue::fromVariant(valueIt.value());
                }
                extensionValues[it.key()] = valueMap;
            }
            jsonData["extensionValues"] = extensionValues;
        }

        addLogEntry("INFO", QString(u8"生成兼容JSON格式成功，包含 %1 个作动器，%2 个组")
                    .arg(allActuators.size()).arg(allGroups.size()));

        return jsonData;

    } catch (const std::exception& e) {
        setError(QString(u8"生成兼容JSON格式时发生异常: %1").arg(e.what()), ErrorType::SystemError);
        return QJsonObject();
    }
}

//bool ActuatorViewModel1_2::fromCompatibleJSON(const QJsonObject& jsonData)
//{
//    try {
//        // 验证格式兼容性
//        if (!validateJSONCompatibility(jsonData)) {
//            return false; // 错误信息已在validateJSONCompatibility中设置
//        }

//        // 清空现有数据
//        clearAll();

//        // 设置版本信息
//        dataFormatVersion_ = jsonData["version"].toString();

//        // 加载扩展字段定义
//        if (jsonData.contains("extensionFields")) {
//            QJsonObject extensionFields = jsonData["extensionFields"].toObject();
//            for (auto it = extensionFields.begin(); it != extensionFields.end(); ++it) {
//                QJsonObject fieldDef = it.value().toObject();
//                QString fieldType = fieldDef["type"].toString();
//                QVariant defaultValue = fieldDef["defaultValue"].toVariant();
//                registerDataFieldExtension(it.key(), fieldType, defaultValue);
//            }
//        }

//        // 加载作动器数据
//        QJsonArray actuators = jsonData["actuators"].toArray();
//        for (const auto& value : actuators) {
//            QJsonObject actuatorObj = value.toObject();
//            UI::ActuatorParams_1_2 params = actuatorFromCompatibleJSON(actuatorObj);
//            if (!params.params.sn.isEmpty()) {
//                saveActuator(params);
//            }
//        }

//        // 加载作动器组数据
//        QJsonArray groups = jsonData["actuatorGroups"].toArray();
//        for (const auto& value : groups) {
//            QJsonObject groupObj = value.toObject();
//            UI::ActuatorGroup_1_2 group = actuatorGroupFromCompatibleJSON(groupObj);
//            if (!group.groupName.isEmpty()) {
//                saveActuatorGroup(group);
//            }
//        }

//        // 加载扩展字段值
//        if (jsonData.contains("extensionValues")) {
//            QJsonObject extensionValues = jsonData["extensionValues"].toObject();
//            for (auto it = extensionValues.begin(); it != extensionValues.end(); ++it) {
//                QString serialNumber = it.key();
//                QJsonObject valueMap = it.value().toObject();
//                for (auto valueIt = valueMap.begin(); valueIt != valueMap.end(); ++valueIt) {
//                    setExtensionFieldValue(serialNumber, valueIt.key(), valueIt.value().toVariant());
//                }
//            }
//        }

//        addLogEntry("INFO", QString(u8"从兼容JSON格式加载成功，版本: %1").arg(dataFormatVersion_));
//        return true;

//    } catch (const std::exception& e) {
//        setError(QString(u8"从兼容JSON格式加载时发生异常: %1").arg(e.what()), ErrorType::SystemError);
//        return false;
//    }
//}

QString ActuatorViewModel1_2::getDataFormatVersion() const
{
    return dataFormatVersion_.isEmpty() ? CURRENT_FORMAT_VERSION : dataFormatVersion_;
}

void ActuatorViewModel1_2::setDataFormatVersion(const QString& version)
{
    dataFormatVersion_ = version;
    addLogEntry("INFO", QString(u8"设置数据格式版本: %1").arg(version));
}

// ==================== 扩展功能辅助方法实现 ====================

bool ActuatorViewModel1_2::validateExtensionFieldType(const QVariant& value, const QString& expectedType) const
{
    if (expectedType == "string" || expectedType == "QString") {
        return value.canConvert<QString>();
    } else if (expectedType == "int" || expectedType == "integer") {
        return value.canConvert<int>();
    } else if (expectedType == "double" || expectedType == "float") {
        return value.canConvert<double>();
    } else if (expectedType == "bool" || expectedType == "boolean") {
        return value.canConvert<bool>();
    } else if (expectedType == "date" || expectedType == "datetime") {
        return value.canConvert<QDateTime>();
    }

    // 默认允许任何类型
    return true;
}

bool ActuatorViewModel1_2::isVersionCompatible(const QString& version) const
{
    // 简单的版本兼容性检查
    // 支持 1.x.x 版本
    QStringList parts = version.split('.');
    if (parts.size() >= 1) {
        bool ok;
        int majorVersion = parts[0].toInt(&ok);
        if (ok && majorVersion == 1) {
            return true;
        }
    }

    // 也支持当前版本
    return version == CURRENT_FORMAT_VERSION;
}

bool ActuatorViewModel1_2::validateActuatorJSONFormat(const QJsonObject& actuatorObj) const
{
    // 🔧 修复：检查新的必需字段
    QStringList requiredFields = {"name", "sn", "model", "type"};
    for (const QString& field : requiredFields) {
        if (!actuatorObj.contains(field)) {
            setError(QString(u8"作动器JSON格式验证失败：缺少字段 %1").arg(field), ErrorType::ValidationError);
            return false;
        }
    }

    // 检查数据类型
    if (!actuatorObj["name"].isString()) {
        setError(u8"作动器JSON格式验证失败：name必须是字符串", ErrorType::ValidationError);
        return false;
    }

    if (!actuatorObj["sn"].isString()) {
        setError(u8"作动器JSON格式验证失败：sn必须是字符串", ErrorType::ValidationError);
        return false;
    }

    return true;
}

QJsonObject ActuatorViewModel1_2::actuatorToCompatibleJSON(const UI::ActuatorParams_1_2& params) const
{
    QJsonObject obj;

    // 🔧 修复：新的基础字段
    obj["name"] = params.name;
    obj["sn"] = params.params.sn;
    obj["model"] = params.params.model;
    obj["type"] = static_cast<int>(params.type);
    obj["zero_offset"] = params.zero_offset;
    obj["lc_id"] = params.lc_id;
    obj["station_id"] = params.station_id;
    obj["k"] = params.params.k;
    obj["b"] = params.params.b;
    obj["precision"] = params.params.precision;
    obj["polarity"] = static_cast<int>(params.params.polarity);
    obj["meas_unit"] = static_cast<int>(params.params.meas_unit);
    obj["meas_range_min"] = params.params.meas_range_min;
    obj["meas_range_max"] = params.params.meas_range_max;
    obj["output_signal_unit"] = params.params.output_signal_unit;
    obj["output_signal_range_min"] = params.params.output_signal_range_min;
    obj["output_signal_range_max"] = params.params.output_signal_range_max;

    // 扩展字段
    if (extensionFieldValues_.contains(params.params.sn)) {
        QJsonObject extensionObj;
        const auto& fieldMap = extensionFieldValues_[params.params.sn];
        for (auto it = fieldMap.begin(); it != fieldMap.end(); ++it) {
            extensionObj[it.key()] = QJsonValue::fromVariant(it.value());
        }
        if (!extensionObj.isEmpty()) {
            obj["extensionFields"] = extensionObj;
        }
    }

    return obj;
}

UI::ActuatorParams_1_2 ActuatorViewModel1_2::actuatorFromCompatibleJSON(const QJsonObject& actuatorObj) const
{
    UI::ActuatorParams_1_2 params;

    // 🔧 修复：新的基础字段
    params.name = actuatorObj["name"].toString();
    params.params.sn = actuatorObj["sn"].toString();
    params.params.model = actuatorObj["model"].toString();
    params.type = static_cast<UI::ActuatorType_1_2>(actuatorObj["type"].toInt());
    params.zero_offset = actuatorObj["zero_offset"].toDouble();
    params.lc_id = actuatorObj["lc_id"].toInt();
    params.station_id = actuatorObj["station_id"].toInt();
    params.params.k = actuatorObj["k"].toDouble();
    params.params.b = actuatorObj["b"].toDouble();
    params.params.precision = actuatorObj["precision"].toDouble();
    params.params.polarity = static_cast<UI::Polarity_1_2>(actuatorObj["polarity"].toInt());
    params.params.meas_unit = static_cast<UI::MeasurementUnit_1_2>(actuatorObj["meas_unit"].toInt());
    params.params.meas_range_min = actuatorObj["meas_range_min"].toDouble();
    params.params.meas_range_max = actuatorObj["meas_range_max"].toDouble();
    params.params.output_signal_unit = actuatorObj["output_signal_unit"].toInt();
    params.params.output_signal_range_min = actuatorObj["output_signal_range_min"].toDouble();
    params.params.output_signal_range_max = actuatorObj["output_signal_range_max"].toDouble();

    // 扩展字段将在fromCompatibleJSON中单独处理

    return params;
}

QJsonObject ActuatorViewModel1_2::actuatorGroupToCompatibleJSON(const UI::ActuatorGroup_1_2& group) const
{
    QJsonObject obj;

    obj["groupId"] = group.groupId;
    obj["groupName"] = group.groupName;
    obj["groupType"] = group.groupType;
    obj["createTime"] = group.createTime;
    obj["groupNotes"] = group.groupNotes;

    // 作动器列表
    QJsonArray actuatorsArray;
    for (const auto& actuator : group.actuators) {
        actuatorsArray.append(actuatorToCompatibleJSON(actuator));
    }
    obj["actuators"] = actuatorsArray;

    return obj;
}

UI::ActuatorGroup_1_2 ActuatorViewModel1_2::actuatorGroupFromCompatibleJSON(const QJsonObject& groupObj) const
{
    UI::ActuatorGroup_1_2 group;

    group.groupId = groupObj["groupId"].toInt();
    group.groupName = groupObj["groupName"].toString();
    group.groupType = groupObj["groupType"].toString();
    group.createTime = groupObj["createTime"].toString();
    group.groupNotes = groupObj["groupNotes"].toString();

    // 作动器列表
    QJsonArray actuatorsArray = groupObj["actuators"].toArray();
    for (const auto& value : actuatorsArray) {
        QJsonObject actuatorObj = value.toObject();
        UI::ActuatorParams_1_2 actuator = actuatorFromCompatibleJSON(actuatorObj);
        group.actuators.append(actuator);
    }

    return group;
}

// ==================== 缺失方法的实现 ====================

QList<UI::ActuatorParams_1_2> ActuatorViewModel1_2::getAllActuators() const
{
    if (config_.threadSafe) {
        return safeDataAccess<QList<UI::ActuatorParams_1_2>>([this]() {
            return getAllActuatorsImpl();
        });
    } else {
        return getAllActuatorsImpl();
    }
}

QList<UI::ActuatorParams_1_2> ActuatorViewModel1_2::getAllActuatorsImpl() const
{
    try {
        // 检查数据管理器
        if (!dataManager_) {
            setError(u8"数据管理器未初始化", ErrorType::SystemError);
            return QList<UI::ActuatorParams_1_2>();
        }

        return dataManager_->getAllActuatorDetailedParams();

    } catch (const std::exception& e) {
        setError(QString(u8"获取所有作动器时发生异常: %1").arg(e.what()), ErrorType::SystemError);
        return QList<UI::ActuatorParams_1_2>();
    }
}

QList<UI::ActuatorParams_1_2> ActuatorViewModel1_2::getActuatorsByType(const QString& actuatorType) const
{
    try {
        // 检查数据管理器
        if (!dataManager_) {
            setError(u8"数据管理器未初始化", ErrorType::SystemError);
            return QList<UI::ActuatorParams_1_2>();
        }

        return dataManager_->getActuatorsByType(actuatorType);

    } catch (const std::exception& e) {
        setError(QString(u8"根据类型获取作动器时发生异常: %1").arg(e.what()), ErrorType::SystemError);
        return QList<UI::ActuatorParams_1_2>();
    }
}

QList<UI::ActuatorParams_1_2> ActuatorViewModel1_2::getActuatorsByGroup(int groupId) const
{
    try {
        // 检查数据管理器
        if (!dataManager_) {
            setError(u8"数据管理器未初始化", ErrorType::SystemError);
            return QList<UI::ActuatorParams_1_2>();
        }

        return dataManager_->getActuatorsByGroup(groupId);

    } catch (const std::exception& e) {
        setError(QString(u8"根据组ID获取作动器时发生异常: %1").arg(e.what()), ErrorType::SystemError);
        return QList<UI::ActuatorParams_1_2>();
    }
}

QStringList ActuatorViewModel1_2::getAllActuatorSerialNumbers() const
{
    try {
        // 检查数据管理器
        if (!dataManager_) {
            setError(u8"数据管理器未初始化", ErrorType::SystemError);
            return QStringList();
        }

        return dataManager_->getAllActuatorSerialNumbers();

    } catch (const std::exception& e) {
        setError(QString(u8"获取所有作动器序列号时发生异常: %1").arg(e.what()), ErrorType::SystemError);
        return QStringList();
    }
}

QStringList ActuatorViewModel1_2::validateAllActuators() const
{
    try {
        // 检查数据管理器
        if (!dataManager_) {
            setError(u8"数据管理器未初始化", ErrorType::SystemError);
            return QStringList() << u8"数据管理器未初始化";
        }

        return dataManager_->validateAllActuators();

    } catch (const std::exception& e) {
        setError(QString(u8"验证所有作动器时发生异常: %1").arg(e.what()), ErrorType::SystemError);
        return QStringList() << QString(u8"验证异常: %1").arg(e.what());
    }
}

QStringList ActuatorViewModel1_2::validateAllActuatorGroups() const
{
    try {
        // 检查数据管理器
        if (!dataManager_) {
            setError(u8"数据管理器未初始化", ErrorType::SystemError);
            return QStringList() << u8"数据管理器未初始化";
        }

        return dataManager_->validateAllActuatorGroups();

    } catch (const std::exception& e) {
        setError(QString(u8"验证所有作动器组时发生异常: %1").arg(e.what()), ErrorType::SystemError);
        return QStringList() << QString(u8"验证异常: %1").arg(e.what());
    }
}

QStringList ActuatorViewModel1_2::findDuplicateSerialNumbers() const
{
    try {
        // 检查数据管理器
        if (!dataManager_) {
            setError(u8"数据管理器未初始化", ErrorType::SystemError);
            return QStringList();
        }

        return dataManager_->findDuplicateSerialNumbers();

    } catch (const std::exception& e) {
        setError(QString(u8"查找重复序列号时发生异常: %1").arg(e.what()), ErrorType::SystemError);
        return QStringList();
    }
}

QMap<QString, int> ActuatorViewModel1_2::getUnitTypeStatistics() const
{
    try {
        // 检查数据管理器
        if (!dataManager_) {
            setError(u8"数据管理器未初始化", ErrorType::SystemError);
            return QMap<QString, int>();
        }

        return dataManager_->getUnitTypeStatistics();

    } catch (const std::exception& e) {
        setError(QString(u8"获取单位类型统计时发生异常: %1").arg(e.what()), ErrorType::SystemError);
        return QMap<QString, int>();
    }
}

QMap<QString, int> ActuatorViewModel1_2::getPolarityStatistics() const
{
    try {
        // 检查数据管理器
        if (!dataManager_) {
            setError(u8"数据管理器未初始化", ErrorType::SystemError);
            return QMap<QString, int>();
        }

        return dataManager_->getPolarityStatistics();

    } catch (const std::exception& e) {
        setError(QString(u8"获取极性统计时发生异常: %1").arg(e.what()), ErrorType::SystemError);
        return QMap<QString, int>();
    }
}

QStringList ActuatorViewModel1_2::getUsedActuatorTypes() const
{
    try {
        // 检查数据管理器
        if (!dataManager_) {
            setError(u8"数据管理器未初始化", ErrorType::SystemError);
            return QStringList();
        }

        return dataManager_->getUsedActuatorTypes();

    } catch (const std::exception& e) {
        setError(QString(u8"获取已使用作动器类型时发生异常: %1").arg(e.what()), ErrorType::SystemError);
        return QStringList();
    }
}

QStringList ActuatorViewModel1_2::getUsedUnitTypes() const
{
    try {
        // 检查数据管理器
        if (!dataManager_) {
            setError(u8"数据管理器未初始化", ErrorType::SystemError);
            return QStringList();
        }

        return dataManager_->getUsedUnitTypes();

    } catch (const std::exception& e) {
        setError(QString(u8"获取已使用单位类型时发生异常: %1").arg(e.what()), ErrorType::SystemError);
        return QStringList();
    }
}

void ActuatorViewModel1_2::clearAllActuators()
{
    try {
        // 检查数据管理器
        if (!dataManager_) {
            setError(u8"数据管理器未初始化", ErrorType::SystemError);
            return;
        }

        dataManager_->clearAllActuators();

        // 清空相关缓存
        if (config_.enableCache) {
            actuatorCache_.clear();
            invalidateCache();
        }

        emit statisticsChanged();
        addLogEntry("INFO", u8"所有作动器已清空");

    } catch (const std::exception& e) {
        setError(QString(u8"清空所有作动器时发生异常: %1").arg(e.what()), ErrorType::SystemError);
    }
}

void ActuatorViewModel1_2::clearAllActuatorGroups()
{
    try {
        // 检查数据管理器
        if (!dataManager_) {
            setError(u8"数据管理器未初始化", ErrorType::SystemError);
            return;
        }

        dataManager_->clearAllActuatorGroups();

        // 清空相关缓存
        if (config_.enableCache) {
            groupCache_.clear();
            invalidateCache();
        }

        emit statisticsChanged();
        addLogEntry("INFO", u8"所有作动器组已清空");

    } catch (const std::exception& e) {
        setError(QString(u8"清空所有作动器组时发生异常: %1").arg(e.what()), ErrorType::SystemError);
    }
}

void ActuatorViewModel1_2::clearAll()
{
    try {
        clearAllActuators();
        clearAllActuatorGroups();
        clearCache();

        addLogEntry("INFO", u8"所有数据已清空");

    } catch (const std::exception& e) {
        setError(QString(u8"清空所有数据时发生异常: %1").arg(e.what()), ErrorType::SystemError);
    }
}

bool ActuatorViewModel1_2::editActuatorDevice(const QString& serialNumber, const UI::ActuatorParams_1_2& newParams)
{
    // 获取作动器所属的组ID
    int groupId = dataManager_->getActuatorGroupId(serialNumber);
    if (groupId == -1) {
        return false; // 作动器不存在
    }
    return updateActuator(serialNumber, newParams, groupId);
}

bool ActuatorViewModel1_2::deleteActuatorDevice(const QString& serialNumber)
{
    // 参数验证
    if (serialNumber.isEmpty()) {
        setError(u8"序列号不能为空", ErrorType::ValidationError);
        return false;
    }

    // 获取作动器所属的组ID
    int groupId = dataManager_->getActuatorGroupId(serialNumber);
    if (groupId == -1) {
        // 设置详细错误信息，包含数据管理器的错误
        QString dataManagerError = dataManager_->getLastError();
        if (dataManagerError.isEmpty()) {
            setError(QString(u8"作动器不存在: %1").arg(serialNumber), ErrorType::NotFoundError);
        } else {
            setError(QString(u8"获取作动器组ID失败: %1 - %2").arg(serialNumber).arg(dataManagerError), ErrorType::NotFoundError);
        }
        return false;
    }
    
    // 执行删除操作
    return removeActuator(serialNumber, groupId);
}

int ActuatorViewModel1_2::getActuatorGroupId(const QString& serialNumber) const
{
    if (!dataManager_) {
        return -1;
    }
    return dataManager_->getActuatorGroupId(serialNumber);
}

//QString ActuatorViewModel1_2::getActuatorDetailsByName(const QString& actuatorName) const
//{
//    try {
//        // 通过序列号查找作动器（假设actuatorName就是serialNumber）
//        // 获取作动器所属的组ID
//        int groupId = dataManager_->getActuatorGroupId(actuatorName);
//        if (groupId == -1) {
//            return QString(u8"未找到作动器所属组: %1").arg(actuatorName);
//        }
        
//        UI::ActuatorParams_1_2 params = getActuator(actuatorName, groupId);
//        if (params.params.sn.isEmpty()) {
//            return QString(u8"未找到作动器: %1").arg(actuatorName);
//        }

//        return actuatorParamsToDebugString(params);

//    } catch (const std::exception& e) {
//        return QString(u8"获取作动器详细信息时发生异常: %1").arg(e.what());
//    }
//}

QString ActuatorViewModel1_2::getActuatorDeviceDetails(const QString& serialNumber) const
{
    try {
        // 获取作动器所属的组ID
        int groupId = dataManager_->getActuatorGroupId(serialNumber);
        if (groupId == -1) {
            return QString(u8"未找到作动器所属组: %1").arg(serialNumber);
        }
        
        UI::ActuatorParams_1_2 params = getActuator(serialNumber, groupId);
        if (params.params.sn.isEmpty()) {
            return QString(u8"未找到作动器设备: %1").arg(serialNumber);
        }

        QString details = u8"作动器设备详细信息:\n";
        details += QString(u8"设备名称: %1\n").arg(serialNumber);
        details += QString(u8"设备类型: 作动器设备\n");
        details += actuatorParamsToDebugString(params);

        // 查找所属组信息
        QList<UI::ActuatorGroup_1_2> allGroups = getAllActuatorGroups();
        for (const auto& group : allGroups) {
            for (const auto& actuator : group.actuators) {
                if (actuator.params.sn == serialNumber) {
                    details += QString(u8"所属组: %1 (ID: %2)\n").arg(group.groupName).arg(group.groupId);
                    details += QString(u8"组内序号: %3\n").arg(actuator.actuatorId);
                    break;
                }
            }
        }

        return details;

    } catch (const std::exception& e) {
        return QString(u8"获取作动器设备详细信息时发生异常: %1").arg(e.what());
    }
}

QVector<QStringList> ActuatorViewModel1_2::exportToCSVData() const
{
    try {
        // 检查数据管理器
        if (!dataManager_) {
            setError(u8"数据管理器未初始化", ErrorType::SystemError);
            return QVector<QStringList>();
        }

        return dataManager_->exportToCSVData();

    } catch (const std::exception& e) {
        setError(QString(u8"导出CSV数据时发生异常: %1").arg(e.what()), ErrorType::SystemError);
        return QVector<QStringList>();
    }
}

QVector<QStringList> ActuatorViewModel1_2::exportGroupsToCSVData() const
{
    try {
        // 检查数据管理器
        if (!dataManager_) {
            setError(u8"数据管理器未初始化", ErrorType::SystemError);
            return QVector<QStringList>();
        }

        return dataManager_->exportGroupsToCSVData();

    } catch (const std::exception& e) {
        setError(QString(u8"导出组CSV数据时发生异常: %1").arg(e.what()), ErrorType::SystemError);
        return QVector<QStringList>();
    }
}

// 🚫 已注释：独立JSON导出功能已废弃
//QJsonArray ActuatorViewModel1_2::exportToJSONArray() const
//{
//    try {
//        // 检查数据管理器
//        if (!dataManager_) {
//            setError(u8"数据管理器未初始化", ErrorType::SystemError);
//            return QJsonArray();
//        }
//
//        return dataManager_->exportToJSONArray();
//
//    } catch (const std::exception& e) {
//        setError(QString(u8"导出JSON数据时发生异常: %1").arg(e.what()), ErrorType::SystemError);
//        return QJsonArray();
//    }
//}

void ActuatorViewModel1_2::addActuatorDeviceDebugInfo(QString& debugInfo, const QString& serialNumber) const
{
    try {
        // 获取作动器所属的组ID（同时检查作动器是否存在）
        int groupId = dataManager_->getActuatorGroupId(serialNumber);
        if (groupId == -1) {
            debugInfo += QString(u8"❌ 作动器设备未找到: %1\n").arg(serialNumber);
            return;
        }
        
        UI::ActuatorParams_1_2 params = getActuator(serialNumber, groupId);
        debugInfo += QString(u8"✅ 作动器设备: %1\n").arg(serialNumber);
        debugInfo += QString(u8"│  类型: %1\n").arg(ActuatorDataManager_1_2::actuatorTypeToString(params.type));
        debugInfo += QString(u8"│  行程: %1 m\n").arg(params.params.meas_range_max, 0, 'f', 3);
        debugInfo += QString(u8"│  位移: %1 m\n").arg(params.zero_offset, 0, 'f', 3);
        debugInfo += QString(u8"│  极性: %1\n").arg(ActuatorDataManager_1_2::polarityToString(params.params.polarity));

        // 查找所属组信息
        QList<UI::ActuatorGroup_1_2> allGroups = getAllActuatorGroups();
        for (const auto& group : allGroups) {
            for (const auto& actuator : group.actuators) {
                if (actuator.params.sn == serialNumber) {
                    debugInfo += QString(u8"│  所属组: %1 (ID: %2)\n").arg(group.groupName).arg(group.groupId);
                    debugInfo += QString(u8"│  组内ID: %3\n").arg(actuator.actuatorId);
                    return;
                }
            }
        }

        debugInfo += QString(u8"│  所属组: 未分组\n");

    } catch (const std::exception& e) {
        debugInfo += QString(u8"❌ 获取作动器调试信息时发生异常: %1\n").arg(e.what());
    }
}

void ActuatorViewModel1_2::addActuatorGroupDebugInfo(QString& debugInfo, const QString& groupName) const
{
    try {
        // 查找组
        QList<UI::ActuatorGroup_1_2> allGroups = getAllActuatorGroups();
        for (const auto& group : allGroups) {
            if (group.groupName == groupName) {
                debugInfo += QString(u8"✅ 作动器组: %1\n").arg(groupName);
                debugInfo += QString(u8"│  组ID: %1\n").arg(group.groupId);
                debugInfo += QString(u8"│  组类型: %1\n").arg(group.groupType);
                debugInfo += QString(u8"│  创建时间: %1\n").arg(group.createTime);
                debugInfo += QString(u8"│  作动器数量: %1\n").arg(group.actuators.size());

                for (int i = 0; i < group.actuators.size(); ++i) {
                    const auto& actuator = group.actuators[i];
                    debugInfo += QString(u8"│  [%1] %2 (ID: %3)\n")
                                 .arg(i + 1)
                                 .arg(actuator.params.sn)
                                 .arg(actuator.actuatorId);
                }
                return;
            }
        }

        debugInfo += QString(u8"❌ 作动器组未找到: %1\n").arg(groupName);

    } catch (const std::exception& e) {
        debugInfo += QString(u8"❌ 获取作动器组调试信息时发生异常: %1\n").arg(e.what());
    }
}

bool ActuatorViewModel1_2::validateDataIntegrity() const
{
    try {
        QStringList actuatorErrors = validateAllActuators();
        QStringList groupErrors = validateAllActuatorGroups();

        if (!actuatorErrors.isEmpty()) {
            setError(QString(u8"作动器数据完整性验证失败: %1").arg(actuatorErrors.join("; ")), ErrorType::ValidationError);
            return false;
        }

        if (!groupErrors.isEmpty()) {
            setError(QString(u8"作动器组数据完整性验证失败: %1").arg(groupErrors.join("; ")), ErrorType::ValidationError);
            return false;
        }

        return true;

    } catch (const std::exception& e) {
        setError(QString(u8"验证数据完整性时发生异常: %1").arg(e.what()), ErrorType::SystemError);
        return false;
    }
}

// ==================== 私有实现方法已在前面定义 ====================
// 这些方法的实现已经在前面的代码中定义了，这里不需要重复声明

// ==================== 延迟加载方法实现 ====================

void ActuatorViewModel1_2::ensureStatisticsLoaded() const
{
    if (statisticsLoaded_) {
        return;
    }

    try {
        // 加载统计信息
        typeStatisticsCache_ = getActuatorTypeStatistics();
        unitStatisticsCache_ = getUnitTypeStatistics();
        polarityStatisticsCache_ = getPolarityStatistics();

        statisticsLoaded_ = true;
        addLogEntry("DEBUG", u8"统计信息已加载");

    } catch (const std::exception& e) {
        addLogEntry("ERROR", QString(u8"加载统计信息时发生异常: %1").arg(e.what()));
    }
}

void ActuatorViewModel1_2::ensureGroupsLoaded() const
{
    if (groupsLoaded_) {
        return;
    }

    try {
        // 加载组数据到缓存
        QList<UI::ActuatorGroup_1_2> groups = getAllActuatorGroups();
        for (const auto& group : groups) {
            groupCache_[group.groupId] = group;
        }

        groupsLoaded_ = true;
        addLogEntry("DEBUG", QString(u8"组数据已加载: %1个组").arg(groups.size()));

    } catch (const std::exception& e) {
        addLogEntry("ERROR", QString(u8"加载组数据时发生异常: %1").arg(e.what()));
    }
}

void ActuatorViewModel1_2::ensureActuatorsLoaded() const
{
    if (actuatorsLoaded_) {
        return;
    }

    try {
        // 🔧 修复：不再使用getAllActuators缓存，因为缺少组ID信息
        // 改为加载所有组数据，这样能获得完整的组ID信息用于正确缓存
        QList<UI::ActuatorGroup_1_2> groups = getAllActuatorGroups(); // 这会触发正确的缓存更新
        
        // 统计总的作动器数量
        int totalActuators = 0;
        for (const auto& group : groups) {
            totalActuators += group.actuators.size();
        }

        actuatorsLoaded_ = true;
        addLogEntry("DEBUG", QString(u8"作动器数据已加载: %1个组，共%2个作动器").arg(groups.size()).arg(totalActuators));

    } catch (const std::exception& e) {
        addLogEntry("ERROR", QString(u8"加载作动器数据时发生异常: %1").arg(e.what()));
    }
}

void ActuatorViewModel1_2::updateStatisticsCache() const
{
    try {
        typeStatisticsCache_ = getActuatorTypeStatistics();
        unitStatisticsCache_ = getUnitTypeStatistics();
        polarityStatisticsCache_ = getPolarityStatistics();
        statisticsLoaded_ = true;

        addLogEntry("DEBUG", u8"统计缓存已更新");

    } catch (const std::exception& e) {
        addLogEntry("ERROR", QString(u8"更新统计缓存时发生异常: %1").arg(e.what()));
    }
}

int ActuatorViewModel1_2::parseGroupIdFromName(const QString& groupName) const
{
    // 尝试从组名称中解析数字ID
    QRegularExpression regex("\\d+");
    QRegularExpressionMatch match = regex.match(groupName);

    if (match.hasMatch()) {
        bool ok;
        int id = match.captured(0).toInt(&ok);
        if (ok && id > 0) {
            return id;
        }
    }

    // 如果无法解析，返回-1
    return -1;
}

// ==================== 业务逻辑接口实现 ====================

int ActuatorViewModel1_2::createActuatorGroupBusiness(const QString& groupName) {
    if (groupName.isEmpty()) {
        lastError_ = u8"组名称不能为空";
        emit businessValidationError(lastError_);
        return -1;
    }

    // 检查组名是否已存在
    if (isActuatorGroupNameExistsBusiness(groupName)) {
        lastError_ = QString(u8"作动器组名称 '%1' 已存在").arg(groupName);
        emit businessValidationError(lastError_);
        return -1;
    }

    // 生成新的组ID
    QList<UI::ActuatorGroup_1_2> allGroups = dataManager_->getAllActuatorGroups();
    int maxGroupId = 0;
    for (const UI::ActuatorGroup_1_2& group : allGroups) {
        if (group.groupId > maxGroupId) {
            maxGroupId = group.groupId;
        }
    }
    int newGroupId = maxGroupId + 1;

    // 创建新组
    UI::ActuatorGroup_1_2 newGroup;
    newGroup.groupId = newGroupId;
    newGroup.groupName = groupName;
    newGroup.groupType = u8"自动创建";
    newGroup.createTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
    newGroup.groupNotes = u8"通过业务逻辑自动创建的作动器组";

    // 保存组
    if (dataManager_->saveActuatorGroup(newGroup)) {
        emit actuatorGroupCreatedBusiness(groupName, newGroupId);
        emit actuatorGroupDataChanged(newGroupId, "create");

        qDebug() << QString(u8"%1\r\n\r\n\r\n").arg(newGroupId);
        qDebug() << QString(u8"%1\r\n\r\n\r\n").arg(newGroupId);
        qDebug() << QString(u8"%1\r\n\r\n\r\n").arg(newGroupId);
        qDebug() << QString(u8"%1\r\n\r\n\r\n").arg(newGroupId);
        qDebug() << QString(u8"%1\r\n\r\n\r\n").arg(newGroupId);
        qDebug() << QString(u8"%1\r\n\r\n\r\n").arg(newGroupId);

        return newGroupId;
    } else {
        lastError_ = QString(u8"保存作动器组失败: %1").arg(dataManager_->getLastError());
        emit businessValidationError(lastError_);
        return -1;
    }
}

bool ActuatorViewModel1_2::deleteActuatorGroupBusiness(int groupId) {
    if (groupId <= 0) {
        lastError_ = u8"无效的组ID";
        emit businessValidationError(lastError_);
        return false;
    }

    // 检查组是否存在
    if (!dataManager_->hasActuatorGroup(groupId)) {
        lastError_ = QString(u8"作动器组不存在: ID=%1").arg(groupId);
        emit businessValidationError(lastError_);
        return false;
    }

    // 获取组信息
    UI::ActuatorGroup_1_2 group = dataManager_->getActuatorGroup(groupId);

    // 删除组内所有作动器
    for (const UI::ActuatorParams_1_2& actuator : group.actuators) {
        if (!dataManager_->removeActuator(actuator.params.sn, groupId)) {
            lastError_ = QString(u8"删除作动器失败: %1").arg(actuator.params.sn);
            emit businessValidationError(lastError_);
            return false;
        }
    }

    // 删除组
    if (dataManager_->removeActuatorGroup(groupId)) {
        emit actuatorGroupDataChanged(groupId, "delete");

        qDebug() << QString(u8"delete %1\r\n\r\n\r\n").arg(groupId);
        qDebug() << QString(u8"delete %1\r\n\r\n\r\n").arg(groupId);
        qDebug() << QString(u8"delete %1\r\n\r\n\r\n").arg(groupId);
        qDebug() << QString(u8"delete %1\r\n\r\n\r\n").arg(groupId);
        qDebug() << QString(u8"delete %1\r\n\r\n\r\n").arg(groupId);

        return true;
    } else {
        lastError_ = QString(u8"删除作动器组失败: %1").arg(dataManager_->getLastError());
        emit businessValidationError(lastError_);
        return false;
    }
}

bool ActuatorViewModel1_2::isActuatorGroupNameExistsBusiness(const QString& groupName) const {
    QList<UI::ActuatorGroup_1_2> allGroups = dataManager_->getAllActuatorGroups();
    for (const UI::ActuatorGroup_1_2& group : allGroups) {
        if (group.groupName == groupName) {
            return true;
        }
    }
    return false;
}

bool ActuatorViewModel1_2::createActuatorDeviceBusiness(int groupId, const UI::ActuatorParams_1_2& params) {
    // 验证参数
    if (!validateActuatorParamsBusiness(params)) {
        return false; // 错误信息已在validateActuatorParamsBusiness中设置
    }

    // 检查组是否存在
    if (!dataManager_->hasActuatorGroup(groupId)) {
        lastError_ = QString(u8"作动器组不存在: ID=%1").arg(groupId);
        emit businessValidationError(lastError_);
        return false;
    }

    // 检查序列号在组内是否唯一
    if (!isSerialNumberUniqueInGroupBusiness(params.params.sn, groupId)) {
        lastError_ = QString(u8"组内作动器序列号重复: %1").arg(params.params.sn);
        emit businessValidationError(lastError_);
        return false;
    }

    // 获取组信息
    UI::ActuatorGroup_1_2 group = dataManager_->getActuatorGroup(groupId);

    // 为新作动器分配组内ID
    UI::ActuatorParams_1_2 actuatorWithId = params;
    int maxIdInGroup = 0;
    for (const UI::ActuatorParams_1_2& existingActuator : group.actuators) {
        if (existingActuator.actuatorId > maxIdInGroup) {
            maxIdInGroup = existingActuator.actuatorId;
        }
    }
    actuatorWithId.actuatorId = maxIdInGroup + 1;

    // 保存作动器到DataManager（指定组ID）
    if (!dataManager_->addActuator(actuatorWithId, groupId)) {
        lastError_ = QString(u8"添加作动器失败: %1").arg(dataManager_->getLastError());
        emit businessValidationError(lastError_);
        return false;
    }

    // 更新组信息
    group.actuators.append(actuatorWithId);
    if (!dataManager_->updateActuatorGroup(groupId, group)) {
        // 如果更新组失败，需要回滚作动器添加
        dataManager_->removeActuator(actuatorWithId.params.sn, groupId);
        lastError_ = QString(u8"更新作动器组失败: %1").arg(dataManager_->getLastError());
        emit businessValidationError(lastError_);
        return false;
    }

    emit actuatorDeviceCreatedBusiness(params.params.sn, groupId);
    emit actuatorDataChanged(params.params.sn, "create");
    return true;
}

bool ActuatorViewModel1_2::editActuatorDeviceBusiness(const QString& serialNumber, const UI::ActuatorParams_1_2& newParams) {
    // 验证参数
    if (!validateActuatorParamsBusiness(newParams)) {
        return false; // 错误信息已在validateActuatorParamsBusiness中设置
    }

    // 获取作动器所属的组ID（同时检查作动器是否存在）
    int groupId = dataManager_->getActuatorGroupId(serialNumber);
    if (groupId == -1) {
        lastError_ = QString(u8"作动器不存在: %1").arg(serialNumber);
        emit businessValidationError(lastError_);
        return false;
    }

    // 🔧 修复：使用精准版本的业务方法
    return editActuatorDeviceBusinessWithGroupId(serialNumber, newParams, groupId);
}

bool ActuatorViewModel1_2::editActuatorDeviceBusinessWithGroupId(const QString& serialNumber, const UI::ActuatorParams_1_2& newParams, int groupId) {
    // 验证参数
    if (!validateActuatorParamsBusiness(newParams)) {
        return false; // 错误信息已在validateActuatorParamsBusiness中设置
    }

    // 验证组ID
    if (groupId <= 0) {
        lastError_ = QString(u8"无效的组ID: %1").arg(groupId);
        emit businessValidationError(lastError_);
        return false;
    }

    // 检查作动器是否存在于指定组中
    if (!dataManager_->hasActuator(serialNumber, groupId)) {
        lastError_ = QString(u8"作动器不存在于组 %1 中: %2").arg(groupId).arg(serialNumber);
        emit businessValidationError(lastError_);
        return false;
    }

    // 获取当前作动器信息
    UI::ActuatorParams_1_2 currentParams = dataManager_->getActuator(serialNumber, groupId);

    // 如果序列号发生变化，需要检查新序列号的唯一性
    if (newParams.params.sn != serialNumber) {
        if (!isSerialNumberUniqueInGroupBusiness(newParams.params.sn, groupId, currentParams.actuatorId)) {
            lastError_ = QString(u8"新序列号在组内重复: %1").arg(newParams.params.sn);
            emit businessValidationError(lastError_);
            return false;
        }
    }

    // 保持原有的作动器ID
    UI::ActuatorParams_1_2 updatedParams = newParams;
    updatedParams.actuatorId = currentParams.actuatorId;

    // 更新作动器 (使用精准的组ID)
    if (dataManager_->updateActuator(serialNumber, updatedParams, groupId)) {
        emit actuatorDeviceEditedBusiness(newParams.params.sn, groupId);
        emit actuatorDataChanged(newParams.params.sn, "update");
        return true;
    } else {
        lastError_ = QString(u8"更新作动器失败: %1").arg(dataManager_->getLastError());
        emit businessValidationError(lastError_);
        return false;
    }
}

bool ActuatorViewModel1_2::deleteActuatorDeviceBusiness(const QString& serialNumber) {
    if (serialNumber.isEmpty()) {
        lastError_ = u8"序列号不能为空";
        emit businessValidationError(lastError_);
        return false;
    }

    // 获取作动器所属的组ID（同时检查作动器是否存在）
    int groupId = dataManager_->getActuatorGroupId(serialNumber);
    if (groupId == -1) {
        lastError_ = QString(u8"作动器不存在: %1").arg(serialNumber);
        emit businessValidationError(lastError_);
        return false;
    }

    // 删除作动器
    if (dataManager_->removeActuator(serialNumber, groupId)) {
        emit actuatorDeviceDeletedBusiness(serialNumber);
        emit actuatorDataChanged(serialNumber, "delete");
        return true;
    } else {
        lastError_ = QString(u8"删除作动器失败: %1").arg(dataManager_->getLastError());
        emit businessValidationError(lastError_);
        return false;
    }
}

bool ActuatorViewModel1_2::isSerialNumberUniqueInGroupBusiness(const QString& serialNumber, int groupId, int excludeId) const {
    if (!dataManager_->hasActuatorGroup(groupId)) {
        return true; // 组不存在，认为唯一
    }

    UI::ActuatorGroup_1_2 group = dataManager_->getActuatorGroup(groupId);
    for (const UI::ActuatorParams_1_2& actuator : group.actuators) {
        if (actuator.params.sn == serialNumber && actuator.actuatorId != excludeId) {
            return false; // 找到重复的序列号
        }
    }
    return true; // 唯一
}

int ActuatorViewModel1_2::extractGroupIdFromNameBusiness(const QString& groupName) const {
    QList<UI::ActuatorGroup_1_2> allGroups = dataManager_->getAllActuatorGroups();
    for (const UI::ActuatorGroup_1_2& group : allGroups) {
        if (group.groupName == groupName) {
            return group.groupId;
        }
    }
    return -1; // 未找到
}

QString ActuatorViewModel1_2::generateActuatorDetailedInfoBusiness(const QString& serialNumber) const {
    // 获取作动器所属的组ID（同时检查作动器是否存在）
    int groupId = dataManager_->getActuatorGroupId(serialNumber);
    if (groupId == -1) {
        return QString(u8"作动器不存在: %1").arg(serialNumber);
    }

    UI::ActuatorParams_1_2 actuator = dataManager_->getActuator(serialNumber, groupId);

    QString details;
    details += QString(u8"═══ %1 作动器设备详细信息 ═══\n").arg(serialNumber);
    details += QString(u8"作动器ID: %1\n").arg(actuator.actuatorId);
    details += QString(u8"序列号: %1\n").arg(actuator.params.sn);
    details += QString(u8"类型: %1\n").arg(ActuatorDataManager_1_2::actuatorTypeToString(actuator.type));
    details += QString(u8"单位: %1\n").arg(ActuatorDataManager_1_2::measurementUnitToString(actuator.params.meas_unit));
    details += u8"─────────────────────\n";

    // 物理参数
    details += u8"物理参数:\n";
    details += QString(u8"│  缸径: %1 m\n").arg(actuator.params.meas_range_max, 0, 'f', 3);
    details += QString(u8"│  杆径: %1 m\n").arg(actuator.params.meas_range_min, 0, 'f', 3);
    details += QString(u8"│  行程: %1 m\n").arg(actuator.params.meas_range_max, 0, 'f', 3);

    // 截面参数
    details += u8"截面参数:\n";
    details += QString(u8"│  拉伸面积: %1 m²\n").arg(actuator.params.meas_range_max, 0, 'f', 6);
    details += QString(u8"│  压缩面积: %1 m²\n").arg(actuator.params.meas_range_min, 0, 'f', 6);
    details += QString(u8"│  位移: %1 m\n").arg(actuator.zero_offset, 0, 'f', 3);

    // 伺服控制器参数
    details += u8"伺服控制器参数:\n";
    details += QString(u8"│  极性: %1\n").arg(ActuatorDataManager_1_2::polarityToString(actuator.params.polarity));
    details += QString(u8"│  Dither值: %1 V\n").arg(actuator.params.k, 0, 'f', 1);
    details += QString(u8"│  频率: %1 Hz\n").arg(actuator.params.precision, 0, 'f', 1);
    details += QString(u8"│  输出倍数: %1\n").arg(actuator.params.output_signal_unit);
    details += QString(u8"│  平衡值: %1 V\n").arg(actuator.params.b, 0, 'f', 1);

    // 查找所属组信息
    QList<UI::ActuatorGroup_1_2> allGroups = dataManager_->getAllActuatorGroups();
    for (const UI::ActuatorGroup_1_2& group : allGroups) {
        for (const UI::ActuatorParams_1_2& groupActuator : group.actuators) {
            if (groupActuator.params.sn == serialNumber) {
                details += u8"─────────────────────\n";
                details += QString(u8"所属组: %1 (ID:%2)\n").arg(group.groupName).arg(group.groupId);
                details += QString(u8"组创建时间: %1\n").arg(group.createTime);
                break;
            }
        }
    }

    // 其他信息
    details += u8"其他信息:\n";
    details += QString(u8"│  作动器名称: %1\n").arg(actuator.name.isEmpty() ? u8"未设置" : actuator.name);
    if (!actuator.name.isEmpty()) {
        details += QString(u8"备注: %1").arg(actuator.name);
    } else {
        details += u8"备注: 无";
    }

    return details;
}

QStringList ActuatorViewModel1_2::getActuatorGroupNamesBusiness() const {
    QStringList groupNames;
    QList<UI::ActuatorGroup_1_2> allGroups = dataManager_->getAllActuatorGroups();
    for (const UI::ActuatorGroup_1_2& group : allGroups) {
        groupNames.append(group.groupName);
    }
    return groupNames;
}

bool ActuatorViewModel1_2::validateActuatorParamsBusiness(const UI::ActuatorParams_1_2& params) const {
    // 检查序列号
    if (params.params.sn.isEmpty()) {
        const_cast<ActuatorViewModel1_2*>(this)->lastError_ = u8"作动器序列号不能为空";
        const_cast<ActuatorViewModel1_2*>(this)->businessValidationError(lastError_);
        return false;
    }

    // 检查类型（枚举类型不需要检查isEmpty）
    // 枚举类型总是有有效值，无需检查

    // 检查物理参数的合理性
    if (params.params.meas_range_max <= 0) {
        const_cast<ActuatorViewModel1_2*>(this)->lastError_ = u8"缸径必须大于0";
        const_cast<ActuatorViewModel1_2*>(this)->businessValidationError(lastError_);
        return false;
    }

//    if (params.params.meas_range_min <= 0) {
//        const_cast<ActuatorViewModel1_2*>(this)->lastError_ = u8"杆径必须大于0";
//        const_cast<ActuatorViewModel1_2*>(this)->businessValidationError(lastError_);
//        return false;
//    }

//    if (params.params.meas_range_max <= 0) {
//        const_cast<ActuatorViewModel1_2*>(this)->lastError_ = u8"行程必须大于0";
//        const_cast<ActuatorViewModel1_2*>(this)->businessValidationError(lastError_);
//        return false;
//    }

//    // 检查杆径不能大于缸径
//    if (params.params.meas_range_min >= params.params.meas_range_max) {
//        const_cast<ActuatorViewModel1_2*>(this)->lastError_ = u8"杆径不能大于或等于缸径";
//        const_cast<ActuatorViewModel1_2*>(this)->businessValidationError(lastError_);
//        return false;
//    }

    return true;
}

QString ActuatorViewModel1_2::getActuatorGroupNameBusiness(int groupId) const {
    if (dataManager_->hasActuatorGroup(groupId)) {
        UI::ActuatorGroup_1_2 group = dataManager_->getActuatorGroup(groupId);
        return group.groupName;
    }
    return QString(); // 返回空字符串表示未找到
}

QString ActuatorViewModel1_2::generateActuatorGroupDebugInfoBusiness(const QString& groupName) const {
    QString debugInfo;

    if (!dataManager_) {
        debugInfo += u8"❌ ActuatorDataManager未初始化\n";
        return debugInfo;
    }

    // 🔧 修复：通过组名称查找对应的组，而不是从名称提取ID
    QList<UI::ActuatorGroup_1_2> allGroups = dataManager_->getAllActuatorGroups();
    UI::ActuatorGroup_1_2 targetGroup;
    bool groupFound = false;

    for (const UI::ActuatorGroup_1_2& group : allGroups) {
        if (group.groupName == groupName) {
            targetGroup = group;
            groupFound = true;
            break;
        }
    }

    if (groupFound) {
        // 🔧 DEBUG信息：显示组ID、ID、序号，并添加诊断信息
        debugInfo += QString(u8"组ID: %1").arg(targetGroup.groupId);

        // 🔧 添加组ID诊断信息
        if (targetGroup.groupId > 1000) {
            debugInfo += QString(u8" ⚠️异常值");
        }
        debugInfo += u8"\n";

        // 🔧 DEBUG - 显示组的详细信息用于诊断
        debugInfo += QString(u8"🔍 组名: %1, 作动器数: %2\n")
                    .arg(targetGroup.groupName)
                    .arg(targetGroup.actuators.size());

        // 显示组内作动器的ID和序号
        if (!targetGroup.actuators.isEmpty()) {
            for (int i = 0; i < targetGroup.actuators.size(); ++i) {
                const auto& actuator = targetGroup.actuators[i];
                debugInfo += QString(u8"ID: %1, 序号: %2\n")
                            .arg(actuator.actuatorId)
                            .arg(i + 1); // 序号从1开始
            }
        }
    } else {
        debugInfo += QString(u8"❌ 未找到对应的作动器组: %1\n").arg(groupName);

        // 显示数据管理器中的所有作动器组
        QList<UI::ActuatorGroup_1_2> allGroups = dataManager_->getAllActuatorGroups();
        if (!allGroups.isEmpty()) {
            debugInfo += u8"📋 数据管理器中的作动器组:\n";
            for (int i = 0; i < qMin(3, allGroups.size()); ++i) {
                debugInfo += QString(u8"  [%1] %2 (ID:%3)\n")
                            .arg(i+1)
                            .arg(allGroups[i].groupName)
                            .arg(allGroups[i].groupId);
            }
            if (allGroups.size() > 3) {
                debugInfo += QString(u8"  ... 还有%1个\n").arg(allGroups.size() - 3);
            }
        } else {
            debugInfo += u8"📋 作动器组数据管理器为空\n";
        }
    }

    return debugInfo;
}

QString ActuatorViewModel1_2::generateActuatorDeviceDebugInfoBusiness(const QString& serialNumber) const {
    QString debugInfo;

    if (!dataManager_) {
        debugInfo += u8"❌ ActuatorDataManager未初始化\n";
        return debugInfo;
    }

    // 获取作动器所属的组ID（同时检查作动器是否存在）
    int groupId = dataManager_->getActuatorGroupId(serialNumber);
    if (groupId != -1) {

        UI::ActuatorParams_1_2 actuator = dataManager_->getActuator(serialNumber, groupId);

        // 🔧 DEBUG - 查找所属组ID和序号信息
        QList<UI::ActuatorGroup_1_2> allGroups = dataManager_->getAllActuatorGroups();
        bool foundGroup = false;
        int actuatorSequenceInGroup = 0;
        int groupId = 0;

        for (const auto& group : allGroups) {
            for (int i = 0; i < group.actuators.size(); ++i) {
                if (group.actuators[i].params.sn == serialNumber) {
                    actuatorSequenceInGroup = i + 1; // 从1开始计数
                    groupId = group.groupId;
                    foundGroup = true;
                    break;
                }
            }
            if (foundGroup) break;
        }

        // 🔧 DEBUG信息：只显示当前设备的组ID、ID、序号
        if (foundGroup) {
            debugInfo += QString(u8"组ID: %1, ID: %2, 序号: %3\n")
                        .arg(groupId)
                        .arg(actuator.actuatorId)
                        .arg(actuatorSequenceInGroup);
        } else {
            debugInfo += QString(u8"ID: %1, 序号: 未知\n").arg(actuator.actuatorId);
        }

    } else {
        // 🔧 DEBUG - 显示诊断信息
        debugInfo += QString(u8"❌ 作动器数据未找到: %1\n").arg(serialNumber);

        // 显示数据管理器中的所有作动器序列号
        QList<UI::ActuatorParams_1_2> allActuators = dataManager_->getAllActuators();
        if (!allActuators.isEmpty()) {
            debugInfo += u8"📋 数据管理器中的作动器:\n";
            for (int i = 0; i < qMin(3, allActuators.size()); ++i) {
                debugInfo += QString(u8"  [%1] %2\n").arg(i+1).arg(allActuators[i].params.sn);
            }
            if (allActuators.size() > 3) {
                debugInfo += QString(u8"  ... 还有%1个\n").arg(allActuators.size() - 3);
            }
        } else {
            debugInfo += u8"📋 数据管理器为空\n";
        }
    }

    return debugInfo;
}
