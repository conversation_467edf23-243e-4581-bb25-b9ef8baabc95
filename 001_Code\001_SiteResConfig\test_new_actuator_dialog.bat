@echo off
chcp 65001 >nul
echo ========================================
echo  新建作动器界面测试
echo ========================================
echo.

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile* >nul 2>&1
if exist "debug" rmdir /s /q debug >nul 2>&1
if exist "release" rmdir /s /q release >nul 2>&1
if exist "*.o" del *.o >nul 2>&1
if exist "ui_*.h" del ui_*.h >nul 2>&1

echo.
echo 检查新建作动器界面修改...
echo.
echo 1. 检查ActuatorParams结构体修改...
findstr /C:"tensionArea" "include\ActuatorDialog.h" >nul
if errorlevel 1 (
    echo ❌ ActuatorParams结构体未添加截面数据字段
    goto :error
) else (
    echo ✅ ActuatorParams结构体已添加截面数据字段
)

echo.
echo 2. 检查UI界面尺寸修改...
findstr /C:"800" "ui\ActuatorDialog.ui" | findstr /C:"600" >nul
if errorlevel 1 (
    echo ❌ UI界面尺寸未修改为800x600
    goto :error
) else (
    echo ✅ UI界面尺寸已修改为800x600
)

echo.
echo 3. 检查双面板布局...
findstr /C:"actuatorGroupBox" "ui\ActuatorDialog.ui" >nul
if errorlevel 1 (
    echo ❌ 左侧Actuator面板未创建
    goto :error
) else (
    echo ✅ 左侧Actuator面板已创建
)

findstr /C:"servoGroupBox" "ui\ActuatorDialog.ui" >nul
if errorlevel 1 (
    echo ❌ 右侧伺服控制器面板未创建
    goto :error
) else (
    echo ✅ 右侧伺服控制器面板已创建
)

echo.
echo 4. 检查新增控件...
findstr /C:"tensionAreaSpinBox" "ui\ActuatorDialog.ui" >nul
if errorlevel 1 (
    echo ❌ 拉伸面积控件未添加
    goto :error
) else (
    echo ✅ 拉伸面积控件已添加
)

findstr /C:"compressionAreaSpinBox" "ui\ActuatorDialog.ui" >nul
if errorlevel 1 (
    echo ❌ 压缩面积控件未添加
    goto :error
) else (
    echo ✅ 压缩面积控件已添加
)

findstr /C:"categoryCombo" "ui\ActuatorDialog.ui" >nul
if errorlevel 1 (
    echo ❌ 分类选择控件未添加
    goto :error
) else (
    echo ✅ 分类选择控件已添加
)

echo.
echo 5. 检查新增槽函数...
findstr /C:"onPreviewClicked" "include\ActuatorDialog.h" >nul
if errorlevel 1 (
    echo ❌ 预览功能槽函数未声明
    goto :error
) else (
    echo ✅ 预览功能槽函数已声明
)

findstr /C:"onCategoryChanged" "include\ActuatorDialog.h" >nul
if errorlevel 1 (
    echo ❌ 分类变化槽函数未声明
    goto :error
) else (
    echo ✅ 分类变化槽函数已声明
)

echo.
echo 6. 检查计算方法...
findstr /C:"calculateCylinderDiameter" "include\ActuatorDialog.h" >nul
if errorlevel 1 (
    echo ❌ 缸径计算方法未声明
    goto :error
) else (
    echo ✅ 缸径计算方法已声明
)

findstr /C:"calculateRodDiameter" "include\ActuatorDialog.h" >nul
if errorlevel 1 (
    echo ❌ 杆径计算方法未声明
    goto :error
) else (
    echo ✅ 杆径计算方法已声明
)

echo.
echo 生成UI头文件...
uic ui\ActuatorDialog.ui -o ui_ActuatorDialog.h >nul 2>&1
if errorlevel 1 (
    echo ❌ ActuatorDialog UI文件生成失败
    goto :error
) else (
    echo ✅ ActuatorDialog UI头文件生成成功
)

uic ui\MainWindow.ui -o ui_MainWindow.h >nul 2>&1
if errorlevel 1 (
    echo ❌ MainWindow UI文件生成失败
    goto :error
) else (
    echo ✅ MainWindow UI头文件生成成功
)

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++ >nul 2>&1
if errorlevel 1 (
    echo ❌ qmake失败
    goto :error
) else (
    echo ✅ Makefile生成成功
)

echo.
echo 开始编译测试...
echo 这可能需要几分钟时间，请耐心等待...
echo.

mingw32-make clean >nul 2>&1
mingw32-make -j4 2>compile_errors.txt
if errorlevel 1 (
    echo ❌ 编译失败！
    echo.
    echo 编译错误信息：
    type compile_errors.txt
    echo.
    echo 请检查上述错误信息并进行修复。
    pause
    exit /b 1
) else (
    echo ✅ 编译成功！
    
    if exist compile_errors.txt del compile_errors.txt >nul 2>&1
    
    echo.
    echo ========================================
    echo  🎉 新建作动器界面编译成功！
    echo ========================================
    
    if exist "SiteResConfig.exe" (
        echo.
        echo 📊 编译结果:
        echo ├─ 可执行文件: SiteResConfig.exe
        echo ├─ 文件大小: 
        for %%F in (SiteResConfig.exe) do echo │  └─ %%~zF 字节
        echo └─ 修改时间: 
        for %%F in (SiteResConfig.exe) do echo    └─ %%~tF
        echo.
        echo 🎯 新建作动器界面特性:
        echo.
        echo 📋 界面布局:
        echo ├─ ✅ 800x600像素对话框
        echo ├─ ✅ 双面板布局设计
        echo ├─ ✅ 左侧Actuator基本参数面板
        echo ├─ ✅ 右侧伺服控制器参数面板
        echo └─ ✅ 底部操作按钮区域
        echo.
        echo 🔧 Actuator基本参数:
        echo ├─ ✅ Serial Number (序列号输入)
        echo ├─ ✅ Unit Length (单位长度选择)
        echo ├─ ✅ Stroke (行程设置)
        echo ├─ ✅ Displacement (位移设置)
        echo ├─ ✅ Tension Area (拉伸面积)
        echo └─ ✅ Compression Area (压缩面积)
        echo.
        echo ⚡ 伺服控制器参数:
        echo ├─ ✅ Type (作动器类型)
        echo ├─ ✅ Polarity (极性选择)
        echo ├─ ✅ Deliver (输出电压)
        echo ├─ ✅ Frequency (控制频率)
        echo ├─ ✅ Output Multiplier (输出倍数)
        echo ├─ ✅ Balance (平衡电压)
        echo └─ ✅ Auto Balance (自动平衡按钮)
        echo.
        echo 🎨 用户体验特性:
        echo ├─ ✅ 作动器分类选择 (液压/电动/气动/伺服)
        echo ├─ ✅ 智能参数预设 (根据类型自动调整)
        echo ├─ ✅ 配置预览功能 (完整参数预览)
        echo ├─ ✅ 帮助说明功能 (详细使用指导)
        echo ├─ ✅ 类型信息提示 (各类型特点说明)
        echo └─ ✅ 专业界面样式 (分组边框、图标标识)
        echo.
        echo 🔢 智能计算功能:
        echo ├─ ✅ 缸径自动计算 (从拉伸面积计算)
        echo ├─ ✅ 杆径自动计算 (从面积差计算)
        echo ├─ ✅ 参数验证检查 (合理性验证)
        echo └─ ✅ 数据结构统一 (与ActuatorParams完全匹配)
        echo.
        echo 💾 操作按钮:
        echo ├─ ✅ 保存并添加 (主要操作)
        echo ├─ ✅ 取消 (取消操作)
        echo ├─ ✅ 预览配置 (查看完整配置)
        echo └─ ✅ 帮助 (使用说明)
        echo.
        echo 🚀 使用方法:
        echo.
        echo 1. 选择作动器分类 (自动调整默认参数)
        echo 2. 输入序列号和基本参数
        echo 3. 设置截面数据 (行程、位移、面积)
        echo 4. 配置伺服控制器参数
        echo 5. 点击预览查看完整配置
        echo 6. 保存并添加到硬件树
        echo.
        
        set /p choice="是否启动程序测试新建作动器界面？(Y/N): "
        if /i "%choice%"=="Y" (
            echo 启动程序...
            start SiteResConfig.exe
        )
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
        set /p choice="是否启动程序测试？(Y/N): "
        if /i "%choice%"=="Y" start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
        set /p choice="是否启动程序测试？(Y/N): "
        if /i "%choice%"=="Y" start release\SiteResConfig.exe
    ) else (
        echo ⚠️ 警告: 找不到可执行文件
    )
)

echo.
echo 📖 新建作动器界面说明:
echo.
echo 🎯 核心改进:
echo - 双面板布局，清晰分离不同参数类型
echo - 截面数据与ActuatorParams结构体完全统一
echo - 智能参数计算，自动计算缸径和杆径
echo - 丰富的用户交互功能和帮助系统
echo.
echo 🔧 技术特点:
echo - 基于Qt Designer的专业界面设计
echo - 完整的信号槽连接和事件处理
echo - 数据验证和错误处理机制
echo - 与现有系统无缝集成
echo.
echo 测试完成！
pause
exit /b 0

:error
echo.
echo ❌ 新建作动器界面测试失败！
echo.
echo 可能的问题：
echo 1. 代码修改不完整
echo 2. UI文件格式错误
echo 3. 槽函数实现缺失
echo 4. 编译环境问题
echo.
echo 请检查错误信息并重新修复。
pause
exit /b 1
