<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ActuatorDialog_1_2</class>
 <widget class="QDialog" name="ActuatorDialog_1_2">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>900</width>
    <height>650</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>作动器配置 - 新需求格式</string>
  </property>
  <property name="modal">
   <bool>true</bool>
  </property>
  <layout class="QVBoxLayout" name="mainLayout">
   <item>
    <widget class="QLabel" name="titleLabel">
     <property name="font">
      <font>
       <pointsize>14</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="text">
      <string>作动器配置 - 新需求格式</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QTabWidget" name="tabWidget">
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="basicTab">
      <attribute name="title">
       <string>基本信息</string>
      </attribute>
      <layout class="QFormLayout" name="basicFormLayout">
       <item row="0" column="0">
        <widget class="QLabel" name="nameLabel">
         <property name="text">
          <string>控制量名称:</string>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QLineEdit" name="nameEdit"/>
       </item>
       <item row="1" column="0">
        <widget class="QLabel" name="typeLabel">
         <property name="text">
          <string>作动器类型:</string>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="QComboBox" name="typeCombo">
         <item>
          <property name="text">
           <string>单出杆</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>双出杆</string>
          </property>
         </item>
        </widget>
       </item>
       <item row="2" column="0">
        <widget class="QLabel" name="zeroOffsetLabel">
         <property name="text">
          <string>零偏:</string>
         </property>
        </widget>
       </item>
       <item row="2" column="1">
        <widget class="QDoubleSpinBox" name="zeroOffsetSpinBox">
         <property name="decimals">
          <number>3</number>
         </property>
         <property name="minimum">
          <double>-1000.000000000000000</double>
         </property>
         <property name="maximum">
          <double>1000.000000000000000</double>
         </property>
        </widget>
       </item>
       <item row="3" column="0">
        <widget class="QLabel" name="serialLabel">
         <property name="text">
          <string>序列号:</string>
         </property>
        </widget>
       </item>
       <item row="3" column="1">
        <widget class="QLineEdit" name="serialEdit"/>
       </item>
       <item row="4" column="0">
        <widget class="QLabel" name="modelLabel">
         <property name="text">
          <string>型号:</string>
         </property>
        </widget>
       </item>
       <item row="4" column="1">
        <widget class="QLineEdit" name="modelEdit">
         <property name="text">
          <string>MD500</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="hardwareTab">
      <attribute name="title">
       <string>硬件配置</string>
      </attribute>
      <layout class="QFormLayout" name="hardwareFormLayout">
       <item row="0" column="0">
        <widget class="QLabel" name="lcIdLabel">
         <property name="text">
          <string>下位机ID:</string>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QSpinBox" name="lcIdSpinBox">
         <property name="minimum">
          <number>1</number>
         </property>
         <property name="maximum">
          <number>255</number>
         </property>
         <property name="value">
          <number>1</number>
         </property>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QLabel" name="stationIdLabel">
         <property name="text">
          <string>站点ID:</string>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="QSpinBox" name="stationIdSpinBox">
         <property name="minimum">
          <number>1</number>
         </property>
         <property name="maximum">
          <number>255</number>
         </property>
         <property name="value">
          <number>1</number>
         </property>
        </widget>
       </item>
       <item row="2" column="0">
        <widget class="QLabel" name="aoConfigLabel">
         <property name="text">
          <string>AO板卡配置:</string>
         </property>
        </widget>
       </item>
       <item row="3" column="0">
        <widget class="QLabel" name="boardIdAoLabel">
         <property name="text">
          <string>  板卡ID:</string>
         </property>
        </widget>
       </item>
       <item row="3" column="1">
        <widget class="QSpinBox" name="boardIdAoSpinBox">
         <property name="minimum">
          <number>1</number>
         </property>
         <property name="maximum">
          <number>255</number>
         </property>
         <property name="value">
          <number>1</number>
         </property>
        </widget>
       </item>
       <item row="4" column="0">
        <widget class="QLabel" name="boardTypeAoLabel">
         <property name="text">
          <string>  板卡类型:</string>
         </property>
        </widget>
       </item>
       <item row="4" column="1">
        <widget class="QSpinBox" name="boardTypeAoSpinBox">
         <property name="minimum">
          <number>1</number>
         </property>
         <property name="maximum">
          <number>255</number>
         </property>
         <property name="value">
          <number>1</number>
         </property>
        </widget>
       </item>
       <item row="5" column="0">
        <widget class="QLabel" name="portIdAoLabel">
         <property name="text">
          <string>  端口ID:</string>
         </property>
        </widget>
       </item>
       <item row="5" column="1">
        <widget class="QSpinBox" name="portIdAoSpinBox">
         <property name="minimum">
          <number>1</number>
         </property>
         <property name="maximum">
          <number>255</number>
         </property>
         <property name="value">
          <number>1</number>
         </property>
        </widget>
       </item>
       <item row="6" column="0">
        <widget class="QLabel" name="doConfigLabel">
         <property name="text">
          <string>DO板卡配置:</string>
         </property>
        </widget>
       </item>
       <item row="7" column="0">
        <widget class="QLabel" name="boardIdDoLabel">
         <property name="text">
          <string>  板卡ID:</string>
         </property>
        </widget>
       </item>
       <item row="7" column="1">
        <widget class="QSpinBox" name="boardIdDoSpinBox">
         <property name="minimum">
          <number>1</number>
         </property>
         <property name="maximum">
          <number>255</number>
         </property>
         <property name="value">
          <number>1</number>
         </property>
        </widget>
       </item>
       <item row="8" column="0">
        <widget class="QLabel" name="boardTypeDoLabel">
         <property name="text">
          <string>  板卡类型:</string>
         </property>
        </widget>
       </item>
       <item row="8" column="1">
        <widget class="QSpinBox" name="boardTypeDoSpinBox">
         <property name="minimum">
          <number>1</number>
         </property>
         <property name="maximum">
          <number>255</number>
         </property>
         <property name="value">
          <number>1</number>
         </property>
        </widget>
       </item>
       <item row="9" column="0">
        <widget class="QLabel" name="portIdDoLabel">
         <property name="text">
          <string>  端口ID:</string>
         </property>
        </widget>
       </item>
       <item row="9" column="1">
        <widget class="QSpinBox" name="portIdDoSpinBox">
         <property name="minimum">
          <number>1</number>
         </property>
         <property name="maximum">
          <number>255</number>
         </property>
         <property name="value">
          <number>1</number>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="paramsTab">
      <attribute name="title">
       <string>详细参数</string>
      </attribute>
      <layout class="QFormLayout" name="paramsFormLayout">
       <item row="0" column="0">
        <widget class="QLabel" name="kLabel">
         <property name="text">
          <string>K系数:</string>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QDoubleSpinBox" name="kSpinBox">
         <property name="decimals">
          <number>6</number>
         </property>
         <property name="minimum">
          <double>0.000001000000000</double>
         </property>
         <property name="maximum">
          <double>1000.000000000000000</double>
         </property>
         <property name="value">
          <double>1.000000000000000</double>
         </property>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QLabel" name="bLabel">
         <property name="text">
          <string>B系数:</string>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="QDoubleSpinBox" name="bSpinBox">
         <property name="decimals">
          <number>6</number>
         </property>
         <property name="minimum">
          <double>-1000.000000000000000</double>
         </property>
         <property name="maximum">
          <double>1000.000000000000000</double>
         </property>
         <property name="value">
          <double>0.000000000000000</double>
         </property>
        </widget>
       </item>
       <item row="2" column="0">
        <widget class="QLabel" name="precisionLabel">
         <property name="text">
          <string>精度:</string>
         </property>
        </widget>
       </item>
       <item row="2" column="1">
        <widget class="QDoubleSpinBox" name="precisionSpinBox">
         <property name="decimals">
          <number>6</number>
         </property>
         <property name="minimum">
          <double>0.000001000000000</double>
         </property>
         <property name="maximum">
          <double>100.000000000000000</double>
         </property>
         <property name="value">
          <double>0.100000000000000</double>
         </property>
        </widget>
       </item>
       <item row="3" column="0">
        <widget class="QLabel" name="polarityLabel">
         <property name="text">
          <string>极性:</string>
         </property>
        </widget>
       </item>
       <item row="3" column="1">
        <widget class="QComboBox" name="polarityCombo">
         <item>
          <property name="text">
           <string>正极性 (+)</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>负极性 (-)</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>双极性 (±)</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>无极性</string>
          </property>
         </item>
        </widget>
       </item>
       <item row="4" column="0">
        <widget class="QLabel" name="measUnitLabel">
         <property name="text">
          <string>测量单位:</string>
         </property>
        </widget>
       </item>
       <item row="4" column="1">
        <widget class="QComboBox" name="measUnitCombo">
         <item>
          <property name="text">
           <string>mm</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>m</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>cm</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>inch</string>
          </property>
         </item>
        </widget>
       </item>
       <item row="5" column="0">
        <widget class="QLabel" name="measRangeLabel">
         <property name="text">
          <string>测量范围:</string>
         </property>
        </widget>
       </item>
       <item row="6" column="0">
        <widget class="QLabel" name="measRangeMinLabel">
         <property name="text">
          <string>  下限:</string>
         </property>
        </widget>
       </item>
       <item row="6" column="1">
        <widget class="QDoubleSpinBox" name="measRangeMinSpinBox">
         <property name="decimals">
          <number>3</number>
         </property>
         <property name="minimum">
          <double>-10000.000000000000000</double>
         </property>
         <property name="maximum">
          <double>10000.000000000000000</double>
         </property>
         <property name="value">
          <double>-100.000000000000000</double>
         </property>
        </widget>
       </item>
       <item row="7" column="0">
        <widget class="QLabel" name="measRangeMaxLabel">
         <property name="text">
          <string>  上限:</string>
         </property>
        </widget>
       </item>
       <item row="7" column="1">
        <widget class="QDoubleSpinBox" name="measRangeMaxSpinBox">
         <property name="decimals">
          <number>3</number>
         </property>
         <property name="minimum">
          <double>-10000.000000000000000</double>
         </property>
         <property name="maximum">
          <double>10000.000000000000000</double>
         </property>
         <property name="value">
          <double>100.000000000000000</double>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="outputTab">
      <attribute name="title">
       <string>输出信号</string>
      </attribute>
      <layout class="QFormLayout" name="outputFormLayout">
       <item row="0" column="0">
        <widget class="QLabel" name="outputSignalUnitLabel">
         <property name="text">
          <string>输出信号单位:</string>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QSpinBox" name="outputSignalUnitSpinBox">
         <property name="minimum">
          <number>1</number>
         </property>
         <property name="maximum">
          <number>10</number>
         </property>
         <property name="value">
          <number>1</number>
         </property>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="QLabel" name="outputRangeLabel">
         <property name="text">
          <string>输出信号范围:</string>
         </property>
        </widget>
       </item>
       <item row="2" column="0">
        <widget class="QLabel" name="outputRangeMinLabel">
         <property name="text">
          <string>  下限:</string>
         </property>
        </widget>
       </item>
       <item row="2" column="1">
        <widget class="QDoubleSpinBox" name="outputRangeMinSpinBox">
         <property name="decimals">
          <number>3</number>
         </property>
         <property name="minimum">
          <double>-100.000000000000000</double>
         </property>
         <property name="maximum">
          <double>100.000000000000000</double>
         </property>
         <property name="value">
          <double>-10.000000000000000</double>
         </property>
        </widget>
       </item>
       <item row="3" column="0">
        <widget class="QLabel" name="outputRangeMaxLabel">
         <property name="text">
          <string>  上限:</string>
         </property>
        </widget>
       </item>
       <item row="3" column="1">
        <widget class="QDoubleSpinBox" name="outputRangeMaxSpinBox">
         <property name="decimals">
          <number>3</number>
         </property>
         <property name="minimum">
          <double>-100.000000000000000</double>
         </property>
         <property name="maximum">
          <double>100.000000000000000</double>
         </property>
         <property name="value">
          <double>10.000000000000000</double>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="buttonLayout">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="okButton">
       <property name="text">
        <string>确定</string>
       </property>
       <property name="default">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="cancelButton">
       <property name="text">
        <string>取消</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>okButton</sender>
   <signal>clicked()</signal>
   <receiver>ActuatorDialog_1_2</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>278</x>
     <y>253</y>
    </hint>
    <hint type="destinationlabel">
     <x>96</x>
     <y>254</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>cancelButton</sender>
   <signal>clicked()</signal>
   <receiver>ActuatorDialog_1_2</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>369</x>
     <y>253</y>
    </hint>
    <hint type="destinationlabel">
     <x>179</x>
     <y>254</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>


