/**
 * @file ControlChannelEditDialog.cpp
 * @brief 控制通道编辑对话框实现
 * @details 实现控制通道的完整配置编辑功能，使用UI文件定义界面
 * <AUTHOR> Assistant
 * @date 2025-01-27
 * @version 1.1.0
 */

#include "ControlChannelEditDialog.h"
#include "ui_ControlChannelEditDialog.h"
#include "MainWindow_Qt_Simple.h"
#include <QtWidgets/QMessageBox>
#include <QtWidgets/QTabWidget>
#include <QtCore/QDebug>
#include <QtCore/QTimer> // Added for QTimer

namespace UI {

ControlChannelEditDialog::ControlChannelEditDialog(const ControlChannelParams& params, CMyMainWindow* mainWindow, QWidget* parent)
    : QDialog(parent)
    , ui(new Ui::ControlChannelEditDialog)
    , mainWindow_(mainWindow)
    , originalParams_(params)
{
    try {
        ui->setupUi(this);
        
        // 🆕 验证UI控件是否正确创建
        if (!validateUIControls()) {
            qDebug() << "ERROR: UI控件验证失败，无法继续初始化";
            return;
        }
        
        setWindowTitle(QString(u8"编辑控制通道配置 - %1").arg(QString::fromStdString(params.channelId)));
        
        qDebug() << "ControlChannelEditDialog: 开始初始化，通道ID:" << QString::fromStdString(params.channelId);
        
        initializeUI();
        
        // 🆕 延迟设置参数，确保组数据加载完成
        QTimer::singleShot(50, this, [this, params]() {
            setControlChannelParams(params);
        });
        
        connectSignals();
        
        qDebug() << "ControlChannelEditDialog: 初始化完成";
    } catch (const std::exception& e) {
        qDebug() << "ControlChannelEditDialog: 初始化时发生异常:" << e.what();
        showErrorMessage(QString("初始化失败: %1").arg(e.what()));
    } catch (...) {
        qDebug() << "ControlChannelEditDialog: 初始化时发生未知异常";
        showErrorMessage("初始化时发生未知异常");
    }
}

ControlChannelEditDialog::~ControlChannelEditDialog() {
    delete ui;
}

void ControlChannelEditDialog::initializeUI() {
    // UI已经通过.ui文件创建，只需初始化下拉框选项
    initializeComboBoxes();
}

void ControlChannelEditDialog::connectSignals() {
    // 按钮信号已经在UI文件中连接
    connect(ui->okButton, &QPushButton::clicked, this, &ControlChannelEditDialog::onAcceptClicked);
    
    // 输入字段变更时触发校验
    connect(ui->channelNameEdit, &QLineEdit::textChanged, this, &ControlChannelEditDialog::validateInput);
    connect(ui->lcIdSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), this, &ControlChannelEditDialog::validateInput);
    connect(ui->stationIdSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), this, &ControlChannelEditDialog::validateInput);
    connect(ui->controlModeCombo, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &ControlChannelEditDialog::validateInput);
    
    // 极性变更信号
    connect(ui->servoControlPolarityCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &ControlChannelEditDialog::onPolarityChanged);
    connect(ui->payload1PolarityCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &ControlChannelEditDialog::onPolarityChanged);
    connect(ui->payload2PolarityCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &ControlChannelEditDialog::onPolarityChanged);
    connect(ui->positionPolarityCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &ControlChannelEditDialog::onPolarityChanged);
    
    // 🔧 修改：连接新的两级下拉框信号
    
    // 硬件组选择变更
    connect(ui->hardwareGroupCombo, QOverload<const QString&>::of(&QComboBox::currentTextChanged),
            this, &ControlChannelEditDialog::onHardwareGroupChanged);
    
    // 传感器组选择变更
    connect(ui->load1SensorGroupCombo, QOverload<const QString&>::of(&QComboBox::currentTextChanged),
            this, &ControlChannelEditDialog::onLoad1SensorGroupChanged);
    connect(ui->load2SensorGroupCombo, QOverload<const QString&>::of(&QComboBox::currentTextChanged),
            this, &ControlChannelEditDialog::onLoad2SensorGroupChanged);
    connect(ui->positionSensorGroupCombo, QOverload<const QString&>::of(&QComboBox::currentTextChanged),
            this, &ControlChannelEditDialog::onPositionSensorGroupChanged);
    
    // 作动器组选择变更
    connect(ui->controlActuatorGroupCombo, QOverload<const QString&>::of(&QComboBox::currentTextChanged),
            this, &ControlChannelEditDialog::onControlActuatorGroupChanged);
    
    // 关联配置变更信号（设备选择变更时）
    connect(ui->hardwareChannelCombo, QOverload<const QString&>::of(&QComboBox::currentTextChanged),
            this, &ControlChannelEditDialog::onAssociationChanged);
    connect(ui->load1SensorDeviceCombo, QOverload<const QString&>::of(&QComboBox::currentTextChanged),
            this, &ControlChannelEditDialog::onAssociationChanged);
    connect(ui->load2SensorDeviceCombo, QOverload<const QString&>::of(&QComboBox::currentTextChanged),
            this, &ControlChannelEditDialog::onAssociationChanged);
    connect(ui->positionSensorDeviceCombo, QOverload<const QString&>::of(&QComboBox::currentTextChanged),
            this, &ControlChannelEditDialog::onAssociationChanged);
    connect(ui->controlActuatorDeviceCombo, QOverload<const QString&>::of(&QComboBox::currentTextChanged),
            this, &ControlChannelEditDialog::onAssociationChanged);
}

void ControlChannelEditDialog::initializeComboBoxes() {
    // 初始化极性选项（UI文件中已经有默认选项，这里清空重新添加）
    ui->servoControlPolarityCombo->clear();
    ui->servoControlPolarityCombo->addItem(u8"正极性 (+)", 1);
    ui->servoControlPolarityCombo->addItem(u8"负极性 (-)", -1);
    ui->servoControlPolarityCombo->addItem(u8"双极性 (±)", 9);
    ui->servoControlPolarityCombo->addItem(u8"无极性", 0);
    
    ui->payload1PolarityCombo->clear();
    ui->payload1PolarityCombo->addItem(u8"正极性 (+)", 1);
    ui->payload1PolarityCombo->addItem(u8"负极性 (-)", -1);
    ui->payload1PolarityCombo->addItem(u8"双极性 (±)", 9);
    ui->payload1PolarityCombo->addItem(u8"无极性", 0);
    
    ui->payload2PolarityCombo->clear();
    ui->payload2PolarityCombo->addItem(u8"正极性 (+)", 1);
    ui->payload2PolarityCombo->addItem(u8"负极性 (-)", -1);
    ui->payload2PolarityCombo->addItem(u8"双极性 (±)", 9);
    ui->payload2PolarityCombo->addItem(u8"无极性", 0);
    
    ui->positionPolarityCombo->clear();
    ui->positionPolarityCombo->addItem(u8"正极性 (+)", 1);
    ui->positionPolarityCombo->addItem(u8"负极性 (-)", -1);
    ui->positionPolarityCombo->addItem(u8"双极性 (±)", 9);
    ui->positionPolarityCombo->addItem(u8"无极性", 0);
    
    // 初始化控制模式选项
    ui->controlModeCombo->clear();
    ui->controlModeCombo->addItem(u8"位置控制", 1);
    ui->controlModeCombo->addItem(u8"速度控制", 2);
    ui->controlModeCombo->addItem(u8"力控制", 3);
    ui->controlModeCombo->addItem(u8"混合控制", 4);
    
    // 初始化所有组名下拉框
    loadGroupData();
}

ControlChannelParams ControlChannelEditDialog::getUpdatedParams() const {
    ControlChannelParams params = originalParams_;
    
    // 基本信息
    params.channelName = ui->channelNameEdit->text().trimmed().toStdString();
    params.lc_id = ui->lcIdSpinBox->value();
    params.station_id = ui->stationIdSpinBox->value();
    params.enable = ui->enableCheckBox->isChecked();
    
    // 控制模式
    if (ui->controlModeCombo->currentIndex() >= 0) {
        params.control_mode = ui->controlModeCombo->currentData().toInt();
    }
    
    // 🔧 修改：组合关联配置（从两级下拉框生成完整名称）
    
    // 硬件关联：组合 "组名 - 通道名"
    QString hwGroup = ui->hardwareGroupCombo->currentText().trimmed();
    QString hwChannel = ui->hardwareChannelCombo->currentText().trimmed();
    params.hardwareAssociation = combineAssociationName(hwGroup, hwChannel).toStdString();
    
    // 载荷1传感器：组合 "组名 - 设备名"
    QString load1Group = ui->load1SensorGroupCombo->currentText().trimmed();
    QString load1Device = ui->load1SensorDeviceCombo->currentText().trimmed();
    params.load1Sensor = combineAssociationName(load1Group, load1Device).toStdString();
    
    // 载荷2传感器：组合 "组名 - 设备名"
    QString load2Group = ui->load2SensorGroupCombo->currentText().trimmed();
    QString load2Device = ui->load2SensorDeviceCombo->currentText().trimmed();
    params.load2Sensor = combineAssociationName(load2Group, load2Device).toStdString();
    
    // 位置传感器：组合 "组名 - 设备名"
    QString posGroup = ui->positionSensorGroupCombo->currentText().trimmed();
    QString posDevice = ui->positionSensorDeviceCombo->currentText().trimmed();
    params.positionSensor = combineAssociationName(posGroup, posDevice).toStdString();
    
    // 控制作动器：组合 "组名 - 设备名"
    QString actGroup = ui->controlActuatorGroupCombo->currentText().trimmed();
    QString actDevice = ui->controlActuatorDeviceCombo->currentText().trimmed();
    params.controlActuator = combineAssociationName(actGroup, actDevice).toStdString();
    
    // 极性配置
    params.servo_control_polarity = stringToPolarity(ui->servoControlPolarityCombo->currentText());
    params.payload_sensor1_polarity = stringToPolarity(ui->payload1PolarityCombo->currentText());
    params.payload_sensor2_polarity = stringToPolarity(ui->payload2PolarityCombo->currentText());
    params.position_sensor_polarity = stringToPolarity(ui->positionPolarityCombo->currentText());
    
    // 备注信息
    params.notes = ui->notesEdit->toPlainText().trimmed().toStdString();
    
    return params;
}

void ControlChannelEditDialog::setControlChannelParams(const ControlChannelParams& params) {
    qDebug() << "ControlChannelEditDialog: 开始设置参数，通道ID:" << QString::fromStdString(params.channelId);
    
    try {
        // 🆕 验证UI控件状态
        if (!validateUIControlsForParameterSetting()) {
            qDebug() << "ERROR: UI控件状态验证失败，无法设置参数";
            return;
        }
        
        // 基本信息
        qDebug() << "  设置基本信息...";
        ui->channelNameEdit->setText(QString::fromStdString(params.channelName));
        ui->lcIdSpinBox->setValue(params.lc_id);
        ui->stationIdSpinBox->setValue(params.station_id);
        ui->enableCheckBox->setChecked(params.enable);
        
        // 设置控制模式
        qDebug() << "  设置控制模式:" << params.control_mode;
        bool modeFound = false;
        for (int i = 0; i < ui->controlModeCombo->count(); ++i) {
            if (ui->controlModeCombo->itemData(i).toInt() == params.control_mode) {
                ui->controlModeCombo->setCurrentIndex(i);
                modeFound = true;
                break;
            }
        }
        if (!modeFound) {
            qDebug() << "WARNING: 未找到匹配的控制模式，使用默认值";
            ui->controlModeCombo->setCurrentIndex(0);
        }
        
        // 🆕 确保组数据已加载，然后再加载设备数据
        qDebug() << "  确保组数据已加载...";
        ensureGroupDataLoaded();
        
        // 🔧 修改：加载设备数据并设置当前选择
        qDebug() << "  加载设备数据...";
        loadDeviceDataForCurrentChannels();
        
        // 极性配置
        qDebug() << "  设置极性配置...";
        setComboBoxByValue(ui->servoControlPolarityCombo, params.servo_control_polarity);
        setComboBoxByValue(ui->payload1PolarityCombo, params.payload_sensor1_polarity);
        setComboBoxByValue(ui->payload2PolarityCombo, params.payload_sensor2_polarity);
        setComboBoxByValue(ui->positionPolarityCombo, params.position_sensor_polarity);
        
        // 备注信息
        qDebug() << "  设置备注信息...";
        ui->notesEdit->setPlainText(QString::fromStdString(params.notes));
        
        qDebug() << "ControlChannelEditDialog: 参数设置完成";
        
    } catch (const std::exception& e) {
        qDebug() << "ERROR: 设置参数时发生异常:" << e.what();
        showErrorMessage(QString("参数设置失败: %1").arg(e.what()));
    } catch (...) {
        qDebug() << "ERROR: 设置参数时发生未知异常";
        showErrorMessage("参数设置时发生未知异常");
    }
}

void ControlChannelEditDialog::setHardwareNodes(const QStringList& nodes) {
    hardwareNodes_ = nodes;
    // 🔧 注释：旧的单一下拉框设置，已替换为两级下拉框
    // ui->hardwareCombo->clear();
    // ui->hardwareCombo->addItems(nodes);
}

void ControlChannelEditDialog::setSensorList(const QStringList& sensors) {
    sensorList_ = sensors;
    // 🔧 注释：旧的单一下拉框设置，已替换为两级下拉框
    // ui->load1SensorCombo->clear();
    // ui->load1SensorCombo->addItems(sensors);
    // ui->load2SensorCombo->clear();
    // ui->load2SensorCombo->addItems(sensors);
    // ui->positionSensorCombo->clear();
    // ui->positionSensorCombo->addItems(sensors);
}

void ControlChannelEditDialog::setActuatorList(const QStringList& actuators) {
    actuatorList_ = actuators;
    // 🔧 注释：旧的单一下拉框设置，已替换为两级下拉框
    // ui->controlActuatorCombo->clear();
    // ui->controlActuatorCombo->addItems(actuators);
}

// 🆕 新增：设置硬件组列表
void ControlChannelEditDialog::setHardwareGroups(const QStringList& groups) {
    hardwareGroups_ = groups;
    ui->hardwareGroupCombo->clear();
    ui->hardwareGroupCombo->addItems(groups);
}

// 🆕 新增：设置传感器组列表  
void ControlChannelEditDialog::setSensorGroups(const QStringList& groups) {
    sensorGroups_ = groups;
    ui->load1SensorGroupCombo->clear();
    ui->load1SensorGroupCombo->addItems(groups);
    ui->load2SensorGroupCombo->clear();
    ui->load2SensorGroupCombo->addItems(groups);
    ui->positionSensorGroupCombo->clear();
    ui->positionSensorGroupCombo->addItems(groups);
}

// 🆕 新增：设置作动器组列表
void ControlChannelEditDialog::setActuatorGroups(const QStringList& groups) {
    actuatorGroups_ = groups;
    ui->controlActuatorGroupCombo->clear();
    ui->controlActuatorGroupCombo->addItems(groups);
}

// 🆕 新增：设置组成员映射函数
void ControlChannelEditDialog::setHardwareGroupMembers(const QMap<QString, QStringList>& members) {
    hardwareGroupMembers_ = members;
}

void ControlChannelEditDialog::setSensorGroupMembers(const QMap<QString, QStringList>& members) {
    sensorGroupMembers_ = members;
}

void ControlChannelEditDialog::setActuatorGroupMembers(const QMap<QString, QStringList>& members) {
    actuatorGroupMembers_ = members;
}

void ControlChannelEditDialog::onPolarityChanged() {
    updatePolarityDisplay();
}

void ControlChannelEditDialog::onAssociationChanged() {
    validateInput();
}

void ControlChannelEditDialog::validateInput() {
    // 验证输入的有效性
    bool isValid = true;
    
    // 检查通道名称
    if (ui->channelNameEdit->text().trimmed().isEmpty()) {
        isValid = false;
    }
    
    // 🔧 修改：检查新的两级下拉框关联配置
    // 检查硬件关联（组和通道都必须选择）
    if (ui->hardwareGroupCombo->currentText().trimmed().isEmpty() ||
        ui->hardwareChannelCombo->currentText().trimmed().isEmpty()) {
        isValid = false;
    }
    
    // 更新确定按钮状态
    ui->okButton->setEnabled(isValid);
}

/**
 * @brief 详细校验所有输入字段
 * @return 错误信息列表，如果为空则表示校验通过
 */
QStringList ControlChannelEditDialog::validateAllFields() const {
    QStringList errors;
    
    // 1. 检查通道名称
    if (ui->channelNameEdit->text().trimmed().isEmpty()) {
        errors << u8"• 通道名称不能为空";
    }
    
    // 2. 检查LC ID 范围
    int lcId = ui->lcIdSpinBox->value();
    if (lcId < 0 || lcId > 999999) {
        errors << u8"• LC ID 超出有效范围 (0-999999)";
    }
    
    // 3. 检查Station ID 范围
    int stationId = ui->stationIdSpinBox->value();
    if (stationId < 0 || stationId > 999999) {
        errors << u8"• Station ID 超出有效范围 (0-999999)";
    }
    
    // 4. 检查控制模式
    if (ui->controlModeCombo->currentIndex() < 0) {
        errors << u8"• 请选择控制模式";
    }
    
    // 5. 检查硬件关联配置（必须配置）
    if (ui->hardwareGroupCombo->currentText().trimmed().isEmpty()) {
        errors << u8"• 请选择硬件组";
    }
    if (ui->hardwareChannelCombo->currentText().trimmed().isEmpty()) {
        errors << u8"• 请选择硬件通道";
    }
    
    // 6. 检查传感器配置（至少配置一个传感器）
    bool hasSensor = false;
    
    // 载荷1传感器
    if (!ui->load1SensorGroupCombo->currentText().trimmed().isEmpty() &&
        !ui->load1SensorDeviceCombo->currentText().trimmed().isEmpty()) {
        hasSensor = true;
    }
    
    // 载荷2传感器
    if (!ui->load2SensorGroupCombo->currentText().trimmed().isEmpty() &&
        !ui->load2SensorDeviceCombo->currentText().trimmed().isEmpty()) {
        hasSensor = true;
    }
    
    // 位置传感器
    if (!ui->positionSensorGroupCombo->currentText().trimmed().isEmpty() &&
        !ui->positionSensorDeviceCombo->currentText().trimmed().isEmpty()) {
        hasSensor = true;
    }
    
    if (!hasSensor) {
        errors << u8"• 至少需要配置一个传感器（载荷1、载荷2或位置传感器）";
    }
    
    // 7. 检查控制作动器配置（必须配置）
    if (ui->controlActuatorGroupCombo->currentText().trimmed().isEmpty()) {
        errors << u8"• 请选择控制作动器组";
    }
    if (ui->controlActuatorDeviceCombo->currentText().trimmed().isEmpty()) {
        errors << u8"• 请选择控制作动器设备";
    }
    
    // 8. 检查不完整的传感器配置
    // 载荷1传感器：如果选择了组但没选择设备，或反之
    if ((ui->load1SensorGroupCombo->currentText().trimmed().isEmpty()) != 
        (ui->load1SensorDeviceCombo->currentText().trimmed().isEmpty())) {
        errors << u8"• 载荷1传感器配置不完整：请同时选择组和设备，或者都不选择";
    }
    
    // 载荷2传感器：如果选择了组但没选择设备，或反之
    if ((ui->load2SensorGroupCombo->currentText().trimmed().isEmpty()) != 
        (ui->load2SensorDeviceCombo->currentText().trimmed().isEmpty())) {
        errors << u8"• 载荷2传感器配置不完整：请同时选择组和设备，或者都不选择";
    }
    
    // 位置传感器：如果选择了组但没选择设备，或反之
    if ((ui->positionSensorGroupCombo->currentText().trimmed().isEmpty()) != 
        (ui->positionSensorDeviceCombo->currentText().trimmed().isEmpty())) {
        errors << u8"• 位置传感器配置不完整：请同时选择组和设备，或者都不选择";
    }
    
    return errors;
}

/**
 * @brief 详细校验所有输入字段，并返回第一个有错误的控件
 * @param firstErrorWidget 输出参数，第一个有错误的控件
 * @return 错误信息列表，如果为空则表示校验通过
 */
QStringList ControlChannelEditDialog::validateAllFieldsWithFocus(QWidget*& firstErrorWidget) const {
    QStringList errors;
    firstErrorWidget = nullptr;
    
    // 1. 检查通道名称
    if (ui->channelNameEdit->text().trimmed().isEmpty()) {
        errors << u8"• 通道名称不能为空";
        if (!firstErrorWidget) firstErrorWidget = ui->channelNameEdit;
    }
    
    // 2. 检查LC ID 范围
    int lcId = ui->lcIdSpinBox->value();
    if (lcId < 0 || lcId > 999999) {
        errors << u8"• LC ID 超出有效范围 (0-999999)";
        if (!firstErrorWidget) firstErrorWidget = ui->lcIdSpinBox;
    }
    
    // 3. 检查Station ID 范围
    int stationId = ui->stationIdSpinBox->value();
    if (stationId < 0 || stationId > 999999) {
        errors << u8"• Station ID 超出有效范围 (0-999999)";
        if (!firstErrorWidget) firstErrorWidget = ui->stationIdSpinBox;
    }
    
    // 4. 检查控制模式
    if (ui->controlModeCombo->currentIndex() < 0) {
        errors << u8"• 请选择控制模式";
        if (!firstErrorWidget) firstErrorWidget = ui->controlModeCombo;
    }
    
    // 5. 检查硬件关联配置（必须配置）
    if (ui->hardwareGroupCombo->currentText().trimmed().isEmpty()) {
        errors << u8"• 请选择硬件组";
        if (!firstErrorWidget) firstErrorWidget = ui->hardwareGroupCombo;
    }
    if (ui->hardwareChannelCombo->currentText().trimmed().isEmpty()) {
        errors << u8"• 请选择硬件通道";
        if (!firstErrorWidget) firstErrorWidget = ui->hardwareChannelCombo;
    }
    
    // 6. 检查传感器配置（至少配置一个传感器）
    bool hasSensor = false;
    
    // 载荷1传感器
    if (!ui->load1SensorGroupCombo->currentText().trimmed().isEmpty() &&
        !ui->load1SensorDeviceCombo->currentText().trimmed().isEmpty()) {
        hasSensor = true;
    }
    
    // 载荷2传感器
    if (!ui->load2SensorGroupCombo->currentText().trimmed().isEmpty() &&
        !ui->load2SensorDeviceCombo->currentText().trimmed().isEmpty()) {
        hasSensor = true;
    }
    
    // 位置传感器
    if (!ui->positionSensorGroupCombo->currentText().trimmed().isEmpty() &&
        !ui->positionSensorDeviceCombo->currentText().trimmed().isEmpty()) {
        hasSensor = true;
    }
    
    if (!hasSensor) {
        errors << u8"• 至少需要配置一个传感器（载荷1、载荷2或位置传感器）";
        if (!firstErrorWidget) firstErrorWidget = ui->load1SensorGroupCombo;
    }
    
    // 7. 检查控制作动器配置（必须配置）
    if (ui->controlActuatorGroupCombo->currentText().trimmed().isEmpty()) {
        errors << u8"• 请选择控制作动器组";
        if (!firstErrorWidget) firstErrorWidget = ui->controlActuatorGroupCombo;
    }
    if (ui->controlActuatorDeviceCombo->currentText().trimmed().isEmpty()) {
        errors << u8"• 请选择控制作动器设备";
        if (!firstErrorWidget) firstErrorWidget = ui->controlActuatorDeviceCombo;
    }
    
    // 8. 检查不完整的传感器配置
    // 载荷1传感器：如果选择了组但没选择设备，或反之
    if ((ui->load1SensorGroupCombo->currentText().trimmed().isEmpty()) != 
        (ui->load1SensorDeviceCombo->currentText().trimmed().isEmpty())) {
        errors << u8"• 载荷1传感器配置不完整：请同时选择组和设备，或者都不选择";
        if (!firstErrorWidget) {
            firstErrorWidget = ui->load1SensorGroupCombo->currentText().trimmed().isEmpty() ? 
                              ui->load1SensorGroupCombo : ui->load1SensorDeviceCombo;
        }
    }
    
    // 载荷2传感器：如果选择了组但没选择设备，或反之
    if ((ui->load2SensorGroupCombo->currentText().trimmed().isEmpty()) != 
        (ui->load2SensorDeviceCombo->currentText().trimmed().isEmpty())) {
        errors << u8"• 载荷2传感器配置不完整：请同时选择组和设备，或者都不选择";
        if (!firstErrorWidget) {
            firstErrorWidget = ui->load2SensorGroupCombo->currentText().trimmed().isEmpty() ? 
                              ui->load2SensorGroupCombo : ui->load2SensorDeviceCombo;
        }
    }
    
    // 位置传感器：如果选择了组但没选择设备，或反之
    if ((ui->positionSensorGroupCombo->currentText().trimmed().isEmpty()) != 
        (ui->positionSensorDeviceCombo->currentText().trimmed().isEmpty())) {
        errors << u8"• 位置传感器配置不完整：请同时选择组和设备，或者都不选择";
        if (!firstErrorWidget) {
            firstErrorWidget = ui->positionSensorGroupCombo->currentText().trimmed().isEmpty() ? 
                              ui->positionSensorGroupCombo : ui->positionSensorDeviceCombo;
        }
    }
    
    return errors;
}

void ControlChannelEditDialog::loadGroupData() {
    // 🆕 使用真实数据替代模拟数据
    loadRealHardwareGroupData();
    loadRealSensorGroupData();
    loadRealActuatorGroupData();
    
    // 为硬件组下拉框添加选项
    ui->hardwareGroupCombo->clear();
    ui->hardwareGroupCombo->addItem(""); // 添加空选项
    ui->hardwareGroupCombo->addItems(hardwareGroups_);
    
    // 为传感器组下拉框添加选项
    QList<QComboBox*> sensorCombos = {
        ui->load1SensorGroupCombo,
        ui->load2SensorGroupCombo,
        ui->positionSensorGroupCombo
    };
    
    for (QComboBox* combo : sensorCombos) {
        combo->clear();
        combo->addItem(""); // 添加空选项
        combo->addItems(sensorGroups_);
    }
    
    // 为作动器组下拉框添加选项
    ui->controlActuatorGroupCombo->clear();
    ui->controlActuatorGroupCombo->addItem(""); // 添加空选项
    ui->controlActuatorGroupCombo->addItems(actuatorGroups_);
}

void ControlChannelEditDialog::loadDeviceDataForCurrentChannels() {
    qDebug() << "ControlChannelEditDialog: 开始加载设备数据";
    
    try {
        // 🆕 验证组下拉框是否有数据
        if (ui->hardwareGroupCombo->count() == 0 || ui->load1SensorGroupCombo->count() == 0 ||
            ui->controlActuatorGroupCombo->count() == 0) {
            qDebug() << "WARNING: 组下拉框数据不完整，尝试重新加载组数据";
            loadGroupData();
        }
        
        // 硬件关联
        qDebug() << "  处理硬件关联...";
        QString hwAssociation = QString::fromStdString(originalParams_.hardwareAssociation);
        QString hwGroup = extractGroupFromAssociation(hwAssociation);
        QString hwDevice = extractDeviceFromAssociation(hwAssociation);
        
        qDebug() << "    原始数据:" << hwAssociation;
        qDebug() << "    提取的组:" << hwGroup;
        qDebug() << "    提取的设备:" << hwDevice;
        
        if (!hwGroup.isEmpty()) {
            if (!setComboBoxText(ui->hardwareGroupCombo, hwGroup)) {
                qDebug() << "WARNING: 未找到硬件组'" << hwGroup << "'，可用选项:" << getComboBoxItems(ui->hardwareGroupCombo);
            }
            loadDevicesForGroup(hwGroup, ui->hardwareChannelCombo);
            
            if (!hwDevice.isEmpty()) {
                if (!setComboBoxText(ui->hardwareChannelCombo, hwDevice)) {
                    qDebug() << "WARNING: 未找到硬件设备'" << hwDevice << "'，可用选项:" << getComboBoxItems(ui->hardwareChannelCombo);
                }
            }
        }
        
        // 载荷1传感器
        qDebug() << "  处理载荷1传感器...";
        QString load1Association = QString::fromStdString(originalParams_.load1Sensor);
        QString load1Group = extractGroupFromAssociation(load1Association);
        QString load1Device = extractDeviceFromAssociation(load1Association);
        
        qDebug() << "    原始数据:" << load1Association;
        qDebug() << "    提取的组:" << load1Group;
        qDebug() << "    提取的设备:" << load1Device;
        
        if (!load1Group.isEmpty()) {
            if (!setComboBoxText(ui->load1SensorGroupCombo, load1Group)) {
                qDebug() << "WARNING: 未找到传感器组'" << load1Group << "'，可用选项:" << getComboBoxItems(ui->load1SensorGroupCombo);
            }
            loadDevicesForGroup(load1Group, ui->load1SensorDeviceCombo);
            
            if (!load1Device.isEmpty()) {
                if (!setComboBoxText(ui->load1SensorDeviceCombo, load1Device)) {
                    qDebug() << "WARNING: 未找到载荷1传感器设备'" << load1Device << "'，可用选项:" << getComboBoxItems(ui->load1SensorDeviceCombo);
                }
            }
        }
        
        // 载荷2传感器
        qDebug() << "  处理载荷2传感器...";
        QString load2Association = QString::fromStdString(originalParams_.load2Sensor);
        QString load2Group = extractGroupFromAssociation(load2Association);
        QString load2Device = extractDeviceFromAssociation(load2Association);
        
        qDebug() << "    原始数据:" << load2Association;
        qDebug() << "    提取的组:" << load2Group;
        qDebug() << "    提取的设备:" << load2Device;
        
        if (!load2Group.isEmpty()) {
            if (!setComboBoxText(ui->load2SensorGroupCombo, load2Group)) {
                qDebug() << "WARNING: 未找到传感器组'" << load2Group << "'，可用选项:" << getComboBoxItems(ui->load2SensorGroupCombo);
            }
            loadDevicesForGroup(load2Group, ui->load2SensorDeviceCombo);
            
            if (!load2Device.isEmpty()) {
                if (!setComboBoxText(ui->load2SensorDeviceCombo, load2Device)) {
                    qDebug() << "WARNING: 未找到载荷2传感器设备'" << load2Device << "'，可用选项:" << getComboBoxItems(ui->load2SensorDeviceCombo);
                }
            }
        }
        
        // 位置传感器
        qDebug() << "  处理位置传感器...";
        QString posAssociation = QString::fromStdString(originalParams_.positionSensor);
        QString posGroup = extractGroupFromAssociation(posAssociation);
        QString posDevice = extractDeviceFromAssociation(posAssociation);
        
        qDebug() << "    原始数据:" << posAssociation;
        qDebug() << "    提取的组:" << posGroup;
        qDebug() << "    提取的设备:" << posDevice;
        
        if (!posGroup.isEmpty()) {
            if (!setComboBoxText(ui->positionSensorGroupCombo, posGroup)) {
                qDebug() << "WARNING: 未找到传感器组'" << posGroup << "'，可用选项:" << getComboBoxItems(ui->positionSensorGroupCombo);
            }
            loadDevicesForGroup(posGroup, ui->positionSensorDeviceCombo);
            
            if (!posDevice.isEmpty()) {
                if (!setComboBoxText(ui->positionSensorDeviceCombo, posDevice)) {
                    qDebug() << "WARNING: 未找到位置传感器设备'" << posDevice << "'，可用选项:" << getComboBoxItems(ui->positionSensorDeviceCombo);
                }
            }
        }
        
        // 控制作动器
        qDebug() << "  处理控制作动器...";
        QString actAssociation = QString::fromStdString(originalParams_.controlActuator);
        QString actGroup = extractGroupFromAssociation(actAssociation);
        QString actDevice = extractDeviceFromAssociation(actAssociation);
        
        qDebug() << "    原始数据:" << actAssociation;
        qDebug() << "    提取的组:" << actGroup;
        qDebug() << "    提取的设备:" << actDevice;
        
        if (!actGroup.isEmpty()) {
            if (!setComboBoxText(ui->controlActuatorGroupCombo, actGroup)) {
                qDebug() << "WARNING: 未找到作动器组'" << actGroup << "'，可用选项:" << getComboBoxItems(ui->controlActuatorGroupCombo);
            }
            loadDevicesForGroup(actGroup, ui->controlActuatorDeviceCombo);
            
            if (!actDevice.isEmpty()) {
                if (!setComboBoxText(ui->controlActuatorDeviceCombo, actDevice)) {
                    qDebug() << "WARNING: 未找到控制作动器设备'" << actDevice << "'，可用选项:" << getComboBoxItems(ui->controlActuatorDeviceCombo);
                }
            }
        }
        
        qDebug() << "ControlChannelEditDialog: 设备数据加载完成";
        
    } catch (const std::exception& e) {
        qDebug() << "ERROR: 加载设备数据时发生异常:" << e.what();
    } catch (...) {
        qDebug() << "ERROR: 加载设备数据时发生未知异常";
    }
}

void ControlChannelEditDialog::loadDevicesForGroup(const QString& groupName, QComboBox* deviceCombo) {
    qDebug() << "ControlChannelEditDialog::loadDevicesForGroup: 开始为组'" << groupName << "'加载设备";
    
    if (!deviceCombo) {
        qDebug() << "ERROR: deviceCombo为空指针";
        return;
    }
    
    if (groupName.trimmed().isEmpty()) {
        qDebug() << "WARNING: 组名为空，清空设备下拉框";
        deviceCombo->clear();
        deviceCombo->addItem(""); // 只保留空选项
        return;
    }
    
    try {
        // 🆕 获取指定组的真实设备列表
        QStringList devices = getRealDevicesForGroup(groupName);
        
        qDebug() << "  为组'" << groupName << "'找到设备数量:" << devices.size();
        if (!devices.isEmpty()) {
            qDebug() << "  设备列表:";
            for (int i = 0; i < devices.size(); ++i) {
                qDebug() << "    [" << i << "]" << devices[i];
            }
        } else {
            qDebug() << "  WARNING: 组'" << groupName << "'没有找到任何设备";
        }
        
        // 保存当前选择（如果有的话）
        QString currentSelection = deviceCombo->currentText();
        qDebug() << "  当前选择:" << currentSelection;
        
        // 更新设备下拉框
        deviceCombo->clear();
        deviceCombo->addItem(""); // 添加空选项
        deviceCombo->addItems(devices);
        
        qDebug() << "  下拉框更新完成，总选项数:" << deviceCombo->count();
        
        // 尝试恢复之前的选择
        if (!currentSelection.isEmpty()) {
            if (setComboBoxText(deviceCombo, currentSelection)) {
                qDebug() << "  成功恢复之前的选择:" << currentSelection;
            } else {
                qDebug() << "  无法恢复之前的选择:" << currentSelection;
            }
        }
        
    } catch (const std::exception& e) {
        qDebug() << "ERROR: 为组'" << groupName << "'加载设备时发生异常:" << e.what();
        // 确保下拉框至少有空选项
        deviceCombo->clear();
        deviceCombo->addItem("");
    } catch (...) {
        qDebug() << "ERROR: 为组'" << groupName << "'加载设备时发生未知异常";
        // 确保下拉框至少有空选项
        deviceCombo->clear();
        deviceCombo->addItem("");
    }
}

QStringList ControlChannelEditDialog::getAvailableGroups() const {
    // 模拟数据，实际应用中应该从配置管理器或数据库获取
    QStringList groups;
    groups << "硬件组1" << "硬件组2" << "硬件组3"
           << "传感器组A" << "传感器组B" << "传感器组C"
           << "作动器组X" << "作动器组Y" << "作动器组Z";
    
    return groups;
}

QStringList ControlChannelEditDialog::getDevicesForGroup(const QString& groupName) const {
    // 模拟数据，实际应用中应该从配置管理器或数据库获取
    QStringList devices;
    
    if (groupName.contains("硬件组")) {
        devices << "通道1" << "通道2" << "通道3" << "通道4";
    }
    else if (groupName.contains("传感器组")) {
        devices << "传感器设备1" << "传感器设备2" << "传感器设备3";
    }
    else if (groupName.contains("作动器组")) {
        devices << "作动器设备1" << "作动器设备2" << "作动器设备3";
    }
    else {
        // 默认设备列表
        devices << "设备1" << "设备2" << "设备3";
    }
    
    return devices;
}

QString ControlChannelEditDialog::extractGroupFromAssociation(const QString& association) const {
    qDebug() << "ControlChannelEditDialog: 提取组名，原始字符串:" << association;
    
    if (association.trimmed().isEmpty()) {
        qDebug() << "  -> 空字符串，返回空";
        return QString();
    }
    
    QString trimmedAssociation = association.trimmed();
    
    // 🆕 支持多种分隔符格式
    QStringList separators = {" - ", " -", "- ", "-", " | ", "|", "::", ":"};
    
    for (const QString& separator : separators) {
        if (trimmedAssociation.contains(separator)) {
            QStringList parts = trimmedAssociation.split(separator, Qt::SkipEmptyParts);
            if (parts.size() >= 1) {
                QString groupName = parts[0].trimmed();
                qDebug() << "  -> 使用分隔符'" << separator << "'，提取到组名:" << groupName;
                return groupName;
            }
        }
    }
    
    // 🆕 如果没有找到分隔符，检查是否整个字符串就是组名
    qDebug() << "  -> 未找到分隔符，将整个字符串作为组名:" << trimmedAssociation;
    return trimmedAssociation;
}

QString ControlChannelEditDialog::extractDeviceFromAssociation(const QString& association) const {
    qDebug() << "ControlChannelEditDialog: 提取设备名，原始字符串:" << association;
    
    if (association.trimmed().isEmpty()) {
        qDebug() << "  -> 空字符串，返回空";
        return QString();
    }
    
    QString trimmedAssociation = association.trimmed();
    
    // 🆕 支持多种分隔符格式
    QStringList separators = {" - ", " -", "- ", "-", " | ", "|", "::", ":"};
    
    for (const QString& separator : separators) {
        if (trimmedAssociation.contains(separator)) {
            QStringList parts = trimmedAssociation.split(separator, Qt::SkipEmptyParts);
            if (parts.size() >= 2) {
                QString deviceName = parts[1].trimmed();
                qDebug() << "  -> 使用分隔符'" << separator << "'，提取到设备名:" << deviceName;
                return deviceName;
            }
        }
    }
    
    // 🆕 如果没有找到分隔符或只有一部分，返回空（表示没有设备名）
    qDebug() << "  -> 未找到有效的设备名部分，返回空";
    return QString();
}

void ControlChannelEditDialog::onAcceptClicked() {
    // 详细校验所有字段，并获取第一个需要修正的控件
    QWidget* firstErrorWidget = nullptr;
    QStringList errors = validateAllFieldsWithFocus(firstErrorWidget);
    
    if (!errors.isEmpty()) {
        QString errorMessage = u8"配置信息验证失败，请修正以下问题：\n\n";
        errorMessage += errors.join("\n");
        
        QMessageBox::warning(this, u8"配置验证失败", errorMessage);
        
        // 将焦点切换到第一个有问题的控件
        if (firstErrorWidget) {
            // 🆕 切换到控件所在的标签页
            switchToWidgetTab(firstErrorWidget);
            
            firstErrorWidget->setFocus();
            
            // 如果是下拉框，则展开显示选项
            if (QComboBox* combo = qobject_cast<QComboBox*>(firstErrorWidget)) {
                combo->showPopup();
            }
            // 如果是输入框，则选中所有文本
            else if (QLineEdit* lineEdit = qobject_cast<QLineEdit*>(firstErrorWidget)) {
                lineEdit->selectAll();
            }
            // 如果是数字输入框，则选中所有文本
            else if (QSpinBox* spinBox = qobject_cast<QSpinBox*>(firstErrorWidget)) {
                spinBox->selectAll();
            }
        }
        
        return; // 不关闭对话框
    }
    
    // 校验通过，关闭对话框
    accept();
}

void ControlChannelEditDialog::updatePolarityDisplay() {
    // 更新极性显示相关的UI
    // 这里可以添加一些极性变更后的UI更新逻辑
}

QString ControlChannelEditDialog::polarityToString(int polarity) const {
    // 极性存储必须使用数字格式，不再使用文本描述
    return QString::number(polarity);
}

int ControlChannelEditDialog::stringToPolarity(const QString& str) const {
    if (str.contains("正极性") || str.contains("+")) {
        return 1;
    } else if (str.contains("负极性") || str.contains("-")) {
        return -1;
    } else if (str.contains("双极性") || str.contains("±")) {
        return 9;
    } else if (str.contains("无极性")) {
        return 0;
    }
    return 0; // 默认返回无极性
}

void ControlChannelEditDialog::setComboBoxByValue(QComboBox* combo, int value) {
    for (int i = 0; i < combo->count(); ++i) {
        if (combo->itemData(i).toInt() == value) {
            combo->setCurrentIndex(i);
            break;
        }
    }
}

// 🆕 新增：槽函数实现

void ControlChannelEditDialog::onHardwareGroupChanged() {
    QString selectedGroup = ui->hardwareGroupCombo->currentText();
    loadDevicesForGroup(selectedGroup, ui->hardwareChannelCombo);
}

void ControlChannelEditDialog::onLoad1SensorGroupChanged() {
    QString selectedGroup = ui->load1SensorGroupCombo->currentText();
    loadDevicesForGroup(selectedGroup, ui->load1SensorDeviceCombo);
}

void ControlChannelEditDialog::onLoad2SensorGroupChanged() {
    QString selectedGroup = ui->load2SensorGroupCombo->currentText();
    loadDevicesForGroup(selectedGroup, ui->load2SensorDeviceCombo);
}

void ControlChannelEditDialog::onPositionSensorGroupChanged() {
    QString selectedGroup = ui->positionSensorGroupCombo->currentText();
    loadDevicesForGroup(selectedGroup, ui->positionSensorDeviceCombo);
}

void ControlChannelEditDialog::onControlActuatorGroupChanged() {
    QString selectedGroup = ui->controlActuatorGroupCombo->currentText();
    loadDevicesForGroup(selectedGroup, ui->controlActuatorDeviceCombo);
}

// 🆕 新增：辅助函数实现

void ControlChannelEditDialog::updateHardwareChannelCombo(const QString& groupName) {
    ui->hardwareChannelCombo->clear();
    if (!groupName.isEmpty() && hardwareGroupMembers_.contains(groupName)) {
        ui->hardwareChannelCombo->addItems(hardwareGroupMembers_[groupName]);
    }
}

void ControlChannelEditDialog::updateSensorDeviceCombo(const QString& groupName, QComboBox* deviceCombo) {
    if (!deviceCombo) return;
    
    deviceCombo->clear();
    if (!groupName.isEmpty() && sensorGroupMembers_.contains(groupName)) {
        deviceCombo->addItems(sensorGroupMembers_[groupName]);
    }
}

void ControlChannelEditDialog::updateActuatorDeviceCombo(const QString& groupName) {
    ui->controlActuatorDeviceCombo->clear();
    if (!groupName.isEmpty() && actuatorGroupMembers_.contains(groupName)) {
        ui->controlActuatorDeviceCombo->addItems(actuatorGroupMembers_[groupName]);
    }
}

QPair<QString, QString> ControlChannelEditDialog::parseAssociationName(const QString& fullName) const {
    // 解析 "组名 - 设备名" 格式
    QStringList parts = fullName.split(" - ");
    if (parts.size() >= 2) {
        return qMakePair(parts[0].trimmed(), parts[1].trimmed());
    }
    return qMakePair(QString(), QString());
}

QString ControlChannelEditDialog::combineAssociationName(const QString& groupName, const QString& deviceName) const {
    // 组合为 "组名 - 设备名" 格式
    if (groupName.isEmpty() || deviceName.isEmpty()) {
        return QString();
    }
    return QString("%1 - %2").arg(groupName).arg(deviceName);
}

// 🆕 新增方法实现

int ControlChannelEditDialog::getTabIndexForWidget(QWidget* widget) const {
    if (!widget || !ui->tabWidget) {
        return -1;
    }
    
    // 遍历每个标签页，检查控件是否在其中
    for (int i = 0; i < ui->tabWidget->count(); ++i) {
        QWidget* tabPage = ui->tabWidget->widget(i);
        if (!tabPage) continue;
        
        // 检查控件是否是该标签页的子控件
        QWidget* parent = widget;
        while (parent) {
            if (parent == tabPage) {
                return i;
            }
            parent = parent->parentWidget();
        }
    }
    
    return -1;
}

void ControlChannelEditDialog::switchToWidgetTab(QWidget* widget) {
    if (!widget || !ui->tabWidget) {
        return;
    }
    
    int tabIndex = getTabIndexForWidget(widget);
    if (tabIndex >= 0) {
        ui->tabWidget->setCurrentIndex(tabIndex);
    }
}

void ControlChannelEditDialog::loadRealHardwareGroupData() {
    hardwareGroups_.clear();
    hardwareGroupMembers_.clear();
    
    if (mainWindow_) {
        try {
            auto result = mainWindow_->getHardwareGroupsAndMembers();
            hardwareGroups_ = result.first;
            hardwareGroupMembers_ = result.second;
            
            qDebug() << "ControlChannelEditDialog: 加载硬件组数据成功, 组数量:" << hardwareGroups_.size();
            for (const QString& group : hardwareGroups_) {
                qDebug() << "  - 硬件组:" << group << ", 成员数量:" << hardwareGroupMembers_[group].size();
            }
        } catch (...) {
            qDebug() << "ControlChannelEditDialog: 加载硬件组数据时发生异常";
        }
    } else {
        qDebug() << "ControlChannelEditDialog: MainWindow指针为空，无法加载硬件组数据";
    }
}

void ControlChannelEditDialog::loadRealSensorGroupData() {
    sensorGroups_.clear();
    sensorGroupMembers_.clear();
    
    if (mainWindow_) {
        try {
            auto result = mainWindow_->getSensorGroupsAndMembers();
            sensorGroups_ = result.first;
            sensorGroupMembers_ = result.second;
            
            qDebug() << "ControlChannelEditDialog: 加载传感器组数据成功, 组数量:" << sensorGroups_.size();
            for (const QString& group : sensorGroups_) {
                qDebug() << "  - 传感器组:" << group << ", 成员数量:" << sensorGroupMembers_[group].size();
            }
        } catch (...) {
            qDebug() << "ControlChannelEditDialog: 加载传感器组数据时发生异常";
        }
    } else {
        qDebug() << "ControlChannelEditDialog: MainWindow指针为空，无法加载传感器组数据";
    }
}

void ControlChannelEditDialog::loadRealActuatorGroupData() {
    actuatorGroups_.clear();
    actuatorGroupMembers_.clear();
    
    if (mainWindow_) {
        try {
            auto result = mainWindow_->getActuatorGroupsAndMembers();
            actuatorGroups_ = result.first;
            actuatorGroupMembers_ = result.second;
            
            qDebug() << "ControlChannelEditDialog: 加载作动器组数据成功, 组数量:" << actuatorGroups_.size();
            for (const QString& group : actuatorGroups_) {
                qDebug() << "  - 作动器组:" << group << ", 成员数量:" << actuatorGroupMembers_[group].size();
            }
        } catch (...) {
            qDebug() << "ControlChannelEditDialog: 加载作动器组数据时发生异常";
        }
    } else {
        qDebug() << "ControlChannelEditDialog: MainWindow指针为空，无法加载作动器组数据";
    }
}

QStringList ControlChannelEditDialog::getRealDevicesForGroup(const QString& groupName) const {
    if (groupName.trimmed().isEmpty()) {
        return QStringList();
    }
    
    // 根据组名判断类型，返回对应的设备列表
    if (hardwareGroupMembers_.contains(groupName)) {
        return hardwareGroupMembers_[groupName];
    }
    else if (sensorGroupMembers_.contains(groupName)) {
        return sensorGroupMembers_[groupName];
    }
    else if (actuatorGroupMembers_.contains(groupName)) {
        return actuatorGroupMembers_[groupName];
    }
    
    return QStringList();
}

// 🆕 新增：UI控件验证函数
bool ControlChannelEditDialog::validateUIControls() {
    qDebug() << "ControlChannelEditDialog: 开始UI控件验证";
    
    // 检查主要UI控件是否存在
    QStringList missingControls;
    
    if (!ui->channelNameEdit) missingControls << "channelNameEdit";
    if (!ui->lcIdSpinBox) missingControls << "lcIdSpinBox";
    if (!ui->stationIdSpinBox) missingControls << "stationIdSpinBox";
    if (!ui->enableCheckBox) missingControls << "enableCheckBox";
    if (!ui->controlModeCombo) missingControls << "controlModeCombo";
    if (!ui->notesEdit) missingControls << "notesEdit";
    if (!ui->okButton) missingControls << "okButton";
    if (!ui->tabWidget) missingControls << "tabWidget";

    // 检查两级下拉框组合
    if (!ui->hardwareGroupCombo) missingControls << "hardwareGroupCombo";
    if (!ui->hardwareChannelCombo) missingControls << "hardwareChannelCombo";
    if (!ui->load1SensorGroupCombo) missingControls << "load1SensorGroupCombo";
    if (!ui->load1SensorDeviceCombo) missingControls << "load1SensorDeviceCombo";
    if (!ui->load2SensorGroupCombo) missingControls << "load2SensorGroupCombo";
    if (!ui->load2SensorDeviceCombo) missingControls << "load2SensorDeviceCombo";
    if (!ui->positionSensorGroupCombo) missingControls << "positionSensorGroupCombo";
    if (!ui->positionSensorDeviceCombo) missingControls << "positionSensorDeviceCombo";
    if (!ui->controlActuatorGroupCombo) missingControls << "controlActuatorGroupCombo";
    if (!ui->controlActuatorDeviceCombo) missingControls << "controlActuatorDeviceCombo";

    // 检查极性下拉框
    if (!ui->servoControlPolarityCombo) missingControls << "servoControlPolarityCombo";
    if (!ui->payload1PolarityCombo) missingControls << "payload1PolarityCombo";
    if (!ui->payload2PolarityCombo) missingControls << "payload2PolarityCombo";
    if (!ui->positionPolarityCombo) missingControls << "positionPolarityCombo";

    if (!missingControls.isEmpty()) {
        qDebug() << "ERROR: UI控件验证失败，缺少控件:" << missingControls.join(", ");
        return false;
    }

    qDebug() << "ControlChannelEditDialog: UI控件验证通过，所有必需控件都存在";
    return true;
}

void ControlChannelEditDialog::showErrorMessage(const QString& message) {
    QMessageBox::critical(this, u8"错误", message);
}

// 🆕 新增：参数设置时的UI控件验证
bool ControlChannelEditDialog::validateUIControlsForParameterSetting() {
    qDebug() << "ControlChannelEditDialog: 验证UI控件状态以进行参数设置";
    
    // 检查下拉框是否已初始化（是否有选项）
    if (ui->controlModeCombo->count() == 0) {
        qDebug() << "ERROR: 控制模式下拉框未初始化";
        return false;
    }
    
    if (ui->servoControlPolarityCombo->count() == 0 ||
        ui->payload1PolarityCombo->count() == 0 ||
        ui->payload2PolarityCombo->count() == 0 ||
        ui->positionPolarityCombo->count() == 0) {
        qDebug() << "ERROR: 极性下拉框未初始化";
        return false;
    }
    
    qDebug() << "ControlChannelEditDialog: UI控件状态验证通过";
    return true;
}

// 🆕 新增：确保组数据已加载
void ControlChannelEditDialog::ensureGroupDataLoaded() {
    qDebug() << "ControlChannelEditDialog: 确保组数据已加载";
    
    // 检查硬件组数据
    if (hardwareGroups_.isEmpty()) {
        qDebug() << "  硬件组数据为空，重新加载...";
        loadRealHardwareGroupData();
        if (hardwareGroups_.isEmpty()) {
            qDebug() << "WARNING: 硬件组数据仍为空，使用默认数据";
            hardwareGroups_ << "默认硬件组";
        }
    }
    
    // 检查传感器组数据
    if (sensorGroups_.isEmpty()) {
        qDebug() << "  传感器组数据为空，重新加载...";
        loadRealSensorGroupData();
        if (sensorGroups_.isEmpty()) {
            qDebug() << "WARNING: 传感器组数据仍为空，使用默认数据";
            sensorGroups_ << "默认传感器组";
        }
    }
    
    // 检查作动器组数据
    if (actuatorGroups_.isEmpty()) {
        qDebug() << "  作动器组数据为空，重新加载...";
        loadRealActuatorGroupData();
        if (actuatorGroups_.isEmpty()) {
            qDebug() << "WARNING: 作动器组数据仍为空，使用默认数据";
            actuatorGroups_ << "默认作动器组";
        }
    }
    
    qDebug() << "ControlChannelEditDialog: 组数据确保完成";
    qDebug() << "  硬件组数量:" << hardwareGroups_.size();
    qDebug() << "  传感器组数量:" << sensorGroups_.size();
    qDebug() << "  作动器组数量:" << actuatorGroups_.size();
}

// 🆕 新增：安全设置下拉框文本
bool ControlChannelEditDialog::setComboBoxText(QComboBox* combo, const QString& text) {
    if (!combo || text.trimmed().isEmpty()) {
        return false;
    }
    
    // 尝试精确匹配
    int index = combo->findText(text, Qt::MatchExactly);
    if (index >= 0) {
        combo->setCurrentIndex(index);
        return true;
    }
    
    // 尝试包含匹配
    index = combo->findText(text, Qt::MatchContains);
    if (index >= 0) {
        combo->setCurrentIndex(index);
        qDebug() << "    使用包含匹配设置下拉框，目标文本:" << text << "，匹配到:" << combo->itemText(index);
        return true;
    }
    
    return false;
}

// 🆕 新增：获取下拉框所有选项
QStringList ControlChannelEditDialog::getComboBoxItems(QComboBox* combo) {
    QStringList items;
    if (combo) {
        for (int i = 0; i < combo->count(); ++i) {
            items << combo->itemText(i);
        }
    }
    return items;
}

} // namespace UI 