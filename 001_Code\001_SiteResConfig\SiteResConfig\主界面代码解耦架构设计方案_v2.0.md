﻿#  **主界面代码解耦架构设计方案 v2.0**

##  **项目现状分析**

### **当前问题**
- **主界面文件**: MainWindow_Qt_Simple.cpp (10,903行)
- **方法数量**: 约100+个方法
- **功能模块**: 15+个不同功能域
- **依赖关系**: 高度耦合，难以维护
- **UI组件**: 6个标准对话框 + 2个自定义树控件 + 多个信息面板

### **现有格式支持**
-  **Excel格式**: 支持导入导出 (.xlsx)
-  **JSON格式**: 支持导入导出 (.json)

### **现有UI组件清单**
- **标准对话框**: SensorDialog, ActuatorDialog, HardwareConfigDialog, PIDParametersDialog, ControlModeDialog, NodeConfigDialog
- **自定义控件**: CustomHardwareTreeWidget, CustomTestConfigTreeWidget
- **信息面板**: BasicInfoWidget, DetailInfoPanel
- **交互处理器**: TreeInteractionHandler

##  **解耦架构设计 v2.0**

### **1. 分层架构设计**

`

                    Presentation Layer                       
               
   MainWindow    TreeManager   MenuManager            
   (简化版)                                            
               
               
  DialogManager UIManager     StyleManager            
                                                      
               
─
─
                    Business Logic Layer                     
    ─           
  ProjectMgr    DeviceMgr    │ ImportExport            
                              Manager                 
  ─             
    ─           
  ConfigMgr     ValidationMgr EventMgr                
                                                      
  ─             
─
─
                      Data Layer                             
               
  SensorMgr     ActuatorMgr   HardwareMgr             
                                                      
               
─
`

##  **模块化分解方案 v2.0**

### **A. 项目管理模块 (ProjectManager)**

`cpp
//  include/ProjectManager.h
class ProjectManager : public QObject {
    Q_OBJECT
public:
    // 项目生命周期管理
    bool createNewProject(const QString& projectName);
    bool openProject(const QString& filePath);
    bool saveProject();
    bool saveAsProject(const QString& filePath);
    void closeProject();
    
    // 项目状态管理
    bool hasActiveProject() const;
    QString getCurrentProjectPath() const;
    QString getCurrentProjectName() const;
    
    // 数据保存检查
    bool promptSaveIfNeeded();
    bool hasUnsavedChanges() const;
    
    // 项目配置管理
    void setProjectConfig(const QJsonObject& config);
    QJsonObject getProjectConfig() const;
    
signals:
    void projectOpened(const QString& path, const QString& name);
    void projectClosed();
    void projectSaved();
    void projectError(const QString& error);
    void projectConfigChanged();
};
`

### **B. 设备管理模块 (DeviceManager)**

`cpp
//  include/DeviceManager.h
class DeviceManager : public QObject {
    Q_OBJECT
public:
    // 传感器管理
    bool createSensorGroup(const QString& groupName);
    bool createSensorDevice(int groupId, const UI::SensorParams_1_2& params);
    bool editSensorDevice(const QString& serialNumber, const UI::SensorParams_1_2& params);
    bool deleteSensorDevice(const QString& serialNumber);
    
    // 作动器管理
    bool createActuatorGroup(const QString& groupName);
    bool createActuatorDevice(int groupId, const UI::ActuatorParams_1_2& params);
    bool editActuatorDevice(const QString& serialNumber, const UI::ActuatorParams_1_2& params);
    bool deleteActuatorDevice(const QString& serialNumber);
    
    // 硬件节点管理
    bool createHardwareNode(const UI::CreateHardwareNodeParams& params);
    bool editHardwareNode(const QString& nodeName, const UI::CreateHardwareNodeParams& params);
    bool deleteHardwareNode(const QString& nodeName);
    
    // 控制通道管理
    bool createControlChannel(const UI::ChannelInfo& channelInfo);
    bool editControlChannel(const QString& channelId, const UI::ChannelInfo& channelInfo);
    bool deleteControlChannel(const QString& channelId);
    
    // 数据管理器访问
    SensorDataManager_1_2* getSensorDataManager() const;
    ActuatorDataManager_1_2* getActuatorDataManager() const;
    CtrlChanDataManager_1_2* getCtrlChanDataManager() const;
    HardwareNodeResDataManager_1_2* getHardwareNodeResDataManager() const;
    
    // 数据清理
    void clearAllData();
    void clearSensorData();
    void clearActuatorData();
    void clearHardwareNodeData();
    void clearControlChannelData();
    
    // 设备状态管理
    void setDeviceStatus(const QString& deviceId, const QString& status);
    QString getDeviceStatus(const QString& deviceId) const;
    
signals:
    void deviceCreated(const QString& type, const QString& serialNumber);
    void deviceEdited(const QString& type, const QString& serialNumber);
    void deviceDeleted(const QString& type, const QString& serialNumber);
    void deviceStatusChanged(const QString& deviceId, const QString& status);
    void deviceError(const QString& error);
};
`

### **C. 导入导出管理模块 (ImportExportManager)**

`cpp
//  include/ImportExportManager.h
class ImportExportManager : public QObject {
    Q_OBJECT
    
public:
    // 构造函数
    explicit ImportExportManager(QObject* parent = nullptr);
    ~ImportExportManager();
    
    // 数据管理器设置
    void setActuatorDataManager(ActuatorDataManager_1_2* manager);
    void setSensorDataManager(SensorDataManager_1_2* manager);
    void setCtrlChanDataManager(CtrlChanDataManager_1_2* manager);
    void setHardwareNodeResDataManager(HardwareNodeResDataManager_1_2* manager);
    
    // 项目级导入导出 (保留现有格式)
    bool importProject(const QString& filePath, QProgressDialog* progressDialog = nullptr);
    bool exportProject(const QString& filePath, QProgressDialog* progressDialog = nullptr);
    
    // Excel格式导入导出 (保留现有格式)
    bool importProjectFromExcel(const QString& filePath);
    bool exportProjectToExcel(const QString& filePath);
    
    // JSON格式导入导出 (保留现有格式)
    bool importProjectFromJSON(const QString& filePath);
    bool exportProjectToJSON(const QString& filePath);
    bool exportChannelConfigToJSON(const QString& filePath);
    
    // 传感器专用导入导出
    bool importSensorProject(const QString& filePath);
    bool exportSensorProject(const QString& filePath);
    bool createSensorTemplate(const QString& filePath);
    
    // 作动器专用导入导出
    bool importActuatorProject(const QString& filePath);
    bool exportActuatorProject(const QString& filePath);
    
    // 硬件节点专用导入导出
    bool importHardwareNodeProject(const QString& filePath);
    bool exportHardwareNodeProject(const QString& filePath);
    
    // 控制通道专用导入导出
    bool importControlChannelProject(const QString& filePath);
    bool exportControlChannelProject(const QString& filePath);
    
    // 数据验证
    bool validateImportedData();
    QStringList getValidationErrors() const;
    
    // 错误处理
    QString getLastError() const;
    void clearError();
    
signals:
    void importStarted(const QString& filePath);
    void importProgress(int percentage, const QString& message);
    void importCompleted(const QString& filePath, bool success);
    void exportStarted(const QString& filePath);
    void exportProgress(int percentage, const QString& message);
    void exportCompleted(const QString& filePath, bool success);
    void dataValidationCompleted(bool valid, const QStringList& errors);
    void errorOccurred(const QString& error);
    
private:
    // 内部组件 (保留现有实现)
    std::unique_ptr<XLSDataExporter_1_2> xlsExporter_;
    std::unique_ptr<JSONDataExporter_1_2> jsonExporter_;
    std::unique_ptr<SensorExcelExtensions_1_2> sensorExcelExtensions_;
    
    // 数据管理器引用
    ActuatorDataManager_1_2* actuatorDataManager_;
    SensorDataManager_1_2* sensorDataManager_;
    CtrlChanDataManager_1_2* ctrlChanDataManager_;
    HardwareNodeResDataManager_1_2* hardwareNodeResDataManager_;
    
    // 错误处理
    QString lastError_;
    QStringList validationErrors_;
    
    // 内部方法
    bool validateFile(const QString& filePath, const QString& expectedExtension);
    bool synchronizeDataManagers();
    void setupProgressDialog(QProgressDialog* dialog, const QString& title);
    QString getFileExtension(const QString& filePath) const;
};
`

### **D. 对话框管理模块 (DialogManager) -  新增**

`cpp
//  include/DialogManager.h
class DialogManager : public QObject {
    Q_OBJECT
    
public:
    explicit DialogManager(QObject* parent = nullptr);
    ~DialogManager();
    
    // 传感器对话框管理
    bool showSensorDialog(const QString& serialNumber = QString());
    bool showSensorDialog(const UI::SensorParams_1_2& params);
    
    // 作动器对话框管理
    bool showActuatorDialog(const QString& serialNumber = QString());
    bool showActuatorDialog(const UI::ActuatorParams_1_2& params);
    
    // 硬件配置对话框管理
    bool showHardwareConfigDialog();
    bool showHardwareConfigDialog(const UI::HardwareConfigParams& params);
    
    // PID参数对话框管理
    bool showPIDParametersDialog();
    bool showPIDParametersDialog(const UI::PIDParameters& params);
    
    // 控制模式对话框管理
    bool showControlModeDialog();
    bool showControlModeDialog(const UI::ControlModeParams& params);
    
    // 节点配置对话框管理
    bool showNodeConfigDialog(const QString& nodeName = "LD-B1");
    bool showNodeConfigDialog(const QString& nodeName, const UI::NodeConfigParams& params);
    
    // 硬件节点创建对话框管理
    bool showCreateHardwareNodeDialog();
    bool showCreateHardwareNodeDialog(const UI::CreateHardwareNodeParams& params);
    
    // 控制通道编辑对话框管理
    bool showControlChannelEditDialog(const QString& channelId = QString());
    bool showControlChannelEditDialog(const UI::ChannelInfo& channelInfo);
    
    // 对话框状态管理
    bool isDialogOpen(const QString& dialogType) const;
    void closeAllDialogs();
    
signals:
    void dialogOpened(const QString& dialogType, const QString& identifier);
    void dialogClosed(const QString& dialogType, const QString& identifier);
    void dialogAccepted(const QString& dialogType, const QString& identifier, const QVariant& result);
    void dialogRejected(const QString& dialogType, const QString& identifier);
    void dialogError(const QString& dialogType, const QString& error);
    
private:
    // 对话框实例管理
    QMap<QString, QDialog*> openDialogs_;
    
    // 内部方法
    QDialog* createDialog(const QString& dialogType, const QVariant& params = QVariant());
    void registerDialog(const QString& dialogType, QDialog* dialog);
    void unregisterDialog(const QString& dialogType);
    QString generateDialogIdentifier(const QString& dialogType, const QVariant& params = QVariant());
};
`

### **E. 树形控件管理模块 (TreeManager) -  完善**

`cpp
//  include/TreeManager.h
class TreeManager : public QObject {
    Q_OBJECT
    
public:
    explicit TreeManager(QObject* parent = nullptr);
    ~TreeManager();
    
    // 树形控件设置
    void setHardwareTreeWidget(CustomHardwareTreeWidget* treeWidget);
    void setTestConfigTreeWidget(CustomTestConfigTreeWidget* treeWidget);
    void setMainWindow(CMyMainWindow* mainWindow);
    
    // 硬件树管理
    void refreshHardwareTree();
    void addHardwareNode(const QString& nodeName, const UI::CreateHardwareNodeParams& params);
    void removeHardwareNode(const QString& nodeName);
    void updateHardwareNode(const QString& nodeName, const UI::CreateHardwareNodeParams& params);
    
    // 测试配置树管理
    void refreshTestConfigTree();
    void addTestConfigItem(const QString& itemName, const QVariant& data);
    void removeTestConfigItem(const QString& itemName);
    void updateTestConfigItem(const QString& itemName, const QVariant& data);
    
    // 树形控件交互管理
    void setupTreeInteractions();
    void handleTreeItemSelection(QTreeWidgetItem* item);
    void handleTreeItemDoubleClick(QTreeWidgetItem* item);
    void handleTreeItemContextMenu(QTreeWidgetItem* item, const QPoint& position);
    
    // 拖拽功能管理
    void enableDragAndDrop(bool enable);
    void handleDragStart(QTreeWidgetItem* item);
    void handleDragEnter(QTreeWidgetItem* targetItem);
    void handleDragMove(QTreeWidgetItem* targetItem);
    void handleDragLeave();
    void handleDrop(QTreeWidgetItem* targetItem, const QMimeData* mimeData);
    
    // 树形控件样式管理
    void setupTreeStyle();
    void updateTreeIcons();
    void setTreeItemHighlight(QTreeWidgetItem* item, bool highlight);
    void clearTreeHighlights();
    
    // 树形控件数据同步
    void syncWithDataManagers();
    void syncHardwareTreeWithData();
    void syncTestConfigTreeWithData();
    
signals:
    void treeItemSelected(const QString& treeType, QTreeWidgetItem* item);
    void treeItemDoubleClicked(const QString& treeType, QTreeWidgetItem* item);
    void treeItemContextMenuRequested(const QString& treeType, QTreeWidgetItem* item, const QPoint& position);
    void dragAndDropCompleted(const QString& sourceType, const QString& targetType, const QVariant& data);
    void treeDataChanged(const QString& treeType);
    void treeError(const QString& error);
    
private:
    // 树形控件引用
    CustomHardwareTreeWidget* hardwareTreeWidget_;
    CustomTestConfigTreeWidget* testConfigTreeWidget_;
    CMyMainWindow* mainWindow_;
    
    // 交互处理器
    std::unique_ptr<TreeInteractionHandler> interactionHandler_;
    
    // 内部方法
    void setupTreeConnections();
    void setupTreeContextMenus();
    QTreeWidgetItem* findTreeItem(QTreeWidget* tree, const QString& itemName);
    void updateTreeItemData(QTreeWidgetItem* item, const QVariant& data);
};
`

### **F. UI管理模块 (UIManager) -  新增**

`cpp
//  include/UIManager.h
class UIManager : public QObject {
    Q_OBJECT
    
public:
    explicit UIManager(QObject* parent = nullptr);
    ~UIManager();
    
    // 主窗口管理
    void setMainWindow(CMyMainWindow* mainWindow);
    void setupMainWindowUI();
    void updateMainWindowTitle(const QString& projectName);
    
    // 信息面板管理
    void setupInfoPanels();
    void updateBasicInfoPanel(const NodeInfo& nodeInfo);
    void updateDetailInfoPanel(const QVariant& data);
    void clearInfoPanels();
    
    // 菜单和工具栏管理
    void setupMenus();
    void setupToolbars();
    void updateMenuStates();
    void updateToolbarStates();
    
    // 状态栏管理
    void setupStatusBar();
    void updateStatusBar(const QString& message);
    void updateProgressBar(int percentage);
    void clearStatusBar();
    
    // 标签页管理
    void setupTabWidgets();
    void switchToTab(const QString& tabName);
    void updateTabContent(const QString& tabName, const QVariant& data);
    
    // UI状态管理
    void setUIEnabled(bool enabled);
    void setUIReadOnly(bool readOnly);
    void updateUIFromProjectState();
    
    // 样式管理
    void loadStyleSheet();
    void applyCustomStyles();
    void updateTheme(const QString& themeName);
    
signals:
    void uiStateChanged(const QString& component, const QString& state);
    void menuActionTriggered(const QString& actionName);
    void toolbarActionTriggered(const QString& actionName);
    void tabChanged(const QString& tabName);
    void uiError(const QString& error);
    
private:
    // UI组件引用
    CMyMainWindow* mainWindow_;
    BasicInfoWidget* basicInfoWidget_;
    DetailInfoPanel* detailInfoPanel_;
    
    // 内部方法
    void setupUIConnections();
    void setupMenuConnections();
    void setupToolbarConnections();
    void setupTabConnections();
    void updateUIFromData();
};
`

### **G. 事件管理模块 (EventManager) -  新增**

`cpp
//  include/EventManager.h
class EventManager : public QObject {
    Q_OBJECT
    
public:
    explicit EventManager(QObject* parent = nullptr);
    ~EventManager();
    
    // 事件注册和注销
    void registerEventHandler(const QString& eventType, QObject* handler);
    void unregisterEventHandler(const QString& eventType, QObject* handler);
    
    // 事件发送
    void sendEvent(const QString& eventType, const QVariant& data = QVariant());
    void sendEventAsync(const QString& eventType, const QVariant& data = QVariant());
    
    // 事件监听
    void addEventListener(const QString& eventType, std::function<void(const QVariant&)> callback);
    void removeEventListener(const QString& eventType);
    
    // 事件队列管理
    void processEventQueue();
    void clearEventQueue();
    int getEventQueueSize() const;
    
    // 事件日志
    void enableEventLogging(bool enable);
    void logEvent(const QString& eventType, const QVariant& data);
    QStringList getEventLog() const;
    
signals:
    void eventProcessed(const QString& eventType, const QVariant& data);
    void eventError(const QString& eventType, const QString& error);
    void eventQueueOverflow();
    
private:
    // 事件处理
    QMap<QString, QList<QObject*>> eventHandlers_;
    QMap<QString, std::function<void(const QVariant&)>> eventListeners_;
    QQueue<QPair<QString, QVariant>> eventQueue_;
    
    // 事件日志
    QStringList eventLog_;
    bool eventLoggingEnabled_;
    
    // 内部方法
    void processEvent(const QString& eventType, const QVariant& data);
    void handleEventError(const QString& eventType, const QString& error);
};
`

##  **代码行数对比 v2.0**

| 模块 | 重构前 | 重构后 | 减少比例 | 功能说明 |
|------|--------|--------|----------|----------|
| **主界面** | 10,903行 | 600行 | 95%  | 简化为主界面协调器 |
| **项目管理** | 分散 | 800行 | 集中化 | 项目生命周期管理 |
| **设备管理** | 分散 | 1,200行 | 集中化 | 传感器、作动器、硬件节点管理 |
| **导入导出管理** | 分散 | 1,500行 | 集中化 | **Excel/JSON格式统一管理** |
| **对话框管理** | 分散 | 1,000行 | 集中化 | **6个标准对话框统一管理** |
| **树形控件管理** | 分散 | 1,200行 | 集中化 | **2个自定义树控件管理** |
| **UI管理** | 分散 | 800行 | 集中化 | **信息面板、菜单、工具栏管理** |
| **事件管理** | 分散 | 400行 | 集中化 | **事件驱动架构** |
| **菜单管理** | 分散 | 300行 | 集中化 | 菜单和工具栏管理 |
| **日志管理** | 分散 | 200行 | 集中化 | 日志记录和管理 |

##  **格式支持保留**

### **Excel格式支持**
-  **XLSDataExporter_1_2**: 通用Excel导入导出器
-  **SensorExcelExtensions_1_2**: 传感器专用Excel扩展
-  **17列标准格式**: 传感器参数完整支持
-  **多工作表支持**: 作动器、传感器、硬件节点、控制通道

### **JSON格式支持**
-  **JSONDataExporter_1_2**: JSON导入导出器
-  **通道配置导出**: channel_config.json格式
-  **完整项目导出**: 包含所有设备数据
-  **servo_control格式**: 作动器专用格式

##  **迁移策略 v2.0**

### **阶段1: 创建核心管理器 (最高优先级)**
1. 创建ImportExportManager类
2. 创建DialogManager类
3. 创建TreeManager类
4. 集成现有的XLSDataExporter_1_2和JSONDataExporter_1_2
5. 保留所有现有格式支持

### **阶段2: 创建业务逻辑管理器**
1. 创建ProjectManager、DeviceManager
2. 创建UIManager、EventManager
3. 实现统一的接口和信号槽连接
4. 保持现有用户界面不变

### **阶段3: 重构主界面**
1. 简化MainWindow，只保留UI协调功能
2. 通过各个管理器处理所有功能
3. 建立完整的事件驱动架构
4. 保持现有用户界面不变

### **阶段4: 测试和优化**
1. 单元测试各个管理器
2. 集成测试整体功能
3. 性能优化和代码清理
4. 用户界面测试

##  **架构优势 v2.0**

### **1. 格式兼容性**
- 完全保留现有Excel、JSON格式支持
- 用户无需改变现有工作流程
- 向后兼容所有现有功能

### **2. 模块化设计**
- 每个管理器负责特定功能域
- 清晰的职责分离
- 易于维护和扩展

### **3. 统一接口**
- 所有功能通过专门的管理器统一管理
- 一致的错误处理和进度反馈
- 统一的用户体验

### **4. 可测试性**
- 每个管理器可以独立测试
- 模拟数据管理器进行单元测试
- 集成测试覆盖完整流程

### **5. 可扩展性**
- 新格式支持只需扩展ImportExportManager
- 新设备类型只需扩展DeviceManager
- 新对话框只需扩展DialogManager
- 新功能模块可以独立添加

### **6. 事件驱动架构**
- 松耦合的组件通信
- 异步事件处理
- 更好的响应性和可维护性

##  **实施建议 v2.0**

### **优先级排序**
1. **最高优先级**: ImportExportManager, DialogManager (保留现有格式支持)
2. **高优先级**: TreeManager, UIManager (核心UI功能)
3. **中优先级**: ProjectManager, DeviceManager (核心业务功能)
4. **低优先级**: EventManager, LogManager (辅助功能)

### **风险控制**
1. **渐进式重构**: 一次只重构一个模块
2. **保持兼容**: 重构过程中保持原有功能
3. **充分测试**: 每个阶段都要进行充分测试
4. **文档更新**: 及时更新相关文档

### **成功标准**
1. 主界面代码从10,903行减少到600行以下
2. 所有现有格式支持完全保留
3. 所有现有对话框功能完全保留
4. 所有现有树形控件功能完全保留
5. 用户界面和操作流程保持不变
6. 所有功能测试通过
7. 代码可维护性显著提升

##  **技术实现细节**

### **依赖注入模式**
`cpp
// 主窗口构造函数中的依赖注入
CMyMainWindow::CMyMainWindow(QWidget* parent)
    : QMainWindow(parent), ui(new Ui::MainWindow) {
    
    // 创建管理器实例
    projectManager_ = std::make_unique<ProjectManager>(this);
    deviceManager_ = std::make_unique<DeviceManager>(this);
    importExportManager_ = std::make_unique<ImportExportManager>(this);
    dialogManager_ = std::make_unique<DialogManager>(this);
    treeManager_ = std::make_unique<TreeManager>(this);
    uiManager_ = std::make_unique<UIManager>(this);
    eventManager_ = std::make_unique<EventManager>(this);
    
    // 设置依赖关系
    setupManagerDependencies();
    
    // 连接信号槽
    connectManagerSignals();
    
    // 初始化UI
    uiManager_->setupMainWindowUI();
}
`

### **信号槽连接模式**
`cpp
// 管理器间的信号槽连接
void CMyMainWindow::connectManagerSignals() {
    // 项目管理器信号
    connect(projectManager_.get(), &ProjectManager::projectOpened,
            this, &CMyMainWindow::onProjectOpened);
    
    // 设备管理器信号
    connect(deviceManager_.get(), &DeviceManager::deviceCreated,
            treeManager_.get(), &TreeManager::onDeviceCreated);
    
    // 对话框管理器信号
    connect(dialogManager_.get(), &DialogManager::dialogAccepted,
            deviceManager_.get(), &DeviceManager::onDialogAccepted);
    
    // 导入导出管理器信号
    connect(importExportManager_.get(), &ImportExportManager::importCompleted,
            this, &CMyMainWindow::onImportCompleted);
}
`

这个v2.0架构方案将主界面的代码从10,903行减少到约600行，同时完全保留现有的Excel、JSON格式支持，以及所有对话框和树形控件功能，实现了更好的模块化、事件驱动架构和可维护性。
