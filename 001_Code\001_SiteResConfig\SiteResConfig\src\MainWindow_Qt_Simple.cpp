/**
 * @file MainWindow_Qt_Simple.cpp
 * @brief Simplified Main Window Implementation (Qt Version)
 * @details Simplified Qt GUI implementation without complex custom classes
 * <AUTHOR> Assistant
 * @date 2025-08-05
 * @version 1.0.0
 */

#include "MainWindow_Qt_Simple.h"
#include "ui_MainWindow.h"
#include "treelinestyle.h"
#include "ControlChannelEditDialog.h"
#include "LogManager.h"
#include "DeviceManager.h"
#include "UIManager.h"
#include "MainWindowHelper.h"
#include <QPainter>
#include <QPixmap>
#include <QDir>
#include <QApplication>

// 包含被从头文件移除的依赖
#include "Common_Fixed.h"
#include "DataModels_Fixed.h"
#include "ConfigManager_Fixed.h"
#include "ActuatorDialog_1_2.h"
#include "SensorDialog_1_2.h"
#include "SensorExcelExtensions_1_2.h"  // 🆕 新增：传感器Excel扩展功能

// 本地极性转换函数
namespace {
    QString polarityToString(int polarity) {
        // 极性存储必须使用数字格式，不再使用文本描述
        return QString::number(polarity);
    }
}
#include "HardwareConfigDialog.h"
#include "PIDParametersDialog.h"

// 🆕 新增：正则表达式支持
#include <QRegularExpression>
#include "ControlModeDialog.h"
#include "NodeConfigDialog.h"
#include "CreateHardwareNodeDialog.h"
#include "CustomTreeWidgets.h"
#include "XLSDataExporter_1_2.h"
#include "DataExporterFactory.h"
#include "DetailInfoPanel.h"

// 确保Qt基础类已包含
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QWidget>
#include <QtWidgets/QTreeWidget>
#include <QtCore/QMimeData>
#include <QtCore/QJsonDocument>
#include <QtCore/QJsonObject>
#include <QtCore/QJsonArray>
#include <QtCore/QSettings>
#include <QtCore/QDebug>
#include <algorithm>  // 🆕 新增：算法支持
#include <numeric>    // 🆕 新增：数值算法支持（std::accumulate）
#include <QtGui/QDragEnterEvent>
#include <QtGui/QDropEvent>

#include <QtWidgets/QApplication>
#include <QtWidgets/QMessageBox>
#include <QtWidgets/QFileDialog>
#include <QtWidgets/QInputDialog>
#include <QtWidgets/QProgressDialog>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QSplitter>
#include <QtWidgets/QTabWidget>
#include <QtWidgets/QTreeWidget>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QTextEdit>
#include <QtGui/QDragEnterEvent>
#include <QtGui/QDropEvent>
#include <QtCore/QMimeData>
#include <QtWidgets/QLayout>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QLabel>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QMenuBar>
#include <QtWidgets/QStatusBar>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QScrollBar>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QDoubleSpinBox>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QDialog>
#include <QtWidgets/QMenu>
#include <QtWidgets/QInputDialog>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QDoubleSpinBox>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QFrame>
#include <QtCore/QStandardPaths>
#include <QtCore/QStandardPaths>
#include <QtCore/QDateTime>
#include <QtCore/QThread>
#include <QtCore/QTimer>
#include <QTextStream>
#include <QStyleFactory>
#include <QDrag>
#include <QDebug>
#include <QPainter>
#include <QPixmap>
#include <QMetaObject>
#include <QBrush>
#include <QProgressDialog>
#include <QTimer>
// Qt 5.14兼容性：QTextCodec相关包含
#include <QTextCodec>

// Windows控制台编码支持
#ifdef Q_OS_WIN
#include <windows.h>
#include <io.h>
#include <fcntl.h>
#include <stdio.h>
#endif

// 包含硬件管理相关头文件
#include <memory>
#include <thread>
#include <mutex>
#include <atomic>
#include <cmath>
#include <cstdlib>

// 注意：自定义树控件类定义已移动到 CustomTreeWidgets.h

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

// 延后包含以避免循环依赖
#include "CustomTreeWidgets.h"

//using namespace UI; // 移除以避免命名冲突
//using Ui::MainWindow;
//using namespace MyDlg;

// ============================================================================
// MainWindow Implementation
// ============================================================================

CMyMainWindow::CMyMainWindow(QWidget* parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
    , configManager_(nullptr)
    , currentProject_(nullptr)
    , sensorDataManager_(std::make_unique<SensorDataManager_1_2>())  // 🆕 新增：传感器1_2版本数据管理器
    // , actuatorViewModel1_2_->getDataManager()(std::make_unique<ActuatorDataManager_1_2>()) // 🔧 已移除：改为通过ViewModel访问
    , actuatorViewModel1_2_(std::make_unique<ActuatorViewModel1_2>()) // 🆕 新增：作动器视图模型1_2版本
    , dataChangeListener_(nullptr)  // 🆕 新增：数据变化监听器
    //, dataExportManager_(nullptr)  // 🆕 新增：数据导出管理器（延后初始化）
    , xlsDataExporter_(nullptr)    // 🆕 新增：XLS数据导出器（延后初始化）
    , logManager_(std::make_unique<LogManager>(this))  // v3.4架构：日志管理器
    , configManager_wrapper_(std::make_unique<ConfigManager>(this))  // v3.4架构：配置管理器
    , eventManager_(std::make_unique<EventManager>(this))  // v3.4架构：事件管理器
    , deviceManager_(std::make_unique<DeviceManager>(this))  // v3.4架构：设备管理器
    , exportManager_(std::make_unique<ExportManager>(this))  // v3.4架构：导入导出管理器
    , projectManager_(std::make_unique<ProjectManager>(this))  // v3.4架构：项目管理器
    , dialogManager_(std::make_unique<DialogManager>(this))  // v3.4架构：对话框管理器
    , treeManager_(std::make_unique<TreeManager>(this))  // v3.4架构：树形控件管理器
    , infoPanelManager_(std::make_unique<InfoPanelManager>(this))  // v3.4架构：信息面板管理器
    , uiManager_(std::make_unique<UIManager>(this, this))  // v3.4架构：UI管理器
    , helper_(nullptr)  // 🆕 新增：辅助类（延后初始化）
    , statusUpdateTimer_(nullptr)
    , isConnected_(false)
    , isTestRunning_(false)
    , startTime_(QDateTime::currentDateTime())
    , hasActiveProject_(false)          // 🆕 新增：初始化项目状态
    , currentProjectPath_("")           // 🆕 新增：初始化项目路径
    , currentProjectName_("")           // 🆕 新增：初始化项目名称
{
    // Setup UI from .ui file
    ui->setupUi(this);

    // Initialize additional components - 使用UIManager
    // SetupUI();  // 🔧 重构：移动到UIManager

    // 🆕 新增：初始化传感器数据管理器
    initializeSensorDataManager();

    // 🆕 新增：初始化控制通道数据管理器
    initializeCtrlChanDataManager();

    // 🆕 新增：初始化硬件节点资源数据管理器
    initializeHardwareNodeResDataManager();

//    // 🆕 新增：初始化数据导出管理器
//    initializeDataExportManager();

    // 🆕 新增：初始化XLS导出器
    initializeXLSExporter();

    // 🆕 新增：初始化JSON导出器
    initializeJSONExporter();

    // 🆕 新增：初始化项目状态
    initializeProjectState();
    
    // 🆕 新增：设置初始状态为没有项目（禁用操作区域）
    updateOperationAreaState(false);

    // 关键：用 Windows 风格即可显示连线
    ui->hardwareTreeWidget->setStyle(QStyleFactory::create("windows"));
    ui->testConfigTreeWidget->setStyle(QStyleFactory::create("windows"));

    // 🆕 新增：连接ActuatorViewModel1_2的业务信号
    connectActuatorViewModelSignals();

    // v3.4架构：初始化管理器依赖注入
    initializeManagerDependencies();
    
    // v3.4架构：连接管理器信号槽
    connectManagerSignals();

    // 🆕 新增：初始化MainWindowHelper辅助类
    helper_ = std::make_unique<MainWindowHelper>(this, this);

    qDebug() << "Constructor completed. v3.4 Architecture managers initialized. Tree widgets ready for initialization.";
}

CMyMainWindow::~CMyMainWindow() {
    // 🆕 新增：清理数据变化监听器
    if (dataChangeListener_) {
        dataChangeListener_->disconnectAll();
        delete dataChangeListener_;
        dataChangeListener_ = nullptr;
    }
    
    delete currentProject_;
    delete ui;
}

bool CMyMainWindow::Initialize() {
    // Get configuration manager
    configManager_ = &Config::ConfigManager::GetInstance();
    if (!configManager_->Initialize()) {
        QMessageBox::critical(this, tr("初始化错误"),
            tr("配置管理器初始化失败！"));
        return false;
    }
    
    // v3.4架构：设置配置管理器包装器的现有配置管理器引用
    if (configManager_wrapper_) {
        configManager_wrapper_->setExistingConfigManager(configManager_);
        qDebug() << "v3.4架构：ConfigManager 现有配置管理器设置完成";
    }

    // Windows控制台编码修复 - 保守版本，不影响Qt内部编码
#ifdef Q_OS_WIN
    try {
        // 只设置控制台代码页，不修改Qt的编码设置
        SetConsoleOutputCP(65001);
        SetConsoleCP(65001);

        qDebug() << "Console encoding setup completed (conservative)";
    } catch (...) {
        qDebug() << "Error: Exception occurred during console encoding setup";
    }
#endif

    // 🆕 新增：从Qt资源系统加载样式表
    loadStyleSheetFromFile();

    // Connect signals and slots
    ConnectSignals();

    // Load initial data (只调用一次)
    LoadInitialData();

    // Update interface status
    UpdateUI();

    // Start status update timer
    statusUpdateTimer_ = new QTimer(this);
    connect(statusUpdateTimer_, &QTimer::timeout, this, &CMyMainWindow::OnUpdateStatus);
    statusUpdateTimer_->start(1000); // Update every second

    // 设置默认窗口标题（软件默认没有工程信息）
    setWindowTitle("SiteResConfig");

    // Show welcome message
    if (ui->statusbar) {
        ui->statusbar->showMessage(tr("系统已就绪 - 欢迎使用灵动加载上位机管理软件"));
    }

    // v3.4架构：完成管理器依赖注入 (UI已创建)
    completeManagerDependencies();

    // Add initial log entries
    LogMessage("INFO", tr("系统初始化完成"));
    LogMessage("INFO", tr("硬件管理器已就绪"));
    LogMessage("INFO", tr("用户界面已就绪"));
    
    // v3.4架构：使用LogManager记录初始化完成
    if (logManager_) {
        logManager_->info("v3.4架构：所有管理器初始化完成");
        logManager_->info("v3.4架构：系统就绪，开始正常运行");
    }

    return true;
}

void CMyMainWindow::SetupUI()
{
    // Connect UI signals to slots
    ConnectUISignals();

    // Connect log buttons
    if (ui->clearLogButton) connect(ui->clearLogButton, &QPushButton::clicked, this, &CMyMainWindow::OnClearLog);
    if (ui->saveLogButton) connect(ui->saveLogButton, &QPushButton::clicked, this, &CMyMainWindow::OnSaveLog);

    // 替换硬件树为自定义控件
    if (ui->hardwareTreeWidget) {
        // 获取原控件的父容器和位置
        QWidget* parent = ui->hardwareTreeWidget->parentWidget();
        QLayout* layout = parent->layout();

        // 创建自定义控件
        CustomHardwareTreeWidget* customHardwareTree = new CustomHardwareTreeWidget(parent);
        customHardwareTree->setMainWindow(this);

        // 复制原控件的属性
        customHardwareTree->setObjectName("hardwareTreeWidget");
        customHardwareTree->setContextMenuPolicy(Qt::CustomContextMenu);
        customHardwareTree->setRootIsDecorated(false);
        customHardwareTree->setItemsExpandable(true);
        customHardwareTree->setIndentation(20);
        customHardwareTree->setAnimated(true);
        customHardwareTree->setAlternatingRowColors(true);
        customHardwareTree->setSelectionMode(QAbstractItemView::SingleSelection);
        customHardwareTree->setHeaderHidden(true);

        // 替换控件
        if (layout) {
            layout->replaceWidget(ui->hardwareTreeWidget, customHardwareTree);
        }

        // 删除原控件并更新指针
        delete ui->hardwareTreeWidget;
        ui->hardwareTreeWidget = customHardwareTree;

        // 连接信号
        connect(ui->hardwareTreeWidget, &QTreeWidget::customContextMenuRequested,
                this, &CMyMainWindow::OnHardwareTreeContextMenu);
    }

    // 替换测试配置树为自定义控件
    if (ui->testConfigTreeWidget) {
        // 获取原控件的父容器和位置
        QWidget* parent = ui->testConfigTreeWidget->parentWidget();
        //QLayout* layout = parent->layout();

        // 创建自定义控件
        CustomTestConfigTreeWidget* customTestConfigTree = ui->testConfigTreeWidget;// = new CustomTestConfigTreeWidget(parent);
        customTestConfigTree->setMainWindow(this);

        // 复制原控件的属性
        //customTestConfigTree->setObjectName("testConfigTreeWidget");
        customTestConfigTree->setRootIsDecorated(false);
        customTestConfigTree->setItemsExpandable(true);
        customTestConfigTree->setIndentation(20);
        customTestConfigTree->setAnimated(true);
        customTestConfigTree->setAlternatingRowColors(true);
        customTestConfigTree->setSelectionMode(QAbstractItemView::SingleSelection);
        customTestConfigTree->setHeaderHidden(false);

        // 设置列
//        customTestConfigTree->setColumnCount(2);
//        QStringList headers;
//        headers << "试验配置" << "关联信息";
//        customTestConfigTree->setHeaderLabels(headers);

        // 设置列宽 - 支持6列显示
        customTestConfigTree->setColumnWidth(0, 200);  // 试验配置
        customTestConfigTree->setColumnWidth(1, 380);  // 关联信息
        customTestConfigTree->setColumnWidth(2, 80);   // 下位机ID
        customTestConfigTree->setColumnWidth(3, 80);   // 站点ID
        customTestConfigTree->setColumnWidth(4, 60);   // 使能
        customTestConfigTree->setColumnWidth(5, 80);   // 极性
        customTestConfigTree->header()->setStretchLastSection(true);

        // 🆕 新增：启用自定义右键菜单
        customTestConfigTree->setContextMenuPolicy(Qt::CustomContextMenu);

        // 替换控件
//        if (layout) {
//            layout->replaceWidget(ui->testConfigTreeWidget, customTestConfigTree);
//        }

//        // 删除原控件并更新指针
//        delete ui->testConfigTreeWidget;
//        ui->testConfigTreeWidget = customTestConfigTree;

        // 连接试验配置树的右键菜单
        connect(ui->testConfigTreeWidget, &QTreeWidget::customContextMenuRequested,
                this, &CMyMainWindow::OnTestConfigTreeContextMenu);
        
        // 🆕 新增：连接试验配置树的单击事件（替代TreeInteractionHandler的onItemClicked功能）
        connect(ui->testConfigTreeWidget, &QTreeWidget::itemClicked,
                this, &CMyMainWindow::OnTestConfigTreeItemClicked);
        

    }

    // 启用拖拽功能
    EnableTestConfigTreeDragDrop();

    // 从外部CSS文件加载样式表
    loadStyleSheetFromFile();

    // Initialize timers
    statusUpdateTimer_ = new QTimer(this);
    connect(statusUpdateTimer_, &QTimer::timeout, this, &CMyMainWindow::OnUpdateStatus);
    statusUpdateTimer_->start(1000); // Update every second

    // 注释掉数据模拟定时器初始化 - 数据模拟已弃用
    // dataSimulationTimer_ = new QTimer(this);
    // Data simulation timer - can be used for future data simulation features

    // 隐藏无用的菜单项
    HideUnusedMenuItems();
    
    // 🆕 新增：初始化详细信息面板
    initializeDetailInfoPanel();

    // 🆕 新增：设置详细信息面板初始状态
    if (ui->chkShowDetailInfoDlg) {
        // 默认不选中复选框
        ui->chkShowDetailInfoDlg->setChecked(false);
        // 默认隐藏详细信息面板，设置宽度为 120
        if (ui->detailInfoWidget) {
            ui->detailInfoWidget->setVisible(false);
            ui->detailInfoWidget->setMaximumWidth(120);
            ui->detailInfoWidget->setMinimumWidth(120);
        }
        // 默认设置详细信息组框宽度为 120
        if (ui->detailInfoGroupBox) {
            ui->detailInfoGroupBox->setMaximumWidth(120);
            ui->detailInfoGroupBox->setMinimumWidth(120);
        }
        AddLogEntry("DEBUG", u8"✅ 详细信息面板初始状态：复选框未选中，面板隐藏，宽度设置为 120，组框宽度设置为 120");
    }

    // 🆕 新增：初始化树形控件交互处理器（在详细信息面板之后）
    // initializeTreeInteractionHandler(); // 🚫 已移除：树形控件交互处理器

    AddLogEntry("INFO", tr("用户界面设置完成"));
}

// 🆕 新增：从Qt资源系统加载样式表
void CMyMainWindow::loadStyleSheetFromFile() {
    QString resourcePath = ":/styles/Res/style.css";

    QFile styleFile(resourcePath);
    if (styleFile.open(QIODevice::ReadOnly | QIODevice::Text)) {
        QTextStream stream(&styleFile);
        stream.setCodec("UTF-8"); // 确保正确读取中文注释
        QString styleSheet = stream.readAll();
        styleFile.close();

        // 🔄 修改：应用样式表到整个应用程序，而不仅仅是树形控件
        this->setStyleSheet(styleSheet);

//        // 🆕 新增：使用自定义TreeLineStyle替代CSS图标
//        setupCustomTreeStyle();

        AddLogEntry("INFO", QString(u8"样式表已从资源加载并应用到整个应用程序: %1").arg(resourcePath));
    } else {
        // 如果资源不存在，使用空样式表（系统默认样式）
        this->setStyleSheet("");

        AddLogEntry("WARNING", QString(u8"样式表资源未找到，使用系统默认样式: %1").arg(resourcePath));
    }
}

// 🆕 新增：设置树形控件的加号/减号图标
void CMyMainWindow::setupTreeWidgetIcons() {
    // 创建加号图标（13x13像素，更大更清晰）
    QPixmap plusIcon(13, 13);
    plusIcon.fill(Qt::transparent); // 使用透明背景，让虚线可以穿过
    QPainter plusPainter(&plusIcon);
    plusPainter.setRenderHint(QPainter::Antialiasing, false); // 像素级精确
    plusPainter.setPen(QPen(Qt::black, 1));
    plusPainter.setBrush(Qt::white);

    // 绘制白色背景的方形按钮（中心区域）
    plusPainter.fillRect(2, 2, 9, 9, Qt::white);
    // 绘制边框
    plusPainter.drawRect(2, 2, 8, 8);
    // 绘制加号 - 在按钮中心
    plusPainter.setPen(QPen(Qt::black, 1));
    plusPainter.drawLine(4, 6, 8, 6); // 水平线
    plusPainter.drawLine(6, 4, 6, 8); // 垂直线
    plusPainter.end();

    // 创建减号图标（13x13像素）
    QPixmap minusIcon(13, 13);
    minusIcon.fill(Qt::transparent); // 使用透明背景
    QPainter minusPainter(&minusIcon);
    minusPainter.setRenderHint(QPainter::Antialiasing, false);
    minusPainter.setPen(QPen(Qt::black, 1));
    minusPainter.setBrush(Qt::white);

    // 绘制白色背景的方形按钮（中心区域）
    minusPainter.fillRect(2, 2, 9, 9, Qt::white);
    // 绘制边框
    minusPainter.drawRect(2, 2, 8, 8);
    // 绘制减号 - 在按钮中心
    minusPainter.setPen(QPen(Qt::black, 1));
    minusPainter.drawLine(4, 6, 8, 6); // 水平线
    minusPainter.end();

    // 保存图标到临时文件，然后在CSS中引用
    QString tempDir = QApplication::applicationDirPath() + "/temp/";
    QDir().mkpath(tempDir);

    QString plusPath = tempDir + "plus.png";
    QString minusPath = tempDir + "minus.png";

    plusIcon.save(plusPath, "PNG");
    minusIcon.save(minusPath, "PNG");

    // 创建自定义CSS样式，引用生成的图标
    QString customTreeStyle = QString(
        "QTreeWidget::branch:closed:has-children:has-siblings,"
        "QTreeWidget::branch:closed:has-children:!has-siblings {"
        "    border-image: none;"
        "    image: url(%1);"
        "    background: transparent;"
        "    width: 13px;"
        "    height: 13px;"
        "    margin: 4px;"
        "}"
        "QTreeWidget::branch:open:has-children:has-siblings,"
        "QTreeWidget::branch:open:has-children:!has-siblings {"
        "    border-image: none;"
        "    image: url(%2);"
        "    background: transparent;"
        "    width: 13px;"
        "    height: 13px;"
        "    margin: 4px;"
        "}"
    ).arg(plusPath).arg(minusPath);

    // 应用自定义样式到树形控件
    if (ui->hardwareTreeWidget) {
        QString currentStyle = ui->hardwareTreeWidget->styleSheet();
        ui->hardwareTreeWidget->setStyleSheet(currentStyle + customTreeStyle);
    }
    if (ui->testConfigTreeWidget) {
        QString currentStyle = ui->testConfigTreeWidget->styleSheet();
        ui->testConfigTreeWidget->setStyleSheet(currentStyle + customTreeStyle);
    }

    AddLogEntry("INFO", QString(u8"树形控件加号/减号图标已设置: %1, %2").arg(plusPath).arg(minusPath));
}

// 🆕 新增：备用方案 - 简单的加号/减号样式
void CMyMainWindow::setupSimpleTreeIcons() {
    // 简单的CSS样式，使用Unicode字符
    QString simpleTreeStyle = QString(
        "QTreeWidget::branch:closed:has-children {"
        "    background-color: #FFFFFF;"
        "    border: 1px solid #808080;"
        "    border-radius: 0px;"
        "    width: 9px;"
        "    height: 9px;"
        "    margin: 4px;"
        "}"
        "QTreeWidget::branch:open:has-children {"
        "    background-color: #FFFFFF;"
        "    border: 1px solid #808080;"
        "    border-radius: 0px;"
        "    width: 9px;"
        "    height: 9px;"
        "    margin: 4px;"
        "}"
        "QTreeWidget::branch:closed:has-children:hover,"
        "QTreeWidget::branch:open:has-children:hover {"
        "    background-color: #E1ECFF;"
        "    border-color: #0078D4;"
        "}"
    );

    // 应用简单样式到树形控件
    if (ui->hardwareTreeWidget) {
        QString currentStyle = ui->hardwareTreeWidget->styleSheet();
        ui->hardwareTreeWidget->setStyleSheet(currentStyle + simpleTreeStyle);
    }
    if (ui->testConfigTreeWidget) {
        QString currentStyle = ui->testConfigTreeWidget->styleSheet();
        ui->testConfigTreeWidget->setStyleSheet(currentStyle + simpleTreeStyle);
    }

    AddLogEntry("INFO", QString(u8"树形控件简单样式已设置（备用方案）"));
}

// 🆕 新增：设置自定义树形控件样式
void CMyMainWindow::setupCustomTreeStyle() {
    // 创建自定义样式
    TreeLineStyle *customStyle = new TreeLineStyle();

    // 应用到硬件树形控件
    if (ui->hardwareTreeWidget) {
        ui->hardwareTreeWidget->setStyle(customStyle);
        // 设置基础样式属性 - 保留必要的缩进以显示图标
        ui->hardwareTreeWidget->setRootIsDecorated(false); // 禁用默认装饰，使用自定义样式
        ui->hardwareTreeWidget->setIndentation(20); // 恢复缩进以显示展开/折叠图标
        ui->hardwareTreeWidget->setItemsExpandable(true);
        ui->hardwareTreeWidget->setExpandsOnDoubleClick(false);

        AddLogEntry("INFO", QString(u8"硬件树形控件自定义样式已应用"));
    }

    // 应用到测试配置树形控件
    if (ui->testConfigTreeWidget) {
        // 为第二个树形控件创建独立的样式实例
        TreeLineStyle *customStyle2 = new TreeLineStyle();
        ui->testConfigTreeWidget->setStyle(customStyle2);
        // 设置基础样式属性 - 保留必要的缩进以显示图标
        ui->testConfigTreeWidget->setRootIsDecorated(false); // 禁用默认装饰，使用自定义样式
        ui->testConfigTreeWidget->setIndentation(20); // 恢复缩进以显示展开/折叠图标
        ui->testConfigTreeWidget->setItemsExpandable(true);
        ui->testConfigTreeWidget->setExpandsOnDoubleClick(false);

        AddLogEntry("INFO", QString(u8"测试配置树形控件自定义样式已应用"));
    }

    AddLogEntry("INFO", QString(u8"TreeLineStyle自定义样式设置完成"));
}

void CMyMainWindow::ConnectUISignals() {
    // Connect menu actions (using actual names from UI file)
    if (ui->actionNewProject) connect(ui->actionNewProject, &QAction::triggered, this, &CMyMainWindow::OnNewProject);
    if (ui->actionOpenProject) connect(ui->actionOpenProject, &QAction::triggered, this, &CMyMainWindow::OnOpenProject);
    if (ui->actionSaveProject) connect(ui->actionSaveProject, &QAction::triggered, this, &CMyMainWindow::OnSaveProject);
    if (ui->actionSaveAsProject) connect(ui->actionSaveAsProject, &QAction::triggered, this, &CMyMainWindow::OnSaveAsProject);
    if (ui->actionExit) connect(ui->actionExit, &QAction::triggered, this, &QWidget::close);

    // Hardware node configuration
    if (ui->actionNodeLD_B1) connect(ui->actionNodeLD_B1, &QAction::triggered, this, &CMyMainWindow::OnConfigureNodeLD_B1);
    if (ui->actionNodeLD_B2) connect(ui->actionNodeLD_B2, &QAction::triggered, this, &CMyMainWindow::OnConfigureNodeLD_B2);

    if (ui->actionAbout) connect(ui->actionAbout, &QAction::triggered, this, &CMyMainWindow::OnAbout);
    if (ui->actionRestoreColors) connect(ui->actionRestoreColors, &QAction::triggered, this, &CMyMainWindow::OnRestoreColors);

    // 🆕 新增：连接详细信息显示复选框信号
    if (ui->chkShowDetailInfoDlg) {
        connect(ui->chkShowDetailInfoDlg, &QCheckBox::toggled, this, &CMyMainWindow::OnShowDetailInfoDlgChanged);
    }
}

void CMyMainWindow::LoadInitialData() {
    qDebug() << "LoadInitialData() called";
    qDebug() << "Hardware tree widget pointer:" << ui->hardwareTreeWidget;
    qDebug() << "Test config tree widget pointer:" << ui->testConfigTreeWidget;

    // Initialize hardware tree
    InitializeHardwareTree();

    // Initialize test config tree
    InitializeTestConfigTree();

    // 不加载示例数据，保持树形控件为空白状态
    // 用户可以通过右键菜单手动创建所需的组和设备

    AddLogEntry("INFO", tr("硬件树初始化完成，等待用户创建设备"));

    // 注意：不在启动时自动调用智能关联，保持CH1/CH2关联信息为空
    // 只有在用户创建硬件节点时才会触发智能关联

    qDebug() << "LoadInitialData() completed";
}

void CMyMainWindow::InitializeHardwareTree() {
    if (!ui->hardwareTreeWidget) {
        qDebug() << "ERROR: hardwareTreeWidget is null!";
        return;
    }

    qDebug() << "Initializing hardware tree...";

    // 清空现有内容
    ui->hardwareTreeWidget->clear();

    // 创建根节点：任务1
    QTreeWidgetItem* taskRoot = new QTreeWidgetItem(ui->hardwareTreeWidget);
    taskRoot->setText(0, tr("硬件配置"));
    taskRoot->setExpanded(true);
    // 🆕 新增：为硬件配置根节点添加提示信息
    taskRoot->setToolTip(0, QString(u8"硬件配置管理\n包含作动器、传感器和硬件节点资源的配置信息\n右键可创建新的硬件组件"));

    // 在任务1下创建子节点
    QTreeWidgetItem* actuatorRoot = new QTreeWidgetItem(taskRoot);
    actuatorRoot->setText(0, tr("作动器"));
    actuatorRoot->setData(0, Qt::UserRole, "作动器"); // 设置类型为作动器
    actuatorRoot->setExpanded(true);
    // 🆕 新增：为作动器根节点添加提示信息
    actuatorRoot->setToolTip(0, QString(u8"作动器配置管理\n管理液压、电动等各类作动器设备\n右键可创建作动器组和作动器设备"));

    QTreeWidgetItem* sensorRoot = new QTreeWidgetItem(taskRoot);
    sensorRoot->setText(0, tr("传感器"));
    sensorRoot->setData(0, Qt::UserRole, "传感器"); // 设置类型为传感器
    sensorRoot->setExpanded(true);
    // 🆕 新增：为传感器根节点添加提示信息
    sensorRoot->setToolTip(0, QString(u8"传感器配置管理\n管理载荷、位置、压力等各类传感器设备\n右键可创建传感器组和传感器设备"));

    QTreeWidgetItem* hardwareRoot = new QTreeWidgetItem(taskRoot);
    hardwareRoot->setText(0, tr("硬件节点资源"));
    hardwareRoot->setData(0, Qt::UserRole, "硬件节点资源"); // 设置类型为硬件节点资源
    hardwareRoot->setExpanded(true);
    // 🆕 新增：为硬件节点资源根节点添加提示信息
    hardwareRoot->setToolTip(0, QString(u8"硬件节点资源管理\n管理控制器、数据采集器等硬件节点设备\n右键可创建硬件节点和硬件节点组"));

    // 🆕 修复：初始化时确保树形控件完全展开
    ui->hardwareTreeWidget->expandAll();

    qDebug() << "Hardware tree initialized with" << ui->hardwareTreeWidget->topLevelItemCount() << "top level items";
    qDebug() << "Root item:" << taskRoot->text(0).toUtf8().data();
    qDebug() << "Child count:" << taskRoot->childCount();
}

void CMyMainWindow::InitializeTestConfigTree() {
    if (!ui->testConfigTreeWidget) {
        qDebug() << "ERROR: testConfigTreeWidget is null!";
        return;
    }

    qDebug() << "Initializing test config tree...";

    // 清空现有内容
    ui->testConfigTreeWidget->clear();

    // 创建根节点：实验
    QTreeWidgetItem* taskRoot = new QTreeWidgetItem(ui->testConfigTreeWidget);
    taskRoot->setText(0, tr("实验"));
    taskRoot->setText(1, ""); // 关联信息列默认无信息
    taskRoot->setData(0, Qt::UserRole, "试验节点");
    taskRoot->setExpanded(true);
    // 🆕 新增：为实验根节点添加提示信息
    taskRoot->setToolTip(0, QString(u8"实验配置管理\n配置实验的控制通道、指令和数字IO\n管理硬件设备与实验资源的关联关系"));

    // 在实验下创建子节点
    QTreeWidgetItem* channelRoot = new QTreeWidgetItem(taskRoot);
    channelRoot->setText(0, tr("指令"));
    channelRoot->setText(1, ""); // 关联信息列默认无信息
    channelRoot->setData(0, Qt::UserRole, "试验节点");
    channelRoot->setExpanded(true);
    // 🆕 新增：为指令节点添加提示信息
    channelRoot->setToolTip(0, QString(u8"指令配置\n配置实验过程中的控制指令\n定义实验的执行流程和控制逻辑"));

    QTreeWidgetItem* spectrumRoot = new QTreeWidgetItem(taskRoot);
    spectrumRoot->setText(0, tr("DI"));
    spectrumRoot->setText(1, ""); // 关联信息列默认无信息
    spectrumRoot->setData(0, Qt::UserRole, "试验节点");
    spectrumRoot->setExpanded(true);
    // 🆕 新增：为DI节点添加提示信息
    spectrumRoot->setToolTip(0, QString(u8"数字输入(DI)配置\n配置数字输入信号\n监控开关状态、限位信号等数字量输入"));

    QTreeWidgetItem* loadChannelRoot = new QTreeWidgetItem(taskRoot);
    loadChannelRoot->setText(0, tr("DO"));
    loadChannelRoot->setText(1, ""); // 关联信息列默认无信息
    loadChannelRoot->setData(0, Qt::UserRole, "试验节点");
    loadChannelRoot->setExpanded(true);
    // 🆕 新增：为DO节点添加提示信息
    loadChannelRoot->setToolTip(0, QString(u8"数字输出(DO)配置\n配置数字输出信号\n控制继电器、指示灯等数字量输出"));

    QTreeWidgetItem* controlChannelRoot = new QTreeWidgetItem(taskRoot);
    controlChannelRoot->setText(0, tr("控制通道"));
    controlChannelRoot->setText(1, ""); // 关联信息列默认无信息
    controlChannelRoot->setData(0, Qt::UserRole, "控制通道组");
    controlChannelRoot->setExpanded(true);
    // 🆕 新增：为控制通道根节点添加提示信息
    controlChannelRoot->setToolTip(0, QString(u8"控制通道配置\n配置实验的控制通道(CH1、CH2)\n管理载荷、位置、控制等资源的关联关系"));

    // 🆕 修改：在控制通道下创建CH1和CH2，启动时关联信息为空
    for (int ch = 1; ch <= 2; ++ch) {
        QTreeWidgetItem* channelItem = new QTreeWidgetItem(controlChannelRoot);
        QString channelId = QString("CH%1").arg(ch);
        channelItem->setText(0, channelId);
        channelItem->setText(1, ""); // 启动时关联信息为空
        // 🆕 新增：初始化扩展列默认值
        channelItem->setText(2, "1");    // 默认下位机ID
        channelItem->setText(3, "1");    // 默认站点ID
        channelItem->setText(4, u8"✅");  // 默认启用
        channelItem->setText(5, "");     // CH1、CH2第五列不显示值
        channelItem->setData(0, Qt::UserRole, "控制通道");
        // 🔧 修复：存储channelId用于后续查找
        channelItem->setData(1, Qt::UserRole, channelId);
        channelItem->setExpanded(true);
        // 🆕 新增：为CH节点添加详细提示信息
        channelItem->setToolTip(0, GenerateControlChannelDetailedInfo(channelId));

        // 在每个通道下创建载荷1、载荷2、位置、控制子节点，启动时关联信息为空
        QTreeWidgetItem* load1Item = new QTreeWidgetItem(channelItem);
        load1Item->setText(0, tr("载荷1"));
        load1Item->setText(1, ""); // 启动时关联信息为空
        load1Item->setData(0, Qt::UserRole, "载荷传感器");
        // 🆕 修改：为载荷1节点添加详细提示信息
        load1Item->setToolTip(0, GenerateLoadSensorDetailedInfo("载荷1", ""));

        QTreeWidgetItem* load2Item = new QTreeWidgetItem(channelItem);
        load2Item->setText(0, tr("载荷2"));
        load2Item->setText(1, ""); // 启动时关联信息为空
        load2Item->setData(0, Qt::UserRole, "载荷传感器");
        // 🆕 修改：为载荷2节点添加详细提示信息
        load2Item->setToolTip(0, GenerateLoadSensorDetailedInfo("载荷2", ""));

        QTreeWidgetItem* positionItem = new QTreeWidgetItem(channelItem);
        positionItem->setText(0, tr("位置"));
        positionItem->setText(1, ""); // 启动时关联信息为空
        positionItem->setData(0, Qt::UserRole, "位置传感器");
        // 🆕 修改：为位置节点添加详细提示信息
        positionItem->setToolTip(0, GeneratePositionSensorDetailedInfo(""));

        QTreeWidgetItem* controlItem = new QTreeWidgetItem(channelItem);
        controlItem->setText(0, tr("控制"));
        controlItem->setText(1, ""); // 启动时关联信息为空
        controlItem->setData(0, Qt::UserRole, "控制作动器");
        // 🆕 修改：为控制节点添加详细提示信息
        controlItem->setToolTip(0, GenerateControlActuatorDetailedInfo(""));
    }

    // 🆕 新增：将控制通道数据添加到CtrlChanDataManager（只在新建工程且数据为空时创建）
    if (ctrlChanDataManager_) {
        auto existingGroups = ctrlChanDataManager_->getAllControlChannelGroups();
        
        // 🆕 重要修复：只在以下情况创建默认控制通道组：
        // 1. 当前没有任何控制通道组数据
        // 2. 当前项目路径为空（表示是新建工程，不是加载的工程）
        if (existingGroups.isEmpty() && currentProjectPath_.isEmpty()) {
            AddLogEntry("INFO", QString(u8"📋 新建工程：创建默认控制通道组"));
            
            // 创建默认控制通道组
            UI::ControlChannelGroup group;
            group.groupId = 1;
            group.groupName = "默认控制通道组";
            group.groupType = "控制通道";
            group.createTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss").toStdString();
            group.groupNotes = "系统初始化时创建的默认控制通道组";

            // 添加CH1和CH2通道
            for (int ch = 1; ch <= 2; ++ch) {
                UI::ControlChannelParams channel;
                channel.channelId = QString("CH%1").arg(ch).toStdString();
                channel.channelName = QString("CH%1").arg(ch).toStdString();
                channel.hardwareAssociation = "";  // 默认为空，等待用户拖拽关联
                channel.load1Sensor = "";          // 默认为空，等待用户拖拽关联
                channel.load2Sensor = "";          // 默认为空，等待用户拖拽关联
                channel.positionSensor = "";       // 默认为空，等待用户拖拽关联
                channel.controlActuator = "";      // 默认为空，等待用户拖拽关联
                channel.notes = "";                // 默认为空

                group.channels.push_back(channel);
            }

            bool success = ctrlChanDataManager_->createControlChannelGroup(group);
            if (success) {
                AddLogEntry("INFO", QString(u8"✅ 默认控制通道组已添加到数据管理器: %1个通道").arg(group.channels.size()));
            } else {
                AddLogEntry("WARNING", QString(u8"⚠️ 默认控制通道组添加到数据管理器失败"));
            }
        } else if (!existingGroups.isEmpty()) {
            AddLogEntry("INFO", QString(u8"🔍 数据管理器中已有%1个控制通道组，跳过默认创建").arg(existingGroups.size()));
        } else {
            AddLogEntry("INFO", QString(u8"🔍 加载工程模式，跳过默认数据创建"));
        }
    } else {
        AddLogEntry("ERROR", QString(u8"❌ 控制通道数据管理器未初始化"));
    }
    
    // 🆕 新增：初始化时确保实验配置树形控件完全展开
    ui->testConfigTreeWidget->expandAll();
    AddLogEntry("INFO", QString(u8"✅ 实验配置树初始化完成并已完全展开"));
}

void CMyMainWindow::ConnectSignals() {
    // 注意：菜单信号连接已在ConnectUISignals()中完成，这里不再重复连接
    // 只连接其他非菜单相关的信号

    // Hardware node configuration
    if (ui->actionNodeLD_B1) connect(ui->actionNodeLD_B1, &QAction::triggered, this, &CMyMainWindow::OnConfigureNodeLD_B1);
    if (ui->actionNodeLD_B2) connect(ui->actionNodeLD_B2, &QAction::triggered, this, &CMyMainWindow::OnConfigureNodeLD_B2);

    if (ui->actionAbout) connect(ui->actionAbout, &QAction::triggered, this, &CMyMainWindow::OnAbout);
    if (ui->actionRestoreColors) connect(ui->actionRestoreColors, &QAction::triggered, this, &CMyMainWindow::OnRestoreColors);
}

// 更新用户界面状态 - 当前为空实现
void CMyMainWindow::UpdateUI() {
    // Update UI based on current state
}

// Event Handlers
void CMyMainWindow::OnNewProject() {
    // v3.4架构：完全使用ProjectManager处理新建项目
    if (projectManager_) {
        // 使用LogManager记录日志
        if (logManager_) {
            logManager_->info("v3.4架构：使用ProjectManager新建实验工程...");
        }
        
        // 委托给ProjectManager处理
        bool success = projectManager_->createNewProject();
        if (success) {
            if (logManager_) {
                logManager_->info("v3.4架构：ProjectManager新建项目成功");
            }
            
            // v3.4架构：项目创建成功，完成初始化
            completeNewProjectSetup();
        } else {
            if (logManager_) {
                logManager_->warning("v3.4架构：ProjectManager处理失败");
            }
        }
        return;
    }
    
    // 如果ProjectManager未初始化，记录错误
    AddLogEntry("ERROR", "ProjectManager未初始化，无法创建新项目");
}

void CMyMainWindow::OnOpenProject() {
    // v3.4架构：完全使用ProjectManager处理打开项目
    if (projectManager_) {
        // 使用LogManager记录日志
        if (logManager_) {
            logManager_->info("v3.4架构：使用ProjectManager打开实验工程...");
        }
        
        // 委托给ProjectManager处理
        bool success = projectManager_->openProjectWithDialog();
        if (success) {
            if (logManager_) {
                logManager_->info("v3.4架构：ProjectManager打开项目成功");
            }
        } else {
            if (logManager_) {
                logManager_->warning("v3.4架构：ProjectManager处理失败，用户可能取消了操作");
            }
        }
        return;
    }
    
    // 如果ProjectManager未初始化，记录错误
    AddLogEntry("ERROR", "ProjectManager未初始化，无法打开项目");
}

void CMyMainWindow::OnSaveProject() {
    // v3.4架构：完全使用ProjectManager处理保存项目
    if (projectManager_) {
        // 使用LogManager记录日志
        if (logManager_) {
            logManager_->info("v3.4架构：使用ProjectManager保存实验工程...");
        }
        
        // 委托给ProjectManager处理
        bool success = projectManager_->saveProjectWithDialog();
        if (success) {
            if (logManager_) {
                logManager_->info("v3.4架构：ProjectManager保存项目成功");
            }
        } else {
            if (logManager_) {
                logManager_->warning("v3.4架构：ProjectManager保存失败或用户取消了操作");
            }
        }
        return;
    }
    
    // 如果ProjectManager未初始化，记录错误
    AddLogEntry("ERROR", "ProjectManager未初始化，无法保存项目");
}

void CMyMainWindow::OnSaveAsProject() {
    //// 调用新的导出函数
    //OnSaveAsProject_1_2();

    exportProjectAsChannelConfig();
}

// 🚫 已注释：冗余的完整项目导出功能，已被exportProjectAsChannelConfig()替代
//void CMyMainWindow::OnSaveAsProject_1_2() {
//    if (!currentProject_) {
//        QMessageBox::warning(this, tr("导出工程"), tr("没有可导出的工程！"));
//        return;
//    }
//
//    // 3. 设置文件保存对话框（只支持JSON格式）
//    QString defaultFileName = QString("%1.json").arg(QString::fromStdString(currentProject_->projectName));
//    QString filter = tr("JSON文件 (*.json)");
//
//    QString fileName = QFileDialog::getSaveFileName(this,
//        tr("导出实验工程 (JSON格式)"),
//        QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + "/" + defaultFileName,
//        filter);
//
//    if (!fileName.isEmpty()) {
//        // 确保JSON导出器已初始化
//        if (!jsonDataExporter_) {
//            initializeJSONExporter();
//        }
//        
//        bool success = false;
//        if (jsonDataExporter_) {
//            // 使用JSON导出器导出完整项目数据
//            success = jsonDataExporter_->exportToJSON_1_2(fileName);
//            
//            if (!success) {
//                AddLogEntry("ERROR", QString(u8"JSON导出失败: %1").arg(jsonDataExporter_->getLastError()));
//            }
//        } else {
//            AddLogEntry("ERROR", u8"JSON导出器初始化失败");
//        }
//
//        if (success) {
//            AddLogEntry("INFO", QString("导出实验工程 (基于内存数据): %1").arg(fileName));
//            QMessageBox::information(this, tr("导出成功"),
//                QString("实验工程已导出到:\n%1\n\n导出格式: JSON (基于内存数据)").arg(fileName));
//        } else {
//            AddLogEntry("ERROR", QString("导出实验工程失败: %1").arg(fileName));
//            QMessageBox::critical(this, tr("导出失败"), tr("导出实验工程失败！\n请检查文件路径和权限。"));
//        }
//    }
//}

void CMyMainWindow::OnExit() {
    close();
}

void CMyMainWindow::OnClearLog() {
    if (ui->logTextEdit) {
        ui->logTextEdit->clear();
        AddLogEntry("INFO", tr("日志已清空"));
    }
}

void CMyMainWindow::OnSaveLog() {
    QMessageBox::information(this, tr("保存日志"), tr("日志保存功能正在开发中..."));
}

void CMyMainWindow::OnAbout() {
    QMessageBox::about(this, tr("关于软件"),
        tr("<h3>SiteResConfig - 灵动加载上位机管理软件</h3>"
           "<p><b>版本:</b> 1.0.0</p>"
           "<p><b>构建日期:</b> 2025-08-05</p>"
           "<p><b>描述:</b> 专业的加载控制系统管理软件</p>"));
}

void CMyMainWindow::OnRestoreColors() {
    // 手动恢复所有树形控件的颜色

    ForceRestoreAllTreeColors();
    QMessageBox::information(this, tr("颜色恢复"),
        tr("已成功恢复所有树形控件的颜色！"));
}

void CMyMainWindow::OnUpdateStatus() {
    if (ui->statusbar) {
        QString statusText = tr("系统运行正常");
        if (isConnected_) {
            statusText += tr(" | 配置已加载");
        } else {
            statusText += tr(" | 配置未加载");
        }

        if (isTestRunning_) {
            statusText += tr(" | 数据制作中");
        }

        ui->statusbar->showMessage(statusText);
    }
}

/**
 * @brief 处理详细信息显示复选框状态变化
 * @param checked 复选框是否被选中
 */
void CMyMainWindow::OnShowDetailInfoDlgChanged(bool checked) {
    AddLogEntry("INFO", QString(u8"🔍 详细信息显示状态变化：%1").arg(checked ? "显示" : "隐藏"));
    
    if (checked) {
        // 复选框被选中：显示详细信息面板，设置最大宽度为 16777215
        if (ui->detailInfoWidget) {
            ui->detailInfoWidget->setVisible(true);
            ui->detailInfoWidget->setMaximumWidth(16777215);
            ui->detailInfoWidget->setMinimumWidth(120);
            AddLogEntry("DEBUG", u8"✅ 详细信息面板已显示，宽度设置为最大 16777215");
        }
        if (ui->detailInfoGroupBox) {
            ui->detailInfoGroupBox->setMaximumWidth(16777215);
            ui->detailInfoGroupBox->setMinimumWidth(120);
            AddLogEntry("DEBUG", u8"✅ 详细信息组框宽度设置为最大 16777215");
        }
    } else {
        // 复选框未选中：设置宽度为 120，但不显示详细信息面板
        if (ui->detailInfoWidget) {
            ui->detailInfoWidget->setMaximumWidth(120);
            ui->detailInfoWidget->setMinimumWidth(120);
            ui->detailInfoWidget->setVisible(false);
            AddLogEntry("DEBUG", u8"✅ 详细信息面板宽度设置为 120，已隐藏");
        }
        if (ui->detailInfoGroupBox) {
            ui->detailInfoGroupBox->setMaximumWidth(120);
            ui->detailInfoGroupBox->setMinimumWidth(120);
            AddLogEntry("DEBUG", u8"✅ 详细信息组框宽度设置为 120");
        }
    }
}

// 🆕 新增：处理树控件项目编辑事件


void CMyMainWindow::AddSampleHardwareNode(const QString& name, const QString& ip, const QString& status) {
    if (!ui->hardwareTreeWidget) return;

    // 获取任务1根节点
    QTreeWidgetItem* taskRoot = ui->hardwareTreeWidget->topLevelItem(0);
    if (!taskRoot) return;

    // 获取硬件节点资源子节点（第3个子节点，索引为2）
    QTreeWidgetItem* hardwareRoot = taskRoot->child(2);
    if (!hardwareRoot) return;

    QTreeWidgetItem* item = new QTreeWidgetItem(hardwareRoot);
    item->setText(0, QString("%1 [%2]").arg(name).arg(status));
    item->setData(0, Qt::UserRole, "硬件节点设备"); // 设置类型为硬件节点设备
    item->setToolTip(0, QString("IP地址: %1").arg(ip));
}

void CMyMainWindow::AddSampleActuator(const QString& name, const QString& capacity, const QString& type) {
    if (!ui->hardwareTreeWidget) return;

    // 获取任务1根节点
    QTreeWidgetItem* taskRoot = ui->hardwareTreeWidget->topLevelItem(0);
    if (!taskRoot) return;

    // 获取作动器子节点（第1个子节点，索引为0）
    QTreeWidgetItem* actuatorRoot = taskRoot->child(0);
    if (!actuatorRoot) return;

    QTreeWidgetItem* item = new QTreeWidgetItem(actuatorRoot);
    item->setText(0, QString("%1 [%2]").arg(name).arg(capacity));
    item->setData(0, Qt::UserRole, "作动器设备"); // 设置类型为作动器设备
    item->setToolTip(0, QString("类型: %1").arg(type));
}

void CMyMainWindow::AddSampleSensor(const QString& name, const QString& range, const QString& type) {
    if (!ui->hardwareTreeWidget) return;

    // 获取任务1根节点
    QTreeWidgetItem* taskRoot = ui->hardwareTreeWidget->topLevelItem(0);
    if (!taskRoot) return;

    // 获取传感器子节点（第2个子节点，索引为1）
    QTreeWidgetItem* sensorRoot = taskRoot->child(1);
    if (!sensorRoot) return;

    QTreeWidgetItem* item = new QTreeWidgetItem(sensorRoot);
    item->setText(0, QString("%1 [%2]").arg(name).arg(range));
    item->setData(0, Qt::UserRole, "传感器设备"); // 设置类型为传感器设备
    item->setToolTip(0, QString("类型: %1").arg(type));
}

void CMyMainWindow::AddSampleLoadChannel(const QString& name, const QString& capacity, const QString& mode) {
    if (!ui->testConfigTreeWidget) return;

    // 获取实验根节点
    QTreeWidgetItem* taskRoot = ui->testConfigTreeWidget->topLevelItem(0);
    if (!taskRoot) return;

    // 获取指令子节点（第1个子节点，索引为0）
    QTreeWidgetItem* channelRoot = taskRoot->child(0);
    if (!channelRoot) return;

    QTreeWidgetItem* item = new QTreeWidgetItem(channelRoot);
    item->setText(0, QString("%1").arg(name));
    item->setText(1, ""); // 关联信息列默认无信息
    item->setData(0, Qt::UserRole, "加载通道设备"); // 设置类型为加载通道设备
    item->setToolTip(0, QString("容量: %1\n控制模式: %2").arg(capacity).arg(mode));
}

void CMyMainWindow::AddLogEntry(const QString& level, const QString& message) const {
    // Use log text edit from UI file
    if (ui->logTextEdit) {
        QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
        QString coloredMessage;
        QString strMsg;

        if (level == "ERROR" || level == "CRITICAL") {
            coloredMessage = QString("<span style='color: #f48771;'>[%1] [%2] %3</span>")
                .arg(timestamp).arg(level).arg(message);
            strMsg = QString("[%1] [%2] %3").arg(timestamp).arg(level).arg(message);
        } else if (level == "WARNING") {
            coloredMessage = QString("<span style='color: #dcdcaa;'>[%1] [%2] %3</span>")
                .arg(timestamp).arg(level).arg(message);
            strMsg = QString("[%1] [%2] %3").arg(timestamp).arg(level).arg(message);
        } else if (level == "INFO") {
            coloredMessage = QString("<span style='color: #4ec9b0;'>[%1] [%2] %3</span>")
                .arg(timestamp).arg(level).arg(message);
            strMsg = QString("[%1] [%2] %3").arg(timestamp).arg(level).arg(message);
        } else {
            coloredMessage = QString("<span style='color: #569cd6;'>[%1] [%2] %3</span>")
                .arg(timestamp).arg(level).arg(message);
            strMsg = QString("[%1] [%2] %3").arg(timestamp).arg(level).arg(message);
        }

        ui->logTextEdit->append(coloredMessage);

        // Auto-scroll to bottom
        QScrollBar* scrollBar = ui->logTextEdit->verticalScrollBar();
        if (scrollBar) {
            scrollBar->setValue(scrollBar->maximum());
        }
        qDebug() << strMsg;
    }
}

//// 创建新项目 - 当前为空实现
//void CMyMainWindow::CreateNewProject() {
//    // Create new project
//}

//bool CMyMainWindow::LoadProject(const StringType& filePath) {
//    // Load project
//    return true;
//}

//bool CMyMainWindow::SaveProject(const StringType& filePath) {
//    if (!currentProject_) {
//        return false;
//    }

//    try {
//        return currentProject_->SaveToFile(filePath);
//    } catch (const std::exception& e) {
//        AddLogEntry("ERROR", QString("保存工程失败: %1").arg(e.what()));
//        return false;
//    }
//}

bool CMyMainWindow::SaveProjectToJSON(const QString& filePath) {
    if (!currentProject_) {
        AddLogEntry("ERROR", u8"没有当前项目，无法保存JSON");
        return false;
    }

    AddLogEntry("INFO", QString(u8"开始保存项目到JSON: %1").arg(filePath));

    try {
        // 更新项目的修改时间
        currentProject_->modifiedDate = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss").toStdString();

        // 策略1：使用新的JSON导出器进行保存
        // 使用toLocal8Bit()来正确处理中文文件名
        AddLogEntry("DEBUG", QString(u8"开始调用JSON导出器: %1").arg(filePath));

        // 🆕 使用新的JSON导出功能
        if (!jsonDataExporter_) {
            initializeJSONExporter();
        }

        bool directSuccess = false;
        if (jsonDataExporter_) {
            directSuccess = jsonDataExporter_->exportCompleteProject(filePath);
            if (!directSuccess) {
                AddLogEntry("ERROR", QString(u8"JSON导出失败: %1").arg(jsonDataExporter_->getLastError()));
            }
        } else {
            AddLogEntry("ERROR", u8"JSON导出器初始化失败");
        }

        if (directSuccess) {
            AddLogEntry("INFO", QString(u8"项目已保存为JSON: %1").arg(filePath));
            return true;
        } else {
            AddLogEntry("ERROR", u8"JSON保存失败");
            return false;
        }

    } catch (const std::exception& e) {
        AddLogEntry("ERROR", QString("保存JSON文件失败: %1").arg(e.what()));
        return false;
    }
}

//// 注释掉LoadProjectFromCSV方法 - CSV操作已弃用


//bool CMyMainWindow::LoadProjectFromJSON(const QString& filePath) {
//    try {
//        // 创建新项目
//        if (currentProject_) {
//            delete currentProject_;
//        }
//        currentProject_ = new DataModels::TestProject();

//        // 使用TestProject的LoadFromFile方法
//        bool success = currentProject_->LoadFromFile(filePath.toStdString());

//        if (success)
//        {
//            // 🆕 新增：设置项目对象的DataManager引用
//            if (currentProject_) {
//                currentProject_->setSensorDataManager(sensorDataManager_.get());
//                currentProject_->setActuatorDataManager(actuatorViewModel1_2_->getDataManager().get());
//            }

//            // 重新初始化界面
//            InitializeHardwareTree();
//            InitializeTestConfigTree();

//            // 更新窗口标题
//            setWindowTitle(QString("SiteResConfig - %1").arg(QString::fromStdString(currentProject_->projectName)));
//        }

//        return success;
//    } catch (const std::exception& e) {
//        AddLogEntry("ERROR", QString("加载JSON文件失败: %1").arg(e.what()));
//        return false;
//    }
//}

/**
 * @brief 带有通道配置支持的JSON导出函数
 * @param filePath 文件路径
 * @return 导出成功返回true
 */
bool CMyMainWindow::SaveProjectToJSON_1_2(const QString& filePath) {
    if (!currentProject_) {
        AddLogEntry("ERROR", u8"没有当前项目，无法保存JSON");
        return false;
    }

    AddLogEntry("INFO", QString(u8"开始保存项目到JSON (带通道配置): %1").arg(filePath));

    try {
        // 更新项目的修改时间
        currentProject_->modifiedDate = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss").toStdString();

        // 使用新的JSON导出器进行保存
        if (!jsonDataExporter_) {
            initializeJSONExporter(); // 初始化带通道配置的JSON导出器
        }

        bool directSuccess = false;
        if (jsonDataExporter_) {
            // 设置通道配置数据
            //jsonDataExporter_->setChannelConfigData(channelConfigData_);
            directSuccess = jsonDataExporter_->exportCompleteProject(filePath);
            if (!directSuccess) {
                AddLogEntry("ERROR", QString(u8"JSON导出失败: %1").arg(jsonDataExporter_->getLastError()));
            }
        } else {
            AddLogEntry("ERROR", u8"JSON导出器初始化失败");
        }

        if (directSuccess) {
            AddLogEntry("INFO", QString(u8"项目已保存为JSON (带通道配置): %1").arg(filePath));
            return true;
        } else {
            AddLogEntry("ERROR", u8"JSON保存失败");
            return false;
        }

    } catch (const std::exception& e) {
        AddLogEntry("ERROR", QString("保存JSON文件失败: %1").arg(e.what()));
        return false;
    }
}

/**
 * @brief 统一的项目数据导出接口（带通道配置）
 * @param filePath 文件路径
 * @return 导出成功返回true
 */
bool CMyMainWindow::exportProjectData_1_2(const QString& filePath) {
    AddLogEntry("INFO", QString(u8"开始导出项目数据 (带通道配置，仅JSON格式): %1").arg(filePath));

    // 验证文件路径
    if (filePath.isEmpty()) {
        AddLogEntry("ERROR", u8"文件路径为空，无法导出");
        return false;
    }

//    // 加载通道配置数据
//    if (!loadChannelConfigData()) {
//        AddLogEntry("WARNING", u8"通道配置数据加载失败，将使用默认配置");
//    }

    bool success = false;
    // 仅导出为JSON格式
    success = SaveProjectToJSON_1_2(filePath);

    // 确保文件扩展名为.json
    QFileInfo fileInfo(filePath);
    if (fileInfo.suffix().toLower() != "json") {
        AddLogEntry("WARNING", u8"文件名没有.json后缀，建议添加以确保兼容性");
    }

    if (success) {
        AddLogEntry("INFO", QString(u8"项目数据导出 (带通道配置)成功: %1").arg(filePath));
    } else {
        AddLogEntry("ERROR", QString(u8"项目数据导出 (带通道配置)失败: %1").arg(filePath));
    }

    return success;
}

///**
// * @brief 加载通道配置数据
// * @return 加载成功返回true
// */
//bool CMyMainWindow::loadChannelConfigData() {
//    // 实现从channel_config.json加载通道配置的逻辑
//    QString configPath = QCoreApplication::applicationDirPath() + "/channel_config.json";
//    QFile file(configPath);

//    if (!file.exists()) {
//        AddLogEntry("ERROR", QString(u8"通道配置文件不存在: %1").arg(configPath));
//        return false;
//    }

//    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
//        AddLogEntry("ERROR", QString(u8"无法打开通道配置文件: %1").arg(configPath));
//        return false;
//    }

//    QByteArray data = file.readAll();
//    file.close();

//    QJsonDocument doc = QJsonDocument::fromJson(data);
//    if (doc.isNull()) {
//        AddLogEntry("ERROR", u8"通道配置文件不是有效的JSON格式");
//        return false;
//    }

//    QJsonObject rootObj = doc.object();
//    channelConfigData_ = rootObj;

//    AddLogEntry("INFO", QString(u8"成功加载通道配置数据: %1").arg(configPath));
//    return true;
//}

bool CMyMainWindow::integrateChannelConfigToProject_1_2() {
//    if (channelConfigData_.isEmpty()) {
//        AddLogEntry("WARNING", tr("通道配置数据为空，跳过集成"));
//        return false;
//    }
    
    try {
        // 创建控制通道组JSON
        QJsonObject channelGroups = createControlChannelGroupsJson_1_2();
        
        // 如果项目中没有控制通道数据，则添加
        if (!currentProject_) {
            AddLogEntry("ERROR", tr("当前项目为空，无法集成通道配置"));
            return false;
        }
        
//        // 将通道配置数据设置到JSON导出器
//        if (jsonDataExporter_) {
//            jsonDataExporter_->setChannelConfigData(channelConfigData_);
//            AddLogEntry("INFO", tr("通道配置数据已集成到JSON导出器"));
//        }
        
        return true;
    } catch (const std::exception& e) {
        AddLogEntry("ERROR", QString("集成通道配置失败: %1").arg(e.what()));
        return false;
    }
}

QJsonObject CMyMainWindow::createControlChannelGroupsJson_1_2() {
    QJsonObject result;
    
//    if (!channelConfigData_.contains("channels")) {
//        return result;
//    }
    
    QJsonArray channelsArray;// = channelConfigData_["channels"].toArray();
    QJsonArray controlChannelGroups;
    
    // 遍历channel_config.json中的通道配置
    for (const QJsonValue& channelValue : channelsArray) {
        QJsonObject channel = channelValue.toObject();
        
        QJsonObject controlChannelGroup;
        controlChannelGroup["channelId"] = channel["id"];
        controlChannelGroup["channelName"] = channel["name"];
        controlChannelGroup["controlMode"] = channel["control_mode"];
        controlChannelGroup["enable"] = channel["enable"];
        
        // 极性配置
        QJsonObject polarityConfig;
        polarityConfig["servo_control_polarity"] = channel["servo_control_polarity"];
        polarityConfig["payload_sensor1_polarity"] = channel["payload_sensor1_polarity"];
        polarityConfig["payload_sensor2_polarity"] = channel["payload_sensor2_polarity"];
        polarityConfig["position_sensor_polarity"] = channel["position_sensor_polarity"];
        controlChannelGroup["polarity_config"] = polarityConfig;
        
        // 作动器配置
        if (channel.contains("servo_control")) {
            controlChannelGroup["servo_control"] = channel["servo_control"];
        }
        
        // 传感器配置
        if (channel.contains("payload_sensor1")) {
            controlChannelGroup["payload_sensor1"] = channel["payload_sensor1"];
        }
        if (channel.contains("payload_sensor2")) {
            controlChannelGroup["payload_sensor2"] = channel["payload_sensor2"];
        }
        if (channel.contains("position_sensor")) {
            controlChannelGroup["position_sensor"] = channel["position_sensor"];
        }
        
        controlChannelGroups.append(controlChannelGroup);
    }
    
    result["control_channel_groups"] = controlChannelGroups;
    return result;
}

bool CMyMainWindow::SaveProjectToXLS(const QString& filePath) {
    if (!xlsDataExporter_) {
        AddLogEntry("ERROR", QString(u8"XLS导出器未初始化，无法保存项目"));
        return false;
    }

    AddLogEntry("INFO", QString(u8"开始保存项目到XLS: %1 (完全基于内存数据)").arg(filePath));

    try {
        // 🎯 修复：保存XLSX文件时，完全以DataManager为准，不从UI硬件树同步
        // 传感器数据：以sensorDataManager_为准
        // 作动器数据：以actuatorDataManager_为准
        // 硬件节点数据：以hardwareNodeResDataManager_为准

        // 🆕 新增：设置作动器数据管理器到导出器
        if (actuatorViewModel1_2_) {
            xlsDataExporter_->setActuatorDataManager(actuatorViewModel1_2_->getDataManager());
            int actuatorGroupCount = actuatorViewModel1_2_->getAllActuatorGroups().size();
            AddLogEntry("INFO", QString(u8"设置作动器数据管理器到导出器: %1个作动器组").arg(actuatorGroupCount));
        }

        // 🔄 修正：设置硬件节点配置数据到导出器，确保从内存获取而不是从界面
        if (hardwareNodeResDataManager_) {
            QList<UI::NodeConfigParams> nodeConfigs = hardwareNodeResDataManager_->getAllHardwareNodeConfigs();
            xlsDataExporter_->setHardwareNodeConfigs(nodeConfigs);
            AddLogEntry("INFO", QString(u8"设置硬件节点配置数据到导出器: %1个配置").arg(nodeConfigs.size()));
        }

        // 🆕 新增：设置控制通道数据管理器到导出器
        if (ctrlChanDataManager_) {
            // 🆕 修复：检查控制通道数据，但不自动创建默认数据
            auto channelGroups = ctrlChanDataManager_->getAllControlChannelGroups();
            
            if (channelGroups.isEmpty()) {
                AddLogEntry("WARNING", QString(u8"⚠️ 控制通道数据为空，导出的Excel文件将不包含控制通道配置"));
                // 🆕 重要修复：不再自动创建默认控制通道组，让用户自己决定
            } else {
                AddLogEntry("INFO", QString(u8"🔍 控制通道数据管理器已有%1个组，使用现有数据导出").arg(channelGroups.size()));
            }
            
            xlsDataExporter_->setCtrlChanDataManager(ctrlChanDataManager_.get());
            int channelGroupCount = channelGroups.size();
            
            // 🆕 增强调试：输出控制通道详细信息
            AddLogEntry("INFO", QString(u8"设置控制通道数据管理器到导出器: %1个通道组").arg(channelGroupCount));
            for (int i = 0; i < channelGroups.size(); ++i) {
                const UI::ControlChannelGroup& group = channelGroups[i];
                AddLogEntry("DEBUG", QString(u8"  通道组%1: ID=%2, 名称='%3', 通道数=%4")
                           .arg(i+1).arg(group.groupId).arg(QString::fromStdString(group.groupName)).arg(group.channels.size()));
            }
        } else {
            AddLogEntry("WARNING", QString(u8"控制通道数据管理器未初始化，无法设置到导出器"));
        }

        // 🆕 新增：设置硬件节点资源数据管理器到导出器
        if (hardwareNodeResDataManager_) {
            xlsDataExporter_->setHardwareNodeResDataManager(hardwareNodeResDataManager_.get());
            int nodeCount = hardwareNodeResDataManager_->getAllHardwareNodeConfigs().size();
            AddLogEntry("INFO", QString(u8"设置硬件节点资源数据管理器到导出器: %1个节点").arg(nodeCount));
        }

        // 🆕 新增：保存前强制重新分配所有传感器组的ID，确保Excel中ID连续递增
        if (sensorDataManager_) {
            // 先验证当前ID连续性
            QStringList idIssues = sensorDataManager_->validateAllSensorIdSequences();
            if (!idIssues.isEmpty()) {
                AddLogEntry("WARNING", QString(u8"⚠️ 检测到传感器ID不连续问题，将自动修复："));
                for (const QString& issue : idIssues) {
                    AddLogEntry("WARNING", QString(u8"  %1").arg(issue));
                }
            }
            
            // 重新分配所有组的ID
            reassignAllSensorGroupIds();
            
            // 验证修复结果
            QStringList remainingIssues = sensorDataManager_->validateAllSensorIdSequences();
            if (remainingIssues.isEmpty()) {
                AddLogEntry("SUCCESS", QString(u8"✅ 所有传感器组ID现在都是连续的"));
            } else {
                AddLogEntry("ERROR", QString(u8"❌ 仍有传感器ID连续性问题未解决"));
            }
        }

        // 使用标准的完整项目导出（直接从DataManager获取数据）
        bool success = xlsDataExporter_->exportCompleteProject(filePath);

        // 记录保存的数据统计（完全基于DataManager）
        int sensorCount = sensorDataManager_ ? sensorDataManager_->getAllSensors().size() : 0;
        int actuatorCount = actuatorViewModel1_2_ ? actuatorViewModel1_2_->getAllActuatorGroups().size() : 0;
        int hardwareNodeCount = hardwareNodeResDataManager_ ? hardwareNodeResDataManager_->getAllHardwareNodeConfigs().size() : 0;
        int controlChannelCount = ctrlChanDataManager_ ? ctrlChanDataManager_->getAllControlChannelGroups().size() : 0;
        AddLogEntry("INFO", QString(u8"保存完整项目数据 - 传感器: %1个, 作动器组: %2个, 硬件节点: %3个, 控制通道组: %4个 (数据来源: DataManager)")
                   .arg(sensorCount).arg(actuatorCount).arg(hardwareNodeCount).arg(controlChannelCount));

        if (success) {
            AddLogEntry("INFO", QString(u8"项目保存到XLS成功: %1").arg(filePath));

            // 更新项目路径
            if (currentProject_) {
                currentProject_->projectPath = filePath.toLocal8Bit().constData();
            }

            return true;
        } else {
            QString errorMsg = xlsDataExporter_->getLastError();
            AddLogEntry("ERROR", QString(u8"项目保存到XLS失败: %1 - %2").arg(filePath).arg(errorMsg));
            return false;
        }
    } catch (const std::exception& e) {
        AddLogEntry("ERROR", QString(u8"保存XLS项目时发生异常: %1").arg(e.what()));
        return false;
    }
}

void CMyMainWindow::reassignAllSensorGroupIds() {
    if (!sensorDataManager_) {
        AddLogEntry("WARNING", QString(u8"传感器数据管理器未初始化，无法重新分配ID"));
        return;
    }
    
    // 获取所有传感器组
    QList<UI::SensorGroup_1_2> allGroups = sensorDataManager_->getAllSensorGroups();
    if (allGroups.isEmpty()) {
        AddLogEntry("INFO", QString(u8"没有传感器组需要重新分配ID"));
        return;
    }
    
    int totalReassignedGroups = 0;
    int totalReassignedSensors = 0;
    
    AddLogEntry("INFO", QString(u8"🔄 开始重新分配所有传感器组的ID（共%1个组）").arg(allGroups.size()));
    
    for (UI::SensorGroup_1_2& group : allGroups) {
        if (group.sensors.isEmpty()) {
            continue;  // 跳过空组
        }
        
        // 检查是否需要重新分配
        bool needsReassignment = false;
        for (int i = 0; i < group.sensors.size(); ++i) {
            if (group.sensors[i].sensorId != i + 1) {
                needsReassignment = true;
                break;
            }
        }
        
        if (needsReassignment) {
            AddLogEntry("DEBUG", QString(u8"  📋 重新分配组ID=%1, 名称='%2', 传感器数=%3")
                       .arg(group.groupId).arg(group.groupName).arg(group.sensors.size()));
            
            // 重新分配组内传感器ID（从1开始连续递增）
            for (int i = 0; i < group.sensors.size(); ++i) {
                int oldId = group.sensors[i].sensorId;
                group.sensors[i].sensorId = i + 1;
                
                if (oldId != i + 1) {
                    AddLogEntry("DEBUG", QString(u8"    ✅ 传感器'%1': ID %2 → %3")
                               .arg(group.sensors[i].params_sn).arg(oldId).arg(i + 1));
                }
            }
            
            // 保存更新后的组
            if (sensorDataManager_->saveSensorGroup(group)) {
                totalReassignedGroups++;
                totalReassignedSensors += group.sensors.size();
                AddLogEntry("INFO", QString(u8"  ✅ 组ID=%1重新分配完成，传感器数=%2")
                           .arg(group.groupId).arg(group.sensors.size()));
            } else {
                AddLogEntry("ERROR", QString(u8"  ❌ 组ID=%1重新分配失败: %2")
                           .arg(group.groupId).arg(sensorDataManager_->getLastError()));
            }
        } else {
            AddLogEntry("DEBUG", QString(u8"  ✓ 组ID=%1已是连续ID，无需重新分配").arg(group.groupId));
        }
    }
    
    AddLogEntry("SUCCESS", QString(u8"🎉 传感器组ID重新分配完成：%1个组已更新，共%2个传感器")
               .arg(totalReassignedGroups).arg(totalReassignedSensors));
}

bool CMyMainWindow::LoadProjectFromXLS(const QString& filePath) {
    if (!xlsDataExporter_) {
        AddLogEntry("ERROR", QString(u8"XLS导出器未初始化，无法加载项目"));
        return false;
    }

    AddLogEntry("INFO", QString(u8"开始从XLS加载项目: %1 (完全基于内存数据)").arg(filePath));

    // 🆕 新增：创建进度对话框
    QProgressDialog progressDialog(this);
    progressDialog.setWindowTitle(tr("导入工程"));
    progressDialog.setLabelText(tr("正在导入工程文件，请稍候..."));
    progressDialog.setRange(0, 100);
    progressDialog.setModal(true);
    progressDialog.show();
    QApplication::processEvents();

    try {
        // 检查当前界面是否有数据，如果有则提示保存
        progressDialog.setValue(10);
        progressDialog.setLabelText(tr("检查当前数据..."));
        QApplication::processEvents();

        if (!PromptSaveIfNeeded()) {
            progressDialog.close();
            return false; // 用户取消操作
        }

        // 清空当前界面数据
        progressDialog.setValue(20);
        progressDialog.setLabelText(tr("清空当前界面数据..."));
        QApplication::processEvents();
        ClearInterfaceData();

        // 🆕 修改：使用新的工程导入方法，添加超时检查
        progressDialog.setValue(30);
        progressDialog.setLabelText(tr("正在读取Excel文件..."));
        QApplication::processEvents();

        // 设置超时定时器
        QTimer timeoutTimer;
        timeoutTimer.setSingleShot(true);
        bool importTimedOut = false;

        connect(&timeoutTimer, &QTimer::timeout, [&importTimedOut]() {
            importTimedOut = true;
        });

        timeoutTimer.start(30000); // 30秒超时

        // 🆕 新增：在导入前设置数据管理器到导出器
        // 注意：传感器数据管理器通过构造函数设置，不需要单独设置
        if (actuatorViewModel1_2_) {
            xlsDataExporter_->setActuatorDataManager(actuatorViewModel1_2_->getDataManager());
        }
        if (ctrlChanDataManager_) {
            xlsDataExporter_->setCtrlChanDataManager(ctrlChanDataManager_.get());
        }
        if (hardwareNodeResDataManager_) {
            xlsDataExporter_->setHardwareNodeResDataManager(hardwareNodeResDataManager_.get());
            AddLogEntry("INFO", QString(u8"已设置硬件节点资源数据管理器到导入器"));
        }

        bool success = xlsDataExporter_->importProject(filePath);
        timeoutTimer.stop();

        if (importTimedOut) {
            progressDialog.close();
            AddLogEntry("ERROR", QString(u8"导入超时: %1").arg(filePath));
            QMessageBox::critical(this, tr("导入失败"),
                tr("导入工程文件超时！\n文件可能过大或损坏。"));
            return false;
        }

        if (success) {
            progressDialog.setValue(70);
            progressDialog.setLabelText(tr("正在刷新界面显示..."));
            QApplication::processEvents();

            // 🆕 核心修复：强制数据同步，确保所有DataManager的数据一致性
            progressDialog.setValue(72);
            progressDialog.setLabelText(tr("正在同步数据管理器..."));
            QApplication::processEvents();
            
            bool syncSuccess = SynchronizeAllDataManagers();
            if (!syncSuccess) {
                AddLogEntry("WARNING", QString(u8"数据同步过程中发现问题，但继续进行"));
            }

            // 🆕 注释：硬件节点数据现在直接在importHardwareNodeDetails中保存到数据管理器
            // 不再需要手动同步步骤

            // 🆕 新增：导入成功后验证数据
            progressDialog.setValue(75);
            progressDialog.setLabelText(tr("正在验证导入数据..."));
            QApplication::processEvents();

            // 验证各数据管理器中的数据
            int actuatorGroupCount = actuatorViewModel1_2_ ? actuatorViewModel1_2_->getAllActuatorGroups().size() : 0;
            int sensorGroupCount = sensorDataManager_ ? sensorDataManager_->getAllSensorGroups().size() : 0;
            int hardwareNodeCount = hardwareNodeResDataManager_ ? hardwareNodeResDataManager_->getAllHardwareNodeConfigs().size() : 0;
            int channelGroupCount = ctrlChanDataManager_ ? ctrlChanDataManager_->getAllControlChannelGroups().size() : 0;

            AddLogEntry("INFO", QString(u8"🔍 数据验证：作动器组=%1，传感器组=%2，硬件节点=%3，控制通道组=%4")
                       .arg(actuatorGroupCount).arg(sensorGroupCount).arg(hardwareNodeCount).arg(channelGroupCount));

            // 🆕 新增：详细的数据验证
            bool dataValid = ValidateImportedData();
            if (!dataValid) {
                AddLogEntry("WARNING", QString(u8"导入数据验证发现问题，请检查日志"));
            }

            // 🆕 新增：确保JSON导出器与数据管理器同步
            progressDialog.setValue(78);
            progressDialog.setLabelText(tr("正在同步JSON导出器..."));
            QApplication::processEvents();
            SynchronizeJSONExporterWithDataManagers();

                // 🆕 重要修复：设置当前项目路径以防止后续创建默认数据
    currentProjectPath_ = filePath;
    
    // 创建新项目对象
    if (currentProject_) {
        delete currentProject_;
    }
    currentProject_ = new DataModels::TestProject();

    // 设置项目基本信息
    QFileInfo fileInfo(filePath);
    QString projectName = fileInfo.baseName();
    currentProject_->projectName = projectName.toStdString();
    currentProject_->description = u8"从Excel文件导入的实验工程";
    currentProject_->createdDate = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss").toStdString();
    currentProject_->version = "1.0.0";
    currentProject_->projectPath = filePath.toLocal8Bit().constData();

    progressDialog.setValue(80);
    progressDialog.setLabelText(tr("正在刷新界面显示..."));
    QApplication::processEvents();

    // 🆕 新增：导入成功后刷新界面显示（只从数据管理器读取，不创建默认数据）
    refreshAllDataFromManagers();

    progressDialog.setValue(90);
    progressDialog.setLabelText(tr("正在更新界面显示..."));
    QApplication::processEvents();

    // 更新界面显示（使用RefreshTestConfigTreeFromDataManagers而非RefreshTestConfigTreeWidget）
    RefreshHardwareTreeFromDataManagers();
    RefreshTestConfigTreeFromDataManagers();

            // 🔧 修复：重新启用拖拽功能
            EnableTestConfigTreeDragDrop();

            // 更新窗口标题
            setWindowTitle(QString("SiteResConfig - %1").arg(projectName));

            progressDialog.setValue(100);
            progressDialog.setLabelText(tr("导入完成"));
            QApplication::processEvents();

            // 延迟关闭进度对话框
            QTimer::singleShot(500, &progressDialog, &QProgressDialog::close);

            AddLogEntry("INFO", QString(u8"从XLS加载项目成功: %1").arg(filePath));

            // 🆕 修复：确保数据完全加载后再通知项目打开
            QTimer::singleShot(100, this, [this, filePath, projectName]() {
                // 延迟执行，确保所有数据加载完成
                OnProjectOpened(filePath, projectName);
            });

            return true;
        } else {
            progressDialog.close();

            // 🆕 修改：使用新的导入错误信息
            QString errorMsg = xlsDataExporter_->getImportError();
            if (errorMsg.isEmpty()) {
                errorMsg = xlsDataExporter_->getLastError();
            }
            AddLogEntry("ERROR", QString(u8"从XLS加载项目失败: %1 - %2").arg(filePath).arg(errorMsg));

            // 显示错误对话框
            QMessageBox::critical(this, tr("导入失败"),
                QString(u8"导入工程文件失败：\n%1").arg(errorMsg));
            return false;
        }
    } catch (const std::exception& e) {
        progressDialog.close();
        AddLogEntry("ERROR", QString(u8"加载XLS项目时发生异常: %1").arg(e.what()));
        QMessageBox::critical(this, tr("导入异常"),
            QString(u8"导入过程中发生异常：\n%1").arg(e.what()));
        return false;
    } catch (...) {
        progressDialog.close();
        AddLogEntry("ERROR", QString(u8"加载XLS项目时发生未知异常"));
        QMessageBox::critical(this, tr("导入异常"),
            tr("导入过程中发生未知异常！"));
        return false;
    }
}

QString CMyMainWindow::ExtractParameterFromTooltip(const QString& tooltip, const QString& parameterName) {
    QStringList lines = tooltip.split('\n');
    for (const QString& line : lines) {
        if (line.contains(parameterName + ":")) {
            QStringList parts = line.split(':', QString::SkipEmptyParts);
            if (parts.size() >= 2) {
                return parts[1].trimmed();
            }
        }
    }
    return QString();
}

int CMyMainWindow::GetItemLevel(QTreeWidgetItem* item) {
    int level = 0;
    QTreeWidgetItem* parent = item->parent();
    while (parent) {
        level++;
        parent = parent->parent();
    }
    return level;
}

void CMyMainWindow::HideUnusedMenuItems() {
    // 修改文件菜单项
    if (ui->actionSaveAsProject) {
        ui->actionSaveAsProject->setText(tr("导出工程(&E)..."));
        ui->actionSaveAsProject->setToolTip(tr("导出试验配置为JSON文件"));
    }
    if (ui->actionRecentProjects) ui->actionRecentProjects->setVisible(false);

    if (ui->actionTestConfig) ui->actionTestConfig->setVisible(false);

    // 隐藏视图菜单中的无用项
    if (ui->actionShowHardwarePanel) ui->actionShowHardwarePanel->setVisible(false);
    if (ui->actionShowTestPanel) ui->actionShowTestPanel->setVisible(false);
    if (ui->actionFullScreen) ui->actionFullScreen->setVisible(false);

    // 隐藏工具菜单中的无用项
    if (ui->actionSystemSettings) ui->actionSystemSettings->setVisible(false);
    if (ui->actionCalibration) ui->actionCalibration->setVisible(false);

    // 隐藏帮助菜单中的无用项
    if (ui->actionUserManual) ui->actionUserManual->setVisible(false);
    if (ui->actionTechnicalSupport) ui->actionTechnicalSupport->setVisible(false);

    // 完全隐藏某些菜单
    if (ui->menuTest) ui->menuTest->menuAction()->setVisible(false);
    if (ui->menuView) ui->menuView->menuAction()->setVisible(false);
    if (ui->menuTools) ui->menuTools->menuAction()->setVisible(false);
}

bool CMyMainWindow::IsNameExistsInTree(QTreeWidgetItem* parentItem, const QString& name) {
    if (!parentItem) return false;

    // 检查当前层级的所有子项
    for (int i = 0; i < parentItem->childCount(); ++i) {
        QTreeWidgetItem* child = parentItem->child(i);
        if (child && child->text(0) == name) {
            return true;
        }
    }
    return false;
}

// 🔄 已迁移：IsActuatorGroupNameExists功能已迁移到ActuatorViewModel1_2::isActuatorGroupNameExistsBusiness()

bool CMyMainWindow::IsSensorGroupNameExists(const QString& groupName) {
    if (!ui->hardwareTreeWidget) return false;

    // 获取任务1根节点
    QTreeWidgetItem* taskRoot = ui->hardwareTreeWidget->topLevelItem(0);
    if (!taskRoot) return false;

    // 获取传感器子节点（第2个子节点，索引为1）
    QTreeWidgetItem* sensorRoot = taskRoot->child(1);
    if (!sensorRoot) return false;

    return IsNameExistsInTree(sensorRoot, groupName);
}

bool CMyMainWindow::isSensorSerialNumberExistsInGroup(QTreeWidgetItem* groupItem, const QString& serialNumber) {
    if (!groupItem) return false;

    // 遍历传感器组内的所有传感器设备
    for (int i = 0; i < groupItem->childCount(); ++i) {
        QTreeWidgetItem* child = groupItem->child(i);
        if (child && child->data(0, Qt::UserRole).toString() == "传感器设备") {
            // 检查序列号是否重复（序列号就是节点的显示文本）
            if (child->text(0) == serialNumber) {
                return true; // 找到重复的序列号
            }
        }
    }

    return false; // 没有找到重复的序列号
}

// 🔄 已迁移：isActuatorSerialNumberExistsInGroup功能已迁移到ActuatorViewModel1_2::isSerialNumberUniqueInGroupBusiness()

// 🆕 新增：创建或更新传感器组
bool CMyMainWindow::createOrUpdateSensorGroup(QTreeWidgetItem* groupItem, const UI::SensorParams_1_2& params) {
    if (!groupItem || !sensorDataManager_) {
        return false;
    }

    // 🔧 修改：延迟添加到DataManager，先确定组内ID（参考作动器流程）
    UI::SensorParams_1_2 sensorWithId = params;

    // 🔄 修改：参考作动器流程，从组名称生成组ID
    QString groupName = groupItem->text(0);
    int groupId = generateSensorGroupIdFromName(groupName);
    if (groupId <= 0) {
        AddLogEntry("ERROR", QString(u8"无法为传感器组生成组ID: %1").arg(groupName));
        return false;
    }

    // 🔄 修改：检查传感器组是否已存在，如果不存在则创建
    UI::SensorGroup_1_2 group;
    bool groupExists = sensorDataManager_->hasSensorGroup(groupId);
    
    if (groupExists) {
        // 获取现有组
        group = sensorDataManager_->getSensorGroup(groupId);

        // 检查传感器是否已在组中
        bool sensorExistsInGroup = sensorDataManager_->hasSensorInGroup(groupId, sensorWithId.params_sn);
        
        if (sensorExistsInGroup) {
            // 🔧 修改：更新现有传感器时保持原有的组内ID
            UI::SensorParams_1_2 existingSensor = sensorDataManager_->getSensorInGroup(groupId, sensorWithId.params_sn);
            sensorWithId.sensorId = existingSensor.sensorId; // 保持原有的组内ID
            
            // 在组数据中更新传感器
            for (int i = 0; i < group.sensors.size(); ++i) {
                if (group.sensors[i].params_sn == sensorWithId.params_sn) {
                    group.sensors[i] = sensorWithId;
                    break;
                }
            }
            
            AddLogEntry("INFO", QString(u8"更新传感器，保持组内ID: %1 → ID=%2")
                       .arg(sensorWithId.params_sn).arg(sensorWithId.sensorId));
        } else {
            // 🆕 新增：如果传感器不存在，先分配组内ID
            // 🔧 修改：统一使用连续分配策略，确保组内ID从1开始连续递增
            sensorWithId.sensorId = group.sensors.size() + 1; // 组内连续递增ID
            group.sensors.append(sensorWithId);
            
            AddLogEntry("INFO", QString(u8"为传感器分配组内连续ID: %1 → ID=%2")
                       .arg(sensorWithId.params_sn).arg(sensorWithId.sensorId));
        }
    } else {
        // 🆕 新增：创建新的传感器组（参考作动器流程）
        group.groupId = groupId;
        group.groupName = groupName;

        // 从组名称中提取传感器类型
        QString groupType = groupName;
        if (groupName.endsWith("_传感器组")) {
            groupType = groupName.left(groupName.length() - 5); // 移除"_传感器组"后缀
        }
        group.groupType = groupType;

        group.createTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
        group.groupNotes = QString(u8"自动创建的传感器组");
        
        // 🆕 新增：为新组中的第一个传感器分配ID=1
        sensorWithId.sensorId = 1; // 新组中第一个传感器ID为1
        group.sensors.append(sensorWithId);
        
        AddLogEntry("INFO", QString(u8"为新组中的传感器分配ID: %1 → ID=1")
                   .arg(sensorWithId.params_sn));
    }

    // 🔄 修改：保存传感器组（新建或更新）
    bool success;
    if (groupExists) {
        success = sensorDataManager_->updateSensorGroup(groupId, group);
    } else {
        success = sensorDataManager_->saveSensorGroup(group);
    }

    if (success) {
        AddLogEntry("INFO", QString(u8"传感器组保存成功: %1 (组ID: %2, 传感器数量: %3)")
                    .arg(groupName).arg(groupId).arg(group.sensors.size()));
        
        // 🆕 新增：将传感器添加到分层存储中
        if (!sensorDataManager_->addSensor(sensorWithId, groupId)) {
            AddLogEntry("ERROR", QString(u8"添加传感器到分层存储失败: %1").arg(sensorDataManager_->getLastError()));
            return false;
        }
        AddLogEntry("INFO", QString(u8"传感器已添加到分层存储: %1 (组ID: %2, 传感器ID: %3)")
                   .arg(sensorWithId.params_sn).arg(groupId).arg(sensorWithId.sensorId));
    } else {
        AddLogEntry("ERROR", QString(u8"传感器组保存失败: %1").arg(sensorDataManager_->getLastError()));
    }

    return success;
}

// 🔄 优化：从作动器组项目中提取组ID（使用ViewModel业务方法）
int CMyMainWindow::extractActuatorGroupIdFromItem(QTreeWidgetItem* groupItem) const {
    if (!groupItem || !actuatorViewModel1_2_) {
        return 0;
    }

    // 尝试从UserRole数据中获取组ID（在CreateActuatorGroupUI中设置）
    QVariant groupIdVariant = groupItem->data(1, Qt::UserRole);
    if (groupIdVariant.isValid()) {
        return groupIdVariant.toInt();
    }

    // 🔄 优化：使用ViewModel的业务方法查找组ID
    QString groupName = groupItem->text(0);
    return actuatorViewModel1_2_->extractGroupIdFromNameBusiness(groupName);
}

// 🆕 新增：从传感器组项目中提取组ID
int CMyMainWindow::extractSensorGroupIdFromItem(QTreeWidgetItem* groupItem) const {
    if (!groupItem) {
        return 0;
    }

    // 尝试从UserRole数据中获取组ID（在CreateSensorGroup中设置）
    QVariant groupIdVariant = groupItem->data(1, Qt::UserRole);
    if (groupIdVariant.isValid()) {
        return groupIdVariant.toInt();
    }

    // 如果没有存储组ID，尝试从组名称中查找
    QString groupName = groupItem->text(0);
    if (sensorDataManager_) {
        QList<UI::SensorGroup_1_2> allGroups = sensorDataManager_->getAllSensorGroups();
        for (const auto& group : allGroups) {
            if (group.groupName == groupName) {
                return group.groupId;
            }
        }
    }

    return 0; // 未找到组ID
}

// 🆕 新增：为传感器组生成组ID（参考作动器流程）
int CMyMainWindow::generateSensorGroupIdFromName(const QString& groupName) const {
    if (!sensorDataManager_) {
        return 0;
    }

    // 🔄 修复：首先检查是否已存在具有相同名称的组
    QList<UI::SensorGroup_1_2> existingGroups = sensorDataManager_->getAllSensorGroups();
    for (const UI::SensorGroup_1_2& group : existingGroups) {
        if (group.groupName == groupName) {
            AddLogEntry("INFO", QString(u8"找到现有传感器组: %1 → ID=%2").arg(groupName).arg(group.groupId));
            return group.groupId;
        }
    }

    // 🔧 修复：使用简单的递增ID方式，类似作动器组
    // 查找下一个可用的组ID
    int maxGroupId = 0;
    for (const UI::SensorGroup_1_2& existingGroup : existingGroups) {
        if (existingGroup.groupId > maxGroupId) {
            maxGroupId = existingGroup.groupId;
        }
    }
    int groupId = maxGroupId + 1;

    AddLogEntry("INFO", QString(u8"为传感器组生成新ID: %1 → %2").arg(groupName).arg(groupId));
    return groupId;
}

bool CMyMainWindow::IsHardwareNodeNameExists(const QString& nodeName) {
    if (!ui->hardwareTreeWidget) return false;

    // 获取任务1根节点
    QTreeWidgetItem* taskRoot = ui->hardwareTreeWidget->topLevelItem(0);
    if (!taskRoot) return false;

    // 获取硬件节点资源子节点（第3个子节点，索引为2）
    QTreeWidgetItem* hardwareRoot = taskRoot->child(2);
    if (!hardwareRoot) return false;

    return IsNameExistsInTree(hardwareRoot, nodeName);
}

bool CMyMainWindow::HasInterfaceData() {
    if (!ui->hardwareTreeWidget || !ui->testConfigTreeWidget) return false;

    // 检查硬件树是否有用户创建的数据
    QTreeWidgetItem* taskRoot = ui->hardwareTreeWidget->topLevelItem(0);
    if (taskRoot) {
        // 检查作动器节点是否有子组
        QTreeWidgetItem* actuatorRoot = taskRoot->child(0);
        if (actuatorRoot && actuatorRoot->childCount() > 0) {
            return true;
        }

        // 检查传感器节点是否有子组
        QTreeWidgetItem* sensorRoot = taskRoot->child(1);
        if (sensorRoot && sensorRoot->childCount() > 0) {
            return true;
        }

        // 检查硬件节点资源是否有子节点
        QTreeWidgetItem* hardwareRoot = taskRoot->child(2);
        if (hardwareRoot && hardwareRoot->childCount() > 0) {
            return true;
        }
    }

    // 检查试验配置树是否有用户创建的数据
    QTreeWidgetItem* testRoot = ui->testConfigTreeWidget->topLevelItem(0);
    if (testRoot) {
        // 检查指令节点是否有子项
        QTreeWidgetItem* commandRoot = testRoot->child(0);
        if (commandRoot && commandRoot->childCount() > 0) {
            return true;
        }
    }

    return false;
}

void CMyMainWindow::ClearInterfaceData() {
    if (!ui->hardwareTreeWidget || !ui->testConfigTreeWidget) return;

    AddLogEntry("INFO", tr("正在清空硬件树数据..."));

    // 完全清空硬件树
    ui->hardwareTreeWidget->clear();

    AddLogEntry("INFO", tr("正在清空试验配置树数据..."));

    // 完全清空试验配置树
    ui->testConfigTreeWidget->clear();

    AddLogEntry("INFO", tr("正在清空其他界面元素..."));

    // 清空其他可能的界面元素
    // 注意：不清空日志，保留操作记录

    AddLogEntry("INFO", tr("界面数据已完全清空"));

    // 🆕 新增：通知项目已关闭（如果当前有项目）
    if (hasActiveProject_) {
        OnProjectClosed();
    }
}

void CMyMainWindow::SetDefaultEmptyInterface() {
    AddLogEntry("INFO", tr("正在重新初始化界面..."));

    // 重新初始化为默认的空界面状态
    InitializeHardwareTree();
    InitializeTestConfigTree();

    AddLogEntry("INFO", tr("界面已重置为默认空白状态"));
}

bool CMyMainWindow::PromptSaveIfNeeded() {
    // 检查是否有未保存的数据
    if (!HasInterfaceData()) {
        return true; // 没有数据，可以直接继续
    }

    // 提示用户保存
    QMessageBox::StandardButton reply = QMessageBox::question(this,
        tr("保存工程"),
        tr("当前工程包含未保存的数据，是否需要保存？\n\n"
           "点击\"是\"保存当前工程\n"
           "点击\"否\"不保存直接继续\n"
           "点击\"取消\"返回当前工程"),
        QMessageBox::Yes | QMessageBox::No | QMessageBox::Cancel,
        QMessageBox::Yes);

    if (reply == QMessageBox::Cancel) {
        return false; // 用户取消操作
    } else if (reply == QMessageBox::Yes) {
        // 用户选择保存
        OnSaveProject();
        return true;
    } else {
        // 用户选择不保存，直接继续
        return true;
    }
}

void CMyMainWindow::ClearNodeAssociations(QTreeWidgetItem* node) {
    if (!node) return;

    // 清空当前节点的关联信息
    node->setText(1, "");

    // 递归清空所有子节点的关联信息
    for (int i = 0; i < node->childCount(); ++i) {
        ClearNodeAssociations(node->child(i));
    }
}

void CMyMainWindow::ForceRestoreAllTreeColors() {
    // 强制恢复硬件树的所有颜色
    if (ui->hardwareTreeWidget) {
        CustomHardwareTreeWidget* hardwareTree = qobject_cast<CustomHardwareTreeWidget*>(ui->hardwareTreeWidget);
        if (hardwareTree) {
            hardwareTree->forceRestoreAllColors();
        }
    }

    // 强制恢复配置树的所有颜色
    if (ui->testConfigTreeWidget) {
        CustomTestConfigTreeWidget* configTree = qobject_cast<CustomTestConfigTreeWidget*>(ui->testConfigTreeWidget);
        if (configTree) {
            configTree->forceRestoreAllColors();
        }
    }
}

CustomTestConfigTreeWidget* CMyMainWindow::getTestConfigTreeWidget() const {
    if (ui->testConfigTreeWidget) {
        return qobject_cast<CustomTestConfigTreeWidget*>(ui->testConfigTreeWidget);
    }
    return nullptr;
}

QTreeWidget* CMyMainWindow::getHardwareTreeWidget() const {
    return ui ? ui->hardwareTreeWidget : nullptr;
}

LogManager* CMyMainWindow::getLogManager() const {
    return logManager_.get();
}

DeviceManager* CMyMainWindow::getDeviceManager() const {
    return deviceManager_.get();
}

SensorDataManager_1_2* CMyMainWindow::getSensorDataManager() const {
    return sensorDataManager_.get();
}

ActuatorViewModel1_2* CMyMainWindow::getActuatorViewModel() const {
    return actuatorViewModel1_2_.get();
}

// ===== v3.4架构管理器访问接口实现 =====

ConfigManager* CMyMainWindow::getConfigManager() const {
    return configManager_wrapper_.get();
}

EventManager* CMyMainWindow::getEventManager() const {
    return eventManager_.get();
}

ExportManager* CMyMainWindow::getExportManager() const {
    return exportManager_.get();
}

ProjectManager* CMyMainWindow::getProjectManager() const {
    return projectManager_.get();
}

DialogManager* CMyMainWindow::getDialogManager() const {
    return dialogManager_.get();
}

TreeManager* CMyMainWindow::getTreeManager() const {
    return treeManager_.get();
}

InfoPanelManager* CMyMainWindow::getInfoPanelManager() const {
    return infoPanelManager_.get();
}

// ===== v3.4架构管理器初始化方法 =====

/**
 * @brief 初始化管理器依赖注入 (v3.4架构)
 * @details 按照设计方案设置现有组件的引用，不创建新组件
 */
void CMyMainWindow::initializeManagerDependencies() {
    qDebug() << "v3.4架构：开始初始化管理器依赖注入...";
    
    // 1. 日志管理器 - 设置主窗口引用 (使用现有AddLogEntry)
    if (logManager_) {
        logManager_->setMainWindow(this);
        qDebug() << "v3.4架构：LogManager 依赖注入完成";
    }
    
    // 2. 配置管理器 - 设置现有配置管理器 (使用现有Config::ConfigManager)
    if (configManager_wrapper_) {
        // 注意：这里需要在Initialize()中设置，因为configManager_在那里才初始化
        qDebug() << "v3.4架构：ConfigManager 包装器创建完成";
    }
    
    // 3. 设备管理器 - 设置现有数据管理器
    if (deviceManager_) {
        deviceManager_->setMainWindow(this);
        deviceManager_->setSensorDataManager(sensorDataManager_.get());
        if (actuatorViewModel1_2_) {
            deviceManager_->setActuatorDataManager(actuatorViewModel1_2_->getDataManager());
        }
        deviceManager_->setCtrlChanDataManager(ctrlChanDataManager_.get());
        deviceManager_->setHardwareNodeResDataManager(hardwareNodeResDataManager_.get());
        qDebug() << "v3.4架构：DeviceManager 依赖注入完成";
    }
    
    // 4. 导入导出管理器 - 设置现有数据管理器
    if (exportManager_) {
        if (actuatorViewModel1_2_) {
            exportManager_->setActuatorDataManager(actuatorViewModel1_2_->getDataManager());
        }
        exportManager_->setSensorDataManager(sensorDataManager_.get());
        exportManager_->setCtrlChanDataManager(ctrlChanDataManager_.get());
        exportManager_->setHardwareNodeResDataManager(hardwareNodeResDataManager_.get());
        qDebug() << "v3.4架构：ExportManager 依赖注入完成";
    }
    
    // 5. 树形控件管理器 - 设置现有树形控件 (需要在UI创建后)
    if (treeManager_) {
        treeManager_->setMainWindow(this);
        // 树形控件在UI创建后设置
        qDebug() << "v3.4架构：TreeManager 主窗口引用设置完成";
    }
    
    // 6. 项目管理器 - 设置主窗口引用
    if (projectManager_) {
        projectManager_->setMainWindow(this);
        qDebug() << "v3.4架构：ProjectManager 主窗口引用设置完成";
    }
    
    // 7. 信息面板管理器 - 设置现有信息面板 (需要在UI创建后)
    if (infoPanelManager_) {
        // 信息面板在UI创建后设置
        qDebug() << "v3.4架构：InfoPanelManager 创建完成";
    }
    
    // 8. UI管理器 - 初始化UI组件
    if (uiManager_) {
        // UI管理器负责UI初始化，替代原来的SetupUI()
        qDebug() << "v3.4架构：UIManager 创建完成";
    }
    
    qDebug() << "v3.4架构：管理器依赖注入初始化完成";
}

/**
 * @brief 完成管理器依赖注入 (在UI创建后调用)
 * @details 设置需要UI组件的管理器依赖
 */
void CMyMainWindow::completeManagerDependencies() {
    qDebug() << "v3.4架构：完成管理器依赖注入...";
    
    // UI管理器 - 执行UI初始化
    if (uiManager_) {
        uiManager_->setupUI();
        qDebug() << "v3.4架构：UIManager UI初始化完成";
    }
    
    // 树形控件管理器 - 设置现有树形控件
    if (treeManager_ && ui) {
        // 设置现有硬件树形控件
        CustomHardwareTreeWidget* hardwareTree = qobject_cast<CustomHardwareTreeWidget*>(ui->hardwareTreeWidget);
        if (hardwareTree) {
            treeManager_->setHardwareTreeWidget(hardwareTree);
        }
        
        // 设置现有测试配置树形控件
        CustomTestConfigTreeWidget* testConfigTree = qobject_cast<CustomTestConfigTreeWidget*>(ui->testConfigTreeWidget);
        if (testConfigTree) {
            treeManager_->setTestConfigTreeWidget(testConfigTree);
        }
        
        qDebug() << "v3.4架构：TreeManager 树形控件设置完成";
    }
    
    // 信息面板管理器 - 设置现有信息面板
    if (infoPanelManager_) {
        // 基础信息控件将在项目加载时设置
        // 详细信息面板将在创建时设置
        qDebug() << "v3.4架构：InfoPanelManager 信息面板准备完成";
    }
    
    qDebug() << "v3.4架构：管理器依赖注入完成";
}

/**
 * @brief 连接管理器信号槽 (v3.4架构)
 * @details 建立管理器之间的信号槽连接
 */
void CMyMainWindow::connectManagerSignals() {
    qDebug() << "v3.4架构：开始连接管理器信号槽...";
    
    // 1. 日志管理器信号连接
    if (logManager_) {
        connect(logManager_.get(), &LogManager::logAdded,
                this, [this](const QString& level, const QString& message) {
                    // 可以在这里添加额外的日志处理逻辑
                });
    }
    
    // 2. 事件管理器信号连接
    if (eventManager_) {
        connect(eventManager_.get(), &EventManager::eventProcessed,
                this, [this](const QString& eventType, const QVariant& data) {
                    qDebug() << "v3.4架构：事件处理完成 -" << eventType << "数据:" << data;
                });
    }
    
    // 3. 设备管理器信号连接
    if (deviceManager_) {
        connect(deviceManager_.get(), &DeviceManager::deviceCreated,
                this, [this](const QString& type, const QString& serialNumber) {
                    if (logManager_) {
                        logManager_->info(QString("设备创建: %1 - %2").arg(type).arg(serialNumber));
                    }
                });
        
        connect(deviceManager_.get(), &DeviceManager::deviceEdited,
                this, [this](const QString& type, const QString& serialNumber) {
                    if (logManager_) {
                        logManager_->info(QString("设备编辑: %1 - %2").arg(type).arg(serialNumber));
                    }
                });
        
        connect(deviceManager_.get(), &DeviceManager::deviceDeleted,
                this, [this](const QString& type, const QString& serialNumber) {
                    if (logManager_) {
                        logManager_->info(QString("设备删除: %1 - %2").arg(type).arg(serialNumber));
                    }
                });
        
        connect(deviceManager_.get(), &DeviceManager::deviceError,
                this, [this](const QString& error) {
                    if (logManager_) {
                        logManager_->error(QString("设备错误: %1").arg(error));
                    }
                });
    }
    
    // 4. 导入导出管理器信号连接
    if (exportManager_) {
        connect(exportManager_.get(), &ExportManager::importCompleted,
                this, [this](const QString& filePath, bool success) {
                    if (logManager_) {
                        QString message = success ? 
                            QString("导入完成: %1").arg(filePath) :
                            QString("导入失败: %1").arg(filePath);
                        logManager_->info(message);
                    }
                });
        
        connect(exportManager_.get(), &ExportManager::exportCompleted,
                this, [this](const QString& filePath, bool success) {
                    if (logManager_) {
                        QString message = success ? 
                            QString("导出完成: %1").arg(filePath) :
                            QString("导出失败: %1").arg(filePath);
                        logManager_->info(message);
                    }
                });
        
        connect(exportManager_.get(), &ExportManager::errorOccurred,
                this, [this](const QString& error) {
                    if (logManager_) {
                        logManager_->error(QString("导入导出错误: %1").arg(error));
                    }
                });
    }
    
    // 5. 项目管理器信号连接
    if (projectManager_) {
        connect(projectManager_.get(), &ProjectManager::projectOpened,
                this, [this](const QString& path, const QString& name) {
                    if (logManager_) {
                        logManager_->info(QString("项目打开: %1 - %2").arg(name).arg(path));
                    }
                    // 可以触发其他管理器的项目打开事件
                });
        
        connect(projectManager_.get(), &ProjectManager::projectClosed,
                this, [this]() {
                    if (logManager_) {
                        logManager_->info("项目关闭");
                    }
                });
        
        connect(projectManager_.get(), &ProjectManager::projectSaved,
                this, [this]() {
                    if (logManager_) {
                        logManager_->info("项目保存");
                    }
                });
        
        connect(projectManager_.get(), &ProjectManager::projectError,
                this, [this](const QString& error) {
                    if (logManager_) {
                        logManager_->error(QString("项目错误: %1").arg(error));
                    }
                });
    }
    
    // 6. 对话框管理器信号连接
    if (dialogManager_) {
        connect(dialogManager_.get(), &DialogManager::dialogAccepted,
                this, [this](const QString& dialogType, const QString& identifier) {
                    if (logManager_) {
                        logManager_->info(QString("对话框确认: %1 - %2").arg(dialogType).arg(identifier));
                    }
                });
        
        connect(dialogManager_.get(), &DialogManager::dialogRejected,
                this, [this](const QString& dialogType, const QString& identifier) {
                    if (logManager_) {
                        logManager_->info(QString("对话框取消: %1 - %2").arg(dialogType).arg(identifier));
                    }
                });
    }
    
    // 7. 树形控件管理器信号连接
    if (treeManager_) {
        connect(treeManager_.get(), &TreeManager::treeItemSelected,
                this, [this](const QString& treeType, QTreeWidgetItem* item) {
                    if (item && logManager_) {
                        logManager_->debug(QString("树形控件选择: %1 - %2").arg(treeType).arg(item->text(0)));
                    }
                });
        
        connect(treeManager_.get(), &TreeManager::treeItemDoubleClicked,
                this, [this](const QString& treeType, QTreeWidgetItem* item) {
                    if (item && logManager_) {
                        logManager_->debug(QString("树形控件双击: %1 - %2").arg(treeType).arg(item->text(0)));
                    }
                });
    }
    
    // 8. 信息面板管理器信号连接
    if (infoPanelManager_) {
        connect(infoPanelManager_.get(), &InfoPanelManager::infoUpdated,
                this, [this](const QString& infoType) {
                    if (logManager_) {
                        logManager_->debug(QString("信息面板更新: %1").arg(infoType));
                    }
                });
        
        connect(infoPanelManager_.get(), &InfoPanelManager::infoCleared,
                this, [this]() {
                    if (logManager_) {
                        logManager_->debug("信息面板清空");
                    }
                });
    }
    
    qDebug() << "v3.4架构：管理器信号槽连接完成";
}

/**
 * @brief 完成新项目设置 (v3.4架构)
 * @details 在ProjectManager创建新项目后，完成必要的初始化
 */
void CMyMainWindow::completeNewProjectSetup() {
    qDebug() << "v3.4架构：开始完成新项目设置...";
    
    // 生成默认工程名称：日期(年月日时分秒)+"_实验工程"
    QString timestamp = QDateTime::currentDateTime().toString("yyyyMMddhhmmss");
    QString defaultProjectName = QString("%1_实验工程").arg(timestamp);
    QString projectName = defaultProjectName;

    // 选择保存文件路径（包含文件名） - 默认使用XLS格式
    QString defaultFileName = projectName + ".xls";
    QString defaultPath = QDir(QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation)).filePath(defaultFileName);

    QString projectFilePath = QFileDialog::getSaveFileName(this,
        tr("选择实验工程保存位置"),
        defaultPath,
        tr("Excel文件 (*.xls);;Excel 2007+ (*.xlsx);;所有文件 (*.*)"));

    if (projectFilePath.isEmpty()) {
        // 用户取消操作，保持界面为空白状态
        if (logManager_) {
            logManager_->info(tr("用户取消选择保存路径，界面保持空白状态"));
        }
        return;
    }

    // 创建新的工程对象
    currentProject_ = new DataModels::TestProject();
    currentProject_->projectName = projectName.toStdString();
    currentProject_->description = "灵动加载试验工程";
    currentProject_->createdDate = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss").toStdString();
    currentProject_->version = "1.0.0";

    // 设置工程保存路径
    currentProject_->projectPath = projectFilePath.toLocal8Bit().constData();

    // 重新添加默认数据
    SetDefaultEmptyInterface();

    // 使用LogManager记录日志
    if (logManager_) {
        logManager_->info(QString("新建实验工程: %1").arg(projectName));
        logManager_->info(QString("工程保存路径: %1").arg(projectFilePath));
        logManager_->info(tr("界面已重新初始化完成"));
    }

    // 创建成功后立即保存文件 - 只支持XLS格式
    if (logManager_) {
        logManager_->info(tr("正在保存新建的工程文件..."));
    }
    
    bool saveSuccess = false;
    QFileInfo fileInfo(projectFilePath);
    QString extension = fileInfo.suffix().toLower();

    if (extension == "xlsx" || extension == "xls") {
        saveSuccess = SaveProjectToXLS(projectFilePath);
    } else {
        // 默认保存为XLS格式
        saveSuccess = SaveProjectToXLS(projectFilePath);
    }

    if (saveSuccess) {
        if (logManager_) {
            logManager_->info(tr("新建工程文件保存成功"));
        }
        QMessageBox::information(this, tr("创建成功"),
            QString("实验工程 '%1' 创建成功并已保存！\n保存路径: %2").arg(projectName).arg(projectFilePath));
    } else {
        if (logManager_) {
            logManager_->error(tr("新建工程文件保存失败"));
        }
        QMessageBox::warning(this, tr("创建成功"),
            QString("实验工程 '%1' 创建成功，但保存文件时出现错误！\n保存路径: %2\n请手动保存工程。").arg(projectName).arg(projectFilePath));
    }

    // 通知项目已打开
    OnProjectOpened(projectFilePath, projectName);
    
    // 确保作动器视图模型处于正确状态
    if (actuatorViewModel1_2_) {
        actuatorViewModel1_2_->clearMemoryData();
        if (logManager_) {
            logManager_->debug("✅ 新建工程后作动器视图模型已清空并重置");
        }
    } else {
        // 如果作动器视图模型为空，重新创建它
        if (logManager_) {
            logManager_->warning("⚠️ 作动器视图模型为空，正在重新创建...");
        }
        actuatorViewModel1_2_ = std::make_unique<ActuatorViewModel1_2>();
        if (actuatorViewModel1_2_) {
            // 重新连接信号
            connectActuatorViewModelSignals();
            if (logManager_) {
                logManager_->info("✅ 作动器视图模型重新创建成功");
            }
        } else {
            if (logManager_) {
                logManager_->error("❌ 作动器视图模型重新创建失败");
            }
        }
    }
    
    qDebug() << "v3.4架构：新项目设置完成";
}

bool CMyMainWindow::canAcceptDrop(QTreeWidgetItem* targetItem, const QString& sourceType) const {
    if (!targetItem) return false;

    QString targetText = targetItem->text(0);

    // 严格按照约束条件进行验证：

    // 1. 作动器设备 -> 只能关联到"控制"节点
    if (sourceType == "作动器设备") {
        return (targetText == "控制");
    }

    // 2. 传感器设备 -> 可以关联到"载荷1"、"载荷2"、"位置"节点
    if (sourceType == "传感器设备") {
        return (targetText == "载荷1" || targetText == "载荷2" || targetText == "位置");
    }

    // 3. 硬件节点通道 -> 只能关联到主通道节点（支持动态通道名称和数量）
    if (sourceType == "硬件节点通道") {
        // 🔧 修复：使用新的辅助方法判断主通道节点
        return isMainChannelNode(targetItem);
    }

    // 其他所有情况都不允许拖拽
    return false;
}

// 🔧 新增：检查目标节点是否是主通道节点
bool CMyMainWindow::isMainChannelNode(QTreeWidgetItem* item) const {
    if (!item || !item->parent()) return false;
    
    QString parentText = item->parent()->text(0);
    
    // 检查是否是控制通道下的主通道节点
    if (parentText == "控制通道") {
        // ✅ 允许拖拽到任何主通道节点（CH1、CH2、CH3...或用户自定义名称）
        // 不限制固定格式，支持动态通道名称和数量
        return true;
    }
    
    return false;  // ❌ 不允许拖拽到子通道节点（载荷1、载荷2、位置、控制）
}

void CMyMainWindow::HandleDragDropAssociation(QTreeWidgetItem* targetItem, const QString& sourceText, const QString& sourceType) {
    // 生成详细的关联信息，包含父节点信息
    QString detailedAssociationInfo = GenerateDetailedAssociationInfo(sourceText, sourceType);

    // 在第二列显示详细的关联信息
    targetItem->setText(1, detailedAssociationInfo);

    // 🆕 新增：保存拖拽关联信息到CtrlChanDataManager
    SaveDragDropAssociationToDataManager(targetItem, detailedAssociationInfo, sourceType);

    // 🆕 新增：拖拽完成后更新节点提示信息
    UpdateNodeTooltipAfterDragDrop(targetItem, detailedAssociationInfo, sourceType);

    // 记录关联操作日志
    AddLogEntry("INFO", QString("已关联 %1(%2) 到 %3")
                .arg(detailedAssociationInfo)
                .arg(sourceType)
                .arg(targetItem->text(0)));

    // 源节点保持原有状态，不做任何修改
    // 目标节点只更新第二列关联信息，保持原有树形结构

    // 拖拽关联完成后，强制恢复所有树形控件的颜色
    QTimer::singleShot(200, this, [this]() {
        ForceRestoreAllTreeColors();
    });
}

void CMyMainWindow::HandleDragDropAssociationWithParent(QTreeWidgetItem* targetItem, const QString& sourceText, const QString& sourceType, const QString& parentText) {
    // 直接使用父节点信息生成关联信息，无需查找
    QString detailedAssociationInfo;

    if (sourceType == "硬件节点通道" && !parentText.isEmpty()) {
        // 硬件节点通道：格式为 "LD-B1 - CH1"（使用传递的父节点信息）
        detailedAssociationInfo = QString("%1 - %2").arg(parentText).arg(sourceText);
    } else if (sourceType == "传感器设备" && !parentText.isEmpty()) {
        // 传感器设备：格式为 "传感器组 - 传感器名"
        detailedAssociationInfo = QString("%1 - %2").arg(parentText).arg(sourceText);
    } else if (sourceType == "作动器设备" && !parentText.isEmpty()) {
        // 作动器设备：格式为 "作动器组 - 作动器名"
        detailedAssociationInfo = QString("%1 - %2").arg(parentText).arg(sourceText);
    } else {
        // 其他情况或没有父节点信息时，回退到原始方法
        detailedAssociationInfo = GenerateDetailedAssociationInfo(sourceText, sourceType);
    }

    // 在第二列显示详细的关联信息
    targetItem->setText(1, detailedAssociationInfo);

    // 🆕 新增：保存拖拽关联信息到CtrlChanDataManager
    SaveDragDropAssociationToDataManager(targetItem, detailedAssociationInfo, sourceType);

    // 记录关联操作日志
    AddLogEntry("INFO", QString("已关联 %1(%2) 到 %3")
                .arg(detailedAssociationInfo)
                .arg(sourceType)
                .arg(targetItem->text(0)));

    // 拖拽关联完成后，强制恢复所有树形控件的颜色
    QTimer::singleShot(200, this, [this]() {
        ForceRestoreAllTreeColors();
    });
}

QString CMyMainWindow::GenerateDetailedAssociationInfo(const QString& sourceText, const QString& sourceType) {
    // 根据源节点的文本和类型，查找源节点并获取父节点信息
    QTreeWidgetItem* sourceItem = FindSourceItem(sourceText, sourceType);

    if (!sourceItem || !sourceItem->parent()) {
        // 如果找不到源节点或没有父节点，返回原始文本
        return sourceText;
    }

    QString parentText = sourceItem->parent()->text(0);
    QString detailedInfo;

    // 根据不同的源类型生成详细信息
    if (sourceType == "硬件节点通道") {
        // CH1/CH2节点：格式为 "LD-B1 - CH1"（显示父节点和通道名称）
        detailedInfo = QString("%1 - %2").arg(parentText).arg(sourceText);
    } else if (sourceType == "传感器设备") {
        // 传感器节点：格式为 "载荷_传感器组 - 传感器_000003"
        detailedInfo = QString("%1 - %2").arg(parentText).arg(sourceText);
    } else if (sourceType == "作动器设备") {
        // 作动器节点：格式为 "自定义_作动器组 - 作动器_000003"
        detailedInfo = QString("%1 - %2").arg(parentText).arg(sourceText);
    } else {
        // 其他类型：返回原始文本
        detailedInfo = sourceText;
    }

    return detailedInfo;
}

QTreeWidgetItem* CMyMainWindow::FindSourceItem(const QString& sourceText, const QString& sourceType) {
    // 在硬件配置树中查找匹配的源节点
    QTreeWidgetItem* rootItem = ui->hardwareTreeWidget->invisibleRootItem();
    return FindItemRecursive(rootItem, sourceText, sourceType);
}

QTreeWidgetItem* CMyMainWindow::FindItemRecursive(QTreeWidgetItem* parent, const QString& targetText, const QString& targetType) {
    if (!parent) return nullptr;

    // 检查当前节点
    if (parent->text(0) == targetText) {
        QString itemType = getItemType(parent);
        if (itemType == targetType) {
            return parent;
        }
    }

    // 递归检查子节点
    for (int i = 0; i < parent->childCount(); ++i) {
        QTreeWidgetItem* result = FindItemRecursive(parent->child(i), targetText, targetType);
        if (result) {
            return result;
        }
    }

    return nullptr;
}

bool CMyMainWindow::canDragItem(QTreeWidgetItem* item) const {
    if (!item) return false;

    QString itemType = getItemType(item);
    QString itemText = item->text(0);

    // 只允许以下类型的节点拖拽：
    // 1. 作动器设备（作动器节点下的具体作动器）
    // 2. 传感器设备（传感器节点下的具体传感器）
    // 3. 硬件节点通道（硬件节点下的CH1、CH2）
    return (itemType == "作动器设备" ||
            itemType == "传感器设备" ||
            itemType == "硬件节点通道");
}

QString CMyMainWindow::getItemType(QTreeWidgetItem* item) const {
    if (!item) return "";

    QVariant userData = item->data(0, Qt::UserRole);
    if (userData.isValid()) {
        return userData.toString();
    }

    // 如果没有UserRole数据，根据文本判断
    QString text = item->text(0);
    if (text == "CH1" || text == "CH2" || text == "通道1" || text == "通道2") {
        return "硬件节点通道";
    }

    return "";
}

// 硬件树右键菜单实现
void CMyMainWindow::OnHardwareTreeContextMenu(const QPoint& pos) {
    if (!ui->hardwareTreeWidget) return;

    QTreeWidgetItem* item = ui->hardwareTreeWidget->itemAt(pos);
    if (!item) return;

    // 获取节点类型（从UserRole数据中获取）
    QString nodeType = item->data(0, Qt::UserRole).toString();

    // 创建右键菜单
    QMenu contextMenu(this);

    // 根据节点类型显示不同的菜单
    if (nodeType == "作动器") {
        // 创建"新建"子菜单
        QMenu* newMenu = contextMenu.addMenu(tr("新建"));

        // 在"新建"子菜单中添加"作动器组"选项
        QAction* createGroupAction = newMenu->addAction(tr("作动器组"));
        connect(createGroupAction, &QAction::triggered, this, &CMyMainWindow::OnCreateActuatorGroup);

        // 隐藏快速创建作动器组菜单
        // contextMenu.addSeparator();
        // QMenu* predefinedGroupMenu = contextMenu.addMenu(tr("快速创建作动器组"));

    } else if (nodeType == "作动器组") {
        // 作动器组右键菜单
        QMenu* newMenu = contextMenu.addMenu(tr("新建"));

        // 在"新建"子菜单中添加"作动器"选项
        QAction* createActuatorAction = newMenu->addAction(tr("作动器"));
        connect(createActuatorAction, &QAction::triggered, [this, item]() {
            OnCreateActuator(item);
        });

    } else if (nodeType == "作动器组") {
        // 作动器组右键菜单
        QMenu* newMenu = contextMenu.addMenu(tr("新建"));

        // 在"新建"子菜单中添加"作动器"选项
        QAction* createActuatorAction = newMenu->addAction(tr("作动器"));
        connect(createActuatorAction, &QAction::triggered, [this, item]() {
            OnCreateActuator(item);
        });

    } else if (nodeType == "作动器设备") {
        // 🔧 修复：作动器设备右键菜单 - 添加"新建"菜单
        QMenu* newMenu = contextMenu.addMenu(tr("新建"));

        // 在"新建"子菜单中添加"作动器"选项
        QAction* createActuatorAction = newMenu->addAction(tr("作动器"));
        connect(createActuatorAction, &QAction::triggered, [this, item]() {
            // 获取父节点（作动器组）
            QTreeWidgetItem* parentGroup = item->parent();
            if (parentGroup) {
                OnCreateActuator(parentGroup);
            }
        });

        // 在"新建"子菜单中添加"作动器组"选项
        QAction* createActuatorGroupAction = newMenu->addAction(tr("作动器组"));
        connect(createActuatorGroupAction, &QAction::triggered, this, &CMyMainWindow::OnCreateActuatorGroup);

        contextMenu.addSeparator();

//        // 恢复：独立的servo_control导出功能
//        QMenu* exportMenu = contextMenu.addMenu(tr("导出"));
//        QAction* exportServoControlAction = exportMenu->addAction(tr("导出为ServoControl JSON"));
//        connect(exportServoControlAction, &QAction::triggered, [this, item]() {
//            // 设置当前选中项，然后调用导出方法
//            ui->hardwareTreeWidget->setCurrentItem(item);
//            OnExportActuatorAsServoControl();
//        });

        contextMenu.addSeparator();

        // 添加编辑菜单项
        QAction* editActuatorAction = contextMenu.addAction(tr("编辑作动器设备"));
        connect(editActuatorAction, &QAction::triggered, [this, item]() {
            OnEditActuatorDevice(item);
        });

        contextMenu.addSeparator();

        QAction* deleteActuatorAction = contextMenu.addAction(tr("删除作动器设备"));
        connect(deleteActuatorAction, &QAction::triggered, [this, item]() {
            OnDeleteActuatorDevice(item);
        });

    } else if (nodeType == "传感器组") {
        // 传感器组右键菜单
        QMenu* newMenu = contextMenu.addMenu(tr("新建"));

        // 在"新建"子菜单中添加"传感器"选项
        QAction* createSensorAction = newMenu->addAction(tr("传感器"));
        connect(createSensorAction, &QAction::triggered, [this, item]() {
            OnCreateSensor(item);
        });



    } else if (nodeType == "传感器设备") {
        // 🔧 修复：传感器设备右键菜单 - 添加"新建"菜单
        QMenu* newMenu = contextMenu.addMenu(tr("新建"));

        // 在"新建"子菜单中添加"传感器"选项
        QAction* createSensorAction = newMenu->addAction(tr("传感器"));
        connect(createSensorAction, &QAction::triggered, [this, item]() {
            // 获取父节点（传感器组）
            QTreeWidgetItem* parentGroup = item->parent();
            if (parentGroup) {
                OnCreateSensor(parentGroup);
            }
        });

        // 在"新建"子菜单中添加"传感器组"选项
        QAction* createSensorGroupAction = newMenu->addAction(tr("传感器组"));
        connect(createSensorGroupAction, &QAction::triggered, this, &CMyMainWindow::OnCreateSensorGroup);

        contextMenu.addSeparator();

        QAction* editSensorAction = contextMenu.addAction(tr("编辑传感器设备"));
        connect(editSensorAction, &QAction::triggered, [this, item]() {
            OnEditSensorDevice(item);
        });

        contextMenu.addSeparator();

        QAction* deleteSensorAction = contextMenu.addAction(tr("删除传感器设备"));
        connect(deleteSensorAction, &QAction::triggered, [this, item]() {
            OnDeleteSensorDevice(item);
        });

    } else if (nodeType == "传感器") {
        // 创建"新建"子菜单
        QMenu* newMenu = contextMenu.addMenu(tr("新建"));

        // 在"新建"子菜单中添加"传感器组"选项
        QAction* createGroupAction = newMenu->addAction(tr("传感器组"));
        connect(createGroupAction, &QAction::triggered, this, &CMyMainWindow::OnCreateSensorGroup);



    } else if (nodeType == "硬件节点资源") {
        // 创建"新建"子菜单
        QMenu* newMenu = contextMenu.addMenu(tr("新建"));

        // 在"新建"子菜单中添加"硬件节点"选项
        QAction* createNodeAction = newMenu->addAction(tr("硬件节点"));
        connect(createNodeAction, &QAction::triggered, this, &CMyMainWindow::OnCreateHardwareNode);
    } else if (nodeType == "硬件节点") {
        // 硬件节点右键菜单
        QAction* editNodeAction = contextMenu.addAction(tr("编辑硬件节点"));
        connect(editNodeAction, &QAction::triggered, [this, item]() {
            OnEditHardwareNode(item);
        });
        
        contextMenu.addSeparator();
        
        QAction* deleteNodeAction = contextMenu.addAction(tr("删除硬件节点"));
        connect(deleteNodeAction, &QAction::triggered, [this, item]() {
            OnDeleteHardwareNode(item);
        });
    }

    // 显示菜单
    if (!contextMenu.actions().isEmpty()) {
        contextMenu.exec(ui->hardwareTreeWidget->mapToGlobal(pos));
    }
}

// 试验配置树右键菜单实现
void CMyMainWindow::OnTestConfigTreeContextMenu(const QPoint& pos) {
    if (!ui->testConfigTreeWidget) return;

    QTreeWidgetItem* item = ui->testConfigTreeWidget->itemAt(pos);
    if (!item) return;

    // 获取节点类型（从UserRole数据中获取）
    QString nodeType = item->data(0, Qt::UserRole).toString();
    QString itemText = item->text(0);

    // 🔍 调试信息
    QString debugInfo = QString("右键菜单调试: 节点='%1', 类型='%2'").arg(itemText).arg(nodeType);
    if (item->parent()) {
        debugInfo += QString(", 父节点='%1'").arg(item->parent()->text(0));
    } else {
        debugInfo += ", 无父节点";
    }
    AddLogEntry("DEBUG", debugInfo);

    // 创建右键菜单
    QMenu contextMenu(this);

    // 根据节点类型显示不同的菜单
    // 🔧 修复：判断是否为控制通道相关节点
    bool isMainControlChannelNode = false;  // 控制通道主节点
    bool isSubControlChannelNode = false;   // 载荷1/载荷2/位置/控制子节点
    
    if (nodeType == "控制通道" && item->parent()) {
        QString parentText = item->parent()->text(0);
        
        // 检查是否为控制通道主节点（父节点是"控制通道"）
        if (parentText == tr("控制通道")) {
            isMainControlChannelNode = true;
            AddLogEntry("DEBUG", QString("识别为主控制通道节点: %1").arg(itemText));
        }
    } else if (nodeType == "载荷传感器" || nodeType == "位置传感器" || nodeType == "控制作动器") {
        // 检查是否为子节点（父节点有自己的父节点且父节点的父节点是"控制通道"）
        if (item->parent() && item->parent()->parent() && 
            item->parent()->parent()->text(0) == tr("控制通道")) {
            isSubControlChannelNode = true;
            AddLogEntry("DEBUG", QString("识别为子控制通道节点: %1 (父节点: %2)").arg(itemText).arg(item->parent()->text(0)));
        }
    }
    
    if (isMainControlChannelNode) {
        // 🎯 CH1/CH2 主节点右键菜单
        QAction* editChannelAction = contextMenu.addAction(tr("编辑通道配置"));
        connect(editChannelAction, &QAction::triggered, [this, item]() {
            OnEditControlChannelDetailed(item);
        });
        
        contextMenu.addSeparator();
        
        QAction* clearSingleAssociationAction = contextMenu.addAction(tr("删除关联信息"));
        connect(clearSingleAssociationAction, &QAction::triggered, [this, item]() {
            OnClearSingleAssociation(item);
        });
        
        QAction* clearAllAssociationAction = contextMenu.addAction(tr("删除所有关联信息"));
        connect(clearAllAssociationAction, &QAction::triggered, [this, item]() {
            OnClearAllAssociation(item);
        });
        
        contextMenu.addSeparator();
        
        // 🆕 新增：删除通道功能
        QAction* deleteChannelAction = contextMenu.addAction(tr("删除通道"));
        connect(deleteChannelAction, &QAction::triggered, [this, item]() {
            OnDeleteControlChannel(item);
        });
    } else if (isSubControlChannelNode) {
        // 🎯 子节点右键菜单（载荷1/载荷2/位置/控制）
        QAction* editChannelAction = contextMenu.addAction(tr("编辑通道配置"));
        connect(editChannelAction, &QAction::triggered, [this, item]() {
            // 子节点编辑时传递父节点（CH1/CH2）
            OnEditControlChannelDetailed(item->parent());
        });
        
        contextMenu.addSeparator();
        
        QAction* clearAssociationAction = contextMenu.addAction(tr("删除关联信息"));
        connect(clearAssociationAction, &QAction::triggered, [this, item]() {
            OnClearSingleAssociation(item);
        });
    } else {
        // 🆕 新增：为其他节点类型添加通用菜单
        QString nodeType = item->data(0, Qt::UserRole).toString();
        
        if (nodeType == "控制通道组") {
            // 控制通道组节点
            contextMenu.addSeparator();
            
            // 🆕 新增：新建控制通道功能
            QAction* createChannelAction = contextMenu.addAction(tr("新建控制通道"));
            connect(createChannelAction, &QAction::triggered, [this, item]() {
                OnCreateControlChannel(item);
            });
        }
    }

    // 显示菜单
    if (!contextMenu.actions().isEmpty()) {
        AddLogEntry("DEBUG", QString("显示右键菜单，包含 %1 个菜单项").arg(contextMenu.actions().size()));
        contextMenu.exec(ui->testConfigTreeWidget->mapToGlobal(pos));
    } else {
        AddLogEntry("DEBUG", QString("右键菜单为空，不显示菜单"));
    }
}

/**
 * @brief 试验配置树双击事件处理
 */
void CMyMainWindow::OnTestConfigTreeItemDoubleClicked(QTreeWidgetItem* item, int column) {
    if (!item) return;
    
    // 获取节点类型和文本
    QString nodeType = item->data(0, Qt::UserRole).toString();
    QString itemText = item->text(0);
    
    // 只有控制通道节点支持双击编辑
    if (nodeType == "试验节点" && (itemText == "CH1" || itemText == "CH2")) {
        AddLogEntry("INFO", QString(u8"🖱️ 双击编辑控制通道: %1").arg(itemText));
        OnEditControlChannelDetailed(item);
    }
}

/**
 * @brief 试验配置树单击事件处理（替代TreeInteractionHandler的onItemClicked功能）
 */
void CMyMainWindow::OnTestConfigTreeItemClicked(QTreeWidgetItem* item, int column) {
    if (!item) return;
    
    // 获取节点信息
    QString nodeName = item->text(0);
    QString nodeType = item->data(0, Qt::UserRole).toString();
    QString associationInfo = item->text(1);
    
    AddLogEntry("DEBUG", QString(u8"🖱️ 试验配置树节点单击: %1 (类型: %2)").arg(nodeName).arg(nodeType));
    
    // 根据节点类型处理不同的单击事件
    if (nodeType == "控制通道组") {
        // 控制通道根节点：显示控制通道组汇总信息
        AddLogEntry("INFO", QString(u8"🎛️ 显示控制通道组汇总信息"));
        ShowControlChannelGroupInfo();
    } else if (nodeType == "控制通道") {
        // 控制通道节点：显示控制通道详细信息
        AddLogEntry("INFO", QString(u8"🎛️ 显示控制通道详细信息: %1").arg(nodeName));
        ShowControlChannelDetailInfo(nodeName);
    } else if (nodeType == "载荷传感器" || nodeType == "位置传感器" || nodeType == "控制作动器") {
        // 传感器和作动器节点：显示父通道信息
        QTreeWidgetItem* parentItem = item->parent();
        if (parentItem && parentItem->data(0, Qt::UserRole).toString() == "控制通道") {
            QString parentChannelName = parentItem->text(0);
            AddLogEntry("INFO", QString(u8"🎯 显示父通道信息: %1 -> %2").arg(nodeName).arg(parentChannelName));
            ShowControlChannelDetailInfo(parentChannelName);
        } else {
            AddLogEntry("INFO", QString(u8"📋 显示传感器/作动器基本信息: %1").arg(nodeName));
            // 这里可以添加显示传感器/作动器基本信息的逻辑
        }
    } else if (nodeType == "试验节点") {
        // 其他试验节点：显示基本信息
        AddLogEntry("INFO", QString(u8"📋 显示试验节点基本信息: %1").arg(nodeName));
        // 这里可以添加显示试验节点基本信息的逻辑
    }
    
    // 🆕 新增：更新详细信息面板（如果有的话）
    if (detailInfoPanel_) {
        // 🚫 已移除：HTML格式详细信息功能
        // 使用纯文本方式显示节点信息
        QString nodeName = item->text(0);
        QString nodeType = item->data(0, Qt::UserRole).toString();
        QString associationInfo = item->text(1);
        
        // 创建纯文本信息
        QString detailInfo = QString("节点名称: %1\n节点类型: %2\n关联信息: %3")
                           .arg(nodeName)
                           .arg(nodeType)
                           .arg(associationInfo.isEmpty() ? "无" : associationInfo);
        
        // 使用现有的setNodeInfo方法或其他纯文本显示方法
        // detailInfoPanel_->setNodeInfo(...); // 需要根据实际接口调整
    }
}

/**
 * @brief 删除控制通道
 */
void CMyMainWindow::OnDeleteControlChannel(QTreeWidgetItem* item) {
    if (!item) return;
    
    QString channelId = item->text(0);
    QString channelIdData = item->data(1, Qt::UserRole).toString();
    
    // 使用存储的channelId数据，如果没有则使用显示文本
    if (!channelIdData.isEmpty()) {
        channelId = channelIdData;
    }
    
    // 确认删除
    int ret = QMessageBox::question(this, tr("确认删除"),
        QString("确定要删除控制通道 '%1' 吗？\n\n此操作将：\n- 删除该通道及其所有子节点\n- 清除相关的试验配置关联\n- 无法撤销").arg(channelId),
        QMessageBox::Yes | QMessageBox::No,
        QMessageBox::No);
    
    if (ret != QMessageBox::Yes) {
        return;
    }
    
    // 🔧 修复：从数据管理器中删除控制通道配置
    if (ctrlChanDataManager_) {
        // 查找并删除对应的控制通道组
        auto existingGroups = ctrlChanDataManager_->getAllControlChannelGroups();
        bool channelFound = false;
        
        // 遍历所有组查找要删除的通道
        for (int groupIndex = 0; groupIndex < existingGroups.size(); ++groupIndex) {
            const auto& group = existingGroups[groupIndex];
            for (const auto& channel : group.channels) {
                if (QString::fromStdString(channel.channelId) == channelId) {
                    // 使用数据管理器的删除方法
                    if (ctrlChanDataManager_->removeChannelFromGroup(groupIndex, channel.channelId)) {
                        AddLogEntry("INFO", QString("控制通道已从数据管理器中删除: %1").arg(channelId));
                        channelFound = true;
                    } else {
                        AddLogEntry("ERROR", QString("删除控制通道失败: %1").arg(channelId));
                    }
                    break;
                }
            }
            if (channelFound) break;
        }
        
        if (!channelFound) {
            AddLogEntry("WARNING", QString("未找到要删除的控制通道: %1").arg(channelId));
        }
    } else {
        AddLogEntry("WARNING", QString("控制通道数据管理器未初始化，无法删除数据: %1").arg(channelId));
    }
    
    // 从树形控件中删除节点
    QTreeWidgetItem* parent = item->parent();
    if (parent) {
        parent->removeChild(item);
        delete item;
        AddLogEntry("INFO", QString("控制通道已从界面中删除: %1").arg(channelId));
    }
    
    // 🆕 新增：删除控制通道后更新所有树形控件节点提示
    UpdateAllTreeWidgetTooltips();
}

/**
 * @brief 生成节点详细信息（已移除HTML格式，使用纯文本）
 */
QString CMyMainWindow::generateNodeDetailInfo(QTreeWidgetItem* item) {
    if (!item) return QString();
    
    QString nodeName = item->text(0);
    QString nodeType = item->data(0, Qt::UserRole).toString();
    QString associationInfo = item->text(1);
    
    QString detailInfo;
    
    if (nodeType == "控制通道组") {
        // 控制通道组信息
        detailInfo = QString(u8"🎛️ 控制通道组配置\n"
                           u8"组名称: %1\n"
                           u8"组类型: %2\n"
                           u8"子通道数量: %3\n"
                           u8"功能说明: 管理实验的控制通道配置，包含载荷、位置、控制等资源的关联关系")
                           .arg(nodeName)
                           .arg(nodeType)
                           .arg(item->childCount());
    } else if (nodeType == "控制通道") {
        // 控制通道信息
        detailInfo = QString(u8"🎛️ 控制通道配置\n"
                           u8"通道名称: %1\n"
                           u8"通道类型: %2\n"
                           u8"下位机ID: %3\n"
                           u8"站点ID: %4\n"
                           u8"启用状态: %5\n"
                           u8"子节点数量: %6\n"
                           u8"功能说明: 配置第%1个控制通道的资源关联，包含载荷、位置、控制等资源的配置")
                           .arg(nodeName)
                           .arg(nodeType)
                           .arg(item->text(2))
                           .arg(item->text(3))
                           .arg(item->text(4))
                           .arg(item->childCount())
                           .arg(nodeName);
    } else if (nodeType == "载荷传感器") {
        // 载荷传感器信息
        detailInfo = QString(u8"📊 载荷传感器配置\n"
                           u8"传感器名称: %1\n"
                           u8"传感器类型: %2\n"
                           u8"关联信息: %3\n"
                           u8"功能说明: 配置载荷传感器的关联关系，用于测量和控制载荷信号")
                           .arg(nodeName)
                           .arg(nodeType)
                           .arg(associationInfo.isEmpty() ? u8"未关联" : associationInfo);
    } else if (nodeType == "位置传感器") {
        // 位置传感器信息
        detailInfo = QString(u8"📏 位置传感器配置\n"
                           u8"传感器名称: %1\n"
                           u8"传感器类型: %2\n"
                           u8"关联信息: %3\n"
                           u8"功能说明: 配置位置传感器的关联关系，用于测量和控制位置信号")
                           .arg(nodeName)
                           .arg(nodeType)
                           .arg(associationInfo.isEmpty() ? u8"未关联" : associationInfo);
    } else if (nodeType == "控制作动器") {
        // 控制作动器信息
        detailInfo = QString(u8"⚙️ 控制作动器配置\n"
                           u8"作动器名称: %1\n"
                           u8"作动器类型: %2\n"
                           u8"关联信息: %3\n"
                           u8"功能说明: 配置控制作动器的关联关系，用于执行控制指令和动作")
                           .arg(nodeName)
                           .arg(nodeType)
                           .arg(associationInfo.isEmpty() ? u8"未关联" : associationInfo);
    } else if (nodeType == "试验节点") {
        // 其他试验节点信息
        detailInfo = QString(u8"📋 试验节点配置\n"
                           u8"节点名称: %1\n"
                           u8"节点类型: %2\n"
                           u8"子节点数量: %3\n"
                           u8"功能说明: 配置实验过程中的相关参数和设置")
                           .arg(nodeName)
                           .arg(nodeType)
                           .arg(item->childCount());
    } else {
        // 默认信息
        detailInfo = QString(u8"📋 节点信息\n"
                           u8"节点名称: %1\n"
                           u8"节点类型: %2\n"
                           u8"关联信息: %3")
                           .arg(nodeName)
                           .arg(nodeType)
                           .arg(associationInfo.isEmpty() ? u8"无" : associationInfo);
    }
    
    // 添加操作提示
    detailInfo += QString(u8"\n\n💡 操作提示:\n"
                         u8"• 右键点击可查看详细配置和操作选项\n"
                         u8"• 双击可编辑节点参数（如果支持）\n"
                         u8"• 拖拽可调整节点顺序");
    
    return detailInfo;
}

/**
 * @brief 新建控制通道
 */
void CMyMainWindow::OnCreateControlChannel(QTreeWidgetItem* parentItem) {
    if (!parentItem) return;
    
    // 获取当前通道数量，用于生成新的通道ID
    int currentChannelCount = parentItem->childCount();
    int newChannelNumber = currentChannelCount + 1;
    
    // 生成新的通道ID
    QString newChannelId = QString("CH%1").arg(newChannelNumber);
    
    // 检查通道ID是否已存在
    while (findChannelItem(newChannelId) != nullptr) {
        newChannelNumber++;
        newChannelId = QString("CH%1").arg(newChannelNumber);
    }
    
    // 创建新的通道节点
    QTreeWidgetItem* newChannelItem = new QTreeWidgetItem(parentItem);
    newChannelItem->setText(0, newChannelId);
    newChannelItem->setText(1, ""); // 启动时关联信息为空
    newChannelItem->setText(2, "1");    // 默认下位机ID
    newChannelItem->setText(3, "1");    // 默认站点ID
    newChannelItem->setText(4, u8"✅");  // 默认启用
    newChannelItem->setText(5, "");     // 第五列不显示值
    newChannelItem->setData(0, Qt::UserRole, "控制通道");
    newChannelItem->setData(1, Qt::UserRole, newChannelId);
    newChannelItem->setExpanded(true);
    newChannelItem->setToolTip(0, GenerateControlChannelDetailedInfo(newChannelId));
    
    // 在每个通道下创建载荷1、载荷2、位置、控制子节点，启动时关联信息为空
    QTreeWidgetItem* load1Item = new QTreeWidgetItem(newChannelItem);
    load1Item->setText(0, tr("载荷1"));
    load1Item->setText(1, ""); // 启动时关联信息为空
    load1Item->setData(0, Qt::UserRole, "载荷传感器");
    load1Item->setToolTip(0, GenerateLoadSensorDetailedInfo("载荷1", ""));
    
    QTreeWidgetItem* load2Item = new QTreeWidgetItem(newChannelItem);
    load2Item->setText(0, tr("载荷2"));
    load2Item->setText(1, ""); // 启动时关联信息为空
    load2Item->setData(0, Qt::UserRole, "载荷传感器");
    load2Item->setToolTip(0, GenerateLoadSensorDetailedInfo("载荷2", ""));
    
    QTreeWidgetItem* positionItem = new QTreeWidgetItem(newChannelItem);
    positionItem->setText(0, tr("位置"));
    positionItem->setText(1, ""); // 启动时关联信息为空
    positionItem->setData(0, Qt::UserRole, "位置传感器");
    positionItem->setToolTip(0, GeneratePositionSensorDetailedInfo(""));
    
    QTreeWidgetItem* controlItem = new QTreeWidgetItem(newChannelItem);
    controlItem->setText(0, tr("控制"));
    controlItem->setText(1, ""); // 启动时关联信息为空
    controlItem->setData(0, Qt::UserRole, "控制作动器");
    controlItem->setToolTip(0, GenerateControlActuatorDetailedInfo(""));
    
    // 🆕 新增：将新控制通道添加到数据管理器
    if (ctrlChanDataManager_) {
        // 查找默认控制通道组
        auto existingGroups = ctrlChanDataManager_->getAllControlChannelGroups();
        if (!existingGroups.isEmpty()) {
            // 使用第一个组作为默认组
            int defaultGroupId = 0; // 假设第一个组的ID为0
            
            // 创建新的控制通道参数
            UI::ControlChannelParams newChannel;
            newChannel.channelId = newChannelId.toStdString();
            newChannel.channelName = newChannelId.toStdString();
            newChannel.hardwareAssociation = "";  // 默认为空，等待用户拖拽关联
            newChannel.load1Sensor = "";          // 默认为空，等待用户拖拽关联
            newChannel.load2Sensor = "";          // 默认为空，等待用户拖拽关联
            newChannel.positionSensor = "";       // 默认为空，等待用户拖拽关联
            newChannel.controlActuator = "";      // 默认为空，等待用户拖拽关联
            newChannel.notes = "";                // 默认为空
            
            // 使用数据管理器的添加方法
            if (ctrlChanDataManager_->addChannelToGroup(defaultGroupId, newChannel)) {
                AddLogEntry("INFO", QString("新控制通道已添加到数据管理器: %1").arg(newChannelId));
            } else {
                AddLogEntry("ERROR", QString("添加控制通道到数据管理器失败: %1").arg(newChannelId));
            }
        } else {
            AddLogEntry("WARNING", QString("未找到控制通道组，无法添加新通道: %1").arg(newChannelId));
        }
    } else {
        AddLogEntry("ERROR", QString("控制通道数据管理器未初始化，无法添加新通道: %1").arg(newChannelId));
    }
    
    // 🆕 新增：新建控制通道后更新所有树形控件节点提示
    UpdateAllTreeWidgetTooltips();
    
    AddLogEntry("INFO", QString("新控制通道已创建: %1").arg(newChannelId));
}

void CMyMainWindow::OnCreateActuatorGroup() {
    // v3.4架构：完全使用DeviceManager处理作动器组创建
    if (deviceManager_) {
        if (logManager_) {
            logManager_->info("v3.4架构：使用DeviceManager创建作动器组...");
        }
        
        bool success = deviceManager_->createActuatorGroup();
        if (success) {
            if (logManager_) {
                logManager_->info("v3.4架构：DeviceManager创建作动器组成功");
            }
        } else {
            if (logManager_) {
                logManager_->warning("v3.4架构：DeviceManager创建作动器组失败");
            }
        }
        return;
    }
    
    // 如果DeviceManager未初始化，记录错误
    AddLogEntry("ERROR", "DeviceManager未初始化，无法创建作动器组");
}

void CMyMainWindow::OnCreateSensorGroup() {
    // v3.4架构：完全使用DeviceManager处理传感器组创建
    if (deviceManager_) {
        if (logManager_) {
            logManager_->info("v3.4架构：使用DeviceManager创建传感器组...");
        }
        
        bool success = deviceManager_->createSensorGroup();
        if (success) {
            if (logManager_) {
                logManager_->info("v3.4架构：DeviceManager创建传感器组成功");
            }
        } else {
            if (logManager_) {
                logManager_->warning("v3.4架构：DeviceManager创建传感器组失败");
            }
        }
        return;
    }
    
    // 如果DeviceManager未初始化，记录错误
    AddLogEntry("ERROR", "DeviceManager未初始化，无法创建传感器组");
}

// 🔄 已迁移：CreateActuatorGroup功能已迁移到ActuatorViewModel1_2::createActuatorGroupBusiness()
// 🔄 UI创建功能已迁移到CreateActuatorGroupUI()

// 🔄 已迁移：CreateActuatorGroupByCapacity功能已迁移到ActuatorViewModel1_2业务逻辑中

// 创建传感器组的具体实现
void CMyMainWindow::CreateSensorGroup(const QString& groupName) {
    if (!ui->hardwareTreeWidget) return;

    // 检查传感器组名称是否已存在
    if (IsSensorGroupNameExists(groupName)) {
        QMessageBox::warning(this, tr("名称重复"),
            QString("传感器组名称 '%1' 已存在！\n请使用不同的名称。").arg(groupName));
        AddLogEntry("WARNING", QString("传感器组名称重复: %1").arg(groupName));
        return;
    }

    // 🔧 修复：创建UI节点的同时预创建传感器组数据，避免循环依赖问题
    // 先生成组ID并创建空的传感器组，这样在添加传感器时就能获取到有效的组
    int groupId = generateSensorGroupIdFromName(groupName);
    if (groupId <= 0) {
        QMessageBox::warning(this, tr("创建失败"),
            QString(u8"无法为传感器组 '%1' 生成有效的组ID").arg(groupName));
        AddLogEntry("ERROR", QString(u8"传感器组ID生成失败: %1").arg(groupName));
        return;
    }

    // 🆕 新增：创建空的传感器组数据并保存到SensorDataManager
    if (sensorDataManager_ && !sensorDataManager_->hasSensorGroup(groupId)) {
        UI::SensorGroup_1_2 emptyGroup;
        emptyGroup.groupId = groupId;
        emptyGroup.groupName = groupName;
        
        // 从组名称中提取传感器类型
        QString groupType = groupName;
        if (groupName.endsWith("_传感器组")) {
            groupType = groupName.left(groupName.length() - 5); // 移除"_传感器组"后缀
        }
        emptyGroup.groupType = groupType;
        emptyGroup.createTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
        emptyGroup.groupNotes = QString(u8"通过UI创建的传感器组（暂无传感器）");
        
        // 保存空组到数据管理器
        if (!sensorDataManager_->saveSensorGroup(emptyGroup)) {
            QMessageBox::warning(this, tr("创建失败"),
                QString(u8"传感器组数据保存失败: %1").arg(sensorDataManager_->getLastError()));
            AddLogEntry("ERROR", QString(u8"传感器组数据保存失败: %1 - %2").arg(groupName).arg(sensorDataManager_->getLastError()));
            return;
        }
        
        AddLogEntry("INFO", QString(u8"传感器组数据已保存: %1 (ID: %2)").arg(groupName).arg(groupId));
    }

    // 获取任务1根节点
    QTreeWidgetItem* taskRoot = ui->hardwareTreeWidget->topLevelItem(0);
    if (!taskRoot) return;

    // 获取传感器子节点（第2个子节点，索引为1）
    QTreeWidgetItem* sensorRoot = taskRoot->child(1);
    if (!sensorRoot) return;

    // 创建传感器组节点
    QTreeWidgetItem* groupItem = new QTreeWidgetItem(sensorRoot);
    groupItem->setText(0, groupName);
    groupItem->setData(0, Qt::UserRole, "传感器组"); // 设置类型为传感器组
    groupItem->setData(1, Qt::UserRole, groupId);    // 🔧 修复：存储组ID到第1列，供extractSensorGroupIdFromItem使用
    groupItem->setExpanded(true);
    // 🆕 新增：为传感器组添加提示信息
    groupItem->setToolTip(0, QString(u8"传感器组: %1 (ID: %2)\n管理同类型的传感器设备\n当前状态: 空组，等待添加传感器\n右键可添加传感器设备\n包含载荷、位置、压力等各类传感器").arg(groupName).arg(groupId));

    AddLogEntry("INFO", QString(u8"创建传感器组: %1").arg(groupName));
    
    // 🆕 新增：新建操作成功后，不更新详细信息界面
    // 根据需求7：新建作动器组、作动器、传感器组、传感器、硬件节点资源,不更新"详细信息"界面数据
    AddLogEntry("INFO", QString(u8"✅ 传感器组创建成功，详细信息界面无需更新（新建操作）"));

    // 🆕 新增：添加传感器组后更新所有树形控件节点提示
    UpdateAllTreeWidgetTooltips();
}

// 创建作动器的详细参数输入对话框
void CMyMainWindow::OnCreateActuator(QTreeWidgetItem* groupItem) {
    // v3.4架构：完全使用DeviceManager处理作动器设备创建
    if (deviceManager_) {
        if (logManager_) {
            logManager_->info("v3.4架构：使用DeviceManager创建作动器设备...");
        }
        
        bool success = deviceManager_->createActuatorDevice(groupItem);
        if (success) {
            if (logManager_) {
                logManager_->info("v3.4架构：DeviceManager创建作动器设备成功");
            }
        } else {
            if (logManager_) {
                logManager_->warning("v3.4架构：DeviceManager创建作动器设备失败");
            }
        }
        return;
    }
    
    // 如果DeviceManager未初始化，记录错误
    AddLogEntry("ERROR", "DeviceManager未初始化，无法创建作动器设备");
}

// 🔄 已迁移：createOrUpdateActuatorGroup功能已迁移到ActuatorViewModel1_2的业务逻辑方法中

// ❌ 已废弃：从组名称提取组ID的函数（错误的逻辑）
// 现在改为通过组名称在数据管理器中查找对应的组来获取真实的组ID
/*
int CMyMainWindow::extractGroupIdFromName(const QString& groupName) const {
    // 这个函数的逻辑是错误的，不应该从组名称中提取数字作为组ID
    // 正确的做法是通过组名称在数据管理器中查找对应的组
    // 已在所有调用处修复为正确的查找逻辑
    return 0; // 不再使用
}
*/

// ============================================================================
// ✅ 数据同步方法实现（通过ViewModel进行）
// ============================================================================

void CMyMainWindow::syncMemoryDataToProject() {
    // 注意：由于TestProject的方法已不存在，这个方法现在主要确保数据在DataManager中是最新的
    AddLogEntry("INFO", u8"开始同步内存数据...");

    // 同步传感器数据 - 数据已在SensorDataManager中
    if (sensorDataManager_) {
        QList<UI::SensorParams_1_2> sensors = sensorDataManager_->getAllSensors();
        AddLogEntry("INFO", QString(u8"传感器数据已准备: %1个").arg(sensors.size()));
    }

    // 同步作动器数据 - 数据已在ActuatorViewModel1_2中
    if (actuatorViewModel1_2_) {
        QList<UI::ActuatorParams_1_2> actuators = actuatorViewModel1_2_->getAllActuators();
        QList<UI::ActuatorGroup_1_2> groups = actuatorViewModel1_2_->getAllActuatorGroups();
        AddLogEntry("INFO", QString(u8"作动器数据已准备: %1个作动器, %2个组").arg(actuators.size()).arg(groups.size()));
    }

    AddLogEntry("INFO", u8"内存数据同步完成");
}

void CMyMainWindow::syncProjectDataToMemory() {
    // 注意：由于TestProject的方法已不存在，这个方法现在主要是清空和重置数据
    AddLogEntry("INFO", u8"开始同步项目数据到内存...");

    // 清空内存数据
    clearMemoryData();

    // 注意：实际的项目数据加载应该通过其他方式进行
    // 比如从JSON文件加载或通过其他数据源

    AddLogEntry("INFO", u8"项目数据同步到内存完成");
}

void CMyMainWindow::clearMemoryData() {
    AddLogEntry("INFO", u8"清空内存数据...");

    if (sensorDataManager_) {
        sensorDataManager_->clearAllSensors();
    }

    // 🔧 修改：通过ViewModel清空作动器数据
    if (actuatorViewModel1_2_) {
        actuatorViewModel1_2_->clearMemoryData();
    }

    AddLogEntry("INFO", u8"内存数据清空完成");
}

// 🔄 已迁移：CreateActuatorDevice和CreateActuatorDeviceWithExtendedParams功能已迁移到CreateActuatorDeviceUI()

// 创建传感器的详细参数输入对话框
void CMyMainWindow::OnCreateSensor(QTreeWidgetItem* groupItem) {
    // v3.4架构：完全使用DeviceManager处理传感器设备创建
    if (deviceManager_) {
        if (logManager_) {
            logManager_->info("v3.4架构：使用DeviceManager创建传感器设备...");
        }
        
        bool success = deviceManager_->createSensorDevice(groupItem);
        if (success) {
            if (logManager_) {
                logManager_->info("v3.4架构：DeviceManager创建传感器设备成功");
            }
        } else {
            if (logManager_) {
                logManager_->warning("v3.4架构：DeviceManager创建传感器设备失败");
            }
        }
        return;
    }
    
    // 如果DeviceManager未初始化，记录错误
    AddLogEntry("ERROR", "DeviceManager未初始化，无法创建传感器设备");

}

// 创建传感器设备节点
void CMyMainWindow::CreateSensorDevice(QTreeWidgetItem* groupItem, const QString& serialNumber,
                                  const QString& sensorType, const QString& model,
                                  const QString& range, const QString& precision) {
    if (!groupItem) return;

    // 创建传感器设备节点
    QTreeWidgetItem* sensorItem = new QTreeWidgetItem(groupItem);

    // 设置显示文本：序列号 [类型]
    //sensorItem->setText(0, QString("%1 [%2]").arg(serialNumber).arg(sensorType));
    sensorItem->setText(0, QString("%1").arg(serialNumber));

    // 设置节点类型
    sensorItem->setData(0, Qt::UserRole, "传感器设备");

    // 设置详细的工具提示信息（14字段新需求版本）
    QString tooltip = QString("序列号: %1\n类型: %2\n型号: %3\n精度: %4")
                        .arg(serialNumber)
                        .arg(sensorType)
                        .arg(model)
                        .arg(precision);
    sensorItem->setToolTip(0, tooltip);

    // 展开父组节点以显示新创建的传感器
    groupItem->setExpanded(true);

    // 🔄 修复：移除重复的项目数据存储，数据统一由 SensorDataManager_1_2 管理
    // 注释：传感器数据已经在 OnCreateSensor() 中通过 SensorDataManager_1_2 保存
    // 避免重复存储到 TestProject::sensors
    AddLogEntry("DEBUG", QString("传感器UI节点创建完成: %1 (数据由SensorDataManager统一管理)").arg(serialNumber));

    // 记录日志（14字段新需求版本）
    AddLogEntry("INFO", QString("创建传感器设备: %1, 类型: %2, 型号: %3, 精度: %4")
                .arg(serialNumber)
                .arg(sensorType)
                .arg(model)
                .arg(precision));

    // 🆕 新增：创建传感器设备后更新所有树形控件节点提示
    UpdateAllTreeWidgetTooltips();
}

// 创建作动器设备节点（参考传感器的直接操作方式）
void CMyMainWindow::CreateActuatorDeviceUI(const QString& serialNumber, const QString& groupName) {
    if (!ui->hardwareTreeWidget) return;
    
    // 找到作动器组节点
    QTreeWidgetItem* taskRoot = ui->hardwareTreeWidget->topLevelItem(0);
    if (!taskRoot) return;
    
    QTreeWidgetItem* actuatorRoot = taskRoot->child(0);
    if (!actuatorRoot) return;
    
    // 找到指定的组节点
    QTreeWidgetItem* groupItem = nullptr;
    for (int i = 0; i < actuatorRoot->childCount(); ++i) {
        QTreeWidgetItem* item = actuatorRoot->child(i);
        if (item && item->text(0) == groupName) {
            groupItem = item;
            break;
        }
    }
    
    if (!groupItem) {
        AddLogEntry("WARNING", QString(u8"未找到作动器组：%1").arg(groupName));
        return;
    }
    
    // 创建作动器设备节点
    QTreeWidgetItem* actuatorItem = new QTreeWidgetItem(groupItem);
    actuatorItem->setText(0, serialNumber);
    actuatorItem->setData(0, Qt::UserRole, "作动器设备");
    
    // 设置工具提示
    QString tooltip = QString("序列号: %1\n组名: %2").arg(serialNumber).arg(groupName);
    actuatorItem->setToolTip(0, tooltip);
    
    // 展开父组节点以显示新创建的作动器（只展开当前组，不影响其他节点）
    groupItem->setExpanded(true);
    
    AddLogEntry("INFO", QString(u8"作动器设备UI节点创建完成: %1 (组: %2)").arg(serialNumber).arg(groupName));
}

// 硬件节点配置槽函数
void CMyMainWindow::OnConfigureNodeLD_B1() {
    // 使用标准的NodeConfigDialog类 (.h + .cpp + .ui 模式)
    UI::NodeConfigDialog dialog("LD-B1", this);

    if (dialog.exec() == QDialog::Accepted) {
        // 获取节点配置参数
        UI::NodeConfigParams params = dialog.getNodeConfigParams();

        // 处理配置结果
        QString configInfo = QString("节点 %1 配置完成:\n通道数量: %2")
                            .arg(params.nodeName)
                            .arg(params.channelCount);

        for (int i = 0; i < params.channels.size(); ++i) {
            const UI::ChannelInfo& ch = params.channels[i];
            configInfo += QString("\nCH%1: %2:%3 (%4)")
                         .arg(ch.channelId)
                         .arg(ch.ipAddress)
                         .arg(ch.port)
                         .arg(ch.enabled ? "启用" : "禁用");
        }

        AddLogEntry("INFO", QString("LD-B1节点配置已更新"));
        QMessageBox::information(this, tr("配置成功"), configInfo);
    }
}

void CMyMainWindow::OnConfigureNodeLD_B2() {
    // 使用标准的NodeConfigDialog类 (.h + .cpp + .ui 模式)
    UI::NodeConfigDialog dialog("LD-B2", this);

    if (dialog.exec() == QDialog::Accepted) {
        // 获取节点配置参数
        UI::NodeConfigParams params = dialog.getNodeConfigParams();

        // 处理配置结果
        QString configInfo = QString("节点 %1 配置完成:\n通道数量: %2")
                            .arg(params.nodeName)
                            .arg(params.channelCount);

        for (int i = 0; i < params.channels.size(); ++i) {
            const UI::ChannelInfo& ch = params.channels[i];
            configInfo += QString("\nCH%1: %2:%3 (%4)")
                         .arg(ch.channelId)
                         .arg(ch.ipAddress)
                         .arg(ch.port)
                         .arg(ch.enabled ? "启用" : "禁用");
        }

        AddLogEntry("INFO", QString("LD-B2节点配置已更新"));
        QMessageBox::information(this, tr("配置成功"), configInfo);
    }
}

// 创建硬件节点槽函数
void CMyMainWindow::OnCreateHardwareNode() {
    // v3.4架构：使用DeviceManager和DialogManager处理硬件节点创建
    if (deviceManager_ && dialogManager_) {
        // 使用LogManager记录日志
        if (logManager_) {
            logManager_->info("v3.4架构：开始使用DeviceManager创建硬件节点...");
        }
        
        // 委托给DeviceManager处理
        bool success = deviceManager_->createHardwareNode();
        if (success) {
            if (logManager_) {
                logManager_->info("v3.4架构：DeviceManager硬件节点创建成功");
            }
            return;
        } else {
            if (logManager_) {
                logManager_->warning("v3.4架构：DeviceManager处理失败，回退到原有逻辑");
            }
        }
    }
    
    // 原有逻辑保持不变（作为备用方案）
    // 生成下一个可用的节点名称
    QString suggestedName = GenerateNextHardwareNodeName();

    // 使用标准的CreateHardwareNodeDialog类 (.h + .cpp + .ui 模式)
    UI::CreateHardwareNodeDialog dialog(suggestedName, this);

    if (dialog.exec() == QDialog::Accepted) {
        // 获取硬件节点创建参数
        UI::CreateHardwareNodeParams params = dialog.getCreateHardwareNodeParams();

        // 创建硬件节点
        CreateHardwareNodeInTree(params);

        // 记录日志
        // v3.4架构：使用LogManager替代AddLogEntry
        if (logManager_) {
            logManager_->info(QString("硬件节点 %1 已创建，包含 %2 个通道")
                               .arg(params.nodeName)
                               .arg(params.channelCount));
        } else {
            AddLogEntry("INFO", QString("硬件节点 %1 已创建，包含 %2 个通道")
                               .arg(params.nodeName)
                               .arg(params.channelCount));
        }

        // 🆕 新增：新建操作成功后，不更新详细信息界面
        // 根据需求7：新建作动器组、作动器、传感器组、传感器、硬件节点资源,不更新"详细信息"界面数据
        AddLogEntry("INFO", QString(u8"✅ 硬件节点创建成功，详细信息界面无需更新（新建操作）"));

        // 显示成功消息
        QString successInfo = QString("硬件节点 %1 创建成功!\n通道数量: %2")
                             .arg(params.nodeName)
                             .arg(params.channelCount);

        for (int i = 0; i < params.channels.size(); ++i) {
            const UI::ChannelInfo& ch = params.channels[i];
            successInfo += QString("\nCH%1: %2:%3 (%4)")
                          .arg(ch.channelId)
                          .arg(ch.ipAddress)
                          .arg(ch.port)
                          .arg(ch.enabled ? "启用" : "禁用");
        }

        QMessageBox::information(this, tr("创建成功"), successInfo);
    }
}

// 生成下一个可用的硬件节点名称
QString CMyMainWindow::GenerateNextHardwareNodeName() {
    if (!ui->hardwareTreeWidget) return "LD-B1";

    // 获取任务1根节点
    QTreeWidgetItem* taskRoot = ui->hardwareTreeWidget->topLevelItem(0);
    if (!taskRoot) return "LD-B1";

    // 获取硬件节点资源子节点（第3个子节点，索引为2）
    QTreeWidgetItem* hardwareRoot = taskRoot->child(2);
    if (!hardwareRoot) return "LD-B1";

    // 查找现有的LD-B节点，找到最大的编号
    int maxNumber = 0;
    for (int i = 0; i < hardwareRoot->childCount(); ++i) {
        QTreeWidgetItem* child = hardwareRoot->child(i);
        QString nodeName = child->text(0);

        // 检查是否是LD-B格式的节点
        if (nodeName.startsWith("LD-B")) {
            QString numberPart = nodeName.mid(4); // 去掉"LD-B"前缀
            bool ok;
            int number = numberPart.toInt(&ok);
            if (ok && number > maxNumber) {
                maxNumber = number;
            }
        }
    }

    // 返回下一个可用的名称
    return QString("LD-B%1").arg(maxNumber + 1);
}

// 验证并更新智能通道关联
void CMyMainWindow::UpdateSmartChannelAssociations() {
    if (!ui->testConfigTreeWidget) return;

    // 获取试验配置树的根节点
    QTreeWidgetItem* taskRoot = ui->testConfigTreeWidget->topLevelItem(0);
    if (!taskRoot) return;

    // 查找控制通道节点
    QTreeWidgetItem* controlChannelRoot = nullptr;
    for (int i = 0; i < taskRoot->childCount(); ++i) {
        QTreeWidgetItem* child = taskRoot->child(i);
        if (child && child->text(0) == tr("控制通道")) {
            controlChannelRoot = child;
            break;
        }
    }

    if (!controlChannelRoot) return;

    // 更新CH1和CH2的关联信息
    for (int i = 0; i < controlChannelRoot->childCount(); ++i) {
        QTreeWidgetItem* channelItem = controlChannelRoot->child(i);
        if (!channelItem) continue;

        QString channelName = channelItem->text(0);
        if (channelName == "CH1" || channelName == "CH2") {
            // 🔧 修复：确保channelId正确存储
            channelItem->setData(1, Qt::UserRole, channelName); // 存储channelId
            
            // 提取通道号
            int channelNum = channelName.mid(2).toInt();
            QString hardwareNodeName = QString("LD-B%1").arg(channelNum);

            // 检查对应的硬件节点是否存在
            if (IsHardwareNodeExists(hardwareNodeName)) {
                // 硬件节点存在，设置关联信息
                QString association = QString("%1 - %2").arg(hardwareNodeName).arg(channelName);
                channelItem->setText(1, association);

                AddLogEntry("INFO", QString("智能关联: %1 -> %2").arg(channelName).arg(association));
            } else {
                // 硬件节点不存在，确保关联信息为空（不记录日志，避免启动时的噪音）
                channelItem->setText(1, "");
            }
        }
    }
}

// 检查硬件节点是否存在
bool CMyMainWindow::IsHardwareNodeExists(const QString& nodeName) const {
    if (!ui->hardwareTreeWidget) return false;

    // 获取硬件配置树的根节点
    QTreeWidgetItem* taskRoot = ui->hardwareTreeWidget->topLevelItem(0);
    if (!taskRoot) return false;

    // 获取硬件节点资源子节点（第3个子节点，索引为2）
    QTreeWidgetItem* hardwareRoot = taskRoot->child(2);
    if (!hardwareRoot) return false;

    // 查找指定名称的硬件节点
    for (int i = 0; i < hardwareRoot->childCount(); ++i) {
        QTreeWidgetItem* child = hardwareRoot->child(i);
        if (child && child->text(0) == nodeName) {
            return true;
        }
    }

    return false;
}

// 删除硬件节点
void CMyMainWindow::OnDeleteHardwareNode(QTreeWidgetItem* item) {
    if (!item) return;

    QString nodeName = item->text(0);

    // 确认删除
    int ret = QMessageBox::question(this, tr("确认删除"),
        QString("确定要删除硬件节点 '%1' 吗？\n\n此操作将：\n- 删除该硬件节点及其所有通道\n- 清除相关的试验配置关联\n- 无法撤销").arg(nodeName),
        QMessageBox::Yes | QMessageBox::No,
        QMessageBox::No);

    if (ret != QMessageBox::Yes) {
        return;
    }

    // 🔧 修复：从数据管理器中删除硬件节点配置
    if (hardwareNodeResDataManager_) {
        if (hardwareNodeResDataManager_->removeHardwareNodeConfig(nodeName)) {
            AddLogEntry("INFO", QString("硬件节点已从数据管理器中删除: %1").arg(nodeName));
        } else {
            AddLogEntry("WARNING", QString("从数据管理器删除硬件节点失败: %1").arg(nodeName));
        }
    } else {
        AddLogEntry("WARNING", QString("硬件节点数据管理器未初始化，无法删除数据: %1").arg(nodeName));
    }

    // 从树形控件中删除节点
    QTreeWidgetItem* parent = item->parent();
    if (parent) {
        parent->removeChild(item);
        delete item;
        AddLogEntry("INFO", QString("硬件节点已从界面中删除: %1").arg(nodeName));
    }

    // 删除硬件节点后，更新试验配置中的智能通道关联
    UpdateSmartChannelAssociations();

    // 🆕 新增：删除硬件节点后更新控制通道关联信息
    UpdateControlChannelAssociationsAfterHardwareNodeDelete(nodeName);

    // 🆕 新增：删除硬件节点后更新所有树形控件节点提示
    UpdateAllTreeWidgetTooltips();
}

// 编辑硬件节点
void CMyMainWindow::OnEditHardwareNode(QTreeWidgetItem* item) {
    if (!item) return;

    QString nodeName = item->text(0);

    // 从数据管理器获取现有的硬件节点配置
    if (!hardwareNodeResDataManager_) {
        QMessageBox::warning(this, tr("编辑失败"), 
            tr("硬件节点数据管理器未初始化，无法编辑硬件节点。"));
        return;
    }

    UI::NodeConfigParams existingConfig = hardwareNodeResDataManager_->getHardwareNodeConfig(nodeName);
    if (existingConfig.nodeName.isEmpty()) {
        QMessageBox::warning(this, tr("编辑失败"), 
            QString("未找到硬件节点 '%1' 的配置信息。").arg(nodeName));
        return;
    }

    // 将NodeConfigParams转换为CreateHardwareNodeParams
    UI::CreateHardwareNodeParams editParams;
    editParams.nodeName = existingConfig.nodeName;
    editParams.channelCount = existingConfig.channelCount;
    editParams.channels = existingConfig.channels;

    // 创建编辑对话框
    UI::CreateHardwareNodeDialog editDialog(editParams.nodeName, this);
    editDialog.setEditMode(true);
    editDialog.setHardwareNodeParams(editParams);

    if (editDialog.exec() == QDialog::Accepted) {
        // 获取编辑后的参数
        UI::CreateHardwareNodeParams updatedParams = editDialog.getCreateHardwareNodeParams();

        // 检查节点名称是否有变化且是否与其他节点冲突
        if (updatedParams.nodeName != existingConfig.nodeName) {
            if (IsHardwareNodeNameExists(updatedParams.nodeName)) {
                QMessageBox::warning(this, tr("编辑失败"), 
                    QString("硬件节点名称 '%1' 已存在，请使用其他名称。").arg(updatedParams.nodeName));
                return;
            }

                    // 节点名称改变时不再直接删除旧配置，而是在addOrUpdateHardwareNodeConfig中处理
        }

        // 将CreateHardwareNodeParams转换为NodeConfigParams并更新
        UI::NodeConfigParams updatedConfig;
        updatedConfig.nodeName = updatedParams.nodeName;
        updatedConfig.channelCount = updatedParams.channelCount;
        updatedConfig.channels = updatedParams.channels;

        // 更新数据管理器中的配置，传递旧节点名称以保持顺序
        if (hardwareNodeResDataManager_->addOrUpdateHardwareNodeConfig(updatedConfig, existingConfig.nodeName)) {
            // 更新树形控件中的显示
            if (updatedParams.nodeName != existingConfig.nodeName) {
                // 如果节点名称改变，更新树形控件中的文本
                item->setText(0, updatedParams.nodeName);
            }

            // 更新子节点（通道）的显示
            UpdateHardwareNodeChannelsInTree(item, updatedParams);

            // 记录日志
            AddLogEntry("INFO", QString("硬件节点 %1 已更新，包含 %2 个通道")
                               .arg(updatedParams.nodeName)
                               .arg(updatedParams.channelCount));

            // 显示成功消息
            QString successInfo = QString("硬件节点 %1 编辑成功!\n通道数量: %2")
                                 .arg(updatedParams.nodeName)
                                 .arg(updatedParams.channelCount);

            for (int i = 0; i < updatedParams.channels.size(); ++i) {
                const UI::ChannelInfo& ch = updatedParams.channels[i];
                successInfo += QString("\nCH%1: %2:%3 (%4)")
                              .arg(ch.channelId)
                              .arg(ch.ipAddress)
                              .arg(ch.port)
                              .arg(ch.enabled ? "启用" : "禁用");
            }

            QMessageBox::information(this, tr("编辑成功"), successInfo);

            // 更新试验配置中的智能通道关联
            UpdateSmartChannelAssociations();

            // 🆕 新增：硬件节点编辑后更新控制通道关联信息
            UpdateControlChannelAssociationsAfterHardwareNodeEdit(existingConfig.nodeName, updatedParams.nodeName, updatedParams.channels);

            // 🆕 新增：编辑操作成功后，详细信息界面会自动更新
            // 根据需求7：编辑作动器组、作动器、传感器组、传感器、硬件节点资源,需要更新"详细信息"界面数据相对应修改数据
            AddLogEntry("INFO", QString(u8"✅ 硬件节点编辑成功，详细信息界面将自动更新（编辑操作）"));

            // 更新所有树形控件节点提示
            UpdateAllTreeWidgetTooltips();
        } else {
            QMessageBox::critical(this, tr("编辑失败"), 
                tr("更新硬件节点配置失败，请检查配置数据。"));
        }
    }
}

// 启用试验配置树的拖拽功能
void CMyMainWindow::EnableTestConfigTreeDragDrop() {
    if (!ui->testConfigTreeWidget || !ui->hardwareTreeWidget) return;

    // 🔧 修复：强化拖拽功能启用，确保自定义控件正确设置

    // 启用试验配置树的拖拽接收
    ui->testConfigTreeWidget->setAcceptDrops(true);
    ui->testConfigTreeWidget->setDropIndicatorShown(true);

    // 启用硬件树的拖拽发送
    ui->hardwareTreeWidget->setDragEnabled(true);
    ui->hardwareTreeWidget->setDragDropMode(QAbstractItemView::DragOnly);
    ui->hardwareTreeWidget->setDefaultDropAction(Qt::CopyAction);

    // 🆕 新增：确保自定义控件的主窗口连接正确
    CustomHardwareTreeWidget* hardwareTree = qobject_cast<CustomHardwareTreeWidget*>(ui->hardwareTreeWidget);
    if (hardwareTree) {
        hardwareTree->setMainWindow(this);
        AddLogEntry("DEBUG", "硬件树自定义控件主窗口连接已确认");
    }

    CustomTestConfigTreeWidget* configTree = qobject_cast<CustomTestConfigTreeWidget*>(ui->testConfigTreeWidget);
    if (configTree) {
        configTree->setMainWindow(this);
        AddLogEntry("DEBUG", "配置树自定义控件主窗口连接已确认");
    }

    AddLogEntry("INFO", "拖拽功能已启用：硬件节点通道可拖拽到试验配置通道");
}

// 公共日志记录方法（供自定义控件调用）
void CMyMainWindow::LogMessage(const QString& level, const QString& message) {
    AddLogEntry(level, message);
}

// 🆕 新增：从内存数据获取控制通道组（完全参考作动器详细信息）
QList<UI::ControlChannelGroup> CMyMainWindow::buildControlChannelGroupsFromUI() const {
    // 🔄 修正：完全参考作动器实现，只从内存数据管理器获取控制通道组
    if (!ctrlChanDataManager_) {
        AddLogEntry("WARNING", u8"控制通道数据管理器未初始化");
        return QList<UI::ControlChannelGroup>();
    }

    // 🔄 统一使用数据管理器获取控制通道组数据
    QList<UI::ControlChannelGroup> groups = ctrlChanDataManager_->getAllControlChannelGroups();

    AddLogEntry("INFO", QString(u8"从数据管理器获取控制通道组: %1个组").arg(groups.size()));

    return groups;
}

// 🆕 新增：添加控制作动器debug信息 - 只显示组ID、ID、序号
void CMyMainWindow::AddControlActuatorDebugInfo(QString& debugInfo, const QString& associationInfo) const {
    if (!associationInfo.isEmpty()) {
        // 解析关联信息获取组名和设备名
        QStringList parts = associationInfo.split(" - ");
        if (parts.size() >= 2) {
            QString groupName = parts[0];
            QString deviceName = parts[1];

            // 🔧 修复：通过组名称查找对应的作动器组
            if (actuatorViewModel1_2_) {
                QList<UI::ActuatorGroup_1_2> allGroups = actuatorViewModel1_2_->getAllActuatorGroups();
                for (const UI::ActuatorGroup_1_2& group : allGroups) {
                    if (group.groupName == groupName) {
                        // 查找设备在组内的位置
                        for (int i = 0; i < group.actuators.size(); ++i) {
                            if (group.actuators[i].params.sn == deviceName) {
                                debugInfo += QString(u8"组ID: %1, ID: %2, 序号: %3\n")
                                            .arg(group.groupId)
                                            .arg(group.actuators[i].actuatorId)
                                            .arg(i + 1); // 序号从1开始
                                return;
                            }
                        }
                        break;
                    }
                }
            }
            debugInfo += QString(u8"关联: %1 - %2\n").arg(groupName).arg(deviceName);
        }
    }
}

// ============================================================================
// 🆕 新增：数据同步修复方案核心实现
// ============================================================================

/**
 * @brief 同步所有数据管理器，确保数据一致性
 * @return 同步是否成功
 * 
 * 这是解决JSON导出数据不一致问题的核心方法。
 * 确保从Excel加载的数据在所有DataManager中保持一致状态。
 */
bool CMyMainWindow::SynchronizeAllDataManagers() {
    AddLogEntry("INFO", QString(u8"🔄 开始执行数据同步修复方案..."));
    
    bool allSuccess = true;
    int syncCount = 0;
    
    try {
        // 1. 同步硬件节点数据（核心修复）
        if (SynchronizeHardwareNodeData()) {
            syncCount++;
            AddLogEntry("INFO", QString(u8"✅ 硬件节点数据同步成功"));
        } else {
            allSuccess = false;
            AddLogEntry("ERROR", QString(u8"❌ 硬件节点数据同步失败"));
        }
        
        // 2. 同步作动器数据
        if (SynchronizeActuatorData()) {
            syncCount++;
            AddLogEntry("INFO", QString(u8"✅ 作动器数据同步成功"));
        } else {
            allSuccess = false;
            AddLogEntry("ERROR", QString(u8"❌ 作动器数据同步失败"));
        }
        
        // 3. 同步传感器数据
        if (SynchronizeSensorData()) {
            syncCount++;
            AddLogEntry("INFO", QString(u8"✅ 传感器数据同步成功"));
        } else {
            allSuccess = false;
            AddLogEntry("ERROR", QString(u8"❌ 传感器数据同步失败"));
        }
        
        // 4. 同步控制通道数据
        if (SynchronizeControlChannelData()) {
            syncCount++;
            AddLogEntry("INFO", QString(u8"✅ 控制通道数据同步成功"));
        } else {
            allSuccess = false;
            AddLogEntry("ERROR", QString(u8"❌ 控制通道数据同步失败"));
        }
        
        AddLogEntry("INFO", QString(u8"🔄 数据同步完成：%1/4个模块同步成功，整体状态：%2")
                   .arg(syncCount)
                   .arg(allSuccess ? u8"成功" : u8"部分失败"));
                   
    } catch (const std::exception& e) {
        AddLogEntry("ERROR", QString(u8"数据同步过程中发生异常: %1").arg(e.what()));
        allSuccess = false;
    } catch (...) {
        AddLogEntry("ERROR", QString(u8"数据同步过程中发生未知异常"));
        allSuccess = false;
    }
    
    return allSuccess;
}

/**
 * @brief 同步硬件节点数据（核心修复）
 * @return 同步是否成功
 * 
 * 这是解决333333.json和555555.json数据不一致的关键方法。
 * 确保硬件节点数据从xlsDataExporter_正确同步到hardwareNodeResDataManager_。
 */
bool CMyMainWindow::SynchronizeHardwareNodeData() {
    if (!xlsDataExporter_ || !hardwareNodeResDataManager_) {
        AddLogEntry("ERROR", QString(u8"硬件节点同步失败：数据管理器未初始化"));
        return false;
    }
    
    try {
        // 获取从Excel文件加载的硬件节点数据
        QList<UI::NodeConfigParams> excelNodeConfigs = xlsDataExporter_->getHardwareNodeConfigs();
        
        // 获取当前数据管理器中的硬件节点数据
        QList<UI::NodeConfigParams> managerNodeConfigs = hardwareNodeResDataManager_->getAllHardwareNodeConfigs();
        
        AddLogEntry("INFO", QString(u8"🔍 硬件节点数据对比：Excel中%1个，DataManager中%2个")
                   .arg(excelNodeConfigs.size())
                   .arg(managerNodeConfigs.size()));
        
        // 如果数据不一致，执行同步
        if (excelNodeConfigs.size() != managerNodeConfigs.size()) {
            AddLogEntry("WARNING", QString(u8"🚨 发现数据不一致！开始强制同步..."));
            
            // 清空DataManager中的现有数据
            hardwareNodeResDataManager_->clearAllHardwareNodeConfigs();
            
            // 将Excel中的数据同步到DataManager
            int syncedCount = 0;
            for (const UI::NodeConfigParams& config : excelNodeConfigs) {
                if (hardwareNodeResDataManager_->addOrUpdateHardwareNodeConfig(config)) {
                    syncedCount++;
                } else {
                    AddLogEntry("ERROR", QString(u8"同步硬件节点失败: %1").arg(config.nodeName));
                }
            }
            
            AddLogEntry("INFO", QString(u8"🔄 硬件节点数据同步结果：%1/%2个节点同步成功")
                       .arg(syncedCount)
                       .arg(excelNodeConfigs.size()));
                       
            return syncedCount == excelNodeConfigs.size();
        } else {
            AddLogEntry("INFO", QString(u8"✅ 硬件节点数据已同步，无需操作"));
            return true;
        }
        
    } catch (const std::exception& e) {
        AddLogEntry("ERROR", QString(u8"硬件节点数据同步异常: %1").arg(e.what()));
        return false;
    }
}

/**
 * @brief 同步作动器数据
 * @return 同步是否成功
 */
bool CMyMainWindow::SynchronizeActuatorData() {
    if (!xlsDataExporter_ || !actuatorViewModel1_2_) {
        AddLogEntry("ERROR", QString(u8"作动器同步失败：数据管理器未初始化"));
        return false;
    }
    
    try {
        // 获取Excel中的作动器数据
        ActuatorDataManager_1_2* excelActuatorManager = xlsDataExporter_->getActuatorDataManager();
        if (!excelActuatorManager) {
            AddLogEntry("WARNING", QString(u8"Excel中未找到作动器数据管理器"));
            return true; // 没有数据也算成功
        }
        
        QList<UI::ActuatorGroup_1_2> excelGroups = excelActuatorManager->getAllActuatorGroups();
        QList<UI::ActuatorGroup_1_2> managerGroups = actuatorViewModel1_2_->getAllActuatorGroups();
        
        AddLogEntry("INFO", QString(u8"🔍 作动器数据对比：Excel中%1个组，ViewModel中%2个组")
                   .arg(excelGroups.size())
                   .arg(managerGroups.size()));
        
        // 检查数据一致性
        if (excelGroups.size() != managerGroups.size()) {
            AddLogEntry("WARNING", QString(u8"🚨 作动器数据不一致！需要同步"));
            // 这里可以添加具体的同步逻辑
        } else {
            AddLogEntry("INFO", QString(u8"✅ 作动器数据已同步"));
        }
        
        return true;
        
    } catch (const std::exception& e) {
        AddLogEntry("ERROR", QString(u8"作动器数据同步异常: %1").arg(e.what()));
        return false;
    }
}

/**
 * @brief 同步传感器数据
 * @return 同步是否成功
 */
bool CMyMainWindow::SynchronizeSensorData() {
    if (!xlsDataExporter_ || !sensorDataManager_) {
        AddLogEntry("ERROR", QString(u8"传感器同步失败：数据管理器未初始化"));
        return false;
    }
    
    try {
        // 获取Excel中的传感器数据
        SensorDataManager_1_2* excelSensorManager = xlsDataExporter_->getSensorDataManager();
        if (!excelSensorManager) {
            AddLogEntry("WARNING", QString(u8"Excel中未找到传感器数据管理器"));
            return true; // 没有数据也算成功
        }
        
        QList<UI::SensorGroup_1_2> excelGroups = excelSensorManager->getAllSensorGroups();
        QList<UI::SensorGroup_1_2> managerGroups = sensorDataManager_->getAllSensorGroups();
        
        AddLogEntry("INFO", QString(u8"🔍 传感器数据对比：Excel中%1个组，DataManager中%2个组")
                   .arg(excelGroups.size())
                   .arg(managerGroups.size()));
        
        // 检查数据一致性
        if (excelGroups.size() != managerGroups.size()) {
            AddLogEntry("WARNING", QString(u8"🚨 传感器数据不一致！需要同步"));
            // 这里可以添加具体的同步逻辑
        } else {
            AddLogEntry("INFO", QString(u8"✅ 传感器数据已同步"));
        }
        
        return true;
        
    } catch (const std::exception& e) {
        AddLogEntry("ERROR", QString(u8"传感器数据同步异常: %1").arg(e.what()));
        return false;
    }
}

/**
 * @brief 同步控制通道数据
 * @return 同步是否成功
 */
bool CMyMainWindow::SynchronizeControlChannelData() {
    if (!xlsDataExporter_ || !ctrlChanDataManager_) {
        AddLogEntry("ERROR", QString(u8"控制通道同步失败：数据管理器未初始化"));
        return false;
    }
    
    try {
        // 获取Excel中的控制通道数据
        CtrlChanDataManager* excelChannelManager = xlsDataExporter_->getCtrlChanDataManager();
        if (!excelChannelManager) {
            AddLogEntry("WARNING", QString(u8"Excel中未找到控制通道数据管理器"));
            return true; // 没有数据也算成功
        }
        
        QList<UI::ControlChannelGroup> excelGroups = excelChannelManager->getAllControlChannelGroups();
        QList<UI::ControlChannelGroup> managerGroups = ctrlChanDataManager_->getAllControlChannelGroups();
        
        AddLogEntry("INFO", QString(u8"🔍 控制通道数据对比：Excel中%1个组，DataManager中%2个组")
                   .arg(excelGroups.size())
                   .arg(managerGroups.size()));
        
        // 检查数据一致性
        if (excelGroups.size() != managerGroups.size()) {
            AddLogEntry("WARNING", QString(u8"🚨 控制通道数据不一致！需要同步"));
            // 这里可以添加具体的同步逻辑
        } else {
            AddLogEntry("INFO", QString(u8"✅ 控制通道数据已同步"));
        }
        
        return true;
        
    } catch (const std::exception& e) {
        AddLogEntry("ERROR", QString(u8"控制通道数据同步异常: %1").arg(e.what()));
        return false;
    }
}

/**
 * @brief 同步JSON导出器与数据管理器
 * 
 * 确保JSON导出器能够获取到最新的、一致的数据。
 * 这是解决333333.json和555555.json数据不一致的最后一步。
 */
void CMyMainWindow::SynchronizeJSONExporterWithDataManagers() {
    AddLogEntry("INFO", QString(u8"🔄 开始同步JSON导出器与数据管理器..."));
    
    try {
        // 重新初始化JSON导出器
        if (jsonDataExporter_) {
            // 确保JSON导出器使用最新的XLS导出器
            jsonDataExporter_->setXLSExporter(xlsDataExporter_.get());
            
            // 确保JSON导出器使用最新的控制通道数据管理器
            if (ctrlChanDataManager_) {
                jsonDataExporter_->setCtrlChanDataManager(ctrlChanDataManager_.get());
            }
            
            AddLogEntry("INFO", QString(u8"✅ JSON导出器已更新数据源"));
        } else {
            // 如果JSON导出器未初始化，重新初始化
            initializeJSONExporter();
            AddLogEntry("INFO", QString(u8"✅ JSON导出器已重新初始化"));
        }
        
        // 验证JSON导出器能否正确获取数据
        if (jsonDataExporter_ && xlsDataExporter_) {
            // 测试数据获取
            int actuatorGroups = 0, sensorGroups = 0, hardwareNodes = 0, channelGroups = 0;
            
            if (xlsDataExporter_->getActuatorDataManager()) {
                actuatorGroups = xlsDataExporter_->getActuatorDataManager()->getAllActuatorGroups().size();
            }
            if (xlsDataExporter_->getSensorDataManager()) {
                sensorGroups = xlsDataExporter_->getSensorDataManager()->getAllSensorGroups().size();
            }
            hardwareNodes = xlsDataExporter_->getHardwareNodeConfigs().size();
            if (xlsDataExporter_->getCtrlChanDataManager()) {
                channelGroups = xlsDataExporter_->getCtrlChanDataManager()->getAllControlChannelGroups().size();
            }
            
            AddLogEntry("INFO", QString(u8"🔍 JSON导出器数据验证：作动器组=%1，传感器组=%2，硬件节点=%3，控制通道组=%4")
                       .arg(actuatorGroups).arg(sensorGroups).arg(hardwareNodes).arg(channelGroups));
        }
        
    } catch (const std::exception& e) {
        AddLogEntry("ERROR", QString(u8"同步JSON导出器时发生异常: %1").arg(e.what()));
    } catch (...) {
        AddLogEntry("ERROR", QString(u8"同步JSON导出器时发生未知异常"));
    }
}

//     QString fileName = QFileDialog::getSaveFileName(this,
//         u8"导出控制通道详细配置",
//         defaultPath,
//         u8"Excel文件 (*.xlsx);;所有文件 (*)");

//     if (fileName.isEmpty()) {
//         AddLogEntry("INFO", "用户取消了控制通道详细配置导出");
//         return;
//     }

//     // 🔄 完全参考作动器实现：使用xlsDataExporter_进行导出
//     bool success = xlsDataExporter_->exportControlChannelDetails(channelGroups, fileName);
//     handleExportResult(success, fileName, u8"导出控制通道详细配置到Excel",
//                       QString(u8"控制通道详细配置已成功导出到：\n%1\n\n包含 %2 个控制通道组，共 %3 个通道")
//                       .arg(fileName).arg(channelGroups.size()).arg(totalChannels));
// }

// 在树形控件中创建硬件节点
void CMyMainWindow::CreateHardwareNodeInTree(const UI::CreateHardwareNodeParams& params) {
    if (!ui->hardwareTreeWidget) return;

    // 检查硬件节点名称是否已存在
    if (IsHardwareNodeNameExists(params.nodeName)) {
        QMessageBox::warning(this, tr("名称重复"),
            QString("硬件节点名称 '%1' 已存在！\n请使用不同的名称。").arg(params.nodeName));
        AddLogEntry("WARNING", QString("硬件节点名称重复: %1").arg(params.nodeName));
        return;
    }

    // 获取任务1根节点
    QTreeWidgetItem* taskRoot = ui->hardwareTreeWidget->topLevelItem(0);
    if (!taskRoot) return;

    // 获取硬件节点资源子节点（第3个子节点，索引为2）
    QTreeWidgetItem* hardwareRoot = taskRoot->child(2);
    if (!hardwareRoot) return;

    // 创建硬件节点
    QTreeWidgetItem* nodeItem = new QTreeWidgetItem(hardwareRoot);
    nodeItem->setText(0, params.nodeName);
    nodeItem->setData(0, Qt::UserRole, "硬件节点"); // 设置类型为硬件节点
    nodeItem->setExpanded(true);

    // 为硬件节点设置包含所有通道信息的tooltip
    QString nodeTooltip;
    for (int i = 0; i < params.channels.size(); ++i) {
        const UI::ChannelInfo& ch = params.channels[i];
        if (i > 0) nodeTooltip += "\n";
        nodeTooltip += QString("CH%1: IP=%2, Port=%3, %4")
                      .arg(ch.channelId)
                      .arg(ch.ipAddress)
                      .arg(ch.port)
                      .arg(ch.enabled ? "启用" : "禁用");
    }
    nodeItem->setToolTip(0, nodeTooltip);

    // 为每个通道创建子节点
    for (int i = 0; i < params.channels.size(); ++i) {
        const UI::ChannelInfo& ch = params.channels[i];

        QTreeWidgetItem* channelItem = new QTreeWidgetItem(nodeItem);
        channelItem->setText(0, QString("CH%1").arg(ch.channelId));
        channelItem->setData(0, Qt::UserRole, "硬件节点通道"); // 设置类型为硬件节点通道

        // 设置通道的详细信息作为工具提示
        QString tooltip = QString("节点: %1\n通道: CH%2\nIP地址: %3\n端口: %4\n状态: %5")
                         .arg(params.nodeName)
                         .arg(ch.channelId)
                         .arg(ch.ipAddress)
                         .arg(ch.port)
                         .arg(ch.enabled ? "启用" : "禁用");
        channelItem->setToolTip(0, tooltip);
    }

    // 展开硬件节点资源根节点以显示新创建的节点
    hardwareRoot->setExpanded(true);

    // 🆕 新增：将硬件节点数据添加到HardwareNodeResDataManager
    if (hardwareNodeResDataManager_) {
        UI::NodeConfigParams nodeConfig;
        nodeConfig.nodeName = params.nodeName;
        nodeConfig.channelCount = params.channelCount;
        nodeConfig.channels = params.channels;

        bool success = hardwareNodeResDataManager_->addHardwareNodeConfig(nodeConfig);
        if (success) {
            AddLogEntry("INFO", QString("硬件节点已添加到数据管理器: %1").arg(params.nodeName));
        } else {
            AddLogEntry("WARNING", QString("硬件节点添加到数据管理器失败: %1").arg(params.nodeName));
        }
    } else {
        AddLogEntry("ERROR", QString("硬件节点资源数据管理器未初始化"));
    }

    // 注意：不自动触发智能通道关联，保持试验配置树的关联信息为空
    // UpdateSmartChannelAssociations();

    // 🆕 新增：添加硬件节点资源后更新所有树形控件节点提示
    UpdateAllTreeWidgetTooltips();
}

void CMyMainWindow::UpdateHardwareNodeChannelsInTree(QTreeWidgetItem* nodeItem, const UI::CreateHardwareNodeParams& params) {
    if (!nodeItem) return;

    // 更新硬件节点的tooltip
    QString nodeTooltip;
    for (int i = 0; i < params.channels.size(); ++i) {
        const UI::ChannelInfo& ch = params.channels[i];
        if (i > 0) nodeTooltip += "\n";
        nodeTooltip += QString("CH%1: IP=%2, Port=%3, %4")
                      .arg(ch.channelId)
                      .arg(ch.ipAddress)
                      .arg(ch.port)
                      .arg(ch.enabled ? "启用" : "禁用");
    }
    nodeItem->setToolTip(0, nodeTooltip);

    // 清除现有的子节点（通道）
    QList<QTreeWidgetItem*> childrenToRemove;
    for (int i = 0; i < nodeItem->childCount(); ++i) {
        QTreeWidgetItem* child = nodeItem->child(i);
        if (child && child->data(0, Qt::UserRole).toString() == "硬件节点通道") {
            childrenToRemove.append(child);
        }
    }
    
    for (QTreeWidgetItem* child : childrenToRemove) {
        nodeItem->removeChild(child);
        delete child;
    }

    // 重新创建通道子节点
    for (int i = 0; i < params.channels.size(); ++i) {
        const UI::ChannelInfo& ch = params.channels[i];

        QTreeWidgetItem* channelItem = new QTreeWidgetItem(nodeItem);
        channelItem->setText(0, QString("CH%1").arg(ch.channelId));
        channelItem->setData(0, Qt::UserRole, "硬件节点通道"); // 设置类型为硬件节点通道

        // 设置通道的详细信息作为工具提示
        QString tooltip = QString("节点: %1\n通道: CH%2\nIP地址: %3\n端口: %4\n状态: %5")
                         .arg(params.nodeName)
                         .arg(ch.channelId)
                         .arg(ch.ipAddress)
                         .arg(ch.port)
                         .arg(ch.enabled ? "启用" : "禁用");
        channelItem->setToolTip(0, tooltip);
    }

    // 确保节点保持展开状态
    nodeItem->setExpanded(true);
}

// ==================== CSV路径记忆功能实现 ====================

//QString CMyMainWindow::GetLastUsedCSVPath() const {
//    if (!lastUsedCSVPath_.isEmpty() && QDir(lastUsedCSVPath_).exists()) {
//        return lastUsedCSVPath_;
//    }

//    // 如果记忆路径无效，返回默认路径
//    return GetDefaultCSVPath();
//}

//void CMyMainWindow::SaveLastUsedCSVPath(const QString& path) {
//    if (path.isEmpty()) {
//        return;
//    }

//    // 提取目录路径
//    QFileInfo fileInfo(path);
//    QString dirPath;

//    if (fileInfo.isDir()) {
//        dirPath = path;
//    } else {
//        dirPath = fileInfo.absolutePath();
//    }

//    // 只有当路径确实改变时才更新
//    if (dirPath != lastUsedCSVPath_) {
//        lastUsedCSVPath_ = QDir::toNativeSeparators(dirPath);

//        // 立即保存到配置文件
//        SaveCSVPathSettings();

//        AddLogEntry("INFO", QString(u8"更新CSV路径记忆: %1").arg(lastUsedCSVPath_));
//    }
//}

//QString CMyMainWindow::GetCSVPathConfigFile() const {
//    if (csvPathConfigFile_.isEmpty()) {
//        // 配置文件保存在exe同目录下
//        QString appDir = QCoreApplication::applicationDirPath();
//        return QDir(appDir).absoluteFilePath("csv_path_config.ini");
//    }
//    return csvPathConfigFile_;
//}

//bool CMyMainWindow::LoadCSVPathSettings() {
//    QString configFile = GetCSVPathConfigFile();

//    if (!QFile::exists(configFile)) {
//        AddLogEntry("INFO", u8"CSV路径配置文件不存在，使用默认设置");
//        return true; // 不存在不算错误
//    }

//    QSettings settings(configFile, QSettings::IniFormat);
//    settings.setIniCodec("UTF-8"); // 确保中文路径正确处理

//    // 读取上次使用的CSV路径
//    QString savedPath = settings.value("CSV/LastUsedPath", "").toString();

//    if (!savedPath.isEmpty() && QDir(savedPath).exists()) {
//        lastUsedCSVPath_ = savedPath;
//        AddLogEntry("INFO", QString(u8"加载CSV路径记忆: %1").arg(lastUsedCSVPath_));
//        return true;
//    } else if (!savedPath.isEmpty()) {
//        AddLogEntry("WARNING", QString(u8"记忆的CSV路径不存在: %1，将使用默认路径").arg(savedPath));
//    }

//    return true;
//}

//bool CMyMainWindow::SaveCSVPathSettings() {
//    QString configFile = GetCSVPathConfigFile();

//    // 确保配置文件目录存在
//    QFileInfo fileInfo(configFile);
//    QDir dir = fileInfo.absoluteDir();
//    if (!dir.exists()) {
//        dir.mkpath(".");
//    }

//    QSettings settings(configFile, QSettings::IniFormat);
//    settings.setIniCodec("UTF-8"); // 确保中文路径正确保存

//    // 保存当前的CSV路径
//    if (!lastUsedCSVPath_.isEmpty()) {
//        settings.setValue("CSV/LastUsedPath", lastUsedCSVPath_);

//        // 添加时间戳和版本信息
//        settings.setValue("CSV/LastUpdateTime", QDateTime::currentDateTime().toString(Qt::ISODate));
//        settings.setValue("CSV/ConfigVersion", "1.0");

//        // 强制写入
//        settings.sync();

//        if (settings.status() == QSettings::NoError) {
//            AddLogEntry("INFO", QString(u8"CSV路径配置保存成功: %1").arg(configFile));
//            return true;
//        } else {
//            AddLogEntry("ERROR", QString(u8"CSV路径配置保存失败: %1").arg(configFile));
//            return false;
//        }
//    }

//    return true;
//}

//QString CMyMainWindow::GetSmartCSVPath() const {
//    // 优先使用记忆路径
//    QString smartPath = GetLastUsedCSVPath();

//    // 验证路径有效性
//    if (!QDir(smartPath).exists()) {
//        // 如果记忆路径无效，尝试创建
//        QDir dir;
//        if (!dir.mkpath(smartPath)) {
//            // 创建失败，使用默认路径
//            smartPath = GetDefaultCSVPath();
//            qDebug() << u8"无法创建记忆路径，使用默认路径:" << smartPath;
//        }
//    }

//    return smartPath;
//}

//void CMyMainWindow::UpdateCSVPathMemory(const QString& filePath) {
//    if (filePath.isEmpty()) {
//        return;
//    }

//    // 提取文件所在目录
//    QFileInfo fileInfo(filePath);
//    QString dirPath = fileInfo.absolutePath();

//    // 只有当路径不是默认路径时才记忆
//    QString defaultPath = GetDefaultCSVPath();
//    if (dirPath != defaultPath) {
//        SaveLastUsedCSVPath(dirPath);
//        AddLogEntry("INFO", QString(u8"记忆CSV操作路径: %1").arg(dirPath));
//    }
//}

// ==================== JSON导出功能实现 ====================

//bool CMyMainWindow::ExportDataToJSON(const QVector<QStringList>& data, const QString& fileName, const QString& subDir) {
//    if (data.isEmpty()) {
//        AddLogEntry("WARNING", u8"数据为空，无法导出JSON");
//        return false;
//    }

//    AddLogEntry("INFO", QString(u8"开始导出数据到JSON: %1").arg(fileName));

//    // 第一步：先保存为CSV文件
//    QString csvFileName = fileName;
//    if (csvFileName.endsWith(".json", Qt::CaseInsensitive)) {
//        csvFileName.replace(".json", ".csv", Qt::CaseInsensitive);
//    } else if (!csvFileName.endsWith(".csv", Qt::CaseInsensitive)) {
//        csvFileName += ".csv";
//    }

//    // 导出CSV文件
//    bool csvSuccess = ExportDataToCSV(data, csvFileName, subDir);
//    if (!csvSuccess) {
//        AddLogEntry("ERROR", QString(u8"CSV导出失败，无法继续JSON导出: %1").arg(csvFileName));
//        return false;
//    }

//    // 第二步：获取CSV文件路径并转换为JSON
//    QString csvFilePath = GenerateCSVFilePath(csvFileName, subDir);
//    QString jsonFilePath = GenerateJSONFilePath(fileName, subDir);

//    // 执行CSV到JSON的转换
//    bool jsonSuccess = ConvertCSVToJSON(csvFilePath, jsonFilePath);

//    if (jsonSuccess) {
//        // 更新路径记忆（JSON文件也记忆）
//        UpdateCSVPathMemory(jsonFilePath);

//        AddLogEntry("INFO", QString(u8"JSON数据导出成功: %1").arg(jsonFilePath));

//        // 可选：删除临时CSV文件（如果用户只需要JSON）
//        // QFile::remove(csvFilePath);

//    } else {
//        AddLogEntry("ERROR", QString(u8"JSON数据导出失败: %1").arg(jsonFilePath));
//    }

//    return jsonSuccess;
//}

//bool CMyMainWindow::QuickSaveProjectToJSON(QString* savedPath, const QString& baseName, bool useTimestamp) {
//    if (!currentProject_) {
//        AddLogEntry("ERROR", u8"没有当前项目，无法保存JSON");
//        return false;
//    }

//    // 确保目录存在
//    if (!EnsureCSVDirectoryExists()) {
//        return false;
//    }

//    // 生成文件名
//    QString fileName;
//    if (!baseName.isEmpty()) {
//        fileName = baseName;
//    } else if (!currentProject_->projectName.empty()) {
//        fileName = QString::fromStdString(currentProject_->projectName);
//    } else {
//        fileName = u8"实验工程";
//    }

//    // 添加时间戳（如果需要）
//    if (useTimestamp) {
//        QString timestampedName = GenerateTimestampedFileName(fileName, true);
//        // 将.csv扩展名替换为.json
//        fileName = timestampedName.replace(".csv", ".json", Qt::CaseInsensitive);
//    } else {
//        if (!fileName.endsWith(".json", Qt::CaseInsensitive)) {
//            fileName += ".json";
//        }
//    }

//    // 生成完整路径
//    QString fullPath = GenerateJSONFilePath(fileName);

//    // 第一步：先保存为CSV
//    QString csvPath = fullPath;
//    csvPath.replace(".json", ".csv", Qt::CaseInsensitive);

//    bool csvSuccess = SaveProjectToCSV(csvPath);
//    if (!csvSuccess) {
//        AddLogEntry("ERROR", QString(u8"项目CSV保存失败，无法继续JSON保存: %1").arg(csvPath));
//        return false;
//    }

//    // 第二步：转换CSV为JSON
//    bool jsonSuccess = ConvertCSVToJSON(csvPath, fullPath);

//    if (jsonSuccess) {
//        // 更新路径记忆
//        UpdateCSVPathMemory(fullPath);

//        AddLogEntry("INFO", QString(u8"项目快速JSON保存成功: %1").arg(fullPath));
//        if (savedPath) {
//            *savedPath = fullPath;
//        }

//        // 可选：保留CSV文件作为备份
//        AddLogEntry("INFO", QString(u8"CSV备份文件保留: %1").arg(csvPath));

//    } else {
//        AddLogEntry("ERROR", QString(u8"项目快速JSON保存失败: %1").arg(fullPath));
//    }

//    return jsonSuccess;
//}

//QString CMyMainWindow::GenerateJSONFilePath(const QString& fileName, const QString& subDir) const {
//    // 使用智能路径（优先使用记忆路径）
//    QString basePath = GetSmartCSVPath();

//    // 如果指定了子目录，添加到路径中
//    if (!subDir.isEmpty()) {
//        basePath = QDir(basePath).absoluteFilePath(subDir);

//        // 确保子目录存在
//        QDir dir;
//        if (!dir.exists(basePath)) {
//            dir.mkpath(basePath);
//        }
//    }

//    // 确保文件名有正确的JSON扩展名
//    QString jsonFileName = fileName;
//    if (!jsonFileName.endsWith(".json", Qt::CaseInsensitive)) {
//        if (jsonFileName.endsWith(".csv", Qt::CaseInsensitive)) {
//            jsonFileName.replace(".csv", ".json", Qt::CaseInsensitive);
//        } else {
//            jsonFileName += ".json";
//        }
//    }

//    // 构建完整文件路径
//    QString fullPath = QDir(basePath).absoluteFilePath(jsonFileName);

//    return QDir::toNativeSeparators(fullPath);
//}

//bool CMyMainWindow::ConvertCSVToJSON(const QString& csvFilePath, const QString& jsonFilePath) {
//    AddLogEntry("INFO", QString(u8"开始CSV到JSON转换: %1 -> %2").arg(csvFilePath).arg(jsonFilePath));

//    // 检查CSV文件是否存在
//    if (!QFile::exists(csvFilePath)) {
//        AddLogEntry("ERROR", QString(u8"CSV文件不存在: %1").arg(csvFilePath));
//        return false;
//    }

//    try {
//        // 使用CSV管理器加载CSV文件
//        if (!csvManager_->loadFromFile(csvFilePath)) {
//            AddLogEntry("ERROR", QString(u8"加载CSV文件失败: %1 - %2").arg(csvFilePath).arg(csvManager_->getErrorString()));
//            return false;
//        }

//        // 使用CSV管理器的JSON导出功能
//        bool success = csvManager_->exportToFormat(jsonFilePath, "json");

//        if (success) {
//            AddLogEntry("INFO", QString(u8"CSV到JSON转换成功: %1").arg(jsonFilePath));

//            // 验证生成的JSON文件
//            QFileInfo jsonInfo(jsonFilePath);
//            if (jsonInfo.exists() && jsonInfo.size() > 0) {
//                AddLogEntry("INFO", QString(u8"JSON文件验证成功，大小: %1 字节").arg(jsonInfo.size()));
//            } else {
//                AddLogEntry("WARNING", QString(u8"JSON文件可能为空或损坏: %1").arg(jsonFilePath));
//            }

//        } else {
//            AddLogEntry("ERROR", QString(u8"CSV到JSON转换失败: %1 - %2").arg(jsonFilePath).arg(csvManager_->getErrorString()));
//        }

//        return success;

//    } catch (const std::exception& e) {
//        AddLogEntry("ERROR", QString(u8"CSV到JSON转换异常: %1").arg(e.what()));
//        return false;
//    }
//}

// ============================================================================
// 🆕 新增：传感器数据管理方法实现
// ============================================================================

// 初始化传感器数据管理器
void CMyMainWindow::initializeSensorDataManager() {
    // 🔄 修改：清理所有现有数据，确保干净的数据环境
    if (sensorDataManager_) {
        sensorDataManager_->clearAllSensors();
        sensorDataManager_->clearAllSensorGroups();
        AddLogEntry("INFO", u8"已清理所有传感器数据，传感器数据管理器已初始化");
    }
}

// bool CMyMainWindow::saveSensorDetailedParams(const UI::SensorParams_1_2& params) {
//     if (!sensorDataManager_) {
//         return false;
//     }
//     return sensorDataManager_->addSensor(params);
// }

// // 🆕 新增：支持组内重复序列号的保存方法
// bool CMyMainWindow::saveOrUpdateSensorDetailedParams(const UI::SensorParams_1_2& params) {
//     if (!sensorDataManager_) {
//         return false;
//     }

//     // 检查传感器是否已存在（14字段新需求版本）
//     if (sensorDataManager_->hasSensor(params.params_sn)) {
//         // 如果已存在，更新而不是添加
//         return sensorDataManager_->updateSensor(params.params_sn, params);
//     } else {
//         // 如果不存在，添加新的
//         return sensorDataManager_->addSensor(params);
//     }
// }

// UI::SensorParams_1_2 CMyMainWindow::getSensorDetailedParams(const QString& serialNumber) const {
//     if (!sensorDataManager_) {
//         return UI::SensorParams_1_2();
//     }
//     return sensorDataManager_->getSensor(serialNumber);
// }

// bool CMyMainWindow::updateSensorDetailedParams(const QString& serialNumber, const UI::SensorParams_1_2& params) {
//     if (!sensorDataManager_) {
//         return false;
//     }
//     return sensorDataManager_->updateSensor(serialNumber, params);
// }

// // 注释：updateSensorList 方法已删除，因为界面中没有对应控件且未在头文件中声明

// bool CMyMainWindow::removeSensorDetailedParams(const QString& serialNumber) {
//     if (!sensorDataManager_) {
//         return false;
//     }
//     return sensorDataManager_->removeSensor(serialNumber);
// }

// QStringList CMyMainWindow::getAllSensorSerialNumbers() const {
//     if (!sensorDataManager_) {
//         return QStringList();
//     }
//     return sensorDataManager_->getAllSensorSerialNumbers();
// }

// QList<UI::SensorParams_1_2> CMyMainWindow::getAllSensorDetailedParams() const {
//     if (!sensorDataManager_) {
//         return QList<UI::SensorParams_1_2>();
//     }
//     return sensorDataManager_->getAllSensors();
// }

// 🆕 新增：带组ID的传感器数据管理接口实现
bool CMyMainWindow::saveOrUpdateSensorDetailedParamsInGroup(int groupId, const UI::SensorParams_1_2& params) {
    if (!sensorDataManager_) {
        return false;
    }
    
    // 检查传感器是否已存在于指定组中
    bool hasData = false;
    UI::SensorParams_1_2 existingSensor = sensorDataManager_->getSensorDetailedParamsInGroup(groupId, params.params_sn, hasData);
    if (hasData) {
        // 如果已存在，更新而不是添加
        return sensorDataManager_->updateSensorDetailedParamsInGroup(groupId, params.params_sn, params);
    } else {
        // 如果不存在，添加新的
        return sensorDataManager_->saveSensorDetailedParamsInGroup(groupId, params);
    }
}

UI::SensorParams_1_2 CMyMainWindow::getSensorDetailedParamsInGroup(int groupId, const QString& serialNumber, bool &isHasData) const {
    if (!sensorDataManager_) {
        isHasData = false;
        return UI::SensorParams_1_2();
    }
    return sensorDataManager_->getSensorDetailedParamsInGroup(groupId, serialNumber, isHasData);
}

bool CMyMainWindow::updateSensorDetailedParamsInGroup(int groupId, const QString& serialNumber, const UI::SensorParams_1_2& params) {
    if (!sensorDataManager_) {
        return false;
    }
    return sensorDataManager_->updateSensorDetailedParamsInGroup(groupId, serialNumber, params);
}

bool CMyMainWindow::removeSensorDetailedParamsInGroup(int groupId, const QString& serialNumber) {
    if (!sensorDataManager_) {
        return false;
    }
    return sensorDataManager_->removeSensorDetailedParamsInGroup(groupId, serialNumber);
}

// 🆕 新增：组内保存传感器详细参数
bool CMyMainWindow::saveSensorDetailedParamsInGroup(int groupId, const UI::SensorParams_1_2& params) {
    if (!sensorDataManager_) {
        return false;
    }
    return sensorDataManager_->saveSensorDetailedParamsInGroup(groupId, params);
}

// 🆕 新增：组内获取所有传感器序列号
QStringList CMyMainWindow::getAllSensorSerialNumbersInGroup(int groupId) const {
    if (!sensorDataManager_) {
        return QStringList();
    }
    return sensorDataManager_->getAllSensorSerialNumbersInGroup(groupId);
}

// 🆕 新增：组内获取所有传感器详细参数
QList<UI::SensorParams_1_2> CMyMainWindow::getAllSensorDetailedParamsInGroup(int groupId) const {
    if (!sensorDataManager_) {
        return QList<UI::SensorParams_1_2>();
    }
    return sensorDataManager_->getAllSensorDetailedParamsInGroup(groupId);
}

bool CMyMainWindow::validateSensorData() const {
    if (!sensorDataManager_) {
        return false;
    }

    QStringList errors = sensorDataManager_->validateAllSensors();
    if (!errors.isEmpty()) {
        for (const QString& error : errors) {
            AddLogEntry("WARNING", QString(u8"传感器数据验证: %1").arg(error));
        }
        return false;
    }
    return true;
}

QMap<QString, int> CMyMainWindow::getSensorTypeStatistics() const {
    if (!sensorDataManager_) {
        return QMap<QString, int>();
    }
    return sensorDataManager_->getSensorTypeStatistics();
}

// 🆕 新增：传感器存储一致性验证
bool CMyMainWindow::validateSensorStorageConsistency() const {
    if (!sensorDataManager_) {
        AddLogEntry("ERROR", QString(u8"❌ 传感器数据管理器未初始化"));
        return false;
    }
    
    // 调用数据管理器的一致性验证
    if (!sensorDataManager_->validateStorageConsistency()) {
        AddLogEntry("WARNING", QString(u8"⚠️ 检测到传感器存储结构不一致"));
        return false;
    }
    
    AddLogEntry("INFO", QString(u8"✅ 传感器存储结构一致性验证通过"));
    return true;
}

// 🔄 已迁移：作动器数据管理接口已迁移到ActuatorViewModel1_2中

// 🔄 重构：统一使用数据管理器，移除硬件树提取功能
QList<UI::ActuatorGroup_1_2> CMyMainWindow::getAllActuatorGroups_MainDlg() const {
    if (!actuatorViewModel1_2_) {
        AddLogEntry("WARNING", u8"作动器视图模型未初始化");
        return QList<UI::ActuatorGroup_1_2>();
    }

    // 🔄 统一使用视图模型获取作动器组数据
    QList<UI::ActuatorGroup_1_2> actuatorGroups = actuatorViewModel1_2_->getAllActuatorGroups();

    AddLogEntry("INFO", QString(u8"从数据管理器获取作动器组: %1个组").arg(actuatorGroups.size()));

    return actuatorGroups;
}

// 🔄 重构：统一使用数据管理器，移除硬件树提取功能
QList<UI::SensorGroup_1_2> CMyMainWindow::getAllSensorGroups() const {
    if (!sensorDataManager_) {
        AddLogEntry("WARNING", u8"传感器数据管理器未初始化");
        return QList<UI::SensorGroup_1_2>();
    }

    // 🔄 统一使用数据管理器获取传感器组数据
    QList<UI::SensorGroup_1_2> sensorGroups = sensorDataManager_->getAllSensorGroups();

    AddLogEntry("INFO", QString(u8"从数据管理器获取传感器组: %1个组").arg(sensorGroups.size()));

    return sensorGroups;
}

// ============================================================================
// 🆕 新增：控制通道数据管理方法实现
// ============================================================================

// 初始化控制通道数据管理器
void CMyMainWindow::initializeCtrlChanDataManager() {
    if (!ctrlChanDataManager_) {
        ctrlChanDataManager_.reset(new CtrlChanDataManager(this));
        qDebug() << "CtrlChanDataManager 初始化完成";

        // 连接信号槽
        connect(ctrlChanDataManager_.get(), &CtrlChanDataManager::controlChannelGroupCreated,
                this, [this](int groupId) {
                    AddLogEntry("INFO", QString("控制通道组已创建: ID=%1").arg(groupId));
                });

        connect(ctrlChanDataManager_.get(), &CtrlChanDataManager::controlChannelAdded,
                this, [this](int groupId, const QString& channelId) {
                    AddLogEntry("INFO", QString("控制通道已添加: 组ID=%1, 通道ID=%2").arg(groupId).arg(channelId));
                });
    }
}

// ============================================================================
// 🆕 新增：硬件节点资源数据管理方法实现
// ============================================================================

// 初始化硬件节点资源数据管理器
void CMyMainWindow::initializeHardwareNodeResDataManager() {
    if (!hardwareNodeResDataManager_) {
        hardwareNodeResDataManager_.reset(new HardwareNodeResDataManager(this));
        qDebug() << "HardwareNodeResDataManager 初始化完成";

        // 连接信号槽
        connect(hardwareNodeResDataManager_.get(), &HardwareNodeResDataManager::hardwareNodeConfigAdded,
                this, [this](const QString& nodeName) {
                    AddLogEntry("INFO", QString("硬件节点配置已添加: %1").arg(nodeName));
                });

        connect(hardwareNodeResDataManager_.get(), &HardwareNodeResDataManager::hardwareNodeConfigUpdated,
                this, [this](const QString& nodeName) {
                    AddLogEntry("INFO", QString("硬件节点配置已更新: %1").arg(nodeName));
                });

        connect(hardwareNodeResDataManager_.get(), &HardwareNodeResDataManager::hardwareNodeConfigRemoved,
                this, [this](const QString& nodeName) {
                    AddLogEntry("INFO", QString("硬件节点配置已删除: %1").arg(nodeName));
                });
    }
}

/**
 * @brief 初始化树形控件交互处理器
 */
// void CMyMainWindow::initializeTreeInteractionHandler() { // 🚫 已移除：树形控件交互处理器
//     // 检查树形控件是否存在
//     if (!ui->testConfigTreeWidget) {
//         AddLogEntry("ERROR", "树形控件未找到，交互处理器初始化失败");
//         return;
//     }
//     
//     // 创建交互处理器（使用新的详细信息面板）
//     treeInteractionHandler_ = std::make_unique<TreeInteractionHandler>(
//         ui->testConfigTreeWidget, 
//         detailInfoPanel_.get(), // 传递详细信息面板原始指针
//         this, 
//         this
//     );
//     
//     if (treeInteractionHandler_) {
//         AddLogEntry("INFO", "树形控件交互处理器初始化成功");
//     } else {
//         AddLogEntry("ERROR", "树形控件交互处理器创建失败");
//     }
// }

// 🆕 收集传感器详细数据用于导出
bool CMyMainWindow::collectSensorDetailedDataForExport(QJsonArray& jsonArray) {
    if (!sensorDataManager_) {
        return false;
    }

    try {
        // 获取所有传感器详细参数
        QList<UI::SensorParams_1_2> sensors = sensorDataManager_->getAllSensors();

        if (sensors.isEmpty()) {
            // 如果没有传感器，不添加传感器数据节
            return true;
        }

        // 添加传感器详细数据分节标题
        QJsonObject sensorSectionObj;
        sensorSectionObj["# 实验工程配置文件"] = "# 传感器详细配置";
        sensorSectionObj["field2"] = "";
        sensorSectionObj["field3"] = "";
        sensorSectionObj["field4"] = "";
        sensorSectionObj["field5"] = "";
        jsonArray.append(sensorSectionObj);

        // 为每个传感器添加详细信息
        for (const UI::SensorParams_1_2& params : sensors) {
            // 传感器基本信息（14字段新需求版本）
            QJsonObject basicInfoObj;
            basicInfoObj["# 实验工程配置文件"] = QString(u8"传感器基本信息");
            basicInfoObj["field2"] = params.params_sn;     // 使用新字段
            basicInfoObj["field3"] = params.params_model;  // 使用新字段
            basicInfoObj["field4"] = QString(u8"精度: %1").arg(params.params_precision);
            basicInfoObj["field5"] = QString(u8"极性: %1").arg(polarityToString(params.params_polarity));
            jsonArray.append(basicInfoObj);

            // 传感器详细参数（注意：14字段新需求不包含这些字段，先注释掉）
            // QJsonObject detailsObj;
            // detailsObj["# 实验工程配置文件"] = QString(u8"传感器详细参数");
            // detailsObj["field2"] = QString(u8"EDS标识: %1").arg(params.edsId);
            // detailsObj["field3"] = QString(u8"尺寸: %1").arg(params.dimension);
            // detailsObj["field4"] = QString(u8"单位: %1").arg(params.unit);
            // detailsObj["field5"] = QString(u8"灵敏度: %1").arg(params.sensitivity);
            // jsonArray.append(detailsObj);

            // 注释：14字段新需求不包含校准相关字段，所以注释掉整个校准信息块
            // if (params.calibrationEnabled) {
            //     QJsonObject calibrationObj;
            //     calibrationObj["# 实验工程配置文件"] = QString(u8"校准信息");
            //     calibrationObj["field2"] = QString(u8"校准日期: %1").arg(params.calibrationDate);
            //     calibrationObj["field3"] = QString(u8"校准人员: %1").arg(params.performedBy);
            //     calibrationObj["field4"] = QString(u8"单位类型: %1").arg(params.unitType);
            //     calibrationObj["field5"] = QString(u8"满量程: %1 %2").arg(params.fullScaleMax).arg(params.fullScaleMaxUnit);
            //     jsonArray.append(calibrationObj);
            // }

            // 信号调理信息（14字段新需求版本）
            QJsonObject conditioningObj;
            conditioningObj["# 实验工程配置文件"] = QString(u8"信号调理");
            conditioningObj["field2"] = QString(u8"极性: %1").arg(polarityToString(params.params_polarity));
            conditioningObj["field3"] = QString(u8"系数K: %1").arg(params.params_k);
            conditioningObj["field4"] = QString(u8"系数B: %1").arg(params.params_b);
            conditioningObj["field5"] = QString(u8"精度: %1").arg(params.params_precision);
            jsonArray.append(conditioningObj);

            // 激励信息（注意：14字段新需求不包含这些字段，先注释掉）
            // if (params.enableExcitation) {
            //     QJsonObject excitationObj;
            //     excitationObj["# 实验工程配置文件"] = QString(u8"激励设置");
            //     excitationObj["field2"] = QString(u8"激励电压: %1V").arg(params.excitationVoltage);
            //     excitationObj["field3"] = QString(u8"激励频率: %1").arg(params.excitationFrequency);
            //     excitationObj["field4"] = QString(u8"相位: %1").arg(params.phase);
            //     excitationObj["field5"] = QString(u8"激励平衡: %1").arg(params.excitationBalance);
            //     jsonArray.append(excitationObj);
            // }

            // 添加空行分隔不同传感器
            QJsonObject separatorObj;
            separatorObj["# 实验工程配置文件"] = "";
            separatorObj["field2"] = "";
            separatorObj["field3"] = "";
            separatorObj["field4"] = "";
            separatorObj["field5"] = "";
            jsonArray.append(separatorObj);
        }

        return true;
    } catch (const std::exception& e) {
        AddLogEntry("ERROR", QString(u8"收集传感器详细数据失败: %1").arg(e.what()));
        return false;
    }
}

//// 🆕 添加传感器详细信息到CSV - 基于完整的SensorParams结构体
//void CMyMainWindow::AddSensorDetailToCSV(QTextStream& out, const UI::SensorParams_1_2& params) {
//    // === 基本信息组 (sensorGroupBox) ===
//    // 无论界面控件有无数据，都要保存所有字段
//    out << "," << FormatCSVField(QStringLiteral("  ├─ 序列号")) << "," << FormatCSVField(params.serialNumber) << "," << "" << "," << "" << "\n";
//    out << "," << FormatCSVField(QStringLiteral("  ├─ 类型")) << "," << FormatCSVField(params.sensorType) << "," << "" << "," << "" << "\n";
//    out << "," << FormatCSVField(QStringLiteral("  ├─ 型号")) << "," << FormatCSVField(params.model) << "," << "" << "," << "" << "\n";
//    out << "," << FormatCSVField(QStringLiteral("  ├─ 量程")) << "," << FormatCSVField(params.range) << "," << "" << "," << "" << "\n";
//    out << "," << FormatCSVField(QStringLiteral("  ├─ 精度")) << "," << FormatCSVField(params.accuracy) << "," << "" << "," << "" << "\n";
//    out << "," << FormatCSVField(QStringLiteral("  ├─ EDS标识")) << "," << FormatCSVField(params.edsId) << "," << "" << "," << "" << "\n";
//    out << "," << FormatCSVField(QStringLiteral("  ├─ 尺寸")) << "," << FormatCSVField(params.dimension) << "," << "" << "," << "" << "\n";
//    out << "," << FormatCSVField(QStringLiteral("  ├─ 单位")) << "," << FormatCSVField(params.unit) << "," << "" << "," << "" << "\n";
//    out << "," << FormatCSVField(QStringLiteral("  ├─ 灵敏度")) << "," << FormatCSVField(QString::number(params.sensitivity, 'f', 3)) << "," << "" << "," << "" << "\n";

//    // === 校准和范围信息组 (rangeGroupBox) ===
//    // 无论界面控件有无数据，都要保存所有字段
//    out << "," << FormatCSVField(QStringLiteral("  ├─ 校准启用")) << "," << FormatCSVField(params.calibrationEnabled ? "是" : "否") << "," << "" << "," << "" << "\n";
//    out << "," << FormatCSVField(QStringLiteral("  ├─ 校准日期")) << "," << FormatCSVField(params.calibrationDate) << "," << "" << "," << "" << "\n";
//    out << "," << FormatCSVField(QStringLiteral("  ├─ 校准执行人")) << "," << FormatCSVField(params.performedBy) << "," << "" << "," << "" << "\n";
//    out << "," << FormatCSVField(QStringLiteral("  ├─ 单位类型")) << "," << FormatCSVField(params.unitType) << "," << "" << "," << "" << "\n";
//    out << "," << FormatCSVField(QStringLiteral("  ├─ 单位值")) << "," << FormatCSVField(params.unitValue) << "," << "" << "," << "" << "\n";
//    out << "," << FormatCSVField(QStringLiteral("  ├─ 输入范围")) << "," << FormatCSVField(params.inputRange) << "," << "" << "," << "" << "\n";

//    // 满量程最大值 - 始终保存
//    QString maxValueStr = QString::number(params.fullScaleMax, 'f', 3);
//    if (params.fullScaleMaxValue2 != 0) {
//        maxValueStr += QString(" / %1").arg(params.fullScaleMaxValue2);
//    }
//    out << "," << FormatCSVField(QStringLiteral("  ├─ 满量程最大值")) << "," << FormatCSVField(maxValueStr) << "," << FormatCSVField(params.fullScaleMaxUnit) << "," << FormatCSVField(params.fullScaleMaxCombo) << "\n";

//    // 满量程最小值 - 始终保存
//    QString minValueStr = QString::number(params.fullScaleMin, 'f', 3);
//    if (params.fullScaleMinValue2 != 0) {
//        minValueStr += QString(" / %1").arg(params.fullScaleMinValue2);
//    }
//    out << "," << FormatCSVField(QStringLiteral("  ├─ 满量程最小值")) << "," << FormatCSVField(minValueStr) << "," << FormatCSVField(params.fullScaleMinUnit) << "," << FormatCSVField(params.fullScaleMinCombo) << "\n";
//    out << "," << FormatCSVField(QStringLiteral("  ├─ 允许分离设置")) << "," << FormatCSVField(params.allowSeparateMinMax ? "是" : "否") << "," << "" << "," << "" << "\n";

//    // === 信号调理参数组 (conditioningGroupBox) ===
//    // 无论界面控件有无数据，都要保存所有字段
//    out << "," << FormatCSVField(QStringLiteral("  ├─ 极性")) << "," << FormatCSVField(params.polarity) << "," << "" << "," << "" << "\n";
//    out << "," << FormatCSVField(QStringLiteral("  ├─ 前置放大增益")) << "," << FormatCSVField(params.preAmpGain) << "," << "" << "," << "" << "\n";

//    // 后置放大增益 - 始终保存
//    QString postAmpStr = QString::number(params.postAmpGain, 'f', 4);
//    if (params.postAmpGainValue2 != 0) {
//        postAmpStr += QString(" / %1").arg(params.postAmpGainValue2);
//    }
//    out << "," << FormatCSVField(QStringLiteral("  ├─ 后置放大增益")) << "," << FormatCSVField(postAmpStr) << "," << FormatCSVField(params.postAmpGainCombo) << "," << "" << "\n";

//    // 总增益 - 始终保存
//    QString totalGainStr = QString::number(params.totalGain, 'f', 2);
//    if (params.totalGainValue2 != 0) {
//        totalGainStr += QString(" / %1").arg(params.totalGainValue2);
//    }
//    out << "," << FormatCSVField(QStringLiteral("  ├─ 总增益")) << "," << FormatCSVField(totalGainStr) << "," << FormatCSVField(params.totalGainCombo) << "," << "" << "\n";

//    // Delta K增益 - 始终保存
//    QString deltaKStr = QString::number(params.deltaKGain, 'f', 4);
//    if (params.deltaKGainValue2 != 0) {
//        deltaKStr += QString(" / %1").arg(params.deltaKGainValue2);
//    }
//    out << "," << FormatCSVField(QStringLiteral("  ├─ Delta K增益")) << "," << FormatCSVField(deltaKStr) << "," << FormatCSVField(params.deltaKGainCombo) << "," << "" << "\n";

//    // 比例因子 - 始终保存
//    QString scaleFactorStr = params.scaleFactor;
//    if (params.scaleFactorValue != 0) {
//        scaleFactorStr += QString(" (%1)").arg(params.scaleFactorValue);
//    }
//    out << "," << FormatCSVField(QStringLiteral("  ├─ 比例因子")) << "," << FormatCSVField(scaleFactorStr) << "," << FormatCSVField(params.scaleFactorCombo) << "," << "" << "\n";

//    // === 激励设置 ===
//    // 无论界面控件有无数据，都要保存所有字段
//    out << "," << FormatCSVField(QStringLiteral("  ├─ 激励启用")) << "," << FormatCSVField(params.enableExcitation ? "是" : "否") << "," << "" << "," << "" << "\n";

//    // 激励电压 - 始终保存
//    QString excitationStr = QString::number(params.excitationVoltage, 'f', 1);
//    if (params.excitationValue2 != 0) {
//        excitationStr += QString(" / %1").arg(params.excitationValue2);
//    }
//    out << "," << FormatCSVField(QStringLiteral("  ├─ 激励电压")) << "," << FormatCSVField(excitationStr) << "," << FormatCSVField(params.excitationCombo) << "," << "" << "\n";

//    // 激励平衡 - 始终保存
//    QString balanceStr = params.excitationBalance;
//    if (params.excitationBalanceValue != 0) {
//        balanceStr += QString(" (%1)").arg(params.excitationBalanceValue);
//    }
//    out << "," << FormatCSVField(QStringLiteral("  ├─ 激励平衡")) << "," << FormatCSVField(balanceStr) << "," << FormatCSVField(params.excitationBalanceCombo) << "," << "" << "\n";

//    // 激励频率 - 始终保存
//    out << "," << FormatCSVField(QStringLiteral("  ├─ 激励频率")) << "," << FormatCSVField(params.excitationFrequency) << "," << "" << "," << "" << "\n";

//    // 相位 - 始终保存
//    QString phaseStr = params.phase;
//    if (params.phaseValue != 0) {
//        phaseStr += QString(" (%1)").arg(params.phaseValue);
//    }
//    out << "," << FormatCSVField(QStringLiteral("  ├─ 相位")) << "," << FormatCSVField(phaseStr) << "," << FormatCSVField(params.phaseCombo) << "," << "" << "\n";

//    // === 编码器分辨率 ===
//    // 始终保存
//    QString encoderStr = params.encoderResolution;
//    if (params.encoderResolutionValue != 0) {
//        encoderStr += QString(" (%1)").arg(params.encoderResolutionValue);
//    }
//    out << "," << FormatCSVField(QStringLiteral("  ├─ 编码器分辨率")) << "," << FormatCSVField(encoderStr) << "," << FormatCSVField(params.encoderResolutionCombo) << "," << "" << "\n";

//    // === 其他配置参数（兼容性保留） ===


// ============================================================================
// 🆕 新增：数据导出管理方法实现
// ============================================================================

//void CMyMainWindow::initializeDataExportManager() {
//    // 创建数据导出管理器
//    dataExportManager_ = std::make_unique<DataExportManager>(sensorDataManager_.get(), this);

//    // 连接信号和槽
//    connectExportManagerSignals();
//}

//void CMyMainWindow::connectExportManagerSignals() {
//    if (!dataExportManager_) return;

//    // 连接导出进度信号
//    connect(dataExportManager_.get(), &DataExportManager::exportProgress,
//            this, [this](int progress, const QString& message) {
//                // 更新状态栏或进度条
//                AddLogEntry("INFO", QString(u8"导出进度 %1%: %2").arg(progress).arg(message));
//            });

//    // 连接导出完成信号
//    connect(dataExportManager_.get(), &DataExportManager::exportCompleted,
//            this, [this](bool success, const QString& filePath) {
//                if (success) {
//                    AddLogEntry("INFO", QString(u8"数据导出成功: %1").arg(filePath));
//                } else {
//                    AddLogEntry("ERROR", QString(u8"数据导出失败: %1 - %2")
//                                .arg(filePath).arg(dataExportManager_->getLastError()));
//                }
//            });

//    // 连接导出开始信号
//    connect(dataExportManager_.get(), &DataExportManager::exportStarted,
//            this, [this](const QString& filePath, const QString& formatDescription) {
//                AddLogEntry("INFO", QString(u8"开始导出数据到 %1 (%2)").arg(filePath).arg(formatDescription));
//            });
//}

// ============================================================================
// 🆕 新增：XLS导出功能实现
// ============================================================================

void CMyMainWindow::initializeXLSExporter() {
    try {
        // 🆕 修改：同时传入传感器、作动器和控制通道数据管理器
        xlsDataExporter_ = std::make_unique<XLSDataExporter_1_2>(sensorDataManager_.get(),
                                                           actuatorViewModel1_2_ ? actuatorViewModel1_2_->getDataManager() : nullptr,
                                                           ctrlChanDataManager_.get());

        // 配置XLS导出器的默认选项
        configureXLSExporterOptions(xlsDataExporter_.get());

        AddLogEntry("INFO", QString(u8"XLS导出器初始化成功（包含传感器、作动器和控制通道数据管理器）"));
    } catch (const std::exception& e) {
        AddLogEntry("ERROR", QString(u8"XLS导出器初始化失败: %1").arg(e.what()));
        xlsDataExporter_.reset();
    }
}

void CMyMainWindow::configureXLSExporterOptions(XLSDataExporter_1_2* exporter) {
    if (!exporter) return;

    // 设置默认选项
    exporter->setIncludeHeader(true);           // 包含文件头信息
    exporter->setAutoFitColumns(true);          // 自动调整列宽
    exporter->setUseTableStyle(true);           // 使用表格样式
    exporter->setWorksheetName(u8"硬件配置");    // 设置工作表名称
}

// ============================================================================
// 🆕 JSON导出功能实现
// ============================================================================

void CMyMainWindow::initializeJSONExporter() {
    try {
        // 创建JSON导出器
        jsonDataExporter_ = std::make_unique<JSONDataExporter_1_2>(sensorDataManager_.get());

        // 设置XLS导出器以复用数据收集逻辑
        if (xlsDataExporter_) {
            // 🎯 关键修复：在JSON导出初始化时也要设置作动器数据管理器
            if (actuatorViewModel1_2_) {
                xlsDataExporter_->setActuatorDataManager(actuatorViewModel1_2_->getDataManager());
                int actuatorGroupCount = actuatorViewModel1_2_->getAllActuatorGroups().size();
                AddLogEntry("INFO", QString(u8"JSON导出器：设置作动器数据管理器，共%1个作动器组").arg(actuatorGroupCount));
            }
            
            // 🎯 关键修复：设置硬件节点配置数据到导出器
            if (hardwareNodeResDataManager_) {
                QList<UI::NodeConfigParams> nodeConfigs = hardwareNodeResDataManager_->getAllHardwareNodeConfigs();
                xlsDataExporter_->setHardwareNodeConfigs(nodeConfigs);
                AddLogEntry("INFO", QString(u8"JSON导出器：设置硬件节点配置数据，共%1个配置").arg(nodeConfigs.size()));
            }
            
            jsonDataExporter_->setXLSExporter(xlsDataExporter_.get());
            
            // 🆕 设置控制通道数据管理器
            if (ctrlChanDataManager_) {
                jsonDataExporter_->setCtrlChanDataManager(ctrlChanDataManager_.get());
                auto channelGroups = ctrlChanDataManager_->getAllControlChannelGroups();
                AddLogEntry("INFO", QString(u8"JSON导出器：设置控制通道数据管理器，共%1个组").arg(channelGroups.size()));
            }
        }

        // 配置JSON导出器的默认选项
        jsonDataExporter_->setCompactFormat(false);  // 使用格式化输出
        jsonDataExporter_->setIndentSize(2);         // 设置缩进大小

        AddLogEntry("INFO", QString(u8"JSON导出器初始化成功"));
    } catch (const std::exception& e) {
        AddLogEntry("ERROR", QString(u8"JSON导出器初始化失败: %1").arg(e.what()));
        jsonDataExporter_.reset();
    }
}

// 🚫 已注释：独立JSON导出功能已废弃
//void CMyMainWindow::exportToJSON(const QString& filePath) {
//    if (!jsonDataExporter_) {
//        initializeJSONExporter();
//    }
//
//    if (!jsonDataExporter_) {
//        AddLogEntry("ERROR", "JSON导出器初始化失败，无法导出");
//        return;
//    }
//
//    bool success = jsonDataExporter_->exportCompleteProject(filePath);
//
//    if (success) {
//        AddLogEntry("INFO", QString("工程已导出为JSON: %1").arg(filePath));
//    } else {
//        QString error = jsonDataExporter_->getLastError();
//        AddLogEntry("ERROR", QString("导出JSON失败: %1").arg(error));
//    }
//}

/**
 * @brief 导出工程为channel_config.json格式
 */
void CMyMainWindow::exportProjectAsChannelConfig() {
    // 检查控制通道数据管理器是否存在
    if (!ctrlChanDataManager_) {
        QMessageBox::warning(this, u8"警告", u8"控制通道数据管理器未初始化");
        return;
    }
    
    // 获取所有控制通道组
    QList<UI::ControlChannelGroup> allGroups = ctrlChanDataManager_->getAllControlChannelGroups();
    if (allGroups.isEmpty()) {
        QMessageBox::information(this, u8"提示", u8"当前没有控制通道数据可导出");
        return;
    }
    
    // 选择保存路径
    QString filePath = QFileDialog::getSaveFileName(
        this, u8"导出通道配置JSON",
        QString(u8".json"),
        u8"JSON文件 (*.json)");
    
    if (filePath.isEmpty()) {
        return;
    }
    
    // 确保JSON导出器已初始化
    if (!jsonDataExporter_) {
        initializeJSONExporter();
    }
    
    if (!jsonDataExporter_) {
        QMessageBox::critical(this, u8"错误", u8"JSON导出器初始化失败");
        return;
    }
    
    // 调用我们添加的exportChannelConfig方法
    bool success = jsonDataExporter_->exportChannelConfig(filePath);
    if (success) {
        AddLogEntry("INFO", QString(u8"导出通道配置JSON成功: %1").arg(filePath));
        QMessageBox::information(this, u8"成功",
            QString(u8"通道配置导出JSON成功！\n\n文件保存在:\n%1\n\n包含 %2 个控制通道组")
            .arg(filePath)
            .arg(allGroups.size()));
    } else {
        QString error = jsonDataExporter_->getLastError();
        AddLogEntry("ERROR", QString(u8"导出通道配置JSON失败: %1").arg(error));
        QMessageBox::critical(this, u8"错误", QString(u8"导出失败：%1").arg(error));
    }
}

bool CMyMainWindow::showImportConfirmationDialog(const QString& fileName) {
    QString message = QString(u8"确认要从以下Excel文件导入硬件配置吗？\n\n文件: %1\n\n"
                             u8"⚠️ 警告: 导入将清空当前的硬件树配置！\n"
                             u8"建议在导入前先保存当前配置。")
                      .arg(QFileInfo(fileName).fileName());

    int ret = QMessageBox::question(
        this,
        u8"确认导入",
        message,
        QMessageBox::Yes | QMessageBox::No,
        QMessageBox::No
    );

    return ret == QMessageBox::Yes;
}

void CMyMainWindow::handleExportResult(bool success, const QString& filePath,
                                      const QString& operation, const QString& errorMessage) {
    if (success) {
        AddLogEntry("INFO", QString(u8"%1成功: %2").arg(operation).arg(filePath));
        QMessageBox::information(this, u8"导出成功",
            QString(u8"%1成功！\n\n文件已保存到:\n%2").arg(operation).arg(filePath));
    } else {
        QString error = errorMessage.isEmpty() ? u8"未知错误" : errorMessage;
        AddLogEntry("ERROR", QString(u8"%1失败: %2 - %3").arg(operation).arg(filePath).arg(error));
        QMessageBox::critical(this, u8"导出失败",
            QString(u8"%1失败！\n\n错误信息:\n%2").arg(operation).arg(error));
    }
}

void CMyMainWindow::UpdateTreeDisplay() {
    AddLogEntry("INFO", QString(u8"开始更新树控件显示..."));

    // 🆕 修复：重新填充硬件树数据
    RefreshHardwareTreeFromDataManagers();

    // 🆕 修复：重新填充实验配置树数据
    RefreshTestConfigTreeFromDataManagers();

    // 刷新硬件树显示
    if (ui->hardwareTreeWidget) {
        ui->hardwareTreeWidget->update();
        ui->hardwareTreeWidget->expandAll();
    }

    // 刷新测试配置树显示
    if (ui->testConfigTreeWidget) {
        ui->testConfigTreeWidget->update();
        ui->testConfigTreeWidget->expandAll();
    }

    // 强制恢复树控件颜色
    ForceRestoreAllTreeColors();

    AddLogEntry("INFO", QString(u8"界面显示已更新"));
}

// 🆕 新增：从数据管理器刷新硬件树显示
void CMyMainWindow::RefreshHardwareTreeFromDataManagers() {
    if (!ui->hardwareTreeWidget) {
        AddLogEntry("ERROR", QString(u8"硬件树控件为空"));
        return;
    }

    AddLogEntry("INFO", QString(u8"开始从数据管理器刷新硬件树..."));

    // 🆕 修复：保存当前展开状态，避免先收缩再展开的视觉闪烁
    QMap<QString, bool> expandedStates = saveTreeExpandedStates(ui->hardwareTreeWidget);

    // 重新初始化硬件树结构
    InitializeHardwareTree();
    
    // 🆕 修复：立即恢复展开状态，而不是等待延迟展开
    restoreTreeExpandedStates(ui->hardwareTreeWidget, expandedStates);

    // 获取任务1根节点
    QTreeWidgetItem* taskRoot = ui->hardwareTreeWidget->topLevelItem(0);
    if (!taskRoot) {
        AddLogEntry("ERROR", QString(u8"无法获取硬件树根节点"));
        return;
    }

    // 获取各个子节点
    QTreeWidgetItem* actuatorRoot = taskRoot->child(0);  // 作动器
    QTreeWidgetItem* sensorRoot = taskRoot->child(1);    // 传感器
    QTreeWidgetItem* hardwareRoot = taskRoot->child(2);  // 硬件节点资源

    // 1. 填充作动器数据
    if (actuatorRoot && actuatorViewModel1_2_) {
        auto actuatorGroups = actuatorViewModel1_2_->getDataManager()->getAllActuatorGroups();
        AddLogEntry("INFO", QString(u8"🔍 开始填充作动器数据：共%1个组").arg(actuatorGroups.size()));

        for (const auto& group : actuatorGroups) {
            AddLogEntry("INFO", QString(u8"📊 处理作动器组：ID=%1, 名称='%2', 作动器数量=%3")
                       .arg(group.groupId).arg(group.groupName).arg(group.actuators.size()));

            // 🔍 新增：详细调试组名称和作动器信息
            AddLogEntry("DEBUG", QString(u8"🔍 组详细信息：组名='%1', 组类型='%2', 创建时间='%3'")
                       .arg(group.groupName).arg(group.groupType).arg(group.createTime));

            if (group.groupName.isEmpty()) {
                AddLogEntry("WARNING", QString(u8"⚠️ 发现空组名称，组ID=%1，跳过显示").arg(group.groupId));
                continue;
            }

            QTreeWidgetItem* groupItem = new QTreeWidgetItem(actuatorRoot);
            groupItem->setText(0, group.groupName);  // 🆕 修复：直接使用QString，不需要转换
            groupItem->setData(0, Qt::UserRole, "作动器组");
            // 🆕 修改：为作动器组添加详细提示信息
            groupItem->setToolTip(0, GenerateGroupDetailedInfo(group.groupName));

            // 添加组内的作动器
            int actuatorCount = 0;
            for (const auto& actuator : group.actuators) {
                QTreeWidgetItem* actuatorItem = new QTreeWidgetItem(groupItem);
                actuatorItem->setText(0, actuator.params.sn);  // 🆕 修复：直接使用QString，不需要转换
                actuatorItem->setData(0, Qt::UserRole, "作动器设备");  // 🔧 修复：正确设置节点类型为"作动器设备"
                // 🆕 修改：为作动器设备添加详细提示信息
                actuatorItem->setToolTip(0, actuatorViewModel1_2_->generateActuatorDetailedInfoBusiness(actuator.params.sn));
                actuatorCount++;

                AddLogEntry("INFO", QString(u8"  ✅ 添加作动器到界面：序列号='%1', 类型='%2'")
                           .arg(actuator.params.sn).arg(ActuatorDataManager_1_2::actuatorTypeToString(actuator.type)));
            }

            AddLogEntry("INFO", QString(u8"📈 组'%1'界面显示完成：%2个作动器已添加到树控件")
                       .arg(group.groupName).arg(actuatorCount));
        }
        AddLogEntry("INFO", QString(u8"✅ 作动器数据填充完成: %1个组").arg(actuatorGroups.size()));
    }

    // 2. 填充传感器数据
    if (sensorRoot && sensorDataManager_) {
        auto sensorGroups = sensorDataManager_->getAllSensorGroups();
        AddLogEntry("INFO", QString(u8"🔍 开始填充传感器数据：共%1个组").arg(sensorGroups.size()));

        for (const auto& group : sensorGroups) {
            AddLogEntry("INFO", QString(u8"📊 处理传感器组：ID=%1, 名称='%2', 传感器数量=%3")
                       .arg(group.groupId).arg(group.groupName).arg(group.sensors.size()));

            // 🔍 新增：详细调试组名称和传感器信息
            AddLogEntry("DEBUG", QString(u8"🔍 组详细信息：组名='%1', 组类型='%2', 创建时间='%3'")
                       .arg(group.groupName).arg(group.groupType).arg(group.createTime));

            if (group.groupName.isEmpty()) {
                AddLogEntry("WARNING", QString(u8"⚠️ 发现空组名称，组ID=%1，跳过显示").arg(group.groupId));
                continue;
            }

            QTreeWidgetItem* groupItem = new QTreeWidgetItem(sensorRoot);
            groupItem->setText(0, group.groupName);
            groupItem->setData(0, Qt::UserRole, "传感器组");
            // 🆕 修改：为传感器组添加详细提示信息
            groupItem->setToolTip(0, GenerateGroupDetailedInfo(group.groupName));

            // 添加组内的传感器
            int sensorCount = 0;
            for (const auto& sensor : group.sensors) {
                QTreeWidgetItem* sensorItem = new QTreeWidgetItem(groupItem);
                sensorItem->setText(0, sensor.params_sn);  // 使用正确的字段名
                sensorItem->setData(0, Qt::UserRole, "传感器设备");
                // 🆕 修改：为传感器设备添加详细提示信息
                sensorItem->setToolTip(0, GenerateSensorDeviceDetailedInfo(sensor.params_sn));
                sensorCount++;

                AddLogEntry("INFO", QString(u8"  ✅ 添加传感器到界面：序列号='%1', 类型='%2'")
                           .arg(sensor.params_sn).arg(sensor.sensorType));
            }

            AddLogEntry("INFO", QString(u8"📈 组'%1'界面显示完成：%2个传感器已添加到树控件")
                       .arg(group.groupName).arg(sensorCount));
        }
        AddLogEntry("INFO", QString(u8"✅ 传感器数据填充完成: %1个组").arg(sensorGroups.size()));
    }

    // 3. 填充硬件节点数据
    if (hardwareRoot && hardwareNodeResDataManager_) {
        auto nodeConfigs = hardwareNodeResDataManager_->getAllHardwareNodeConfigs();
        AddLogEntry("INFO", QString(u8"🔍 开始填充硬件节点数据：共%1个节点").arg(nodeConfigs.size()));

        for (const auto& nodeConfig : nodeConfigs) {
            AddLogEntry("INFO", QString(u8"📊 处理硬件节点：名称='%1', 通道数量=%2")
                       .arg(nodeConfig.nodeName).arg(nodeConfig.channelCount));

            if (nodeConfig.nodeName.isEmpty()) {
                AddLogEntry("WARNING", QString(u8"⚠️ 发现空节点名称，跳过显示"));
                continue;
            }

            QTreeWidgetItem* nodeItem = new QTreeWidgetItem(hardwareRoot);
            nodeItem->setText(0, nodeConfig.nodeName);
            nodeItem->setData(0, Qt::UserRole, "硬件节点");
            // 🆕 修改：为硬件节点添加详细提示信息
            nodeItem->setToolTip(0, GenerateHardwareNodeDetailedInfo(nodeConfig.nodeName));

            // 添加节点内的通道
            int channelCount = 0;
            for (const auto& channel : nodeConfig.channels) {
                QTreeWidgetItem* channelItem = new QTreeWidgetItem(nodeItem);
                channelItem->setText(0, QString("CH%1").arg(channel.channelId));
                channelItem->setData(0, Qt::UserRole, "硬件节点通道");
                // 🆕 修改：为硬件通道添加详细提示信息
                channelItem->setToolTip(0, QString(u8"通道ID: %1\nIP地址: %2\n端口: %3")
                                       .arg(channel.channelId).arg(channel.ipAddress).arg(channel.port));
                channelCount++;

                AddLogEntry("INFO", QString(u8"  ✅ 添加通道到界面：ID=%1, IP='%2', 端口=%3")
                           .arg(channel.channelId).arg(channel.ipAddress).arg(channel.port));
            }

            AddLogEntry("INFO", QString(u8"📈 节点'%1'界面显示完成：%2个通道已添加到树控件")
                       .arg(nodeConfig.nodeName).arg(channelCount));
        }
        AddLogEntry("INFO", QString(u8"✅ 硬件节点数据填充完成: %1个节点").arg(nodeConfigs.size()));
    }
    
    // 🆕 新增：完全展开硬件配置树
    ui->hardwareTreeWidget->expandAll();
    AddLogEntry("INFO", QString(u8"硬件树数据填充完成并已完全展开"));
}



// ============================================================================
// 🆕 新增：缺失方法的实现
// ============================================================================

/**
 * @brief 初始化项目状态
 */
void CMyMainWindow::initializeProjectState() {
    AddLogEntry("INFO", QString(u8"🔧 正在初始化项目状态..."));
    
    // 初始化各种状态变量
    currentProjectPath_.clear();
    currentProjectName_.clear();
    
    // 清理所有数据管理器
    if (actuatorViewModel1_2_) {
        actuatorViewModel1_2_->clearMemoryData();
    }
    if (sensorDataManager_) {
        sensorDataManager_->clearAll();
    }
    if (ctrlChanDataManager_) {
        ctrlChanDataManager_->clearAllData();
    }
    if (hardwareNodeResDataManager_) {
        hardwareNodeResDataManager_->clearAllData();
    }
    
    // 重置界面状态
    if (ui->hardwareTreeWidget) {
        ui->hardwareTreeWidget->clear();
    }
    if (ui->testConfigTreeWidget) {
        ui->testConfigTreeWidget->clear();
    }
    
    AddLogEntry("INFO", QString(u8"✅ 项目状态初始化完成"));
}

/**
 * @brief 安全获取作动器视图模型，如果为空则自动重新创建
 */
ActuatorViewModel1_2* CMyMainWindow::getSafeActuatorViewModel() {
    if (!actuatorViewModel1_2_) {
        AddLogEntry("WARNING", "⚠️ 作动器视图模型为空，正在重新创建...");
        actuatorViewModel1_2_ = std::make_unique<ActuatorViewModel1_2>();
        if (actuatorViewModel1_2_) {
            // 重新连接信号
            connectActuatorViewModelSignals();
            AddLogEntry("INFO", "✅ 作动器视图模型重新创建成功");
        } else {
            AddLogEntry("ERROR", "❌ 作动器视图模型重新创建失败");
            return nullptr;
        }
    }
    return actuatorViewModel1_2_.get();
}

/**
 * @brief 连接作动器视图模型信号
 */
void CMyMainWindow::connectActuatorViewModelSignals() {
    if (!actuatorViewModel1_2_) {
        AddLogEntry("WARNING", QString(u8"⚠️ 作动器视图模型未初始化，无法连接信号"));
        return;
    }
    
    AddLogEntry("INFO", QString(u8"🔗 正在连接作动器视图模型信号..."));
    
    // 连接业务事件信号
    connect(actuatorViewModel1_2_.get(), &ActuatorViewModel1_2::actuatorGroupCreatedBusiness,
            this, &CMyMainWindow::onActuatorGroupCreatedBusiness);
    
    connect(actuatorViewModel1_2_.get(), &ActuatorViewModel1_2::actuatorDeviceCreatedBusiness,
            this, &CMyMainWindow::onActuatorDeviceCreatedBusiness);
    
    connect(actuatorViewModel1_2_.get(), &ActuatorViewModel1_2::actuatorDeviceEditedBusiness,
            this, &CMyMainWindow::onActuatorDeviceEditedBusiness);
    
    connect(actuatorViewModel1_2_.get(), &ActuatorViewModel1_2::actuatorDeviceDeletedBusiness,
            this, &CMyMainWindow::onActuatorDeviceDeletedBusiness);
    
    connect(actuatorViewModel1_2_.get(), &ActuatorViewModel1_2::businessValidationError,
            this, [this](const QString& error) {
        AddLogEntry("ERROR", QString(u8"❌ 作动器业务验证错误：%1").arg(error));
        QMessageBox::warning(this, tr("验证错误"), error);
    });
    
    // 连接数据变更信号
    connect(actuatorViewModel1_2_.get(), &ActuatorViewModel1_2::actuatorDataChanged,
            this, [this](const QString& serialNumber, const QString& operation) {
        AddLogEntry("DEBUG", QString(u8"📡 作动器数据变更：序列号=%1, 操作=%2")
                   .arg(serialNumber).arg(operation));
        // 可以在这里添加界面更新逻辑
    });
    
    // 连接错误信号
    connect(actuatorViewModel1_2_.get(), &ActuatorViewModel1_2::errorOccurred,
            this, [this](const QString& error) {
        AddLogEntry("ERROR", QString(u8"❌ 作动器视图模型错误：%1").arg(error));
    });
    
    // 连接操作完成信号
    connect(actuatorViewModel1_2_.get(), &ActuatorViewModel1_2::operationCompleted,
            this, [this](const QString& operation, bool success) {
        QString status = success ? u8"成功" : u8"失败";
        AddLogEntry("INFO", QString(u8"🎯 作动器操作完成：%1 - %2").arg(operation).arg(status));
    });
    
    AddLogEntry("INFO", QString(u8"✅ 作动器视图模型信号连接完成"));
}

/**
 * @brief 生成控制通道详细信息
 */
QString CMyMainWindow::GenerateControlChannelDetailedInfo(const QString& channelName) {
    QString info;
    info += QString(u8"🎛️ 控制通道详细信息\n");
    info += QString(u8"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n");
    
    if (ctrlChanDataManager_) {
        // 🔧 修复：支持通过channelId或channelName查找
        auto groups = ctrlChanDataManager_->getAllControlChannelGroups();
        for (const auto& group : groups) {
            for (const auto& channel : group.channels) {
                QString storedChannelId = QString::fromStdString(channel.channelId);
                QString storedChannelName = QString::fromStdString(channel.channelName);
                
                // 支持通过channelId或channelName匹配
                if (storedChannelId == channelName || storedChannelName == channelName) {
                    // 📋 基本信息部分
                    info += QString(u8"📋 基本信息\n");
                    info += QString(u8"通道名称: %1\n").arg(storedChannelName);
                    info += QString(u8"通道ID: %1\n").arg(storedChannelId);
                    info += QString(u8"下位机ID: %1\n").arg(channel.lc_id);
                    info += QString(u8"站点ID: %1\n").arg(channel.station_id);
                    info += QString(u8"使能状态: %1\n").arg(channel.enable ? u8"✅ 启用" : u8"❌ 禁用");
                    info += QString(u8"控制模式: %1\n").arg(getControlModeDisplayText(channel.control_mode));
                    info += QString(u8"所属组: %1\n").arg(QString::fromStdString(group.groupName));
                    
                    info += QString(u8"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n");
                    info += QString(u8"🔗 设备关联\n");
                    info += QString(u8"硬件关联: %1\n").arg(QString::fromStdString(channel.hardwareAssociation).isEmpty() ? u8"未配置" : QString::fromStdString(channel.hardwareAssociation));
                    info += QString(u8"载荷传感器1: %1\n").arg(QString::fromStdString(channel.load1Sensor).isEmpty() ? u8"未配置" : QString::fromStdString(channel.load1Sensor));
                    info += QString(u8"载荷传感器2: %1\n").arg(QString::fromStdString(channel.load2Sensor).isEmpty() ? u8"未配置" : QString::fromStdString(channel.load2Sensor));
                    info += QString(u8"位置传感器: %1\n").arg(QString::fromStdString(channel.positionSensor).isEmpty() ? u8"未配置" : QString::fromStdString(channel.positionSensor));
                    info += QString(u8"控制作动器: %1\n").arg(QString::fromStdString(channel.controlActuator).isEmpty() ? u8"未配置" : QString::fromStdString(channel.controlActuator));
                    
                    info += QString(u8"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n");
                    info += QString(u8"⚡ 极性配置\n");
                    info += QString(u8"控制作动器极性: %1\n").arg(getPolarityDisplayText(channel.servo_control_polarity));
                    info += QString(u8"载荷传感器1极性: %1\n").arg(getPolarityDisplayText(channel.payload_sensor1_polarity));
                    info += QString(u8"载荷传感器2极性: %1\n").arg(getPolarityDisplayText(channel.payload_sensor2_polarity));
                    info += QString(u8"位置传感器极性: %1\n").arg(getPolarityDisplayText(channel.position_sensor_polarity));
                    
                    if (!channel.notes.empty()) {
                        info += QString(u8"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n");
                        info += QString(u8"📝 备注\n");
                        info += QString(u8"备注信息: %1\n").arg(QString::fromStdString(channel.notes));
                    }
                    
                    info += QString(u8"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
                    return info;
                }
            }
        }
    }
    
    // 未找到通道数据
    info += QString(u8"通道名称: %1\n").arg(channelName);
    info += QString(u8"状态: ⚠️ 未配置\n");
    info += QString(u8"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    return info;
}

// 🆕 新增：极性值转换为显示文本
QString CMyMainWindow::getPolarityDisplayText(int polarity) const {
    switch (polarity) {
        case 1:  return u8"正极性 (+)";
        case -1: return u8"负极性 (-)";
        case 9:  return u8"双极性 (±)";
        case 0:  return u8"无极性";
        default: return QString(u8"未知极性 (%1)").arg(polarity);
    }
}

// 🆕 新增：控制模式转换为显示文本
QString CMyMainWindow::getControlModeDisplayText(int controlMode) const {
    switch (controlMode) {
        case 1: return u8"🎯 Force (力控制)";
        case 2: return u8"📏 Position (位移控制)";
        case 3: return u8"🚀 Velocity (速度控制)";
        case 4: return u8"🔄 Hybrid (混合控制)";
        default: return QString(u8"❓ 未知模式 (%1)").arg(controlMode);
    }
}

// 🆕 新增：极性值转换为简短显示文本（用于树形控件列显示）
QString CMyMainWindow::getPolarityShortText(int polarity) const {
    switch (polarity) {
        case 1:  return u8"正极性 (+)";     // 正极性
        case -1: return u8"负极性 (-)";    // 负极性
        case 9:  return u8"双极性 (±)";    // 双极性
        case 0:  return u8"无极性";        // 未知
        default: return QString(u8"未知极性 (%1)").arg(polarity);
    }
}

/**
 * @brief 生成载荷传感器详细信息
 */
QString CMyMainWindow::GenerateLoadSensorDetailedInfo(const QString& sensorName, const QString& associationInfo) {
    QString info;
    info += QString(u8"🔧 载荷传感器详细信息\n");
    info += QString(u8"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n");
    info += QString(u8"传感器名称: %1\n").arg(sensorName);
    
    if (!associationInfo.isEmpty()) {
        info += QString(u8"关联信息: %1\n").arg(associationInfo);
    }
    
    if (sensorDataManager_) {
        // 查找传感器详细信息
        auto groups = sensorDataManager_->getAllSensorGroups();
        for (const auto& group : groups) {
            for (const auto& sensor : group.sensors) {
                if (sensor.params_sn == sensorName) {
                    info += QString(u8"传感器ID: %1\n").arg(sensor.sensorId);
                    info += QString(u8"型号: %1\n").arg(sensor.params_model);
                    info += QString(u8"序列号: %1\n").arg(sensor.params_sn);
                    info += QString(u8"量程: %1 到 %2\n").arg(sensor.meas_range_min).arg(sensor.meas_range_max);
                    info += QString(u8"极性: %1\n").arg(polarityToString(sensor.params_polarity));
                    info += QString(u8"精度: %1\n").arg(sensor.params_precision);
                    return info;
                }
            }
        }
    }
    
    info += QString(u8"状态: 未配置\n");
    info += QString(u8"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    return info;
}

/**
 * @brief 生成位置传感器详细信息
 */
QString CMyMainWindow::GeneratePositionSensorDetailedInfo(const QString& associationInfo) {
    QString info;
    info += QString(u8"📏 位置传感器详细信息\n");
    info += QString(u8"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n");
    
    if (!associationInfo.isEmpty()) {
        info += QString(u8"关联信息: %1\n").arg(associationInfo);
        
        // 解析关联信息
        QStringList parts = associationInfo.split(" - ");
        if (parts.size() >= 2) {
            QString groupName = parts[0];
            QString deviceName = parts[1];
            
            info += QString(u8"关联组: %1\n").arg(groupName);
            info += QString(u8"关联设备: %1\n").arg(deviceName);
            
            // 在传感器数据中查找
            if (sensorDataManager_) {
                auto groups = sensorDataManager_->getAllSensorGroups();
                for (const auto& group : groups) {
                    if (group.groupName == groupName) {
                        for (const auto& sensor : group.sensors) {
                            if (sensor.params_sn == deviceName) {
                                info += QString(u8"型号: %1\n").arg(sensor.params_model);
                                info += QString(u8"量程: %1 到 %2\n").arg(sensor.meas_range_min).arg(sensor.meas_range_max);
                                info += QString(u8"精度: %1\n").arg(sensor.params_precision);
                                info += QString(u8"极性: %1\n").arg(polarityToString(sensor.params_polarity));
                                return info;
                            }
                        }
                    }
                }
            }
        }
    }
    
    info += QString(u8"状态: 未关联\n");
    info += QString(u8"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    return info;
}

/**
 * @brief 生成控制作动器详细信息
 */
QString CMyMainWindow::GenerateControlActuatorDetailedInfo(const QString& associationInfo) {
    QString info;
    info += QString(u8"⚙️ 控制作动器详细信息\n");
    info += QString(u8"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n");
    
    if (!associationInfo.isEmpty()) {
        info += QString(u8"关联信息: %1\n").arg(associationInfo);
        
        // 解析关联信息
        QStringList parts = associationInfo.split(" - ");
        if (parts.size() >= 2) {
            QString groupName = parts[0];
            QString deviceName = parts[1];
            
            info += QString(u8"关联组: %1\n").arg(groupName);
            info += QString(u8"关联设备: %1\n").arg(deviceName);
            
            // 在作动器数据中查找
            if (actuatorViewModel1_2_) {
                auto groups = actuatorViewModel1_2_->getAllActuatorGroups();
                for (const auto& group : groups) {
                    if (group.groupName == groupName) {
                        for (const auto& actuator : group.actuators) {
                            if (actuator.params.sn == deviceName) {
                                info += QString(u8"作动器ID: %1\n").arg(actuator.actuatorId);
                                info += QString(u8"型号: %1\n").arg(actuator.params.model);
                                info += QString(u8"序列号: %1\n").arg(actuator.params.sn);
                                info += QString(u8"K系数: %1\n").arg(actuator.params.k);
                                info += QString(u8"B系数: %1\n").arg(actuator.params.b);
                                info += QString(u8"精度: %1\n").arg(actuator.params.precision);
                                info += QString(u8"零偏: %1\n").arg(actuator.zero_offset);
                                info += QString(u8"下位机ID: %1\n").arg(actuator.lc_id);
                                info += QString(u8"测量范围: %1 到 %2\n").arg(actuator.params.meas_range_min).arg(actuator.params.meas_range_max);
                                return info;
                            }
                        }
                    }
                }
            }
        }
    }
    
    info += QString(u8"状态: 未关联\n");
    info += QString(u8"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    return info;
}

/**
 * @brief 项目打开完成处理
 */
void CMyMainWindow::OnProjectOpened(const QString& projectPath, const QString& projectName) {
    AddLogEntry("INFO", QString(u8"📂 项目打开完成处理：%1 - %2").arg(projectName).arg(projectPath));
    
    // 保存当前项目信息
    currentProjectPath_ = projectPath;
    currentProjectName_ = projectName;
    
    // 🆕 修复：确保数据管理器完全同步
    if (!ensureDataManagerSync()) {
        AddLogEntry("ERROR", "数据管理器同步失败，详细信息功能可能异常");
        QMessageBox::warning(this, tr("警告"), 
            tr("工程数据加载完成，但数据同步存在问题。\n详细信息功能可能无法正常工作。"));
    }
    
    // 🆕 修复：确保作动器视图模型有效
    if (!actuatorViewModel1_2_) {
        AddLogEntry("WARNING", "⚠️ 作动器视图模型为空，正在重新创建...");
        actuatorViewModel1_2_ = std::make_unique<ActuatorViewModel1_2>();
        if (actuatorViewModel1_2_) {
            // 重新连接信号
            connectActuatorViewModelSignals();
            AddLogEntry("INFO", "✅ 作动器视图模型重新创建成功");
        } else {
            AddLogEntry("ERROR", "❌ 作动器视图模型重新创建失败");
        }
    }
    
    // 🆕 修复：重新初始化详细信息面板
    reinitializeDetailInfoPanel();
    
    // 🆕 修改：打开工程后不操作详细信息面板状态，保持用户之前设置
    AddLogEntry("DEBUG", "✅ 打开工程后，详细信息面板状态保持用户设置");
    
    // 🆕 新增：更新项目状态（启用操作区域）
    updateOperationAreaState(true);
    
    // 更新窗口标题
    setWindowTitle(QString(u8"SiteResConfig - %1").arg(projectName));
    
    // 🆕 修复：确保界面数据完全刷新
    refreshAllDataFromManagers();
    
    // 🆕 新增：验证工程状态完整性
    validateProjectState();
    
    // 🆕 修改：工程加载完成后不操作详细信息面板状态，保持用户之前设置
    AddLogEntry("DEBUG", "✅ 工程加载完成，详细信息面板状态保持用户设置");
    
    AddLogEntry("INFO", QString(u8"✅ 项目打开处理完成"));
    
    // 🆕 新增：调试详细信息面板状态
    debugDetailInfoPanelStatus();
}

/**
 * @brief 验证导入的数据
 */
bool CMyMainWindow::ValidateImportedData() {
    AddLogEntry("INFO", QString(u8"🔍 开始验证导入数据..."));
    
    bool isValid = true;
    int totalItems = 0;
    
    // 验证作动器数据
    if (actuatorViewModel1_2_) {
        auto actuatorGroups = actuatorViewModel1_2_->getAllActuatorGroups();
        int actuatorCount = 0;
        for (const auto& group : actuatorGroups) {
            actuatorCount += group.actuators.size();
        }
        totalItems += actuatorCount;
        AddLogEntry("INFO", QString(u8"📊 作动器验证：%1个组，%2个作动器")
                   .arg(actuatorGroups.size()).arg(actuatorCount));
    }
    
    // 验证传感器数据
    if (sensorDataManager_) {
        auto sensorGroups = sensorDataManager_->getAllSensorGroups();
        int sensorCount = 0;
        for (const auto& group : sensorGroups) {
            sensorCount += group.sensors.size();
        }
        totalItems += sensorCount;
        AddLogEntry("INFO", QString(u8"📊 传感器验证：%1个组，%2个传感器")
                   .arg(sensorGroups.size()).arg(sensorCount));
    }
    
    // 验证硬件节点数据
    if (hardwareNodeResDataManager_) {
        auto nodeConfigs = hardwareNodeResDataManager_->getAllHardwareNodeConfigs();
        totalItems += nodeConfigs.size();
        AddLogEntry("INFO", QString(u8"📊 硬件节点验证：%1个节点")
                   .arg(nodeConfigs.size()));
    }
    
    // 验证控制通道数据
    if (ctrlChanDataManager_) {
        auto channelGroups = ctrlChanDataManager_->getAllControlChannelGroups();
        int channelCount = 0;
        for (const auto& group : channelGroups) {
            channelCount += group.channels.size();
        }
        totalItems += channelCount;
        AddLogEntry("INFO", QString(u8"📊 控制通道验证：%1个组，%2个通道")
                   .arg(channelGroups.size()).arg(channelCount));
    }
    
    if (totalItems == 0) {
        AddLogEntry("WARNING", QString(u8"⚠️ 警告：没有导入任何数据项"));
        isValid = false;
    } else {
        AddLogEntry("INFO", QString(u8"✅ 数据验证完成：共%1个数据项").arg(totalItems));
    }
    
    return isValid;
}

/**
 * @brief 从数据管理器刷新所有界面数据
 */
void CMyMainWindow::refreshAllDataFromManagers() {
    AddLogEntry("INFO", QString(u8"🔄 开始从数据管理器刷新界面数据..."));
    
    // 🛠️ 修复：刷新硬件树以反映数据变化（如删除作动器等操作）
    // 注意：只有在明确需要刷新硬件树时才调用，避免不必要的界面重构
    
    // 刷新试验配置树（如果有数据）
    if (ctrlChanDataManager_) {
        auto channelGroups = ctrlChanDataManager_->getAllControlChannelGroups();
        if (!channelGroups.empty()) {
            // 🆕 修复：使用正确的方法刷新试验配置树（不创建默认数据）
            RefreshTestConfigTreeFromDataManagers();
        }
    }
    
    // 更新所有树形控件的提示信息
    UpdateAllTreeWidgetTooltips();
    
    // 🆕 修改：完全展开所有树形控件节点（打开工程时）
    if (ui->hardwareTreeWidget) {
        ui->hardwareTreeWidget->expandAll();
        AddLogEntry("INFO", QString(u8"✅ 硬件配置树已完全展开"));
    }
    
    if (ui->testConfigTreeWidget) {
        ui->testConfigTreeWidget->expandAll();
        AddLogEntry("INFO", QString(u8"✅ 实验配置树已完全展开"));
    }
    
    AddLogEntry("INFO", QString(u8"✅ 界面数据刷新完成"));
}

// ============================================================================
// 🆕 新增：更多缺失方法的实现
// ============================================================================

/**
 * @brief 项目关闭处理
 */
void CMyMainWindow::OnProjectClosed() {
    AddLogEntry("INFO", QString(u8"📂 处理项目关闭..."));
    
    // 🆕 新增：更新项目状态（禁用操作区域）
    updateOperationAreaState(false);
    
    // 清理当前项目信息
    currentProjectPath_.clear();
    currentProjectName_.clear();
    
    // 清理所有数据管理器
    initializeProjectState();
    
    // 重置窗口标题
    setWindowTitle(QString(u8"SiteResConfig"));
    
    // 清理界面显示
    if (ui->hardwareTreeWidget) {
        ui->hardwareTreeWidget->clear();
        InitializeHardwareTree();
    }
    if (ui->testConfigTreeWidget) {
        ui->testConfigTreeWidget->clear();
        InitializeTestConfigTree();
    }
    
    AddLogEntry("INFO", QString(u8"✅ 项目关闭处理完成"));
}

/**
 * @brief 保存拖拽关联到数据管理器
 */
void CMyMainWindow::SaveDragDropAssociationToDataManager(QTreeWidgetItem* targetItem, const QString& associationInfo, const QString& sourceType) {
    if (!targetItem) {
        AddLogEntry("ERROR", QString(u8"❌ 保存拖拽关联失败：目标节点为空"));
        return;
    }
    
    QString nodeName = targetItem->text(0);
    AddLogEntry("INFO", QString(u8"💾 保存拖拽关联：节点='%1', 关联='%2', 类型='%3'")
               .arg(nodeName).arg(associationInfo).arg(sourceType));
    
    // 根据源类型和目标节点类型保存关联
    QString nodeType = targetItem->data(0, Qt::UserRole).toString();
    
    // 🆕 修复：对于子节点（载荷1、载荷2、位置、控制），需要找到父节点（CH1或CH2）
    QString channelToMatch = nodeName;
    if (nodeName == "载荷1" || nodeName == "载荷2" || nodeName == "位置" || nodeName == "控制") {
        QTreeWidgetItem* parentItem = targetItem->parent();
        if (parentItem) {
            channelToMatch = parentItem->text(0); // 获取父节点名称，应该是CH1或CH2
            AddLogEntry("DEBUG", QString(u8"🔍 子节点'%1'的父通道：'%2'").arg(nodeName).arg(channelToMatch));
        }
    }
    
    if (ctrlChanDataManager_) {
        auto groups = ctrlChanDataManager_->getAllControlChannelGroups();
        for (auto& group : groups) {
            for (auto& channel : group.channels) {
                QString channelName = QString::fromStdString(channel.channelName);
                if (channelName == channelToMatch) {
                    // 根据sourceType和具体目标节点更新相应的关联字段
                    if (sourceType == "作动器设备") {
                        channel.controlActuator = associationInfo.toStdString();
                    } else if (sourceType == "传感器设备") {
                        // 🆕 修复：根据具体目标节点决定保存到哪个传感器字段
                        if (nodeName == "载荷1") {
                            channel.load1Sensor = associationInfo.toStdString();
                        } else if (nodeName == "载荷2") {
                            channel.load2Sensor = associationInfo.toStdString();
                        } else if (nodeName == "位置") {
                            channel.positionSensor = associationInfo.toStdString();
                        } else {
                            // 兼容旧逻辑：如果是其他情况，按空闲字段顺序保存
                            if (channel.load1Sensor.empty()) {
                                channel.load1Sensor = associationInfo.toStdString();
                            } else if (channel.load2Sensor.empty()) {
                                channel.load2Sensor = associationInfo.toStdString();
                            } else {
                                channel.positionSensor = associationInfo.toStdString();
                            }
                        }
                    } else if (sourceType == "硬件节点通道") {
                        // 硬件节点通道关联到控制通道的CH1/CH2时，保存到hardwareAssociation字段
                        channel.hardwareAssociation = associationInfo.toStdString();
                    }
                    
                    // 更新数据管理器
                    ctrlChanDataManager_->updateControlChannelGroup(group);
                    AddLogEntry("INFO", QString(u8"✅ 关联已保存到数据管理器"));
                    return;
                }
            }
        }
    }
    
    AddLogEntry("WARNING", QString(u8"⚠️ 未找到对应的数据对象进行关联保存"));
}

/**
 * @brief 拖拽后更新节点提示信息
 */
void CMyMainWindow::UpdateNodeTooltipAfterDragDrop(QTreeWidgetItem* targetItem, const QString& associationInfo, const QString& sourceType) {
    if (!targetItem) {
        return;
    }
    
    QString nodeName = targetItem->text(0);
    AddLogEntry("DEBUG", QString(u8"🔄 更新拖拽后节点提示：节点='%1', 关联='%2', 类型='%3'")
               .arg(nodeName).arg(associationInfo).arg(sourceType));
    
    // 生成新的tooltip
    QString nodeType = targetItem->data(0, Qt::UserRole).toString();
    QString newTooltip;
    
    if (nodeType == "控制通道") {
        newTooltip = GenerateControlChannelDetailedInfo(nodeName);
    } else if (nodeType.contains("传感器")) {
        newTooltip = GenerateLoadSensorDetailedInfo(nodeName, associationInfo);
    } else if (nodeType.contains("作动器")) {
        newTooltip = GenerateControlActuatorDetailedInfo(associationInfo);
    } else {
        newTooltip = QString(u8"📋 %1\n关联: %2\n类型: %3").arg(nodeName).arg(associationInfo).arg(sourceType);
    }
    
    targetItem->setToolTip(0, newTooltip);
    AddLogEntry("DEBUG", QString(u8"✅ 节点提示信息已更新"));
}

/**
 * @brief 编辑作动器设备
 */
void CMyMainWindow::OnEditActuatorDevice(QTreeWidgetItem* item) {
    if (!deviceManager_) {
        AddLogEntry("ERROR", "DeviceManager未初始化，无法编辑作动器设备");
        return;
    }
    
    // 委托给DeviceManager处理
    deviceManager_->editActuatorDevice(item);
}

/**
 * @brief 删除作动器设备
 */
void CMyMainWindow::OnDeleteActuatorDevice(QTreeWidgetItem* item) {
    if (!deviceManager_) {
        AddLogEntry("ERROR", "DeviceManager未初始化，无法删除作动器设备");
        return;
    }
    
    // 委托给DeviceManager处理
    deviceManager_->deleteActuatorDevice(item);
}

/**
 * @brief 编辑传感器设备
 */
void CMyMainWindow::OnEditSensorDevice(QTreeWidgetItem* item) {
    if (!deviceManager_) {
        AddLogEntry("ERROR", "DeviceManager未初始化，无法编辑传感器设备");
        return;
    }
    
    // 委托给DeviceManager处理
    deviceManager_->editSensorDevice(item);
}

/**
 * @brief 删除传感器设备
 */
void CMyMainWindow::OnDeleteSensorDevice(QTreeWidgetItem* item) {
    if (!deviceManager_) {
        AddLogEntry("ERROR", "DeviceManager未初始化，无法删除传感器设备");
        return;
    }
    
    // 委托给DeviceManager处理
    deviceManager_->deleteSensorDevice(item);
}

/**
 * @brief 清除关联
 */
void CMyMainWindow::OnClearAssociation(QTreeWidgetItem* item) {
    if (!item) {
        AddLogEntry("ERROR", QString(u8"❌ 清除关联失败：节点为空"));
        return;
    }
    
    QString nodeName = item->text(0);
    AddLogEntry("INFO", QString(u8"🧹 清除关联：节点='%1'").arg(nodeName));
    
    // 确认清除
    QMessageBox::StandardButton reply = QMessageBox::question(
        this, u8"确认清除",
        QString(u8"确定要清除节点 '%1' 的所有关联吗？").arg(nodeName),
        QMessageBox::Yes | QMessageBox::No
    );
    
    if (reply == QMessageBox::Yes) {
        // 清除节点的关联信息
        item->setText(1, "");  // 清除关联列的内容
        
        // 🆕 新增：如果是主节点（CH1/CH2），也要清除所有子节点的界面显示
        if (nodeName == "CH1" || nodeName == "CH2") {
            for (int i = 0; i < item->childCount(); ++i) {
                QTreeWidgetItem* child = item->child(i);
                if (child) {
                    child->setText(1, "");  // 清除子节点的关联列
                    child->setText(5, "");  // 清除子节点的极性列
                }
            }
        } else if (nodeName == tr("载荷1") || nodeName == tr("载荷2") || nodeName == tr("位置") || nodeName == tr("控制")) {
            // 🆕 新增：如果是子节点，也要清除极性列
            item->setText(5, "");  // 清除子节点的极性列
        }
        
        // 从数据管理器中清除关联
        if (ctrlChanDataManager_) {
            auto groups = ctrlChanDataManager_->getAllControlChannelGroups();
            for (auto& group : groups) {
                for (auto& channel : group.channels) {
                    QString channelName = QString::fromStdString(channel.channelName);
                    
                    // 🔄 修改：处理主节点和子节点的不同清除逻辑
                    if (channelName == nodeName) {
                        // 清除主节点（CH1/CH2）的所有关联
                        channel.hardwareAssociation = "";
                        channel.load1Sensor = "";
                        channel.load2Sensor = "";
                        channel.positionSensor = "";
                        channel.controlActuator = "";
                        
                        ctrlChanDataManager_->updateControlChannelGroup(group);
                        break;
                    } else if (item->parent() && QString::fromStdString(channel.channelName) == item->parent()->text(0)) {
                        // 🆕 新增：处理子节点的单独清除
                        if (nodeName == tr("载荷1")) {
                            channel.load1Sensor = "";
                        } else if (nodeName == tr("载荷2")) {
                            channel.load2Sensor = "";
                        } else if (nodeName == tr("位置")) {
                            channel.positionSensor = "";
                        } else if (nodeName == tr("控制")) {
                            channel.controlActuator = "";
                        }
                        
                        ctrlChanDataManager_->updateControlChannelGroup(group);
                        break;
                    }
                }
            }
        }
        
        // 更新tooltip
        UpdateNodeTooltipAfterDragDrop(item, "", "清除");
        AddLogEntry("INFO", QString(u8"✅ 关联已清除"));
    }
}

/**
 * @brief 删除单个节点的关联信息
 */
void CMyMainWindow::OnClearSingleAssociation(QTreeWidgetItem* item) {
    if (!item) {
        AddLogEntry("ERROR", QString(u8"❌ 清除关联失败：节点为空"));
        return;
    }
    
    QString nodeName = item->text(0);
    QString currentAssociation = item->text(1);
    
    AddLogEntry("INFO", QString(u8"🧹 清除单个关联：节点='%1'").arg(nodeName));
    
    // 如果没有关联信息，则提示用户
    if (currentAssociation.isEmpty()) {
        QMessageBox::information(this, tr("提示"), 
            QString("节点 '%1' 当前没有关联信息。").arg(nodeName));
        return;
    }
    
    // 确认清除
    QMessageBox::StandardButton reply = QMessageBox::question(
        this, u8"确认清除",
        QString(u8"确定要清除节点 '%1' 的关联信息吗？\n\n当前关联: %2").arg(nodeName).arg(currentAssociation),
        QMessageBox::Yes | QMessageBox::No
    );
    
    if (reply == QMessageBox::Yes) {
        // 清除节点的关联信息
        item->setText(1, "");  // 清除关联列的内容
        
        // 如果是子节点，也要清除极性列
        if (nodeName == tr("载荷1") || nodeName == tr("载荷2") || nodeName == tr("位置") || nodeName == tr("控制")) {
            item->setText(5, "");  // 清除子节点的极性列
        }
        
        // 从数据管理器中清除关联
        if (ctrlChanDataManager_) {
            auto groups = ctrlChanDataManager_->getAllControlChannelGroups();
            for (auto& group : groups) {
                for (auto& channel : group.channels) {
                    QString channelName = QString::fromStdString(channel.channelName);
                    
                    if (channelName == nodeName) {
                        // 清除主节点（CH1/CH2）的硬件关联，但保留子节点关联
                        channel.hardwareAssociation = "";
                        ctrlChanDataManager_->updateControlChannelGroup(group);
                        break;
                    } else if (item->parent() && QString::fromStdString(channel.channelName) == item->parent()->text(0)) {
                        // 处理子节点的单独清除
                        if (nodeName == tr("载荷1")) {
                            channel.load1Sensor = "";
                        } else if (nodeName == tr("载荷2")) {
                            channel.load2Sensor = "";
                        } else if (nodeName == tr("位置")) {
                            channel.positionSensor = "";
                        } else if (nodeName == tr("控制")) {
                            channel.controlActuator = "";
                        }
                        
                        ctrlChanDataManager_->updateControlChannelGroup(group);
                        break;
                    }
                }
            }
        }
        
        // 更新tooltip
        UpdateNodeTooltipAfterDragDrop(item, "", "清除");
        AddLogEntry("INFO", QString(u8"✅ 单个关联已清除"));
        
        // 显示成功消息
        QMessageBox::information(this, tr("操作成功"), 
            QString("已成功删除节点 '%1' 的关联信息。").arg(nodeName));
    }
}

/**
 * @brief 删除所有关联信息（主节点及其所有子节点）
 */
void CMyMainWindow::OnClearAllAssociation(QTreeWidgetItem* item) {
    if (!item) {
        AddLogEntry("ERROR", QString(u8"❌ 清除所有关联失败：节点为空"));
        return;
    }
    
    QString nodeName = item->text(0);
    AddLogEntry("INFO", QString(u8"🧹 清除所有关联：节点='%1'").arg(nodeName));
    
    // 统计关联信息
    int associationCount = 0;
    QString associationList;
    
    // 检查主节点关联
    if (!item->text(1).isEmpty()) {
        associationCount++;
        associationList += QString("• %1: %2\n").arg(nodeName).arg(item->text(1));
    }
    
    // 检查子节点关联
    for (int i = 0; i < item->childCount(); ++i) {
        QTreeWidgetItem* child = item->child(i);
        if (child && !child->text(1).isEmpty()) {
            associationCount++;
            associationList += QString("• %1: %2\n").arg(child->text(0)).arg(child->text(1));
        }
    }
    
    if (associationCount == 0) {
        QMessageBox::information(this, tr("提示"), 
            QString("节点 '%1' 及其子节点当前都没有关联信息。").arg(nodeName));
        return;
    }
    
    // 确认清除
    QMessageBox::StandardButton reply = QMessageBox::question(
        this, u8"确认清除所有关联",
        QString(u8"确定要清除节点 '%1' 及其所有子节点的关联信息吗？\n\n将清除以下 %2 个关联：\n%3\n此操作将清除主节点和所有子节点的关联信息。")
            .arg(nodeName).arg(associationCount).arg(associationList),
        QMessageBox::Yes | QMessageBox::No
    );
    
    if (reply == QMessageBox::Yes) {
        // 清除主节点的关联信息
        item->setText(1, "");  // 清除关联列的内容
        
        // 清除所有子节点的界面显示
        for (int i = 0; i < item->childCount(); ++i) {
            QTreeWidgetItem* child = item->child(i);
            if (child) {
                child->setText(1, "");  // 清除子节点的关联列
                child->setText(5, "");  // 清除子节点的极性列
            }
        }
        
        // 从数据管理器中清除所有关联
        if (ctrlChanDataManager_) {
            auto groups = ctrlChanDataManager_->getAllControlChannelGroups();
            for (auto& group : groups) {
                for (auto& channel : group.channels) {
                    QString channelName = QString::fromStdString(channel.channelName);
                    if (channelName == nodeName) {
                        // 清除主节点（CH1/CH2）的所有关联
                        channel.hardwareAssociation = "";
                        channel.load1Sensor = "";
                        channel.load2Sensor = "";
                        channel.positionSensor = "";
                        channel.controlActuator = "";
                        
                        ctrlChanDataManager_->updateControlChannelGroup(group);
                        break;
                    }
                }
            }
        }
        
        // 更新tooltip
        UpdateNodeTooltipAfterDragDrop(item, "", "清除");
        AddLogEntry("INFO", QString(u8"✅ 所有关联已清除"));
        
        // 显示成功消息
        QMessageBox::information(this, tr("操作成功"), 
            QString("已成功删除节点 '%1' 及其所有子节点的关联信息。").arg(nodeName));
    }
}

/**
 * @brief 编辑控制通道详细配置
 */
void CMyMainWindow::OnEditControlChannelDetailed(QTreeWidgetItem* item) {
    if (!item) {
        AddLogEntry("ERROR", QString(u8"❌ 编辑控制通道失败：节点为空"));
        return;
    }
    
    QString channelId = item->text(0);
    AddLogEntry("INFO", QString(u8"🔧 编辑控制通道配置：通道='%1'").arg(channelId));
    
    // 获取当前通道参数
    UI::ControlChannelParams currentParams = getCurrentChannelParams(channelId);
    
    // 创建编辑对话框（传递MainWindow实例以获取真实数据）
    UI::ControlChannelEditDialog dialog(currentParams, this, this);
    
    // 🔧 注释：现在对话框会自动从MainWindow获取真实数据，不需要手动设置
    /*
    // 设置硬件组列表和成员信息
    auto hardwareData = getHardwareGroupsAndMembers();
    dialog.setHardwareGroups(hardwareData.first);
    dialog.setHardwareGroupMembers(hardwareData.second);
    
    // 设置传感器组列表和成员信息
    auto sensorData = getSensorGroupsAndMembers();
    dialog.setSensorGroups(sensorData.first);
    dialog.setSensorGroupMembers(sensorData.second);
    
    // 设置作动器组列表和成员信息
    auto actuatorData = getActuatorGroupsAndMembers();
    dialog.setActuatorGroups(actuatorData.first);
    dialog.setActuatorGroupMembers(actuatorData.second);
    
    // 兼容性：保留原有的设置方式（虽然不再使用）
    dialog.setHardwareNodes(getAvailableHardwareNodes());
    dialog.setSensorList(getAvailableSensors());
    dialog.setActuatorList(getAvailableActuators());
    "*/
    
    if (dialog.exec() == QDialog::Accepted) {
        UI::ControlChannelParams updatedParams = dialog.getUpdatedParams();
        
        // 更新数据管理器
        updateControlChannelParams(channelId, updatedParams);
        
        // 更新UI显示
        updateControlChannelDisplay(channelId, updatedParams);
        
        AddLogEntry("SUCCESS", QString(u8"✅ 控制通道 %1 配置更新成功").arg(channelId));
    }
}

/**
 * @brief 更新所有树形控件的提示信息
 */
void CMyMainWindow::UpdateAllTreeWidgetTooltips() {
    AddLogEntry("DEBUG", QString(u8"🔄 开始更新所有树形控件提示信息..."));
    
    // 更新硬件配置树提示
    UpdateHardwareTreeTooltips();
    
    // 更新试验配置树提示
    UpdateExperimentTreeTooltips();
    
    AddLogEntry("DEBUG", QString(u8"✅ 所有树形控件提示信息更新完成"));
}

/**
 * @brief 更新单个作动器的tooltip信息
 */
void CMyMainWindow::UpdateSingleActuatorTooltip(const QString& serialNumber, int groupId) {
    AddLogEntry("DEBUG", QString(u8"🔄 更新单个作动器tooltip：序列号=%1, 组ID=%2").arg(serialNumber).arg(groupId));
    
    if (!actuatorViewModel1_2_) {
        AddLogEntry("WARNING", u8"⚠️ 作动器视图模型未初始化");
        return;
    }
    
    // 获取组名
    QString groupName;
    auto groups = actuatorViewModel1_2_->getAllActuatorGroups();
    for (const auto& group : groups) {
        if (group.groupId == groupId) {
            groupName = group.groupName;
            break;
        }
    }
    
    if (groupName.isEmpty()) {
        AddLogEntry("WARNING", QString(u8"⚠️ 未找到组ID=%1对应的组名").arg(groupId));
        return;
    }
    
    // 在硬件树中查找并更新节点
    UpdateActuatorNodeTooltipInTree(ui->hardwareTreeWidget, serialNumber, groupName);
    
    // 在试验配置树中查找并更新节点
    UpdateActuatorNodeTooltipInTree(ui->testConfigTreeWidget, serialNumber, groupName);
    
    AddLogEntry("DEBUG", QString(u8"✅ 单个作动器tooltip更新完成：%1").arg(serialNumber));
}

/**
 * @brief 在指定树形控件中更新作动器节点的tooltip
 */
void CMyMainWindow::UpdateActuatorNodeTooltipInTree(QTreeWidget* treeWidget, const QString& serialNumber, const QString& groupName) {
    if (!treeWidget) {
        return;
    }
    
    // 递归查找作动器节点
    std::function<void(QTreeWidgetItem*)> findAndUpdateNode = [&](QTreeWidgetItem* item) {
        if (!item) return;
        
        // 检查当前节点是否为目标作动器
        QString itemText = item->text(0);
        QString itemType = item->data(0, Qt::UserRole).toString();
        
        if (itemText == serialNumber && itemType == "作动器设备") {
                         // 检查是否在正确的组下
             QTreeWidgetItem* parentItem = item->parent();
             if (parentItem && parentItem->text(0) == groupName) {
                 // 获取组ID
                 int groupId = -1;
                 if (actuatorViewModel1_2_) {
                     auto groups = actuatorViewModel1_2_->getAllActuatorGroups();
                     for (const auto& group : groups) {
                         if (group.groupName == groupName) {
                             groupId = group.groupId;
                             break;
                         }
                     }
                 }
                 
                 // 使用精准版本的tooltip生成
                 QString newTooltip;
                 if (groupId != -1) {
                     newTooltip = GenerateActuatorDeviceDetailedInfo(serialNumber, groupId);
                 } else {
                     newTooltip = GenerateActuatorDeviceDetailedInfo(serialNumber); // 兜底使用原版本
                 }
                 
                 item->setToolTip(0, newTooltip);
                 AddLogEntry("DEBUG", QString(u8"✅ 已更新作动器节点tooltip：%1 (组：%2, 组ID：%3)").arg(serialNumber).arg(groupName).arg(groupId));
                 return;
             }
        }
        
        // 递归检查子节点
        for (int i = 0; i < item->childCount(); ++i) {
            findAndUpdateNode(item->child(i));
        }
    };
    
    // 从根节点开始查找
    QTreeWidgetItem* rootItem = treeWidget->topLevelItem(0);
    if (rootItem) {
        findAndUpdateNode(rootItem);
    }
}

/**
 * @brief 更新硬件配置树的所有节点提示
 */
void CMyMainWindow::UpdateHardwareTreeTooltips() {
    if (!ui->hardwareTreeWidget) {
        return;
    }
    
    AddLogEntry("DEBUG", QString(u8"🔄 更新硬件配置树提示信息..."));
    
    QTreeWidgetItem* rootItem = ui->hardwareTreeWidget->topLevelItem(0);
    if (rootItem) {
        UpdateTreeItemTooltipsRecursively(rootItem);
    }
    
    AddLogEntry("DEBUG", QString(u8"✅ 硬件配置树提示信息更新完成"));
}

/**
 * @brief 更新试验配置树的所有节点提示
 */
void CMyMainWindow::UpdateExperimentTreeTooltips() {
    if (!ui->testConfigTreeWidget) {
        return;
    }
    
    AddLogEntry("DEBUG", QString(u8"🔄 更新试验配置树提示信息..."));
    
    QTreeWidgetItem* rootItem = ui->testConfigTreeWidget->topLevelItem(0);
    if (rootItem) {
        UpdateTreeItemTooltipsRecursively(rootItem);
    }
    
    AddLogEntry("DEBUG", QString(u8"✅ 试验配置树提示信息更新完成"));
}

/**
 * @brief 递归更新树节点的提示信息
 */
void CMyMainWindow::UpdateTreeItemTooltipsRecursively(QTreeWidgetItem* item) {
    if (!item) {
        return;
    }
    
    // 更新当前节点的提示
    UpdateSingleNodeTooltip(item);
    
    // 递归更新子节点
    for (int i = 0; i < item->childCount(); ++i) {
        UpdateTreeItemTooltipsRecursively(item->child(i));
    }
}

/**
 * @brief 更新单个节点的提示信息
 */
void CMyMainWindow::UpdateSingleNodeTooltip(QTreeWidgetItem* item) {
    if (!item) {
        return;
    }
    
    QString nodeName = item->text(0);
    QString nodeType = item->data(0, Qt::UserRole).toString();
    QString associationInfo = item->text(1);
    
    QString tooltip;
    
    if (nodeType == "控制通道") {
        tooltip = GenerateControlChannelDetailedInfo(nodeName);
    } else if (nodeType == "载荷传感器") {
        tooltip = GenerateLoadSensorDetailedInfo(nodeName, associationInfo);
    } else if (nodeType == "位置传感器") {
        tooltip = GeneratePositionSensorDetailedInfo(associationInfo);
    } else if (nodeType == "控制作动器") {
        tooltip = GenerateControlActuatorDetailedInfo(associationInfo);
    } else if (nodeType == "作动器设备") {
        tooltip = GenerateActuatorDeviceDetailedInfo(nodeName);
    } else if (nodeType == "传感器设备") {
        tooltip = GenerateSensorDeviceDetailedInfo(nodeName);
    } else {
        tooltip = QString(u8"📋 %1\n类型: %2").arg(nodeName).arg(nodeType);
        if (!associationInfo.isEmpty()) {
            tooltip += QString(u8"\n关联: %1").arg(associationInfo);
        }
    }
    
    item->setToolTip(0, tooltip);
}

// ============================================================================
// 🆕 新增：最后一批缺失方法的实现
// ============================================================================

/**
 * @brief 从数据管理器刷新试验配置树显示
 */
void CMyMainWindow::RefreshTestConfigTreeFromDataManagers() {
    if (!ui->testConfigTreeWidget) {
        AddLogEntry("ERROR", QString(u8"试验配置树控件为空"));
        return;
    }

    AddLogEntry("INFO", QString(u8"开始从数据管理器刷新试验配置树..."));

    // 重新初始化试验配置树结构
    InitializeTestConfigTree();

    // 获取任务1根节点
    QTreeWidgetItem* taskRoot = ui->testConfigTreeWidget->topLevelItem(0);
    if (!taskRoot) {
        AddLogEntry("ERROR", QString(u8"无法获取试验配置树根节点"));
        return;
    }

    // 从控制通道数据管理器填充数据
    if (ctrlChanDataManager_) {
        auto channelGroups = ctrlChanDataManager_->getAllControlChannelGroups();
        AddLogEntry("INFO", QString(u8"🔍 开始填充控制通道数据：共%1个组").arg(channelGroups.size()));

        // ✅ 查找现有的"控制通道"根节点
        QTreeWidgetItem* controlChannelRoot = nullptr;
        for (int i = 0; i < taskRoot->childCount(); ++i) {
            QTreeWidgetItem* child = taskRoot->child(i);
            if (child && child->text(0) == tr("控制通道")) {
                controlChannelRoot = child;
                break;
            }
        }

        if (controlChannelRoot) {
            for (const auto& group : channelGroups) {
                AddLogEntry("INFO", QString(u8"📊 处理控制通道组：ID=%1, 名称='%2', 通道数量=%3")
                           .arg(group.groupId).arg(QString::fromStdString(group.groupName)).arg(group.channels.size()));

                for (const auto& channel : group.channels) {
                    QString channelName = QString::fromStdString(channel.channelName);
                    
                    // ✅ 查找现有的CH1/CH2节点并根据channelId正确更新
                    QString channelId = QString::fromStdString(channel.channelId);
                    QTreeWidgetItem* targetChannelItem = nullptr;
                    
                    // 🔧 修复：优先按channelId查找，避免channelName混乱
                    for (int i = 0; i < controlChannelRoot->childCount(); ++i) {
                        QTreeWidgetItem* existingChannel = controlChannelRoot->child(i);
                        if (!existingChannel) continue;
                        
                        QString storedChannelId = existingChannel->data(1, Qt::UserRole).toString();
                        if (storedChannelId == channelId) {
                            targetChannelItem = existingChannel;
                            break;
                        }
                    }
                    
                    if (targetChannelItem) {
                        // ✅ 更新通道名称显示（确保显示Excel中的通道名称）
                        if (!channelName.isEmpty()) {
                            targetChannelItem->setText(0, channelName);
                            AddLogEntry("INFO", QString(u8"✅ 更新通道%1名称显示: %2").arg(channelId).arg(channelName));
                        }
                        
                        // ✅ 确保channelId正确存储
                        targetChannelItem->setData(1, Qt::UserRole, channelId);
                        
                        // ✅ 设置到现有节点的关联信息列
                        targetChannelItem->setText(1, QString::fromStdString(channel.hardwareAssociation));
                        
                        // 🆕 新增：设置扩展列信息（下位机ID、站点ID、使能、极性）
                        targetChannelItem->setText(2, QString::number(channel.lc_id));           // 下位机ID
                        targetChannelItem->setText(3, QString::number(channel.station_id));      // 站点ID
                        targetChannelItem->setText(4, channel.enable ? u8"✅" : u8"❌");          // 使能状态
                        
                        // 🔧 修改：CH1、CH2第五列不显示极性值
                        if (channelId == "CH1" || channelId == "CH2") {
                            targetChannelItem->setText(5, "");     // CH1、CH2第五列不显示值
                        } else {
                            targetChannelItem->setText(5, getPolarityShortText(channel.servo_control_polarity)); // 其他通道显示极性
                        }
                        
                        // ✅ 设置子节点的关联信息和极性
                        for (int j = 0; j < targetChannelItem->childCount(); ++j) {
                            QTreeWidgetItem* child = targetChannelItem->child(j);
                            if (!child) continue;
                            
                            QString childName = child->text(0);
                            if (childName == tr("载荷1")) {
                                child->setText(1, QString::fromStdString(channel.load1Sensor));
                                child->setText(5, getPolarityShortText(channel.payload_sensor1_polarity)); // 🆕 新增：载荷1传感器极性
                            } else if (childName == tr("载荷2")) {
                                child->setText(1, QString::fromStdString(channel.load2Sensor));
                                child->setText(5, getPolarityShortText(channel.payload_sensor2_polarity)); // 🆕 新增：载荷2传感器极性
                            } else if (childName == tr("位置")) {
                                child->setText(1, QString::fromStdString(channel.positionSensor));
                                child->setText(5, getPolarityShortText(channel.position_sensor_polarity)); // 🆕 新增：位置传感器极性
                            } else if (childName == tr("控制")) {
                                child->setText(1, QString::fromStdString(channel.controlActuator));
                                child->setText(5, getPolarityShortText(channel.servo_control_polarity)); // 🆕 新增：控制作动器极性
                            }
                        }
                        
                        AddLogEntry("INFO", QString(u8"✅ 已正确填充通道 %1 (%2) 的名称和关联信息").arg(channelId).arg(channelName));
                    } else {
                        AddLogEntry("WARNING", QString(u8"⚠️ 未找到通道%1的UI节点，无法更新显示").arg(channelId));
                    }
                }
            }
        } else {
            AddLogEntry("WARNING", QString(u8"未找到控制通道根节点，跳过数据填充"));
        }
    }

    // 🆕 新增：完全展开实验配置树
    ui->testConfigTreeWidget->expandAll();
    
    // 🔧 修复：确保右键菜单事件在树重建后重新连接
    if (ui->testConfigTreeWidget) {
        ui->testConfigTreeWidget->setContextMenuPolicy(Qt::CustomContextMenu);
        // 重新连接右键菜单信号（确保连接有效）
        disconnect(ui->testConfigTreeWidget, &QTreeWidget::customContextMenuRequested, this, &CMyMainWindow::OnTestConfigTreeContextMenu);
        connect(ui->testConfigTreeWidget, &QTreeWidget::customContextMenuRequested, this, &CMyMainWindow::OnTestConfigTreeContextMenu);
        AddLogEntry("DEBUG", QString(u8"已重新连接试验配置树右键菜单事件"));
    }
    
    AddLogEntry("INFO", QString(u8"试验配置树数据填充完成并已完全展开"));
}

/**
 * @brief 生成组详细信息
 */
QString CMyMainWindow::GenerateGroupDetailedInfo(const QString& groupName) {
    QString info;
    info += QString(u8"📋 组详细信息\n");
    info += QString(u8"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n");
    info += QString(u8"组名称: %1\n").arg(groupName);

    // 查找作动器组信息
    if (actuatorViewModel1_2_) {
        auto groups = actuatorViewModel1_2_->getAllActuatorGroups();
        for (const auto& group : groups) {
            if (group.groupName == groupName) {
                info += QString(u8"组ID: %1\n").arg(group.groupId);
                info += QString(u8"组类型: %1\n").arg(group.groupType);
                info += QString(u8"创建时间: %1\n").arg(group.createTime);
                info += QString(u8"设备数量: %1\n").arg(group.actuators.size());
                if (!group.groupNotes.isEmpty()) {
                    info += QString(u8"备注: %1\n").arg(group.groupNotes);
                }
                info += QString(u8"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
                return info;
            }
        }
    }

    // 查找传感器组信息
    if (sensorDataManager_) {
        auto groups = sensorDataManager_->getAllSensorGroups();
        for (const auto& group : groups) {
            if (group.groupName == groupName) {
                info += QString(u8"组ID: %1\n").arg(group.groupId);
                info += QString(u8"组类型: %1\n").arg(group.groupType);
                info += QString(u8"创建时间: %1\n").arg(group.createTime);
                info += QString(u8"设备数量: %1\n").arg(group.sensors.size());
                if (!group.groupNotes.isEmpty()) {
                    info += QString(u8"备注: %1\n").arg(group.groupNotes);
                }
                info += QString(u8"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
                return info;
            }
        }
    }

    info += QString(u8"状态: 未找到组信息\n");
    info += QString(u8"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    return info;
}

/**
 * @brief 生成作动器设备详细信息
 */
QString CMyMainWindow::GenerateActuatorDeviceDetailedInfo(const QString& deviceName) {
    QString info;
    info += QString(u8"⚙️ 作动器设备详细信息\n");
    info += QString(u8"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n");
    info += QString(u8"设备名称: %1\n").arg(deviceName);

    if (actuatorViewModel1_2_) {
        // 获取作动器所属的组ID
        int groupId = actuatorViewModel1_2_->getActuatorGroupId(deviceName);
        if (groupId == -1) {
            info += QString(u8"状态: 未找到作动器所属组\n");
            info += QString(u8"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            return info;
        }
        
        UI::ActuatorParams_1_2 params = actuatorViewModel1_2_->getActuator(deviceName, groupId);
        if (!params.params.sn.isEmpty()) {
            info += QString(u8"作动器ID: %1\n").arg(params.actuatorId);
            info += QString(u8"型号: %1\n").arg(params.params.model);
            info += QString(u8"序列号: %1\n").arg(params.params.sn);
            info += QString(u8"控制量名称: %1\n").arg(params.name);
            info += QString(u8"K系数: %1\n").arg(params.params.k);
            info += QString(u8"B系数: %1\n").arg(params.params.b);
            info += QString(u8"精度: %1\n").arg(params.params.precision);
            info += QString(u8"零偏: %1\n").arg(params.zero_offset);
            info += QString(u8"下位机ID: %1\n").arg(params.lc_id);
            info += QString(u8"站点ID: %1\n").arg(params.station_id);
            info += QString(u8"测量范围: %1 到 %2\n").arg(params.params.meas_range_min).arg(params.params.meas_range_max);
            info += QString(u8"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            return info;
        }
    }

    info += QString(u8"状态: 未找到设备信息\n");
    info += QString(u8"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    return info;
}

/**
 * @brief 生成作动器设备详细信息（带组ID的精准版本）
 */
QString CMyMainWindow::GenerateActuatorDeviceDetailedInfo(const QString& deviceName, int groupId) {
    QString info;
    info += QString(u8"⚙️ 作动器设备详细信息\n");
    info += QString(u8"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n");
    info += QString(u8"设备名称: %1\n").arg(deviceName);
    info += QString(u8"所属组ID: %1\n").arg(groupId);

    if (actuatorViewModel1_2_) {
        // 🔧 修复：使用精准的组ID获取作动器信息
        UI::ActuatorParams_1_2 params = actuatorViewModel1_2_->getActuator(deviceName, groupId);
        if (!params.params.sn.isEmpty()) {
            info += QString(u8"作动器ID: %1\n").arg(params.actuatorId);
            info += QString(u8"型号: %1\n").arg(params.params.model);
            info += QString(u8"序列号: %1\n").arg(params.params.sn);
            info += QString(u8"控制量名称: %1\n").arg(params.name);
            info += QString(u8"K系数: %1\n").arg(params.params.k);
            info += QString(u8"B系数: %1\n").arg(params.params.b);
            info += QString(u8"精度: %1\n").arg(params.params.precision);
            info += QString(u8"零偏: %1\n").arg(params.zero_offset);
            info += QString(u8"下位机ID: %1\n").arg(params.lc_id);
            info += QString(u8"站点ID: %1\n").arg(params.station_id);
            info += QString(u8"测量范围: %1 到 %2\n").arg(params.params.meas_range_min).arg(params.params.meas_range_max);
            info += QString(u8"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            return info;
        }
    }

    info += QString(u8"状态: 未找到设备信息\n");
    info += QString(u8"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    return info;
}

/**
 * @brief 生成传感器设备详细信息
 */
QString CMyMainWindow::GenerateSensorDeviceDetailedInfo(const QString& deviceName) {
    QString info;
    info += QString(u8"🔧 传感器设备详细信息\n");
    info += QString(u8"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n");
    info += QString(u8"设备名称: %1\n").arg(deviceName);

    if (sensorDataManager_) {
        auto groups = sensorDataManager_->getAllSensorGroups();
        for (const auto& group : groups) {
            for (const auto& sensor : group.sensors) {
                if (sensor.params_sn == deviceName) {
                    info += QString(u8"传感器ID: %1\n").arg(sensor.sensorId);
                    info += QString(u8"型号: %1\n").arg(sensor.params_model);
                    info += QString(u8"序列号: %1\n").arg(sensor.params_sn);
                    info += QString(u8"传感器类型: %1\n").arg(sensor.sensorType);
                    info += QString(u8"K系数: %1\n").arg(sensor.params_k);
                    info += QString(u8"B系数: %1\n").arg(sensor.params_b);
                    info += QString(u8"精度: %1\n").arg(sensor.params_precision);
                    info += QString(u8"极性: %1\n").arg(polarityToString(sensor.params_polarity));
                    info += QString(u8"零点偏移: %1\n").arg(sensor.zero_offset);
                    info += QString(u8"启用状态: %1\n").arg(sensor.enable ? u8"启用" : u8"禁用");
                    info += QString(u8"测量范围: %1 到 %2\n").arg(sensor.meas_range_min).arg(sensor.meas_range_max);
                    info += QString(u8"输出信号范围: %1 到 %2\n").arg(sensor.output_signal_range_min).arg(sensor.output_signal_range_max);
                    info += QString(u8"所属组: %1\n").arg(group.groupName);
                    info += QString(u8"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
                    return info;
                }
            }
        }
    }

    info += QString(u8"状态: 未找到设备信息\n");
    info += QString(u8"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    return info;
}

/**
 * @brief 项目保存完成处理
 */
void CMyMainWindow::OnProjectSaved(const QString& projectPath) {
    AddLogEntry("INFO", QString(u8"💾 项目保存完成：%1").arg(projectPath));
    
    // 更新当前项目路径
    currentProjectPath_ = projectPath;
    
    // 更新窗口标题显示保存状态
    QFileInfo fileInfo(projectPath);
    setWindowTitle(QString(u8"SiteResConfig - %1 [已保存]").arg(fileInfo.baseName()));
    
    // 发送项目保存信号（如果需要）
    // emit projectSaved(projectPath);
    
    AddLogEntry("INFO", QString(u8"✅ 项目保存处理完成"));
}

/**
 * @brief 从UI收集控制通道组数据
 */
QList<UI::ControlChannelGroup> CMyMainWindow::collectControlChannelGroupsFromUI() const {
    QList<UI::ControlChannelGroup> groups;
    
    if (!ui->testConfigTreeWidget) {
        return groups;
    }
    
    QTreeWidgetItem* rootItem = ui->testConfigTreeWidget->topLevelItem(0);
    if (!rootItem) {
        return groups;
    }
    
    // 遍历所有控制通道组
    for (int i = 0; i < rootItem->childCount(); ++i) {
        QTreeWidgetItem* groupItem = rootItem->child(i);
        QString groupType = groupItem->data(0, Qt::UserRole).toString();
        
        if (groupType == "控制通道组") {
            UI::ControlChannelGroup group;
            group.groupName = groupItem->text(0).toStdString();
            group.groupId = i + 1;
            group.groupType = "控制通道";
            
            // 遍历组内的通道
            for (int j = 0; j < groupItem->childCount(); ++j) {
                QTreeWidgetItem* channelItem = groupItem->child(j);
                UI::ControlChannelParams channel;
                channel.channelName = channelItem->text(0).toStdString();
                channel.hardwareAssociation = channelItem->text(1).toStdString();
                channel.channelId = QString("CH%1").arg(j + 1).toStdString();
                group.channels.push_back(channel);
            }
            
            groups.append(group);
        }
    }
    
    AddLogEntry("DEBUG", QString(u8"从UI收集到%1个控制通道组").arg(groups.size()));
    return groups;
}

/**
 * @brief 从UI构建硬件节点配置
 */
QList<UI::NodeConfigParams> CMyMainWindow::buildHardwareNodeConfigsFromUI() const {
    QList<UI::NodeConfigParams> nodeConfigs;
    
    if (hardwareNodeResDataManager_) {
        auto configs = hardwareNodeResDataManager_->getAllHardwareNodeConfigs();
        for (const auto& config : configs) {
            UI::NodeConfigParams nodeConfig;
            nodeConfig.nodeName = config.nodeName;
            nodeConfig.channelCount = config.channels.size();
            nodeConfig.channels = config.channels;
            nodeConfigs.append(nodeConfig);
        }
    }
    
    AddLogEntry("DEBUG", QString(u8"从UI构建了%1个硬件节点配置").arg(nodeConfigs.size()));
    return nodeConfigs;
}

/**
 * @brief 作动器组创建业务处理
 */
void CMyMainWindow::onActuatorGroupCreatedBusiness(const QString& groupName, int groupId) {
    AddLogEntry("INFO", QString(u8"📋 作动器组创建业务处理：组名='%1', 组ID=%2").arg(groupName).arg(groupId));
    
    // 🆕 参考传感器操作：直接创建UI节点，不刷新整个树
    CreateActuatorGroupUI(groupName, groupId);
    
    // 更新界面提示
    UpdateAllTreeWidgetTooltips();
    
    AddLogEntry("INFO", QString(u8"✅ 作动器组创建业务处理完成"));
}

/**
 * @brief 作动器设备创建业务处理
 */
void CMyMainWindow::onActuatorDeviceCreatedBusiness(const QString& serialNumber, int groupId) {
    AddLogEntry("INFO", QString(u8"⚙️ 作动器设备创建业务处理：序列号='%1', 组ID=%2").arg(serialNumber).arg(groupId));
    
    // 🆕 修复：参考传感器处理方式，直接操作树形控件，避免刷新整个树
    if (actuatorViewModel1_2_) {
        // 获取组名
        auto groups = actuatorViewModel1_2_->getAllActuatorGroups();
        for (const auto& group : groups) {
            if (group.groupId == groupId) {
                // 使用直接操作方式创建UI节点（类似传感器）
                CreateActuatorDeviceUI(serialNumber, group.groupName);
                
                AddLogEntry("INFO", QString(u8"✅ 作动器设备创建业务处理完成 - 直接操作模式"));
                return;
            }
        }
    }
    
    AddLogEntry("WARNING", QString(u8"未找到组ID=%1对应的作动器组").arg(groupId));
}

/**
 * @brief 作动器设备编辑业务处理
 */
void CMyMainWindow::onActuatorDeviceEditedBusiness(const QString& serialNumber, int groupId) {
    AddLogEntry("INFO", QString(u8"✏️ 作动器设备编辑业务处理：序列号='%1', 组ID=%2").arg(serialNumber).arg(groupId));
    
    // 🔧 修复：使用精准的组ID获取作动器信息，避免全局搜索
    if (actuatorViewModel1_2_) {
        UI::ActuatorParams_1_2 actuator = actuatorViewModel1_2_->getActuator(serialNumber, groupId);
        if (!actuator.params.sn.isEmpty()) {
            // 获取组名以使用精准更新函数
            QString groupName = actuatorViewModel1_2_->getActuatorGroupNameBusiness(groupId);
            if (!groupName.isEmpty()) {
                // 精准更新指定组的作动器关联（使用精准更新函数）
                UpdateControlChannelAssociationsAfterActuatorEditPrecise(groupId, groupName, serialNumber, actuator);
            } else {
                AddLogEntry("WARNING", QString(u8"⚠️ 无法获取组名：组ID=%1").arg(groupId));
                // 回退到普通更新函数
                UpdateControlChannelAssociationsAfterActuatorEdit(serialNumber, actuator);
            }
            
            // 🆕 修复：只更新特定节点的tooltip，而不是全部刷新
            UpdateSingleActuatorTooltip(serialNumber, groupId);
            
            AddLogEntry("SUCCESS", QString(u8"✅ 作动器编辑业务处理完成：%1").arg(serialNumber));
            return;
        }
    }
    
    AddLogEntry("WARNING", QString(u8"⚠️ 未找到编辑的作动器：序列号=%1, 组ID=%2").arg(serialNumber).arg(groupId));
    
    AddLogEntry("INFO", QString(u8"✅ 作动器设备编辑业务处理完成"));
}

/**
 * @brief 作动器设备删除业务处理
 */
void CMyMainWindow::onActuatorDeviceDeletedBusiness(const QString& serialNumber) {
    AddLogEntry("INFO", QString(u8"🗑️ 作动器设备删除业务处理：序列号='%1'").arg(serialNumber));
    
    // 🆕 增强：获取作动器所有组名并精确清除控制通道关联
    QStringList groupNames = GetActuatorGroupNamesBySerialNumber(serialNumber);
    if (!groupNames.isEmpty()) {
        if (groupNames.size() > 1) {
            AddLogEntry("WARNING", QString(u8"⚠️ 作动器 %1 属于多个组: %2，将从所有相关控制通道中删除").arg(serialNumber).arg(groupNames.join(", ")));
        }
        // 为每个组调用删除函数，确保完全清理
        for (const QString& groupName : groupNames) {
            UpdateControlChannelAssociationsAfterActuatorDelete(serialNumber, groupName);
        }
    } else {
        // 兜底：使用兼容版删除函数
        UpdateControlChannelAssociationsAfterActuatorDelete(serialNumber);
    }
    
    AddLogEntry("INFO", QString(u8"✅ 作动器设备删除业务处理完成"));
}

/**
 * @brief 作动器验证错误处理
 */
void CMyMainWindow::onActuatorValidationError(const QString& errorMessage) {
    AddLogEntry("ERROR", QString(u8"❌ 作动器验证错误：%1").arg(errorMessage));
    
    // 显示错误消息给用户
    QMessageBox::warning(this, u8"作动器验证错误", errorMessage);
}

// ============================================================================
// 🆕 新增：树形控件展开方法实现
// ============================================================================

/**
 * @brief 保存树形控件的展开状态
 * @param tree 目标树形控件
 * @return 节点路径到展开状态的映射
 */
QMap<QString, bool> CMyMainWindow::saveTreeExpandedStates(QTreeWidget* tree) {
    QMap<QString, bool> states;
    if (!tree) return states;
    
    // 使用迭代器遍历所有节点
    QTreeWidgetItemIterator it(tree);
    while (*it) {
        QTreeWidgetItem* item = *it;
        if (item) {
            // 构建节点的完整路径作为唯一标识
            QString path = getItemPath(item);
            states[path] = item->isExpanded();
        }
        ++it;
    }
    
    AddLogEntry("DEBUG", QString(u8"保存了%1个节点的展开状态").arg(states.size()));
    return states;
}

/**
 * @brief 恢复树形控件的展开状态
 * @param tree 目标树形控件
 * @param states 节点路径到展开状态的映射
 */
void CMyMainWindow::restoreTreeExpandedStates(QTreeWidget* tree, const QMap<QString, bool>& states) {
    if (!tree || states.isEmpty()) return;
    
    // 使用迭代器遍历所有节点
    QTreeWidgetItemIterator it(tree);
    int restoredCount = 0;
    while (*it) {
        QTreeWidgetItem* item = *it;
        if (item) {
            QString path = getItemPath(item);
            if (states.contains(path)) {
                item->setExpanded(states[path]);
                if (states[path]) {
                    restoredCount++;
                }
            }
        }
        ++it;
    }
    
    AddLogEntry("DEBUG", QString(u8"恢复了%1个节点的展开状态").arg(restoredCount));
}

/**
 * @brief 获取树形控件项的完整路径
 * @param item 树形控件项
 * @return 从根到该项的完整路径字符串
 */
QString CMyMainWindow::getItemPath(QTreeWidgetItem* item) {
    if (!item) return QString();
    
    QStringList pathParts;
    QTreeWidgetItem* current = item;
    
    // 从当前项向上遍历到根节点
    while (current) {
        pathParts.prepend(current->text(0));
        current = current->parent();
    }
    
    return pathParts.join("/");
}

/**
 * @brief 展开指定作动器组在树形控件中的显示
 * @param groupName 作动器组名称
 */
void CMyMainWindow::expandActuatorGroupInTree(const QString& groupName) {
    if (!ui->hardwareTreeWidget) {
        AddLogEntry("WARNING", QString(u8"⚠️ 树形控件不存在，无法展开组：%1").arg(groupName));
        return;
    }
    
    AddLogEntry("DEBUG", QString(u8"🔍 开始查找并展开作动器组：%1").arg(groupName));
    
    // 查找硬件配置根节点
    QTreeWidgetItem* taskRoot = ui->hardwareTreeWidget->topLevelItem(0);
    if (!taskRoot) {
        AddLogEntry("WARNING", QString(u8"⚠️ 未找到根节点，无法展开组：%1").arg(groupName));
        return;
    }
    
    AddLogEntry("DEBUG", QString(u8"✅ 找到根节点：%1").arg(taskRoot->text(0)));
    
    // 查找作动器根节点
    QTreeWidgetItem* actuatorRoot = nullptr;
    for (int i = 0; i < taskRoot->childCount(); ++i) {
        QTreeWidgetItem* child = taskRoot->child(i);
        if (child && child->text(0) == tr("作动器")) {
            actuatorRoot = child;
            AddLogEntry("DEBUG", QString(u8"✅ 找到作动器根节点，子节点数量：%1").arg(actuatorRoot->childCount()));
            break;
        }
    }
    
    if (!actuatorRoot) {
        AddLogEntry("WARNING", QString(u8"⚠️ 未找到作动器根节点，无法展开组：%1").arg(groupName));
        return;
    }
    
    // 查找指定的作动器组并展开
    bool groupFound = false;
    for (int i = 0; i < actuatorRoot->childCount(); ++i) {
        QTreeWidgetItem* groupItem = actuatorRoot->child(i);
        if (groupItem) {
            AddLogEntry("DEBUG", QString(u8"🔍 检查作动器组[%1]：'%2' vs 目标：'%3'").arg(i).arg(groupItem->text(0)).arg(groupName));
            
            if (groupItem->text(0) == groupName) {
                // 展开层级：根节点 -> 作动器节点 -> 作动器组
                taskRoot->setExpanded(true);
                actuatorRoot->setExpanded(true);
                groupItem->setExpanded(true);
                
                // 🆕 新增：使用QTimer延迟滚动，确保树形控件完全渲染后再滚动
                QTimer::singleShot(100, [this, groupItem]() {
                    if (groupItem && ui->hardwareTreeWidget) {
                        ui->hardwareTreeWidget->scrollToItem(groupItem);
                        ui->hardwareTreeWidget->setCurrentItem(groupItem);
                    }
                });
                
                AddLogEntry("INFO", QString(u8"📂 已展开作动器组：%1 (子节点数量：%2)").arg(groupName).arg(groupItem->childCount()));
                groupFound = true;
                break;
            }
        }
    }
    
    if (!groupFound) {
        AddLogEntry("WARNING", QString(u8"⚠️ 未找到指定的作动器组：%1，当前作动器组列表：").arg(groupName));
        for (int i = 0; i < actuatorRoot->childCount(); ++i) {
            QTreeWidgetItem* groupItem = actuatorRoot->child(i);
            if (groupItem) {
                AddLogEntry("DEBUG", QString(u8"  - 组[%1]：%2").arg(i).arg(groupItem->text(0)));
            }
        }
    }
}

/**
 * @brief 展开包含指定作动器设备的组在树形控件中的显示
 * @param serialNumber 作动器序列号
 */
void CMyMainWindow::expandActuatorDeviceInTree(const QString& serialNumber) {
    if (!ui->hardwareTreeWidget) {
        AddLogEntry("WARNING", QString(u8"⚠️ 树形控件不存在，无法展开设备：%1").arg(serialNumber));
        return;
    }
    
    AddLogEntry("DEBUG", QString(u8"🔍 开始查找并展开作动器设备：%1").arg(serialNumber));
    
    // 查找硬件配置根节点
    QTreeWidgetItem* taskRoot = ui->hardwareTreeWidget->topLevelItem(0);
    if (!taskRoot) {
        AddLogEntry("WARNING", QString(u8"⚠️ 未找到根节点，无法展开设备：%1").arg(serialNumber));
        return;
    }
    
    AddLogEntry("DEBUG", QString(u8"✅ 找到根节点：%1").arg(taskRoot->text(0)));
    
    // 查找作动器根节点
    QTreeWidgetItem* actuatorRoot = nullptr;
    for (int i = 0; i < taskRoot->childCount(); ++i) {
        QTreeWidgetItem* child = taskRoot->child(i);
        if (child && child->text(0) == tr("作动器")) {
            actuatorRoot = child;
            AddLogEntry("DEBUG", QString(u8"✅ 找到作动器根节点，子节点数量：%1").arg(actuatorRoot->childCount()));
            break;
        }
    }
    
    if (!actuatorRoot) {
        AddLogEntry("WARNING", QString(u8"⚠️ 未找到作动器根节点，无法展开设备：%1").arg(serialNumber));
        return;
    }
    
    // 遍历所有作动器组，查找包含指定序列号的设备
    bool deviceFound = false;
    for (int i = 0; i < actuatorRoot->childCount(); ++i) {
        QTreeWidgetItem* groupItem = actuatorRoot->child(i);
        if (!groupItem) continue;
        
        // 在组内查找指定的作动器设备
        for (int j = 0; j < groupItem->childCount(); ++j) {
            QTreeWidgetItem* deviceItem = groupItem->child(j);
            if (deviceItem && deviceItem->text(0) == serialNumber) {
                // 展开整个层级：根节点 -> 作动器节点 -> 作动器组 -> 作动器设备
                taskRoot->setExpanded(true);
                actuatorRoot->setExpanded(true);
                groupItem->setExpanded(true);
                
                // 🆕 新增：使用QTimer延迟滚动，确保树形控件完全渲染后再滚动
                QTimer::singleShot(100, [this, deviceItem]() {
                    if (deviceItem && ui->hardwareTreeWidget) {
                        ui->hardwareTreeWidget->scrollToItem(deviceItem);
                        ui->hardwareTreeWidget->setCurrentItem(deviceItem);
                    }
                });
                
                AddLogEntry("INFO", QString(u8"📂 已展开作动器设备：%1 (组：%2)").arg(serialNumber).arg(groupItem->text(0)));
                deviceFound = true;
                break;
            }
        }
        
        if (deviceFound) {
            break;
        }
    }
    
    if (!deviceFound) {
        AddLogEntry("WARNING", QString(u8"⚠️ 未找到作动器设备：%1").arg(serialNumber));
    }
}

/**
 * @brief 生成硬件节点详细信息
 */
QString CMyMainWindow::GenerateHardwareNodeDetailedInfo(const QString& nodeName) {
    QString info;
    info += QString(u8"🖥️ 硬件节点详细信息\n");
    info += QString(u8"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n");
    info += QString(u8"节点名称: %1\n").arg(nodeName);

    if (hardwareNodeResDataManager_) {
        auto nodeConfigs = hardwareNodeResDataManager_->getAllHardwareNodeConfigs();
        for (const auto& nodeConfig : nodeConfigs) {
            if (nodeConfig.nodeName == nodeName) {
                info += QString(u8"通道数量: %1\n").arg(nodeConfig.channelCount);
                info += QString(u8"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n");
                info += QString(u8"通道详细信息:\n");
                
                for (int i = 0; i < nodeConfig.channels.size(); ++i) {
                    const auto& channel = nodeConfig.channels[i];
                    info += QString(u8"  通道%1:\n").arg(channel.channelId);
                    info += QString(u8"    IP地址: %1\n").arg(channel.ipAddress);
                    info += QString(u8"    端口: %1\n").arg(channel.port);
                    if (i < nodeConfig.channels.size() - 1) {
                        info += QString(u8"  ────────────────────\n");
                    }
                }
                info += QString(u8"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
                return info;
            }
        }
    }

    info += QString(u8"状态: 未找到节点信息\n");
    info += QString(u8"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
    return info;
}

// ============================================================================
// 🆕 新增：项目状态管理实现
// ============================================================================

/**
 * @brief 更新操作区域的启用/禁用状态
 * @param hasProject 是否有活动项目
 */
void CMyMainWindow::updateOperationAreaState(bool hasProject) {
    AddLogEntry("DEBUG", QString(u8"🔄 更新操作区域状态：%1").arg(hasProject ? u8"启用" : u8"禁用"));
    
    // 更新状态变量
    hasActiveProject_ = hasProject;
    
    // 设置操作区域控件的启用状态
    setOperationControlsEnabled(hasProject);
    
    // 显示项目状态提示信息
    showProjectStatusMessage(hasProject);
}

/**
 * @brief 显示项目状态提示信息
 * @param hasProject 是否有活动项目
 */
void CMyMainWindow::showProjectStatusMessage(bool hasProject) {
    if (!hasProject) {
        // 显示"没有项目"提示
        if (statusBar()) {
            statusBar()->showMessage(u8"⚠️ 没有项目，请新建项目或打开项目", 0);
        }
        AddLogEntry("INFO", QString(u8"📋 显示状态：没有活动项目"));
    } else {
        // 显示项目就绪状态
        QString statusMessage = QString(u8"✅ 项目已就绪：%1").arg(currentProjectName_);
        if (statusBar()) {
            statusBar()->showMessage(statusMessage, 0);
        }
        AddLogEntry("INFO", QString(u8"📋 显示状态：项目已就绪 - %1").arg(currentProjectName_));
    }
}

/**
 * @brief 设置操作区域控件的启用状态
 * @param enabled 是否启用
 */
void CMyMainWindow::setOperationControlsEnabled(bool enabled) {
    AddLogEntry("DEBUG", QString(u8"🎛️ 设置操作控件状态：%1").arg(enabled ? u8"启用" : u8"禁用"));
    
    // 🔧 核心功能：控制树形控件的启用状态
    if (ui->hardwareTreeWidget) {
        ui->hardwareTreeWidget->setEnabled(enabled);
        AddLogEntry("DEBUG", QString(u8"硬件树形控件已%1").arg(enabled ? u8"启用" : u8"禁用"));
    }
    
    if (ui->testConfigTreeWidget) {
        ui->testConfigTreeWidget->setEnabled(enabled);
        AddLogEntry("DEBUG", QString(u8"试验配置树形控件已%1").arg(enabled ? u8"启用" : u8"禁用"));
    }
    
    // 🔧 扩展功能：控制其他相关控件（如果存在的话）
    // 注意：这些控件可能在UI文件中不存在，所以需要检查指针
    
    // 工具栏和按钮控件
    if (ui->actionNewProject) ui->actionNewProject->setEnabled(true);  // 新建项目始终可用
    if (ui->actionOpenProject) ui->actionOpenProject->setEnabled(true); // 打开项目始终可用
    if (ui->actionSaveProject) ui->actionSaveProject->setEnabled(enabled);
    if (ui->actionSaveAsProject) ui->actionSaveAsProject->setEnabled(enabled);
    
    // 🔧 扩展功能：控制其他相关控件（当前UI中暂未定义，预留接口）
    // 注意：以下控件在当前UI文件中不存在，使用findChild安全查找
    
    // 硬件相关菜单项（预留）
    QAction* connectHardwareAction = findChild<QAction*>("actionConnectHardware");
    if (connectHardwareAction) connectHardwareAction->setEnabled(enabled);
    
    QAction* startTestAction = findChild<QAction*>("actionStartTest");
    if (startTestAction) startTestAction->setEnabled(enabled);
    
    QAction* dataExportAction = findChild<QAction*>("actionDataExport");
    if (dataExportAction) dataExportAction->setEnabled(enabled);
    
    // 可能存在的其他按钮控件（预留）
    QPushButton* startDataButton = findChild<QPushButton*>("startDataButton");
    if (startDataButton) startDataButton->setEnabled(enabled);
    
    QPushButton* stopDataButton = findChild<QPushButton*>("stopDataButton");
    if (stopDataButton) stopDataButton->setEnabled(enabled);
    
    QPushButton* exportDataButton = findChild<QPushButton*>("exportDataButton");
    if (exportDataButton) exportDataButton->setEnabled(enabled);
    
    AddLogEntry("DEBUG", QString(u8"✅ 操作控件状态设置完成"));
}

/**
 * @brief 检查当前是否有活动项目
 * @return 有活动项目返回true
 */
bool CMyMainWindow::hasActiveProject() const {
    return hasActiveProject_;
}

/**
 * @brief 创建作动器组UI节点（参考传感器组的实现方式）
 * @param groupName 作动器组名称
 * @param groupId 作动器组ID
 */
void CMyMainWindow::CreateActuatorGroupUI(const QString& groupName, int groupId) {
    if (!ui->hardwareTreeWidget) {
        AddLogEntry("ERROR", QString(u8"硬件树控件为空，无法创建作动器组UI"));
        return;
    }

    AddLogEntry("INFO", QString(u8"🎨 开始创建作动器组UI节点：组名='%1', 组ID=%2").arg(groupName).arg(groupId));

    // 获取任务1根节点
    QTreeWidgetItem* taskRoot = ui->hardwareTreeWidget->topLevelItem(0);
    if (!taskRoot) {
        AddLogEntry("ERROR", QString(u8"无法获取硬件树根节点"));
        return;
    }

    // 获取作动器子节点（第0个子节点，索引为0）
    QTreeWidgetItem* actuatorRoot = taskRoot->child(0);
    if (!actuatorRoot) {
        AddLogEntry("ERROR", QString(u8"无法获取作动器根节点"));
        return;
    }

    // 🔍 检查作动器组是否已存在，避免重复创建
    for (int i = 0; i < actuatorRoot->childCount(); ++i) {
        QTreeWidgetItem* existingGroup = actuatorRoot->child(i);
        if (existingGroup && existingGroup->text(0) == groupName) {
            AddLogEntry("WARNING", QString(u8"作动器组 '%1' 已存在，跳过创建").arg(groupName));
            // 确保现有组处于展开状态
            existingGroup->setExpanded(true);
            return;
        }
    }

    // 🆕 参考传感器组：直接创建作动器组节点
    QTreeWidgetItem* groupItem = new QTreeWidgetItem(actuatorRoot);
    groupItem->setText(0, groupName);
    groupItem->setData(0, Qt::UserRole, "作动器组"); // 设置类型为作动器组
    groupItem->setData(1, Qt::UserRole, groupId);    // 存储组ID到第1列
    groupItem->setExpanded(true); // 🎯 关键：新创建的组自动展开

    // 🆕 新增：为作动器组添加提示信息
    groupItem->setToolTip(0, QString(u8"作动器组: %1 (ID: %2)\n管理同类型的作动器设备\n当前状态: 空组，等待添加作动器\n右键可添加作动器设备\n包含液压、气动等各类作动器").arg(groupName).arg(groupId));

    // 🎯 关键：确保父节点也处于展开状态，但不影响其他节点
    taskRoot->setExpanded(true);
    actuatorRoot->setExpanded(true);

    AddLogEntry("INFO", QString(u8"✅ 作动器组UI节点创建完成：%1").arg(groupName));
}

/**
 * @brief 根据传感器序列号获取所有包含该序列号的传感器组名
 * @param serialNumber 传感器序列号
 * @return 包含该序列号的所有组名列表
 */
QStringList CMyMainWindow::GetSensorGroupNamesBySerialNumber(const QString& serialNumber) const {
    QStringList groupNames;
    
    if (!sensorDataManager_) {
        return groupNames;
    }
    
    // 遍历所有传感器组，查找包含该序列号的组
    QList<UI::SensorGroup_1_2> allGroups = sensorDataManager_->getAllSensorGroups();
    for (const auto& group : allGroups) {
        for (const auto& sensor : group.sensors) {
            if (sensor.params_sn == serialNumber) {
                groupNames.append(group.groupName);
                break; // 找到就跳出，避免同一组重复添加
            }
        }
    }
    
    return groupNames;
}

/*
 * @brief 根据传感器序列号获取传感器组名（返回第一个匹配的组名，兼容性函数）
 * @deprecated 不严谨！已注释掉。建议使用精准操作函数 GetSensorByGroupIdAndSerialNumber 或 GetSensorByGroupNameAndSerialNumber
 * @warning 此函数可能返回错误的组信息，因为同一序列号可能属于多个组
 */
/*
QString CMyMainWindow::GetSensorGroupNameBySerialNumber(const QString& serialNumber) const {
    QStringList groupNames = GetSensorGroupNamesBySerialNumber(serialNumber);
    return groupNames.isEmpty() ? QString() : groupNames.first();
}
*/

/**
 * @brief 根据作动器序列号获取所有包含该序列号的作动器组名
 * @param serialNumber 作动器序列号
 * @return 包含该序列号的所有组名列表
 */
QStringList CMyMainWindow::GetActuatorGroupNamesBySerialNumber(const QString& serialNumber) const {
    QStringList groupNames;
    
    if (!actuatorViewModel1_2_) {
        return groupNames;
    }
    
    // 获取所有作动器组
    auto groups = actuatorViewModel1_2_->getAllActuatorGroups();
    
    for (const auto& group : groups) {
        for (const auto& actuator : group.actuators) {
            if (actuator.params.sn == serialNumber) {
                groupNames.append(group.groupName);
                break; // 找到就跳出，避免同一组重复添加
            }
        }
    }
    
    return groupNames;
}

/*
 * @brief 根据作动器序列号获取作动器组名（返回第一个匹配的组名，兼容性函数）
 * @deprecated 不严谨！已注释掉。建议使用精准操作函数 GetActuatorByGroupIdAndSerialNumber 或 GetActuatorByGroupNameAndSerialNumber
 * @warning 此函数可能返回错误的组信息，因为同一序列号可能属于多个组
 */
/*
QString CMyMainWindow::GetActuatorGroupNameBySerialNumber(const QString& serialNumber) const {
    QStringList groupNames = GetActuatorGroupNamesBySerialNumber(serialNumber);
    return groupNames.isEmpty() ? QString() : groupNames.first();
}
*/

// ===============================
// 🔍 精准操作函数实现（推荐使用）
// ===============================

/**
 * @brief 通过组ID和序列号精准获取传感器信息
 */
UI::SensorParams_1_2 CMyMainWindow::GetSensorByGroupIdAndSerialNumber(int groupId, const QString& serialNumber) const {
    UI::SensorParams_1_2 emptyParams;
    
    if (!sensorDataManager_) {
        AddLogEntry("ERROR", QString(u8"❌ 传感器数据管理器未初始化"));
        return emptyParams;
    }
    
    if (groupId <= 0 || serialNumber.isEmpty()) {
        AddLogEntry("ERROR", QString(u8"❌ 无效的组ID(%1)或序列号(%2)").arg(groupId).arg(serialNumber));
        return emptyParams;
    }
    
    // 首先检查组是否存在
    if (!sensorDataManager_->hasSensorGroup(groupId)) {
        AddLogEntry("ERROR", QString(u8"❌ 传感器组不存在: GroupID=%1").arg(groupId));
        return emptyParams;
    }
    
    // 获取组中的传感器
    UI::SensorGroup_1_2 group = sensorDataManager_->getSensorGroup(groupId);
    for (const auto& sensor : group.sensors) {
        if (sensor.params_sn == serialNumber) {
            AddLogEntry("INFO", QString(u8"✅ 精准找到传感器: GroupID=%1, SN=%2").arg(groupId).arg(serialNumber));
            return sensor;
        }
    }
    
    AddLogEntry("WARNING", QString(u8"⚠️ 在组 %1 中未找到传感器 %2").arg(groupId).arg(serialNumber));
    return emptyParams;
}

/**
 * @brief 通过组名和序列号精准获取传感器信息
 */
UI::SensorParams_1_2 CMyMainWindow::GetSensorByGroupNameAndSerialNumber(const QString& groupName, const QString& serialNumber) const {
    UI::SensorParams_1_2 emptyParams;
    
    if (!sensorDataManager_) {
        AddLogEntry("ERROR", QString(u8"❌ 传感器数据管理器未初始化"));
        return emptyParams;
    }
    
    if (groupName.isEmpty() || serialNumber.isEmpty()) {
        AddLogEntry("ERROR", QString(u8"❌ 无效的组名(%1)或序列号(%2)").arg(groupName).arg(serialNumber));
        return emptyParams;
    }
    
    // 通过组名查找组ID
    QList<UI::SensorGroup_1_2> allGroups = sensorDataManager_->getAllSensorGroups();
    for (const auto& group : allGroups) {
        if (group.groupName == groupName) {
            // 在找到的组中查找传感器
            for (const auto& sensor : group.sensors) {
                if (sensor.params_sn == serialNumber) {
                    AddLogEntry("INFO", QString(u8"✅ 精准找到传感器: GroupName=%1, SN=%2").arg(groupName).arg(serialNumber));
                    return sensor;
                }
            }
            AddLogEntry("WARNING", QString(u8"⚠️ 在组 %1 中未找到传感器 %2").arg(groupName).arg(serialNumber));
            return emptyParams;
        }
    }
    
    AddLogEntry("ERROR", QString(u8"❌ 传感器组不存在: %1").arg(groupName));
    return emptyParams;
}

/**
 * @brief 通过组ID和序列号精准删除传感器
 */
bool CMyMainWindow::DeleteSensorByGroupIdAndSerialNumber(int groupId, const QString& serialNumber) {
    if (!sensorDataManager_) {
        AddLogEntry("ERROR", QString(u8"❌ 传感器数据管理器未初始化"));
        return false;
    }
    
    if (groupId <= 0 || serialNumber.isEmpty()) {
        AddLogEntry("ERROR", QString(u8"❌ 无效的组ID(%1)或序列号(%2)").arg(groupId).arg(serialNumber));
        return false;
    }
    
    // 验证传感器存在
    UI::SensorParams_1_2 sensor = GetSensorByGroupIdAndSerialNumber(groupId, serialNumber);
    if (sensor.params_sn.isEmpty()) {
        AddLogEntry("ERROR", QString(u8"❌ 传感器不存在: GroupID=%1, SN=%2").arg(groupId).arg(serialNumber));
        return false;
    }
    
    // 获取组名用于日志和关联清理
    QString groupName;
    if (sensorDataManager_->hasSensorGroup(groupId)) {
        UI::SensorGroup_1_2 group = sensorDataManager_->getSensorGroup(groupId);
        groupName = group.groupName;
    }
    
    // 执行删除
    if (sensorDataManager_->removeSensorDetailedParamsInGroup(groupId, serialNumber)) {
        AddLogEntry("SUCCESS", QString(u8"✅ 精准删除传感器成功: GroupID=%1, GroupName=%2, SN=%3").arg(groupId).arg(groupName).arg(serialNumber));
        
        // 🆕 新增：删除后验证数据一致性
        validateSensorStorageConsistency();
        
        // 精准清理控制通道关联
        ClearControlChannelAssociationsAfterSensorDeletePrecise(groupId, groupName, serialNumber);
        
        // 更新UI
        RefreshHardwareTreeFromDataManagers();
        UpdateAllTreeWidgetTooltips();
        
        return true;
    } else {
        AddLogEntry("ERROR", QString(u8"❌ 精准删除传感器失败: %1").arg(sensorDataManager_->getLastError()));
        return false;
    }
}

/**
 * @brief 通过组名和序列号精准删除传感器
 */
bool CMyMainWindow::DeleteSensorByGroupNameAndSerialNumber(const QString& groupName, const QString& serialNumber) {
    if (!sensorDataManager_) {
        AddLogEntry("ERROR", QString(u8"❌ 传感器数据管理器未初始化"));
        return false;
    }
    
    if (groupName.isEmpty() || serialNumber.isEmpty()) {
        AddLogEntry("ERROR", QString(u8"❌ 无效的组名(%1)或序列号(%2)").arg(groupName).arg(serialNumber));
        return false;
    }
    
    // 通过组名查找组ID
    QList<UI::SensorGroup_1_2> allGroups = sensorDataManager_->getAllSensorGroups();
    for (const auto& group : allGroups) {
        if (group.groupName == groupName) {
            return DeleteSensorByGroupIdAndSerialNumber(group.groupId, serialNumber);
        }
    }
    
    AddLogEntry("ERROR", QString(u8"❌ 传感器组不存在: %1").arg(groupName));
    return false;
}

/**
 * @brief 通过组ID和序列号精准获取作动器信息
 */
UI::ActuatorParams_1_2 CMyMainWindow::GetActuatorByGroupIdAndSerialNumber(int groupId, const QString& serialNumber) const {
    UI::ActuatorParams_1_2 emptyParams;
    
    if (!actuatorViewModel1_2_) {
        AddLogEntry("ERROR", QString(u8"❌ 作动器视图模型未初始化"));
        return emptyParams;
    }
    
    if (groupId <= 0 || serialNumber.isEmpty()) {
        AddLogEntry("ERROR", QString(u8"❌ 无效的组ID(%1)或序列号(%2)").arg(groupId).arg(serialNumber));
        return emptyParams;
    }
    
    // 直接使用视图模型的精准查询
    UI::ActuatorParams_1_2 actuator = actuatorViewModel1_2_->getActuator(serialNumber, groupId);
    if (!actuator.params.sn.isEmpty()) {
        AddLogEntry("INFO", QString(u8"✅ 精准找到作动器: GroupID=%1, SN=%2").arg(groupId).arg(serialNumber));
        return actuator;
    }
    
    AddLogEntry("WARNING", QString(u8"⚠️ 在组 %1 中未找到作动器 %2").arg(groupId).arg(serialNumber));
    return emptyParams;
}

/**
 * @brief 通过组名和序列号精准获取作动器信息
 */
UI::ActuatorParams_1_2 CMyMainWindow::GetActuatorByGroupNameAndSerialNumber(const QString& groupName, const QString& serialNumber) const {
    UI::ActuatorParams_1_2 emptyParams;
    
    if (!actuatorViewModel1_2_) {
        AddLogEntry("ERROR", QString(u8"❌ 作动器视图模型未初始化"));
        return emptyParams;
    }
    
    if (groupName.isEmpty() || serialNumber.isEmpty()) {
        AddLogEntry("ERROR", QString(u8"❌ 无效的组名(%1)或序列号(%2)").arg(groupName).arg(serialNumber));
        return emptyParams;
    }
    
    // 通过组名查找组ID
    int groupId = actuatorViewModel1_2_->extractGroupIdFromNameBusiness(groupName);
    if (groupId == -1) {
        AddLogEntry("ERROR", QString(u8"❌ 作动器组不存在: %1").arg(groupName));
        return emptyParams;
    }
    
    return GetActuatorByGroupIdAndSerialNumber(groupId, serialNumber);
}

/**
 * @brief 通过组ID和序列号精准删除作动器
 */
bool CMyMainWindow::DeleteActuatorByGroupIdAndSerialNumber(int groupId, const QString& serialNumber) {
    if (!actuatorViewModel1_2_) {
        AddLogEntry("ERROR", QString(u8"❌ 作动器视图模型未初始化"));
        return false;
    }
    
    if (groupId <= 0 || serialNumber.isEmpty()) {
        AddLogEntry("ERROR", QString(u8"❌ 无效的组ID(%1)或序列号(%2)").arg(groupId).arg(serialNumber));
        return false;
    }
    
    // 验证作动器存在并获取组名
    UI::ActuatorParams_1_2 actuator = GetActuatorByGroupIdAndSerialNumber(groupId, serialNumber);
    if (actuator.params.sn.isEmpty()) {
        AddLogEntry("ERROR", QString(u8"❌ 作动器不存在: GroupID=%1, SN=%2").arg(groupId).arg(serialNumber));
        return false;
    }
    
    // 获取组名
    QString groupName;
    if (actuatorViewModel1_2_->getDataManager() && actuatorViewModel1_2_->getDataManager()->hasActuatorGroup(groupId)) {
        UI::ActuatorGroup_1_2 group = actuatorViewModel1_2_->getDataManager()->getActuatorGroup(groupId);
        groupName = group.groupName;
    }
    
    // 执行删除
    if (actuatorViewModel1_2_->deleteActuatorDeviceBusiness(serialNumber)) {
        AddLogEntry("SUCCESS", QString(u8"✅ 精准删除作动器成功: GroupID=%1, GroupName=%2, SN=%3").arg(groupId).arg(groupName).arg(serialNumber));
        
        // 精准清理控制通道关联
        ClearControlChannelAssociationsAfterActuatorDeletePrecise(groupId, groupName, serialNumber);
        
        // 更新UI
        RefreshHardwareTreeFromDataManagers();
        UpdateAllTreeWidgetTooltips();
        
        return true;
    } else {
        AddLogEntry("ERROR", QString(u8"❌ 精准删除作动器失败: %1").arg(actuatorViewModel1_2_->getLastError()));
        return false;
    }
}

/**
 * @brief 通过组名和序列号精准删除作动器
 */
bool CMyMainWindow::DeleteActuatorByGroupNameAndSerialNumber(const QString& groupName, const QString& serialNumber) {
    if (!actuatorViewModel1_2_) {
        AddLogEntry("ERROR", QString(u8"❌ 作动器视图模型未初始化"));
        return false;
    }
    
    if (groupName.isEmpty() || serialNumber.isEmpty()) {
        AddLogEntry("ERROR", QString(u8"❌ 无效的组名(%1)或序列号(%2)").arg(groupName).arg(serialNumber));
        return false;
    }
    
    // 通过组名查找组ID
    int groupId = actuatorViewModel1_2_->extractGroupIdFromNameBusiness(groupName);
    if (groupId == -1) {
        AddLogEntry("ERROR", QString(u8"❌ 作动器组不存在: %1").arg(groupName));
        return false;
    }
    
    return DeleteActuatorByGroupIdAndSerialNumber(groupId, serialNumber);
}

/**
 * @brief 精准更新控制通道中的传感器关联信息
 */
void CMyMainWindow::UpdateControlChannelAssociationsAfterSensorEditPrecise(int groupId, const QString& groupName, const QString& serialNumber, const UI::SensorParams_1_2& updatedParams) {
    if (!ctrlChanDataManager_ || !ui->testConfigTreeWidget) {
        return;
    }
    
    AddLogEntry("INFO", QString(u8"🔄 精准更新控制通道关联信息：传感器编辑 - GroupID=%1, GroupName=%2, SN=%3").arg(groupId).arg(groupName).arg(serialNumber));
    
    // 获取所有控制通道组
    auto groups = ctrlChanDataManager_->getAllControlChannelGroups();
    bool hasUpdates = false;
    
    // 构建精准的关联字符串：组名 - 序列号
    QString preciseAssociation = QString(u8"%1 - %2").arg(groupName).arg(serialNumber);
    
    for (auto& group : groups) {
        for (auto& channel : group.channels) {
            bool channelUpdated = false;
            
            // 检查并更新载荷1传感器关联（精准匹配：组名+序列号）
            if (IsExactSensorMatch(channel.load1Sensor, serialNumber, groupName)) {
                channel.load1Sensor = preciseAssociation.toStdString();
                channelUpdated = true;
                AddLogEntry("INFO", QString(u8"✅ 精准更新CH%1载荷1关联: %2").arg(QString::fromStdString(channel.channelName)).arg(preciseAssociation));
            }
            
            // 检查并更新载荷2传感器关联（精准匹配：组名+序列号）
            if (IsExactSensorMatch(channel.load2Sensor, serialNumber, groupName)) {
                channel.load2Sensor = preciseAssociation.toStdString();
                channelUpdated = true;
                AddLogEntry("INFO", QString(u8"✅ 精准更新CH%1载荷2关联: %2").arg(QString::fromStdString(channel.channelName)).arg(preciseAssociation));
            }
            
            // 检查并更新位置传感器关联（精准匹配：组名+序列号）
            if (IsExactSensorMatch(channel.positionSensor, serialNumber, groupName)) {
                channel.positionSensor = preciseAssociation.toStdString();
                channelUpdated = true;
                AddLogEntry("INFO", QString(u8"✅ 精准更新CH%1位置关联: %2").arg(QString::fromStdString(channel.channelName)).arg(preciseAssociation));
            }
            
            if (channelUpdated) {
                hasUpdates = true;
            }
        }
        
        // 如果有更新，保存到数据管理器
        if (hasUpdates) {
            ctrlChanDataManager_->updateControlChannelGroup(group);
        }
    }
    
    // 更新UI显示
    if (hasUpdates) {
        // 🆕 新增：同步数据管理器的关联信息到界面显示
        SynchronizeControlChannelAssociationsToUI();
        UpdateAllTreeWidgetTooltips();
        AddLogEntry("SUCCESS", QString(u8"✅ 精准控制通道关联信息更新完成"));
    } else {
        AddLogEntry("INFO", QString(u8"ℹ️ 无需更新控制通道关联信息"));
    }
}

/**
 * @brief 精准更新控制通道中的作动器关联信息
 */
void CMyMainWindow::UpdateControlChannelAssociationsAfterActuatorEditPrecise(int groupId, const QString& groupName, const QString& serialNumber, const UI::ActuatorParams_1_2& updatedParams) {
    if (!ctrlChanDataManager_ || !ui->testConfigTreeWidget) {
        return;
    }
    
    AddLogEntry("INFO", QString(u8"🔄 精准更新控制通道关联信息：作动器编辑 - GroupID=%1, GroupName=%2, SN=%3").arg(groupId).arg(groupName).arg(serialNumber));
    
    // 获取所有控制通道组
    auto groups = ctrlChanDataManager_->getAllControlChannelGroups();
    bool hasUpdates = false;
    
    // 构建精准的关联字符串：组名 - 序列号
    QString preciseAssociation = QString(u8"%1 - %2").arg(groupName).arg(serialNumber);
    
    for (auto& group : groups) {
        for (auto& channel : group.channels) {
            // 检查并更新控制作动器关联（精准匹配：组名+序列号）
            if (IsExactActuatorMatch(channel.controlActuator, serialNumber, groupName)) {
                channel.controlActuator = preciseAssociation.toStdString();
                hasUpdates = true;
                AddLogEntry("INFO", QString(u8"✅ 精准更新CH%1控制关联: %2").arg(QString::fromStdString(channel.channelName)).arg(preciseAssociation));
            }
        }
        
        // 如果有更新，保存到数据管理器
        if (hasUpdates) {
            ctrlChanDataManager_->updateControlChannelGroup(group);
        }
    }
    
    // 更新UI显示
    if (hasUpdates) {
        // 🆕 新增：同步数据管理器的关联信息到界面显示
        SynchronizeControlChannelAssociationsToUI();
        UpdateAllTreeWidgetTooltips();
        AddLogEntry("SUCCESS", QString(u8"✅ 精准控制通道关联信息更新完成"));
    } else {
        AddLogEntry("INFO", QString(u8"ℹ️ 无需更新控制通道关联信息"));
    }
}

/**
 * @brief 精准清理控制通道中的传感器关联信息
 */
void CMyMainWindow::ClearControlChannelAssociationsAfterSensorDeletePrecise(int groupId, const QString& groupName, const QString& serialNumber) {
    if (!ctrlChanDataManager_ || !ui->testConfigTreeWidget) {
        return;
    }
    
    AddLogEntry("INFO", QString(u8"🔄 精准清理控制通道关联信息：传感器删除 - GroupID=%1, GroupName=%2, SN=%3").arg(groupId).arg(groupName).arg(serialNumber));
    
    // 获取所有控制通道组
    auto groups = ctrlChanDataManager_->getAllControlChannelGroups();
    bool hasUpdates = false;
    int clearedCount = 0;
    
    for (auto& group : groups) {
        for (auto& channel : group.channels) {
            bool channelUpdated = false;
            
            // 精准清除载荷1传感器关联（组名+序列号）
            if (IsExactSensorMatch(channel.load1Sensor, serialNumber, groupName)) {
                QString oldAssociation = QString::fromStdString(channel.load1Sensor);
                channel.load1Sensor = "";
                channelUpdated = true;
                clearedCount++;
                AddLogEntry("INFO", QString(u8"✅ 精准清除CH%1载荷1关联: %2").arg(QString::fromStdString(channel.channelName)).arg(oldAssociation));
            }
            
            // 精准清除载荷2传感器关联（组名+序列号）
            if (IsExactSensorMatch(channel.load2Sensor, serialNumber, groupName)) {
                QString oldAssociation = QString::fromStdString(channel.load2Sensor);
                channel.load2Sensor = "";
                channelUpdated = true;
                clearedCount++;
                AddLogEntry("INFO", QString(u8"✅ 精准清除CH%1载荷2关联: %2").arg(QString::fromStdString(channel.channelName)).arg(oldAssociation));
            }
            
            // 精准清除位置传感器关联（组名+序列号）
            if (IsExactSensorMatch(channel.positionSensor, serialNumber, groupName)) {
                QString oldAssociation = QString::fromStdString(channel.positionSensor);
                channel.positionSensor = "";
                channelUpdated = true;
                clearedCount++;
                AddLogEntry("INFO", QString(u8"✅ 精准清除CH%1位置关联: %2").arg(QString::fromStdString(channel.channelName)).arg(oldAssociation));
            }
            
            if (channelUpdated) {
                hasUpdates = true;
            }
        }
        
        // 如果有更新，保存到数据管理器
        if (hasUpdates) {
            ctrlChanDataManager_->updateControlChannelGroup(group);
        }
    }
    
    // 更新UI显示
    if (hasUpdates) {
        UpdateAllTreeWidgetTooltips();
        AddLogEntry("SUCCESS", QString(u8"✅ 精准控制通道关联信息清理完成：共清理 %1 个关联").arg(clearedCount));
    } else {
        AddLogEntry("INFO", QString(u8"ℹ️ 无需清理控制通道关联信息"));
    }
}

/**
 * @brief 精准清理控制通道中的作动器关联信息
 */
void CMyMainWindow::ClearControlChannelAssociationsAfterActuatorDeletePrecise(int groupId, const QString& groupName, const QString& serialNumber) {
    if (!ctrlChanDataManager_ || !ui->testConfigTreeWidget) {
        return;
    }
    
    AddLogEntry("INFO", QString(u8"🔄 精准清理控制通道关联信息：作动器删除 - GroupID=%1, GroupName=%2, SN=%3").arg(groupId).arg(groupName).arg(serialNumber));
    
    // 获取所有控制通道组
    auto groups = ctrlChanDataManager_->getAllControlChannelGroups();
    bool hasUpdates = false;
    int clearedCount = 0;
    
    for (auto& group : groups) {
        for (auto& channel : group.channels) {
            // 精准清除控制作动器关联（组名+序列号）
            if (IsExactActuatorMatch(channel.controlActuator, serialNumber, groupName)) {
                QString oldAssociation = QString::fromStdString(channel.controlActuator);
                channel.controlActuator = "";
                hasUpdates = true;
                clearedCount++;
                AddLogEntry("INFO", QString(u8"✅ 精准清除CH%1控制关联: %2").arg(QString::fromStdString(channel.channelName)).arg(oldAssociation));
            }
        }
        
        // 如果有更新，保存到数据管理器
        if (hasUpdates) {
            ctrlChanDataManager_->updateControlChannelGroup(group);
        }
    }
    
    // 更新UI显示
    if (hasUpdates) {
        UpdateAllTreeWidgetTooltips();
        AddLogEntry("SUCCESS", QString(u8"✅ 精准控制通道关联信息清理完成：共清理 %1 个关联").arg(clearedCount));
    } else {
        AddLogEntry("INFO", QString(u8"ℹ️ 无需清理控制通道关联信息"));
    }
}

/**
 * @brief 传感器编辑后更新控制通道关联信息
 */
void CMyMainWindow::UpdateControlChannelAssociationsAfterSensorEdit(const QString& serialNumber, const UI::SensorParams_1_2& updatedParams) {
    if (!ctrlChanDataManager_ || !ui->testConfigTreeWidget) {
        return;
    }
    
    AddLogEntry("INFO", QString(u8"🔄 更新控制通道关联信息：传感器编辑 - %1").arg(serialNumber));
    
    // 获取传感器的所有组名
    QStringList groupNames = GetSensorGroupNamesBySerialNumber(serialNumber);
    if (groupNames.isEmpty()) {
        AddLogEntry("WARNING", QString(u8"⚠️ 无法找到传感器 %1 的组名").arg(serialNumber));
        return;
    }
    
    // 如果传感器属于多个组，记录警告信息
    if (groupNames.size() > 1) {
        AddLogEntry("WARNING", QString(u8"⚠️ 传感器 %1 属于多个组: %2").arg(serialNumber).arg(groupNames.join(", ")));
    }
    
    // 获取所有控制通道组
    auto groups = ctrlChanDataManager_->getAllControlChannelGroups();
    bool hasUpdates = false;
    
    for (auto& group : groups) {
        for (auto& channel : group.channels) {
            bool channelUpdated = false;
            
            // 检查并更新载荷1传感器关联
            if (QString::fromStdString(channel.load1Sensor).contains(serialNumber)) {
                // 如果传感器属于多个组，使用所有组名
                QString newAssociation;
                if (groupNames.size() == 1) {
                    newAssociation = QString(u8"%1组 - %2").arg(groupNames.first()).arg(serialNumber);
                } else {
                    newAssociation = QString(u8"%1组 - %2").arg(groupNames.join("/")).arg(serialNumber);
                }
                channel.load1Sensor = newAssociation.toStdString();
                channelUpdated = true;
                AddLogEntry("INFO", QString(u8"✅ 更新CH%1载荷1关联: %2").arg(QString::fromStdString(channel.channelName)).arg(newAssociation));
            }
            
            // 检查并更新载荷2传感器关联
            if (QString::fromStdString(channel.load2Sensor).contains(serialNumber)) {
                QString newAssociation;
                if (groupNames.size() == 1) {
                    newAssociation = QString(u8"%1组 - %2").arg(groupNames.first()).arg(serialNumber);
                } else {
                    newAssociation = QString(u8"%1组 - %2").arg(groupNames.join("/")).arg(serialNumber);
                }
                channel.load2Sensor = newAssociation.toStdString();
                channelUpdated = true;
                AddLogEntry("INFO", QString(u8"✅ 更新CH%1载荷2关联: %2").arg(QString::fromStdString(channel.channelName)).arg(newAssociation));
            }
            
            // 检查并更新位置传感器关联
            if (QString::fromStdString(channel.positionSensor).contains(serialNumber)) {
                QString newAssociation;
                if (groupNames.size() == 1) {
                    newAssociation = QString(u8"%1组 - %2").arg(groupNames.first()).arg(serialNumber);
                } else {
                    newAssociation = QString(u8"%1组 - %2").arg(groupNames.join("/")).arg(serialNumber);
                }
                channel.positionSensor = newAssociation.toStdString();
                channelUpdated = true;
                AddLogEntry("INFO", QString(u8"✅ 更新CH%1位置关联: %2").arg(QString::fromStdString(channel.channelName)).arg(newAssociation));
            }
            
            if (channelUpdated) {
                hasUpdates = true;
            }
        }
        
        // 如果有更新，保存到数据管理器
        if (hasUpdates) {
            ctrlChanDataManager_->updateControlChannelGroup(group);
        }
    }
    
    // 更新UI显示
    if (hasUpdates) {
        // 🆕 新增：同步数据管理器的关联信息到界面显示
        SynchronizeControlChannelAssociationsToUI();
        // 使用现有的UI更新机制
        UpdateAllTreeWidgetTooltips();
        AddLogEntry("SUCCESS", QString(u8"✅ 控制通道关联信息更新完成"));
    } else {
        AddLogEntry("INFO", QString(u8"ℹ️ 无需更新控制通道关联信息"));
    }
}

/**
 * @brief 传感器删除后更新控制通道关联信息（增强版，支持组名验证）
 */
void CMyMainWindow::UpdateControlChannelAssociationsAfterSensorDelete(const QString& serialNumber, const QString& groupName) {
    if (!ctrlChanDataManager_ || !ui->testConfigTreeWidget) {
        return;
    }
    
    AddLogEntry("INFO", QString(u8"🔄 更新控制通道关联信息：传感器删除 - %1组-%2").arg(groupName).arg(serialNumber));
    
    // 获取所有控制通道组
    auto groups = ctrlChanDataManager_->getAllControlChannelGroups();
    bool hasUpdates = false;
    int clearedCount = 0;
    
    for (auto& group : groups) {
        for (auto& channel : group.channels) {
            bool channelUpdated = false;
            
            // 🆕 增强：使用精确匹配（组名+序列号）清除载荷1传感器关联
            if (IsExactSensorMatch(channel.load1Sensor, serialNumber, groupName)) {
                QString oldAssociation = QString::fromStdString(channel.load1Sensor);
                channel.load1Sensor = "";
                channelUpdated = true;
                clearedCount++;
                AddLogEntry("INFO", QString(u8"✅ 精确清除CH%1载荷1关联: %2").arg(QString::fromStdString(channel.channelName)).arg(oldAssociation));
            }
            
            // 🆕 增强：使用精确匹配（组名+序列号）清除载荷2传感器关联
            if (IsExactSensorMatch(channel.load2Sensor, serialNumber, groupName)) {
                QString oldAssociation = QString::fromStdString(channel.load2Sensor);
                channel.load2Sensor = "";
                channelUpdated = true;
                clearedCount++;
                AddLogEntry("INFO", QString(u8"✅ 精确清除CH%1载荷2关联: %2").arg(QString::fromStdString(channel.channelName)).arg(oldAssociation));
            }
            
            // 🆕 增强：使用精确匹配（组名+序列号）清除位置传感器关联
            if (IsExactSensorMatch(channel.positionSensor, serialNumber, groupName)) {
                QString oldAssociation = QString::fromStdString(channel.positionSensor);
                channel.positionSensor = "";
                channelUpdated = true;
                clearedCount++;
                AddLogEntry("INFO", QString(u8"✅ 精确清除CH%1位置关联: %2").arg(QString::fromStdString(channel.channelName)).arg(oldAssociation));
            }
            
            if (channelUpdated) {
                hasUpdates = true;
            }
        }
        
        // 如果有更新，保存到数据管理器
        if (hasUpdates) {
            ctrlChanDataManager_->updateControlChannelGroup(group);
        }
    }
    
    // 更新UI显示
    if (hasUpdates) {
        // 使用现有的UI更新机制
        UpdateAllTreeWidgetTooltips();
        AddLogEntry("SUCCESS", QString(u8"✅ 控制通道关联信息精确清除完成：共清除 %1 个关联").arg(clearedCount));
    } else {
        AddLogEntry("INFO", QString(u8"ℹ️ 无需清除控制通道关联信息"));
    }
}

/**
 * @brief 作动器编辑后更新控制通道关联信息
 */
void CMyMainWindow::UpdateControlChannelAssociationsAfterActuatorEdit(const QString& serialNumber, const UI::ActuatorParams_1_2& updatedParams) {
    if (!ctrlChanDataManager_ || !ui->testConfigTreeWidget) {
        return;
    }
    
    AddLogEntry("INFO", QString(u8"🔄 更新控制通道关联信息：作动器编辑 - %1").arg(serialNumber));
    
    // 获取作动器的组信息（精准定位）
    QStringList groupNames = GetActuatorGroupNamesBySerialNumber(serialNumber);
    if (groupNames.isEmpty()) {
        AddLogEntry("WARNING", QString(u8"⚠️ 无法找到作动器 %1 的组名").arg(serialNumber));
        return;
    }
    
    // 获取所有控制通道组
    auto groups = ctrlChanDataManager_->getAllControlChannelGroups();
    bool hasUpdates = false;
    
    // 为每个组生成精准的关联字符串并更新
    for (const QString& groupName : groupNames) {
        QString preciseAssociation = QString(u8"%1 - %2").arg(groupName).arg(serialNumber);
        
        for (auto& group : groups) {
            for (auto& channel : group.channels) {
                // 🔧 修复：使用精准匹配，只更新特定组的作动器关联
                if (IsExactActuatorMatch(channel.controlActuator, serialNumber, groupName)) {
                    channel.controlActuator = preciseAssociation.toStdString();
                    hasUpdates = true;
                    AddLogEntry("INFO", QString(u8"✅ 精准更新CH%1控制关联: %2").arg(QString::fromStdString(channel.channelName)).arg(preciseAssociation));
                }
            }
            
            // 如果有更新，保存到数据管理器
            if (hasUpdates) {
                ctrlChanDataManager_->updateControlChannelGroup(group);
            }
        }
    }
    
    // 记录多组的警告信息
    if (groupNames.size() > 1) {
        AddLogEntry("WARNING", QString(u8"⚠️ 作动器 %1 属于多个组: %2").arg(serialNumber).arg(groupNames.join(", ")));
    }
    
    // 更新UI显示
    if (hasUpdates) {
        // 🆕 新增：同步数据管理器的关联信息到界面显示
        SynchronizeControlChannelAssociationsToUI();
        // 使用现有的UI更新机制
        UpdateAllTreeWidgetTooltips();
        AddLogEntry("SUCCESS", QString(u8"✅ 控制通道关联信息精准更新完成"));
    } else {
        AddLogEntry("INFO", QString(u8"ℹ️ 无需更新控制通道关联信息"));
    }
}

/**
 * @brief 作动器删除后更新控制通道关联信息
 */
void CMyMainWindow::UpdateControlChannelAssociationsAfterActuatorDelete(const QString& serialNumber) {
    if (!ctrlChanDataManager_ || !ui->testConfigTreeWidget) {
        return;
    }
    
    AddLogEntry("INFO", QString(u8"🔄 更新控制通道关联信息：作动器删除 - %1").arg(serialNumber));
    
    // 获取所有控制通道组
    auto groups = ctrlChanDataManager_->getAllControlChannelGroups();
    bool hasUpdates = false;
    
    for (auto& group : groups) {
        for (auto& channel : group.channels) {
            // 清除控制作动器关联
            if (QString::fromStdString(channel.controlActuator).contains(serialNumber)) {
                channel.controlActuator = "";
                hasUpdates = true;
                AddLogEntry("INFO", QString(u8"✅ 清除CH%1控制关联: %2").arg(QString::fromStdString(channel.channelName)).arg(serialNumber));
            }
        }
        
        // 如果有更新，保存到数据管理器
        if (hasUpdates) {
            ctrlChanDataManager_->updateControlChannelGroup(group);
        }
    }
    
    // 更新UI显示
    if (hasUpdates) {
        // 使用现有的UI更新机制
        UpdateAllTreeWidgetTooltips();
        AddLogEntry("SUCCESS", QString(u8"✅ 控制通道关联信息清除完成"));
    } else {
        AddLogEntry("INFO", QString(u8"ℹ️ 无需清除控制通道关联信息"));
    }
}

/**
 * @brief 作动器删除后更新控制通道关联信息（增强版，支持组名验证）
 */
void CMyMainWindow::UpdateControlChannelAssociationsAfterActuatorDelete(const QString& serialNumber, const QString& groupName) {
    if (!ctrlChanDataManager_ || !ui->testConfigTreeWidget) {
        return;
    }
    
    AddLogEntry("INFO", QString(u8"🔄 更新控制通道关联信息：作动器删除 - %1组-%2").arg(groupName).arg(serialNumber));
    
    // 获取所有控制通道组
    auto groups = ctrlChanDataManager_->getAllControlChannelGroups();
    bool hasUpdates = false;
    int clearedCount = 0;
    
    for (auto& group : groups) {
        for (auto& channel : group.channels) {
            // 使用精确匹配（组名+序列号）清除控制作动器关联
            if (IsExactActuatorMatch(channel.controlActuator, serialNumber, groupName)) {
                QString oldAssociation = QString::fromStdString(channel.controlActuator);
                channel.controlActuator = "";
                hasUpdates = true;
                clearedCount++;
                AddLogEntry("INFO", QString(u8"✅ 精确清除CH%1控制关联: %2").arg(QString::fromStdString(channel.channelName)).arg(oldAssociation));
            }
        }
        
        // 如果有更新，保存到数据管理器
        if (hasUpdates) {
            ctrlChanDataManager_->updateControlChannelGroup(group);
        }
    }
    
    // 更新UI显示
    if (hasUpdates) {
        // 使用现有的UI更新机制
        UpdateAllTreeWidgetTooltips();
        AddLogEntry("SUCCESS", QString(u8"✅ 控制通道关联信息精确清除完成：共清除 %1 个关联").arg(clearedCount));
    } else {
        AddLogEntry("INFO", QString(u8"ℹ️ 无需清除控制通道关联信息"));
    }
}

/**
 * @brief 硬件节点编辑后更新控制通道关联信息
 * @param oldNodeName 旧硬件节点名称
 * @param newNodeName 新硬件节点名称  
 * @param channels 更新后的通道信息
 * 
 * @details 智能更新逻辑：
 * - 处理节点名称变更：完全匹配并更新关联信息
 * - 处理通道数减少：清空多余通道的关联信息
 * - 处理通道数增加：保持新增通道为空白状态
 * - 确保UI与数据管理器保持同步
 */
void CMyMainWindow::UpdateControlChannelAssociationsAfterHardwareNodeEdit(const QString& oldNodeName, const QString& newNodeName, const QList<UI::ChannelInfo>& channels) {
    if (!ctrlChanDataManager_ || !ui->testConfigTreeWidget) {
        return;
    }
    
    AddLogEntry("INFO", QString(u8"🔄 更新控制通道关联信息：硬件节点编辑 - %1 → %2 (通道数: %3)")
               .arg(oldNodeName).arg(newNodeName).arg(channels.size()));
    
    // 获取所有控制通道组
    auto groups = ctrlChanDataManager_->getAllControlChannelGroups();
    bool hasUpdates = false;
    int updatedCount = 0;
    int clearedCount = 0;
    
    // 🆕 步骤1：构建当前有效的通道ID集合
    QSet<int> validChannelIds;
    for (const auto& channelInfo : channels) {
        validChannelIds.insert(channelInfo.channelId);
    }
    
    // 🆕 步骤2：构建精准匹配的关联信息模式（基于当前有效通道）
    QStringList oldAssociationPatterns;
    QStringList newAssociationPatterns;
    
    // 为每个有效通道构建"节点名称 - 通道名称"格式的关联信息  
    for (const auto& channelInfo : channels) {
        QString channelName = QString("CH%1").arg(channelInfo.channelId);
        QString oldPattern = QString("%1 - %2").arg(oldNodeName).arg(channelName);
        QString newPattern = QString("%1 - %2").arg(newNodeName).arg(channelName);
        oldAssociationPatterns.append(oldPattern);
        newAssociationPatterns.append(newPattern);
    }
    
    // 🆕 步骤3：遍历所有控制通道，智能处理关联信息
    for (auto& group : groups) {
        bool groupHasUpdates = false;
        
        for (auto& channel : group.channels) {
            QString currentAssociation = QString::fromStdString(channel.hardwareAssociation);
            
            if (!currentAssociation.isEmpty()) {
                // 检查是否与目标硬件节点相关（以"节点名称 - "开头）
                QString nodePrefix = QString("%1 - ").arg(oldNodeName);
                if (currentAssociation.startsWith(nodePrefix)) {
                    // 提取通道部分（如："LD-B1 - CH3" → "CH3"）
                    QString channelPart = currentAssociation.mid(nodePrefix.length());
                    
                    // 判断该通道是否仍然有效
                    bool isValidChannel = false;
                    for (int i = 0; i < oldAssociationPatterns.size(); ++i) {
                        if (currentAssociation == oldAssociationPatterns[i]) {
                            // 找到匹配的有效通道，更新关联信息
                            QString newAssociation = newAssociationPatterns[i];
                            if (newAssociation != currentAssociation) {
                                channel.hardwareAssociation = newAssociation.toStdString();
                                groupHasUpdates = true;
                                hasUpdates = true;
                                updatedCount++;
                                AddLogEntry("INFO", QString(u8"✅ 精准更新%1硬件关联: %2 → %3")
                                           .arg(QString::fromStdString(channel.channelName))
                                           .arg(currentAssociation)
                                           .arg(newAssociation));
                            }
                            isValidChannel = true;
                            break;
                        }
                    }
                    
                    // 🆕 如果该通道不再有效（通道数减少），清空关联信息
                    if (!isValidChannel) {
                        channel.hardwareAssociation = "";
                        groupHasUpdates = true;
                        hasUpdates = true;
                        clearedCount++;
                        AddLogEntry("INFO", QString(u8"🗑️ 清空无效通道关联: %1 (通道已删除)")
                                   .arg(currentAssociation));
                    }
                }
            }
        }
        
        // 如果组有更新，保存到数据管理器
        if (groupHasUpdates) {
            ctrlChanDataManager_->updateControlChannelGroup(group);
        }
    }
    
    // 🆕 步骤4：强制同步UI显示，确保界面与数据管理器一致
    SynchronizeControlChannelAssociationsToUI();
    UpdateAllTreeWidgetTooltips();
    
    // 记录处理结果
    if (hasUpdates) {
        QString resultMsg = QString(u8"✅ 控制通道关联信息处理完成");
        if (updatedCount > 0) {
            resultMsg += QString(u8"：更新 %1 个关联").arg(updatedCount);
        }
        if (clearedCount > 0) {
            resultMsg += QString(u8"，清空 %1 个无效关联").arg(clearedCount);
        }
        AddLogEntry("SUCCESS", resultMsg);
    } else {
        AddLogEntry("INFO", QString(u8"ℹ️ 无需更新控制通道关联信息"));
    }
}

/**
 * @brief 硬件节点删除后更新控制通道关联信息
 * @param nodeName 被删除的硬件节点名称
 * 
 * @details 精准匹配逻辑：
 * - 检查关联信息是否以"节点名称 - "开头
 * - 只清除完全匹配该节点的关联信息
 * - 避免误删除相似节点名称的关联（如：LD-B1 vs LD-B10）
 * - 清空匹配的硬件关联字段
 */
void CMyMainWindow::UpdateControlChannelAssociationsAfterHardwareNodeDelete(const QString& nodeName) {
    if (!ctrlChanDataManager_ || !ui->testConfigTreeWidget) {
        return;
    }
    
    AddLogEntry("INFO", QString(u8"🔄 更新控制通道关联信息：硬件节点删除 - %1").arg(nodeName));
    
    // 获取所有控制通道组
    auto groups = ctrlChanDataManager_->getAllControlChannelGroups();
    bool hasUpdates = false;
    int clearedCount = 0;
    
    // 遍历所有控制通道，精准查找和清除包含已删除硬件节点的关联
    for (auto& group : groups) {
        bool groupHasUpdates = false;
        
        for (auto& channel : group.channels) {
            QString currentAssociation = QString::fromStdString(channel.hardwareAssociation);
            
            // 精准匹配：检查关联信息是否以"节点名称 - "开头
            if (!currentAssociation.isEmpty()) {
                QString nodePattern = QString("%1 - ").arg(nodeName);
                
                // 精准匹配：关联信息必须以"节点名称 - "开头
                if (currentAssociation.startsWith(nodePattern)) {
                    QString oldAssociation = currentAssociation;
                    channel.hardwareAssociation = "";  // 清空硬件关联
                    groupHasUpdates = true;
                    hasUpdates = true;
                    clearedCount++;
                    AddLogEntry("INFO", QString(u8"✅ 精准清除CH%1硬件关联: %2")
                               .arg(QString::fromStdString(channel.channelName))
                               .arg(oldAssociation));
                }
            }
        }
        
        // 如果组有更新，保存到数据管理器
        if (groupHasUpdates) {
            ctrlChanDataManager_->updateControlChannelGroup(group);
        }
    }
    
    // 更新UI显示
    if (hasUpdates) {
        // 🆕 新增：同步数据管理器的关联信息到界面显示
        SynchronizeControlChannelAssociationsToUI();
        UpdateAllTreeWidgetTooltips();
        AddLogEntry("SUCCESS", QString(u8"✅ 控制通道硬件关联信息清除完成：共清除 %1 个关联").arg(clearedCount));
    } else {
        AddLogEntry("INFO", QString(u8"ℹ️ 无需清除控制通道硬件关联信息"));
    }
}

/**
 * @brief 同步控制通道关联信息从数据管理器到界面显示
 * @details 确保界面上的"关联信息"列与数据管理器中的数据一致
 */
void CMyMainWindow::SynchronizeControlChannelAssociationsToUI() {
    if (!ctrlChanDataManager_ || !ui->testConfigTreeWidget) {
        return;
    }
    
    AddLogEntry("DEBUG", QString(u8"🔄 开始同步控制通道关联信息到界面显示"));
    
    // 获取试验配置树的根节点
    QTreeWidgetItem* taskRoot = ui->testConfigTreeWidget->topLevelItem(0);
    if (!taskRoot) {
        AddLogEntry("WARNING", QString(u8"⚠️ 试验配置树根节点不存在"));
        return;
    }

    // 查找控制通道节点
    QTreeWidgetItem* controlChannelRoot = nullptr;
    for (int i = 0; i < taskRoot->childCount(); ++i) {
        QTreeWidgetItem* child = taskRoot->child(i);
        if (child && child->text(0) == tr("控制通道")) {
            controlChannelRoot = child;
            break;
        }
    }

    if (!controlChannelRoot) {
        AddLogEntry("WARNING", QString(u8"⚠️ 控制通道根节点不存在"));
        return;
    }

    // 获取数据管理器中的所有控制通道组
    auto groups = ctrlChanDataManager_->getAllControlChannelGroups();
    int syncedCount = 0;
    
    // 遍历界面上的控制通道节点
    for (int i = 0; i < controlChannelRoot->childCount(); ++i) {
        QTreeWidgetItem* channelItem = controlChannelRoot->child(i);
        if (!channelItem) continue;

        QString channelName = channelItem->text(0);
        
        // 在数据管理器中查找对应的通道数据
        for (const auto& group : groups) {
            for (const auto& channel : group.channels) {
                QString dataChannelName = QString::fromStdString(channel.channelName);
                
                if (dataChannelName == channelName) {
                    // 找到对应的通道，同步关联信息到界面
                    QString hardwareAssociation = QString::fromStdString(channel.hardwareAssociation);
                    
                    // 更新主通道节点的关联信息列
                    QString currentUIAssociation = channelItem->text(1);
                    if (currentUIAssociation != hardwareAssociation) {
                        channelItem->setText(1, hardwareAssociation);
                        syncedCount++;
                        AddLogEntry("DEBUG", QString(u8"🔄 同步%1关联信息: '%2' → '%3'")
                                   .arg(channelName)
                                   .arg(currentUIAssociation)
                                   .arg(hardwareAssociation));
                    }
                    
                    // 同步子节点的关联信息
                    SynchronizeChannelChildAssociationsToUI(channelItem, channel);
                    break;
                }
            }
        }
    }
    
    if (syncedCount > 0) {
        AddLogEntry("SUCCESS", QString(u8"✅ 控制通道关联信息同步完成：共同步 %1 个关联").arg(syncedCount));
    } else {
        AddLogEntry("DEBUG", QString(u8"ℹ️ 控制通道关联信息已是最新状态"));
    }
}

/**
 * @brief 同步单个控制通道子节点的关联信息到界面
 * @param channelItem 通道界面节点
 * @param channelData 通道数据对象
 */
void CMyMainWindow::SynchronizeChannelChildAssociationsToUI(QTreeWidgetItem* channelItem, const UI::ControlChannelParams& channelData) {
    if (!channelItem) return;
    
    // 遍历子节点并更新关联信息
    for (int i = 0; i < channelItem->childCount(); ++i) {
        QTreeWidgetItem* childItem = channelItem->child(i);
        if (!childItem) continue;
        
        QString childName = childItem->text(0);
        QString newAssociation;
        
        // 根据子节点类型获取对应的关联信息
        if (childName == "载荷1") {
            newAssociation = QString::fromStdString(channelData.load1Sensor);
        } else if (childName == "载荷2") {
            newAssociation = QString::fromStdString(channelData.load2Sensor);
        } else if (childName == "位置") {
            newAssociation = QString::fromStdString(channelData.positionSensor);
        } else if (childName == "控制") {
            newAssociation = QString::fromStdString(channelData.controlActuator);
        }
        
        // 更新界面显示
        QString currentAssociation = childItem->text(1);
        if (currentAssociation != newAssociation) {
            childItem->setText(1, newAssociation);
            AddLogEntry("DEBUG", QString(u8"🔄 同步%1子节点关联: %2 → %3")
                       .arg(QString::fromStdString(channelData.channelName))
                       .arg(childName)
                       .arg(newAssociation));
        }
    }
}

/**
 * @brief 获取当前控制通道参数
 */
UI::ControlChannelParams CMyMainWindow::getCurrentChannelParams(const QString& channelId) {
    UI::ControlChannelParams params;
    params.channelId = channelId.toStdString();
    params.channelName = channelId.toStdString(); // 默认使用通道ID作为名称
    
    AddLogEntry("DEBUG", QString(u8"🔍 获取通道参数，通道ID: %1").arg(channelId));
    
    // 🆕 首先尝试从数据管理器获取
    bool foundInDataManager = false;
    if (ctrlChanDataManager_) {
        auto groups = ctrlChanDataManager_->getAllControlChannelGroups();
        for (const auto& group : groups) {
            for (const auto& channel : group.channels) {
                // 🔧 修复：支持通过channelId或channelName匹配
                QString storedChannelId = QString::fromStdString(channel.channelId);
                QString storedChannelName = QString::fromStdString(channel.channelName);
                
                if (storedChannelId == channelId || storedChannelName == channelId) {
                    params = channel;
                    foundInDataManager = true;
                    AddLogEntry("DEBUG", QString(u8"  ✅ 从数据管理器找到通道参数 (匹配%1: %2)")
                               .arg(storedChannelId == channelId ? "ID" : "名称")
                               .arg(channelId));
                    break;
                }
            }
            if (foundInDataManager) break;
        }
    }
    
    // 🆕 如果数据管理器没有数据，尝试从UI树形结构读取（备用方案）
    if (!foundInDataManager) {
        AddLogEntry("DEBUG", QString(u8"⚠️ 数据管理器无通道信息，尝试从UI树形结构读取"));
        params = extractChannelParamsFromUI(channelId);
    }
    
    // 🆕 验证和补充参数
    if (params.channelName.empty()) {
        params.channelName = channelId.toStdString();
    }
    
    AddLogEntry("DEBUG", QString(u8"  通道参数获取完成：")
                + QString(u8"硬件关联='%1', ").arg(QString::fromStdString(params.hardwareAssociation))
                + QString(u8"载荷1='%1', ").arg(QString::fromStdString(params.load1Sensor))
                + QString(u8"载荷2='%1', ").arg(QString::fromStdString(params.load2Sensor))
                + QString(u8"位置='%1', ").arg(QString::fromStdString(params.positionSensor))
                + QString(u8"控制='%1'").arg(QString::fromStdString(params.controlActuator)));
    
    return params;
}

/**
 * @brief 调试详细信息面板状态
 */
void CMyMainWindow::debugDetailInfoPanelStatus() {
    AddLogEntry("DEBUG", "🔍 开始调试详细信息面板状态...");
    
    // 检查UI容器
    if (ui->detailInfoWidget) {
        AddLogEntry("DEBUG", QString("✅ detailInfoWidget 容器状态：可见=%1, 尺寸=%2x%3")
                   .arg(ui->detailInfoWidget->isVisible())
                   .arg(ui->detailInfoWidget->width())
                   .arg(ui->detailInfoWidget->height()));
        
        // 检查布局
        QLayout* layout = ui->detailInfoWidget->layout();
        if (layout) {
            AddLogEntry("DEBUG", QString("✅ detailInfoWidget 布局状态：项目数量=%1")
                       .arg(layout->count()));
        } else {
            AddLogEntry("WARNING", "⚠️ detailInfoWidget 没有布局");
        }
    } else {
        AddLogEntry("ERROR", "❌ detailInfoWidget 容器未找到");
    }
    
    // 检查详细信息面板
    if (detailInfoPanel_) {
        AddLogEntry("DEBUG", QString("✅ detailInfoPanel_ 状态：可见=%1, 尺寸=%2x%3")
                   .arg(detailInfoPanel_->isVisible())
                   .arg(detailInfoPanel_->width())
                   .arg(detailInfoPanel_->height()));
        
        // 检查父窗口
        QWidget* parent = detailInfoPanel_->parentWidget();
        if (parent) {
            AddLogEntry("DEBUG", QString("✅ detailInfoPanel_ 父窗口：%1, 可见=%2")
                       .arg(parent->objectName())
                       .arg(parent->isVisible()));
        } else {
            AddLogEntry("WARNING", "⚠️ detailInfoPanel_ 没有父窗口");
        }
    } else {
        AddLogEntry("ERROR", "❌ detailInfoPanel_ 未创建");
    }
    
    AddLogEntry("DEBUG", "🔍 详细信息面板状态调试完成");
}
    
//    // 更新基本信息
//    channel.channelId = params.channelId;
//    channel.channelName = params.channelName;
    
//    // 解析作动器关联信息
//    if (!params.controlActuator.empty()) {
//        std::string actuatorAssociation = params.controlActuator;
        
//        // 尝试从格式"组名 - 设备名"中提取作动器ID
//        std::string actuatorId = extractDeviceIdFromAssociation(actuatorAssociation, "作动器");
//        if (!actuatorId.empty()) {
//            channel.actuatorId = actuatorId;
//            AddLogEntry("DEBUG", QString(u8"  作动器ID: %1").arg(QString::fromStdString(actuatorId)));
//        }
//    }
    
//    // 解析传感器关联信息
//    channel.sensorIds.clear();
    
//    // 载荷1传感器
//    if (!params.load1Sensor.empty()) {
//        std::string sensorId = extractDeviceIdFromAssociation(params.load1Sensor, "传感器");
//        if (!sensorId.empty()) {
//            channel.sensorIds.push_back(sensorId);
//            AddLogEntry("DEBUG", QString(u8"  载荷1传感器ID: %1").arg(QString::fromStdString(sensorId)));
//        }
//    }
    
//    // 载荷2传感器
//    if (!params.load2Sensor.empty()) {
//        std::string sensorId = extractDeviceIdFromAssociation(params.load2Sensor, "传感器");
//        if (!sensorId.empty()) {
//            channel.sensorIds.push_back(sensorId);
//            AddLogEntry("DEBUG", QString(u8"  载荷2传感器ID: %1").arg(QString::fromStdString(sensorId)));
//        }
//    }
    
//    // 位置传感器
//    if (!params.positionSensor.empty()) {
//        std::string sensorId = extractDeviceIdFromAssociation(params.positionSensor, "传感器");
//        if (!sensorId.empty()) {
//            channel.sensorIds.push_back(sensorId);
//            AddLogEntry("DEBUG", QString(u8"  位置传感器ID: %1").arg(QString::fromStdString(sensorId)));
//        }
//    }
    
//    AddLogEntry("DEBUG", QString(u8"✅ LoadControlChannel数据更新完成"));
//}

///**
// * @brief 从关联字符串中提取设备ID
// */
//std::string CMyMainWindow::extractDeviceIdFromAssociation(const std::string& association, const std::string& deviceType) {
//    // 格式："组名 - 设备名"，需要反向查找对应的设备ID
//    QString assocStr = QString::fromStdString(association);
    
//    if (assocStr.contains(" - ")) {
//        QStringList parts = assocStr.split(" - ");
//        if (parts.size() >= 2) {
//            QString groupName = parts[0].trimmed();
//            QString deviceName = parts[1].trimmed();
            
//            // 根据设备类型查找对应的设备ID
//            if (deviceType == "作动器" && currentProject_) {
//                for (const auto& pair : currentProject_->actuators) {
//                    const DataModels::ActuatorInfo& actuator = pair.second;
//                    if (QString::fromStdString(actuator.groupName) == groupName &&
//                        QString::fromStdString(actuator.actuatorName) == deviceName) {
//                        return pair.first; // 返回作动器ID
//                    }
//                }
//            } else if (deviceType == "传感器" && currentProject_) {
//                for (const auto& pair : currentProject_->sensors) {
//                    const DataModels::SensorInfo& sensor = pair.second;
//                    if (QString::fromStdString(sensor.groupName) == groupName &&
//                        QString::fromStdString(sensor.sensorName) == deviceName) {
//                        return pair.first; // 返回传感器ID
//                    }
//                }
//            }
//        }
//    }
    
//    // 如果无法解析，返回原始字符串作为ID
//    AddLogEntry("WARNING", QString(u8"⚠️ 无法解析%1关联信息: %2")
//                .arg(QString::fromStdString(deviceType))
//                .arg(QString::fromStdString(association)));
//    return association;
//}

///**
// * @brief 从UI树形结构提取通道参数
// */
UI::ControlChannelParams CMyMainWindow::extractChannelParamsFromUI(const QString& channelId) {
    UI::ControlChannelParams params;
    params.channelId = channelId.toStdString();
    params.channelName = channelId.toStdString();
    
    AddLogEntry("DEBUG", QString(u8"🌳 从UI树形结构提取通道参数: %1").arg(channelId));
    
    // 在试验配置树中查找指定的通道节点
    QTreeWidgetItem* channelItem = findChannelItem(channelId);
    if (!channelItem) {
        AddLogEntry("WARNING", QString(u8"⚠️ 未在UI树中找到通道: %1").arg(channelId));
        return params;
    }
    
    // 从通道节点的第二列获取硬件关联信息
    QString hardwareAssociation = channelItem->text(1);
    params.hardwareAssociation = hardwareAssociation.toStdString();
    AddLogEntry("DEBUG", QString(u8"  硬件关联: %1").arg(hardwareAssociation));
    
    // 遍历子节点获取设备关联信息
    for (int i = 0; i < channelItem->childCount(); ++i) {
        QTreeWidgetItem* childItem = channelItem->child(i);
        if (!childItem) continue;
        
        QString childName = childItem->text(0);
        QString childAssociation = childItem->text(1);
        
        if (childName == "载荷1") {
            params.load1Sensor = childAssociation.toStdString();
            AddLogEntry("DEBUG", QString(u8"  载荷1传感器: %1").arg(childAssociation));
        } else if (childName == "载荷2") {
            params.load2Sensor = childAssociation.toStdString();
            AddLogEntry("DEBUG", QString(u8"  载荷2传感器: %1").arg(childAssociation));
        } else if (childName == "位置") {
            params.positionSensor = childAssociation.toStdString();
            AddLogEntry("DEBUG", QString(u8"  位置传感器: %1").arg(childAssociation));
        } else if (childName == "控制") {
            params.controlActuator = childAssociation.toStdString();
            AddLogEntry("DEBUG", QString(u8"  控制作动器: %1").arg(childAssociation));
        }
    }
    
    AddLogEntry("DEBUG", QString(u8"✅ UI树形结构参数提取完成"));
    return params;
}

///**
// * @brief 在试验通道树中查找指定的通道节点
// */
//QTreeWidgetItem* CMyMainWindow::findChannelItemInTree(const QString& channelId) {
//    if (!ui->testChannelTreeWidget) {
//        AddLogEntry("ERROR", QString(u8"❌ 试验通道树控件为空"));
//        return nullptr;
//    }
    
//    QTreeWidgetItem* rootItem = ui->testChannelTreeWidget->invisibleRootItem();
//    if (!rootItem) {
//        AddLogEntry("ERROR", QString(u8"❌ 试验通道树根节点为空"));
//        return nullptr;
//    }
    
//    // 递归查找通道节点
//    return findChannelItemRecursive(rootItem, channelId);
//}

/**
 * @brief 递归查找通道节点
 */
QTreeWidgetItem* CMyMainWindow::findChannelItemRecursive(QTreeWidgetItem* parentItem, const QString& channelId) {
    if (!parentItem) return nullptr;
    
    // 检查当前节点是否是目标通道
    if (parentItem->text(0) == channelId) {
        QString nodeType = parentItem->data(0, Qt::UserRole).toString();
        if (nodeType == "试验节点") {
            return parentItem;
        }
    }
    
    // 递归检查子节点
    for (int i = 0; i < parentItem->childCount(); ++i) {
        QTreeWidgetItem* foundItem = findChannelItemRecursive(parentItem->child(i), channelId);
        if (foundItem) {
            return foundItem;
        }
    }
    
    return nullptr;
}

/**
 * @brief 更新控制通道参数
 */
void CMyMainWindow::updateControlChannelParams(const QString& channelId, const UI::ControlChannelParams& updatedParams) {
    AddLogEntry("DEBUG", QString(u8"📝 更新控制通道参数: %1").arg(channelId));
    
    // 🆕 优先更新TestProject内存数据
    bool updatedInProject = false;
    bool channelFound = false;
    if (currentProject_) {
//        DataModels::LoadControlChannel* channel = currentProject_->GetChannel(channelId.toStdString());
//        if (channel) {
//            // 将UI参数转换为LoadControlChannel并更新
//            updateLoadControlChannelFromParams(*channel, updatedParams);
//            updatedInProject = true;
//            AddLogEntry("DEBUG", QString(u8"  ✅ TestProject内存数据已更新"));
//        }
    }
    
    // 🆕 同步更新数据管理器（保持兼容性）
    if (ctrlChanDataManager_) {
        auto groups = ctrlChanDataManager_->getAllControlChannelGroups();
        for (const auto& group : groups) {
            for (const auto& channel : group.channels) {
                // 🔧 修复：支持通过channelId或channelName匹配（与getCurrentChannelParams保持一致）
                QString storedChannelId = QString::fromStdString(channel.channelId);
                QString storedChannelName = QString::fromStdString(channel.channelName);
                
                if (storedChannelId == channelId || storedChannelName == channelId) {
                    // 🔧 修复：确保updatedParams使用正确的channelId（数据管理器中的实际ID）
                    UI::ControlChannelParams correctParams = updatedParams;
                    correctParams.channelId = channel.channelId; // 使用数据管理器中的原始ID
                    
                    // 🔧 修复：直接调用updateChannelInGroup，不修改副本
                    bool updateSuccess = ctrlChanDataManager_->updateChannelInGroup(group.groupId, correctParams);
                    if (updateSuccess) {
                        AddLogEntry("DEBUG", QString(u8"  ✅ 数据管理器已同步更新: 组ID=%1, 通道%2=%3")
                                   .arg(group.groupId)
                                   .arg(storedChannelId == channelId ? "ID" : "名称")
                                   .arg(channelId));
                        channelFound = true;
                    } else {
                        AddLogEntry("ERROR", QString(u8"  ❌ 数据管理器更新失败: 组ID=%1, 通道%2=%3")
                                   .arg(group.groupId)
                                   .arg(storedChannelId == channelId ? "ID" : "名称")
                                   .arg(channelId));
                    }
                    break;
                }
            }
            if (channelFound) break;
        }
        
        if (!channelFound) {
            AddLogEntry("WARNING", QString(u8"⚠️ 在数据管理器中未找到通道: %1").arg(channelId));
        }
    }
    
    // 🔧 修复：根据数据管理器更新结果显示消息
    if (channelFound) {
        AddLogEntry("INFO", QString(u8"📝 通道参数已更新: %1 → %2")
                   .arg(channelId)
                   .arg(QString::fromStdString(updatedParams.channelName)));
    } else {
        AddLogEntry("WARNING", QString(u8"⚠️ 未找到通道: %1").arg(channelId));
    }
}

/**
 * @brief 更新控制通道UI显示
 */
void CMyMainWindow::updateControlChannelDisplay(const QString& channelId, const UI::ControlChannelParams& params) {
    // 更新试验配置树中的显示
    if (ui->testConfigTreeWidget) {
        QTreeWidgetItem* channelItem = findChannelItem(channelId);
        if (channelItem) {
            // 🔧 修复：确保channelId存储在userData中
            channelItem->setData(1, Qt::UserRole, channelId);
            
            // 更新通道名称显示
            QString displayName = QString::fromStdString(params.channelName);
            if (!displayName.isEmpty() && displayName != channelId) {
                // 🔧 修复：只显示通道名称，不显示CH1前缀和括号
                channelItem->setText(0, displayName);
                AddLogEntry("INFO", QString(u8"✅ 更新通道%1显示名称: %2").arg(channelId).arg(displayName));
            } else {
                // 🔧 修复：如果通道名称为空或与channelId相同，显示默认的channelId
                channelItem->setText(0, channelId);
                AddLogEntry("INFO", QString(u8"✅ 更新通道%1显示默认名称: %2").arg(channelId).arg(channelId));
            }
            
            // 更新关联信息显示
            channelItem->setText(1, QString::fromStdString(params.hardwareAssociation));
            
            // 🆕 新增：更新扩展列信息
            channelItem->setText(2, QString::number(params.lc_id));           // 下位机ID
            channelItem->setText(3, QString::number(params.station_id));      // 站点ID
            channelItem->setText(4, params.enable ? u8"✅" : u8"❌");          // 使能状态
            
            // 🔧 修改：CH1、CH2第五列不显示极性值
            QString channelId = QString::fromStdString(params.channelId);
            if (channelId == "CH1" || channelId == "CH2") {
                channelItem->setText(5, "");     // CH1、CH2第五列不显示值
            } else {
                channelItem->setText(5, getPolarityShortText(params.servo_control_polarity)); // 其他通道显示极性
            }
            
            // 更新子节点显示
            updateChannelChildNodes(channelItem, params);
            
            // 🔧 修复：更新通道的tooltip信息
            channelItem->setToolTip(0, GenerateControlChannelDetailedInfo(channelId));
        } else {
            AddLogEntry("WARNING", QString(u8"⚠️ 未找到通道%1的UI节点，无法更新显示").arg(channelId));
        }
    }
    
    // 🔧 修复：只更新tooltip信息，不重新初始化整个树
    // RefreshTestConfigTreeFromDataManagers(); // ❌ 移除：这会清空所有数据
    UpdateAllTreeWidgetTooltips(); // ✅ 只更新tooltip信息
}

/**
 * @brief 查找控制通道项
 */
QTreeWidgetItem* CMyMainWindow::findChannelItem(const QString& channelId) {
    if (!ui->testConfigTreeWidget) return nullptr;
    
    // 首先获取控制通道根节点
    QTreeWidgetItem* taskRoot = ui->testConfigTreeWidget->topLevelItem(0);
    if (!taskRoot) return nullptr;
    
    QTreeWidgetItem* controlChannelRoot = nullptr;
    for (int i = 0; i < taskRoot->childCount(); ++i) {
        QTreeWidgetItem* child = taskRoot->child(i);
        if (child && child->text(0) == tr("控制通道")) {
            controlChannelRoot = child;
            break;
        }
    }
    
    if (!controlChannelRoot) return nullptr;
    
    // 🔧 修复：直接按channelId查找，避免通过channelName查找导致的混乱
    // 先尝试直接按channelId匹配UI节点
    for (int j = 0; j < controlChannelRoot->childCount(); ++j) {
        QTreeWidgetItem* channelItem = controlChannelRoot->child(j);
        if (!channelItem) continue;
        
        // 🔧 修复：使用userData存储的channelId进行匹配，如果没有则尝试其他方式
        QString storedChannelId = channelItem->data(1, Qt::UserRole).toString();
        if (!storedChannelId.isEmpty() && storedChannelId == channelId) {
            return channelItem;
        }
        
        // 🔧 修复：如果没有存储channelId，检查是否是CH1或CH2节点的原始形式
        QString displayText = channelItem->text(0);
        if ((channelId == "CH1" && (displayText == "CH1" || displayText.contains("CH1"))) ||
            (channelId == "CH2" && (displayText == "CH2" || displayText.contains("CH2")))) {
            // 找到匹配的节点，存储channelId以便下次快速查找
            channelItem->setData(1, Qt::UserRole, channelId);
            return channelItem;
        }
    }
    
    // 备用方案：直接按channelId匹配（用于向后兼容）
    QTreeWidgetItemIterator it(ui->testConfigTreeWidget);
    while (*it) {
        if ((*it)->text(0) == channelId) {
            QString nodeType = (*it)->data(0, Qt::UserRole).toString();
            if (nodeType == "试验节点") {
                QTreeWidgetItem* parent = (*it)->parent();
                if (parent && parent->text(0) == tr("控制通道")) {
                    return *it;
                }
            }
        }
        ++it;
    }
    
    return nullptr;
}

/**
 * @brief 更新通道子节点显示
 */
void CMyMainWindow::updateChannelChildNodes(QTreeWidgetItem* channelItem, const UI::ControlChannelParams& params) {
    if (!channelItem) return;
    
    for (int i = 0; i < channelItem->childCount(); ++i) {
        QTreeWidgetItem* childItem = channelItem->child(i);
        QString childName = childItem->text(0);
        
        if (childName == u8"载荷1") {
            childItem->setText(1, QString::fromStdString(params.load1Sensor));
            childItem->setText(5, getPolarityShortText(params.payload_sensor1_polarity)); // 🆕 新增：载荷1传感器极性
        } else if (childName == u8"载荷2") {
            childItem->setText(1, QString::fromStdString(params.load2Sensor));
            childItem->setText(5, getPolarityShortText(params.payload_sensor2_polarity)); // 🆕 新增：载荷2传感器极性
        } else if (childName == u8"位置") {
            childItem->setText(1, QString::fromStdString(params.positionSensor));
            childItem->setText(5, getPolarityShortText(params.position_sensor_polarity)); // 🆕 新增：位置传感器极性
        } else if (childName == u8"控制") {
            childItem->setText(1, QString::fromStdString(params.controlActuator));
            childItem->setText(5, getPolarityShortText(params.servo_control_polarity)); // 🆕 新增：控制作动器极性
        }
    }
}

/**
 * @brief 获取可用硬件节点列表
 * @details 返回"节点名 - 通道名称"格式的列表，用于硬件关联配置
 */
QStringList CMyMainWindow::getAvailableHardwareNodes() {
    QStringList nodes;
    
    if (ui->hardwareTreeWidget) {
        QTreeWidgetItemIterator it(ui->hardwareTreeWidget);
        while (*it) {
            QString nodeType = (*it)->data(0, Qt::UserRole).toString();
            if (nodeType == "硬件节点") {
                QString nodeName = (*it)->text(0);
                
                // 遍历该硬件节点的所有通道，生成"节点名 - 通道名称"格式
                for (int i = 0; i < (*it)->childCount(); ++i) {
                    QTreeWidgetItem* channelItem = (*it)->child(i);
                    if (channelItem && channelItem->data(0, Qt::UserRole).toString() == "硬件节点通道") {
                        QString channelName = channelItem->text(0);  // 如：CH1, CH2
                        QString fullName = QString("%1 - %2").arg(nodeName).arg(channelName);
                        nodes << fullName;
                    }
                }
            }
            ++it;
        }
    }
    
    return nodes;
}

/**
 * @brief 获取可用传感器列表
 */
QStringList CMyMainWindow::getAvailableSensors() {
    QStringList sensors;
    
    if (ui->hardwareTreeWidget) {
        QTreeWidgetItemIterator it(ui->hardwareTreeWidget);
        while (*it) {
            QString nodeType = (*it)->data(0, Qt::UserRole).toString();
            if (nodeType == "传感器设备") {
                QString sensorName = (*it)->text(0);
                QTreeWidgetItem* parentItem = (*it)->parent();
                if (parentItem) {
                    QString groupName = parentItem->text(0);
                    sensors << QString("%1 - %2").arg(groupName).arg(sensorName);
                }
            }
            ++it;
        }
    }
    
    return sensors;
}

/**
 * @brief 获取可用作动器列表
 */
QStringList CMyMainWindow::getAvailableActuators() {
    QStringList actuators;
    
    if (ui->hardwareTreeWidget) {
        QTreeWidgetItemIterator it(ui->hardwareTreeWidget);
        while (*it) {
            QString nodeType = (*it)->data(0, Qt::UserRole).toString();
            if (nodeType == "作动器设备") {
                QString actuatorName = (*it)->text(0);
                QTreeWidgetItem* parentItem = (*it)->parent();
                if (parentItem) {
                    QString groupName = parentItem->text(0);
                    actuators << QString("%1 - %2").arg(groupName).arg(actuatorName);
                }
            }
            ++it;
        }
    }
    
    return actuators;
}

/**
 * @brief 获取硬件组列表和成员信息
 * @return QPair<组列表, 组->成员映射>
 */
QPair<QStringList, QMap<QString, QStringList>> CMyMainWindow::getHardwareGroupsAndMembers() {
    QStringList groups;
    QMap<QString, QStringList> groupMembers;
    
    if (ui->hardwareTreeWidget) {
        QTreeWidgetItemIterator it(ui->hardwareTreeWidget);
        while (*it) {
            QString nodeType = (*it)->data(0, Qt::UserRole).toString();
            if (nodeType == "硬件节点") {
                QString nodeName = (*it)->text(0);
                QStringList channels;
                
                // 遍历该硬件节点的所有通道
                for (int i = 0; i < (*it)->childCount(); ++i) {
                    QTreeWidgetItem* channelItem = (*it)->child(i);
                    if (channelItem && channelItem->data(0, Qt::UserRole).toString() == "硬件节点通道") {
                        QString channelName = channelItem->text(0);  // 如：CH1, CH2
                        channels << channelName;
                    }
                }
                
                if (!channels.isEmpty()) {
                    groups << nodeName;
                    groupMembers[nodeName] = channels;
                }
            }
            ++it;
        }
    }
    
    return qMakePair(groups, groupMembers);
}

/**
 * @brief 获取传感器组列表和成员信息
 * @return QPair<组列表, 组->成员映射>
 */
QPair<QStringList, QMap<QString, QStringList>> CMyMainWindow::getSensorGroupsAndMembers() {
    QStringList groups;
    QMap<QString, QStringList> groupMembers;
    
    if (ui->hardwareTreeWidget) {
        QTreeWidgetItemIterator it(ui->hardwareTreeWidget);
        while (*it) {
            QString nodeType = (*it)->data(0, Qt::UserRole).toString();
            if (nodeType == "传感器组") {
                QString groupName = (*it)->text(0);
                QStringList sensors;
                
                // 遍历该传感器组的所有设备
                for (int i = 0; i < (*it)->childCount(); ++i) {
                    QTreeWidgetItem* sensorItem = (*it)->child(i);
                    if (sensorItem && sensorItem->data(0, Qt::UserRole).toString() == "传感器设备") {
                        QString sensorName = sensorItem->text(0);  // 如：传感器_000001
                        sensors << sensorName;
                    }
                }
                
                if (!sensors.isEmpty()) {
                    groups << groupName;
                    groupMembers[groupName] = sensors;
                }
            }
            ++it;
        }
    }
    
    return qMakePair(groups, groupMembers);
}

/**
 * @brief 获取作动器组列表和成员信息
 * @return QPair<组列表, 组->成员映射>
 */
QPair<QStringList, QMap<QString, QStringList>> CMyMainWindow::getActuatorGroupsAndMembers() {
    QStringList groups;
    QMap<QString, QStringList> groupMembers;
    
    if (ui->hardwareTreeWidget) {
        QTreeWidgetItemIterator it(ui->hardwareTreeWidget);
        while (*it) {
            QString nodeType = (*it)->data(0, Qt::UserRole).toString();
            if (nodeType == "作动器组") {
                QString groupName = (*it)->text(0);
                QStringList actuators;
                
                // 遍历该作动器组的所有设备
                for (int i = 0; i < (*it)->childCount(); ++i) {
                    QTreeWidgetItem* actuatorItem = (*it)->child(i);
                    if (actuatorItem && actuatorItem->data(0, Qt::UserRole).toString() == "作动器设备") {
                        QString actuatorName = actuatorItem->text(0);  // 如：作动器_000001
                        actuators << actuatorName;
                    }
                }
                
                if (!actuators.isEmpty()) {
                    groups << groupName;
                    groupMembers[groupName] = actuators;
                }
            }
            ++it;
        }
    }
    
    return qMakePair(groups, groupMembers);
}

/**
 * @brief 初始化详细信息面板
 */
void CMyMainWindow::initializeDetailInfoPanel() {
    // 检查详细信息面板容器是否存在
    if (!ui->detailInfoWidget) {
        AddLogEntry("ERROR", "详细信息面板容器未找到，初始化失败");
        return;
    }
    
    // 🆕 修复：检查并清理现有子控件，但不删除UI文件中的布局
    QList<QWidget*> childWidgets = ui->detailInfoWidget->findChildren<QWidget*>(QString(), Qt::FindDirectChildrenOnly);
    for (QWidget* child : childWidgets) {
        AddLogEntry("DEBUG", QString("🔄 清理子控件：%1").arg(child->objectName()));
        child->setParent(nullptr);
        child->deleteLater();
    }
    
    // 创建详细信息面板
    detailInfoPanel_ = std::make_unique<DetailInfoPanel>(ui->detailInfoWidget);
    qDebug() << __FUNCTION__ << __LINE__ << "CMyMainWindow::initializeDetailInfoPanel()\r\n\r\n\r\n\r\n\r\n\r\n";
    
    if (detailInfoPanel_) {
        // 🆕 修复：直接添加到现有的UI布局中，不创建新布局
        // 获取UI文件中的布局
        QLayout* existingLayout = ui->detailInfoWidget->layout();
        if (existingLayout) {
            existingLayout->addWidget(detailInfoPanel_.get());
            AddLogEntry("DEBUG", "✅ 已添加到现有UI布局");
        } else {
            // 如果没有现有布局，才创建新布局（这种情况不应该发生）
            AddLogEntry("WARNING", "⚠️ 未找到现有布局，创建新布局");
            QVBoxLayout* layout = new QVBoxLayout(ui->detailInfoWidget);
            layout->setContentsMargins(0, 0, 0, 0);
            layout->setSpacing(0);
            layout->addWidget(detailInfoPanel_.get());
        }
        
        // 连接信号
        connectDetailInfoPanelSignals();
        
        // 🆕 新增：初始化数据变化监听器
        if (!dataChangeListener_) {
            dataChangeListener_ = new DataChangeListener(this);
        }
        dataChangeListener_->setDetailInfoPanel(detailInfoPanel_.get());
        
        // 🆕 新增：连接数据管理器信号
        if (sensorDataManager_) {
            dataChangeListener_->connectSensorDataManager(sensorDataManager_.get());
            AddLogEntry("DEBUG", "✅ 传感器数据管理器信号已连接");
        }
        
        // 🆕 新增：连接作动器数据管理器信号
        if (actuatorViewModel1_2_) {
            dataChangeListener_->connectActuatorViewModel(actuatorViewModel1_2_.get());
            AddLogEntry("DEBUG", "✅ 作动器视图模型信号已连接");
        }
        
        // 🆕 新增：确保面板可见
        detailInfoPanel_->setVisible(true);
        ui->detailInfoWidget->setVisible(true);
        ui->detailInfoWidget->setMaximumWidth(16777215);
        ui->detailInfoWidget->setMinimumWidth(120);
        // 设置详细信息组框宽度为最大
        if (ui->detailInfoGroupBox) {
            ui->detailInfoGroupBox->setMaximumWidth(16777215);
            ui->detailInfoGroupBox->setMinimumWidth(120);
        }
        
        AddLogEntry("INFO", "详细信息面板初始化成功");
    } else {
        AddLogEntry("ERROR", "详细信息面板创建失败");
    }
}

/**
 * @brief 连接详细信息面板信号
 */
void CMyMainWindow::connectDetailInfoPanelSignals() {
    if (!detailInfoPanel_) {
        AddLogEntry("ERROR", "详细信息面板未初始化，无法连接信号");
        return;
    }
    

    
    AddLogEntry("INFO", "详细信息面板信号连接完成");
    
    // 🆕 新增：调试详细信息面板状态
    AddLogEntry("DEBUG", QString("详细信息面板状态检查：detailInfoPanel_=%1, detailInfoWidget=%2")
               .arg(detailInfoPanel_ ? "已创建" : "未创建")
               .arg(ui->detailInfoWidget ? "已找到" : "未找到"));
}

// 🆕 新增：控制通道详细信息显示管理
/**
 * @brief 显示控制通道详细信息
 * @param channelName 通道名称（如"CH1"、"CH2"或"控制通道"）
 * @details 当用户在树形控件中选择控制通道时，调用此方法显示详细信息
 */
void CMyMainWindow::ShowControlChannelDetailInfo(const QString& channelName) {
    AddLogEntry("INFO", QString(u8"🔍 显示控制通道详细信息：%1").arg(channelName));
    
    // 检查详细信息面板是否已初始化
    if (!detailInfoPanel_) {
        AddLogEntry("WARNING", QString(u8"⚠️ 详细信息面板未初始化，无法显示控制通道信息"));
        return;
    }
    
    // 🆕 修改：统一处理控制通道节点，无论是根节点还是子节点
    if (channelName == "控制通道" || channelName.startsWith("CH")) {
        AddLogEntry("INFO", QString(u8"🎛️ 检测到控制通道节点：%1，显示统一格式信息").arg(channelName));
        
        // 获取测试配置树形控件
        CustomTestConfigTreeWidget* testConfigTree = getTestConfigTreeWidget();
        if (!testConfigTree) {
            AddLogEntry("ERROR", u8"❌ 测试配置树形控件未找到，无法获取子通道信息");
            return;
        }
        
        // 🆕 新增：调试树形控件结构
        AddLogEntry("DEBUG", QString(u8"🔍 树形控件顶层项目数量：%1").arg(testConfigTree->topLevelItemCount()));
        for (int i = 0; i < testConfigTree->topLevelItemCount(); ++i) {
            QTreeWidgetItem* topLevelItem = testConfigTree->topLevelItem(i);
            AddLogEntry("DEBUG", QString(u8"  - 顶层项目[%1]：%2").arg(i).arg(topLevelItem->text(0)));
            AddLogEntry("DEBUG", QString(u8"    子项目数量：%1").arg(topLevelItem->childCount()));
            for (int j = 0; j < topLevelItem->childCount(); ++j) {
                QTreeWidgetItem* child = topLevelItem->child(j);
                AddLogEntry("DEBUG", QString(u8"      - 子项目[%1]：%2").arg(j).arg(child->text(0)));
                AddLogEntry("DEBUG", QString(u8"        子项目数量：%1").arg(child->childCount()));
                if (child->text(0) == "控制通道") {
                    for (int k = 0; k < child->childCount(); ++k) {
                        QTreeWidgetItem* grandChild = child->child(k);
                        AddLogEntry("DEBUG", QString(u8"          - 控制通道子项目[%1]：%2").arg(k).arg(grandChild->text(0)));
                    }
                }
            }
        }
        
        // 查找控制通道根节点
        QTreeWidgetItem* controlChannelRoot = nullptr;
        for (int i = 0; i < testConfigTree->topLevelItemCount(); ++i) {
            QTreeWidgetItem* topLevelItem = testConfigTree->topLevelItem(i);
            if (topLevelItem->text(0) == "实验") {
                AddLogEntry("DEBUG", QString(u8"✅ 找到实验节点，子项目数量：%1").arg(topLevelItem->childCount()));
                // 在"实验"节点下查找"控制通道"子节点
                for (int j = 0; j < topLevelItem->childCount(); ++j) {
                    QTreeWidgetItem* child = topLevelItem->child(j);
                    AddLogEntry("DEBUG", QString(u8"  - 检查子项目[%1]：%2").arg(j).arg(child->text(0)));
                    if (child->text(0) == "控制通道") {
                        controlChannelRoot = child;
                        AddLogEntry("DEBUG", QString(u8"🎯 找到控制通道根节点，子项目数量：%1").arg(controlChannelRoot->childCount()));
                        break;
                    }
                }
                if (controlChannelRoot) break; // 找到后退出外层循环
            }
        }
        
        if (controlChannelRoot) {
            // 获取所有子通道
            QList<QTreeWidgetItem*> childChannels;
            AddLogEntry("DEBUG", QString(u8"🔍 开始查找子通道，控制通道根节点子项目数量：%1").arg(controlChannelRoot->childCount()));
            for (int i = 0; i < controlChannelRoot->childCount(); ++i) {
                QTreeWidgetItem* child = controlChannelRoot->child(i);
                AddLogEntry("DEBUG", QString(u8"  - 检查子项目[%1]：%2，是否以CH开头：%3").arg(i).arg(child->text(0)).arg(child->text(0).startsWith("CH") ? "是" : "否"));
                if (child->text(0).startsWith("CH")) {
                    childChannels.append(child);
                    AddLogEntry("DEBUG", QString(u8"    ✅ 添加子通道：%1").arg(child->text(0)));
                }
            }
            
            AddLogEntry("INFO", QString(u8"✅ 找到控制通道根节点，子通道数量：%1").arg(childChannels.size()));
            
            // 🆕 新增：如果是单个子通道节点，需要高亮对应的行
            int selectedRow = -1;
            if (channelName.startsWith("CH")) {
                // 找到对应的子通道索引
                for (int i = 0; i < childChannels.size(); ++i) {
                    if (childChannels[i]->text(0) == channelName) {
                        selectedRow = i;
                        break;
                    }
                }
                AddLogEntry("INFO", QString(u8"🎯 子通道 %1 对应表格行：%2").arg(channelName).arg(selectedRow));
            }
            
            // 使用BasicInfoWidget的静态方法创建控制通道根节点的NodeInfo
            NodeInfo rootNodeInfo = BasicInfoWidget::createControlChannelRootNodeInfo("控制通道", childChannels);
            
            // 🆕 新增：设置选中的行索引，用于后续高亮显示
            rootNodeInfo.setBasicProperty("selectedRow", selectedRow);
            
            // 设置详细信息到面板
            detailInfoPanel_->setNodeInfo(rootNodeInfo);
            
            // 🆕 新增：如果指定了选中行，通知详细信息面板高亮对应行
            if (selectedRow >= 0) {
                detailInfoPanel_->setSelectedRow(selectedRow);
                AddLogEntry("INFO", QString(u8"🎯 已设置选中行：%1，对应子通道：%2").arg(selectedRow).arg(channelName));
            }
            
            AddLogEntry("SUCCESS", QString(u8"✅ 控制通道 %1 详细信息已显示，包含 %2 个子通道")
                       .arg(channelName).arg(childChannels.size()));
        } else {
            AddLogEntry("WARNING", u8"⚠️ 未找到控制通道根节点");
        }
        return;
    }
    
    // 🆕 修复：对于子通道节点，需要显示控制通道组信息并高亮对应行
    AddLogEntry("DEBUG", QString(u8"🔍 处理子通道节点：%1").arg(channelName));
    
    // 获取测试配置树形控件
    CustomTestConfigTreeWidget* testConfigTree = getTestConfigTreeWidget();
    if (!testConfigTree) {
        AddLogEntry("ERROR", u8"❌ 测试配置树形控件未找到，无法获取控制通道组信息");
        return;
    }
    
    // 查找控制通道根节点
    QTreeWidgetItem* controlChannelRoot = nullptr;
    for (int i = 0; i < testConfigTree->topLevelItemCount(); ++i) {
        QTreeWidgetItem* topLevelItem = testConfigTree->topLevelItem(i);
        if (topLevelItem->text(0) == "实验") {
            // 在"实验"节点下查找"控制通道"子节点
            for (int j = 0; j < topLevelItem->childCount(); ++j) {
                QTreeWidgetItem* child = topLevelItem->child(j);
                if (child->text(0) == "控制通道") {
                    controlChannelRoot = child;
                    break;
                }
            }
            if (controlChannelRoot) break;
        }
    }
    
    if (controlChannelRoot) {
        // 获取所有子通道
        QList<QTreeWidgetItem*> childChannels;
        AddLogEntry("DEBUG", QString(u8"🔍 开始查找子通道，控制通道根节点子项目数量：%1").arg(controlChannelRoot->childCount()));
        for (int i = 0; i < controlChannelRoot->childCount(); ++i) {
            QTreeWidgetItem* child = controlChannelRoot->child(i);
            // 🆕 修复：子通道识别逻辑，支持多种命名方式
            QString childName = child->text(0);
            
            // 🔧 修复：使用正确的方法获取节点类型
            QString childType;
            QVariant userData = child->data(0, Qt::UserRole);
            if (userData.isValid()) {
                childType = userData.toString();
            } else {
                // 如果没有UserRole数据，根据子节点数量判断
                childType = (child->childCount() == 4) ? "控制通道" : "未知类型";
            }
            
            // 检查是否为控制通道子节点：
            // 1. 名称以"CH"开头（如CH1、CH2）
            // 2. 或者节点类型为"控制通道"
            // 3. 或者有4个子节点（载荷1、载荷2、位置、控制）
            bool isControlChannel = childName.startsWith("CH") || 
                                  childType == "控制通道" || 
                                  child->childCount() == 4;
            
            if (isControlChannel) {
                childChannels.append(child);
                AddLogEntry("DEBUG", QString(u8"✅ 找到子通道：%1，类型：%2，子节点数：%3").arg(childName).arg(childType).arg(child->childCount()));
            } else {
                AddLogEntry("DEBUG", QString(u8"⚠️ 跳过非控制通道子节点：%1，类型：%2，子节点数：%3").arg(childName).arg(childType).arg(child->childCount()));
            }
        }
        
        AddLogEntry("INFO", QString(u8"✅ 找到控制通道根节点，子通道数量：%1").arg(childChannels.size()));
        
        // 🆕 修复：找到对应的子通道索引，用于高亮显示
        int selectedRow = -1;
        for (int i = 0; i < childChannels.size(); ++i) {
            if (childChannels[i]->text(0) == channelName) {
                selectedRow = i;
                break;
            }
        }
        
        if (selectedRow >= 0) {
            AddLogEntry("INFO", QString(u8"🎯 子通道 %1 对应表格行：%2").arg(channelName).arg(selectedRow));
        } else {
            AddLogEntry("WARNING", QString(u8"⚠️ 未找到子通道 %1 对应的表格行").arg(channelName));
        }
        
        // 使用BasicInfoWidget的静态方法创建控制通道根节点的NodeInfo
        NodeInfo rootNodeInfo = BasicInfoWidget::createControlChannelRootNodeInfo("控制通道", childChannels);
        
        // 🆕 修复：设置选中的行索引，用于后续高亮显示
        rootNodeInfo.setBasicProperty("selectedRow", selectedRow);
        
        // 设置详细信息到面板
        detailInfoPanel_->setNodeInfo(rootNodeInfo);
        
        // 🆕 修复：如果指定了选中行，通知详细信息面板高亮对应行
        if (selectedRow >= 0) {
            detailInfoPanel_->setSelectedRow(selectedRow);
            AddLogEntry("INFO", QString(u8"🎯 已设置选中行：%1，对应子通道：%2").arg(selectedRow).arg(channelName));
        }
        
        AddLogEntry("SUCCESS", QString(u8"✅ 子通道 %1 详细信息已显示，包含 %2 个子通道，高亮行：%3")
                   .arg(channelName).arg(childChannels.size()).arg(selectedRow));
    } else {
        AddLogEntry("WARNING", u8"⚠️ 未找到控制通道根节点");
        
        // 回退到原有的处理逻辑
        UI::ControlChannelParams channelParams = GetControlChannelParams(channelName);
        
        // 使用DetailInfoPanel的静态方法创建NodeInfo
        NodeInfo nodeInfo = DetailInfoPanel::createControlChannelNodeInfo(
            channelName, 
            QString::fromStdString(channelParams.channelId), 
            channelParams
        );
        
        // 设置详细信息到面板
        detailInfoPanel_->setNodeInfo(nodeInfo);
        
        AddLogEntry("SUCCESS", QString(u8"✅ 控制通道 %1 详细信息已显示（回退模式），包含 %2 个子节点")
                   .arg(channelName).arg(nodeInfo.subNodes.size()));
    }
}

/**
 * @brief 获取控制通道参数
 * @param channelName 通道名称
 * @return 控制通道参数，如果未找到返回默认参数
 */
UI::ControlChannelParams CMyMainWindow::GetControlChannelParams(const QString& channelName) {
    AddLogEntry("DEBUG", QString(u8"🔍 获取控制通道参数：%1").arg(channelName));
    
    // 首先尝试从数据管理器获取
    if (ctrlChanDataManager_) {
        auto groups = ctrlChanDataManager_->getAllControlChannelGroups();
        for (const auto& group : groups) {
            for (const auto& channel : group.channels) {
                QString storedChannelId = QString::fromStdString(channel.channelId);
                QString storedChannelName = QString::fromStdString(channel.channelName);
                
                // 支持通过channelId或channelName匹配
                if (storedChannelId == channelName || storedChannelName == channelName) {
                    AddLogEntry("DEBUG", QString(u8"✅ 从数据管理器找到通道参数：%1").arg(channelName));
                    return channel;
                }
            }
        }
    }
    
    // 如果数据管理器没有数据，尝试从UI树形结构读取
    AddLogEntry("DEBUG", QString(u8"⚠️ 数据管理器无通道信息，尝试从UI树形结构读取：%1").arg(channelName));
    return extractChannelParamsFromUI(channelName);
}

/**
 * @brief 显示控制通道组汇总信息
 * @details 当用户在树形控件中选择控制通道根节点时，调用此方法显示汇总信息
 */
void CMyMainWindow::ShowControlChannelGroupInfo() {
    AddLogEntry("INFO", u8"🎛️ 显示控制通道组汇总信息");
    
    // 检查详细信息面板是否已初始化
    if (!detailInfoPanel_) {
        AddLogEntry("WARNING", u8"⚠️ 详细信息面板未初始化，无法显示控制通道组信息");
        return;
    }
    
    // 获取测试配置树形控件
    CustomTestConfigTreeWidget* testConfigTree = getTestConfigTreeWidget();
    if (!testConfigTree) {
        AddLogEntry("ERROR", u8"❌ 测试配置树形控件未找到，无法获取控制通道组信息");
        return;
    }
    
    // 查找控制通道根节点
    QTreeWidgetItem* controlChannelRoot = nullptr;
    for (int i = 0; i < testConfigTree->topLevelItemCount(); ++i) {
        QTreeWidgetItem* topLevelItem = testConfigTree->topLevelItem(i);
        if (topLevelItem->text(0) == "实验") {
            // 在"实验"节点下查找"控制通道"子节点
            for (int j = 0; j < topLevelItem->childCount(); ++j) {
                QTreeWidgetItem* child = topLevelItem->child(j);
                if (child->text(0) == "控制通道") {
                    controlChannelRoot = child;
                    break;
                }
            }
            if (controlChannelRoot) break; // 找到后退出外层循环
        }
    }
    
    if (controlChannelRoot) {
        // 获取所有子通道
        QList<QTreeWidgetItem*> childChannels;
        for (int i = 0; i < controlChannelRoot->childCount(); ++i) {
            QTreeWidgetItem* child = controlChannelRoot->child(i);
            // 🆕 修复：子通道识别逻辑，支持多种命名方式
            QString childName = child->text(0);
            
            // 🔧 修复：使用正确的方法获取节点类型
            QString childType;
            QVariant userData = child->data(0, Qt::UserRole);
            if (userData.isValid()) {
                childType = userData.toString();
            } else {
                // 如果没有UserRole数据，根据子节点数量判断
                childType = (child->childCount() == 4) ? "控制通道" : "未知类型";
            }
            
            // 检查是否为控制通道子节点：
            // 1. 名称以"CH"开头（如CH1、CH2）
            // 2. 或者节点类型为"控制通道"
            // 3. 或者有4个子节点（载荷1、载荷2、位置、控制）
            bool isControlChannel = childName.startsWith("CH") || 
                                  childType == "控制通道" || 
                                  child->childCount() == 4;
            
            if (isControlChannel) {
                childChannels.append(child);
                AddLogEntry("DEBUG", QString(u8"✅ 找到子通道：%1，类型：%2，子节点数：%3").arg(childName).arg(childType).arg(child->childCount()));
            } else {
                AddLogEntry("DEBUG", QString(u8"⚠️ 跳过非控制通道子节点：%1，类型：%2，子节点数：%3").arg(childName).arg(childType).arg(child->childCount()));
            }
        }
        
        AddLogEntry("INFO", QString(u8"✅ 找到控制通道根节点，子通道数量：%1").arg(childChannels.size()));
        
        // 使用BasicInfoWidget的静态方法创建控制通道根节点的NodeInfo
        NodeInfo rootNodeInfo = BasicInfoWidget::createControlChannelRootNodeInfo("控制通道", childChannels);
        
        // 设置详细信息到面板
        detailInfoPanel_->setNodeInfo(rootNodeInfo);
        
        AddLogEntry("SUCCESS", QString(u8"✅ 控制通道组汇总信息已显示，包含 %1 个子通道")
                   .arg(childChannels.size()));
    } else {
        AddLogEntry("WARNING", u8"⚠️ 未找到控制通道根节点");
        
        // 如果找不到控制通道根节点，显示默认信息
        NodeInfo defaultInfo;
        defaultInfo.nodeName = "控制通道组";
        defaultInfo.nodeType = "控制通道组";
        defaultInfo.status = NodeStatus::Unknown;
        detailInfoPanel_->setNodeInfo(defaultInfo);
    }
}

// ============================================================================
// 🆕 新增：工程状态修复相关方法实现
// ============================================================================

/**
 * @brief 确保数据管理器完全同步
 * @return 是否同步成功
 */
bool CMyMainWindow::ensureDataManagerSync() {
    AddLogEntry("DEBUG", "🔄 开始验证数据管理器同步状态...");
    
    bool syncSuccess = true;
    
    // 验证传感器数据管理器
    if (sensorDataManager_) {
        // 检查数据有效性（如果管理器有验证方法）
        // 这里可以根据实际的数据管理器接口进行调整
        AddLogEntry("DEBUG", "✅ 传感器数据管理器状态正常");
    } else {
        AddLogEntry("WARNING", "⚠️ 传感器数据管理器未初始化");
        syncSuccess = false;
    }
    
    // 验证作动器数据管理器
    if (actuatorViewModel1_2_) {
        // 检查数据有效性（如果管理器有验证方法）
        // 这里可以根据实际的数据管理器接口进行调整
        AddLogEntry("DEBUG", "✅ 作动器数据管理器状态正常");
    } else {
        AddLogEntry("WARNING", "⚠️ 作动器数据管理器未初始化");
        // 🆕 修复：尝试重新创建作动器视图模型
        AddLogEntry("INFO", "🔄 尝试重新创建作动器视图模型...");
        actuatorViewModel1_2_ = std::make_unique<ActuatorViewModel1_2>();
        if (actuatorViewModel1_2_) {
            // 重新连接信号
            connectActuatorViewModelSignals();
            AddLogEntry("INFO", "✅ 作动器视图模型重新创建成功");
            syncSuccess = true; // 重新创建成功，同步状态改为成功
        } else {
            AddLogEntry("ERROR", "❌ 作动器视图模型重新创建失败");
            syncSuccess = false;
        }
    }
    
    // 验证控制通道数据管理器
    if (ctrlChanDataManager_) {
        AddLogEntry("DEBUG", "✅ 控制通道数据管理器状态正常");
    } else {
        AddLogEntry("WARNING", "⚠️ 控制通道数据管理器未初始化");
        syncSuccess = false;
    }
    
    // 验证硬件节点数据管理器
    if (hardwareNodeResDataManager_) {
        AddLogEntry("DEBUG", "✅ 硬件节点数据管理器状态正常");
    } else {
        AddLogEntry("WARNING", "⚠️ 硬件节点数据管理器未初始化");
        syncSuccess = false;
    }
    
    AddLogEntry("DEBUG", QString("🔄 数据管理器同步验证完成，结果：%1").arg(syncSuccess ? "成功" : "失败"));
    return syncSuccess;
}

/**
 * @brief 重新初始化详细信息面板
 */
void CMyMainWindow::reinitializeDetailInfoPanel() {
    AddLogEntry("DEBUG", "🔄 重新初始化详细信息面板...");
    
    qDebug() << __FUNCTION__ << __LINE__ << "CMyMainWindow::reinitializeDetailInfoPanel()\r\n\r\n\r\n\r\n\r\n\r\n";

    // 清理现有面板
    if (detailInfoPanel_) {
        detailInfoPanel_->clearInfo();
        qDebug() << __FUNCTION__ << __LINE__ << "detailInfoPanel_->deleteLater()\r\n\r\n\r\n\r\n\r\n\r\n";
    }
    else
    {
        detailInfoPanel_ = std::make_unique<DetailInfoPanel>(ui->detailInfoWidget);
        // 重新创建面板
        if (ui->detailInfoWidget) {
            // 🆕 修复：检查并清理现有子控件，但不删除UI文件中的布局
            QList<QWidget*> childWidgets = ui->detailInfoWidget->findChildren<QWidget*>(QString(), Qt::FindDirectChildrenOnly);
            for (QWidget* child : childWidgets) {
                AddLogEntry("DEBUG", QString("🔄 清理子控件：%1").arg(child->objectName()));
                child->setParent(nullptr);
                child->deleteLater();
            }

            detailInfoPanel_ = std::make_unique<DetailInfoPanel>(ui->detailInfoWidget);

            if (detailInfoPanel_) {
                // 🆕 修复：直接添加到现有的UI布局中，不创建新布局
                // 获取UI文件中的布局
                QLayout* existingLayout = ui->detailInfoWidget->layout();
                if (existingLayout) {
                    existingLayout->addWidget(detailInfoPanel_.get());
                    AddLogEntry("DEBUG", "✅ 已添加到现有UI布局");
                } else {
                    // 如果没有现有布局，才创建新布局（这种情况不应该发生）
                    AddLogEntry("WARNING", "⚠️ 未找到现有布局，创建新布局");
                    QVBoxLayout* layout = new QVBoxLayout(ui->detailInfoWidget);
                    layout->setContentsMargins(0, 0, 0, 0);
                    layout->setSpacing(0);
                    layout->addWidget(detailInfoPanel_.get());
                }

                // 连接信号
                connectDetailInfoPanelSignals();

                // 🆕 修改：重新初始化后不自动设置控件状态，保持用户之前设置
                // 只设置面板本身可见，不改变其他控件的状态
                detailInfoPanel_->setVisible(true);

                AddLogEntry("INFO", "详细信息面板重新初始化成功");
            } else {
                AddLogEntry("ERROR", "详细信息面板重新创建失败");
            }
        } else {
            AddLogEntry("ERROR", "详细信息面板容器未找到");
        }
    }
}

// ============================================================================
// 🆕 新增：编辑组相关方法实现
// ============================================================================

/**
 * @brief 编辑作动器组
 * @param item 作动器组树形项目
 */
void CMyMainWindow::OnEditActuatorGroup(QTreeWidgetItem* item) {
    if (!deviceManager_) {
        AddLogEntry("ERROR", "DeviceManager未初始化，无法编辑作动器组");
        return;
    }
    
    // 委托给DeviceManager处理
    deviceManager_->editActuatorGroup(item);
}

/**
 * @brief 编辑传感器组
 * @param item 传感器组树形项目
 */
void CMyMainWindow::OnEditSensorGroup(QTreeWidgetItem* item) {
    if (!deviceManager_) {
        AddLogEntry("ERROR", "DeviceManager未初始化，无法编辑传感器组");
        return;
    }
    
    // 委托给DeviceManager处理
    deviceManager_->editSensorGroup(item);
}

/**
 * @brief 验证工程状态完整性
 */
void CMyMainWindow::validateProjectState() {
    AddLogEntry("DEBUG", "🔍 开始验证工程状态完整性...");
    
    bool isValid = true;
    QStringList issues;
    
    // 验证数据管理器状态
    if (!sensorDataManager_) {
        isValid = false;
        issues << "传感器数据管理器未初始化";
    }
    
    if (!actuatorViewModel1_2_) {
        isValid = false;
        issues << "作动器数据管理器未初始化";
    }
    
    if (!ctrlChanDataManager_) {
        isValid = false;
        issues << "控制通道数据管理器未初始化";
    }
    
    if (!hardwareNodeResDataManager_) {
        isValid = false;
        issues << "硬件节点数据管理器未初始化";
    }
    
    // 验证树形控件数据
    if (ui->hardwareTreeWidget && ui->hardwareTreeWidget->topLevelItemCount() == 0) {
        isValid = false;
        issues << "硬件树数据为空";
    }
    
    if (ui->testConfigTreeWidget && ui->testConfigTreeWidget->topLevelItemCount() == 0) {
        isValid = false;
        issues << "试验配置树数据为空";
    }
    
    // 验证详细信息面板
    if (!detailInfoPanel_) {
        isValid = false;
        issues << "详细信息面板未初始化";
    }
    
    // 记录验证结果
    if (isValid) {
        AddLogEntry("INFO", "✅ 工程状态验证通过，详细信息功能就绪");
    } else {
        AddLogEntry("WARNING", QString("⚠️ 工程状态验证发现问题：%1").arg(issues.join("; ")));
        
        // 显示警告信息
        QMessageBox::warning(this, tr("工程状态警告"), 
            tr("工程加载完成，但发现以下问题：\n%1\n\n详细信息功能可能无法正常工作。")
            .arg(issues.join("\n")));
    }
}
