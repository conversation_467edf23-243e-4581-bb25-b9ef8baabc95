@echo off
echo ========================================
echo Fix Encoding Regression
echo ========================================
echo.

echo [INFO] Encoding regression fix:
echo   - Removed problematic qDebug console output
echo   - Restored original AddLogEntry behavior
echo   - Removed Qt locale codec changes that caused issues
echo   - Kept only minimal console code page setting
echo.
echo [ANALYSIS] The issue was caused by:
echo   1. Adding qDebug output in AddLogEntry (interfered with UI log encoding)
echo   2. Changing Qt's locale codec (affected internal string handling)
echo   3. The original UI log display was working fine!
echo.

REM Find Qt installation
set QT_FOUND=0

REM Check user's actual Qt path first
if exist "D:\Qt\Qt5.14.2\5.14.2\mingw73_32\bin\qmake.exe" (
    set QTDIR=D:\Qt\Qt5.14.2\5.14.2\mingw73_32
    set MINGW_PATH=D:\Qt\Qt5.14.2\Tools\mingw730_32\bin
    set QT_FOUND=1
    echo Found Qt at D:\Qt\Qt5.14.2\5.14.2\mingw73_32
)

if %QT_FOUND%==0 (
    if exist "C:\Qt\5.14.2\mingw73_32\bin\qmake.exe" (
        set QTDIR=C:\Qt\5.14.2\mingw73_32
        set MINGW_PATH=C:\Qt\Tools\mingw730_32\bin
        set QT_FOUND=1
        echo Found Qt at C:\Qt\5.14.2\mingw73_32
    )
)

if %QT_FOUND%==0 (
    if exist "C:\Qt\Qt5.14.2\5.14.2\mingw73_32\bin\qmake.exe" (
        set QTDIR=C:\Qt\Qt5.14.2\5.14.2\mingw73_32
        set MINGW_PATH=C:\Qt\Qt5.14.2\Tools\mingw730_32\bin
        set QT_FOUND=1
        echo Found Qt at C:\Qt\Qt5.14.2\5.14.2\mingw73_32
    )
)

if %QT_FOUND%==0 (
    echo ERROR: Qt not found!
    pause
    exit /b 1
)

set PATH=%QTDIR%\bin;%MINGW_PATH%;%PATH%
echo Qt environment set: %QTDIR%
echo.

cd /d "%~dp0\SiteResConfig"

echo Cleaning and compiling...
mingw32-make clean > nul 2>&1
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo ERROR: qmake failed!
    pause
    exit /b 1
)

mingw32-make -j4
if errorlevel 1 (
    echo ERROR: Compilation failed!
    pause
    exit /b 1
)

echo.
echo SUCCESS: Encoding regression fix compiled!
echo.
echo [CHANGES MADE]
echo   ✅ Removed qDebug console output from AddLogEntry
echo   ✅ Removed Qt locale codec changes
echo   ✅ Kept minimal console code page setting
echo   ✅ Preserved all functional fixes (deadlock, validation, etc.)
echo.
echo Starting application...
if exist "debug\SiteResConfig.exe" (
    start "" "debug\SiteResConfig.exe"
    echo Application started - Chinese characters should display correctly now
) else (
    echo ERROR: Executable not found
)

echo.
echo [TEST VERIFICATION]
echo 1. Check UI log panel - Chinese characters should be clear
echo 2. Import test file and verify log messages are readable
echo 3. Confirm all previous fixes still work (no deadlock, successful import)
echo.
echo [EXPECTED RESULT]
echo - UI log shows: [INFO] 系统初始化完成
echo - UI log shows: [INFO] 控制通道组已创建: ID=1
echo - No more garbled characters like: ÍÏ×§¹¦ÄÜÒÑÆôÓÃ
echo.
pause
