# 📊 作动器XLSX存储格式 - 基于修改后界面

## 🔍 界面修改分析

基于对修改后的"新建作动器"界面分析，发现以下结构：

### ActuatorParams结构体 (保持不变)
```cpp
struct ActuatorParams {
    // 基本信息
    QString serialNumber;      // 序列号
    QString type;             // 类型（单出杆/双出杆）
    
    // 截面数据 (Actuator基本参数)
    QString unitLength;       // 单位长度
    double stroke;            // 行程 (m)
    double displacement;      // 位移 (m)
    double tensionArea;       // 拉伸面积 (m²)
    double compressionArea;   // 压缩面积 (m²)
    
    // 伺服控制器参数 (Value)
    QString polarity;         // 极性（Positive/Negative）
    double dither;            // Dither/Deliver值 (V)
    double frequency;         // 频率 (Hz)
    double outputMultiplier;  // 输出倍数
    double balance;           // 平衡值 (V)
    
    // 物理参数 (计算得出)
    double cylinderDiameter;  // 缸径 (m)
    double rodDiameter;       // 杆径 (m)
};
```

### 界面控件映射
- `serialEdit` → serialNumber
- `typeCombo` → type (单出杆/双出杆)
- `unitLengthCombo` → unitLength
- `strokeSpinBox` → stroke
- `displacementSpinBox` → displacement
- `tensionAreaSpinBox` → tensionArea
- `compressionAreaSpinBox` → compressionArea
- `polarityCombo` → polarity
- `ditherSpinBox` → dither
- `frequencySpinBox` → frequency
- `multiplierSpinBox` → outputMultiplier
- `balanceSpinBox` → balance

## 📋 XLSX存储格式设计

### 工作表：作动器配置表 (Actuator_Configuration)

#### 表头信息区域 (A1:N4)
```
A1: 作动器配置数据表                    | B1-N1: [合并单元格]
A2: 导出时间: 2025-08-14 15:30:00      | B2-N2: [合并单元格]
A3: 说明: 包含完整的作动器参数配置信息    | B3-N3: [合并单元格]
A4: [空行]                            | B4-N4: [空行]
```

#### 数据表头 (A5:N5)
```
A5: 序号
B5: 序列号
C5: 类型
D5: 单位长度
E5: 行程(m)
F5: 位移(m)
G5: 拉伸面积(m²)
H5: 压缩面积(m²)
I5: 极性
J5: Deliver(V)
K5: 频率(Hz)
L5: 输出倍数
M5: 平衡(V)
N5: 备注
```

#### 数据行格式 (A6开始)
```
A6: 1
B6: ACT_001
C6: 单出杆
D6: m
E6: 0.30
F6: 0.30
G6: 0.60
H6: 0.50
I6: Positive
J6: 0.100
K6: 100.00
L6: 1.000
M6: 0.000
N6: 液压系统主作动器
```

## 📊 详细字段规格

### 基本信息字段
| 列 | 字段名 | 数据类型 | 宽度 | 说明 | 示例值 |
|---|--------|----------|------|------|--------|
| A | 序号 | Integer | 6 | 自动递增序号 | 1 |
| B | 序列号 | String | 15 | 作动器唯一标识 | "ACT_001" |
| C | 类型 | String | 10 | 单出杆/双出杆 | "单出杆" |
| N | 备注 | String | 25 | 附加说明信息 | "液压系统主作动器" |

### 截面数据字段
| 列 | 字段名 | 数据类型 | 单位 | 精度 | 范围 | 示例值 |
|---|--------|----------|------|------|------|--------|
| D | 单位长度 | String | - | - | m/mm/cm/inch | "m" |
| E | 行程 | Double | m | 2位小数 | 0.01-10.00 | 0.30 |
| F | 位移 | Double | m | 2位小数 | 0.01-10.00 | 0.30 |
| G | 拉伸面积 | Double | m² | 2位小数 | 0.01-100.00 | 0.60 |
| H | 压缩面积 | Double | m² | 2位小数 | 0.01-100.00 | 0.50 |

### 伺服控制器参数字段
| 列 | 字段名 | 数据类型 | 单位 | 精度 | 范围 | 示例值 |
|---|--------|----------|------|------|------|--------|
| I | 极性 | String | - | - | Positive/Negative | "Positive" |
| J | Deliver | Double | V | 3位小数 | -10.000~10.000 | 0.100 |
| K | 频率 | Double | Hz | 2位小数 | 1.00-10000.00 | 100.00 |
| L | 输出倍数 | Double | - | 3位小数 | 0.001-1000.000 | 1.000 |
| M | 平衡 | Double | V | 3位小数 | -10.000~10.000 | 0.000 |

## 🎨 格式样式

### 表头样式 (A5:N5)
- **背景色**: #4472C4 (深蓝色)
- **字体色**: 白色
- **字体**: 微软雅黑, 11pt, 粗体
- **对齐**: 居中对齐
- **边框**: 全边框, 白色, 1pt

### 数据行样式 (A6开始)
- **奇数行背景**: #F8F9FA (浅灰色)
- **偶数行背景**: 白色
- **字体**: 微软雅黑, 10pt, 常规
- **数值对齐**: 右对齐
- **文本对齐**: 左对齐
- **边框**: 全边框, 灰色, 0.5pt

### 列宽设置
```
A列(序号): 6
B列(序列号): 15
C列(类型): 10
D列(单位长度): 10
E列(行程): 10
F列(位移): 10
G列(拉伸面积): 12
H列(压缩面积): 12
I列(极性): 10
J列(Deliver): 12
K列(频率): 10
L列(输出倍数): 12
M列(平衡): 10
N列(备注): 25
```

## 📤 导出示例数据

### 完整数据行示例
```
序号 | 序列号      | 类型   | 单位长度 | 行程  | 位移  | 拉伸面积 | 压缩面积 | 极性     | Deliver | 频率    | 输出倍数 | 平衡  | 备注
-----|------------|--------|----------|-------|-------|----------|----------|----------|---------|---------|----------|-------|----------------
1    | ACT_HYD_01 | 单出杆 | m        | 0.30  | 0.30  | 0.60     | 0.50     | Positive | 0.100   | 100.00  | 1.000    | 0.000 | 液压系统主作动器
2    | ACT_HYD_02 | 双出杆 | m        | 0.25  | 0.25  | 0.45     | 0.45     | Positive | 0.080   | 120.00  | 1.000    | 0.000 | 液压系统副作动器
3    | ACT_ELE_01 | 单出杆 | m        | 0.20  | 0.20  | 0.30     | 0.25     | Positive | 0.050   | 1000.00 | 1.000    | 0.000 | 电动精密作动器
4    | ACT_PNE_01 | 单出杆 | m        | 0.15  | 0.15  | 0.20     | 0.18     | Positive | 0.200   | 50.00   | 1.000    | 0.000 | 气动快速作动器
```

## 🔄 数据验证规则

### 必填字段
- 序列号: 不能为空，必须唯一
- 类型: 必须为"单出杆"或"双出杆"
- 行程: 必须大于0
- 拉伸面积: 必须大于0
- 压缩面积: 必须大于0

### 逻辑验证
- 压缩面积 ≤ 拉伸面积 (单出杆)
- 压缩面积 = 拉伸面积 (双出杆)
- 位移 ≤ 行程

### 数值范围
- 行程: 0.01 - 10.00 m
- 位移: 0.01 - 10.00 m
- 拉伸面积: 0.01 - 100.00 m²
- 压缩面积: 0.01 - 100.00 m²
- Deliver: -10.000 - 10.000 V
- 频率: 1.00 - 10000.00 Hz
- 输出倍数: 0.001 - 1000.000
- 平衡: -10.000 - 10.000 V

## 📁 文件命名规范

```
SiteResConfig_Actuators_YYYYMMDD_HHMMSS.xlsx
```

示例: `SiteResConfig_Actuators_20250814_153000.xlsx`

## 🎯 存储格式特点

### 简化设计
- ✅ 移除了计算字段（缸径、杆径）
- ✅ 专注于用户输入的核心参数
- ✅ 14列紧凑布局

### 数据完整性
- ✅ 包含所有ActuatorParams字段
- ✅ 支持单出杆/双出杆类型
- ✅ 完整的伺服控制器参数

### 实用性
- ✅ 标准XLSX格式
- ✅ Excel完全兼容
- ✅ 支持数据验证
- ✅ 专业表格样式

这个存储格式完全基于修改后的"新建作动器"界面结构，专注于作动器的核心配置参数，提供简洁、实用的XLSX存储方案。
