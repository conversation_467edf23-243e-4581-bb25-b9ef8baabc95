/**
 * @file test_control_channel_detail.cpp
 * @brief 控制通道详细信息显示功能测试
 * @details 测试当选择控制通道时显示详细信息和所有子节点信息的功能
 * <AUTHOR> Assistant
 * @date 2025-01-27
 * @version 1.0.0
 */

#include <QApplication>
#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QMessageBox>
#include <QDebug>
#include <QTreeWidget>
#include <QTreeWidgetItem>
#include <QDockWidget>

#include "DetailInfoPanel.h"
#include "DataModels_Fixed.h"

// 测试用的主窗口类
class TestControlChannelWindow : public QMainWindow
{
    Q_OBJECT
    
public:
    TestControlChannelWindow(QWidget *parent = nullptr) : QMainWindow(parent)
    {
        setupUI();
        setupTestData();
        setupConnections();
    }
    
private slots:
    void onTestCH1Clicked()
    {
        testControlChannel("CH1");
    }
    
    void onTestCH2Clicked()
    {
        testControlChannel("CH2");
    }
    
    void onClearClicked()
    {
        m_detailInfoPanel->clearInfo();
        QMessageBox::information(this, "测试", "已清空详细信息");
    }
    
private:
    void setupUI()
    {
        setWindowTitle("控制通道详细信息显示测试");
        setMinimumSize(1200, 800);
        resize(1400, 900);
        
        // 创建中央窗口
        QWidget* centralWidget = new QWidget(this);
        setCentralWidget(centralWidget);
        
        QVBoxLayout* centralLayout = new QVBoxLayout(centralWidget);
        
        // 创建测试按钮
        QHBoxLayout* buttonLayout = new QHBoxLayout();
        
        QPushButton* testCH1Button = new QPushButton("测试CH1详细信息", this);
        QPushButton* testCH2Button = new QPushButton("测试CH2详细信息", this);
        QPushButton* clearButton = new QPushButton("清空信息", this);
        
        buttonLayout->addWidget(testCH1Button);
        buttonLayout->addWidget(testCH2Button);
        buttonLayout->addWidget(clearButton);
        buttonLayout->addStretch();
        
        centralLayout->addLayout(buttonLayout);
        
        // 连接按钮信号
        connect(testCH1Button, &QPushButton::clicked, this, &TestControlChannelWindow::onTestCH1Clicked);
        connect(testCH2Button, &QPushButton::clicked, this, &TestControlChannelWindow::onTestCH2Clicked);
        connect(clearButton, &QPushButton::clicked, this, &TestControlChannelWindow::onClearClicked);
        
        // 创建详细信息面板
        m_detailInfoPanel = new DetailInfoPanel(this);
        
        // 创建停靠窗口
        QDockWidget* detailDock = new QDockWidget("控制通道详细信息", this);
        detailDock->setWidget(m_detailInfoPanel);
        detailDock->setAllowedAreas(Qt::RightDockWidgetArea | Qt::LeftDockWidgetArea);
        addDockWidget(Qt::RightDockWidgetArea, detailDock);
        
        // 创建模拟树形控件
        QTreeWidget* testTree = new QTreeWidget(this);
        testTree->setHeaderLabels(QStringList() << "节点名称" << "类型" << "状态");
        testTree->setAlternatingRowColors(true);
        
        // 添加测试节点
        QTreeWidgetItem* rootItem = new QTreeWidgetItem(testTree);
        rootItem->setText(0, "试验资源");
        rootItem->setExpanded(true);
        
        QTreeWidgetItem* controlChannelItem = new QTreeWidgetItem(rootItem);
        controlChannelItem->setText(0, "控制通道");
        controlChannelItem->setText(1, "控制通道组");
        controlChannelItem->setExpanded(true);
        
        QTreeWidgetItem* ch1Item = new QTreeWidgetItem(controlChannelItem);
        ch1Item->setText(0, "CH1");
        ch1Item->setText(1, "控制通道");
        ch1Item->setText(2, "在线");
        
        QTreeWidgetItem* ch2Item = new QTreeWidgetItem(controlChannelItem);
        ch2Item->setText(0, "CH2");
        ch2Item->setText(1, "控制通道");
        ch2Item->setText(2, "在线");
        
        centralLayout->addWidget(testTree);
        
        // 连接树形控件选择信号
        connect(testTree, &QTreeWidget::itemClicked, this, &TestControlChannelWindow::onTreeItemClicked);
    }
    
    void setupTestData()
    {
        // 初始状态为空
        m_detailInfoPanel->clearInfo();
    }
    
    void setupConnections()
    {
        // 连接详细信息面板信号
        connect(m_detailInfoPanel, &DetailInfoPanel::editConfigRequested,
                this, &TestControlChannelWindow::onEditConfigRequested);
        connect(m_detailInfoPanel, &DetailInfoPanel::viewHistoryRequested,
                this, &TestControlChannelWindow::onViewHistoryRequested);
        connect(m_detailInfoPanel, &DetailInfoPanel::exportInfoRequested,
                this, &TestControlChannelWindow::onExportInfoRequested);
    }
    
    void onTreeItemClicked(QTreeWidgetItem* item, int column)
    {
        Q_UNUSED(column)
        
        QString itemText = item->text(0);
        QString itemType = item->text(1);
        
        if (itemType == "控制通道" || itemText == "CH1" || itemText == "CH2") {
            testControlChannel(itemText);
        }
    }
    
    void testControlChannel(const QString& channelName)
    {
        qDebug() << "测试控制通道详细信息显示:" << channelName;
        
        // 创建测试用的控制通道参数
        UI::ControlChannelParams testParams;
        testParams.channelId = channelName.toStdString();
        testParams.channelName = channelName.toStdString();
        testParams.hardwareAssociation = "LD-B1 - CH1";
        testParams.load1Sensor = "载荷传感器组 - 载荷传感器_001";
        testParams.load2Sensor = "载荷传感器组 - 载荷传感器_002";
        testParams.positionSensor = "位置传感器组 - 位置传感器_001";
        testParams.controlActuator = "伺服作动器组 - 伺服作动器1_1";
        testParams.lc_id = 1;
        testParams.station_id = 1;
        testParams.enable = true;
        testParams.control_mode = 4; // 力控制
        testParams.servo_control_polarity = 1;
        testParams.payload_sensor1_polarity = 1;
        testParams.payload_sensor2_polarity = 1;
        testParams.position_sensor_polarity = 1;
        
        // 使用DetailInfoPanel的静态方法创建NodeInfo
        NodeInfo nodeInfo = DetailInfoPanel::createControlChannelNodeInfo(
            channelName, 
            QString::fromStdString(testParams.channelId), 
            testParams
        );
        
        // 设置详细信息到面板
        m_detailInfoPanel->setNodeInfo(nodeInfo);
        
        QMessageBox::information(this, "测试", 
            QString("已显示控制通道 %1 的详细信息，包含 %2 个子节点")
            .arg(channelName).arg(nodeInfo.subNodes.size()));
    }
    
    void onEditConfigRequested(const NodeInfo& nodeInfo)
    {
        QMessageBox::information(this, "编辑配置", 
            QString("请求编辑节点配置: %1").arg(nodeInfo.nodeName));
    }
    
    void onViewHistoryRequested(const NodeInfo& nodeInfo)
    {
        QMessageBox::information(this, "查看历史", 
            QString("请求查看节点历史: %1").arg(nodeInfo.nodeName));
    }
    
    void onExportInfoRequested(const NodeInfo& nodeInfo)
    {
        QMessageBox::information(this, "导出信息", 
            QString("请求导出节点信息: %1").arg(nodeInfo.nodeName));
    }
    
private:
    DetailInfoPanel* m_detailInfoPanel;
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    TestControlChannelWindow window;
    window.show();
    
    return app.exec();
}

#include "test_control_channel_detail.moc" 