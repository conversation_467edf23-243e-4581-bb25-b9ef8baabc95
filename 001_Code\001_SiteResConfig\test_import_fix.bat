@echo off
chcp 65001 > nul
echo ========================================
echo 测试工程导入修复效果 v2.0
echo ========================================
echo.

echo [INFO] 编译项目...
cd SiteResConfig
qmake SiteResConfig_Simple.pro
mingw32-make clean
mingw32-make

if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] 编译失败！
    pause
    exit /b 1
)

echo.
echo [INFO] 编译成功！
echo.
echo [INFO] 启动应用程序进行测试...
echo.
echo [修复内容]
echo   1. 简化了控制台编码设置（避免权限问题）
echo   2. 简化了界面刷新机制（避免卡死）
echo   3. 改进了异常处理和错误恢复
echo   4. 添加了多重编码尝试机制
echo.
echo [测试步骤]
echo   1. 打开工程文件: C:\Users\<USER>\Desktop\20250818152156_实验工程.xlsx
echo   2. 观察是否显示进度对话框
echo   3. 观察导入是否不再卡死
echo   4. 检查导入完成后的提示对话框
echo.

start "" "debug\SiteResConfig.exe"

echo [INFO] 应用程序已启动
echo [INFO] 请按照上述步骤测试工程导入功能
echo.
pause
