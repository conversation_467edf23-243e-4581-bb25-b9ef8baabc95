@echo off
chcp 65001 >nul
echo ========================================
echo  作动器工作表功能测试
echo ========================================
echo.

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile* >nul 2>&1
if exist "debug" rmdir /s /q debug >nul 2>&1
if exist "release" rmdir /s /q release >nul 2>&1
if exist "*.o" del *.o >nul 2>&1
if exist "ui_*.h" del ui_*.h >nul 2>&1

echo.
echo 检查作动器工作表相关代码修改...
echo.

echo 1. 检查作动器工作表方法声明...
findstr /C:"createActuatorWorksheet" "include\XLSDataExporter.h" >nul
if errorlevel 1 (
    echo ❌ createActuatorWorksheet方法未声明
    goto :error
) else (
    echo ✅ createActuatorWorksheet方法已声明
)

findstr /C:"addActuatorWorksheetToExcel" "include\XLSDataExporter.h" >nul
if errorlevel 1 (
    echo ❌ addActuatorWorksheetToExcel方法未声明
    goto :error
) else (
    echo ✅ addActuatorWorksheetToExcel方法已声明
)

findstr /C:"exportCompleteProjectWithActuators" "include\XLSDataExporter.h" >nul
if errorlevel 1 (
    echo ❌ exportCompleteProjectWithActuators方法未声明
    goto :error
) else (
    echo ✅ exportCompleteProjectWithActuators方法已声明
)

echo.
echo 2. 检查作动器工作表方法实现...
findstr /C:"createActuatorWorksheet" "src\XLSDataExporter.cpp" >nul
if errorlevel 1 (
    echo ❌ createActuatorWorksheet方法未实现
    goto :error
) else (
    echo ✅ createActuatorWorksheet方法已实现
)

findstr /C:"writeActuatorWorksheetHeader" "src\XLSDataExporter.cpp" >nul
if errorlevel 1 (
    echo ❌ writeActuatorWorksheetHeader方法未实现
    goto :error
) else (
    echo ✅ writeActuatorWorksheetHeader方法已实现
)

findstr /C:"applyActuatorWorksheetStyles" "src\XLSDataExporter.cpp" >nul
if errorlevel 1 (
    echo ❌ applyActuatorWorksheetStyles方法未实现
    goto :error
) else (
    echo ✅ applyActuatorWorksheetStyles方法已实现
)

echo.
echo 3. 检查作动器工作表名称...
findstr /C:"作动器" "src\XLSDataExporter.cpp" >nul
if errorlevel 1 (
    echo ❌ 作动器工作表名称未设置
    goto :error
) else (
    echo ✅ 作动器工作表名称已设置
)

echo.
echo 生成UI头文件...
uic ui\ActuatorDialog.ui -o ui_ActuatorDialog.h >nul 2>&1
if errorlevel 1 (
    echo ❌ ActuatorDialog UI文件生成失败
    goto :error
) else (
    echo ✅ ActuatorDialog UI头文件生成成功
)

uic ui\MainWindow.ui -o ui_MainWindow.h >nul 2>&1
if errorlevel 1 (
    echo ❌ MainWindow UI文件生成失败
    goto :error
) else (
    echo ✅ MainWindow UI头文件生成成功
)

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++ >nul 2>&1
if errorlevel 1 (
    echo ❌ qmake失败
    goto :error
) else (
    echo ✅ Makefile生成成功
)

echo.
echo 开始编译测试...
echo 这可能需要几分钟时间，请耐心等待...
echo.

mingw32-make clean >nul 2>&1
mingw32-make -j4 2>compile_errors.txt
if errorlevel 1 (
    echo ❌ 编译失败！
    echo.
    echo 编译错误信息：
    type compile_errors.txt
    echo.
    echo 请检查上述错误信息并进行修复。
    pause
    exit /b 1
) else (
    echo ✅ 编译成功！
    
    if exist compile_errors.txt del compile_errors.txt >nul 2>&1
    
    echo.
    echo ========================================
    echo  🎉 作动器工作表功能实现成功！
    echo ========================================
    
    if exist "SiteResConfig.exe" (
        echo.
        echo 📊 编译结果:
        echo ├─ 可执行文件: SiteResConfig.exe
        echo ├─ 文件大小: 
        for %%F in (SiteResConfig.exe) do echo │  └─ %%~zF 字节
        echo └─ 修改时间: 
        for %%F in (SiteResConfig.exe) do echo    └─ %%~tF
        echo.
        echo 🎯 作动器工作表功能特性:
        echo.
        echo 📋 工作表管理:
        echo ├─ ✅ 独立的"作动器"工作表
        echo ├─ ✅ 专用的作动器存储格式
        echo ├─ ✅ 17列完整数据布局
        echo └─ ✅ 专业的表头和样式设计
        echo.
        echo 🔧 核心方法:
        echo ├─ ✅ createActuatorWorksheet
        echo │  └─ 在Excel文档中创建作动器工作表
        echo ├─ ✅ addActuatorWorksheetToExcel
        echo │  └─ 向现有Excel文件添加作动器工作表
        echo ├─ ✅ exportCompleteProjectWithActuators
        echo │  └─ 导出完整项目（硬件树+作动器）
        echo ├─ ✅ writeActuatorWorksheetHeader
        echo │  └─ 写入作动器工作表专用表头
        echo └─ ✅ applyActuatorWorksheetStyles
        echo    └─ 应用作动器工作表专用样式
        echo.
        echo 📊 存储格式特点:
        echo ├─ ✅ 工作表名称: "作动器"
        echo ├─ ✅ 17列完整布局:
        echo │  ├─ 组序号、作动器组名称
        echo │  ├─ 作动器序号、作动器序列号
        echo │  ├─ 作动器类型、Unit类型、Unit名称
        echo │  ├─ 行程、位移、拉伸面积、压缩面积
        echo │  ├─ 极性、Deliver、频率、输出倍数、平衡
        echo │  └─ 备注信息
        echo ├─ ✅ 分组显示优化:
        echo │  ├─ 组名称只在第一行显示
        echo │  ├─ 组内作动器独立编号
        echo │  └─ 专业的分组样式
        echo └─ ✅ 专用样式设计:
        echo    ├─ 深蓝色表头背景
        echo    ├─ 浅蓝色分组行背景
        echo    ├─ 优化的列宽设置
        echo    └─ 完整的边框样式
        echo.
        echo 🎨 表头信息:
        echo ├─ ✅ 标题: "作动器配置数据表"
        echo ├─ ✅ 导出时间: 自动生成时间戳
        echo ├─ ✅ 说明: 详细的功能描述
        echo └─ ✅ 17列专业表头
        echo.
        echo 🔄 使用方式:
        echo ├─ ✅ 单独创建作动器工作表
        echo ├─ ✅ 向现有Excel添加作动器工作表
        echo ├─ ✅ 导出完整项目（硬件树+作动器）
        echo └─ ✅ 支持作动器组层级结构
        echo.
        echo 💾 文件结构:
        echo ├─ 📄 硬件配置工作表 (原有功能)
        echo ├─ 📄 作动器工作表 (新增功能)
        echo │  ├─ 表头信息区域 (1-4行)
        echo │  ├─ 数据表头 (5行)
        echo │  └─ 作动器数据 (6行开始)
        echo └─ 📄 其他工作表 (传感器等)
        echo.
        
        set /p choice="是否启动程序测试作动器工作表功能？(Y/N): "
        if /i "%choice%"=="Y" (
            echo 启动程序...
            start SiteResConfig.exe
        )
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
        set /p choice="是否启动程序测试？(Y/N): "
        if /i "%choice%"=="Y" start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
        set /p choice="是否启动程序测试？(Y/N): "
        if /i "%choice%"=="Y" start release\SiteResConfig.exe
    ) else (
        echo ⚠️ 警告: 找不到可执行文件
    )
)

echo.
echo 📖 作动器工作表功能说明:
echo.
echo 🎯 核心功能:
echo - 在XLSX文件中创建独立的"作动器"工作表
echo - 使用设计好的17列作动器存储格式
echo - 支持作动器组层级结构显示
echo - 专业的表头和样式设计
echo.
echo 🔧 技术特点:
echo - 完整的工作表管理功能
echo - 专用的作动器数据格式
echo - 灵活的导出方式选择
echo - 与现有功能完美集成
echo.
echo 测试完成！
pause
exit /b 0

:error
echo.
echo ❌ 作动器工作表功能测试失败！
echo.
echo 可能的问题：
echo 1. 方法声明或实现缺失
echo 2. 工作表名称设置错误
echo 3. 样式方法实现不完整
echo 4. 编译环境问题
echo.
echo 请检查错误信息并重新修复。
pause
exit /b 1
