@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 编译控制通道详细信息显示修复程序
echo 项目: SiteResConfig_Simple.pro
echo ========================================
echo.

echo 🎯 编译目标：
echo - 项目文件: SiteResConfig_Simple.pro
echo - 修复内容: 控制通道详细信息显示问题
echo - 测试功能: 验证13列数据正确显示
echo.

echo 📋 编译步骤：
echo.

echo 1. 检查项目文件...
if not exist "SiteResConfig_Simple.pro" (
    echo ❌ 错误：找不到项目文件 SiteResConfig_Simple.pro
    echo 请确保在正确的目录下运行此脚本
    pause
    exit /b 1
)
echo ✅ 项目文件检查通过

echo.
echo 2. 清理之前的构建文件...
if exist "build-SiteResConfig_Simple-Desktop_Qt_5_14_2_MinGW_32_bit-Debug" (
    rmdir /s /q "build-SiteResConfig_Simple-Desktop_Qt_5_14_2_MinGW_32_bit-Debug"
    echo ✅ 清理完成
) else (
    echo ℹ️  无需清理
)

echo.
echo 3. 创建构建目录...
mkdir "build-SiteResConfig_Simple-Desktop_Qt_5_14_2_MinGW_32_bit-Debug"
cd "build-SiteResConfig_Simple-Desktop_Qt_5_14_2_MinGW_32_bit-Debug"
echo ✅ 构建目录创建完成

echo.
echo 4. 运行qmake...
qmake ..\SiteResConfig_Simple.pro
if %errorlevel% neq 0 (
    echo ❌ qmake 失败！
    echo 请检查Qt环境配置
    pause
    exit /b 1
)
echo ✅ qmake 成功

echo.
echo 5. 运行make...
mingw32-make
if %errorlevel% neq 0 (
    echo ❌ 编译失败！
    echo 请检查代码语法错误
    pause
    exit /b 1
)
echo ✅ 编译成功

echo.
echo 6. 检查可执行文件...
if exist "debug\SiteResConfig_Simple.exe" (
    echo ✅ 可执行文件生成成功
    echo 📁 位置: %cd%\debug\SiteResConfig_Simple.exe
) else (
    echo ❌ 可执行文件生成失败
    pause
    exit /b 1
)

echo.
echo ========================================
echo 🎉 编译完成！
echo ========================================
echo.
echo 🚀 运行程序：
echo cd debug
echo SiteResConfig_Simple.exe
echo.
echo 🧪 测试步骤：
echo 1. 启动程序
echo 2. 点击"🧪 测试修复后的信息创建"按钮
echo 3. 检查控制通道详细信息是否正确显示13列数据
echo.
echo 📊 验证内容：
echo - 载荷1传感器选择列显示正确
echo - 载荷2传感器选择列显示正确
echo - 位置传感器选择列显示正确
echo - 控制作动器选择列显示正确
echo - 设备关联状态清晰显示
echo.

pause 