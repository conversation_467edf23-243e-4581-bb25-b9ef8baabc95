@echo off
echo ========================================
echo  完整作动器管理系统演示
echo ========================================

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o
if exist "ui_*.h" del ui_*.h

echo.
echo 生成UI头文件...
uic ui\MainWindow.ui -o ui_MainWindow.h
if errorlevel 1 (
    echo UI文件生成失败！
    pause
    exit /b 1
) else (
    echo ✅ UI头文件生成成功: ui_MainWindow.h
)

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！完整作动器管理系统已实现
    echo ========================================
    
    if exist "SiteResConfig.exe" (
        echo 可执行文件: SiteResConfig.exe
        echo.
        echo ✅ 完整作动器管理系统功能总览！
        echo.
        echo 🌳 初始化后的树形结构:
        echo 任务1
        echo ├─ 作动器 (类型: "作动器") - 初始为空白
        echo ├─ 传感器 (类型: "传感器") - 初始为空白
        echo └─ 硬件节点资源 (类型: "硬件节点资源") - 初始为空白
        echo.
        echo 🌳 用户创建后的完整结构示例:
        echo 任务1
        echo ├─ 作动器 (类型: "作动器")
        echo │  ├─ 50kN_作动器 (类型: "作动器组")
        echo │  │  ├─ 作动器_000001 [单出杆] (类型: "作动器设备")
        echo │  │  ├─ 作动器_000002 [双出杆] (类型: "作动器设备")
        echo │  │  └─ 作动器_000003 [单出杆] (类型: "作动器设备")
        echo │  ├─ 100kN_作动器 (类型: "作动器组")
        echo │  │  ├─ 作动器_000001 [双出杆] (类型: "作动器设备")
        echo │  │  └─ 作动器_000002 [单出杆] (类型: "作动器设备")
        echo │  └─ 200kN_作动器 (类型: "作动器组")
        echo │     └─ 作动器_000001 [单出杆] (类型: "作动器设备")
        echo ├─ 传感器 (类型: "传感器")
        echo └─ 硬件节点资源 (类型: "硬件节点资源")
        echo.
        echo 🖱️ 完整的右键菜单系统:
        echo.
        echo 📁 作动器节点右键:
        echo └─ 新建
        echo    └─ 作动器组 (下拉框选择)
        echo       ├─ 50kN_作动器
        echo       ├─ 100kN_作动器
        echo       ├─ 200kN_作动器
        echo       ├─ 500kN_作动器
        echo       └─ 自定义...
        echo.
        echo 📁 作动器组右键 (如: 100kN_作动器):
        echo └─ 新建
        echo    └─ 作动器 (详细参数输入)
        echo.
        echo 📋 作动器参数输入界面:
        echo ┌─────────────────────────────────────────┐
        echo │          新建作动器                      │
        echo ├─────────────────────────────────────────┤
        echo │     100kN_作动器\作动器_000001          │
        echo ├─────────────────────────────────────────┤
        echo │ 序列号: [作动器_000001            ]     │
        echo │ 类型:   [单出杆 ▼]                     │
        echo │ 缸径:   [0.10] m (精度0.00, 最小值0)   │
        echo │ 杆径:   [0.05] m (精度0.00, 最小值0)   │
        echo │ 行程:   [0.20] m (精度0.00, 最小值0)   │
        echo │                                         │
        echo │              [确定] [取消]              │
        echo └─────────────────────────────────────────┘
        echo.
        echo 🎯 核心功能特色:
        echo.
        echo ✅ 智能编号系统:
        echo - 自动生成唯一编号: 作动器_000001, 作动器_000002...
        echo - 每个组独立计数: 不同组的编号互不影响
        echo - 6位数字格式: 支持最多999999个作动器
        echo - 前导零填充: 保持编号格式统一
        echo.
        echo ✅ 直观显示信息:
        echo - 路径显示: "作动器组\作动器_编号"
        echo - 实时更新: 根据当前组状态动态生成
        echo - 视觉突出: 蓝色粗体字体，居中显示
        echo - 层次清晰: 反斜杠分隔，路径明确
        echo.
        echo ✅ 精确参数控制:
        echo - 缸径: 0.00-10.00 m, 精度0.01 m
        echo - 杆径: 0.00-5.00 m, 精度0.01 m
        echo - 行程: 0.00-2.00 m, 精度0.01 m
        echo - 类型: 单出杆/双出杆下拉选择
        echo - 序列号: 自动生成，可手动修改
        echo.
        echo ✅ 完整信息记录:
        echo - 工具提示: 鼠标悬停显示完整参数
        echo - 日志记录: 详细记录创建信息
        echo - 类型标识: 完整的节点类型体系
        echo - 参数验证: 输入验证和错误提示
        echo.
        echo 🔧 技术架构优势:
        echo - 动态编号: 基于实时数据生成编号
        echo - 类型安全: Qt::UserRole存储类型信息
        echo - 参数精确: QDoubleSpinBox精确控制
        echo - 界面美观: 自定义对话框和样式
        echo - 扩展性好: 易于添加新的参数类型
        echo.
        echo 📊 应用场景示例:
        echo.
        echo 🏭 小型试验室:
        echo - 创建50kN_作动器组
        echo - 添加2-3个精密作动器
        echo - 用于材料拉伸试验
        echo.
        echo 🏗️ 中型实验室:
        echo - 创建100kN_作动器组
        echo - 添加5-8个标准作动器
        echo - 用于构件弯曲试验
        echo.
        echo 🏢 大型试验中心:
        echo - 创建200kN_作动器组和500kN_作动器组
        echo - 添加10+个重载作动器
        echo - 用于大型结构试验
        echo.
        echo 启动程序体验完整作动器管理系统...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
        echo 启动程序...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
        echo 启动程序...
        start release\SiteResConfig.exe
    ) else (
        echo 警告: 找不到可执行文件！
    )
)

echo.
echo 📖 完整作动器管理系统使用指南:
echo.
echo 🎯 推荐操作流程:
echo.
echo 1️⃣ 创建作动器组:
echo   - 右键"作动器" → "新建" → "作动器组"
echo   - 选择"100kN_作动器"或其他规格
echo   - 验证组创建成功
echo.
echo 2️⃣ 批量创建作动器:
echo   - 右键"100kN_作动器"组 → "新建" → "作动器"
echo   - 第1个: 显示"100kN_作动器\作动器_000001"
echo   - 第2个: 显示"100kN_作动器\作动器_000002"
echo   - 第3个: 显示"100kN_作动器\作动器_000003"
echo   - 每个都有独立的参数设置
echo.
echo 3️⃣ 参数配置示例:
echo   作动器_000001:
echo   - 序列号: 作动器_000001
echo   - 类型: 单出杆
echo   - 缸径: 0.10 m
echo   - 杆径: 0.05 m
echo   - 行程: 0.20 m
echo.
echo   作动器_000002:
echo   - 序列号: 作动器_000002
echo   - 类型: 双出杆
echo   - 缸径: 0.12 m
echo   - 杆径: 0.06 m
echo   - 行程: 0.25 m
echo.
echo 🏆 系统优势总结:
echo - 自动化程度高: 编号自动生成，减少人工错误
echo - 信息展示清晰: 路径显示让用户明确当前位置
echo - 参数控制精确: 专业的数值输入控件
echo - 扩展性优秀: 支持无限数量的组和设备
echo - 用户体验佳: 直观的界面和操作流程
echo.
pause
