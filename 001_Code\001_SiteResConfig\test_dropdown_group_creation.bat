@echo off
echo ========================================
echo  下拉框组创建功能测试
echo ========================================

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o
if exist "ui_*.h" del ui_*.h

echo.
echo 生成UI头文件...
uic ui\MainWindow.ui -o ui_MainWindow.h
if errorlevel 1 (
    echo UI文件生成失败！
    pause
    exit /b 1
) else (
    echo ✅ UI头文件生成成功: ui_MainWindow.h
)

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo.
    echo 新增功能：
    echo ✅ 作动器组下拉框选择
    echo ✅ 传感器组下拉框选择
    echo ✅ 硬件节点组下拉框选择
    echo ✅ 自定义选项支持
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！下拉框组创建功能已实现
    echo ========================================
    
    if exist "SiteResConfig.exe" (
        echo 可执行文件: SiteResConfig.exe
        echo.
        echo ✅ 下拉框组创建功能已实现！
        echo.
        echo 📋 作动器组下拉框选项:
        echo ├─ 50kN_作动器
        echo ├─ 100kN_作动器
        echo ├─ 200kN_作动器
        echo ├─ 500kN_作动器
        echo └─ 自定义...
        echo.
        echo 📋 传感器组下拉框选项:
        echo ├─ 力_传感器
        echo ├─ 位移_传感器
        echo ├─ 压力_传感器
        echo ├─ 温度_传感器
        echo └─ 自定义...
        echo.
        echo 📋 硬件节点组下拉框选项:
        echo ├─ 控制器_节点
        echo ├─ 采集卡_节点
        echo ├─ 通信_节点
        echo ├─ 电源_节点
        echo └─ 自定义...
        echo.
        echo 🎯 操作流程:
        echo 1. 右键点击对应节点（作动器/传感器/硬件节点资源）
        echo 2. 选择"新建" → 对应组类型
        echo 3. 在下拉框中选择预定义选项或"自定义..."
        echo 4. 如选择"自定义..."，输入自定义名称
        echo 5. 确认创建组
        echo.
        echo 🎨 用户体验优势:
        echo - 标准化命名: 预定义选项确保命名一致性
        echo - 操作便捷: 下拉选择比手动输入更快
        echo - 灵活性好: 支持自定义选项满足特殊需求
        echo - 错误减少: 避免手动输入的拼写错误
        echo.
        echo 🔧 技术实现:
        echo - QInputDialog::getItem() 下拉框选择
        echo - QInputDialog::getText() 自定义输入
        echo - 预定义选项列表 QStringList
        echo - 条件判断处理不同选择
        echo.
        echo 📊 预定义选项说明:
        echo.
        echo 🔩 作动器组选项:
        echo - 50kN_作动器: 小型精密试验
        echo - 100kN_作动器: 中型常规试验
        echo - 200kN_作动器: 大型结构试验
        echo - 500kN_作动器: 重载极限试验
        echo.
        echo 📡 传感器组选项:
        echo - 力_传感器: 力值测量传感器
        echo - 位移_传感器: 位移测量传感器
        echo - 压力_传感器: 压力测量传感器
        echo - 温度_传感器: 温度测量传感器
        echo.
        echo 💻 硬件节点组选项:
        echo - 控制器_节点: 主控制器设备
        echo - 采集卡_节点: 数据采集设备
        echo - 通信_节点: 通信接口设备
        echo - 电源_节点: 电源管理设备
        echo.
        echo 启动程序测试下拉框组创建功能...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
        echo 启动程序...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
        echo 启动程序...
        start release\SiteResConfig.exe
    ) else (
        echo 警告: 找不到可执行文件！
    )
)

echo.
echo 📖 下拉框组创建测试指南:
echo.
echo 🎯 作动器组测试:
echo 1. 右键"作动器"节点 → "新建" → "作动器组"
echo 2. 验证下拉框显示5个选项
echo 3. 选择"100kN_作动器"，验证组创建成功
echo 4. 选择"自定义..."，输入"液压_作动器"
echo 5. 验证自定义组创建成功
echo.
echo 🎯 传感器组测试:
echo 1. 右键"传感器"节点 → "新建" → "传感器组"
echo 2. 验证下拉框显示传感器选项
echo 3. 选择"力_传感器"，验证组创建成功
echo 4. 测试其他预定义选项
echo.
echo 🎯 硬件节点组测试:
echo 1. 右键"硬件节点资源"节点 → "新建" → "硬件节点组"
echo 2. 验证下拉框显示硬件节点选项
echo 3. 选择"控制器_节点"，验证组创建成功
echo 4. 测试自定义选项功能
echo.
echo 🔍 验证要点:
echo - 下拉框正确显示预定义选项
echo - 选择预定义选项直接创建组
echo - "自定义..."选项弹出输入框
echo - 自定义输入框有合理的默认值
echo - 所有创建的组类型标识正确
echo - 组节点无图标显示
echo - 日志记录操作信息
echo.
pause
