# 🧠 CSV路径记忆功能使用指南

## 🎯 **功能概述**

CSV路径记忆功能会自动记住用户最后一次使用的CSV文件路径，下次创建或打开CSV文件时，自动将该路径设置为默认路径，提供更便捷的用户体验。

## 📁 **配置文件**

路径记忆信息保存在exe同目录下的配置文件中：
```
SiteResConfig.exe所在目录/
└── csv_path_config.ini     # CSV路径配置文件
```

### **配置文件格式**
```ini
[CSV]
LastUsedPath=D:\MyExperiments\CSV_Data
LastUpdateTime=2025-08-11T14:30:25
ConfigVersion=1.0
```

## 🔧 **核心功能方法**

### **1. 路径获取方法**

#### **GetLastUsedCSVPath()**
```cpp
QString lastPath = mainWindow->GetLastUsedCSVPath();
// 返回上次使用的CSV路径，如果无效则返回默认路径
```

#### **GetSmartCSVPath()**
```cpp
QString smartPath = mainWindow->GetSmartCSVPath();
// 智能选择路径：优先使用记忆路径，无效时使用默认路径
```

### **2. 路径保存方法**

#### **SaveLastUsedCSVPath()**
```cpp
mainWindow->SaveLastUsedCSVPath("D:\\MyProjects\\Data");
// 手动保存路径到记忆中
```

#### **UpdateCSVPathMemory()**
```cpp
mainWindow->UpdateCSVPathMemory("/path/to/file.csv");
// 根据文件路径自动更新路径记忆（内部调用）
```

### **3. 配置管理方法**

#### **LoadCSVPathSettings()**
```cpp
bool success = mainWindow->LoadCSVPathSettings();
// 从配置文件加载路径设置
```

#### **SaveCSVPathSettings()**
```cpp
bool success = mainWindow->SaveCSVPathSettings();
// 保存路径设置到配置文件
```

## 🚀 **自动记忆机制**

### **触发条件**
路径记忆会在以下操作后自动更新：

1. **保存项目到CSV** - `SaveProjectToCSV()`
2. **加载CSV项目** - `LoadProjectFromCSV()`
3. **快速保存项目** - `QuickSaveProjectToCSV()`
4. **导出数据到CSV** - `ExportDataToCSV()`

### **记忆规则**
- ✅ **记忆非默认路径** - 只有当用户选择的路径不是默认路径时才记忆
- ✅ **自动提取目录** - 从文件路径自动提取目录路径进行记忆
- ✅ **即时保存** - 路径变化时立即保存到配置文件
- ✅ **有效性验证** - 只记忆存在且可访问的路径

## 📋 **使用场景**

### **场景1：首次使用**
```cpp
// 程序启动时
QString path = mainWindow->GetSmartCSVPath();
// 返回: "D:\SiteResConfig\实验工程" (默认路径)
```

### **场景2：用户自定义路径**
```cpp
// 用户保存文件到自定义位置
QString customPath = "D:\\MyExperiments\\Project_A";
bool success = mainWindow->QuickSaveProjectToCSV(&savedPath, "实验A", true);

// 路径自动记忆
QString rememberedPath = mainWindow->GetLastUsedCSVPath();
// 返回: "D:\MyExperiments\Project_A"
```

### **场景3：下次使用记忆路径**
```cpp
// 下次程序启动或操作时
QString smartPath = mainWindow->GetSmartCSVPath();
// 返回: "D:\MyExperiments\Project_A" (记忆的路径)
```

### **场景4：记忆路径无效时**
```cpp
// 如果记忆的路径被删除或不可访问
QString smartPath = mainWindow->GetSmartCSVPath();
// 自动回退到默认路径: "D:\SiteResConfig\实验工程"
```

## ⚙️ **高级用法**

### **1. 手动设置记忆路径**
```cpp
// 设置特定的工作目录
QString workDir = "D:\\CurrentProject\\Data";
QDir().mkpath(workDir); // 确保目录存在
mainWindow->SaveLastUsedCSVPath(workDir);
```

### **2. 检查配置文件状态**
```cpp
QString configFile = mainWindow->GetCSVPathConfigFile();
if (QFile::exists(configFile)) {
    qDebug() << "配置文件存在:" << configFile;
} else {
    qDebug() << "配置文件不存在，将使用默认设置";
}
```

### **3. 重置路径记忆**
```cpp
// 清空路径记忆，回到默认状态
mainWindow->SaveLastUsedCSVPath("");
// 或删除配置文件
QString configFile = mainWindow->GetCSVPathConfigFile();
QFile::remove(configFile);
```

### **4. 路径验证和修复**
```cpp
QString lastPath = mainWindow->GetLastUsedCSVPath();
if (!QDir(lastPath).exists()) {
    qDebug() << "记忆路径无效，将使用默认路径";
    QString defaultPath = mainWindow->GetDefaultCSVPath();
    mainWindow->SaveLastUsedCSVPath(defaultPath);
}
```

## 🛡️ **错误处理**

### **常见问题和解决方案**

#### **1. 配置文件损坏**
```cpp
// 程序会自动处理配置文件损坏的情况
bool loadSuccess = mainWindow->LoadCSVPathSettings();
if (!loadSuccess) {
    // 自动使用默认设置，不影响程序运行
    qDebug() << "配置文件损坏，使用默认设置";
}
```

#### **2. 记忆路径不存在**
```cpp
// 智能路径会自动处理路径不存在的情况
QString smartPath = mainWindow->GetSmartCSVPath();
// 如果记忆路径不存在，自动回退到默认路径
```

#### **3. 权限问题**
```cpp
// 如果无法写入配置文件
bool saveSuccess = mainWindow->SaveCSVPathSettings();
if (!saveSuccess) {
    qDebug() << "无法保存配置，可能是权限问题";
    // 程序继续运行，只是不保存路径记忆
}
```

## 📊 **配置文件管理**

### **配置文件位置**
- **Windows**: `SiteResConfig.exe同目录\csv_path_config.ini`
- **编码格式**: UTF-8（支持中文路径）
- **格式**: INI格式，易于阅读和编辑

### **配置项说明**
| 配置项 | 说明 | 示例值 |
|--------|------|--------|
| `LastUsedPath` | 上次使用的CSV路径 | `D:\MyProjects\Data` |
| `LastUpdateTime` | 最后更新时间 | `2025-08-11T14:30:25` |
| `ConfigVersion` | 配置版本号 | `1.0` |

### **手动编辑配置文件**
用户可以手动编辑配置文件来设置默认路径：
```ini
[CSV]
LastUsedPath=D:\MyCustomPath\CSV_Files
LastUpdateTime=2025-08-11T15:00:00
ConfigVersion=1.0
```

## 🎯 **最佳实践**

### **1. 项目组织建议**
- 为不同项目创建专门的目录
- 使用有意义的目录名称
- 定期清理过期的项目文件

### **2. 路径管理建议**
- 避免使用过深的目录结构
- 使用英文或中文路径名（避免特殊字符）
- 确保路径具有读写权限

### **3. 备份建议**
- 定期备份重要的CSV文件
- 保存配置文件的副本
- 使用版本控制管理项目文件

## 🔍 **调试和诊断**

### **查看当前设置**
```cpp
qDebug() << "默认路径:" << mainWindow->GetDefaultCSVPath();
qDebug() << "记忆路径:" << mainWindow->GetLastUsedCSVPath();
qDebug() << "智能路径:" << mainWindow->GetSmartCSVPath();
qDebug() << "配置文件:" << mainWindow->GetCSVPathConfigFile();
```

### **测试路径记忆**
```cpp
// 保存测试路径
QString testPath = "D:\\TestPath";
QDir().mkpath(testPath);
mainWindow->SaveLastUsedCSVPath(testPath);

// 验证记忆效果
QString remembered = mainWindow->GetLastUsedCSVPath();
qDebug() << "记忆测试:" << (remembered == testPath ? "成功" : "失败");
```

## ✅ **总结**

CSV路径记忆功能提供了：

1. **智能路径选择** - 自动使用最合适的路径
2. **无缝用户体验** - 记住用户的使用习惯
3. **可靠的存储机制** - 持久化保存路径设置
4. **完善的错误处理** - 优雅处理各种异常情况
5. **灵活的配置管理** - 支持手动和自动配置

这个功能让CSV文件操作变得更加便捷和智能！
