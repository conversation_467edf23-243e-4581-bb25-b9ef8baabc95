/**
 * @file PIDParametersDialog.cpp
 * @brief PID参数设置对话框类实现
 * @details 使用Qt Designer设计的PID参数输入对话框实现
 * <AUTHOR> Assistant
 * @date 2025-08-06
 * @version 1.0.0
 */

#include "PIDParametersDialog.h"
#include "ui_PIDParametersDialog.h"
#include <QtWidgets/QMessageBox>

namespace UI {

PIDParametersDialog::PIDParametersDialog(QWidget* parent)
    : QDialog(parent)
    , ui(new Ui::PIDParametersDialog) {
    
    ui->setupUi(this);
    initializeUI();
    connectSignals();
}

PIDParametersDialog::~PIDParametersDialog() {
    delete ui;
}

void PIDParametersDialog::initializeUI() {
    // 设置窗口大小
    resize(350, 200);
    
    // 设置默认值
    ui->kpLineEdit->setText("1.0");
    ui->kiLineEdit->setText("0.1");
    ui->kdLineEdit->setText("0.01");
}

void PIDParametersDialog::connectSignals() {
    // 重新连接确定按钮，添加验证
    disconnect(ui->okButton, &QPushButton::clicked, this, &QDialog::accept);
    connect(ui->okButton, &QPushButton::clicked, this, &PIDParametersDialog::onAcceptClicked);
}

void PIDParametersDialog::onAcceptClicked() {
    // 直接接受，不进行验证（根据项目要求）
    accept();
}

PIDParameters PIDParametersDialog::getPIDParameters() const {
    PIDParameters params;
    
    bool ok;
    params.kp = ui->kpLineEdit->text().toDouble(&ok);
    if (!ok) params.kp = 1.0;
    
    params.ki = ui->kiLineEdit->text().toDouble(&ok);
    if (!ok) params.ki = 0.1;
    
    params.kd = ui->kdLineEdit->text().toDouble(&ok);
    if (!ok) params.kd = 0.01;
    
    return params;
}

} // namespace UI
