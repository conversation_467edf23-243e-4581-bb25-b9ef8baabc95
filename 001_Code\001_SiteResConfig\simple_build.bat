@echo off
echo Simple Build Script for SiteResConfig
echo =====================================

REM Set Visual Studio environment
call "C:\Program Files (x86)\Microsoft Visual Studio\2017\Community\VC\Auxiliary\Build\vcvars64.bat"

REM Create output directory
if not exist "build" mkdir build
cd build

REM Compile source files
echo Compiling source files...
cl /EHsc /std:c++14 /I..\include ^
   ..\src\Utils_Fixed.cpp ^
   ..\src\DataModels_Simple.cpp ^
   ..\src\ConfigManager_Simple.cpp ^
   ..\src\main_ui_test.cpp ^
   /Fe:SiteResConfig.exe

if errorlevel 1 (
    echo Build failed!
    cd ..
    pause
    exit /b 1
)

echo Build successful!
echo Running application...
SiteResConfig.exe

cd ..
