@echo off
echo ========================================
echo Deadlock Fix and Compilation
echo ========================================
echo.

echo [INFO] Deadlock fix applied:
echo   - Fixed CtrlChanDataManager::clearAllData() recursive mutex lock
echo   - Replaced getTotalChannelCount() call with direct calculation
echo   - Prevents deadlock during project import
echo.

REM Try to find Qt installation
set QT_FOUND=0

if exist "C:\Qt\5.14.2\mingw73_32\bin\qmake.exe" (
    set QTDIR=C:\Qt\5.14.2\mingw73_32
    set MINGW_PATH=C:\Qt\Tools\mingw730_32\bin
    set QT_FOUND=1
    echo Found Qt at C:\Qt\5.14.2\mingw73_32
)

if %QT_FOUND%==0 (
    if exist "C:\Qt\Qt5.14.2\5.14.2\mingw73_32\bin\qmake.exe" (
        set QTDIR=C:\Qt\Qt5.14.2\5.14.2\mingw73_32
        set MINGW_PATH=C:\Qt\Qt5.14.2\Tools\mingw730_32\bin
        set QT_FOUND=1
        echo Found Qt at C:\Qt\Qt5.14.2\5.14.2\mingw73_32
    )
)

if %QT_FOUND%==0 (
    echo ERROR: Qt not found! Please set paths manually:
    echo   set QTDIR=your_qt_path
    echo   set PATH=%%QTDIR%%\bin;your_mingw_path\bin;%%PATH%%
    pause
    exit /b 1
)

set PATH=%QTDIR%\bin;%MINGW_PATH%;%PATH%
echo Qt environment set: %QTDIR%
echo.

cd /d "%~dp0\SiteResConfig"

echo Cleaning and compiling...
mingw32-make clean > nul 2>&1
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo ERROR: qmake failed!
    pause
    exit /b 1
)

mingw32-make -j4
if errorlevel 1 (
    echo ERROR: Compilation failed!
    pause
    exit /b 1
)

echo.
echo SUCCESS: Deadlock fix compiled successfully!
echo.
echo [FIXES APPLIED]
echo   1. Fixed Qt 5.14 compatibility (removed setCodecForCStrings)
echo   2. Fixed deadlock in CtrlChanDataManager::clearAllData()
echo   3. Simplified UI refresh mechanism
echo   4. Enhanced error handling
echo.
echo Starting application...
if exist "debug\SiteResConfig.exe" (
    start "" "debug\SiteResConfig.exe"
    echo Application started - deadlock issue should be resolved
) else (
    echo ERROR: Executable not found
)

echo.
echo [TEST INSTRUCTIONS]
echo 1. Try importing: C:\Users\<USER>\Desktop\20250818152156_shiyangongcheng.xlsx
echo 2. The import should no longer freeze/deadlock
echo 3. Check for progress dialog and completion message
echo.
pause
