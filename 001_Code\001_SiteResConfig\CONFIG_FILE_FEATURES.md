# 🎯 SiteResConfig 配置文件功能

## ✅ **软件功能转换完成**

软件已从硬件依赖模式转换为**配置文件和手动设置模式**，不再需要连接实际硬件设备。

## 🔧 **核心功能变更**

### **原功能** → **新功能**
- ❌ ~~连接硬件~~ → ✅ **加载配置**
- ❌ ~~断开硬件~~ → ✅ **清空配置**
- ❌ ~~硬件状态~~ → ✅ **配置状态**
- ❌ ~~实时硬件数据~~ → ✅ **模拟数据显示**

## 📁 **支持的配置文件格式**

### **1. JSON格式 (.json)**
```json
{
  "nodes": [
    {"name": "节点1", "ip": "*************", "channels": 4},
    {"name": "节点2", "ip": "*************", "channels": 8}
  ],
  "actuators": [
    {"name": "作动器1", "maxForce": 150000, "type": "hydraulic"},
    {"name": "作动器2", "maxForce": 100000, "type": "electric"}
  ],
  "sensors": [
    {"name": "力传感器1", "range": "200kN", "type": "force"},
    {"name": "位移传感器1", "range": "300mm", "type": "displacement"}
  ]
}
```

### **2. XML格式 (.xml)**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<hardware_config>
  <nodes>
    <node name="控制器A" ip="*************" channels="4"/>
    <node name="控制器B" ip="*************" channels="8"/>
  </nodes>
  <actuators>
    <actuator name="液压缸1" maxForce="250000" type="hydraulic"/>
    <actuator name="液压缸2" maxForce="180000" type="hydraulic"/>
  </actuators>
  <sensors>
    <sensor name="压力传感器" range="300bar" type="pressure"/>
    <sensor name="温度传感器" range="150°C" type="temperature"/>
  </sensors>
</hardware_config>
```

### **3. CSV格式 (.csv)**
```csv
设备名称,设备类型,参数
数据节点1,节点,*************
伺服电机,作动器,50kN
编码器,传感器,360°
```

## 🎯 **操作流程**

### **方法1: 从文件导入配置**
1. 点击 **"加载配置"** 按钮
2. 选择 **"Yes"** (从文件导入)
3. 选择配置文件 (JSON/XML/CSV)
4. 系统自动解析并加载配置
5. 硬件树显示导入的设备信息

### **方法2: 手动配置硬件**
1. 点击 **"加载配置"** 按钮
2. 选择 **"No"** (手动配置)
3. 在对话框中设置：
   - 硬件节点数量
   - 每节点通道数
   - 作动器数量和最大力值
   - 传感器数量
4. 点击 **"确定"** 完成配置

### **方法3: 清空当前配置**
1. 点击 **"清空配置"** 按钮
2. 确认清空操作
3. 所有配置信息被清除

## 🎨 **界面更新**

### **菜单栏变更**
- **硬件(H)** → **配置(C)**
  - 加载配置 (F5)
  - 清空配置 (F6)
  - 紧急停止 (F12)

### **状态显示变更**
- **连接状态** → **配置状态**
  - 未配置 (红色)
  - 已配置 (绿色)

### **按钮文本变更**
- **连接硬件** → **加载配置**
- **断开连接** → **清空配置**

## 📊 **数据显示功能**

### **实时数据模拟**
- ✅ **模拟数据生成** - 20Hz频率生成模拟数据
- ✅ **数学函数模拟** - 使用正弦波等函数模拟真实数据
- ✅ **多通道数据** - 支持多个通道的数据显示
- ✅ **实时更新** - 数据表格实时滚动更新

### **数据导出功能**
- ✅ **CSV格式导出** - 完整的数据导出功能
- ✅ **自定义文件名** - 带时间戳的文件命名
- ✅ **进度显示** - 大数据量导出进度条
- ✅ **数据统计** - 显示导出的数据行数

## 🔧 **配置管理功能**

### **配置文件解析**
- ✅ **自动格式检测** - 自动识别JSON/XML/CSV格式
- ✅ **错误处理** - 文件读取错误的友好提示
- ✅ **进度显示** - 文件导入过程进度条
- ✅ **配置验证** - 基本的配置数据验证

### **手动配置界面**
- ✅ **图形化配置** - 直观的参数设置界面
- ✅ **参数验证** - 输入参数的范围检查
- ✅ **实时预览** - 配置结果即时显示
- ✅ **批量生成** - 根据参数批量生成设备

## 🎯 **使用场景**

### **1. 离线演示**
- 无需实际硬件设备
- 使用配置文件模拟各种硬件配置
- 展示软件功能和界面

### **2. 配置验证**
- 在实际部署前验证硬件配置
- 测试不同的参数组合
- 验证试验流程

### **3. 培训教学**
- 培训操作人员使用软件
- 模拟各种操作场景
- 安全的学习环境

### **4. 系统集成**
- 与其他系统的配置文件集成
- 标准化的配置格式
- 便于系统维护和升级

## 🚀 **立即体验**

1. **编译运行软件**
   ```batch
   compile_final.bat
   ```

2. **加载配置**
   - 点击"加载配置"按钮
   - 选择导入文件或手动配置

3. **开始试验**
   - 配置加载后即可开始模拟试验
   - 查看实时数据采集和显示

4. **导出数据**
   - 试验过程中或结束后导出数据
   - 分析模拟的试验结果

**软件现在完全独立于硬件，通过配置文件和手动设置提供完整的功能体验！** 🎉
