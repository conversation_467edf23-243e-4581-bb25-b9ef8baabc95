@echo off
echo ========================================
echo  验证传感器配置参数完整保存修复
echo ========================================
echo.

echo 检查 AddSensorDetailToCSV 方法是否移除了条件判断...
echo.

echo 1. 检查是否移除了 isEmpty() 条件判断...
findstr /n "if.*isEmpty" "SiteResConfig\src\MainWindow_Qt_Simple.cpp" | findstr "AddSensorDetailToCSV" -A 50 -B 5
if errorlevel 1 (
    echo ✅ 成功: AddSensorDetailToCSV 方法中已移除 isEmpty() 条件判断
) else (
    echo ❌ 警告: AddSensorDetailToCSV 方法中仍存在 isEmpty() 条件判断
)
echo.

echo 2. 检查是否移除了数值条件判断...
findstr /n "if.*!= 0" "SiteResConfig\src\MainWindow_Qt_Simple.cpp" | findstr "AddSensorDetailToCSV" -A 50 -B 5
if errorlevel 1 (
    echo ✅ 成功: AddSensorDetailToCSV 方法中已移除数值条件判断
) else (
    echo ❌ 警告: AddSensorDetailToCSV 方法中仍存在数值条件判断
)
echo.

echo 3. 检查 CreateSensorDetailedConfigJSON 方法是否移除了条件判断...
findstr /n "if.*isEmpty" "SiteResConfig\src\MainWindow_Qt_Simple.cpp" | findstr "CreateSensorDetailedConfigJSON" -A 100 -B 5
if errorlevel 1 (
    echo ✅ 成功: CreateSensorDetailedConfigJSON 方法中已移除 isEmpty() 条件判断
) else (
    echo ❌ 警告: CreateSensorDetailedConfigJSON 方法中仍存在 isEmpty() 条件判断
)
echo.

echo 4. 验证是否保存所有基本信息字段...
findstr /n "序列号\|类型\|型号\|量程\|精度\|EDS标识\|尺寸\|单位\|灵敏度" "SiteResConfig\src\MainWindow_Qt_Simple.cpp" | findstr "AddSensorDetailToCSV" -A 20 -B 5
if not errorlevel 1 (
    echo ✅ 成功: 基本信息字段都已包含在CSV保存中
) else (
    echo ❌ 错误: 基本信息字段缺失
)
echo.

echo 5. 验证是否保存所有校准信息字段...
findstr /n "校准启用\|校准日期\|校准执行人\|单位类型\|单位值\|输入范围\|满量程" "SiteResConfig\src\MainWindow_Qt_Simple.cpp" | findstr "AddSensorDetailToCSV" -A 30 -B 5
if not errorlevel 1 (
    echo ✅ 成功: 校准信息字段都已包含在CSV保存中
) else (
    echo ❌ 错误: 校准信息字段缺失
)
echo.

echo 6. 验证是否保存所有信号调理字段...
findstr /n "极性\|前置放大增益\|后置放大增益\|总增益\|Delta K增益\|比例因子" "SiteResConfig\src\MainWindow_Qt_Simple.cpp" | findstr "AddSensorDetailToCSV" -A 30 -B 5
if not errorlevel 1 (
    echo ✅ 成功: 信号调理字段都已包含在CSV保存中
) else (
    echo ❌ 错误: 信号调理字段缺失
)
echo.

echo 7. 验证是否保存所有激励设置字段...
findstr /n "激励启用\|激励电压\|激励平衡\|激励频率\|相位" "SiteResConfig\src\MainWindow_Qt_Simple.cpp" | findstr "AddSensorDetailToCSV" -A 30 -B 5
if not errorlevel 1 (
    echo ✅ 成功: 激励设置字段都已包含在CSV保存中
) else (
    echo ❌ 错误: 激励设置字段缺失
)
echo.

echo 8. 验证是否保存编码器和反馈系数字段...
findstr /n "编码器分辨率\|正向反馈系数\|负向反馈系数\|极性标志" "SiteResConfig\src\MainWindow_Qt_Simple.cpp" | findstr "AddSensorDetailToCSV" -A 20 -B 5
if not errorlevel 1 (
    echo ✅ 成功: 编码器和反馈系数字段都已包含在CSV保存中
) else (
    echo ❌ 错误: 编码器和反馈系数字段缺失
)
echo.

echo ========================================
echo  🎉 验证完成！
echo ========================================
echo.
echo 修复总结:
echo ✅ 移除了所有条件判断，确保无论界面控件有无数据都保存
echo ✅ 所有33个传感器配置参数都会被保存到CSV和JSON
echo ✅ 保证了配置信息的完整性和一致性
echo.
echo 现在传感器配置参数的保存完全符合用户要求！
echo.
pause
