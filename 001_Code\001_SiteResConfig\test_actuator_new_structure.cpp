/**
 * @file test_actuator_new_structure.cpp
 * @brief 测试新的作动器数据结构
 * <AUTHOR> Assistant
 * @date 2025-08-22
 */

#include "ActuatorDialog_1_2.h"
#include "ActuatorDataManager_1_2.h"
#include "JSONDataExporter_1_2.h"
#include "XLSDataExporter_1_2.h"
#include <QApplication>
#include <QDebug>
#include <QJsonDocument>
#include <QJsonObject>

void testNewDataStructure() {
    qDebug() << "=== 测试新的作动器数据结构 ===";
    
    // 创建测试作动器参数
    ActuatorParams_1_2 params;
    params.name = "测试控制量";
    params.type = ActuatorType_1_2::SingleRod;
    params.zero_offset = 0.5;
    params.lc_id = 2;
    params.station_id = 3;
    params.board_id_ao = 4;
    params.board_type_ao = 5;
    params.port_id_ao = 6;
    params.board_id_do = 7;
    params.board_type_do = 8;
    params.port_id_do = 9;
    
    // 设置详细参数
    params.params.model = "MD600";
    params.params.sn = "TEST123";
    params.params.k = 2.0;
    params.params.b = 1.0;
    params.params.precision = 0.05;
    params.params.polarity = Polarity_1_2::Positive;
    params.params.meas_unit = MeasurementUnit_1_2::Millimeter;
    params.params.meas_range_min = -50.0;
    params.params.meas_range_max = 50.0;
    params.params.output_signal_unit = 2;
    params.params.output_signal_range_min = -5.0;
    params.params.output_signal_range_max = 5.0;
    
    // 设置兼容性字段
    params.serialNumber = params.params.sn;
    params.stroke = 100.0;
    params.displacement = 30.0;
    params.tensionArea = 0.01;
    params.compressionArea = 0.008;
    params.polarity = "Positive";
    params.dither = 1.5;
    params.frequency = 50.0;
    params.outputMultiplier = 1.2;
    params.balance = 2.5;
    params.notes = "测试作动器";
    
    qDebug() << "✅ 创建测试作动器参数完成";
    qDebug() << "   - 控制量名称:" << params.name;
    qDebug() << "   - 作动器类型:" << static_cast<int>(params.type);
    qDebug() << "   - 序列号:" << params.params.sn;
    qDebug() << "   - 极性:" << static_cast<int>(params.params.polarity);
    qDebug() << "   - 测量单位:" << static_cast<int>(params.params.meas_unit);
}

void testTypeConversion() {
    qDebug() << "\n=== 测试类型转换方法 ===";
    
    // 测试作动器类型转换
    QString typeStr = ActuatorDataManager_1_2::actuatorTypeToString(ActuatorType_1_2::SingleRod);
    qDebug() << "✅ 单出杆类型转换:" << typeStr;
    
    ActuatorType_1_2 type = ActuatorDataManager_1_2::stringToActuatorType("双出杆");
    qDebug() << "✅ 字符串转类型:" << static_cast<int>(type);
    
    // 测试极性转换
    QString polarityStr = ActuatorDataManager_1_2::polarityToString(Polarity_1_2::Negative);
    qDebug() << "✅ 负极性转换:" << polarityStr;
    
    Polarity_1_2 polarity = ActuatorDataManager_1_2::stringToPolarity("Both");
    qDebug() << "✅ 字符串转极性:" << static_cast<int>(polarity);
    
    // 测试测量单位转换
    QString unitStr = ActuatorDataManager_1_2::measurementUnitToString(MeasurementUnit_1_2::Inch);
    qDebug() << "✅ 英寸单位转换:" << unitStr;
    
    MeasurementUnit_1_2 unit = ActuatorDataManager_1_2::stringToMeasurementUnit("cm");
    qDebug() << "✅ 字符串转单位:" << static_cast<int>(unit);
}

void testJSONExport() {
    qDebug() << "\n=== 测试JSON导出功能 ===";
    
    // 创建测试作动器
    ActuatorParams_1_2 params;
    params.name = "JSON测试控制量";
    params.type = ActuatorType_1_2::DoubleRod;
    params.zero_offset = 1.0;
    params.lc_id = 10;
    params.station_id = 20;
    params.board_id_ao = 30;
    params.board_type_ao = 40;
    params.port_id_ao = 50;
    params.board_id_do = 60;
    params.board_type_do = 70;
    params.port_id_do = 80;
    
    params.params.model = "MD800";
    params.params.sn = "JSON001";
    params.params.k = 3.0;
    params.params.b = 2.0;
    params.params.precision = 0.02;
    params.params.polarity = Polarity_1_2::Both;
    params.params.meas_unit = MeasurementUnit_1_2::Centimeter;
    params.params.meas_range_min = -200.0;
    params.params.meas_range_max = 200.0;
    params.params.output_signal_unit = 3;
    params.params.output_signal_range_min = -15.0;
    params.params.output_signal_range_max = 15.0;
    
    // 测试JSON转换
    JSONDataExporter_1_2 exporter;
    QJsonObject servoControlJson = exporter.convertActuatorToServoControlJson(params);
    
    QJsonDocument doc(servoControlJson);
    QString jsonString = doc.toJson(QJsonDocument::Indented);
    
    qDebug() << "✅ servo_control JSON转换完成:";
    qDebug().noquote() << jsonString;
}

void testDataValidation() {
    qDebug() << "\n=== 测试数据验证功能 ===";
    
    ActuatorDataManager_1_2 manager;
    
    // 测试有效数据
    ActuatorParams_1_2 validParams;
    validParams.name = "有效控制量";
    validParams.type = ActuatorType_1_2::SingleRod;
    validParams.params.sn = "VALID123";
    validParams.params.polarity = Polarity_1_2::Positive;
    validParams.params.meas_unit = MeasurementUnit_1_2::Millimeter;
    validParams.params.precision = 0.1;
    validParams.params.meas_range_min = -100.0;
    validParams.params.meas_range_max = 100.0;
    validParams.params.output_signal_range_min = -10.0;
    validParams.params.output_signal_range_max = 10.0;
    
    bool isValid = manager.validateActuatorParams(validParams);
    qDebug() << "✅ 有效数据验证:" << (isValid ? "通过" : "失败");
    if (!isValid) {
        qDebug() << "   错误信息:" << manager.getLastError();
    }
    
    // 测试无效数据（空序列号）
    ActuatorParams_1_2 invalidParams = validParams;
    invalidParams.params.sn = "";
    
    bool isInvalid = manager.validateActuatorParams(invalidParams);
    qDebug() << "✅ 无效数据验证:" << (isInvalid ? "意外通过" : "正确拒绝");
    if (!isInvalid) {
        qDebug() << "   错误信息:" << manager.getLastError();
    }
}

int main(int argc, char *argv[]) {
    QApplication app(argc, argv);
    
    qDebug() << "开始测试新的作动器数据结构...";
    
    testNewDataStructure();
    testTypeConversion();
    testJSONExport();
    testDataValidation();
    
    qDebug() << "\n=== 所有测试完成 ===";
    
    return 0;
}
