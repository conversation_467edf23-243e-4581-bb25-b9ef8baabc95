/**
 * @file test_actuator_multi_group_sync.cpp
 * @brief 作动器序列号组内唯一性约束和多组数据同步功能测试
 * <AUTHOR> Assistant
 * @date 2025-01-09
 * @version 1.0.0
 */

#include <QtCore/QCoreApplication>
#include <QtCore/QDebug>
#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QList>

#include "ActuatorViewModel_1_2.h"
#include "ActuatorDataManager_1_2.h"
#include "DataModels_Fixed.h"

class ActuatorMultiGroupSyncTester
{
public:
    ActuatorMultiGroupSyncTester() {
        setupTestEnvironment();
    }
    
    ~ActuatorMultiGroupSyncTester() {
        cleanup();
    }
    
    /**
     * @brief 运行所有测试
     */
    bool runAllTests() {
        qDebug() << "========================================";
        qDebug() << "开始作动器多组同步功能测试";
        qDebug() << "========================================";
        
        bool allPassed = true;
        
        // 测试1：序列号组内唯一性约束
        if (!testSerialNumberUniquenessInGroup()) {
            allPassed = false;
            qDebug() << "❌ 测试1失败：序列号组内唯一性约束";
        } else {
            qDebug() << "✅ 测试1通过：序列号组内唯一性约束";
        }
        
        // 测试2：多组数据同步
        if (!testMultiGroupDataSync()) {
            allPassed = false;
            qDebug() << "❌ 测试2失败：多组数据同步";
        } else {
            qDebug() << "✅ 测试2通过：多组数据同步";
        }
        
        // 测试3：获取作动器所在所有组ID
        if (!testGetActuatorGroupIds()) {
            allPassed = false;
            qDebug() << "❌ 测试3失败：获取作动器所在所有组ID";
        } else {
            qDebug() << "✅ 测试3通过：获取作动器所在所有组ID";
        }
        
        qDebug() << "========================================";
        qDebug() << QString("测试总结：%1").arg(allPassed ? "全部通过" : "存在失败");
        qDebug() << "========================================";
        
        return allPassed;
    }

private:
    ActuatorViewModel1_2* viewModel_;
    ActuatorDataManager_1_2* dataManager_;
    
    void setupTestEnvironment() {
        qDebug() << "🔧 初始化测试环境...";
        
        dataManager_ = new ActuatorDataManager_1_2();
        
        ActuatorViewModelConfig config;
        config.enableCache = true;
        config.enableValidation = true;
        config.threadSafe = false;
        
        viewModel_ = new ActuatorViewModel1_2(dataManager_, config);
        
        // 创建测试用的作动器组
        createTestGroups();
        
        qDebug() << "✅ 测试环境初始化完成";
    }
    
    void cleanup() {
        qDebug() << "🧹 清理测试环境...";
        delete viewModel_;
        delete dataManager_;
    }
    
    void createTestGroups() {
        // 创建组1：横向力作动器
        UI::ActuatorGroup_1_2 group1;
        group1.groupId = 1;
        group1.groupName = "横向力作动器";
        group1.groupType = "液压作动器";
        group1.createTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
        group1.groupNotes = "用于横向力加载的作动器组";
        dataManager_->saveActuatorGroup(group1);
        
        // 创建组2：垂向力作动器
        UI::ActuatorGroup_1_2 group2;
        group2.groupId = 2;
        group2.groupName = "垂向力作动器";
        group2.groupType = "液压作动器";
        group2.createTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
        group2.groupNotes = "用于垂向力加载的作动器组";
        dataManager_->saveActuatorGroup(group2);
        
        // 创建组3：纵向力作动器
        UI::ActuatorGroup_1_2 group3;
        group3.groupId = 3;
        group3.groupName = "纵向力作动器";
        group3.groupType = "液压作动器";
        group3.createTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
        group3.groupNotes = "用于纵向力加载的作动器组";
        dataManager_->saveActuatorGroup(group3);
        
        qDebug() << "📁 已创建3个测试组";
    }
    
    /**
     * @brief 测试序列号组内唯一性约束
     */
    bool testSerialNumberUniquenessInGroup() {
        qDebug() << "\n--- 测试1：序列号组内唯一性约束 ---";
        
        // 创建第一个作动器到组1
        UI::ActuatorParams_1_2 actuator1;
        actuator1.params.sn = "ACT_001";
        actuator1.name = "横向作动器1";
        actuator1.params.model = "HYD-100";
        actuator1.params.k = 1000.0;
        actuator1.params.b = 10.0;
        actuator1.zero_offset = 0.0;
        
        if (!viewModel_->createActuatorDeviceBusiness(1, actuator1)) {
            qDebug() << "❌ 第一个作动器创建失败";
            return false;
        }
        qDebug() << "✅ 组1中创建作动器 ACT_001 成功";
        
        // 尝试在同一组创建相同序列号的作动器（应该失败）
        UI::ActuatorParams_1_2 actuator2;
        actuator2.params.sn = "ACT_001"; // 相同序列号
        actuator2.name = "横向作动器2";
        actuator2.params.model = "HYD-200";
        actuator2.params.k = 2000.0;
        actuator2.params.b = 20.0;
        actuator2.zero_offset = 0.0;
        
        if (viewModel_->createActuatorDeviceBusiness(1, actuator2)) {
            qDebug() << "❌ 组内重复序列号应该被拒绝，但创建成功了";
            return false;
        }
        qDebug() << "✅ 组内重复序列号被正确拒绝: " << viewModel_->getLastError();
        
        // 在不同组创建相同序列号的作动器（应该成功）
        if (!viewModel_->createActuatorDeviceBusiness(2, actuator2)) {
            qDebug() << "❌ 跨组相同序列号应该被允许，但创建失败了: " << viewModel_->getLastError();
            return false;
        }
        qDebug() << "✅ 组2中创建相同序列号作动器 ACT_001 成功";
        
        return true;
    }
    
    /**
     * @brief 测试多组数据同步
     */
    bool testMultiGroupDataSync() {
        qDebug() << "\n--- 测试2：多组数据同步 ---";
        
        // 先确保作动器存在于多个组中
        UI::ActuatorParams_1_2 actuator;
        actuator.params.sn = "ACT_SYNC_001";
        actuator.name = "同步测试作动器";
        actuator.params.model = "SYNC-100";
        actuator.params.k = 1500.0;
        actuator.params.b = 15.0;
        actuator.zero_offset = 0.0;
        
        // 在组1和组3中创建相同序列号的作动器
        if (!viewModel_->createActuatorDeviceBusiness(1, actuator)) {
            qDebug() << "❌ 在组1创建同步测试作动器失败: " << viewModel_->getLastError();
            return false;
        }
        
        if (!viewModel_->createActuatorDeviceBusiness(3, actuator)) {
            qDebug() << "❌ 在组3创建同步测试作动器失败: " << viewModel_->getLastError();
            return false;
        }
        
        qDebug() << "✅ 在组1和组3中创建了相同序列号的作动器";
        
        // 获取作动器所在的所有组ID
        QList<int> groupIds = viewModel_->getActuatorGroupIds("ACT_SYNC_001");
        if (groupIds.size() != 2 || !groupIds.contains(1) || !groupIds.contains(3)) {
            qDebug() << "❌ 获取作动器组ID列表失败，期望[1,3]，实际:" << groupIds;
            return false;
        }
        qDebug() << "✅ 正确获取作动器所在的组ID: " << groupIds;
        
        // 修改作动器参数并同步到所有组
        UI::ActuatorParams_1_2 updatedActuator = actuator;
        updatedActuator.name = "同步测试作动器（已更新）";
        updatedActuator.params.model = "SYNC-200";
        updatedActuator.params.k = 2500.0;
        updatedActuator.params.b = 25.0;
        updatedActuator.zero_offset = 1.0;
        
        if (!viewModel_->updateActuatorInAllGroups("ACT_SYNC_001", updatedActuator)) {
            qDebug() << "❌ 多组数据同步失败: " << viewModel_->getLastError();
            return false;
        }
        qDebug() << "✅ 多组数据同步成功";
        
        // 验证所有组中的数据都已更新
        for (int groupId : groupIds) {
            UI::ActuatorParams_1_2 verifyActuator = dataManager_->getActuator("ACT_SYNC_001", groupId);
            if (verifyActuator.name != updatedActuator.name ||
                verifyActuator.params.model != updatedActuator.params.model ||
                verifyActuator.params.k != updatedActuator.params.k) {
                qDebug() << QString("❌ 组%1中的数据未正确更新").arg(groupId);
                qDebug() << QString("  期望名称: %1, 实际: %2").arg(updatedActuator.name).arg(verifyActuator.name);
                qDebug() << QString("  期望型号: %1, 实际: %2").arg(updatedActuator.params.model).arg(verifyActuator.params.model);
                qDebug() << QString("  期望K值: %1, 实际: %2").arg(updatedActuator.params.k).arg(verifyActuator.params.k);
                return false;
            }
        }
        qDebug() << "✅ 所有组中的数据都已正确更新";
        
        return true;
    }
    
    /**
     * @brief 测试获取作动器所在所有组ID
     */
    bool testGetActuatorGroupIds() {
        qDebug() << "\n--- 测试3：获取作动器所在所有组ID ---";
        
        // 测试不存在的作动器
        QList<int> emptyList = viewModel_->getActuatorGroupIds("NOT_EXIST");
        if (!emptyList.isEmpty()) {
            qDebug() << "❌ 不存在的作动器应该返回空列表，但返回了: " << emptyList;
            return false;
        }
        qDebug() << "✅ 不存在的作动器正确返回空列表";
        
        // 测试只存在于一个组的作动器
        QList<int> singleGroup = viewModel_->getActuatorGroupIds("ACT_001");
        if (singleGroup.size() != 2) { // ACT_001存在于组1和组2
            qDebug() << "❌ ACT_001应该存在于2个组，但返回了: " << singleGroup;
            return false;
        }
        qDebug() << "✅ 正确获取单作动器的组列表: " << singleGroup;
        
        // 测试存在于多个组的作动器
        QList<int> multiGroup = viewModel_->getActuatorGroupIds("ACT_SYNC_001");
        if (multiGroup.size() != 2 || !multiGroup.contains(1) || !multiGroup.contains(3)) {
            qDebug() << "❌ ACT_SYNC_001应该存在于组[1,3]，但返回了: " << multiGroup;
            return false;
        }
        qDebug() << "✅ 正确获取多组作动器的组列表: " << multiGroup;
        
        return true;
    }
};

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "作动器多组同步功能测试程序";
    qDebug() << "版本: 1.0.0";
    qDebug() << "日期: 2025-01-09";
    qDebug() << "";
    
    ActuatorMultiGroupSyncTester tester;
    bool success = tester.runAllTests();
    
    if (success) {
        qDebug() << "\n🎉 所有测试通过！";
        return 0;
    } else {
        qDebug() << "\n❌ 测试失败！";
        return 1;
    }
} 