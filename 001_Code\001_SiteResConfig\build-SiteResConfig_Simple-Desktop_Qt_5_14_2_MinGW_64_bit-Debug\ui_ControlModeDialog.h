/********************************************************************************
** Form generated from reading UI file 'ControlModeDialog.ui'
**
** Created by: Qt User Interface Compiler version 5.14.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_CONTROLMODEDIALOG_H
#define UI_CONTROLMODEDIALOG_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QDialog>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QVBoxLayout>

QT_BEGIN_NAMESPACE

class Ui_ControlModeDialog
{
public:
    QVBoxLayout *verticalLayout;
    QHBoxLayout *modeLayout;
    QLabel *modeLabel;
    QComboBox *modeComboBox;
    QSpacerItem *verticalSpacer;
    QHBoxLayout *buttonLayout;
    QSpacerItem *horizontalSpacer;
    QPushButton *okButton;
    QPushButton *cancelButton;

    void setupUi(QDialog *ControlModeDialog)
    {
        if (ControlModeDialog->objectName().isEmpty())
            ControlModeDialog->setObjectName(QString::fromUtf8("ControlModeDialog"));
        ControlModeDialog->resize(300, 150);
        ControlModeDialog->setModal(true);
        verticalLayout = new QVBoxLayout(ControlModeDialog);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        modeLayout = new QHBoxLayout();
        modeLayout->setObjectName(QString::fromUtf8("modeLayout"));
        modeLabel = new QLabel(ControlModeDialog);
        modeLabel->setObjectName(QString::fromUtf8("modeLabel"));

        modeLayout->addWidget(modeLabel);

        modeComboBox = new QComboBox(ControlModeDialog);
        modeComboBox->setObjectName(QString::fromUtf8("modeComboBox"));
        modeComboBox->setMinimumSize(QSize(150, 0));

        modeLayout->addWidget(modeComboBox);


        verticalLayout->addLayout(modeLayout);

        verticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout->addItem(verticalSpacer);

        buttonLayout = new QHBoxLayout();
        buttonLayout->setObjectName(QString::fromUtf8("buttonLayout"));
        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        buttonLayout->addItem(horizontalSpacer);

        okButton = new QPushButton(ControlModeDialog);
        okButton->setObjectName(QString::fromUtf8("okButton"));

        buttonLayout->addWidget(okButton);

        cancelButton = new QPushButton(ControlModeDialog);
        cancelButton->setObjectName(QString::fromUtf8("cancelButton"));

        buttonLayout->addWidget(cancelButton);


        verticalLayout->addLayout(buttonLayout);


        retranslateUi(ControlModeDialog);
        QObject::connect(cancelButton, SIGNAL(clicked()), ControlModeDialog, SLOT(reject()));

        okButton->setDefault(true);


        QMetaObject::connectSlotsByName(ControlModeDialog);
    } // setupUi

    void retranslateUi(QDialog *ControlModeDialog)
    {
        ControlModeDialog->setWindowTitle(QCoreApplication::translate("ControlModeDialog", "\346\216\247\345\210\266\346\250\241\345\274\217\350\256\276\347\275\256", nullptr));
        modeLabel->setText(QCoreApplication::translate("ControlModeDialog", "\346\216\247\345\210\266\346\250\241\345\274\217:", nullptr));
        okButton->setText(QCoreApplication::translate("ControlModeDialog", "\347\241\256\345\256\232", nullptr));
        cancelButton->setText(QCoreApplication::translate("ControlModeDialog", "\345\217\226\346\266\210", nullptr));
    } // retranslateUi

};

namespace Ui {
    class ControlModeDialog: public Ui_ControlModeDialog {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_CONTROLMODEDIALOG_H
