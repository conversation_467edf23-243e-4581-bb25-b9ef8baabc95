#include <iostream>
#include <string>
#include <vector>
#include <sstream>

// 测试硬件节点tooltip解析逻辑
void testTooltipParsing() {
    std::cout << "测试硬件节点tooltip解析..." << std::endl;
    
    // 测试不同格式的tooltip
    std::vector<std::string> testTooltips;

    // 格式1：单行格式
    testTooltips.push_back("CH1: IP=*************, Port=8080, Enable\nCH2: IP=*************, Port=8081, Enable");

    // 格式2：分行格式
    testTooltips.push_back("Channel Info:\nCH1\nIP: *************\nPort: 8080\nCH2\nIP: *************\nPort: 8081");

    // 格式3：混合格式
    testTooltips.push_back("Hardware Node: LD-B1\nCH1: IP=*************, Port=8080\nCH2: IP=*************, Port=8081");

    // 格式4：简单格式
    testTooltips.push_back("CH1\nIP: *************\nPort: 8080\nCH2\nIP: *************\nPort: 8081");
    
    for (size_t i = 0; i < testTooltips.size(); ++i) {
        std::cout << "\n=== 测试格式 " << (i+1) << " ===" << std::endl;
        std::cout << "原始tooltip: " << testTooltips[i] << std::endl;
        std::cout << "解析结果:" << std::endl;
        
        // 模拟解析逻辑
        std::istringstream iss(testTooltips[i]);
        std::string line;
        
        while (std::getline(iss, line)) {
            // 去除前后空格
            size_t start = line.find_first_not_of(" \t");
            size_t end = line.find_last_not_of(" \t");
            if (start != std::string::npos && end != std::string::npos) {
                line = line.substr(start, end - start + 1);
            }
            
            std::cout << "  处理行: '" << line << "'" << std::endl;
            
            // 检查是否包含通道和IP信息
            if (line.find("CH") != std::string::npos && line.find("IP=") != std::string::npos) {
                std::cout << "    -> 发现通道+IP格式" << std::endl;
                
                // 解析通道名
                size_t colonPos = line.find(':');
                if (colonPos != std::string::npos) {
                    std::string channelName = line.substr(0, colonPos);
                    std::string details = line.substr(colonPos + 1);
                    
                    std::cout << "    -> 通道: " << channelName << std::endl;
                    
                    // 解析IP
                    size_t ipPos = details.find("IP=");
                    if (ipPos != std::string::npos) {
                        size_t ipStart = ipPos + 3;
                        size_t ipEnd = details.find_first_of(", \t", ipStart);
                        if (ipEnd == std::string::npos) ipEnd = details.length();
                        std::string ip = details.substr(ipStart, ipEnd - ipStart);
                        std::cout << "    -> IP: " << ip << std::endl;
                    }
                    
                    // 解析端口
                    size_t portPos = details.find("Port=");
                    if (portPos != std::string::npos) {
                        size_t portStart = portPos + 5;
                        size_t portEnd = details.find_first_of(", \t", portStart);
                        if (portEnd == std::string::npos) portEnd = details.length();
                        std::string port = details.substr(portStart, portEnd - portStart);
                        std::cout << "    -> 端口: " << port << std::endl;
                    }
                }
            }
            // 检查分行格式
            else if (line.find("IP:") == 0 || line.find("IP地址:") == 0) {
                size_t colonPos = line.find(':');
                if (colonPos != std::string::npos) {
                    std::string ip = line.substr(colonPos + 1);
                    // 去除空格
                    size_t start = ip.find_first_not_of(" \t");
                    if (start != std::string::npos) {
                        ip = ip.substr(start);
                    }
                    std::cout << "    -> IP: " << ip << std::endl;
                }
            }
            else if (line.find("Port:") == 0 || line.find("端口:") == 0) {
                size_t colonPos = line.find(':');
                if (colonPos != std::string::npos) {
                    std::string port = line.substr(colonPos + 1);
                    // 去除空格
                    size_t start = port.find_first_not_of(" \t");
                    if (start != std::string::npos) {
                        port = port.substr(start);
                    }
                    std::cout << "    -> 端口: " << port << std::endl;
                }
            }
            else if (line.find("CH") == 0 && line.find("IP=") == std::string::npos) {
                std::cout << "    -> 通道: " << line << std::endl;
            }
        }
    }
}

int main() {
    testTooltipParsing();
    
    std::cout << "\n测试完成！" << std::endl;
    std::cout << "请检查解析逻辑是否正确处理了各种tooltip格式。" << std::endl;
    
    return 0;
}
