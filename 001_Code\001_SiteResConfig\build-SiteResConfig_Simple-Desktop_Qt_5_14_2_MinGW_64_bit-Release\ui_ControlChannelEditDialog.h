/********************************************************************************
** Form generated from reading UI file 'ControlChannelEditDialog.ui'
**
** Created by: Qt User Interface Compiler version 5.14.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_CONTROLCHANNELEDITDIALOG_H
#define UI_CONTROLCHANNELEDITDIALOG_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QCheckBox>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QDoubleSpinBox>
#include <QtWidgets/QFormLayout>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QScrollArea>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QTabWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_ControlChannelEditDialog
{
public:
    QVBoxLayout *mainLayout;
    QTabWidget *tabWidget;
    QWidget *basicInfoTab;
    QFormLayout *basicInfoLayout;
    QLabel *channelNameLabel;
    QLineEdit *channelNameEdit;
    QLabel *lcIdLabel;
    QSpinBox *lcIdSpinBox;
    QLabel *stationIdLabel;
    QSpinBox *stationIdSpinBox;
    QLabel *enableLabel;
    QCheckBox *enableCheckBox;
    QLabel *controlModeLabel;
    QComboBox *controlModeCombo;
    QSpacerItem *verticalSpacer_2;
    QWidget *associationTab;
    QFormLayout *associationLayout;
    QLabel *hardwareLabel;
    QHBoxLayout *hardwareLayout;
    QComboBox *hardwareGroupCombo;
    QComboBox *hardwareChannelCombo;
    QLabel *load1SensorLabel;
    QHBoxLayout *load1SensorLayout;
    QComboBox *load1SensorGroupCombo;
    QComboBox *load1SensorDeviceCombo;
    QLabel *load2SensorLabel;
    QHBoxLayout *load2SensorLayout;
    QComboBox *load2SensorGroupCombo;
    QComboBox *load2SensorDeviceCombo;
    QLabel *positionSensorLabel;
    QHBoxLayout *positionSensorLayout;
    QComboBox *positionSensorGroupCombo;
    QComboBox *positionSensorDeviceCombo;
    QLabel *controlActuatorLabel;
    QHBoxLayout *controlActuatorLayout;
    QComboBox *controlActuatorGroupCombo;
    QComboBox *controlActuatorDeviceCombo;
    QSpacerItem *verticalSpacer_3;
    QWidget *polarityTab;
    QFormLayout *polarityLayout;
    QLabel *servoControlPolarityLabel;
    QComboBox *servoControlPolarityCombo;
    QLabel *payload1PolarityLabel;
    QComboBox *payload1PolarityCombo;
    QLabel *payload2PolarityLabel;
    QComboBox *payload2PolarityCombo;
    QLabel *positionPolarityLabel;
    QComboBox *positionPolarityCombo;
    QSpacerItem *verticalSpacer_4;
    QWidget *thresholdsTab;
    QVBoxLayout *thresholdsLayout;
    QTabWidget *tabWidget_2;
    QWidget *payloadLimitsTab;
    QVBoxLayout *payloadLimitsLayout;
    QScrollArea *payloadScrollArea;
    QWidget *payloadScrollAreaWidgetContents;
    QVBoxLayout *payloadMainLayout;
    QGroupBox *payloadInnerGroupBox;
    QGridLayout *payloadInnerGridLayout;
    QLabel *payloadInnerMinLabel;
    QDoubleSpinBox *payloadInnerMinSpinBox;
    QLabel *payloadInnerMaxLabel;
    QDoubleSpinBox *payloadInnerMaxSpinBox;
    QLabel *payloadInnerUnitLabel;
    QComboBox *payloadInnerUnitCombo;
    QLabel *payloadInnerDurationLabel;
    QDoubleSpinBox *payloadInnerDurationSpinBox;
    QLabel *payloadInnerEnabledLabel;
    QCheckBox *payloadInnerEnabledCheckBox;
    QLabel *payloadInnerTypeLabel;
    QComboBox *payloadInnerTypeCombo;
    QGroupBox *payloadOuterGroupBox;
    QGridLayout *payloadOuterGridLayout;
    QLabel *payloadOuterMinLabel;
    QDoubleSpinBox *payloadOuterMinSpinBox;
    QLabel *payloadOuterMaxLabel;
    QDoubleSpinBox *payloadOuterMaxSpinBox;
    QLabel *payloadOuterUnitLabel;
    QComboBox *payloadOuterUnitCombo;
    QLabel *payloadOuterDurationLabel;
    QDoubleSpinBox *payloadOuterDurationSpinBox;
    QLabel *payloadOuterEnabledLabel;
    QCheckBox *payloadOuterEnabledCheckBox;
    QLabel *payloadOuterTypeLabel;
    QComboBox *payloadOuterTypeCombo;
    QGroupBox *payloadMainGroupBox;
    QGridLayout *payloadMainGridLayout;
    QLabel *payloadMainMaxLabel;
    QLabel *payloadMainUnitLabel;
    QLabel *payloadMainTypeLabel;
    QComboBox *payloadMainTypeCombo;
    QLabel *payloadMainMinLabel;
    QDoubleSpinBox *payloadMainMinSpinBox;
    QDoubleSpinBox *payloadMainMaxSpinBox;
    QLabel *payloadMainDurationLabel;
    QDoubleSpinBox *payloadMainDurationSpinBox;
    QComboBox *payloadMainUnitCombo;
    QLabel *payloadMainEnabledLabel;
    QCheckBox *payloadMainEnabledCheckBox;
    QSpacerItem *verticalSpacer_5;
    QWidget *positionLimitsTab;
    QVBoxLayout *positionLimitsLayout;
    QScrollArea *positionScrollArea;
    QWidget *positionScrollAreaWidgetContents;
    QVBoxLayout *positionMainLayout;
    QGroupBox *positionInnerGroupBox;
    QGridLayout *positionInnerGridLayout;
    QLabel *positionInnerMinLabel;
    QDoubleSpinBox *positionInnerMinSpinBox;
    QLabel *positionInnerMaxLabel;
    QDoubleSpinBox *positionInnerMaxSpinBox;
    QLabel *positionInnerUnitLabel;
    QComboBox *positionInnerUnitCombo;
    QLabel *positionInnerDurationLabel;
    QDoubleSpinBox *positionInnerDurationSpinBox;
    QLabel *positionInnerEnabledLabel;
    QCheckBox *positionInnerEnabledCheckBox;
    QLabel *positionInnerTypeLabel;
    QComboBox *positionInnerTypeCombo;
    QGroupBox *positionOuterGroupBox;
    QGridLayout *positionOuterGridLayout;
    QLabel *positionOuterMinLabel;
    QDoubleSpinBox *positionOuterMinSpinBox;
    QLabel *positionOuterMaxLabel;
    QDoubleSpinBox *positionOuterMaxSpinBox;
    QLabel *positionOuterUnitLabel;
    QComboBox *positionOuterUnitCombo;
    QLabel *positionOuterDurationLabel;
    QDoubleSpinBox *positionOuterDurationSpinBox;
    QLabel *positionOuterEnabledLabel;
    QCheckBox *positionOuterEnabledCheckBox;
    QLabel *positionOuterTypeLabel;
    QComboBox *positionOuterTypeCombo;
    QGroupBox *positionMainGroupBox;
    QGridLayout *positionMainGridLayout;
    QLabel *positionMainMinLabel;
    QDoubleSpinBox *positionMainMinSpinBox;
    QLabel *positionMainMaxLabel;
    QDoubleSpinBox *positionMainMaxSpinBox;
    QLabel *positionMainUnitLabel;
    QComboBox *positionMainUnitCombo;
    QLabel *positionMainDurationLabel;
    QDoubleSpinBox *positionMainDurationSpinBox;
    QLabel *positionMainEnabledLabel;
    QCheckBox *positionMainEnabledCheckBox;
    QLabel *positionMainTypeLabel;
    QComboBox *positionMainTypeCombo;
    QWidget *speedLimitsTab;
    QVBoxLayout *speedLimitsLayout;
    QScrollArea *speedScrollArea;
    QWidget *speedScrollAreaWidgetContents;
    QVBoxLayout *speedMainLayout;
    QGroupBox *speedInnerGroupBox;
    QGridLayout *speedInnerGridLayout;
    QLabel *speedInnerMinLabel;
    QDoubleSpinBox *speedInnerMinSpinBox;
    QLabel *speedInnerMaxLabel;
    QDoubleSpinBox *speedInnerMaxSpinBox;
    QLabel *speedInnerUnitLabel;
    QComboBox *speedInnerUnitCombo;
    QLabel *speedInnerDurationLabel;
    QDoubleSpinBox *speedInnerDurationSpinBox;
    QLabel *speedInnerEnabledLabel;
    QCheckBox *speedInnerEnabledCheckBox;
    QLabel *speedInnerTypeLabel;
    QComboBox *speedInnerTypeCombo;
    QGroupBox *speedOuterGroupBox;
    QGridLayout *speedOuterGridLayout;
    QLabel *speedOuterMinLabel;
    QDoubleSpinBox *speedOuterMinSpinBox;
    QLabel *speedOuterMaxLabel;
    QDoubleSpinBox *speedOuterMaxSpinBox;
    QLabel *speedOuterUnitLabel;
    QComboBox *speedOuterUnitCombo;
    QLabel *speedOuterDurationLabel;
    QDoubleSpinBox *speedOuterDurationSpinBox;
    QLabel *speedOuterEnabledLabel;
    QCheckBox *speedOuterEnabledCheckBox;
    QLabel *speedOuterTypeLabel;
    QComboBox *speedOuterTypeCombo;
    QGroupBox *speedMainGroupBox;
    QGridLayout *speedMainGridLayout;
    QLabel *speedMainMinLabel;
    QDoubleSpinBox *speedMainMinSpinBox;
    QLabel *speedMainMaxLabel;
    QDoubleSpinBox *speedMainMaxSpinBox;
    QLabel *speedMainUnitLabel;
    QComboBox *speedMainUnitCombo;
    QLabel *speedMainDurationLabel;
    QDoubleSpinBox *speedMainDurationSpinBox;
    QLabel *speedMainEnabledLabel;
    QCheckBox *speedMainEnabledCheckBox;
    QLabel *speedMainTypeLabel;
    QComboBox *speedMainTypeCombo;
    QWidget *filterTab;
    QVBoxLayout *filterLayout;
    QScrollArea *filterScrollArea;
    QWidget *filterScrollAreaWidgetContents;
    QGridLayout *gridLayout_2;
    QLabel *forceFilterLabel;
    QLabel *forceLowpassLabel;
    QDoubleSpinBox *payloadFcSpinBox;
    QLabel *forceHighpassLabel;
    QComboBox *validComboBox;
    QLabel *positionFilterLabel;
    QLabel *positionLowpassLabel;
    QDoubleSpinBox *positionFcSpinBox;
    QLabel *positionHighpassLabel;
    QDoubleSpinBox *speedFcSpinBox;
    QLabel *velocityFilterLabel;
    QLabel *velocityLowpassLabel;
    QDoubleSpinBox *accFcSpinBox;
    QSpacerItem *verticalSpacer;
    QWidget *pidTab;
    QVBoxLayout *pidLayout;
    QTabWidget *tabWidget_3;
    QWidget *tab;
    QVBoxLayout *positionControlPidLayout;
    QScrollArea *positionControlScrollArea;
    QWidget *positionControlScrollAreaWidgetContents;
    QVBoxLayout *positionControlMainLayout;
    QGroupBox *positionBasicPidGroup;
    QGridLayout *positionBasicPidGridLayout;
    QLabel *positionKpLabel;
    QDoubleSpinBox *positionKpSpinBox;
    QLabel *positionKiLabel;
    QDoubleSpinBox *positionKiSpinBox;
    QLabel *positionKdLabel;
    QDoubleSpinBox *positionKdSpinBox;
    QLabel *positionKsLabel;
    QDoubleSpinBox *positionKsSpinBox;
    QLabel *positionKaLabel;
    QDoubleSpinBox *positionKaSpinBox;
    QGroupBox *positionStepPidGroup;
    QGridLayout *positionStepPidGridLayout;
    QLabel *positionKpStepLabel;
    QDoubleSpinBox *positionKpStepSpinBox;
    QLabel *positionKiStepLabel;
    QDoubleSpinBox *positionKiStepSpinBox;
    QLabel *positionKdStepLabel;
    QDoubleSpinBox *positionKdStepSpinBox;
    QLabel *positionKsStepLabel;
    QDoubleSpinBox *positionKsStepSpinBox;
    QLabel *positionKaStepLabel;
    QDoubleSpinBox *positionKaStepSpinBox;
    QSpacerItem *verticalSpacer_6;
    QSpacerItem *positionVerticalSpacer;
    QWidget *tab_2;
    QVBoxLayout *staticLoadPidLayout;
    QScrollArea *staticLoadScrollArea;
    QWidget *staticLoadScrollAreaWidgetContents;
    QVBoxLayout *staticLoadMainLayout;
    QGroupBox *staticLoadBasicPidGroup;
    QGridLayout *staticLoadBasicPidGridLayout;
    QLabel *staticLoadKpLabel;
    QDoubleSpinBox *staticLoadKpSpinBox;
    QLabel *staticLoadKiLabel;
    QDoubleSpinBox *staticLoadKiSpinBox;
    QLabel *staticLoadKdLabel;
    QDoubleSpinBox *staticLoadKdSpinBox;
    QLabel *staticLoadKsLabel;
    QDoubleSpinBox *staticLoadKsSpinBox;
    QLabel *staticLoadKaLabel;
    QDoubleSpinBox *staticLoadKaSpinBox;
    QGroupBox *staticLoadStepPidGroup;
    QGridLayout *staticLoadStepPidGridLayout;
    QLabel *staticLoadKpStepLabel;
    QDoubleSpinBox *staticLoadKpStepSpinBox;
    QLabel *staticLoadKiStepLabel;
    QDoubleSpinBox *staticLoadKiStepSpinBox;
    QLabel *staticLoadKdStepLabel;
    QDoubleSpinBox *staticLoadKdStepSpinBox;
    QLabel *staticLoadKsStepLabel;
    QDoubleSpinBox *staticLoadKsStepSpinBox;
    QLabel *staticLoadKaStepLabel;
    QDoubleSpinBox *staticLoadKaStepSpinBox;
    QSpacerItem *staticLoadVerticalSpacer;
    QWidget *tab_3;
    QVBoxLayout *dynamicLoadPidLayout;
    QScrollArea *dynamicLoadScrollArea;
    QWidget *dynamicLoadScrollAreaWidgetContents;
    QVBoxLayout *dynamicLoadMainLayout;
    QGroupBox *dynamicLoadBasicPidGroup;
    QGridLayout *dynamicLoadBasicPidGridLayout;
    QLabel *dynamicLoadKpLabel;
    QDoubleSpinBox *dynamicLoadKpSpinBox;
    QLabel *dynamicLoadKiLabel;
    QDoubleSpinBox *dynamicLoadKiSpinBox;
    QLabel *dynamicLoadKdLabel;
    QDoubleSpinBox *dynamicLoadKdSpinBox;
    QLabel *dynamicLoadKsLabel;
    QDoubleSpinBox *dynamicLoadKsSpinBox;
    QLabel *dynamicLoadKaLabel;
    QDoubleSpinBox *dynamicLoadKaSpinBox;
    QGroupBox *dynamicLoadStepPidGroup;
    QGridLayout *dynamicLoadStepPidGridLayout;
    QLabel *dynamicLoadKpStepLabel;
    QDoubleSpinBox *dynamicLoadKpStepSpinBox;
    QLabel *dynamicLoadKiStepLabel;
    QDoubleSpinBox *dynamicLoadKiStepSpinBox;
    QLabel *dynamicLoadKdStepLabel;
    QDoubleSpinBox *dynamicLoadKdStepSpinBox;
    QLabel *dynamicLoadKsStepLabel;
    QDoubleSpinBox *dynamicLoadKsStepSpinBox;
    QLabel *dynamicLoadKaStepLabel;
    QDoubleSpinBox *dynamicLoadKaStepSpinBox;
    QSpacerItem *dynamicLoadVerticalSpacer;
    QWidget *tab_4;
    QVBoxLayout *speedControlPidLayout;
    QScrollArea *speedControlScrollArea;
    QWidget *speedControlScrollAreaWidgetContents;
    QVBoxLayout *speedControlMainLayout;
    QGroupBox *speedControlBasicPidGroup;
    QGridLayout *speedControlBasicPidGridLayout;
    QLabel *speedControlKpLabel;
    QDoubleSpinBox *speedControlKpSpinBox;
    QLabel *speedControlKiLabel;
    QDoubleSpinBox *speedControlKiSpinBox;
    QLabel *speedControlKdLabel;
    QDoubleSpinBox *speedControlKdSpinBox;
    QLabel *speedControlKsLabel;
    QDoubleSpinBox *speedControlKsSpinBox;
    QLabel *speedControlKaLabel;
    QDoubleSpinBox *speedControlKaSpinBox;
    QGroupBox *speedControlStepPidGroup;
    QGridLayout *speedControlStepPidGridLayout;
    QLabel *speedControlKpStepLabel;
    QDoubleSpinBox *speedControlKpStepSpinBox;
    QLabel *speedControlKiStepLabel;
    QDoubleSpinBox *speedControlKiStepSpinBox;
    QLabel *speedControlKdStepLabel;
    QDoubleSpinBox *speedControlKdStepSpinBox;
    QLabel *speedControlKsStepLabel;
    QDoubleSpinBox *speedControlKsStepSpinBox;
    QLabel *speedControlKaStepLabel;
    QDoubleSpinBox *speedControlKaStepSpinBox;
    QSpacerItem *speedControlVerticalSpacer;
    QHBoxLayout *buttonLayout;
    QSpacerItem *buttonSpacer;
    QPushButton *okButton;
    QPushButton *cancelButton;

    void setupUi(QWidget *ControlChannelEditDialog)
    {
        if (ControlChannelEditDialog->objectName().isEmpty())
            ControlChannelEditDialog->setObjectName(QString::fromUtf8("ControlChannelEditDialog"));
        ControlChannelEditDialog->setProperty("modal", QVariant(false));
        ControlChannelEditDialog->resize(1016, 500);
        mainLayout = new QVBoxLayout(ControlChannelEditDialog);
        mainLayout->setObjectName(QString::fromUtf8("mainLayout"));
        tabWidget = new QTabWidget(ControlChannelEditDialog);
        tabWidget->setObjectName(QString::fromUtf8("tabWidget"));
        basicInfoTab = new QWidget();
        basicInfoTab->setObjectName(QString::fromUtf8("basicInfoTab"));
        basicInfoLayout = new QFormLayout(basicInfoTab);
        basicInfoLayout->setObjectName(QString::fromUtf8("basicInfoLayout"));
        channelNameLabel = new QLabel(basicInfoTab);
        channelNameLabel->setObjectName(QString::fromUtf8("channelNameLabel"));

        basicInfoLayout->setWidget(0, QFormLayout::LabelRole, channelNameLabel);

        channelNameEdit = new QLineEdit(basicInfoTab);
        channelNameEdit->setObjectName(QString::fromUtf8("channelNameEdit"));

        basicInfoLayout->setWidget(0, QFormLayout::FieldRole, channelNameEdit);

        lcIdLabel = new QLabel(basicInfoTab);
        lcIdLabel->setObjectName(QString::fromUtf8("lcIdLabel"));

        basicInfoLayout->setWidget(1, QFormLayout::LabelRole, lcIdLabel);

        lcIdSpinBox = new QSpinBox(basicInfoTab);
        lcIdSpinBox->setObjectName(QString::fromUtf8("lcIdSpinBox"));
        lcIdSpinBox->setMinimum(1);
        lcIdSpinBox->setMaximum(999);
        lcIdSpinBox->setValue(1);

        basicInfoLayout->setWidget(1, QFormLayout::FieldRole, lcIdSpinBox);

        stationIdLabel = new QLabel(basicInfoTab);
        stationIdLabel->setObjectName(QString::fromUtf8("stationIdLabel"));

        basicInfoLayout->setWidget(2, QFormLayout::LabelRole, stationIdLabel);

        stationIdSpinBox = new QSpinBox(basicInfoTab);
        stationIdSpinBox->setObjectName(QString::fromUtf8("stationIdSpinBox"));
        stationIdSpinBox->setMinimum(1);
        stationIdSpinBox->setMaximum(999);
        stationIdSpinBox->setValue(1);

        basicInfoLayout->setWidget(2, QFormLayout::FieldRole, stationIdSpinBox);

        enableLabel = new QLabel(basicInfoTab);
        enableLabel->setObjectName(QString::fromUtf8("enableLabel"));

        basicInfoLayout->setWidget(3, QFormLayout::LabelRole, enableLabel);

        enableCheckBox = new QCheckBox(basicInfoTab);
        enableCheckBox->setObjectName(QString::fromUtf8("enableCheckBox"));
        enableCheckBox->setChecked(true);

        basicInfoLayout->setWidget(3, QFormLayout::FieldRole, enableCheckBox);

        controlModeLabel = new QLabel(basicInfoTab);
        controlModeLabel->setObjectName(QString::fromUtf8("controlModeLabel"));

        basicInfoLayout->setWidget(4, QFormLayout::LabelRole, controlModeLabel);

        controlModeCombo = new QComboBox(basicInfoTab);
        controlModeCombo->addItem(QString());
        controlModeCombo->addItem(QString());
        controlModeCombo->addItem(QString());
        controlModeCombo->setObjectName(QString::fromUtf8("controlModeCombo"));

        basicInfoLayout->setWidget(4, QFormLayout::FieldRole, controlModeCombo);

        verticalSpacer_2 = new QSpacerItem(0, 0, QSizePolicy::Minimum, QSizePolicy::Expanding);

        basicInfoLayout->setItem(5, QFormLayout::FieldRole, verticalSpacer_2);

        tabWidget->addTab(basicInfoTab, QString());
        associationTab = new QWidget();
        associationTab->setObjectName(QString::fromUtf8("associationTab"));
        associationLayout = new QFormLayout(associationTab);
        associationLayout->setObjectName(QString::fromUtf8("associationLayout"));
        hardwareLabel = new QLabel(associationTab);
        hardwareLabel->setObjectName(QString::fromUtf8("hardwareLabel"));

        associationLayout->setWidget(0, QFormLayout::LabelRole, hardwareLabel);

        hardwareLayout = new QHBoxLayout();
        hardwareLayout->setObjectName(QString::fromUtf8("hardwareLayout"));
        hardwareGroupCombo = new QComboBox(associationTab);
        hardwareGroupCombo->setObjectName(QString::fromUtf8("hardwareGroupCombo"));

        hardwareLayout->addWidget(hardwareGroupCombo);

        hardwareChannelCombo = new QComboBox(associationTab);
        hardwareChannelCombo->setObjectName(QString::fromUtf8("hardwareChannelCombo"));

        hardwareLayout->addWidget(hardwareChannelCombo);


        associationLayout->setLayout(0, QFormLayout::FieldRole, hardwareLayout);

        load1SensorLabel = new QLabel(associationTab);
        load1SensorLabel->setObjectName(QString::fromUtf8("load1SensorLabel"));

        associationLayout->setWidget(1, QFormLayout::LabelRole, load1SensorLabel);

        load1SensorLayout = new QHBoxLayout();
        load1SensorLayout->setObjectName(QString::fromUtf8("load1SensorLayout"));
        load1SensorGroupCombo = new QComboBox(associationTab);
        load1SensorGroupCombo->setObjectName(QString::fromUtf8("load1SensorGroupCombo"));

        load1SensorLayout->addWidget(load1SensorGroupCombo);

        load1SensorDeviceCombo = new QComboBox(associationTab);
        load1SensorDeviceCombo->setObjectName(QString::fromUtf8("load1SensorDeviceCombo"));

        load1SensorLayout->addWidget(load1SensorDeviceCombo);


        associationLayout->setLayout(1, QFormLayout::FieldRole, load1SensorLayout);

        load2SensorLabel = new QLabel(associationTab);
        load2SensorLabel->setObjectName(QString::fromUtf8("load2SensorLabel"));

        associationLayout->setWidget(2, QFormLayout::LabelRole, load2SensorLabel);

        load2SensorLayout = new QHBoxLayout();
        load2SensorLayout->setObjectName(QString::fromUtf8("load2SensorLayout"));
        load2SensorGroupCombo = new QComboBox(associationTab);
        load2SensorGroupCombo->setObjectName(QString::fromUtf8("load2SensorGroupCombo"));

        load2SensorLayout->addWidget(load2SensorGroupCombo);

        load2SensorDeviceCombo = new QComboBox(associationTab);
        load2SensorDeviceCombo->setObjectName(QString::fromUtf8("load2SensorDeviceCombo"));

        load2SensorLayout->addWidget(load2SensorDeviceCombo);


        associationLayout->setLayout(2, QFormLayout::FieldRole, load2SensorLayout);

        positionSensorLabel = new QLabel(associationTab);
        positionSensorLabel->setObjectName(QString::fromUtf8("positionSensorLabel"));

        associationLayout->setWidget(3, QFormLayout::LabelRole, positionSensorLabel);

        positionSensorLayout = new QHBoxLayout();
        positionSensorLayout->setObjectName(QString::fromUtf8("positionSensorLayout"));
        positionSensorGroupCombo = new QComboBox(associationTab);
        positionSensorGroupCombo->setObjectName(QString::fromUtf8("positionSensorGroupCombo"));

        positionSensorLayout->addWidget(positionSensorGroupCombo);

        positionSensorDeviceCombo = new QComboBox(associationTab);
        positionSensorDeviceCombo->setObjectName(QString::fromUtf8("positionSensorDeviceCombo"));

        positionSensorLayout->addWidget(positionSensorDeviceCombo);


        associationLayout->setLayout(3, QFormLayout::FieldRole, positionSensorLayout);

        controlActuatorLabel = new QLabel(associationTab);
        controlActuatorLabel->setObjectName(QString::fromUtf8("controlActuatorLabel"));

        associationLayout->setWidget(4, QFormLayout::LabelRole, controlActuatorLabel);

        controlActuatorLayout = new QHBoxLayout();
        controlActuatorLayout->setObjectName(QString::fromUtf8("controlActuatorLayout"));
        controlActuatorGroupCombo = new QComboBox(associationTab);
        controlActuatorGroupCombo->setObjectName(QString::fromUtf8("controlActuatorGroupCombo"));

        controlActuatorLayout->addWidget(controlActuatorGroupCombo);

        controlActuatorDeviceCombo = new QComboBox(associationTab);
        controlActuatorDeviceCombo->setObjectName(QString::fromUtf8("controlActuatorDeviceCombo"));

        controlActuatorLayout->addWidget(controlActuatorDeviceCombo);


        associationLayout->setLayout(4, QFormLayout::FieldRole, controlActuatorLayout);

        verticalSpacer_3 = new QSpacerItem(0, 0, QSizePolicy::Minimum, QSizePolicy::Expanding);

        associationLayout->setItem(5, QFormLayout::FieldRole, verticalSpacer_3);

        tabWidget->addTab(associationTab, QString());
        polarityTab = new QWidget();
        polarityTab->setObjectName(QString::fromUtf8("polarityTab"));
        polarityLayout = new QFormLayout(polarityTab);
        polarityLayout->setObjectName(QString::fromUtf8("polarityLayout"));
        servoControlPolarityLabel = new QLabel(polarityTab);
        servoControlPolarityLabel->setObjectName(QString::fromUtf8("servoControlPolarityLabel"));

        polarityLayout->setWidget(0, QFormLayout::LabelRole, servoControlPolarityLabel);

        servoControlPolarityCombo = new QComboBox(polarityTab);
        servoControlPolarityCombo->addItem(QString());
        servoControlPolarityCombo->addItem(QString());
        servoControlPolarityCombo->addItem(QString());
        servoControlPolarityCombo->addItem(QString());
        servoControlPolarityCombo->setObjectName(QString::fromUtf8("servoControlPolarityCombo"));

        polarityLayout->setWidget(0, QFormLayout::FieldRole, servoControlPolarityCombo);

        payload1PolarityLabel = new QLabel(polarityTab);
        payload1PolarityLabel->setObjectName(QString::fromUtf8("payload1PolarityLabel"));

        polarityLayout->setWidget(1, QFormLayout::LabelRole, payload1PolarityLabel);

        payload1PolarityCombo = new QComboBox(polarityTab);
        payload1PolarityCombo->addItem(QString());
        payload1PolarityCombo->addItem(QString());
        payload1PolarityCombo->addItem(QString());
        payload1PolarityCombo->addItem(QString());
        payload1PolarityCombo->setObjectName(QString::fromUtf8("payload1PolarityCombo"));

        polarityLayout->setWidget(1, QFormLayout::FieldRole, payload1PolarityCombo);

        payload2PolarityLabel = new QLabel(polarityTab);
        payload2PolarityLabel->setObjectName(QString::fromUtf8("payload2PolarityLabel"));

        polarityLayout->setWidget(2, QFormLayout::LabelRole, payload2PolarityLabel);

        payload2PolarityCombo = new QComboBox(polarityTab);
        payload2PolarityCombo->addItem(QString());
        payload2PolarityCombo->addItem(QString());
        payload2PolarityCombo->addItem(QString());
        payload2PolarityCombo->addItem(QString());
        payload2PolarityCombo->setObjectName(QString::fromUtf8("payload2PolarityCombo"));

        polarityLayout->setWidget(2, QFormLayout::FieldRole, payload2PolarityCombo);

        positionPolarityLabel = new QLabel(polarityTab);
        positionPolarityLabel->setObjectName(QString::fromUtf8("positionPolarityLabel"));

        polarityLayout->setWidget(3, QFormLayout::LabelRole, positionPolarityLabel);

        positionPolarityCombo = new QComboBox(polarityTab);
        positionPolarityCombo->addItem(QString());
        positionPolarityCombo->addItem(QString());
        positionPolarityCombo->addItem(QString());
        positionPolarityCombo->addItem(QString());
        positionPolarityCombo->setObjectName(QString::fromUtf8("positionPolarityCombo"));

        polarityLayout->setWidget(3, QFormLayout::FieldRole, positionPolarityCombo);

        verticalSpacer_4 = new QSpacerItem(0, 0, QSizePolicy::Minimum, QSizePolicy::Expanding);

        polarityLayout->setItem(4, QFormLayout::FieldRole, verticalSpacer_4);

        tabWidget->addTab(polarityTab, QString());
        thresholdsTab = new QWidget();
        thresholdsTab->setObjectName(QString::fromUtf8("thresholdsTab"));
        thresholdsLayout = new QVBoxLayout(thresholdsTab);
        thresholdsLayout->setObjectName(QString::fromUtf8("thresholdsLayout"));
        tabWidget_2 = new QTabWidget(thresholdsTab);
        tabWidget_2->setObjectName(QString::fromUtf8("tabWidget_2"));
        payloadLimitsTab = new QWidget();
        payloadLimitsTab->setObjectName(QString::fromUtf8("payloadLimitsTab"));
        payloadLimitsLayout = new QVBoxLayout(payloadLimitsTab);
        payloadLimitsLayout->setObjectName(QString::fromUtf8("payloadLimitsLayout"));
        payloadScrollArea = new QScrollArea(payloadLimitsTab);
        payloadScrollArea->setObjectName(QString::fromUtf8("payloadScrollArea"));
        payloadScrollArea->setWidgetResizable(true);
        payloadScrollAreaWidgetContents = new QWidget();
        payloadScrollAreaWidgetContents->setObjectName(QString::fromUtf8("payloadScrollAreaWidgetContents"));
        payloadScrollAreaWidgetContents->setGeometry(QRect(0, -236, 927, 594));
        payloadMainLayout = new QVBoxLayout(payloadScrollAreaWidgetContents);
        payloadMainLayout->setObjectName(QString::fromUtf8("payloadMainLayout"));
        payloadInnerGroupBox = new QGroupBox(payloadScrollAreaWidgetContents);
        payloadInnerGroupBox->setObjectName(QString::fromUtf8("payloadInnerGroupBox"));
        payloadInnerGridLayout = new QGridLayout(payloadInnerGroupBox);
        payloadInnerGridLayout->setObjectName(QString::fromUtf8("payloadInnerGridLayout"));
        payloadInnerMinLabel = new QLabel(payloadInnerGroupBox);
        payloadInnerMinLabel->setObjectName(QString::fromUtf8("payloadInnerMinLabel"));

        payloadInnerGridLayout->addWidget(payloadInnerMinLabel, 0, 0, 1, 1);

        payloadInnerMinSpinBox = new QDoubleSpinBox(payloadInnerGroupBox);
        payloadInnerMinSpinBox->setObjectName(QString::fromUtf8("payloadInnerMinSpinBox"));
        payloadInnerMinSpinBox->setDecimals(3);
        payloadInnerMinSpinBox->setMinimum(-99999.000000000000000);
        payloadInnerMinSpinBox->setMaximum(99999.000000000000000);
        payloadInnerMinSpinBox->setSingleStep(0.100000000000000);
        payloadInnerMinSpinBox->setValue(0.000000000000000);

        payloadInnerGridLayout->addWidget(payloadInnerMinSpinBox, 0, 1, 1, 1);

        payloadInnerMaxLabel = new QLabel(payloadInnerGroupBox);
        payloadInnerMaxLabel->setObjectName(QString::fromUtf8("payloadInnerMaxLabel"));

        payloadInnerGridLayout->addWidget(payloadInnerMaxLabel, 1, 0, 1, 1);

        payloadInnerMaxSpinBox = new QDoubleSpinBox(payloadInnerGroupBox);
        payloadInnerMaxSpinBox->setObjectName(QString::fromUtf8("payloadInnerMaxSpinBox"));
        payloadInnerMaxSpinBox->setDecimals(3);
        payloadInnerMaxSpinBox->setMinimum(-99999.000000000000000);
        payloadInnerMaxSpinBox->setMaximum(99999.000000000000000);
        payloadInnerMaxSpinBox->setSingleStep(0.100000000000000);
        payloadInnerMaxSpinBox->setValue(0.000000000000000);

        payloadInnerGridLayout->addWidget(payloadInnerMaxSpinBox, 1, 1, 1, 1);

        payloadInnerUnitLabel = new QLabel(payloadInnerGroupBox);
        payloadInnerUnitLabel->setObjectName(QString::fromUtf8("payloadInnerUnitLabel"));

        payloadInnerGridLayout->addWidget(payloadInnerUnitLabel, 2, 0, 1, 1);

        payloadInnerUnitCombo = new QComboBox(payloadInnerGroupBox);
        payloadInnerUnitCombo->addItem(QString());
        payloadInnerUnitCombo->addItem(QString());
        payloadInnerUnitCombo->setObjectName(QString::fromUtf8("payloadInnerUnitCombo"));

        payloadInnerGridLayout->addWidget(payloadInnerUnitCombo, 2, 1, 1, 1);

        payloadInnerDurationLabel = new QLabel(payloadInnerGroupBox);
        payloadInnerDurationLabel->setObjectName(QString::fromUtf8("payloadInnerDurationLabel"));

        payloadInnerGridLayout->addWidget(payloadInnerDurationLabel, 3, 0, 1, 1);

        payloadInnerDurationSpinBox = new QDoubleSpinBox(payloadInnerGroupBox);
        payloadInnerDurationSpinBox->setObjectName(QString::fromUtf8("payloadInnerDurationSpinBox"));
        payloadInnerDurationSpinBox->setDecimals(3);
        payloadInnerDurationSpinBox->setMinimum(0.000000000000000);
        payloadInnerDurationSpinBox->setMaximum(9999.000000000000000);
        payloadInnerDurationSpinBox->setSingleStep(0.100000000000000);
        payloadInnerDurationSpinBox->setValue(0.000000000000000);

        payloadInnerGridLayout->addWidget(payloadInnerDurationSpinBox, 3, 1, 1, 1);

        payloadInnerEnabledLabel = new QLabel(payloadInnerGroupBox);
        payloadInnerEnabledLabel->setObjectName(QString::fromUtf8("payloadInnerEnabledLabel"));

        payloadInnerGridLayout->addWidget(payloadInnerEnabledLabel, 4, 0, 1, 1);

        payloadInnerEnabledCheckBox = new QCheckBox(payloadInnerGroupBox);
        payloadInnerEnabledCheckBox->setObjectName(QString::fromUtf8("payloadInnerEnabledCheckBox"));
        payloadInnerEnabledCheckBox->setChecked(false);

        payloadInnerGridLayout->addWidget(payloadInnerEnabledCheckBox, 4, 1, 1, 1);

        payloadInnerTypeLabel = new QLabel(payloadInnerGroupBox);
        payloadInnerTypeLabel->setObjectName(QString::fromUtf8("payloadInnerTypeLabel"));

        payloadInnerGridLayout->addWidget(payloadInnerTypeLabel, 5, 0, 1, 1);

        payloadInnerTypeCombo = new QComboBox(payloadInnerGroupBox);
        payloadInnerTypeCombo->addItem(QString());
        payloadInnerTypeCombo->addItem(QString());
        payloadInnerTypeCombo->addItem(QString());
        payloadInnerTypeCombo->setObjectName(QString::fromUtf8("payloadInnerTypeCombo"));

        payloadInnerGridLayout->addWidget(payloadInnerTypeCombo, 5, 1, 1, 1);


        payloadMainLayout->addWidget(payloadInnerGroupBox);

        payloadOuterGroupBox = new QGroupBox(payloadScrollAreaWidgetContents);
        payloadOuterGroupBox->setObjectName(QString::fromUtf8("payloadOuterGroupBox"));
        payloadOuterGridLayout = new QGridLayout(payloadOuterGroupBox);
        payloadOuterGridLayout->setObjectName(QString::fromUtf8("payloadOuterGridLayout"));
        payloadOuterMinLabel = new QLabel(payloadOuterGroupBox);
        payloadOuterMinLabel->setObjectName(QString::fromUtf8("payloadOuterMinLabel"));

        payloadOuterGridLayout->addWidget(payloadOuterMinLabel, 0, 0, 1, 1);

        payloadOuterMinSpinBox = new QDoubleSpinBox(payloadOuterGroupBox);
        payloadOuterMinSpinBox->setObjectName(QString::fromUtf8("payloadOuterMinSpinBox"));
        payloadOuterMinSpinBox->setDecimals(3);
        payloadOuterMinSpinBox->setMinimum(-99999.000000000000000);
        payloadOuterMinSpinBox->setMaximum(99999.000000000000000);
        payloadOuterMinSpinBox->setSingleStep(0.100000000000000);
        payloadOuterMinSpinBox->setValue(0.000000000000000);

        payloadOuterGridLayout->addWidget(payloadOuterMinSpinBox, 0, 1, 1, 1);

        payloadOuterMaxLabel = new QLabel(payloadOuterGroupBox);
        payloadOuterMaxLabel->setObjectName(QString::fromUtf8("payloadOuterMaxLabel"));

        payloadOuterGridLayout->addWidget(payloadOuterMaxLabel, 1, 0, 1, 1);

        payloadOuterMaxSpinBox = new QDoubleSpinBox(payloadOuterGroupBox);
        payloadOuterMaxSpinBox->setObjectName(QString::fromUtf8("payloadOuterMaxSpinBox"));
        payloadOuterMaxSpinBox->setDecimals(3);
        payloadOuterMaxSpinBox->setMinimum(-99999.000000000000000);
        payloadOuterMaxSpinBox->setMaximum(99999.000000000000000);
        payloadOuterMaxSpinBox->setSingleStep(0.100000000000000);
        payloadOuterMaxSpinBox->setValue(0.000000000000000);

        payloadOuterGridLayout->addWidget(payloadOuterMaxSpinBox, 1, 1, 1, 1);

        payloadOuterUnitLabel = new QLabel(payloadOuterGroupBox);
        payloadOuterUnitLabel->setObjectName(QString::fromUtf8("payloadOuterUnitLabel"));

        payloadOuterGridLayout->addWidget(payloadOuterUnitLabel, 2, 0, 1, 1);

        payloadOuterUnitCombo = new QComboBox(payloadOuterGroupBox);
        payloadOuterUnitCombo->addItem(QString());
        payloadOuterUnitCombo->addItem(QString());
        payloadOuterUnitCombo->setObjectName(QString::fromUtf8("payloadOuterUnitCombo"));

        payloadOuterGridLayout->addWidget(payloadOuterUnitCombo, 2, 1, 1, 1);

        payloadOuterDurationLabel = new QLabel(payloadOuterGroupBox);
        payloadOuterDurationLabel->setObjectName(QString::fromUtf8("payloadOuterDurationLabel"));

        payloadOuterGridLayout->addWidget(payloadOuterDurationLabel, 3, 0, 1, 1);

        payloadOuterDurationSpinBox = new QDoubleSpinBox(payloadOuterGroupBox);
        payloadOuterDurationSpinBox->setObjectName(QString::fromUtf8("payloadOuterDurationSpinBox"));
        payloadOuterDurationSpinBox->setDecimals(3);
        payloadOuterDurationSpinBox->setMinimum(0.000000000000000);
        payloadOuterDurationSpinBox->setMaximum(9999.000000000000000);
        payloadOuterDurationSpinBox->setSingleStep(0.100000000000000);
        payloadOuterDurationSpinBox->setValue(0.000000000000000);

        payloadOuterGridLayout->addWidget(payloadOuterDurationSpinBox, 3, 1, 1, 1);

        payloadOuterEnabledLabel = new QLabel(payloadOuterGroupBox);
        payloadOuterEnabledLabel->setObjectName(QString::fromUtf8("payloadOuterEnabledLabel"));

        payloadOuterGridLayout->addWidget(payloadOuterEnabledLabel, 4, 0, 1, 1);

        payloadOuterEnabledCheckBox = new QCheckBox(payloadOuterGroupBox);
        payloadOuterEnabledCheckBox->setObjectName(QString::fromUtf8("payloadOuterEnabledCheckBox"));
        payloadOuterEnabledCheckBox->setChecked(false);

        payloadOuterGridLayout->addWidget(payloadOuterEnabledCheckBox, 4, 1, 1, 1);

        payloadOuterTypeLabel = new QLabel(payloadOuterGroupBox);
        payloadOuterTypeLabel->setObjectName(QString::fromUtf8("payloadOuterTypeLabel"));

        payloadOuterGridLayout->addWidget(payloadOuterTypeLabel, 5, 0, 1, 1);

        payloadOuterTypeCombo = new QComboBox(payloadOuterGroupBox);
        payloadOuterTypeCombo->addItem(QString());
        payloadOuterTypeCombo->addItem(QString());
        payloadOuterTypeCombo->addItem(QString());
        payloadOuterTypeCombo->setObjectName(QString::fromUtf8("payloadOuterTypeCombo"));

        payloadOuterGridLayout->addWidget(payloadOuterTypeCombo, 5, 1, 1, 1);


        payloadMainLayout->addWidget(payloadOuterGroupBox);

        payloadMainGroupBox = new QGroupBox(payloadScrollAreaWidgetContents);
        payloadMainGroupBox->setObjectName(QString::fromUtf8("payloadMainGroupBox"));
        payloadMainGridLayout = new QGridLayout(payloadMainGroupBox);
        payloadMainGridLayout->setObjectName(QString::fromUtf8("payloadMainGridLayout"));
        payloadMainMaxLabel = new QLabel(payloadMainGroupBox);
        payloadMainMaxLabel->setObjectName(QString::fromUtf8("payloadMainMaxLabel"));

        payloadMainGridLayout->addWidget(payloadMainMaxLabel, 1, 0, 1, 1);

        payloadMainUnitLabel = new QLabel(payloadMainGroupBox);
        payloadMainUnitLabel->setObjectName(QString::fromUtf8("payloadMainUnitLabel"));

        payloadMainGridLayout->addWidget(payloadMainUnitLabel, 2, 0, 1, 1);

        payloadMainTypeLabel = new QLabel(payloadMainGroupBox);
        payloadMainTypeLabel->setObjectName(QString::fromUtf8("payloadMainTypeLabel"));

        payloadMainGridLayout->addWidget(payloadMainTypeLabel, 5, 0, 1, 1);

        payloadMainTypeCombo = new QComboBox(payloadMainGroupBox);
        payloadMainTypeCombo->addItem(QString());
        payloadMainTypeCombo->addItem(QString());
        payloadMainTypeCombo->addItem(QString());
        payloadMainTypeCombo->setObjectName(QString::fromUtf8("payloadMainTypeCombo"));

        payloadMainGridLayout->addWidget(payloadMainTypeCombo, 5, 1, 1, 1);

        payloadMainMinLabel = new QLabel(payloadMainGroupBox);
        payloadMainMinLabel->setObjectName(QString::fromUtf8("payloadMainMinLabel"));

        payloadMainGridLayout->addWidget(payloadMainMinLabel, 0, 0, 1, 1);

        payloadMainMinSpinBox = new QDoubleSpinBox(payloadMainGroupBox);
        payloadMainMinSpinBox->setObjectName(QString::fromUtf8("payloadMainMinSpinBox"));
        payloadMainMinSpinBox->setDecimals(3);
        payloadMainMinSpinBox->setMinimum(-99999.000000000000000);
        payloadMainMinSpinBox->setMaximum(99999.000000000000000);
        payloadMainMinSpinBox->setSingleStep(0.100000000000000);
        payloadMainMinSpinBox->setValue(-200.000000000000000);

        payloadMainGridLayout->addWidget(payloadMainMinSpinBox, 0, 1, 1, 1);

        payloadMainMaxSpinBox = new QDoubleSpinBox(payloadMainGroupBox);
        payloadMainMaxSpinBox->setObjectName(QString::fromUtf8("payloadMainMaxSpinBox"));
        payloadMainMaxSpinBox->setDecimals(3);
        payloadMainMaxSpinBox->setMinimum(-99999.000000000000000);
        payloadMainMaxSpinBox->setMaximum(99999.000000000000000);
        payloadMainMaxSpinBox->setSingleStep(0.100000000000000);
        payloadMainMaxSpinBox->setValue(200.000000000000000);

        payloadMainGridLayout->addWidget(payloadMainMaxSpinBox, 1, 1, 1, 1);

        payloadMainDurationLabel = new QLabel(payloadMainGroupBox);
        payloadMainDurationLabel->setObjectName(QString::fromUtf8("payloadMainDurationLabel"));

        payloadMainGridLayout->addWidget(payloadMainDurationLabel, 3, 0, 1, 1);

        payloadMainDurationSpinBox = new QDoubleSpinBox(payloadMainGroupBox);
        payloadMainDurationSpinBox->setObjectName(QString::fromUtf8("payloadMainDurationSpinBox"));
        payloadMainDurationSpinBox->setDecimals(3);
        payloadMainDurationSpinBox->setMinimum(0.000000000000000);
        payloadMainDurationSpinBox->setMaximum(9999.000000000000000);
        payloadMainDurationSpinBox->setSingleStep(0.100000000000000);
        payloadMainDurationSpinBox->setValue(1.000000000000000);

        payloadMainGridLayout->addWidget(payloadMainDurationSpinBox, 3, 1, 1, 1);

        payloadMainUnitCombo = new QComboBox(payloadMainGroupBox);
        payloadMainUnitCombo->addItem(QString());
        payloadMainUnitCombo->addItem(QString());
        payloadMainUnitCombo->setObjectName(QString::fromUtf8("payloadMainUnitCombo"));

        payloadMainGridLayout->addWidget(payloadMainUnitCombo, 2, 1, 1, 1);

        payloadMainEnabledLabel = new QLabel(payloadMainGroupBox);
        payloadMainEnabledLabel->setObjectName(QString::fromUtf8("payloadMainEnabledLabel"));

        payloadMainGridLayout->addWidget(payloadMainEnabledLabel, 4, 0, 1, 1);

        payloadMainEnabledCheckBox = new QCheckBox(payloadMainGroupBox);
        payloadMainEnabledCheckBox->setObjectName(QString::fromUtf8("payloadMainEnabledCheckBox"));
        payloadMainEnabledCheckBox->setChecked(true);

        payloadMainGridLayout->addWidget(payloadMainEnabledCheckBox, 4, 1, 1, 1);

        verticalSpacer_5 = new QSpacerItem(0, 0, QSizePolicy::Minimum, QSizePolicy::Expanding);

        payloadMainGridLayout->addItem(verticalSpacer_5, 6, 1, 1, 1);


        payloadMainLayout->addWidget(payloadMainGroupBox);

        payloadScrollArea->setWidget(payloadScrollAreaWidgetContents);

        payloadLimitsLayout->addWidget(payloadScrollArea);

        tabWidget_2->addTab(payloadLimitsTab, QString());
        positionLimitsTab = new QWidget();
        positionLimitsTab->setObjectName(QString::fromUtf8("positionLimitsTab"));
        positionLimitsLayout = new QVBoxLayout(positionLimitsTab);
        positionLimitsLayout->setObjectName(QString::fromUtf8("positionLimitsLayout"));
        positionScrollArea = new QScrollArea(positionLimitsTab);
        positionScrollArea->setObjectName(QString::fromUtf8("positionScrollArea"));
        positionScrollArea->setWidgetResizable(true);
        positionScrollAreaWidgetContents = new QWidget();
        positionScrollAreaWidgetContents->setObjectName(QString::fromUtf8("positionScrollAreaWidgetContents"));
        positionScrollAreaWidgetContents->setGeometry(QRect(0, 0, 220, 588));
        positionMainLayout = new QVBoxLayout(positionScrollAreaWidgetContents);
        positionMainLayout->setObjectName(QString::fromUtf8("positionMainLayout"));
        positionInnerGroupBox = new QGroupBox(positionScrollAreaWidgetContents);
        positionInnerGroupBox->setObjectName(QString::fromUtf8("positionInnerGroupBox"));
        positionInnerGridLayout = new QGridLayout(positionInnerGroupBox);
        positionInnerGridLayout->setObjectName(QString::fromUtf8("positionInnerGridLayout"));
        positionInnerMinLabel = new QLabel(positionInnerGroupBox);
        positionInnerMinLabel->setObjectName(QString::fromUtf8("positionInnerMinLabel"));

        positionInnerGridLayout->addWidget(positionInnerMinLabel, 0, 0, 1, 1);

        positionInnerMinSpinBox = new QDoubleSpinBox(positionInnerGroupBox);
        positionInnerMinSpinBox->setObjectName(QString::fromUtf8("positionInnerMinSpinBox"));
        positionInnerMinSpinBox->setDecimals(3);
        positionInnerMinSpinBox->setMinimum(-9999.000000000000000);
        positionInnerMinSpinBox->setMaximum(9999.000000000000000);
        positionInnerMinSpinBox->setSingleStep(0.100000000000000);
        positionInnerMinSpinBox->setValue(0.000000000000000);

        positionInnerGridLayout->addWidget(positionInnerMinSpinBox, 0, 1, 1, 1);

        positionInnerMaxLabel = new QLabel(positionInnerGroupBox);
        positionInnerMaxLabel->setObjectName(QString::fromUtf8("positionInnerMaxLabel"));

        positionInnerGridLayout->addWidget(positionInnerMaxLabel, 1, 0, 1, 1);

        positionInnerMaxSpinBox = new QDoubleSpinBox(positionInnerGroupBox);
        positionInnerMaxSpinBox->setObjectName(QString::fromUtf8("positionInnerMaxSpinBox"));
        positionInnerMaxSpinBox->setDecimals(3);
        positionInnerMaxSpinBox->setMinimum(-9999.000000000000000);
        positionInnerMaxSpinBox->setMaximum(9999.000000000000000);
        positionInnerMaxSpinBox->setSingleStep(0.100000000000000);
        positionInnerMaxSpinBox->setValue(0.000000000000000);

        positionInnerGridLayout->addWidget(positionInnerMaxSpinBox, 1, 1, 1, 1);

        positionInnerUnitLabel = new QLabel(positionInnerGroupBox);
        positionInnerUnitLabel->setObjectName(QString::fromUtf8("positionInnerUnitLabel"));

        positionInnerGridLayout->addWidget(positionInnerUnitLabel, 2, 0, 1, 1);

        positionInnerUnitCombo = new QComboBox(positionInnerGroupBox);
        positionInnerUnitCombo->addItem(QString());
        positionInnerUnitCombo->addItem(QString());
        positionInnerUnitCombo->setObjectName(QString::fromUtf8("positionInnerUnitCombo"));

        positionInnerGridLayout->addWidget(positionInnerUnitCombo, 2, 1, 1, 1);

        positionInnerDurationLabel = new QLabel(positionInnerGroupBox);
        positionInnerDurationLabel->setObjectName(QString::fromUtf8("positionInnerDurationLabel"));

        positionInnerGridLayout->addWidget(positionInnerDurationLabel, 3, 0, 1, 1);

        positionInnerDurationSpinBox = new QDoubleSpinBox(positionInnerGroupBox);
        positionInnerDurationSpinBox->setObjectName(QString::fromUtf8("positionInnerDurationSpinBox"));
        positionInnerDurationSpinBox->setDecimals(3);
        positionInnerDurationSpinBox->setMinimum(0.000000000000000);
        positionInnerDurationSpinBox->setMaximum(9999.000000000000000);
        positionInnerDurationSpinBox->setSingleStep(0.100000000000000);
        positionInnerDurationSpinBox->setValue(0.000000000000000);

        positionInnerGridLayout->addWidget(positionInnerDurationSpinBox, 3, 1, 1, 1);

        positionInnerEnabledLabel = new QLabel(positionInnerGroupBox);
        positionInnerEnabledLabel->setObjectName(QString::fromUtf8("positionInnerEnabledLabel"));

        positionInnerGridLayout->addWidget(positionInnerEnabledLabel, 4, 0, 1, 1);

        positionInnerEnabledCheckBox = new QCheckBox(positionInnerGroupBox);
        positionInnerEnabledCheckBox->setObjectName(QString::fromUtf8("positionInnerEnabledCheckBox"));
        positionInnerEnabledCheckBox->setChecked(false);

        positionInnerGridLayout->addWidget(positionInnerEnabledCheckBox, 4, 1, 1, 1);

        positionInnerTypeLabel = new QLabel(positionInnerGroupBox);
        positionInnerTypeLabel->setObjectName(QString::fromUtf8("positionInnerTypeLabel"));

        positionInnerGridLayout->addWidget(positionInnerTypeLabel, 5, 0, 1, 1);

        positionInnerTypeCombo = new QComboBox(positionInnerGroupBox);
        positionInnerTypeCombo->addItem(QString());
        positionInnerTypeCombo->addItem(QString());
        positionInnerTypeCombo->addItem(QString());
        positionInnerTypeCombo->setObjectName(QString::fromUtf8("positionInnerTypeCombo"));

        positionInnerGridLayout->addWidget(positionInnerTypeCombo, 5, 1, 1, 1);


        positionMainLayout->addWidget(positionInnerGroupBox);

        positionOuterGroupBox = new QGroupBox(positionScrollAreaWidgetContents);
        positionOuterGroupBox->setObjectName(QString::fromUtf8("positionOuterGroupBox"));
        positionOuterGridLayout = new QGridLayout(positionOuterGroupBox);
        positionOuterGridLayout->setObjectName(QString::fromUtf8("positionOuterGridLayout"));
        positionOuterMinLabel = new QLabel(positionOuterGroupBox);
        positionOuterMinLabel->setObjectName(QString::fromUtf8("positionOuterMinLabel"));

        positionOuterGridLayout->addWidget(positionOuterMinLabel, 0, 0, 1, 1);

        positionOuterMinSpinBox = new QDoubleSpinBox(positionOuterGroupBox);
        positionOuterMinSpinBox->setObjectName(QString::fromUtf8("positionOuterMinSpinBox"));
        positionOuterMinSpinBox->setDecimals(3);
        positionOuterMinSpinBox->setMinimum(-9999.000000000000000);
        positionOuterMinSpinBox->setMaximum(9999.000000000000000);
        positionOuterMinSpinBox->setSingleStep(0.100000000000000);
        positionOuterMinSpinBox->setValue(0.000000000000000);

        positionOuterGridLayout->addWidget(positionOuterMinSpinBox, 0, 1, 1, 1);

        positionOuterMaxLabel = new QLabel(positionOuterGroupBox);
        positionOuterMaxLabel->setObjectName(QString::fromUtf8("positionOuterMaxLabel"));

        positionOuterGridLayout->addWidget(positionOuterMaxLabel, 1, 0, 1, 1);

        positionOuterMaxSpinBox = new QDoubleSpinBox(positionOuterGroupBox);
        positionOuterMaxSpinBox->setObjectName(QString::fromUtf8("positionOuterMaxSpinBox"));
        positionOuterMaxSpinBox->setDecimals(3);
        positionOuterMaxSpinBox->setMinimum(-9999.000000000000000);
        positionOuterMaxSpinBox->setMaximum(9999.000000000000000);
        positionOuterMaxSpinBox->setSingleStep(0.100000000000000);
        positionOuterMaxSpinBox->setValue(0.000000000000000);

        positionOuterGridLayout->addWidget(positionOuterMaxSpinBox, 1, 1, 1, 1);

        positionOuterUnitLabel = new QLabel(positionOuterGroupBox);
        positionOuterUnitLabel->setObjectName(QString::fromUtf8("positionOuterUnitLabel"));

        positionOuterGridLayout->addWidget(positionOuterUnitLabel, 2, 0, 1, 1);

        positionOuterUnitCombo = new QComboBox(positionOuterGroupBox);
        positionOuterUnitCombo->addItem(QString());
        positionOuterUnitCombo->addItem(QString());
        positionOuterUnitCombo->setObjectName(QString::fromUtf8("positionOuterUnitCombo"));

        positionOuterGridLayout->addWidget(positionOuterUnitCombo, 2, 1, 1, 1);

        positionOuterDurationLabel = new QLabel(positionOuterGroupBox);
        positionOuterDurationLabel->setObjectName(QString::fromUtf8("positionOuterDurationLabel"));

        positionOuterGridLayout->addWidget(positionOuterDurationLabel, 3, 0, 1, 1);

        positionOuterDurationSpinBox = new QDoubleSpinBox(positionOuterGroupBox);
        positionOuterDurationSpinBox->setObjectName(QString::fromUtf8("positionOuterDurationSpinBox"));
        positionOuterDurationSpinBox->setDecimals(3);
        positionOuterDurationSpinBox->setMinimum(0.000000000000000);
        positionOuterDurationSpinBox->setMaximum(9999.000000000000000);
        positionOuterDurationSpinBox->setSingleStep(0.100000000000000);
        positionOuterDurationSpinBox->setValue(0.000000000000000);

        positionOuterGridLayout->addWidget(positionOuterDurationSpinBox, 3, 1, 1, 1);

        positionOuterEnabledLabel = new QLabel(positionOuterGroupBox);
        positionOuterEnabledLabel->setObjectName(QString::fromUtf8("positionOuterEnabledLabel"));

        positionOuterGridLayout->addWidget(positionOuterEnabledLabel, 4, 0, 1, 1);

        positionOuterEnabledCheckBox = new QCheckBox(positionOuterGroupBox);
        positionOuterEnabledCheckBox->setObjectName(QString::fromUtf8("positionOuterEnabledCheckBox"));
        positionOuterEnabledCheckBox->setChecked(false);

        positionOuterGridLayout->addWidget(positionOuterEnabledCheckBox, 4, 1, 1, 1);

        positionOuterTypeLabel = new QLabel(positionOuterGroupBox);
        positionOuterTypeLabel->setObjectName(QString::fromUtf8("positionOuterTypeLabel"));

        positionOuterGridLayout->addWidget(positionOuterTypeLabel, 5, 0, 1, 1);

        positionOuterTypeCombo = new QComboBox(positionOuterGroupBox);
        positionOuterTypeCombo->addItem(QString());
        positionOuterTypeCombo->addItem(QString());
        positionOuterTypeCombo->addItem(QString());
        positionOuterTypeCombo->setObjectName(QString::fromUtf8("positionOuterTypeCombo"));

        positionOuterGridLayout->addWidget(positionOuterTypeCombo, 5, 1, 1, 1);


        positionMainLayout->addWidget(positionOuterGroupBox);

        positionMainGroupBox = new QGroupBox(positionScrollAreaWidgetContents);
        positionMainGroupBox->setObjectName(QString::fromUtf8("positionMainGroupBox"));
        positionMainGridLayout = new QGridLayout(positionMainGroupBox);
        positionMainGridLayout->setObjectName(QString::fromUtf8("positionMainGridLayout"));
        positionMainMinLabel = new QLabel(positionMainGroupBox);
        positionMainMinLabel->setObjectName(QString::fromUtf8("positionMainMinLabel"));

        positionMainGridLayout->addWidget(positionMainMinLabel, 0, 0, 1, 1);

        positionMainMinSpinBox = new QDoubleSpinBox(positionMainGroupBox);
        positionMainMinSpinBox->setObjectName(QString::fromUtf8("positionMainMinSpinBox"));
        positionMainMinSpinBox->setDecimals(3);
        positionMainMinSpinBox->setMinimum(-9999.000000000000000);
        positionMainMinSpinBox->setMaximum(9999.000000000000000);
        positionMainMinSpinBox->setSingleStep(0.100000000000000);
        positionMainMinSpinBox->setValue(-200.000000000000000);

        positionMainGridLayout->addWidget(positionMainMinSpinBox, 0, 1, 1, 1);

        positionMainMaxLabel = new QLabel(positionMainGroupBox);
        positionMainMaxLabel->setObjectName(QString::fromUtf8("positionMainMaxLabel"));

        positionMainGridLayout->addWidget(positionMainMaxLabel, 1, 0, 1, 1);

        positionMainMaxSpinBox = new QDoubleSpinBox(positionMainGroupBox);
        positionMainMaxSpinBox->setObjectName(QString::fromUtf8("positionMainMaxSpinBox"));
        positionMainMaxSpinBox->setDecimals(3);
        positionMainMaxSpinBox->setMinimum(-9999.000000000000000);
        positionMainMaxSpinBox->setMaximum(9999.000000000000000);
        positionMainMaxSpinBox->setSingleStep(0.100000000000000);
        positionMainMaxSpinBox->setValue(200.000000000000000);

        positionMainGridLayout->addWidget(positionMainMaxSpinBox, 1, 1, 1, 1);

        positionMainUnitLabel = new QLabel(positionMainGroupBox);
        positionMainUnitLabel->setObjectName(QString::fromUtf8("positionMainUnitLabel"));

        positionMainGridLayout->addWidget(positionMainUnitLabel, 2, 0, 1, 1);

        positionMainUnitCombo = new QComboBox(positionMainGroupBox);
        positionMainUnitCombo->addItem(QString());
        positionMainUnitCombo->addItem(QString());
        positionMainUnitCombo->setObjectName(QString::fromUtf8("positionMainUnitCombo"));

        positionMainGridLayout->addWidget(positionMainUnitCombo, 2, 1, 1, 1);

        positionMainDurationLabel = new QLabel(positionMainGroupBox);
        positionMainDurationLabel->setObjectName(QString::fromUtf8("positionMainDurationLabel"));

        positionMainGridLayout->addWidget(positionMainDurationLabel, 3, 0, 1, 1);

        positionMainDurationSpinBox = new QDoubleSpinBox(positionMainGroupBox);
        positionMainDurationSpinBox->setObjectName(QString::fromUtf8("positionMainDurationSpinBox"));
        positionMainDurationSpinBox->setDecimals(3);
        positionMainDurationSpinBox->setMinimum(0.000000000000000);
        positionMainDurationSpinBox->setMaximum(9999.000000000000000);
        positionMainDurationSpinBox->setSingleStep(0.100000000000000);
        positionMainDurationSpinBox->setValue(1.000000000000000);

        positionMainGridLayout->addWidget(positionMainDurationSpinBox, 3, 1, 1, 1);

        positionMainEnabledLabel = new QLabel(positionMainGroupBox);
        positionMainEnabledLabel->setObjectName(QString::fromUtf8("positionMainEnabledLabel"));

        positionMainGridLayout->addWidget(positionMainEnabledLabel, 4, 0, 1, 1);

        positionMainEnabledCheckBox = new QCheckBox(positionMainGroupBox);
        positionMainEnabledCheckBox->setObjectName(QString::fromUtf8("positionMainEnabledCheckBox"));
        positionMainEnabledCheckBox->setChecked(true);

        positionMainGridLayout->addWidget(positionMainEnabledCheckBox, 4, 1, 1, 1);

        positionMainTypeLabel = new QLabel(positionMainGroupBox);
        positionMainTypeLabel->setObjectName(QString::fromUtf8("positionMainTypeLabel"));

        positionMainGridLayout->addWidget(positionMainTypeLabel, 5, 0, 1, 1);

        positionMainTypeCombo = new QComboBox(positionMainGroupBox);
        positionMainTypeCombo->addItem(QString());
        positionMainTypeCombo->addItem(QString());
        positionMainTypeCombo->addItem(QString());
        positionMainTypeCombo->setObjectName(QString::fromUtf8("positionMainTypeCombo"));

        positionMainGridLayout->addWidget(positionMainTypeCombo, 5, 1, 1, 1);


        positionMainLayout->addWidget(positionMainGroupBox);

        positionScrollArea->setWidget(positionScrollAreaWidgetContents);

        positionLimitsLayout->addWidget(positionScrollArea);

        tabWidget_2->addTab(positionLimitsTab, QString());
        speedLimitsTab = new QWidget();
        speedLimitsTab->setObjectName(QString::fromUtf8("speedLimitsTab"));
        speedLimitsLayout = new QVBoxLayout(speedLimitsTab);
        speedLimitsLayout->setObjectName(QString::fromUtf8("speedLimitsLayout"));
        speedScrollArea = new QScrollArea(speedLimitsTab);
        speedScrollArea->setObjectName(QString::fromUtf8("speedScrollArea"));
        speedScrollArea->setWidgetResizable(true);
        speedScrollAreaWidgetContents = new QWidget();
        speedScrollAreaWidgetContents->setObjectName(QString::fromUtf8("speedScrollAreaWidgetContents"));
        speedScrollAreaWidgetContents->setGeometry(QRect(0, 0, 220, 588));
        speedMainLayout = new QVBoxLayout(speedScrollAreaWidgetContents);
        speedMainLayout->setObjectName(QString::fromUtf8("speedMainLayout"));
        speedInnerGroupBox = new QGroupBox(speedScrollAreaWidgetContents);
        speedInnerGroupBox->setObjectName(QString::fromUtf8("speedInnerGroupBox"));
        speedInnerGridLayout = new QGridLayout(speedInnerGroupBox);
        speedInnerGridLayout->setObjectName(QString::fromUtf8("speedInnerGridLayout"));
        speedInnerMinLabel = new QLabel(speedInnerGroupBox);
        speedInnerMinLabel->setObjectName(QString::fromUtf8("speedInnerMinLabel"));

        speedInnerGridLayout->addWidget(speedInnerMinLabel, 0, 0, 1, 1);

        speedInnerMinSpinBox = new QDoubleSpinBox(speedInnerGroupBox);
        speedInnerMinSpinBox->setObjectName(QString::fromUtf8("speedInnerMinSpinBox"));
        speedInnerMinSpinBox->setDecimals(3);
        speedInnerMinSpinBox->setMinimum(-9999.000000000000000);
        speedInnerMinSpinBox->setMaximum(9999.000000000000000);
        speedInnerMinSpinBox->setSingleStep(0.100000000000000);
        speedInnerMinSpinBox->setValue(0.000000000000000);

        speedInnerGridLayout->addWidget(speedInnerMinSpinBox, 0, 1, 1, 1);

        speedInnerMaxLabel = new QLabel(speedInnerGroupBox);
        speedInnerMaxLabel->setObjectName(QString::fromUtf8("speedInnerMaxLabel"));

        speedInnerGridLayout->addWidget(speedInnerMaxLabel, 1, 0, 1, 1);

        speedInnerMaxSpinBox = new QDoubleSpinBox(speedInnerGroupBox);
        speedInnerMaxSpinBox->setObjectName(QString::fromUtf8("speedInnerMaxSpinBox"));
        speedInnerMaxSpinBox->setDecimals(3);
        speedInnerMaxSpinBox->setMinimum(-9999.000000000000000);
        speedInnerMaxSpinBox->setMaximum(9999.000000000000000);
        speedInnerMaxSpinBox->setSingleStep(0.100000000000000);
        speedInnerMaxSpinBox->setValue(0.000000000000000);

        speedInnerGridLayout->addWidget(speedInnerMaxSpinBox, 1, 1, 1, 1);

        speedInnerUnitLabel = new QLabel(speedInnerGroupBox);
        speedInnerUnitLabel->setObjectName(QString::fromUtf8("speedInnerUnitLabel"));

        speedInnerGridLayout->addWidget(speedInnerUnitLabel, 2, 0, 1, 1);

        speedInnerUnitCombo = new QComboBox(speedInnerGroupBox);
        speedInnerUnitCombo->addItem(QString());
        speedInnerUnitCombo->addItem(QString());
        speedInnerUnitCombo->setObjectName(QString::fromUtf8("speedInnerUnitCombo"));

        speedInnerGridLayout->addWidget(speedInnerUnitCombo, 2, 1, 1, 1);

        speedInnerDurationLabel = new QLabel(speedInnerGroupBox);
        speedInnerDurationLabel->setObjectName(QString::fromUtf8("speedInnerDurationLabel"));

        speedInnerGridLayout->addWidget(speedInnerDurationLabel, 3, 0, 1, 1);

        speedInnerDurationSpinBox = new QDoubleSpinBox(speedInnerGroupBox);
        speedInnerDurationSpinBox->setObjectName(QString::fromUtf8("speedInnerDurationSpinBox"));
        speedInnerDurationSpinBox->setDecimals(3);
        speedInnerDurationSpinBox->setMinimum(0.000000000000000);
        speedInnerDurationSpinBox->setMaximum(9999.000000000000000);
        speedInnerDurationSpinBox->setSingleStep(0.100000000000000);
        speedInnerDurationSpinBox->setValue(0.000000000000000);

        speedInnerGridLayout->addWidget(speedInnerDurationSpinBox, 3, 1, 1, 1);

        speedInnerEnabledLabel = new QLabel(speedInnerGroupBox);
        speedInnerEnabledLabel->setObjectName(QString::fromUtf8("speedInnerEnabledLabel"));

        speedInnerGridLayout->addWidget(speedInnerEnabledLabel, 4, 0, 1, 1);

        speedInnerEnabledCheckBox = new QCheckBox(speedInnerGroupBox);
        speedInnerEnabledCheckBox->setObjectName(QString::fromUtf8("speedInnerEnabledCheckBox"));
        speedInnerEnabledCheckBox->setChecked(false);

        speedInnerGridLayout->addWidget(speedInnerEnabledCheckBox, 4, 1, 1, 1);

        speedInnerTypeLabel = new QLabel(speedInnerGroupBox);
        speedInnerTypeLabel->setObjectName(QString::fromUtf8("speedInnerTypeLabel"));

        speedInnerGridLayout->addWidget(speedInnerTypeLabel, 5, 0, 1, 1);

        speedInnerTypeCombo = new QComboBox(speedInnerGroupBox);
        speedInnerTypeCombo->addItem(QString());
        speedInnerTypeCombo->addItem(QString());
        speedInnerTypeCombo->addItem(QString());
        speedInnerTypeCombo->setObjectName(QString::fromUtf8("speedInnerTypeCombo"));

        speedInnerGridLayout->addWidget(speedInnerTypeCombo, 5, 1, 1, 1);


        speedMainLayout->addWidget(speedInnerGroupBox);

        speedOuterGroupBox = new QGroupBox(speedScrollAreaWidgetContents);
        speedOuterGroupBox->setObjectName(QString::fromUtf8("speedOuterGroupBox"));
        speedOuterGridLayout = new QGridLayout(speedOuterGroupBox);
        speedOuterGridLayout->setObjectName(QString::fromUtf8("speedOuterGridLayout"));
        speedOuterMinLabel = new QLabel(speedOuterGroupBox);
        speedOuterMinLabel->setObjectName(QString::fromUtf8("speedOuterMinLabel"));

        speedOuterGridLayout->addWidget(speedOuterMinLabel, 0, 0, 1, 1);

        speedOuterMinSpinBox = new QDoubleSpinBox(speedOuterGroupBox);
        speedOuterMinSpinBox->setObjectName(QString::fromUtf8("speedOuterMinSpinBox"));
        speedOuterMinSpinBox->setDecimals(3);
        speedOuterMinSpinBox->setMinimum(-9999.000000000000000);
        speedOuterMinSpinBox->setMaximum(9999.000000000000000);
        speedOuterMinSpinBox->setSingleStep(0.100000000000000);
        speedOuterMinSpinBox->setValue(0.000000000000000);

        speedOuterGridLayout->addWidget(speedOuterMinSpinBox, 0, 1, 1, 1);

        speedOuterMaxLabel = new QLabel(speedOuterGroupBox);
        speedOuterMaxLabel->setObjectName(QString::fromUtf8("speedOuterMaxLabel"));

        speedOuterGridLayout->addWidget(speedOuterMaxLabel, 1, 0, 1, 1);

        speedOuterMaxSpinBox = new QDoubleSpinBox(speedOuterGroupBox);
        speedOuterMaxSpinBox->setObjectName(QString::fromUtf8("speedOuterMaxSpinBox"));
        speedOuterMaxSpinBox->setDecimals(3);
        speedOuterMaxSpinBox->setMinimum(-9999.000000000000000);
        speedOuterMaxSpinBox->setMaximum(9999.000000000000000);
        speedOuterMaxSpinBox->setSingleStep(0.100000000000000);
        speedOuterMaxSpinBox->setValue(0.000000000000000);

        speedOuterGridLayout->addWidget(speedOuterMaxSpinBox, 1, 1, 1, 1);

        speedOuterUnitLabel = new QLabel(speedOuterGroupBox);
        speedOuterUnitLabel->setObjectName(QString::fromUtf8("speedOuterUnitLabel"));

        speedOuterGridLayout->addWidget(speedOuterUnitLabel, 2, 0, 1, 1);

        speedOuterUnitCombo = new QComboBox(speedOuterGroupBox);
        speedOuterUnitCombo->addItem(QString());
        speedOuterUnitCombo->addItem(QString());
        speedOuterUnitCombo->setObjectName(QString::fromUtf8("speedOuterUnitCombo"));

        speedOuterGridLayout->addWidget(speedOuterUnitCombo, 2, 1, 1, 1);

        speedOuterDurationLabel = new QLabel(speedOuterGroupBox);
        speedOuterDurationLabel->setObjectName(QString::fromUtf8("speedOuterDurationLabel"));

        speedOuterGridLayout->addWidget(speedOuterDurationLabel, 3, 0, 1, 1);

        speedOuterDurationSpinBox = new QDoubleSpinBox(speedOuterGroupBox);
        speedOuterDurationSpinBox->setObjectName(QString::fromUtf8("speedOuterDurationSpinBox"));
        speedOuterDurationSpinBox->setDecimals(3);
        speedOuterDurationSpinBox->setMinimum(0.000000000000000);
        speedOuterDurationSpinBox->setMaximum(9999.000000000000000);
        speedOuterDurationSpinBox->setSingleStep(0.100000000000000);
        speedOuterDurationSpinBox->setValue(0.000000000000000);

        speedOuterGridLayout->addWidget(speedOuterDurationSpinBox, 3, 1, 1, 1);

        speedOuterEnabledLabel = new QLabel(speedOuterGroupBox);
        speedOuterEnabledLabel->setObjectName(QString::fromUtf8("speedOuterEnabledLabel"));

        speedOuterGridLayout->addWidget(speedOuterEnabledLabel, 4, 0, 1, 1);

        speedOuterEnabledCheckBox = new QCheckBox(speedOuterGroupBox);
        speedOuterEnabledCheckBox->setObjectName(QString::fromUtf8("speedOuterEnabledCheckBox"));
        speedOuterEnabledCheckBox->setChecked(false);

        speedOuterGridLayout->addWidget(speedOuterEnabledCheckBox, 4, 1, 1, 1);

        speedOuterTypeLabel = new QLabel(speedOuterGroupBox);
        speedOuterTypeLabel->setObjectName(QString::fromUtf8("speedOuterTypeLabel"));

        speedOuterGridLayout->addWidget(speedOuterTypeLabel, 5, 0, 1, 1);

        speedOuterTypeCombo = new QComboBox(speedOuterGroupBox);
        speedOuterTypeCombo->addItem(QString());
        speedOuterTypeCombo->addItem(QString());
        speedOuterTypeCombo->addItem(QString());
        speedOuterTypeCombo->setObjectName(QString::fromUtf8("speedOuterTypeCombo"));

        speedOuterGridLayout->addWidget(speedOuterTypeCombo, 5, 1, 1, 1);


        speedMainLayout->addWidget(speedOuterGroupBox);

        speedMainGroupBox = new QGroupBox(speedScrollAreaWidgetContents);
        speedMainGroupBox->setObjectName(QString::fromUtf8("speedMainGroupBox"));
        speedMainGridLayout = new QGridLayout(speedMainGroupBox);
        speedMainGridLayout->setObjectName(QString::fromUtf8("speedMainGridLayout"));
        speedMainMinLabel = new QLabel(speedMainGroupBox);
        speedMainMinLabel->setObjectName(QString::fromUtf8("speedMainMinLabel"));

        speedMainGridLayout->addWidget(speedMainMinLabel, 0, 0, 1, 1);

        speedMainMinSpinBox = new QDoubleSpinBox(speedMainGroupBox);
        speedMainMinSpinBox->setObjectName(QString::fromUtf8("speedMainMinSpinBox"));
        speedMainMinSpinBox->setDecimals(3);
        speedMainMinSpinBox->setMinimum(-9999.000000000000000);
        speedMainMinSpinBox->setMaximum(9999.000000000000000);
        speedMainMinSpinBox->setSingleStep(0.100000000000000);
        speedMainMinSpinBox->setValue(-200.000000000000000);

        speedMainGridLayout->addWidget(speedMainMinSpinBox, 0, 1, 1, 1);

        speedMainMaxLabel = new QLabel(speedMainGroupBox);
        speedMainMaxLabel->setObjectName(QString::fromUtf8("speedMainMaxLabel"));

        speedMainGridLayout->addWidget(speedMainMaxLabel, 1, 0, 1, 1);

        speedMainMaxSpinBox = new QDoubleSpinBox(speedMainGroupBox);
        speedMainMaxSpinBox->setObjectName(QString::fromUtf8("speedMainMaxSpinBox"));
        speedMainMaxSpinBox->setDecimals(3);
        speedMainMaxSpinBox->setMinimum(-9999.000000000000000);
        speedMainMaxSpinBox->setMaximum(9999.000000000000000);
        speedMainMaxSpinBox->setSingleStep(0.100000000000000);
        speedMainMaxSpinBox->setValue(200.000000000000000);

        speedMainGridLayout->addWidget(speedMainMaxSpinBox, 1, 1, 1, 1);

        speedMainUnitLabel = new QLabel(speedMainGroupBox);
        speedMainUnitLabel->setObjectName(QString::fromUtf8("speedMainUnitLabel"));

        speedMainGridLayout->addWidget(speedMainUnitLabel, 2, 0, 1, 1);

        speedMainUnitCombo = new QComboBox(speedMainGroupBox);
        speedMainUnitCombo->addItem(QString());
        speedMainUnitCombo->addItem(QString());
        speedMainUnitCombo->setObjectName(QString::fromUtf8("speedMainUnitCombo"));

        speedMainGridLayout->addWidget(speedMainUnitCombo, 2, 1, 1, 1);

        speedMainDurationLabel = new QLabel(speedMainGroupBox);
        speedMainDurationLabel->setObjectName(QString::fromUtf8("speedMainDurationLabel"));

        speedMainGridLayout->addWidget(speedMainDurationLabel, 3, 0, 1, 1);

        speedMainDurationSpinBox = new QDoubleSpinBox(speedMainGroupBox);
        speedMainDurationSpinBox->setObjectName(QString::fromUtf8("speedMainDurationSpinBox"));
        speedMainDurationSpinBox->setDecimals(3);
        speedMainDurationSpinBox->setMinimum(0.000000000000000);
        speedMainDurationSpinBox->setMaximum(9999.000000000000000);
        speedMainDurationSpinBox->setSingleStep(0.100000000000000);
        speedMainDurationSpinBox->setValue(1.000000000000000);

        speedMainGridLayout->addWidget(speedMainDurationSpinBox, 3, 1, 1, 1);

        speedMainEnabledLabel = new QLabel(speedMainGroupBox);
        speedMainEnabledLabel->setObjectName(QString::fromUtf8("speedMainEnabledLabel"));

        speedMainGridLayout->addWidget(speedMainEnabledLabel, 4, 0, 1, 1);

        speedMainEnabledCheckBox = new QCheckBox(speedMainGroupBox);
        speedMainEnabledCheckBox->setObjectName(QString::fromUtf8("speedMainEnabledCheckBox"));
        speedMainEnabledCheckBox->setChecked(true);

        speedMainGridLayout->addWidget(speedMainEnabledCheckBox, 4, 1, 1, 1);

        speedMainTypeLabel = new QLabel(speedMainGroupBox);
        speedMainTypeLabel->setObjectName(QString::fromUtf8("speedMainTypeLabel"));

        speedMainGridLayout->addWidget(speedMainTypeLabel, 5, 0, 1, 1);

        speedMainTypeCombo = new QComboBox(speedMainGroupBox);
        speedMainTypeCombo->addItem(QString());
        speedMainTypeCombo->addItem(QString());
        speedMainTypeCombo->addItem(QString());
        speedMainTypeCombo->setObjectName(QString::fromUtf8("speedMainTypeCombo"));

        speedMainGridLayout->addWidget(speedMainTypeCombo, 5, 1, 1, 1);


        speedMainLayout->addWidget(speedMainGroupBox);

        speedScrollArea->setWidget(speedScrollAreaWidgetContents);

        speedLimitsLayout->addWidget(speedScrollArea);

        tabWidget_2->addTab(speedLimitsTab, QString());

        thresholdsLayout->addWidget(tabWidget_2);

        tabWidget->addTab(thresholdsTab, QString());
        filterTab = new QWidget();
        filterTab->setObjectName(QString::fromUtf8("filterTab"));
        filterLayout = new QVBoxLayout(filterTab);
        filterLayout->setObjectName(QString::fromUtf8("filterLayout"));
        filterScrollArea = new QScrollArea(filterTab);
        filterScrollArea->setObjectName(QString::fromUtf8("filterScrollArea"));
        filterScrollArea->setWidgetResizable(true);
        filterScrollAreaWidgetContents = new QWidget();
        filterScrollAreaWidgetContents->setObjectName(QString::fromUtf8("filterScrollAreaWidgetContents"));
        filterScrollAreaWidgetContents->setGeometry(QRect(0, 0, 972, 398));
        gridLayout_2 = new QGridLayout(filterScrollAreaWidgetContents);
        gridLayout_2->setObjectName(QString::fromUtf8("gridLayout_2"));
        forceFilterLabel = new QLabel(filterScrollAreaWidgetContents);
        forceFilterLabel->setObjectName(QString::fromUtf8("forceFilterLabel"));
        forceFilterLabel->setMinimumSize(QSize(0, 0));
        forceFilterLabel->setMaximumSize(QSize(200, 16777215));

        gridLayout_2->addWidget(forceFilterLabel, 0, 0, 1, 1);

        forceLowpassLabel = new QLabel(filterScrollAreaWidgetContents);
        forceLowpassLabel->setObjectName(QString::fromUtf8("forceLowpassLabel"));
        forceLowpassLabel->setMinimumSize(QSize(0, 0));
        forceLowpassLabel->setMaximumSize(QSize(200, 16777215));

        gridLayout_2->addWidget(forceLowpassLabel, 1, 0, 1, 1);

        payloadFcSpinBox = new QDoubleSpinBox(filterScrollAreaWidgetContents);
        payloadFcSpinBox->setObjectName(QString::fromUtf8("payloadFcSpinBox"));
        payloadFcSpinBox->setMinimum(0.100000000000000);
        payloadFcSpinBox->setMaximum(10000.000000000000000);
        payloadFcSpinBox->setValue(100.000000000000000);

        gridLayout_2->addWidget(payloadFcSpinBox, 1, 1, 1, 1);

        forceHighpassLabel = new QLabel(filterScrollAreaWidgetContents);
        forceHighpassLabel->setObjectName(QString::fromUtf8("forceHighpassLabel"));
        forceHighpassLabel->setMinimumSize(QSize(0, 0));
        forceHighpassLabel->setMaximumSize(QSize(200, 16777215));

        gridLayout_2->addWidget(forceHighpassLabel, 2, 0, 1, 1);

        validComboBox = new QComboBox(filterScrollAreaWidgetContents);
        validComboBox->addItem(QString());
        validComboBox->addItem(QString());
        validComboBox->setObjectName(QString::fromUtf8("validComboBox"));

        gridLayout_2->addWidget(validComboBox, 2, 1, 1, 1);

        positionFilterLabel = new QLabel(filterScrollAreaWidgetContents);
        positionFilterLabel->setObjectName(QString::fromUtf8("positionFilterLabel"));
        positionFilterLabel->setMinimumSize(QSize(0, 0));
        positionFilterLabel->setMaximumSize(QSize(200, 16777215));

        gridLayout_2->addWidget(positionFilterLabel, 3, 0, 1, 2);

        positionLowpassLabel = new QLabel(filterScrollAreaWidgetContents);
        positionLowpassLabel->setObjectName(QString::fromUtf8("positionLowpassLabel"));
        positionLowpassLabel->setMinimumSize(QSize(0, 0));
        positionLowpassLabel->setMaximumSize(QSize(200, 16777215));

        gridLayout_2->addWidget(positionLowpassLabel, 4, 0, 1, 1);

        positionFcSpinBox = new QDoubleSpinBox(filterScrollAreaWidgetContents);
        positionFcSpinBox->setObjectName(QString::fromUtf8("positionFcSpinBox"));
        positionFcSpinBox->setMinimum(0.100000000000000);
        positionFcSpinBox->setMaximum(10000.000000000000000);
        positionFcSpinBox->setValue(50.000000000000000);

        gridLayout_2->addWidget(positionFcSpinBox, 4, 1, 1, 1);

        positionHighpassLabel = new QLabel(filterScrollAreaWidgetContents);
        positionHighpassLabel->setObjectName(QString::fromUtf8("positionHighpassLabel"));
        positionHighpassLabel->setMinimumSize(QSize(0, 0));
        positionHighpassLabel->setMaximumSize(QSize(200, 16777215));

        gridLayout_2->addWidget(positionHighpassLabel, 5, 0, 1, 1);

        speedFcSpinBox = new QDoubleSpinBox(filterScrollAreaWidgetContents);
        speedFcSpinBox->setObjectName(QString::fromUtf8("speedFcSpinBox"));
        speedFcSpinBox->setMinimum(0.010000000000000);
        speedFcSpinBox->setMaximum(1000.000000000000000);
        speedFcSpinBox->setValue(20.000000000000000);

        gridLayout_2->addWidget(speedFcSpinBox, 5, 1, 1, 1);

        velocityFilterLabel = new QLabel(filterScrollAreaWidgetContents);
        velocityFilterLabel->setObjectName(QString::fromUtf8("velocityFilterLabel"));
        velocityFilterLabel->setMinimumSize(QSize(0, 0));
        velocityFilterLabel->setMaximumSize(QSize(200, 16777215));

        gridLayout_2->addWidget(velocityFilterLabel, 6, 0, 1, 1);

        velocityLowpassLabel = new QLabel(filterScrollAreaWidgetContents);
        velocityLowpassLabel->setObjectName(QString::fromUtf8("velocityLowpassLabel"));
        velocityLowpassLabel->setMinimumSize(QSize(0, 0));
        velocityLowpassLabel->setMaximumSize(QSize(200, 16777215));

        gridLayout_2->addWidget(velocityLowpassLabel, 7, 0, 1, 1);

        accFcSpinBox = new QDoubleSpinBox(filterScrollAreaWidgetContents);
        accFcSpinBox->setObjectName(QString::fromUtf8("accFcSpinBox"));
        accFcSpinBox->setMinimum(0.100000000000000);
        accFcSpinBox->setMaximum(10000.000000000000000);
        accFcSpinBox->setValue(30.000000000000000);

        gridLayout_2->addWidget(accFcSpinBox, 7, 1, 1, 1);

        verticalSpacer = new QSpacerItem(0, 0, QSizePolicy::Minimum, QSizePolicy::Expanding);

        gridLayout_2->addItem(verticalSpacer, 8, 1, 1, 1);

        filterScrollArea->setWidget(filterScrollAreaWidgetContents);

        filterLayout->addWidget(filterScrollArea);

        tabWidget->addTab(filterTab, QString());
        pidTab = new QWidget();
        pidTab->setObjectName(QString::fromUtf8("pidTab"));
        pidLayout = new QVBoxLayout(pidTab);
        pidLayout->setObjectName(QString::fromUtf8("pidLayout"));
        tabWidget_3 = new QTabWidget(pidTab);
        tabWidget_3->setObjectName(QString::fromUtf8("tabWidget_3"));
        tab = new QWidget();
        tab->setObjectName(QString::fromUtf8("tab"));
        positionControlPidLayout = new QVBoxLayout(tab);
        positionControlPidLayout->setObjectName(QString::fromUtf8("positionControlPidLayout"));
        positionControlScrollArea = new QScrollArea(tab);
        positionControlScrollArea->setObjectName(QString::fromUtf8("positionControlScrollArea"));
        positionControlScrollArea->setWidgetResizable(true);
        positionControlScrollAreaWidgetContents = new QWidget();
        positionControlScrollAreaWidgetContents->setObjectName(QString::fromUtf8("positionControlScrollAreaWidgetContents"));
        positionControlScrollAreaWidgetContents->setGeometry(QRect(0, -6, 927, 364));
        positionControlMainLayout = new QVBoxLayout(positionControlScrollAreaWidgetContents);
        positionControlMainLayout->setObjectName(QString::fromUtf8("positionControlMainLayout"));
        positionBasicPidGroup = new QGroupBox(positionControlScrollAreaWidgetContents);
        positionBasicPidGroup->setObjectName(QString::fromUtf8("positionBasicPidGroup"));
        positionBasicPidGridLayout = new QGridLayout(positionBasicPidGroup);
        positionBasicPidGridLayout->setObjectName(QString::fromUtf8("positionBasicPidGridLayout"));
        positionKpLabel = new QLabel(positionBasicPidGroup);
        positionKpLabel->setObjectName(QString::fromUtf8("positionKpLabel"));

        positionBasicPidGridLayout->addWidget(positionKpLabel, 0, 0, 1, 1);

        positionKpSpinBox = new QDoubleSpinBox(positionBasicPidGroup);
        positionKpSpinBox->setObjectName(QString::fromUtf8("positionKpSpinBox"));
        positionKpSpinBox->setDecimals(3);
        positionKpSpinBox->setMinimum(0.000000000000000);
        positionKpSpinBox->setMaximum(1000.000000000000000);
        positionKpSpinBox->setSingleStep(0.100000000000000);
        positionKpSpinBox->setValue(9.500000000000000);

        positionBasicPidGridLayout->addWidget(positionKpSpinBox, 0, 1, 1, 1);

        positionKiLabel = new QLabel(positionBasicPidGroup);
        positionKiLabel->setObjectName(QString::fromUtf8("positionKiLabel"));

        positionBasicPidGridLayout->addWidget(positionKiLabel, 1, 0, 1, 1);

        positionKiSpinBox = new QDoubleSpinBox(positionBasicPidGroup);
        positionKiSpinBox->setObjectName(QString::fromUtf8("positionKiSpinBox"));
        positionKiSpinBox->setDecimals(3);
        positionKiSpinBox->setMinimum(0.000000000000000);
        positionKiSpinBox->setMaximum(100.000000000000000);
        positionKiSpinBox->setSingleStep(0.100000000000000);
        positionKiSpinBox->setValue(20.000000000000000);

        positionBasicPidGridLayout->addWidget(positionKiSpinBox, 1, 1, 1, 1);

        positionKdLabel = new QLabel(positionBasicPidGroup);
        positionKdLabel->setObjectName(QString::fromUtf8("positionKdLabel"));

        positionBasicPidGridLayout->addWidget(positionKdLabel, 2, 0, 1, 1);

        positionKdSpinBox = new QDoubleSpinBox(positionBasicPidGroup);
        positionKdSpinBox->setObjectName(QString::fromUtf8("positionKdSpinBox"));
        positionKdSpinBox->setDecimals(3);
        positionKdSpinBox->setMinimum(0.000000000000000);
        positionKdSpinBox->setMaximum(10.000000000000000);
        positionKdSpinBox->setSingleStep(0.100000000000000);
        positionKdSpinBox->setValue(0.000000000000000);

        positionBasicPidGridLayout->addWidget(positionKdSpinBox, 2, 1, 1, 1);

        positionKsLabel = new QLabel(positionBasicPidGroup);
        positionKsLabel->setObjectName(QString::fromUtf8("positionKsLabel"));

        positionBasicPidGridLayout->addWidget(positionKsLabel, 3, 0, 1, 1);

        positionKsSpinBox = new QDoubleSpinBox(positionBasicPidGroup);
        positionKsSpinBox->setObjectName(QString::fromUtf8("positionKsSpinBox"));
        positionKsSpinBox->setDecimals(3);
        positionKsSpinBox->setMinimum(0.000000000000000);
        positionKsSpinBox->setMaximum(10.000000000000000);
        positionKsSpinBox->setSingleStep(0.100000000000000);
        positionKsSpinBox->setValue(0.000000000000000);

        positionBasicPidGridLayout->addWidget(positionKsSpinBox, 3, 1, 1, 1);

        positionKaLabel = new QLabel(positionBasicPidGroup);
        positionKaLabel->setObjectName(QString::fromUtf8("positionKaLabel"));

        positionBasicPidGridLayout->addWidget(positionKaLabel, 4, 0, 1, 1);

        positionKaSpinBox = new QDoubleSpinBox(positionBasicPidGroup);
        positionKaSpinBox->setObjectName(QString::fromUtf8("positionKaSpinBox"));
        positionKaSpinBox->setDecimals(3);
        positionKaSpinBox->setMinimum(0.000000000000000);
        positionKaSpinBox->setMaximum(10.000000000000000);
        positionKaSpinBox->setSingleStep(0.100000000000000);
        positionKaSpinBox->setValue(0.000000000000000);

        positionBasicPidGridLayout->addWidget(positionKaSpinBox, 4, 1, 1, 1);


        positionControlMainLayout->addWidget(positionBasicPidGroup);

        positionStepPidGroup = new QGroupBox(positionControlScrollAreaWidgetContents);
        positionStepPidGroup->setObjectName(QString::fromUtf8("positionStepPidGroup"));
        positionStepPidGridLayout = new QGridLayout(positionStepPidGroup);
        positionStepPidGridLayout->setObjectName(QString::fromUtf8("positionStepPidGridLayout"));
        positionKpStepLabel = new QLabel(positionStepPidGroup);
        positionKpStepLabel->setObjectName(QString::fromUtf8("positionKpStepLabel"));

        positionStepPidGridLayout->addWidget(positionKpStepLabel, 0, 0, 1, 1);

        positionKpStepSpinBox = new QDoubleSpinBox(positionStepPidGroup);
        positionKpStepSpinBox->setObjectName(QString::fromUtf8("positionKpStepSpinBox"));
        positionKpStepSpinBox->setDecimals(3);
        positionKpStepSpinBox->setMinimum(0.001000000000000);
        positionKpStepSpinBox->setMaximum(10.000000000000000);
        positionKpStepSpinBox->setSingleStep(0.001000000000000);
        positionKpStepSpinBox->setValue(0.100000000000000);

        positionStepPidGridLayout->addWidget(positionKpStepSpinBox, 0, 1, 1, 1);

        positionKiStepLabel = new QLabel(positionStepPidGroup);
        positionKiStepLabel->setObjectName(QString::fromUtf8("positionKiStepLabel"));

        positionStepPidGridLayout->addWidget(positionKiStepLabel, 1, 0, 1, 1);

        positionKiStepSpinBox = new QDoubleSpinBox(positionStepPidGroup);
        positionKiStepSpinBox->setObjectName(QString::fromUtf8("positionKiStepSpinBox"));
        positionKiStepSpinBox->setDecimals(3);
        positionKiStepSpinBox->setMinimum(0.001000000000000);
        positionKiStepSpinBox->setMaximum(10.000000000000000);
        positionKiStepSpinBox->setSingleStep(0.001000000000000);
        positionKiStepSpinBox->setValue(0.100000000000000);

        positionStepPidGridLayout->addWidget(positionKiStepSpinBox, 1, 1, 1, 1);

        positionKdStepLabel = new QLabel(positionStepPidGroup);
        positionKdStepLabel->setObjectName(QString::fromUtf8("positionKdStepLabel"));

        positionStepPidGridLayout->addWidget(positionKdStepLabel, 2, 0, 1, 1);

        positionKdStepSpinBox = new QDoubleSpinBox(positionStepPidGroup);
        positionKdStepSpinBox->setObjectName(QString::fromUtf8("positionKdStepSpinBox"));
        positionKdStepSpinBox->setDecimals(3);
        positionKdStepSpinBox->setMinimum(0.001000000000000);
        positionKdStepSpinBox->setMaximum(10.000000000000000);
        positionKdStepSpinBox->setSingleStep(0.001000000000000);
        positionKdStepSpinBox->setValue(0.100000000000000);

        positionStepPidGridLayout->addWidget(positionKdStepSpinBox, 2, 1, 1, 1);

        positionKsStepLabel = new QLabel(positionStepPidGroup);
        positionKsStepLabel->setObjectName(QString::fromUtf8("positionKsStepLabel"));

        positionStepPidGridLayout->addWidget(positionKsStepLabel, 3, 0, 1, 1);

        positionKsStepSpinBox = new QDoubleSpinBox(positionStepPidGroup);
        positionKsStepSpinBox->setObjectName(QString::fromUtf8("positionKsStepSpinBox"));
        positionKsStepSpinBox->setDecimals(3);
        positionKsStepSpinBox->setMinimum(0.001000000000000);
        positionKsStepSpinBox->setMaximum(10.000000000000000);
        positionKsStepSpinBox->setSingleStep(0.001000000000000);
        positionKsStepSpinBox->setValue(0.100000000000000);

        positionStepPidGridLayout->addWidget(positionKsStepSpinBox, 3, 1, 1, 1);

        positionKaStepLabel = new QLabel(positionStepPidGroup);
        positionKaStepLabel->setObjectName(QString::fromUtf8("positionKaStepLabel"));

        positionStepPidGridLayout->addWidget(positionKaStepLabel, 4, 0, 1, 1);

        positionKaStepSpinBox = new QDoubleSpinBox(positionStepPidGroup);
        positionKaStepSpinBox->setObjectName(QString::fromUtf8("positionKaStepSpinBox"));
        positionKaStepSpinBox->setDecimals(3);
        positionKaStepSpinBox->setMinimum(0.001000000000000);
        positionKaStepSpinBox->setMaximum(10.000000000000000);
        positionKaStepSpinBox->setSingleStep(0.001000000000000);
        positionKaStepSpinBox->setValue(0.100000000000000);

        positionStepPidGridLayout->addWidget(positionKaStepSpinBox, 4, 1, 1, 1);


        positionControlMainLayout->addWidget(positionStepPidGroup);

        verticalSpacer_6 = new QSpacerItem(0, 0, QSizePolicy::Minimum, QSizePolicy::Expanding);

        positionControlMainLayout->addItem(verticalSpacer_6);

        positionVerticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        positionControlMainLayout->addItem(positionVerticalSpacer);

        positionControlScrollArea->setWidget(positionControlScrollAreaWidgetContents);

        positionControlPidLayout->addWidget(positionControlScrollArea);

        tabWidget_3->addTab(tab, QString());
        tab_2 = new QWidget();
        tab_2->setObjectName(QString::fromUtf8("tab_2"));
        staticLoadPidLayout = new QVBoxLayout(tab_2);
        staticLoadPidLayout->setObjectName(QString::fromUtf8("staticLoadPidLayout"));
        staticLoadScrollArea = new QScrollArea(tab_2);
        staticLoadScrollArea->setObjectName(QString::fromUtf8("staticLoadScrollArea"));
        staticLoadScrollArea->setWidgetResizable(true);
        staticLoadScrollAreaWidgetContents = new QWidget();
        staticLoadScrollAreaWidgetContents->setObjectName(QString::fromUtf8("staticLoadScrollAreaWidgetContents"));
        staticLoadScrollAreaWidgetContents->setGeometry(QRect(0, 0, 188, 358));
        staticLoadMainLayout = new QVBoxLayout(staticLoadScrollAreaWidgetContents);
        staticLoadMainLayout->setObjectName(QString::fromUtf8("staticLoadMainLayout"));
        staticLoadBasicPidGroup = new QGroupBox(staticLoadScrollAreaWidgetContents);
        staticLoadBasicPidGroup->setObjectName(QString::fromUtf8("staticLoadBasicPidGroup"));
        staticLoadBasicPidGridLayout = new QGridLayout(staticLoadBasicPidGroup);
        staticLoadBasicPidGridLayout->setObjectName(QString::fromUtf8("staticLoadBasicPidGridLayout"));
        staticLoadKpLabel = new QLabel(staticLoadBasicPidGroup);
        staticLoadKpLabel->setObjectName(QString::fromUtf8("staticLoadKpLabel"));

        staticLoadBasicPidGridLayout->addWidget(staticLoadKpLabel, 0, 0, 1, 1);

        staticLoadKpSpinBox = new QDoubleSpinBox(staticLoadBasicPidGroup);
        staticLoadKpSpinBox->setObjectName(QString::fromUtf8("staticLoadKpSpinBox"));
        staticLoadKpSpinBox->setDecimals(3);
        staticLoadKpSpinBox->setMinimum(0.000000000000000);
        staticLoadKpSpinBox->setMaximum(1000.000000000000000);
        staticLoadKpSpinBox->setSingleStep(0.100000000000000);
        staticLoadKpSpinBox->setValue(5.000000000000000);

        staticLoadBasicPidGridLayout->addWidget(staticLoadKpSpinBox, 0, 1, 1, 1);

        staticLoadKiLabel = new QLabel(staticLoadBasicPidGroup);
        staticLoadKiLabel->setObjectName(QString::fromUtf8("staticLoadKiLabel"));

        staticLoadBasicPidGridLayout->addWidget(staticLoadKiLabel, 1, 0, 1, 1);

        staticLoadKiSpinBox = new QDoubleSpinBox(staticLoadBasicPidGroup);
        staticLoadKiSpinBox->setObjectName(QString::fromUtf8("staticLoadKiSpinBox"));
        staticLoadKiSpinBox->setDecimals(3);
        staticLoadKiSpinBox->setMinimum(0.000000000000000);
        staticLoadKiSpinBox->setMaximum(100.000000000000000);
        staticLoadKiSpinBox->setSingleStep(0.100000000000000);
        staticLoadKiSpinBox->setValue(0.000000000000000);

        staticLoadBasicPidGridLayout->addWidget(staticLoadKiSpinBox, 1, 1, 1, 1);

        staticLoadKdLabel = new QLabel(staticLoadBasicPidGroup);
        staticLoadKdLabel->setObjectName(QString::fromUtf8("staticLoadKdLabel"));

        staticLoadBasicPidGridLayout->addWidget(staticLoadKdLabel, 2, 0, 1, 1);

        staticLoadKdSpinBox = new QDoubleSpinBox(staticLoadBasicPidGroup);
        staticLoadKdSpinBox->setObjectName(QString::fromUtf8("staticLoadKdSpinBox"));
        staticLoadKdSpinBox->setDecimals(3);
        staticLoadKdSpinBox->setMinimum(0.000000000000000);
        staticLoadKdSpinBox->setMaximum(10.000000000000000);
        staticLoadKdSpinBox->setSingleStep(0.100000000000000);
        staticLoadKdSpinBox->setValue(0.000000000000000);

        staticLoadBasicPidGridLayout->addWidget(staticLoadKdSpinBox, 2, 1, 1, 1);

        staticLoadKsLabel = new QLabel(staticLoadBasicPidGroup);
        staticLoadKsLabel->setObjectName(QString::fromUtf8("staticLoadKsLabel"));

        staticLoadBasicPidGridLayout->addWidget(staticLoadKsLabel, 3, 0, 1, 1);

        staticLoadKsSpinBox = new QDoubleSpinBox(staticLoadBasicPidGroup);
        staticLoadKsSpinBox->setObjectName(QString::fromUtf8("staticLoadKsSpinBox"));
        staticLoadKsSpinBox->setDecimals(3);
        staticLoadKsSpinBox->setMinimum(0.000000000000000);
        staticLoadKsSpinBox->setMaximum(10.000000000000000);
        staticLoadKsSpinBox->setSingleStep(0.100000000000000);
        staticLoadKsSpinBox->setValue(0.000000000000000);

        staticLoadBasicPidGridLayout->addWidget(staticLoadKsSpinBox, 3, 1, 1, 1);

        staticLoadKaLabel = new QLabel(staticLoadBasicPidGroup);
        staticLoadKaLabel->setObjectName(QString::fromUtf8("staticLoadKaLabel"));

        staticLoadBasicPidGridLayout->addWidget(staticLoadKaLabel, 4, 0, 1, 1);

        staticLoadKaSpinBox = new QDoubleSpinBox(staticLoadBasicPidGroup);
        staticLoadKaSpinBox->setObjectName(QString::fromUtf8("staticLoadKaSpinBox"));
        staticLoadKaSpinBox->setDecimals(3);
        staticLoadKaSpinBox->setMinimum(0.000000000000000);
        staticLoadKaSpinBox->setMaximum(10.000000000000000);
        staticLoadKaSpinBox->setSingleStep(0.100000000000000);
        staticLoadKaSpinBox->setValue(0.000000000000000);

        staticLoadBasicPidGridLayout->addWidget(staticLoadKaSpinBox, 4, 1, 1, 1);


        staticLoadMainLayout->addWidget(staticLoadBasicPidGroup);

        staticLoadStepPidGroup = new QGroupBox(staticLoadScrollAreaWidgetContents);
        staticLoadStepPidGroup->setObjectName(QString::fromUtf8("staticLoadStepPidGroup"));
        staticLoadStepPidGridLayout = new QGridLayout(staticLoadStepPidGroup);
        staticLoadStepPidGridLayout->setObjectName(QString::fromUtf8("staticLoadStepPidGridLayout"));
        staticLoadKpStepLabel = new QLabel(staticLoadStepPidGroup);
        staticLoadKpStepLabel->setObjectName(QString::fromUtf8("staticLoadKpStepLabel"));

        staticLoadStepPidGridLayout->addWidget(staticLoadKpStepLabel, 0, 0, 1, 1);

        staticLoadKpStepSpinBox = new QDoubleSpinBox(staticLoadStepPidGroup);
        staticLoadKpStepSpinBox->setObjectName(QString::fromUtf8("staticLoadKpStepSpinBox"));
        staticLoadKpStepSpinBox->setDecimals(3);
        staticLoadKpStepSpinBox->setMinimum(0.001000000000000);
        staticLoadKpStepSpinBox->setMaximum(10.000000000000000);
        staticLoadKpStepSpinBox->setSingleStep(0.001000000000000);
        staticLoadKpStepSpinBox->setValue(0.100000000000000);

        staticLoadStepPidGridLayout->addWidget(staticLoadKpStepSpinBox, 0, 1, 1, 1);

        staticLoadKiStepLabel = new QLabel(staticLoadStepPidGroup);
        staticLoadKiStepLabel->setObjectName(QString::fromUtf8("staticLoadKiStepLabel"));

        staticLoadStepPidGridLayout->addWidget(staticLoadKiStepLabel, 1, 0, 1, 1);

        staticLoadKiStepSpinBox = new QDoubleSpinBox(staticLoadStepPidGroup);
        staticLoadKiStepSpinBox->setObjectName(QString::fromUtf8("staticLoadKiStepSpinBox"));
        staticLoadKiStepSpinBox->setDecimals(3);
        staticLoadKiStepSpinBox->setMinimum(0.001000000000000);
        staticLoadKiStepSpinBox->setMaximum(10.000000000000000);
        staticLoadKiStepSpinBox->setSingleStep(0.001000000000000);
        staticLoadKiStepSpinBox->setValue(0.100000000000000);

        staticLoadStepPidGridLayout->addWidget(staticLoadKiStepSpinBox, 1, 1, 1, 1);

        staticLoadKdStepLabel = new QLabel(staticLoadStepPidGroup);
        staticLoadKdStepLabel->setObjectName(QString::fromUtf8("staticLoadKdStepLabel"));

        staticLoadStepPidGridLayout->addWidget(staticLoadKdStepLabel, 2, 0, 1, 1);

        staticLoadKdStepSpinBox = new QDoubleSpinBox(staticLoadStepPidGroup);
        staticLoadKdStepSpinBox->setObjectName(QString::fromUtf8("staticLoadKdStepSpinBox"));
        staticLoadKdStepSpinBox->setDecimals(3);
        staticLoadKdStepSpinBox->setMinimum(0.001000000000000);
        staticLoadKdStepSpinBox->setMaximum(10.000000000000000);
        staticLoadKdStepSpinBox->setSingleStep(0.001000000000000);
        staticLoadKdStepSpinBox->setValue(0.100000000000000);

        staticLoadStepPidGridLayout->addWidget(staticLoadKdStepSpinBox, 2, 1, 1, 1);

        staticLoadKsStepLabel = new QLabel(staticLoadStepPidGroup);
        staticLoadKsStepLabel->setObjectName(QString::fromUtf8("staticLoadKsStepLabel"));

        staticLoadStepPidGridLayout->addWidget(staticLoadKsStepLabel, 3, 0, 1, 1);

        staticLoadKsStepSpinBox = new QDoubleSpinBox(staticLoadStepPidGroup);
        staticLoadKsStepSpinBox->setObjectName(QString::fromUtf8("staticLoadKsStepSpinBox"));
        staticLoadKsStepSpinBox->setDecimals(3);
        staticLoadKsStepSpinBox->setMinimum(0.001000000000000);
        staticLoadKsStepSpinBox->setMaximum(10.000000000000000);
        staticLoadKsStepSpinBox->setSingleStep(0.001000000000000);
        staticLoadKsStepSpinBox->setValue(0.100000000000000);

        staticLoadStepPidGridLayout->addWidget(staticLoadKsStepSpinBox, 3, 1, 1, 1);

        staticLoadKaStepLabel = new QLabel(staticLoadStepPidGroup);
        staticLoadKaStepLabel->setObjectName(QString::fromUtf8("staticLoadKaStepLabel"));

        staticLoadStepPidGridLayout->addWidget(staticLoadKaStepLabel, 4, 0, 1, 1);

        staticLoadKaStepSpinBox = new QDoubleSpinBox(staticLoadStepPidGroup);
        staticLoadKaStepSpinBox->setObjectName(QString::fromUtf8("staticLoadKaStepSpinBox"));
        staticLoadKaStepSpinBox->setDecimals(3);
        staticLoadKaStepSpinBox->setMinimum(0.001000000000000);
        staticLoadKaStepSpinBox->setMaximum(10.000000000000000);
        staticLoadKaStepSpinBox->setSingleStep(0.001000000000000);
        staticLoadKaStepSpinBox->setValue(0.100000000000000);

        staticLoadStepPidGridLayout->addWidget(staticLoadKaStepSpinBox, 4, 1, 1, 1);


        staticLoadMainLayout->addWidget(staticLoadStepPidGroup);

        staticLoadVerticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        staticLoadMainLayout->addItem(staticLoadVerticalSpacer);

        staticLoadScrollArea->setWidget(staticLoadScrollAreaWidgetContents);

        staticLoadPidLayout->addWidget(staticLoadScrollArea);

        tabWidget_3->addTab(tab_2, QString());
        tab_3 = new QWidget();
        tab_3->setObjectName(QString::fromUtf8("tab_3"));
        dynamicLoadPidLayout = new QVBoxLayout(tab_3);
        dynamicLoadPidLayout->setObjectName(QString::fromUtf8("dynamicLoadPidLayout"));
        dynamicLoadScrollArea = new QScrollArea(tab_3);
        dynamicLoadScrollArea->setObjectName(QString::fromUtf8("dynamicLoadScrollArea"));
        dynamicLoadScrollArea->setWidgetResizable(true);
        dynamicLoadScrollAreaWidgetContents = new QWidget();
        dynamicLoadScrollAreaWidgetContents->setObjectName(QString::fromUtf8("dynamicLoadScrollAreaWidgetContents"));
        dynamicLoadScrollAreaWidgetContents->setGeometry(QRect(0, 0, 188, 358));
        dynamicLoadMainLayout = new QVBoxLayout(dynamicLoadScrollAreaWidgetContents);
        dynamicLoadMainLayout->setObjectName(QString::fromUtf8("dynamicLoadMainLayout"));
        dynamicLoadBasicPidGroup = new QGroupBox(dynamicLoadScrollAreaWidgetContents);
        dynamicLoadBasicPidGroup->setObjectName(QString::fromUtf8("dynamicLoadBasicPidGroup"));
        dynamicLoadBasicPidGridLayout = new QGridLayout(dynamicLoadBasicPidGroup);
        dynamicLoadBasicPidGridLayout->setObjectName(QString::fromUtf8("dynamicLoadBasicPidGridLayout"));
        dynamicLoadKpLabel = new QLabel(dynamicLoadBasicPidGroup);
        dynamicLoadKpLabel->setObjectName(QString::fromUtf8("dynamicLoadKpLabel"));

        dynamicLoadBasicPidGridLayout->addWidget(dynamicLoadKpLabel, 0, 0, 1, 1);

        dynamicLoadKpSpinBox = new QDoubleSpinBox(dynamicLoadBasicPidGroup);
        dynamicLoadKpSpinBox->setObjectName(QString::fromUtf8("dynamicLoadKpSpinBox"));
        dynamicLoadKpSpinBox->setDecimals(3);
        dynamicLoadKpSpinBox->setMinimum(0.000000000000000);
        dynamicLoadKpSpinBox->setMaximum(1000.000000000000000);
        dynamicLoadKpSpinBox->setSingleStep(0.100000000000000);
        dynamicLoadKpSpinBox->setValue(5.000000000000000);

        dynamicLoadBasicPidGridLayout->addWidget(dynamicLoadKpSpinBox, 0, 1, 1, 1);

        dynamicLoadKiLabel = new QLabel(dynamicLoadBasicPidGroup);
        dynamicLoadKiLabel->setObjectName(QString::fromUtf8("dynamicLoadKiLabel"));

        dynamicLoadBasicPidGridLayout->addWidget(dynamicLoadKiLabel, 1, 0, 1, 1);

        dynamicLoadKiSpinBox = new QDoubleSpinBox(dynamicLoadBasicPidGroup);
        dynamicLoadKiSpinBox->setObjectName(QString::fromUtf8("dynamicLoadKiSpinBox"));
        dynamicLoadKiSpinBox->setDecimals(3);
        dynamicLoadKiSpinBox->setMinimum(0.000000000000000);
        dynamicLoadKiSpinBox->setMaximum(100.000000000000000);
        dynamicLoadKiSpinBox->setSingleStep(0.100000000000000);
        dynamicLoadKiSpinBox->setValue(0.000000000000000);

        dynamicLoadBasicPidGridLayout->addWidget(dynamicLoadKiSpinBox, 1, 1, 1, 1);

        dynamicLoadKdLabel = new QLabel(dynamicLoadBasicPidGroup);
        dynamicLoadKdLabel->setObjectName(QString::fromUtf8("dynamicLoadKdLabel"));

        dynamicLoadBasicPidGridLayout->addWidget(dynamicLoadKdLabel, 2, 0, 1, 1);

        dynamicLoadKdSpinBox = new QDoubleSpinBox(dynamicLoadBasicPidGroup);
        dynamicLoadKdSpinBox->setObjectName(QString::fromUtf8("dynamicLoadKdSpinBox"));
        dynamicLoadKdSpinBox->setDecimals(3);
        dynamicLoadKdSpinBox->setMinimum(0.000000000000000);
        dynamicLoadKdSpinBox->setMaximum(10.000000000000000);
        dynamicLoadKdSpinBox->setSingleStep(0.100000000000000);
        dynamicLoadKdSpinBox->setValue(0.000000000000000);

        dynamicLoadBasicPidGridLayout->addWidget(dynamicLoadKdSpinBox, 2, 1, 1, 1);

        dynamicLoadKsLabel = new QLabel(dynamicLoadBasicPidGroup);
        dynamicLoadKsLabel->setObjectName(QString::fromUtf8("dynamicLoadKsLabel"));

        dynamicLoadBasicPidGridLayout->addWidget(dynamicLoadKsLabel, 3, 0, 1, 1);

        dynamicLoadKsSpinBox = new QDoubleSpinBox(dynamicLoadBasicPidGroup);
        dynamicLoadKsSpinBox->setObjectName(QString::fromUtf8("dynamicLoadKsSpinBox"));
        dynamicLoadKsSpinBox->setDecimals(3);
        dynamicLoadKsSpinBox->setMinimum(0.000000000000000);
        dynamicLoadKsSpinBox->setMaximum(10.000000000000000);
        dynamicLoadKsSpinBox->setSingleStep(0.100000000000000);
        dynamicLoadKsSpinBox->setValue(0.000000000000000);

        dynamicLoadBasicPidGridLayout->addWidget(dynamicLoadKsSpinBox, 3, 1, 1, 1);

        dynamicLoadKaLabel = new QLabel(dynamicLoadBasicPidGroup);
        dynamicLoadKaLabel->setObjectName(QString::fromUtf8("dynamicLoadKaLabel"));

        dynamicLoadBasicPidGridLayout->addWidget(dynamicLoadKaLabel, 4, 0, 1, 1);

        dynamicLoadKaSpinBox = new QDoubleSpinBox(dynamicLoadBasicPidGroup);
        dynamicLoadKaSpinBox->setObjectName(QString::fromUtf8("dynamicLoadKaSpinBox"));
        dynamicLoadKaSpinBox->setDecimals(3);
        dynamicLoadKaSpinBox->setMinimum(0.000000000000000);
        dynamicLoadKaSpinBox->setMaximum(10.000000000000000);
        dynamicLoadKaSpinBox->setSingleStep(0.100000000000000);
        dynamicLoadKaSpinBox->setValue(0.000000000000000);

        dynamicLoadBasicPidGridLayout->addWidget(dynamicLoadKaSpinBox, 4, 1, 1, 1);


        dynamicLoadMainLayout->addWidget(dynamicLoadBasicPidGroup);

        dynamicLoadStepPidGroup = new QGroupBox(dynamicLoadScrollAreaWidgetContents);
        dynamicLoadStepPidGroup->setObjectName(QString::fromUtf8("dynamicLoadStepPidGroup"));
        dynamicLoadStepPidGridLayout = new QGridLayout(dynamicLoadStepPidGroup);
        dynamicLoadStepPidGridLayout->setObjectName(QString::fromUtf8("dynamicLoadStepPidGridLayout"));
        dynamicLoadKpStepLabel = new QLabel(dynamicLoadStepPidGroup);
        dynamicLoadKpStepLabel->setObjectName(QString::fromUtf8("dynamicLoadKpStepLabel"));

        dynamicLoadStepPidGridLayout->addWidget(dynamicLoadKpStepLabel, 0, 0, 1, 1);

        dynamicLoadKpStepSpinBox = new QDoubleSpinBox(dynamicLoadStepPidGroup);
        dynamicLoadKpStepSpinBox->setObjectName(QString::fromUtf8("dynamicLoadKpStepSpinBox"));
        dynamicLoadKpStepSpinBox->setDecimals(3);
        dynamicLoadKpStepSpinBox->setMinimum(0.001000000000000);
        dynamicLoadKpStepSpinBox->setMaximum(10.000000000000000);
        dynamicLoadKpStepSpinBox->setSingleStep(0.001000000000000);
        dynamicLoadKpStepSpinBox->setValue(0.100000000000000);

        dynamicLoadStepPidGridLayout->addWidget(dynamicLoadKpStepSpinBox, 0, 1, 1, 1);

        dynamicLoadKiStepLabel = new QLabel(dynamicLoadStepPidGroup);
        dynamicLoadKiStepLabel->setObjectName(QString::fromUtf8("dynamicLoadKiStepLabel"));

        dynamicLoadStepPidGridLayout->addWidget(dynamicLoadKiStepLabel, 1, 0, 1, 1);

        dynamicLoadKiStepSpinBox = new QDoubleSpinBox(dynamicLoadStepPidGroup);
        dynamicLoadKiStepSpinBox->setObjectName(QString::fromUtf8("dynamicLoadKiStepSpinBox"));
        dynamicLoadKiStepSpinBox->setDecimals(3);
        dynamicLoadKiStepSpinBox->setMinimum(0.001000000000000);
        dynamicLoadKiStepSpinBox->setMaximum(10.000000000000000);
        dynamicLoadKiStepSpinBox->setSingleStep(0.001000000000000);
        dynamicLoadKiStepSpinBox->setValue(0.100000000000000);

        dynamicLoadStepPidGridLayout->addWidget(dynamicLoadKiStepSpinBox, 1, 1, 1, 1);

        dynamicLoadKdStepLabel = new QLabel(dynamicLoadStepPidGroup);
        dynamicLoadKdStepLabel->setObjectName(QString::fromUtf8("dynamicLoadKdStepLabel"));

        dynamicLoadStepPidGridLayout->addWidget(dynamicLoadKdStepLabel, 2, 0, 1, 1);

        dynamicLoadKdStepSpinBox = new QDoubleSpinBox(dynamicLoadStepPidGroup);
        dynamicLoadKdStepSpinBox->setObjectName(QString::fromUtf8("dynamicLoadKdStepSpinBox"));
        dynamicLoadKdStepSpinBox->setDecimals(3);
        dynamicLoadKdStepSpinBox->setMinimum(0.001000000000000);
        dynamicLoadKdStepSpinBox->setMaximum(10.000000000000000);
        dynamicLoadKdStepSpinBox->setSingleStep(0.001000000000000);
        dynamicLoadKdStepSpinBox->setValue(0.100000000000000);

        dynamicLoadStepPidGridLayout->addWidget(dynamicLoadKdStepSpinBox, 2, 1, 1, 1);

        dynamicLoadKsStepLabel = new QLabel(dynamicLoadStepPidGroup);
        dynamicLoadKsStepLabel->setObjectName(QString::fromUtf8("dynamicLoadKsStepLabel"));

        dynamicLoadStepPidGridLayout->addWidget(dynamicLoadKsStepLabel, 3, 0, 1, 1);

        dynamicLoadKsStepSpinBox = new QDoubleSpinBox(dynamicLoadStepPidGroup);
        dynamicLoadKsStepSpinBox->setObjectName(QString::fromUtf8("dynamicLoadKsStepSpinBox"));
        dynamicLoadKsStepSpinBox->setDecimals(3);
        dynamicLoadKsStepSpinBox->setMinimum(0.001000000000000);
        dynamicLoadKsStepSpinBox->setMaximum(10.000000000000000);
        dynamicLoadKsStepSpinBox->setSingleStep(0.001000000000000);
        dynamicLoadKsStepSpinBox->setValue(0.100000000000000);

        dynamicLoadStepPidGridLayout->addWidget(dynamicLoadKsStepSpinBox, 3, 1, 1, 1);

        dynamicLoadKaStepLabel = new QLabel(dynamicLoadStepPidGroup);
        dynamicLoadKaStepLabel->setObjectName(QString::fromUtf8("dynamicLoadKaStepLabel"));

        dynamicLoadStepPidGridLayout->addWidget(dynamicLoadKaStepLabel, 4, 0, 1, 1);

        dynamicLoadKaStepSpinBox = new QDoubleSpinBox(dynamicLoadStepPidGroup);
        dynamicLoadKaStepSpinBox->setObjectName(QString::fromUtf8("dynamicLoadKaStepSpinBox"));
        dynamicLoadKaStepSpinBox->setDecimals(3);
        dynamicLoadKaStepSpinBox->setMinimum(0.001000000000000);
        dynamicLoadKaStepSpinBox->setMaximum(10.000000000000000);
        dynamicLoadKaStepSpinBox->setSingleStep(0.001000000000000);
        dynamicLoadKaStepSpinBox->setValue(0.100000000000000);

        dynamicLoadStepPidGridLayout->addWidget(dynamicLoadKaStepSpinBox, 4, 1, 1, 1);


        dynamicLoadMainLayout->addWidget(dynamicLoadStepPidGroup);

        dynamicLoadVerticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        dynamicLoadMainLayout->addItem(dynamicLoadVerticalSpacer);

        dynamicLoadScrollArea->setWidget(dynamicLoadScrollAreaWidgetContents);

        dynamicLoadPidLayout->addWidget(dynamicLoadScrollArea);

        tabWidget_3->addTab(tab_3, QString());
        tab_4 = new QWidget();
        tab_4->setObjectName(QString::fromUtf8("tab_4"));
        speedControlPidLayout = new QVBoxLayout(tab_4);
        speedControlPidLayout->setObjectName(QString::fromUtf8("speedControlPidLayout"));
        speedControlScrollArea = new QScrollArea(tab_4);
        speedControlScrollArea->setObjectName(QString::fromUtf8("speedControlScrollArea"));
        speedControlScrollArea->setWidgetResizable(true);
        speedControlScrollAreaWidgetContents = new QWidget();
        speedControlScrollAreaWidgetContents->setObjectName(QString::fromUtf8("speedControlScrollAreaWidgetContents"));
        speedControlScrollAreaWidgetContents->setGeometry(QRect(0, 0, 188, 358));
        speedControlMainLayout = new QVBoxLayout(speedControlScrollAreaWidgetContents);
        speedControlMainLayout->setObjectName(QString::fromUtf8("speedControlMainLayout"));
        speedControlBasicPidGroup = new QGroupBox(speedControlScrollAreaWidgetContents);
        speedControlBasicPidGroup->setObjectName(QString::fromUtf8("speedControlBasicPidGroup"));
        speedControlBasicPidGridLayout = new QGridLayout(speedControlBasicPidGroup);
        speedControlBasicPidGridLayout->setObjectName(QString::fromUtf8("speedControlBasicPidGridLayout"));
        speedControlKpLabel = new QLabel(speedControlBasicPidGroup);
        speedControlKpLabel->setObjectName(QString::fromUtf8("speedControlKpLabel"));

        speedControlBasicPidGridLayout->addWidget(speedControlKpLabel, 0, 0, 1, 1);

        speedControlKpSpinBox = new QDoubleSpinBox(speedControlBasicPidGroup);
        speedControlKpSpinBox->setObjectName(QString::fromUtf8("speedControlKpSpinBox"));
        speedControlKpSpinBox->setDecimals(3);
        speedControlKpSpinBox->setMinimum(0.000000000000000);
        speedControlKpSpinBox->setMaximum(1000.000000000000000);
        speedControlKpSpinBox->setSingleStep(0.100000000000000);
        speedControlKpSpinBox->setValue(5.000000000000000);

        speedControlBasicPidGridLayout->addWidget(speedControlKpSpinBox, 0, 1, 1, 1);

        speedControlKiLabel = new QLabel(speedControlBasicPidGroup);
        speedControlKiLabel->setObjectName(QString::fromUtf8("speedControlKiLabel"));

        speedControlBasicPidGridLayout->addWidget(speedControlKiLabel, 1, 0, 1, 1);

        speedControlKiSpinBox = new QDoubleSpinBox(speedControlBasicPidGroup);
        speedControlKiSpinBox->setObjectName(QString::fromUtf8("speedControlKiSpinBox"));
        speedControlKiSpinBox->setDecimals(3);
        speedControlKiSpinBox->setMinimum(0.000000000000000);
        speedControlKiSpinBox->setMaximum(100.000000000000000);
        speedControlKiSpinBox->setSingleStep(0.100000000000000);
        speedControlKiSpinBox->setValue(0.000000000000000);

        speedControlBasicPidGridLayout->addWidget(speedControlKiSpinBox, 1, 1, 1, 1);

        speedControlKdLabel = new QLabel(speedControlBasicPidGroup);
        speedControlKdLabel->setObjectName(QString::fromUtf8("speedControlKdLabel"));

        speedControlBasicPidGridLayout->addWidget(speedControlKdLabel, 2, 0, 1, 1);

        speedControlKdSpinBox = new QDoubleSpinBox(speedControlBasicPidGroup);
        speedControlKdSpinBox->setObjectName(QString::fromUtf8("speedControlKdSpinBox"));
        speedControlKdSpinBox->setDecimals(3);
        speedControlKdSpinBox->setMinimum(0.000000000000000);
        speedControlKdSpinBox->setMaximum(10.000000000000000);
        speedControlKdSpinBox->setSingleStep(0.100000000000000);
        speedControlKdSpinBox->setValue(0.000000000000000);

        speedControlBasicPidGridLayout->addWidget(speedControlKdSpinBox, 2, 1, 1, 1);

        speedControlKsLabel = new QLabel(speedControlBasicPidGroup);
        speedControlKsLabel->setObjectName(QString::fromUtf8("speedControlKsLabel"));

        speedControlBasicPidGridLayout->addWidget(speedControlKsLabel, 3, 0, 1, 1);

        speedControlKsSpinBox = new QDoubleSpinBox(speedControlBasicPidGroup);
        speedControlKsSpinBox->setObjectName(QString::fromUtf8("speedControlKsSpinBox"));
        speedControlKsSpinBox->setDecimals(3);
        speedControlKsSpinBox->setMinimum(0.000000000000000);
        speedControlKsSpinBox->setMaximum(10.000000000000000);
        speedControlKsSpinBox->setSingleStep(0.100000000000000);
        speedControlKsSpinBox->setValue(0.000000000000000);

        speedControlBasicPidGridLayout->addWidget(speedControlKsSpinBox, 3, 1, 1, 1);

        speedControlKaLabel = new QLabel(speedControlBasicPidGroup);
        speedControlKaLabel->setObjectName(QString::fromUtf8("speedControlKaLabel"));

        speedControlBasicPidGridLayout->addWidget(speedControlKaLabel, 4, 0, 1, 1);

        speedControlKaSpinBox = new QDoubleSpinBox(speedControlBasicPidGroup);
        speedControlKaSpinBox->setObjectName(QString::fromUtf8("speedControlKaSpinBox"));
        speedControlKaSpinBox->setDecimals(3);
        speedControlKaSpinBox->setMinimum(0.000000000000000);
        speedControlKaSpinBox->setMaximum(10.000000000000000);
        speedControlKaSpinBox->setSingleStep(0.100000000000000);
        speedControlKaSpinBox->setValue(0.000000000000000);

        speedControlBasicPidGridLayout->addWidget(speedControlKaSpinBox, 4, 1, 1, 1);


        speedControlMainLayout->addWidget(speedControlBasicPidGroup);

        speedControlStepPidGroup = new QGroupBox(speedControlScrollAreaWidgetContents);
        speedControlStepPidGroup->setObjectName(QString::fromUtf8("speedControlStepPidGroup"));
        speedControlStepPidGridLayout = new QGridLayout(speedControlStepPidGroup);
        speedControlStepPidGridLayout->setObjectName(QString::fromUtf8("speedControlStepPidGridLayout"));
        speedControlKpStepLabel = new QLabel(speedControlStepPidGroup);
        speedControlKpStepLabel->setObjectName(QString::fromUtf8("speedControlKpStepLabel"));

        speedControlStepPidGridLayout->addWidget(speedControlKpStepLabel, 0, 0, 1, 1);

        speedControlKpStepSpinBox = new QDoubleSpinBox(speedControlStepPidGroup);
        speedControlKpStepSpinBox->setObjectName(QString::fromUtf8("speedControlKpStepSpinBox"));
        speedControlKpStepSpinBox->setDecimals(3);
        speedControlKpStepSpinBox->setMinimum(0.001000000000000);
        speedControlKpStepSpinBox->setMaximum(10.000000000000000);
        speedControlKpStepSpinBox->setSingleStep(0.001000000000000);
        speedControlKpStepSpinBox->setValue(0.100000000000000);

        speedControlStepPidGridLayout->addWidget(speedControlKpStepSpinBox, 0, 1, 1, 1);

        speedControlKiStepLabel = new QLabel(speedControlStepPidGroup);
        speedControlKiStepLabel->setObjectName(QString::fromUtf8("speedControlKiStepLabel"));

        speedControlStepPidGridLayout->addWidget(speedControlKiStepLabel, 1, 0, 1, 1);

        speedControlKiStepSpinBox = new QDoubleSpinBox(speedControlStepPidGroup);
        speedControlKiStepSpinBox->setObjectName(QString::fromUtf8("speedControlKiStepSpinBox"));
        speedControlKiStepSpinBox->setDecimals(3);
        speedControlKiStepSpinBox->setMinimum(0.001000000000000);
        speedControlKiStepSpinBox->setMaximum(10.000000000000000);
        speedControlKiStepSpinBox->setSingleStep(0.001000000000000);
        speedControlKiStepSpinBox->setValue(0.100000000000000);

        speedControlStepPidGridLayout->addWidget(speedControlKiStepSpinBox, 1, 1, 1, 1);

        speedControlKdStepLabel = new QLabel(speedControlStepPidGroup);
        speedControlKdStepLabel->setObjectName(QString::fromUtf8("speedControlKdStepLabel"));

        speedControlStepPidGridLayout->addWidget(speedControlKdStepLabel, 2, 0, 1, 1);

        speedControlKdStepSpinBox = new QDoubleSpinBox(speedControlStepPidGroup);
        speedControlKdStepSpinBox->setObjectName(QString::fromUtf8("speedControlKdStepSpinBox"));
        speedControlKdStepSpinBox->setDecimals(3);
        speedControlKdStepSpinBox->setMinimum(0.001000000000000);
        speedControlKdStepSpinBox->setMaximum(10.000000000000000);
        speedControlKdStepSpinBox->setSingleStep(0.001000000000000);
        speedControlKdStepSpinBox->setValue(0.100000000000000);

        speedControlStepPidGridLayout->addWidget(speedControlKdStepSpinBox, 2, 1, 1, 1);

        speedControlKsStepLabel = new QLabel(speedControlStepPidGroup);
        speedControlKsStepLabel->setObjectName(QString::fromUtf8("speedControlKsStepLabel"));

        speedControlStepPidGridLayout->addWidget(speedControlKsStepLabel, 3, 0, 1, 1);

        speedControlKsStepSpinBox = new QDoubleSpinBox(speedControlStepPidGroup);
        speedControlKsStepSpinBox->setObjectName(QString::fromUtf8("speedControlKsStepSpinBox"));
        speedControlKsStepSpinBox->setDecimals(3);
        speedControlKsStepSpinBox->setMinimum(0.001000000000000);
        speedControlKsStepSpinBox->setMaximum(10.000000000000000);
        speedControlKsStepSpinBox->setSingleStep(0.001000000000000);
        speedControlKsStepSpinBox->setValue(0.100000000000000);

        speedControlStepPidGridLayout->addWidget(speedControlKsStepSpinBox, 3, 1, 1, 1);

        speedControlKaStepLabel = new QLabel(speedControlStepPidGroup);
        speedControlKaStepLabel->setObjectName(QString::fromUtf8("speedControlKaStepLabel"));

        speedControlStepPidGridLayout->addWidget(speedControlKaStepLabel, 4, 0, 1, 1);

        speedControlKaStepSpinBox = new QDoubleSpinBox(speedControlStepPidGroup);
        speedControlKaStepSpinBox->setObjectName(QString::fromUtf8("speedControlKaStepSpinBox"));
        speedControlKaStepSpinBox->setDecimals(3);
        speedControlKaStepSpinBox->setMinimum(0.001000000000000);
        speedControlKaStepSpinBox->setMaximum(10.000000000000000);
        speedControlKaStepSpinBox->setSingleStep(0.001000000000000);
        speedControlKaStepSpinBox->setValue(0.100000000000000);

        speedControlStepPidGridLayout->addWidget(speedControlKaStepSpinBox, 4, 1, 1, 1);


        speedControlMainLayout->addWidget(speedControlStepPidGroup);

        speedControlVerticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        speedControlMainLayout->addItem(speedControlVerticalSpacer);

        speedControlScrollArea->setWidget(speedControlScrollAreaWidgetContents);

        speedControlPidLayout->addWidget(speedControlScrollArea);

        tabWidget_3->addTab(tab_4, QString());

        pidLayout->addWidget(tabWidget_3);

        tabWidget->addTab(pidTab, QString());

        mainLayout->addWidget(tabWidget);

        buttonLayout = new QHBoxLayout();
        buttonLayout->setObjectName(QString::fromUtf8("buttonLayout"));
        buttonSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        buttonLayout->addItem(buttonSpacer);

        okButton = new QPushButton(ControlChannelEditDialog);
        okButton->setObjectName(QString::fromUtf8("okButton"));

        buttonLayout->addWidget(okButton);

        cancelButton = new QPushButton(ControlChannelEditDialog);
        cancelButton->setObjectName(QString::fromUtf8("cancelButton"));

        buttonLayout->addWidget(cancelButton);


        mainLayout->addLayout(buttonLayout);


        retranslateUi(ControlChannelEditDialog);
        QObject::connect(cancelButton, SIGNAL(clicked()), ControlChannelEditDialog, SLOT(reject()));

        tabWidget->setCurrentIndex(0);
        tabWidget_2->setCurrentIndex(0);
        tabWidget_3->setCurrentIndex(0);
        okButton->setDefault(true);


        QMetaObject::connectSlotsByName(ControlChannelEditDialog);
    } // setupUi

    void retranslateUi(QWidget *ControlChannelEditDialog)
    {
        ControlChannelEditDialog->setWindowTitle(QCoreApplication::translate("ControlChannelEditDialog", "\346\216\247\345\210\266\351\200\232\351\201\223\351\205\215\347\275\256\347\274\226\350\276\221", nullptr));
        channelNameLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "\351\200\232\351\201\223\345\220\215\347\247\260:", nullptr));
        channelNameEdit->setPlaceholderText(QCoreApplication::translate("ControlChannelEditDialog", "\350\257\267\350\276\223\345\205\245\351\200\232\351\201\223\345\220\215\347\247\260", nullptr));
        lcIdLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "\344\270\213\344\275\215\346\234\272ID:", nullptr));
        stationIdLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "\347\253\231\347\202\271ID:", nullptr));
        enableLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "\345\220\257\347\224\250\347\212\266\346\200\201:", nullptr));
        enableCheckBox->setText(QCoreApplication::translate("ControlChannelEditDialog", "\345\220\257\347\224\250\351\200\232\351\201\223", nullptr));
        controlModeLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "\346\216\247\345\210\266\346\250\241\345\274\217:", nullptr));
        controlModeCombo->setItemText(0, QCoreApplication::translate("ControlChannelEditDialog", "\344\275\215\347\275\256\346\216\247\345\210\266", nullptr));
        controlModeCombo->setItemText(1, QCoreApplication::translate("ControlChannelEditDialog", "\345\212\233\346\216\247\345\210\266", nullptr));
        controlModeCombo->setItemText(2, QCoreApplication::translate("ControlChannelEditDialog", "\346\267\267\345\220\210\346\216\247\345\210\266", nullptr));

        tabWidget->setTabText(tabWidget->indexOf(basicInfoTab), QCoreApplication::translate("ControlChannelEditDialog", "\345\237\272\346\234\254\344\277\241\346\201\257", nullptr));
        hardwareLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "\347\241\254\344\273\266\345\205\263\350\201\224:", nullptr));
#if QT_CONFIG(tooltip)
        hardwareGroupCombo->setToolTip(QCoreApplication::translate("ControlChannelEditDialog", "\351\200\211\346\213\251\347\241\254\344\273\266\350\212\202\347\202\271\347\273\204", nullptr));
#endif // QT_CONFIG(tooltip)
#if QT_CONFIG(tooltip)
        hardwareChannelCombo->setToolTip(QCoreApplication::translate("ControlChannelEditDialog", "\351\200\211\346\213\251\347\241\254\344\273\266\351\200\232\351\201\223", nullptr));
#endif // QT_CONFIG(tooltip)
        load1SensorLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "\350\275\275\350\215\2671\344\274\240\346\204\237\345\231\250:", nullptr));
#if QT_CONFIG(tooltip)
        load1SensorGroupCombo->setToolTip(QCoreApplication::translate("ControlChannelEditDialog", "\351\200\211\346\213\251\344\274\240\346\204\237\345\231\250\347\273\204", nullptr));
#endif // QT_CONFIG(tooltip)
#if QT_CONFIG(tooltip)
        load1SensorDeviceCombo->setToolTip(QCoreApplication::translate("ControlChannelEditDialog", "\351\200\211\346\213\251\344\274\240\346\204\237\345\231\250\350\256\276\345\244\207", nullptr));
#endif // QT_CONFIG(tooltip)
        load2SensorLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "\350\275\275\350\215\2672\344\274\240\346\204\237\345\231\250:", nullptr));
#if QT_CONFIG(tooltip)
        load2SensorGroupCombo->setToolTip(QCoreApplication::translate("ControlChannelEditDialog", "\351\200\211\346\213\251\344\274\240\346\204\237\345\231\250\347\273\204", nullptr));
#endif // QT_CONFIG(tooltip)
#if QT_CONFIG(tooltip)
        load2SensorDeviceCombo->setToolTip(QCoreApplication::translate("ControlChannelEditDialog", "\351\200\211\346\213\251\344\274\240\346\204\237\345\231\250\350\256\276\345\244\207", nullptr));
#endif // QT_CONFIG(tooltip)
        positionSensorLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "\344\275\215\347\275\256\344\274\240\346\204\237\345\231\250:", nullptr));
#if QT_CONFIG(tooltip)
        positionSensorGroupCombo->setToolTip(QCoreApplication::translate("ControlChannelEditDialog", "\351\200\211\346\213\251\344\274\240\346\204\237\345\231\250\347\273\204", nullptr));
#endif // QT_CONFIG(tooltip)
#if QT_CONFIG(tooltip)
        positionSensorDeviceCombo->setToolTip(QCoreApplication::translate("ControlChannelEditDialog", "\351\200\211\346\213\251\344\274\240\346\204\237\345\231\250\350\256\276\345\244\207", nullptr));
#endif // QT_CONFIG(tooltip)
        controlActuatorLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "\346\216\247\345\210\266\344\275\234\345\212\250\345\231\250:", nullptr));
#if QT_CONFIG(tooltip)
        controlActuatorGroupCombo->setToolTip(QCoreApplication::translate("ControlChannelEditDialog", "\351\200\211\346\213\251\344\275\234\345\212\250\345\231\250\347\273\204", nullptr));
#endif // QT_CONFIG(tooltip)
#if QT_CONFIG(tooltip)
        controlActuatorDeviceCombo->setToolTip(QCoreApplication::translate("ControlChannelEditDialog", "\351\200\211\346\213\251\344\275\234\345\212\250\345\231\250\350\256\276\345\244\207", nullptr));
#endif // QT_CONFIG(tooltip)
        tabWidget->setTabText(tabWidget->indexOf(associationTab), QCoreApplication::translate("ControlChannelEditDialog", "\345\205\263\350\201\224\351\205\215\347\275\256", nullptr));
        servoControlPolarityLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "\346\216\247\345\210\266\344\275\234\345\212\250\345\231\250\346\236\201\346\200\247:", nullptr));
        servoControlPolarityCombo->setItemText(0, QCoreApplication::translate("ControlChannelEditDialog", "\346\255\243\346\236\201\346\200\247 (+)", nullptr));
        servoControlPolarityCombo->setItemText(1, QCoreApplication::translate("ControlChannelEditDialog", "\350\264\237\346\236\201\346\200\247 (-)", nullptr));
        servoControlPolarityCombo->setItemText(2, QCoreApplication::translate("ControlChannelEditDialog", "\345\217\214\346\236\201\346\200\247 (\302\261)", nullptr));
        servoControlPolarityCombo->setItemText(3, QCoreApplication::translate("ControlChannelEditDialog", "\346\227\240", nullptr));

        payload1PolarityLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "\350\275\275\350\215\2671\344\274\240\346\204\237\345\231\250\346\236\201\346\200\247:", nullptr));
        payload1PolarityCombo->setItemText(0, QCoreApplication::translate("ControlChannelEditDialog", "\346\255\243\346\236\201\346\200\247 (+)", nullptr));
        payload1PolarityCombo->setItemText(1, QCoreApplication::translate("ControlChannelEditDialog", "\350\264\237\346\236\201\346\200\247 (-)", nullptr));
        payload1PolarityCombo->setItemText(2, QCoreApplication::translate("ControlChannelEditDialog", "\345\217\214\346\236\201\346\200\247 (\302\261)", nullptr));
        payload1PolarityCombo->setItemText(3, QCoreApplication::translate("ControlChannelEditDialog", "\346\227\240", nullptr));

        payload2PolarityLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "\350\275\275\350\215\2672\344\274\240\346\204\237\345\231\250\346\236\201\346\200\247:", nullptr));
        payload2PolarityCombo->setItemText(0, QCoreApplication::translate("ControlChannelEditDialog", "\346\255\243\346\236\201\346\200\247 (+)", nullptr));
        payload2PolarityCombo->setItemText(1, QCoreApplication::translate("ControlChannelEditDialog", "\350\264\237\346\236\201\346\200\247 (-)", nullptr));
        payload2PolarityCombo->setItemText(2, QCoreApplication::translate("ControlChannelEditDialog", "\345\217\214\346\236\201\346\200\247 (\302\261)", nullptr));
        payload2PolarityCombo->setItemText(3, QCoreApplication::translate("ControlChannelEditDialog", "\346\227\240", nullptr));

        positionPolarityLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "\344\275\215\347\275\256\344\274\240\346\204\237\345\231\250\346\236\201\346\200\247:", nullptr));
        positionPolarityCombo->setItemText(0, QCoreApplication::translate("ControlChannelEditDialog", "\346\255\243\346\236\201\346\200\247 (+)", nullptr));
        positionPolarityCombo->setItemText(1, QCoreApplication::translate("ControlChannelEditDialog", "\350\264\237\346\236\201\346\200\247 (-)", nullptr));
        positionPolarityCombo->setItemText(2, QCoreApplication::translate("ControlChannelEditDialog", "\345\217\214\346\236\201\346\200\247 (\302\261)", nullptr));
        positionPolarityCombo->setItemText(3, QCoreApplication::translate("ControlChannelEditDialog", "\346\227\240", nullptr));

        tabWidget->setTabText(tabWidget->indexOf(polarityTab), QCoreApplication::translate("ControlChannelEditDialog", "\346\236\201\346\200\247\351\205\215\347\275\256", nullptr));
        payloadInnerGroupBox->setTitle(QCoreApplication::translate("ControlChannelEditDialog", "Inner\351\231\220\345\210\266", nullptr));
        payloadInnerMinLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "min:", nullptr));
        payloadInnerMaxLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "max:", nullptr));
        payloadInnerUnitLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "unit:", nullptr));
        payloadInnerUnitCombo->setItemText(0, QCoreApplication::translate("ControlChannelEditDialog", "N", nullptr));
        payloadInnerUnitCombo->setItemText(1, QCoreApplication::translate("ControlChannelEditDialog", "kN", nullptr));

        payloadInnerDurationLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "duration:", nullptr));
        payloadInnerEnabledLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "enabled:", nullptr));
        payloadInnerTypeLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "type:", nullptr));
        payloadInnerTypeCombo->setItemText(0, QCoreApplication::translate("ControlChannelEditDialog", "\347\261\273\345\236\2131", nullptr));
        payloadInnerTypeCombo->setItemText(1, QCoreApplication::translate("ControlChannelEditDialog", "\347\261\273\345\236\2132", nullptr));
        payloadInnerTypeCombo->setItemText(2, QCoreApplication::translate("ControlChannelEditDialog", "\347\261\273\345\236\2133", nullptr));

        payloadOuterGroupBox->setTitle(QCoreApplication::translate("ControlChannelEditDialog", "Outer\351\231\220\345\210\266", nullptr));
        payloadOuterMinLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "min:", nullptr));
        payloadOuterMaxLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "max:", nullptr));
        payloadOuterUnitLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "unit:", nullptr));
        payloadOuterUnitCombo->setItemText(0, QCoreApplication::translate("ControlChannelEditDialog", "N", nullptr));
        payloadOuterUnitCombo->setItemText(1, QCoreApplication::translate("ControlChannelEditDialog", "kN", nullptr));

        payloadOuterDurationLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "duration:", nullptr));
        payloadOuterEnabledLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "enabled:", nullptr));
        payloadOuterTypeLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "type:", nullptr));
        payloadOuterTypeCombo->setItemText(0, QCoreApplication::translate("ControlChannelEditDialog", "\347\261\273\345\236\2131", nullptr));
        payloadOuterTypeCombo->setItemText(1, QCoreApplication::translate("ControlChannelEditDialog", "\347\261\273\345\236\2132", nullptr));
        payloadOuterTypeCombo->setItemText(2, QCoreApplication::translate("ControlChannelEditDialog", "\347\261\273\345\236\2133", nullptr));

        payloadMainGroupBox->setTitle(QCoreApplication::translate("ControlChannelEditDialog", "Main\351\231\220\345\210\266", nullptr));
        payloadMainMaxLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "max:", nullptr));
        payloadMainUnitLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "unit:", nullptr));
        payloadMainTypeLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "type:", nullptr));
        payloadMainTypeCombo->setItemText(0, QCoreApplication::translate("ControlChannelEditDialog", "\347\261\273\345\236\2131", nullptr));
        payloadMainTypeCombo->setItemText(1, QCoreApplication::translate("ControlChannelEditDialog", "\347\261\273\345\236\2132", nullptr));
        payloadMainTypeCombo->setItemText(2, QCoreApplication::translate("ControlChannelEditDialog", "\347\261\273\345\236\2133", nullptr));

        payloadMainMinLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "min:", nullptr));
        payloadMainDurationLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "duration:", nullptr));
        payloadMainUnitCombo->setItemText(0, QCoreApplication::translate("ControlChannelEditDialog", "N", nullptr));
        payloadMainUnitCombo->setItemText(1, QCoreApplication::translate("ControlChannelEditDialog", "kN", nullptr));

        payloadMainEnabledLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "enabled:", nullptr));
        tabWidget_2->setTabText(tabWidget_2->indexOf(payloadLimitsTab), QCoreApplication::translate("ControlChannelEditDialog", "payload_limits", nullptr));
        positionInnerGroupBox->setTitle(QCoreApplication::translate("ControlChannelEditDialog", "Inner\351\231\220\345\210\266", nullptr));
        positionInnerMinLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "min:", nullptr));
        positionInnerMaxLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "max:", nullptr));
        positionInnerUnitLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "unit:", nullptr));
        positionInnerUnitCombo->setItemText(0, QCoreApplication::translate("ControlChannelEditDialog", "mm", nullptr));
        positionInnerUnitCombo->setItemText(1, QCoreApplication::translate("ControlChannelEditDialog", "cm", nullptr));

        positionInnerDurationLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "duration:", nullptr));
        positionInnerEnabledLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "enabled:", nullptr));
        positionInnerTypeLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "type:", nullptr));
        positionInnerTypeCombo->setItemText(0, QCoreApplication::translate("ControlChannelEditDialog", "\347\261\273\345\236\2131", nullptr));
        positionInnerTypeCombo->setItemText(1, QCoreApplication::translate("ControlChannelEditDialog", "\347\261\273\345\236\2132", nullptr));
        positionInnerTypeCombo->setItemText(2, QCoreApplication::translate("ControlChannelEditDialog", "\347\261\273\345\236\2133", nullptr));

        positionOuterGroupBox->setTitle(QCoreApplication::translate("ControlChannelEditDialog", "Outer\351\231\220\345\210\266", nullptr));
        positionOuterMinLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "min:", nullptr));
        positionOuterMaxLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "max:", nullptr));
        positionOuterUnitLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "unit:", nullptr));
        positionOuterUnitCombo->setItemText(0, QCoreApplication::translate("ControlChannelEditDialog", "mm", nullptr));
        positionOuterUnitCombo->setItemText(1, QCoreApplication::translate("ControlChannelEditDialog", "cm", nullptr));

        positionOuterDurationLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "duration:", nullptr));
        positionOuterEnabledLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "enabled:", nullptr));
        positionOuterTypeLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "type:", nullptr));
        positionOuterTypeCombo->setItemText(0, QCoreApplication::translate("ControlChannelEditDialog", "\347\261\273\345\236\2131", nullptr));
        positionOuterTypeCombo->setItemText(1, QCoreApplication::translate("ControlChannelEditDialog", "\347\261\273\345\236\2132", nullptr));
        positionOuterTypeCombo->setItemText(2, QCoreApplication::translate("ControlChannelEditDialog", "\347\261\273\345\236\2133", nullptr));

        positionMainGroupBox->setTitle(QCoreApplication::translate("ControlChannelEditDialog", "Main\351\231\220\345\210\266", nullptr));
        positionMainMinLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "min:", nullptr));
        positionMainMaxLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "max:", nullptr));
        positionMainUnitLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "unit:", nullptr));
        positionMainUnitCombo->setItemText(0, QCoreApplication::translate("ControlChannelEditDialog", "mm", nullptr));
        positionMainUnitCombo->setItemText(1, QCoreApplication::translate("ControlChannelEditDialog", "cm", nullptr));

        positionMainDurationLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "duration:", nullptr));
        positionMainEnabledLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "enabled:", nullptr));
        positionMainTypeLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "type:", nullptr));
        positionMainTypeCombo->setItemText(0, QCoreApplication::translate("ControlChannelEditDialog", "\347\261\273\345\236\2131", nullptr));
        positionMainTypeCombo->setItemText(1, QCoreApplication::translate("ControlChannelEditDialog", "\347\261\273\345\236\2132", nullptr));
        positionMainTypeCombo->setItemText(2, QCoreApplication::translate("ControlChannelEditDialog", "\347\261\273\345\236\2133", nullptr));

        tabWidget_2->setTabText(tabWidget_2->indexOf(positionLimitsTab), QCoreApplication::translate("ControlChannelEditDialog", "position_limits", nullptr));
        speedInnerGroupBox->setTitle(QCoreApplication::translate("ControlChannelEditDialog", "Inner\351\231\220\345\210\266", nullptr));
        speedInnerMinLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "min:", nullptr));
        speedInnerMaxLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "max:", nullptr));
        speedInnerUnitLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "unit:", nullptr));
        speedInnerUnitCombo->setItemText(0, QCoreApplication::translate("ControlChannelEditDialog", "mm/s", nullptr));
        speedInnerUnitCombo->setItemText(1, QCoreApplication::translate("ControlChannelEditDialog", "cm/s", nullptr));

        speedInnerDurationLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "duration:", nullptr));
        speedInnerEnabledLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "enabled:", nullptr));
        speedInnerTypeLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "type:", nullptr));
        speedInnerTypeCombo->setItemText(0, QCoreApplication::translate("ControlChannelEditDialog", "\347\261\273\345\236\2131", nullptr));
        speedInnerTypeCombo->setItemText(1, QCoreApplication::translate("ControlChannelEditDialog", "\347\261\273\345\236\2132", nullptr));
        speedInnerTypeCombo->setItemText(2, QCoreApplication::translate("ControlChannelEditDialog", "\347\261\273\345\236\2133", nullptr));

        speedOuterGroupBox->setTitle(QCoreApplication::translate("ControlChannelEditDialog", "Outer\351\231\220\345\210\266", nullptr));
        speedOuterMinLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "min:", nullptr));
        speedOuterMaxLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "max:", nullptr));
        speedOuterUnitLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "unit:", nullptr));
        speedOuterUnitCombo->setItemText(0, QCoreApplication::translate("ControlChannelEditDialog", "mm/s", nullptr));
        speedOuterUnitCombo->setItemText(1, QCoreApplication::translate("ControlChannelEditDialog", "cm/s", nullptr));

        speedOuterDurationLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "duration:", nullptr));
        speedOuterEnabledLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "enabled:", nullptr));
        speedOuterTypeLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "type:", nullptr));
        speedOuterTypeCombo->setItemText(0, QCoreApplication::translate("ControlChannelEditDialog", "\347\261\273\345\236\2131", nullptr));
        speedOuterTypeCombo->setItemText(1, QCoreApplication::translate("ControlChannelEditDialog", "\347\261\273\345\236\2132", nullptr));
        speedOuterTypeCombo->setItemText(2, QCoreApplication::translate("ControlChannelEditDialog", "\347\261\273\345\236\2133", nullptr));

        speedMainGroupBox->setTitle(QCoreApplication::translate("ControlChannelEditDialog", "Main\351\231\220\345\210\266", nullptr));
        speedMainMinLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "min:", nullptr));
        speedMainMaxLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "max:", nullptr));
        speedMainUnitLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "unit:", nullptr));
        speedMainUnitCombo->setItemText(0, QCoreApplication::translate("ControlChannelEditDialog", "mm/s", nullptr));
        speedMainUnitCombo->setItemText(1, QCoreApplication::translate("ControlChannelEditDialog", "cm/s", nullptr));

        speedMainDurationLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "duration:", nullptr));
        speedMainEnabledLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "enabled:", nullptr));
        speedMainTypeLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "type:", nullptr));
        speedMainTypeCombo->setItemText(0, QCoreApplication::translate("ControlChannelEditDialog", "\347\261\273\345\236\2131", nullptr));
        speedMainTypeCombo->setItemText(1, QCoreApplication::translate("ControlChannelEditDialog", "\347\261\273\345\236\2132", nullptr));
        speedMainTypeCombo->setItemText(2, QCoreApplication::translate("ControlChannelEditDialog", "\347\261\273\345\236\2133", nullptr));

        tabWidget_2->setTabText(tabWidget_2->indexOf(speedLimitsTab), QCoreApplication::translate("ControlChannelEditDialog", "speed_limits", nullptr));
        tabWidget->setTabText(tabWidget->indexOf(thresholdsTab), QCoreApplication::translate("ControlChannelEditDialog", "limits", nullptr));
        forceFilterLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "<b>payload_fc (Hz)</b>", nullptr));
        forceLowpassLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "payload_fc:", nullptr));
        forceHighpassLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "valid:", nullptr));
        validComboBox->setItemText(0, QCoreApplication::translate("ControlChannelEditDialog", "true", nullptr));
        validComboBox->setItemText(1, QCoreApplication::translate("ControlChannelEditDialog", "false", nullptr));

        positionFilterLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "<b>position_fc (Hz)</b>", nullptr));
        positionLowpassLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "position_fc:", nullptr));
        positionHighpassLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "speed_fc:", nullptr));
        velocityFilterLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "<b>acc_fc (Hz)</b>", nullptr));
        velocityLowpassLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "acc_fc:", nullptr));
        tabWidget->setTabText(tabWidget->indexOf(filterTab), QCoreApplication::translate("ControlChannelEditDialog", "filter", nullptr));
        positionBasicPidGroup->setTitle(QCoreApplication::translate("ControlChannelEditDialog", "\345\237\272\346\234\254PID\345\217\202\346\225\260", nullptr));
        positionKpLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "kp:", nullptr));
        positionKiLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "ki:", nullptr));
        positionKdLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "kd:", nullptr));
        positionKsLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "ks:", nullptr));
        positionKaLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "ka:", nullptr));
        positionStepPidGroup->setTitle(QCoreApplication::translate("ControlChannelEditDialog", "\345\217\202\346\225\260\350\260\203\346\225\264\346\255\245\351\225\277", nullptr));
        positionKpStepLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "kp_step:", nullptr));
        positionKiStepLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "ki_step:", nullptr));
        positionKdStepLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "kd_step:", nullptr));
        positionKsStepLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "ks_step:", nullptr));
        positionKaStepLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "ka_step:", nullptr));
        tabWidget_3->setTabText(tabWidget_3->indexOf(tab), QCoreApplication::translate("ControlChannelEditDialog", "position_control_pid", nullptr));
        staticLoadBasicPidGroup->setTitle(QCoreApplication::translate("ControlChannelEditDialog", "\345\237\272\346\234\254PID\345\217\202\346\225\260", nullptr));
        staticLoadKpLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "kp:", nullptr));
        staticLoadKiLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "ki:", nullptr));
        staticLoadKdLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "kd:", nullptr));
        staticLoadKsLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "ks:", nullptr));
        staticLoadKaLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "ka:", nullptr));
        staticLoadStepPidGroup->setTitle(QCoreApplication::translate("ControlChannelEditDialog", "\345\217\202\346\225\260\350\260\203\346\225\264\346\255\245\351\225\277", nullptr));
        staticLoadKpStepLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "kp_step:", nullptr));
        staticLoadKiStepLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "ki_step:", nullptr));
        staticLoadKdStepLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "kd_step:", nullptr));
        staticLoadKsStepLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "ks_step:", nullptr));
        staticLoadKaStepLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "ka_step:", nullptr));
        tabWidget_3->setTabText(tabWidget_3->indexOf(tab_2), QCoreApplication::translate("ControlChannelEditDialog", "static_load_pid", nullptr));
        dynamicLoadBasicPidGroup->setTitle(QCoreApplication::translate("ControlChannelEditDialog", "\345\237\272\346\234\254PID\345\217\202\346\225\260", nullptr));
        dynamicLoadKpLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "kp:", nullptr));
        dynamicLoadKiLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "ki:", nullptr));
        dynamicLoadKdLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "kd:", nullptr));
        dynamicLoadKsLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "ks:", nullptr));
        dynamicLoadKaLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "ka:", nullptr));
        dynamicLoadStepPidGroup->setTitle(QCoreApplication::translate("ControlChannelEditDialog", "\345\217\202\346\225\260\350\260\203\346\225\264\346\255\245\351\225\277", nullptr));
        dynamicLoadKpStepLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "kp_step:", nullptr));
        dynamicLoadKiStepLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "ki_step:", nullptr));
        dynamicLoadKdStepLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "kd_step:", nullptr));
        dynamicLoadKsStepLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "ks_step:", nullptr));
        dynamicLoadKaStepLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "ka_step:", nullptr));
        tabWidget_3->setTabText(tabWidget_3->indexOf(tab_3), QCoreApplication::translate("ControlChannelEditDialog", "dynamic_load_pid", nullptr));
        speedControlBasicPidGroup->setTitle(QCoreApplication::translate("ControlChannelEditDialog", "\345\237\272\346\234\254PID\345\217\202\346\225\260", nullptr));
        speedControlKpLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "kp:", nullptr));
        speedControlKiLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "ki:", nullptr));
        speedControlKdLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "kd:", nullptr));
        speedControlKsLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "ks:", nullptr));
        speedControlKaLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "ka:", nullptr));
        speedControlStepPidGroup->setTitle(QCoreApplication::translate("ControlChannelEditDialog", "\345\217\202\346\225\260\350\260\203\346\225\264\346\255\245\351\225\277", nullptr));
        speedControlKpStepLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "kp_step:", nullptr));
        speedControlKiStepLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "ki_step:", nullptr));
        speedControlKdStepLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "kd_step:", nullptr));
        speedControlKsStepLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "ks_step:", nullptr));
        speedControlKaStepLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "ka_step:", nullptr));
        tabWidget_3->setTabText(tabWidget_3->indexOf(tab_4), QCoreApplication::translate("ControlChannelEditDialog", "speed_control_pid", nullptr));
        tabWidget->setTabText(tabWidget->indexOf(pidTab), QCoreApplication::translate("ControlChannelEditDialog", "pid", nullptr));
        okButton->setText(QCoreApplication::translate("ControlChannelEditDialog", "\347\241\256\345\256\232", nullptr));
        cancelButton->setText(QCoreApplication::translate("ControlChannelEditDialog", "\345\217\226\346\266\210", nullptr));
    } // retranslateUi

};

namespace Ui {
    class ControlChannelEditDialog: public Ui_ControlChannelEditDialog {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_CONTROLCHANNELEDITDIALOG_H
