@echo off
echo ========================================
echo  测试DataManager集成功能
echo ========================================

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    echo 请确保Qt 5.14.2 MinGW版本已正确安装
    pause
    exit /b 1
)

g++ --version
if errorlevel 1 (
    echo 错误: MinGW编译器未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 编译DataManager集成测试...

REM 编译测试程序
g++ -std=c++14 ^
    -I include ^
    -I %QTDIR%\include ^
    -I %QTDIR%\include\QtCore ^
    -I %QTDIR%\include\QtWidgets ^
    -L %QTDIR%\lib ^
    -lQt5Core ^
    -lQt5Widgets ^
    test_datamanager_integration.cpp ^
    src/DataModels_Simple.cpp ^
    src/SensorDataManager.cpp ^
    src/ActuatorDataManager.cpp ^
    src/Utils_Fixed.cpp ^
    -o test_datamanager_integration.exe

if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo 请检查上面的错误信息。
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  编译成功！开始运行测试...
    echo ========================================
    echo.
    
    REM 运行测试
    test_datamanager_integration.exe
    
    if errorlevel 1 (
        echo.
        echo ❌ 测试失败！
    ) else (
        echo.
        echo ✅ 测试成功！DataManager集成工作正常。
    )
)

echo.
echo 测试完成。
pause
