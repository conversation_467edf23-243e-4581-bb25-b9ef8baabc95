/**
 * @file SensorDataManager_1_2.cpp
 * @brief 传感器数据管理器1_2版本实现
 * @details 基于MVVM架构设计的增强版传感器数据管理器，参照ActuatorDataManager_1_2架构
 * <AUTHOR> Assistant
 * @date 2025-08-23
 * @version 1.2.0
 */

#include "SensorDataManager_1_2.h"
#include "DataModels_Fixed.h"
#include <QtCore/QDebug>
#include <QtCore/QRegularExpression>
#include <QtCore/QDateTime>
#include <algorithm>
#include <QTimer> // Added for QTimer

SensorDataManager_1_2::SensorDataManager_1_2()
    : nextGroupId_(1)
    , nextSensorId_(1)
{
    clearError();
    initializeStorage();
}

SensorDataManager_1_2::~SensorDataManager_1_2()
{
}

// 🆕 新增：传感器详细参数管理接口（与作动器对等）
// bool SensorDataManager_1_2::saveSensorDetailedParams(const UI::SensorParams_1_2& params) {
//     return addSensor(params);
// }

// UI::SensorParams_1_2 SensorDataManager_1_2::getSensorDetailedParams(const QString& serialNumber) const {
//     return getSensor(serialNumber);
// }

// bool SensorDataManager_1_2::updateSensorDetailedParams(const QString& serialNumber, const UI::SensorParams_1_2& params) {
//     return updateSensor(serialNumber, params);
// }

// bool SensorDataManager_1_2::removeSensorDetailedParams(const QString& serialNumber) {
//     return removeSensor(serialNumber);
// }

// QList<UI::SensorParams_1_2> SensorDataManager_1_2::getAllSensorDetailedParams() const {
//     return getAllSensors();
// }

// 🆕 新增：带组ID的传感器详细参数管理接口实现
bool SensorDataManager_1_2::saveSensorDetailedParamsInGroup(int groupId, const UI::SensorParams_1_2& params) {
    return addSensor(params, groupId);
}

UI::SensorParams_1_2 SensorDataManager_1_2::getSensorDetailedParamsInGroup(int groupId, const QString& serialNumber, bool &isHasData) const {
    if (!isValidGroupId(groupId)) {
        setError(QString(u8"无效的组ID: %1").arg(groupId));
        isHasData = false;
        return UI::SensorParams_1_2();
    }
    
    if (groupedSensorStorage_.contains(groupId) && 
        groupedSensorStorage_[groupId].contains(serialNumber)) {
        clearError();
        isHasData = true;
        return groupedSensorStorage_[groupId][serialNumber];
    }
    
    setError(QString(u8"在组%1中未找到传感器: %2").arg(groupId).arg(serialNumber));
    isHasData = false;
    return UI::SensorParams_1_2();
}

bool SensorDataManager_1_2::updateSensorDetailedParamsInGroup(int groupId, const QString& serialNumber, const UI::SensorParams_1_2& params) {
    if (!isValidGroupId(groupId)) {
        setError(QString(u8"无效的组ID: %1").arg(groupId));
        return false;
    }
    
    if (!validateSensorParams(params)) {
        return false; // 错误信息已在validateSensorParams中设置
    }
    
    try {
        // 检查传感器是否存在于指定组中
        if (!groupedSensorStorage_.contains(groupId) || 
            !groupedSensorStorage_[groupId].contains(serialNumber)) {
            setError(QString(u8"在组%1中未找到传感器: %2").arg(groupId).arg(serialNumber));
            return false;
        }
        
        // 更新分层存储中的传感器数据
        groupedSensorStorage_[groupId][serialNumber] = params;
        
        // 同步更新组存储中的传感器数据
        if (groupStorage_.contains(groupId)) {
            UI::SensorGroup_1_2& group = groupStorage_[groupId];
            for (int i = 0; i < group.sensors.size(); ++i) {
                if (group.sensors[i].params_sn == serialNumber) {
                    group.sensors[i] = params;
                    break;
                }
            }
        }
        
        clearError();
        return true;
    } catch (const std::exception& e) {
        setError(QString(u8"更新组内传感器失败: %1").arg(e.what()));
        return false;
    }
}

bool SensorDataManager_1_2::removeSensorDetailedParamsInGroup(int groupId, const QString& serialNumber) {
    if (!isValidGroupId(groupId)) {
        setError(QString(u8"无效的组ID: %1").arg(groupId));
        return false;
    }
    
    try {
        // 🆕 删除前验证数据一致性
        if (!validateStorageConsistency()) {
            qWarning() << QString(u8"⚠️ 删除前检测到数据不一致，尝试自动修复...");
            syncGroupStorageFromGroupedStorage();
        }
        
        // 检查传感器是否存在于指定组中
        if (!groupedSensorStorage_.contains(groupId) || 
            !groupedSensorStorage_[groupId].contains(serialNumber)) {
            setError(QString(u8"在组%1中未找到传感器: %2").arg(groupId).arg(serialNumber));
            return false;
        }
        
        // 从分层存储中删除传感器
        groupedSensorStorage_[groupId].remove(serialNumber);
        
        // 如果该组没有传感器了，删除该组的条目
        if (groupedSensorStorage_[groupId].isEmpty()) {
            groupedSensorStorage_.remove(groupId);
        }
        
        // 从组存储中删除传感器
        if (groupStorage_.contains(groupId)) {
            UI::SensorGroup_1_2& group = groupStorage_[groupId];
            for (int i = group.sensors.size() - 1; i >= 0; --i) {
                if (group.sensors[i].params_sn == serialNumber) {
                    group.sensors.removeAt(i);
                    break;
                }
            }
            
            // 🆕 新增：删除后重新分配组内ID，确保连续递增
            reassignSensorIdsInGroup(group);
            groupStorage_[groupId] = group;
            
            qDebug() << QString(u8"✅ 删除传感器后重新分配组内ID: 组ID=%1, 剩余传感器数=%2")
                        .arg(groupId).arg(group.sensors.size());
        }
        
        // 🆕 删除后验证数据一致性
        if (!validateStorageConsistency()) {
            qWarning() << QString(u8"❌ 删除后数据仍不一致，强制同步...");
            syncGroupStorageFromGroupedStorage();
        }
        
        clearError();
        return true;
    } catch (const std::exception& e) {
        setError(QString(u8"删除组内传感器失败: %1").arg(e.what()));
        return false;
    }
}

// 🆕 新增：类型转换辅助方法实现
QString SensorDataManager_1_2::sensorTypeToString(const QString& type) {
    // 直接返回类型字符串（支持中文）
    return type;
}

QString SensorDataManager_1_2::stringToSensorType(const QString& str) {
    return str;
}

QString SensorDataManager_1_2::unitTypeToString(const QString& unit) {
    return unit;
}

QString SensorDataManager_1_2::stringToUnitType(const QString& str) {
    return str;
}

QString SensorDataManager_1_2::polarityToString(const QString& polarity) {
    return polarity;
}

QString SensorDataManager_1_2::stringToPolarity(const QString& str) {
    return str;
}

// 🔄 修改：使用分层存储架构的addSensor方法
// bool SensorDataManager_1_2::addSensor(const UI::SensorParams_1_2& params) {
//     // 🔄 修改：查找传感器所属组ID（向后兼容的方法）
//     int groupId = findGroupIdBySerialNumber(params.params_sn);
//     if (groupId <= 0) {
//         // 如果找不到现有组，尝试使用默认组ID 1
//         groupId = 1;
//         qDebug() << QString(u8"⚠️ 传感器 %1 未找到所属组，使用默认组ID: %2").arg(params.params_sn).arg(groupId);
//     }
//     
//     return addSensor(params, groupId);
// }

// 🆕 新增：指定组ID的addSensor方法
bool SensorDataManager_1_2::addSensor(const UI::SensorParams_1_2& params, int groupId) {
    if (!validateSensorParams(params)) {
        return false;
    }

    if (!isValidGroupId(groupId)) {
        setError(QString(u8"无效的组ID: %1").arg(groupId));
        return false;
    }

    try {
        // 🆕 修改：使用组内递增ID分配策略
        UI::SensorParams_1_2 sensorWithId = params;
        if (sensorWithId.sensorId == 0) {
            // 分配组内递增ID（从1开始）
            sensorWithId.sensorId = getNextSensorIdInGroup(groupId);
        }
        // 如果已经有ID，则直接使用（支持手动指定组内ID）

        // 🔄 修改：检查组内序列号唯一性
        if (groupedSensorStorage_.contains(groupId) && 
            groupedSensorStorage_[groupId].contains(params.params_sn)) {
            setError(QString(u8"组内传感器序列号重复: %1").arg(params.params_sn));
            return false;
        }

        // 🔄 修改：保存到分层存储中
        groupedSensorStorage_[groupId][params.params_sn] = sensorWithId;
        
        // 🆕 新增：同步更新到组存储中（确保数据一致性）
        if (groupStorage_.contains(groupId)) {
            // 如果组已存在，将传感器添加到组中
            UI::SensorGroup_1_2& group = groupStorage_[groupId];
            
            // 检查传感器是否已在组中存在
            bool sensorExists = false;
            for (int i = 0; i < group.sensors.size(); ++i) {
                if (group.sensors[i].params_sn == sensorWithId.params_sn) {
                    group.sensors[i] = sensorWithId; // 更新现有传感器
                    sensorExists = true;
                    break;
                }
            }
            
            if (!sensorExists) {
                group.sensors.append(sensorWithId); // 添加新传感器
            }
        }
        
        clearError();
        
        // 🆕 新增：发出数据变化信号，通知详细信息面板更新
        // 🔧 修复：延迟发出信号，确保数据完全保存后再通知
        QTimer::singleShot(100, [this, serialNumber = params.params_sn, groupId]() {
            // 🆕 新增：检查对象是否仍然有效
            if (!this) {
                qWarning() << "❌ [SensorDataManager] 延迟发送信号时this指针无效，跳过";
                return;
            }
            
            // 🆕 新增：检查对象是否仍然有效（通过父对象检查）
            try {
                if (!this->parent() && !this->objectName().isNull()) {
                    qWarning() << "❌ [SensorDataManager] 延迟发送信号时对象已无效，跳过";
                    return;
                }
            } catch (...) {
                qWarning() << "❌ [SensorDataManager] 延迟发送信号时对象状态检查失败，跳过";
                return;
            }
            
            // 🆕 新增：安全地发送信号
            try {
                emit sensorDataChanged(serialNumber, "create");
                emit sensorGroupDataChanged(groupId, "create");
                qDebug() << "✅ [SensorDataManager] 延迟信号发送成功: 传感器=" << serialNumber << "组ID=" << groupId;
            } catch (const std::exception& e) {
                qWarning() << "❌ [SensorDataManager] 延迟发送信号时发生异常:" << e.what();
            } catch (...) {
                qWarning() << "❌ [SensorDataManager] 延迟发送信号时发生未知异常";
            }
        });
        
        return true;
        
    } catch (const std::exception& e) {
        setError(QString(u8"添加传感器失败: %1").arg(e.what()));
        return false;
    } catch (...) {
        setError(QString(u8"添加传感器时发生未知异常"));
        return false;
    }
}

// 🔄 修改：使用分层存储架构的getSensor方法
// UI::SensorParams_1_2 SensorDataManager_1_2::getSensor(const QString& serialNumber) const {
//     try {
//         // 🔄 修改：在所有组中查找序列号
//         for (auto groupIt = groupedSensorStorage_.begin(); groupIt != groupedSensorStorage_.end(); ++groupIt) {
//             const auto& groupSensors = groupIt.value();
//             auto sensorIt = groupSensors.find(serialNumber);
//             if (sensorIt != groupSensors.end()) {
//                 return sensorIt.value();
//             }
//         }
//         return UI::SensorParams_1_2();
//     } catch (const std::exception& e) {
//         setError(QString(u8"获取传感器失败: %1").arg(e.what()));
//         return UI::SensorParams_1_2();
//     }
// }

// 🔄 修改：使用分层存储架构的updateSensor方法
// bool SensorDataManager_1_2::updateSensor(const QString& serialNumber, const UI::SensorParams_1_2& params) {
//     if (!validateSensorParams(params)) {
//         return false;
//     }

//     if (!hasSensor(serialNumber)) {
//         setError(QString(u8"传感器不存在: %1").arg(serialNumber));
//         return false;
//     }

//     try {
//         UI::SensorParams_1_2 updatedParams = params;
//         
//         // 🔄 修改：在所有组中查找并更新传感器
//         for (auto groupIt = groupedSensorStorage_.begin(); groupIt != groupedSensorStorage_.end(); ++groupIt) {
//             auto& groupSensors = groupIt.value();
//             if (groupSensors.contains(serialNumber)) {
//                 int originalId = groupSensors[serialNumber].sensorId;
//                 if (updatedParams.sensorId == 0 || updatedParams.sensorId != originalId) {
//                     updatedParams.sensorId = originalId;
//                 }
//                 groupSensors[serialNumber] = updatedParams;
//                 clearError();
//                 return true;
//             }
//         }
//         
//         setError(QString(u8"未找到要更新的传感器: %1").arg(serialNumber));
//         return false;
//     } catch (const std::exception& e) {
//         setError(QString(u8"更新传感器失败: %1").arg(e.what()));
//         return false;
//     }
// }

// 🔄 修改：使用分层存储架构的removeSensor方法
// bool SensorDataManager_1_2::removeSensor(const QString& serialNumber) {
//     if (!hasSensor(serialNumber)) {
//         setError(QString(u8"传感器不存在: %1").arg(serialNumber));
//         return false;
//     }

//     try {
//         // 🔄 修改：在所有组中查找并删除传感器
//         for (auto groupIt = groupedSensorStorage_.begin(); groupIt != groupedSensorStorage_.end(); ++groupIt) {
//             auto& groupSensors = groupIt.value();
//             if (groupSensors.contains(serialNumber)) {
//                 groupSensors.remove(serialNumber);
//                 clearError();
//                 return true;
//             }
//         }
//         
//         setError(QString(u8"未找到要删除的传感器: %1").arg(serialNumber));
//         return false;
//     } catch (const std::exception& e) {
//         setError(QString(u8"删除传感器失败: %1").arg(e.what()));
//         return false;
//     }
// }

// 🔄 修改：使用分层存储架构的hasSensor方法
// bool SensorDataManager_1_2::hasSensor(const QString& serialNumber) const {
//     try {
//         // 🔄 修改：在所有组中查找序列号
//         for (auto groupIt = groupedSensorStorage_.begin(); groupIt != groupedSensorStorage_.end(); ++groupIt) {
//             const auto& groupSensors = groupIt.value();
//             if (groupSensors.contains(serialNumber)) {
//                 return true;
//             }
//         }
//         return false;
//     } catch (const std::exception& e) {
//         setError(QString(u8"检查传感器存在性失败: %1").arg(e.what()));
//         return false;
//     }
// }

// 🔄 修改：使用分层存储架构的getAllSensorSerialNumbers方法
// QStringList SensorDataManager_1_2::getAllSensorSerialNumbers() const {
//     QStringList result;
//     try {
//         // 🔄 修改：遍历所有组收集序列号
//         for (auto groupIt = groupedSensorStorage_.begin(); groupIt != groupedSensorStorage_.end(); ++groupIt) {
//             const auto& groupSensors = groupIt.value();
//             for (auto sensorIt = groupSensors.begin(); sensorIt != groupSensors.end(); ++sensorIt) {
//                 result.append(sensorIt.key());
//             }
//         }
//     } catch (const std::exception& e) {
//         setError(QString(u8"获取传感器列表失败: %1").arg(e.what()));
//     }
//     return result;
// }

// 🔄 修改：使用分层存储架构的getAllSensors方法
QList<UI::SensorParams_1_2> SensorDataManager_1_2::getAllSensors() const {
    QList<UI::SensorParams_1_2> result;
    try {
        // 🔄 修改：遍历所有组收集传感器数据
        for (auto groupIt = groupedSensorStorage_.begin(); groupIt != groupedSensorStorage_.end(); ++groupIt) {
            const auto& groupSensors = groupIt.value();
            for (auto sensorIt = groupSensors.begin(); sensorIt != groupSensors.end(); ++sensorIt) {
                result.append(sensorIt.value());
            }
        }
    } catch (const std::exception& e) {
        setError(QString(u8"获取传感器列表失败: %1").arg(e.what()));
    }
    return result;
}

QList<UI::SensorParams_1_2> SensorDataManager_1_2::getSensorsByType(const QString& sensorType) const {
    QList<UI::SensorParams_1_2> result;
    QList<UI::SensorParams_1_2> allSensors = getAllSensors();
    for (const UI::SensorParams_1_2& params : allSensors) {
        if (params.params_model == sensorType) {  // 使用14字段新需求的型号字段
            result.append(params);
        }
    }
    return result;
}

QList<UI::SensorParams_1_2> SensorDataManager_1_2::getSensorsByUnitType(const QString& unitType) const {
    QList<UI::SensorParams_1_2> result;
    QList<UI::SensorParams_1_2> allSensors = getAllSensors();
    for (const UI::SensorParams_1_2& params : allSensors) {
        // 🆕 新需求：使用新的字段名
        if (params.meas_unit == unitType.toInt()) {
            result.append(params);
        }
    }
    return result;
}

int SensorDataManager_1_2::getSensorCount() const {
    int count = 0;
    for (auto groupIt = groupedSensorStorage_.begin(); groupIt != groupedSensorStorage_.end(); ++groupIt) {
        count += groupIt.value().size();
    }
    return count;
}

// 🆕 新增：传感器组管理接口
bool SensorDataManager_1_2::saveSensorGroup(const UI::SensorGroup_1_2& group) {
    qDebug() << QString(u8"💾 保存传感器组开始 - ID: %1, 名称: '%2', 传感器数量: %3")
                .arg(group.groupId).arg(group.groupName).arg(group.sensors.size());
    
    if (!validateSensorGroup(group)) {
        QString error = QString(u8"传感器组验证失败: %1").arg(getLastError());
        qDebug() << QString(u8"❌ %1").arg(error);
        return false;
    }

    try {
        // 🆕 新增：确保组内ID连续性
        UI::SensorGroup_1_2 updatedGroup = group;
        reassignSensorIdsInGroup(updatedGroup);
        
        groupStorage_[updatedGroup.groupId] = updatedGroup;
        if (updatedGroup.groupId >= nextGroupId_) {
            nextGroupId_ = updatedGroup.groupId + 1;
        }

        // 🔄 修改：将组内的传感器保存到分层存储中
        for (const auto& sensor : updatedGroup.sensors) {
            if (!sensor.params_sn.isEmpty()) {
                groupedSensorStorage_[updatedGroup.groupId][sensor.params_sn] = sensor;
                qDebug() << QString(u8"✅ 传感器已保存到分层存储: 序列号=%1, 组ID=%2, 组内ID=%3")
                            .arg(sensor.params_sn).arg(updatedGroup.groupId).arg(sensor.sensorId);
            }
        }

        qDebug() << QString(u8"✅ 传感器组保存完成: 组ID=%1, 组名=%2, 传感器数量=%3, 总传感器数=%4")
                    .arg(updatedGroup.groupId).arg(updatedGroup.groupName).arg(updatedGroup.sensors.size()).arg(getSensorCount());

        clearError();
        return true;
    } catch (const std::exception& e) {
        QString error = QString(u8"保存传感器组失败: %1").arg(e.what());
        qDebug() << QString(u8"❌ %1").arg(error);
        setError(error);
        return false;
    }
}

UI::SensorGroup_1_2 SensorDataManager_1_2::getSensorGroup(int groupId) const {
    if (!isValidGroupId(groupId)) {
        setError(QString(u8"无效的组ID: %1").arg(groupId));
        return UI::SensorGroup_1_2();
    }

    try {
        if (groupStorage_.contains(groupId)) {
            clearError();
            return groupStorage_[groupId];
        } else {
            setError(QString(u8"传感器组不存在: %1").arg(groupId));
            return UI::SensorGroup_1_2();
        }
    } catch (const std::exception& e) {
        setError(QString(u8"获取传感器组失败: %1").arg(e.what()));
        return UI::SensorGroup_1_2();
    }
}

bool SensorDataManager_1_2::updateSensorGroup(int groupId, const UI::SensorGroup_1_2& group) {
    if (!isValidGroupId(groupId)) {
        setError(QString(u8"无效的组ID: %1").arg(groupId));
        return false;
    }

    if (!validateSensorGroup(group)) {
        return false;
    }

    if (!hasSensorGroup(groupId)) {
        setError(QString(u8"传感器组不存在: %1").arg(groupId));
        return false;
    }

    try {
        // 🔄 修改：先清空该组的分层存储
        if (groupedSensorStorage_.contains(groupId)) {
            groupedSensorStorage_[groupId].clear();
        }

        UI::SensorGroup_1_2 updatedGroup = group;
        updatedGroup.groupId = groupId;
        groupStorage_[groupId] = updatedGroup;

        // 🔄 修改：将更新后组内的传感器保存到分层存储中
        for (const auto& sensor : updatedGroup.sensors) {
            if (!sensor.params_sn.isEmpty()) {
                groupedSensorStorage_[groupId][sensor.params_sn] = sensor;
            }
        }

        clearError();
        return true;
    } catch (const std::exception& e) {
        setError(QString(u8"更新传感器组失败: %1").arg(e.what()));
        return false;
    }
}

bool SensorDataManager_1_2::removeSensorGroup(int groupId) {
    if (!isValidGroupId(groupId)) {
        setError(QString(u8"无效的组ID: %1").arg(groupId));
        return false;
    }

    if (!hasSensorGroup(groupId)) {
        setError(QString(u8"传感器组不存在: %1").arg(groupId));
        return false;
    }

    try {
        // 🔄 修改：移除该组的分层存储
        if (groupedSensorStorage_.contains(groupId)) {
            groupedSensorStorage_.remove(groupId);
        }

        groupStorage_.remove(groupId);
        clearError();
        return true;
    } catch (const std::exception& e) {
        setError(QString(u8"删除传感器组失败: %1").arg(e.what()));
        return false;
    }
}

QList<UI::SensorGroup_1_2> SensorDataManager_1_2::getAllSensorGroups() const {
    QList<UI::SensorGroup_1_2> groups;
    try {
        for (auto it = groupStorage_.begin(); it != groupStorage_.end(); ++it) {
            groups.append(it.value());
        }
        
        std::sort(groups.begin(), groups.end(), [](const UI::SensorGroup_1_2& a, const UI::SensorGroup_1_2& b) {
            if (!a.createTime.isEmpty() && !b.createTime.isEmpty()) {
                QDateTime timeA = QDateTime::fromString(a.createTime, "yyyy-MM-dd hh:mm:ss");
                QDateTime timeB = QDateTime::fromString(b.createTime, "yyyy-MM-dd hh:mm:ss");
                if (timeA.isValid() && timeB.isValid()) {
                    return timeA < timeB;
                }
            }
            return a.groupId < b.groupId;
        });

        clearError();
        return groups;
    } catch (const std::exception& e) {
        setError(QString(u8"获取所有传感器组失败: %1").arg(e.what()));
        return groups;
    }
}

bool SensorDataManager_1_2::hasSensorGroup(int groupId) const {
    if (!isValidGroupId(groupId)) {
        return false;
    }
    try {
        return groupStorage_.contains(groupId);
    } catch (const std::exception& e) {
        setError(QString(u8"检查传感器组存在性失败: %1").arg(e.what()));
        return false;
    }
}

QList<UI::SensorParams_1_2> SensorDataManager_1_2::getSensorsByGroup(int groupId) const {
    QList<UI::SensorParams_1_2> result;
    if (!hasSensorGroup(groupId)) {
        return result;
    }
    try {
        UI::SensorGroup_1_2 group = getSensorGroup(groupId);
        return group.sensors;
    } catch (const std::exception& e) {
        setError(QString(u8"获取组内传感器失败: %1").arg(e.what()));
        return result;
    }
}

int SensorDataManager_1_2::getSensorGroupCount() const {
    try {
        return groupStorage_.size();
    } catch (const std::exception& e) {
        setError(QString(u8"获取传感器组数量失败: %1").arg(e.what()));
        return 0;
    }
}

// 数据验证
bool SensorDataManager_1_2::validateSensorParams(const UI::SensorParams_1_2& params) const {
    // 🆕 新需求：使用新的14字段结构进行验证
    if (params.params_sn.isEmpty()) {
        setError(u8"传感器序列号不能为空");
        return false;
    }

    if (params.params_model.isEmpty()) {
        setError(u8"传感器型号不能为空");
        return false;
    }

    if (params.params_precision <= 0.0) {
        setError(u8"传感器精度必须大于0");
        return false;
    }

    // 验证极性值范围
    if (params.params_polarity != -1 && params.params_polarity != 1) {
        setError(u8"传感器极性必须为-1或1");
        return false;
    }

    // 验证测量范围
    if (params.meas_range_min >= params.meas_range_max) {
        setError(u8"测量范围最小值必须小于最大值");
        return false;
    }

    // 验证输出信号范围
    if (params.output_signal_range_min >= params.output_signal_range_max) {
        setError(u8"输出信号范围最小值必须小于最大值");
        return false;
    }

    clearError();
    return true;
}

bool SensorDataManager_1_2::validateSensorGroup(const UI::SensorGroup_1_2& group) const {
    // 🆕 新增：详细的验证调试信息
    qDebug() << QString(u8"🔍 传感器组验证开始 - ID: %1, 名称: '%2', 传感器数量: %3")
                .arg(group.groupId).arg(group.groupName).arg(group.sensors.size());

    if (group.groupId <= 0) {
        QString error = QString(u8"传感器组ID必须大于0，当前ID: %1").arg(group.groupId);
        qDebug() << QString(u8"❌ 验证失败: %1").arg(error);
        setError(error);
        return false;
    }

    if (group.groupName.isEmpty()) {
        QString error = u8"传感器组名称不能为空";
        qDebug() << QString(u8"❌ 验证失败: %1").arg(error);
        setError(error);
        return false;
    }

    if (group.groupName.length() > 100) {
        QString error = u8"传感器组名称长度不能超过100个字符";
        qDebug() << QString(u8"❌ 验证失败: %1").arg(error);
        setError(error);
        return false;
    }

    for (int i = 0; i < group.sensors.size(); ++i) {
        const UI::SensorParams_1_2& sensor = group.sensors[i];
        qDebug() << QString(u8"🔍 验证组内传感器[%1]: 序列号='%2', ID=%3")
                    .arg(i).arg(sensor.params_sn).arg(sensor.sensorId);
        
        if (!validateSensorParams(sensor)) {
            QString error = QString(u8"组内传感器%1验证失败: %2").arg(i + 1).arg(getLastError());
            qDebug() << QString(u8"❌ %1").arg(error);
            setError(error);
            return false;
        }
    }

    qDebug() << QString(u8"✅ 传感器组验证成功 - ID: %1, 名称: '%2'")
                .arg(group.groupId).arg(group.groupName);
    clearError();
    return true;
}

bool SensorDataManager_1_2::validateSensorInGroup(const UI::SensorParams_1_2& sensor, const UI::SensorGroup_1_2& group) const {
    if (!validateSensorParams(sensor)) {
        return false;
    }

    for (const UI::SensorParams_1_2& existingSensor : group.sensors) {
        if (existingSensor.sensorId == sensor.sensorId) {
            continue; // 跳过自己
        }

        // 🆕 新需求：使用新的序列号字段
        if (existingSensor.params_sn == sensor.params_sn) {
            setError(QString(u8"组内传感器序列号重复: %1").arg(sensor.params_sn));
            return false;
        }

        if (existingSensor.sensorId == sensor.sensorId) {
            setError(QString(u8"组内传感器ID重复: %1").arg(sensor.sensorId));
            return false;
        }
    }

    clearError();
    return true;
}

QStringList SensorDataManager_1_2::validateAllSensors() const {
    QStringList errors;
    QList<UI::SensorParams_1_2> sensors = getAllSensors();
    for (const UI::SensorParams_1_2& params : sensors) {
        if (!validateSensorParams(params)) {
            errors.append(QString(u8"传感器 %1: %2").arg(params.params_sn).arg(getLastError()));
        }
    }
    return errors;
}

QStringList SensorDataManager_1_2::validateAllSensorGroups() const {
    QStringList errors;
    QList<UI::SensorGroup_1_2> allGroups = getAllSensorGroups();
    for (const auto& group : allGroups) {
        if (!validateSensorGroup(group)) {
            errors.append(QString(u8"传感器组 %1: %2").arg(group.groupId).arg(getLastError()));
        }
    }
    return errors;
}

// 错误处理
QString SensorDataManager_1_2::getLastError() const {
    return lastError_;
}

bool SensorDataManager_1_2::hasError() const {
    return !lastError_.isEmpty();
}

void SensorDataManager_1_2::clearError() const {
    lastError_.clear();
}

void SensorDataManager_1_2::setError(const QString& error) const {
    lastError_ = error;
    qDebug() << "SensorDataManager_1_2 Error:" << error;
}

// 数据清理
void SensorDataManager_1_2::clearAllSensors() {
    try {
        // 🔄 修改：清空分层存储
        groupedSensorStorage_.clear();
        clearError();
    } catch (const std::exception& e) {
        setError(QString(u8"清空传感器数据失败: %1").arg(e.what()));
    }
}

void SensorDataManager_1_2::clearAllSensorGroups() {
    try {
        groupStorage_.clear();
        nextGroupId_ = 1;
        clearError();
    } catch (const std::exception& e) {
        setError(QString(u8"清理所有传感器组失败: %1").arg(e.what()));
    }
}

void SensorDataManager_1_2::clearAll() {
    clearAllSensors();
    clearAllSensorGroups();
}

// 辅助方法
bool SensorDataManager_1_2::isValidSerialNumber(const QString& serialNumber) const {
    if (serialNumber.isEmpty()) {
        return false;
    }
    if (serialNumber.length() > 50) {
        return false;
    }
    for (int i = 0; i < serialNumber.length(); ++i) {
        QChar ch = serialNumber.at(i);
        if (!ch.isLetterOrNumber() && ch != '_' && ch != '-' && ch != ' ') {
            return false;
        }
    }
    return true;
}

bool SensorDataManager_1_2::isValidGroupId(int groupId) const {
    return groupId > 0;
}

void SensorDataManager_1_2::initializeStorage() {
    // 🔄 修改：初始化分层存储
    groupedSensorStorage_.clear();
    groupStorage_.clear();
    nextGroupId_ = 1;
    nextSensorId_ = 1;
}

void SensorDataManager_1_2::updateIdCounters() {
    int maxSensorId = 0;
    int maxGroupId = 0;

    // 🔧 修改：保持全局计数器用于兼容性（主要用于组内ID分配）
    // 注意：现在主要使用组内ID分配，此计数器仅用于避免ID冲突
    for (auto groupIt = groupedSensorStorage_.begin(); groupIt != groupedSensorStorage_.end(); ++groupIt) {
        const auto& groupSensors = groupIt.value();
        for (auto sensorIt = groupSensors.begin(); sensorIt != groupSensors.end(); ++sensorIt) {
            if (sensorIt.value().sensorId > maxSensorId) {
                maxSensorId = sensorIt.value().sensorId;
            }
        }
    }

    for (auto it = groupStorage_.begin(); it != groupStorage_.end(); ++it) {
        if (it.key() > maxGroupId) {
            maxGroupId = it.key();
        }
    }

    // 🆕 新增：确保全局计数器不与现有ID冲突（用于兼容性）
    nextSensorId_ = maxSensorId + 1;
    nextGroupId_ = maxGroupId + 1;
}

// 🆕 分层存储辅助方法
int SensorDataManager_1_2::findGroupIdBySerialNumber(const QString& serialNumber) const {
    // 在分层存储中查找序列号所属的组ID
    for (auto groupIt = groupedSensorStorage_.begin(); groupIt != groupedSensorStorage_.end(); ++groupIt) {
        const auto& groupSensors = groupIt.value();
        if (groupSensors.contains(serialNumber)) {
            return groupIt.key();
        }
    }
    
    // 如果在分层存储中没找到，尝试从组信息中查找
    for (auto groupIt = groupStorage_.begin(); groupIt != groupStorage_.end(); ++groupIt) {
        const UI::SensorGroup_1_2& group = groupIt.value();
        for (const auto& sensor : group.sensors) {
            if (sensor.params_sn == serialNumber) {
                return groupIt.key();
            }
        }
    }
    
    return -1; // 未找到
}

QList<UI::SensorParams_1_2> SensorDataManager_1_2::getSensorsByGroupId(int groupId) const {
    QList<UI::SensorParams_1_2> result;
    
    if (groupedSensorStorage_.contains(groupId)) {
        const auto& groupSensors = groupedSensorStorage_[groupId];
        for (auto it = groupSensors.begin(); it != groupSensors.end(); ++it) {
            result.append(it.value());
        }
    }
    
    return result;
}

// 🆕 组内ID分配方法
int SensorDataManager_1_2::assignSensorIdInGroup(const UI::SensorGroup_1_2& group) const {
    return findMaxSensorIdInGroup(group) + 1;
}

int SensorDataManager_1_2::findMaxSensorIdInGroup(const UI::SensorGroup_1_2& group) const {
    int maxId = 0;
    for (const auto& sensor : group.sensors) {
        if (sensor.sensorId > maxId) {
            maxId = sensor.sensorId;
        }
    }
    return maxId;
}

// 🆕 新增：组内ID递增管理方法（统一的连续分配策略）
int SensorDataManager_1_2::getNextSensorIdInGroup(int groupId) const {
    if (!isValidGroupId(groupId)) {
        return 1;  // 新组的第一个传感器ID
    }
    
    // 查找指定组
    if (groupStorage_.contains(groupId)) {
        const UI::SensorGroup_1_2& group = groupStorage_[groupId];
        // 🔧 修改：统一使用连续分配策略，确保组内ID从1开始连续递增
        return group.sensors.size() + 1;  // 组内下一个ID（从1开始递增）
    }
    
    return 1;  // 组不存在或为空组的第一个传感器ID
}

void SensorDataManager_1_2::reassignSensorIdsInGroup(UI::SensorGroup_1_2& group) const {
    // 重新分配组内ID，从1开始连续递增
    for (int i = 0; i < group.sensors.size(); ++i) {
        group.sensors[i].sensorId = i + 1;
    }
}

int SensorDataManager_1_2::getSensorCountInGroup(int groupId) const {
    if (!isValidGroupId(groupId)) {
        return 0;
    }
    
    if (groupStorage_.contains(groupId)) {
        return groupStorage_[groupId].sensors.size();
    }
    
    return 0;
}

// 🆕 新增：ID连续性验证方法
bool SensorDataManager_1_2::validateSensorIdSequence(const UI::SensorGroup_1_2& group) const {
    // 验证组内传感器ID是否从1开始连续递增
    for (int i = 0; i < group.sensors.size(); ++i) {
        if (group.sensors[i].sensorId != i + 1) {
            qDebug() << QString(u8"❌ 组ID=%1中传感器'%2'的ID不连续: 期望%3，实际%4")
                        .arg(group.groupId)
                        .arg(group.sensors[i].params_sn)
                        .arg(i + 1)
                        .arg(group.sensors[i].sensorId);
            return false;
        }
    }
    return true;
}

QStringList SensorDataManager_1_2::validateAllSensorIdSequences() const {
    QStringList issues;
    
    // 验证所有传感器组的ID连续性
    for (auto it = groupStorage_.begin(); it != groupStorage_.end(); ++it) {
        const UI::SensorGroup_1_2& group = it.value();
        
        if (group.sensors.isEmpty()) {
            continue;  // 跳过空组
        }
        
        if (!validateSensorIdSequence(group)) {
            issues.append(QString(u8"组ID=%1 '%2'中传感器ID不连续")
                         .arg(group.groupId).arg(group.groupName));
            
            // 详细列出不连续的传感器
            for (int i = 0; i < group.sensors.size(); ++i) {
                if (group.sensors[i].sensorId != i + 1) {
                    issues.append(QString(u8"  - 传感器'%1': 期望ID=%2，实际ID=%3")
                                 .arg(group.sensors[i].params_sn)
                                 .arg(i + 1)
                                 .arg(group.sensors[i].sensorId));
                }
            }
        }
    }
    
    return issues;
}

// 🆕 新增：序列号管理
//QString SensorDataManager_1_2::generateNextSerialNumber(const QString& prefix) const {
//    QStringList existingNumbers = getAllSensorSerialNumbers();
//    int maxNumber = 0;
//    QRegularExpression regex(QString("^%1(\\d+)$").arg(QRegularExpression::escape(prefix)));

//    for (const QString& serialNumber : existingNumbers) {
//        QRegularExpressionMatch match = regex.match(serialNumber);
//        if (match.hasMatch()) {
//            int number = match.captured(1).toInt();
//            if (number > maxNumber) {
//                maxNumber = number;
//            }
//        }
//    }
//    return QString("%1%2").arg(prefix).arg(maxNumber + 1, 3, 10, QChar('0'));
//}

// 🆕 新增：组内序列号生成
QString SensorDataManager_1_2::generateNextSerialNumberInGroup(int groupId, const QString& prefix) const {
    if (!isValidGroupId(groupId)) {
        // 如果组ID无效，返回默认的第一个序列号
        return QString("%1%2").arg(prefix).arg(1, 6, 10, QChar('0'));
    }
    
    // 获取组内所有现有的序列号
    QStringList existingNumbers;
    
    // 从分层存储中获取组内序列号
    if (groupedSensorStorage_.contains(groupId)) {
        const auto& groupSensors = groupedSensorStorage_[groupId];
        for (auto it = groupSensors.begin(); it != groupSensors.end(); ++it) {
            existingNumbers.append(it.key());  // key是序列号
        }
    }
    
    // 从组存储中获取序列号（双重保险）
    if (groupStorage_.contains(groupId)) {
        const UI::SensorGroup_1_2& group = groupStorage_[groupId];
        for (const auto& sensor : group.sensors) {
            if (!existingNumbers.contains(sensor.params_sn)) {
                existingNumbers.append(sensor.params_sn);
            }
        }
    }
    
    // 查找最大编号
    int maxNumber = 0;
    QRegularExpression regex(QString("^%1(\\d+)$").arg(QRegularExpression::escape(prefix)));
    
    for (const QString& serialNumber : existingNumbers) {
        QRegularExpressionMatch match = regex.match(serialNumber);
        if (match.hasMatch()) {
            int number = match.captured(1).toInt();
            if (number > maxNumber) {
                maxNumber = number;
            }
        }
    }
    
    // 返回下一个可用的序列号
    return QString("%1%2").arg(prefix).arg(maxNumber + 1, 6, 10, QChar('0'));
}

//bool SensorDataManager_1_2::isSerialNumberUnique(const QString& serialNumber) const {
//    return !hasSensor(serialNumber);
//}

bool SensorDataManager_1_2::isSerialNumberUniqueInGroup(const QString& serialNumber, int groupId) const {
    if (!isValidGroupId(groupId) || !groupStorage_.contains(groupId)) {
        return true;
    }
    const UI::SensorGroup_1_2& group = groupStorage_[groupId];
    for (const auto& sensor : group.sensors) {
        // 🆕 新需求：使用新的序列号字段
        if (sensor.params_sn == serialNumber) {
            return false;
        }
    }
    return true;
}

bool SensorDataManager_1_2::isSerialNumberUniqueInGroup(const QString& serialNumber, int groupId, int excludeSensorId) const {
    if (!isValidGroupId(groupId) || !groupStorage_.contains(groupId)) {
        return true;
    }
    const UI::SensorGroup_1_2& group = groupStorage_[groupId];
    for (const auto& sensor : group.sensors) {
        if (sensor.sensorId == excludeSensorId) {
            continue;
        }
        // 🆕 新需求：使用新的序列号字段
        if (sensor.params_sn == serialNumber) {
            return false;
        }
    }
    return true;
}

QStringList SensorDataManager_1_2::findDuplicateSerialNumbers() const {
    QStringList allSerialNumbers;
    QStringList duplicates;
    QSet<QString> seen;

    // 从所有组中收集序列号
    for (auto groupIt = groupedSensorStorage_.begin(); groupIt != groupedSensorStorage_.end(); ++groupIt) {
        const auto& groupSensors = groupIt.value();
        for (auto sensorIt = groupSensors.begin(); sensorIt != groupSensors.end(); ++sensorIt) {
            allSerialNumbers.append(sensorIt.key());
        }
    }

    for (const QString& serialNumber : allSerialNumbers) {
        if (seen.contains(serialNumber)) {
            if (!duplicates.contains(serialNumber)) {
                duplicates.append(serialNumber);
            }
        } else {
            seen.insert(serialNumber);
        }
    }
    return duplicates;
}

// 数据导出和统计功能（基础实现）
QVector<QStringList> SensorDataManager_1_2::exportToCSVData() const {
    QVector<QStringList> csvData;
    // 🆕 新需求：使用新14字段的列头
    QStringList headers = {"SerialNumber", "Model", "K", "B", "Precision", "Polarity", "MeasUnit", "MeasRangeMin", "MeasRangeMax", "OutputUnit", "OutputRangeMin", "OutputRangeMax", "ZeroOffset", "Enable"};
    csvData.append(headers);

    QList<UI::SensorParams_1_2> sensors = getAllSensors();
    for (const UI::SensorParams_1_2& params : sensors) {
        QStringList row;
        // 🆕 新需求：使用新14字段的数据
        row << params.params_sn << params.params_model << QString::number(params.params_k)
            << QString::number(params.params_b) << QString::number(params.params_precision) << QString::number(params.params_polarity)
            << QString::number(params.meas_unit) << QString::number(params.meas_range_min) << QString::number(params.meas_range_max)
            << QString::number(params.output_signal_unit) << QString::number(params.output_signal_range_min) << QString::number(params.output_signal_range_max)
            << QString::number(params.zero_offset) << (params.enable ? "true" : "false");
        csvData.append(row);
    }
    return csvData;
}

QVector<QStringList> SensorDataManager_1_2::exportGroupsToCSVData() const {
    QVector<QStringList> csvData;
    QStringList headers;
    headers << u8"组ID" << u8"组名称" << u8"组类型" << u8"创建时间" << u8"传感器数量" << u8"组备注";
    csvData.append(headers);

    QList<UI::SensorGroup_1_2> groups = getAllSensorGroups();
    for (const UI::SensorGroup_1_2& group : groups) {
        QStringList row;
        row << QString::number(group.groupId) << group.groupName << group.groupType 
            << group.createTime << QString::number(group.sensors.size()) << group.groupNotes;
        csvData.append(row);
    }
    return csvData;
}

// 🚫 已注释：独立JSON导出功能已废弃
//QJsonArray SensorDataManager_1_2::exportToJSONArray() const {
//    QJsonArray jsonArray;
//    QList<UI::SensorParams_1_2> sensors = getAllSensors();
//
//    for (const UI::SensorParams_1_2& params : sensors) {
//        QJsonObject sensorObj;
//        // 🆕 新需求：使用新14字段的JSON结构
//        sensorObj["zero_offset"] = params.zero_offset;
//        sensorObj["enable"] = params.enable;
//        
//        QJsonObject paramsObj;
//        paramsObj["model"] = params.params_model;
//        paramsObj["sn"] = params.params_sn;
//        paramsObj["k"] = params.params_k;
//        paramsObj["b"] = params.params_b;
//        paramsObj["precision"] = params.params_precision;
//        paramsObj["polarity"] = params.params_polarity;
//        paramsObj["meas_unit"] = params.meas_unit;
//        paramsObj["meas_range_min"] = params.meas_range_min;
//        paramsObj["meas_range_max"] = params.meas_range_max;
//        paramsObj["output_signal_unit"] = params.output_signal_unit;
//        paramsObj["output_signal_range_min"] = params.output_signal_range_min;
//        paramsObj["output_signal_range_max"] = params.output_signal_range_max;
//        
//        sensorObj["params"] = paramsObj;
//        jsonArray.append(sensorObj);
//    }
//    return jsonArray;
//}

QMap<QString, int> SensorDataManager_1_2::getSensorTypeStatistics() const {
    QMap<QString, int> statistics;
    QList<UI::SensorParams_1_2> sensors = getAllSensors();
    for (const UI::SensorParams_1_2& params : sensors) {
        // 🆕 新需求：使用型号字段作为类型统计
        statistics[params.params_model]++;
    }
    return statistics;
}

QMap<QString, int> SensorDataManager_1_2::getUnitTypeStatistics() const {
    QMap<QString, int> statistics;
    QList<UI::SensorParams_1_2> sensors = getAllSensors();
    for (const UI::SensorParams_1_2& params : sensors) {
        // 🆕 新需求：使用测量单位字段
        statistics[QString::number(params.meas_unit)]++;
    }
    return statistics;
}

QMap<QString, int> SensorDataManager_1_2::getPolarityStatistics() const {
    QMap<QString, int> statistics;
    QList<UI::SensorParams_1_2> sensors = getAllSensors();
    for (const UI::SensorParams_1_2& params : sensors) {
        // 🆕 新需求：使用新的极性字段
        statistics[QString::number(params.params_polarity)]++;
    }
    return statistics;
}

QStringList SensorDataManager_1_2::getUsedSensorTypes() const {
    QStringList types;
    QMap<QString, int> stats = getSensorTypeStatistics();
    for (auto it = stats.begin(); it != stats.end(); ++it) {
        types.append(it.key());
    }
    return types;
}

QStringList SensorDataManager_1_2::getUsedUnitTypes() const {
    QStringList types;
    QMap<QString, int> stats = getUnitTypeStatistics();
    for (auto it = stats.begin(); it != stats.end(); ++it) {
        types.append(it.key());
    }
    return types;
}

// 🆕 新增：带组ID的传感器详细参数批量获取接口
QStringList SensorDataManager_1_2::getAllSensorSerialNumbersInGroup(int groupId) const {
    QStringList serialNumbers;
    if (!isValidGroupId(groupId)) {
        return serialNumbers;
    }
    
    auto groupIt = groupedSensorStorage_.find(groupId);
    if (groupIt != groupedSensorStorage_.end()) {
        const auto& groupSensors = groupIt.value();
        for (auto it = groupSensors.begin(); it != groupSensors.end(); ++it) {
            serialNumbers.append(it.key());
        }
    }
    return serialNumbers;
}

QList<UI::SensorParams_1_2> SensorDataManager_1_2::getAllSensorDetailedParamsInGroup(int groupId) const {
    QList<UI::SensorParams_1_2> sensors;
    if (!isValidGroupId(groupId)) {
        return sensors;
    }
    
    auto groupIt = groupedSensorStorage_.find(groupId);
    if (groupIt != groupedSensorStorage_.end()) {
        const auto& groupSensors = groupIt.value();
        for (auto it = groupSensors.begin(); it != groupSensors.end(); ++it) {
            sensors.append(it.value());
        }
    }
    return sensors;
}

// 🆕 新增：带组ID的基础传感器操作接口
UI::SensorParams_1_2 SensorDataManager_1_2::getSensorInGroup(int groupId, const QString& serialNumber) const {
    if (!isValidGroupId(groupId)) {
        return UI::SensorParams_1_2();
    }
    
    auto groupIt = groupedSensorStorage_.find(groupId);
    if (groupIt != groupedSensorStorage_.end()) {
        const auto& groupSensors = groupIt.value();
        auto sensorIt = groupSensors.find(serialNumber);
        if (sensorIt != groupSensors.end()) {
            return sensorIt.value();
        }
    }
    return UI::SensorParams_1_2();
}

bool SensorDataManager_1_2::updateSensorInGroup(int groupId, const QString& serialNumber, const UI::SensorParams_1_2& params) {
    if (!validateSensorParams(params)) {
        return false;
    }
    
    if (!isValidGroupId(groupId)) {
        setError(QString(u8"无效的组ID: %1").arg(groupId));
        return false;
    }
    
    try {
        auto groupIt = groupedSensorStorage_.find(groupId);
        if (groupIt != groupedSensorStorage_.end()) {
            auto& groupSensors = groupIt.value();
            auto sensorIt = groupSensors.find(serialNumber);
            if (sensorIt != groupSensors.end()) {
                // 🆕 新增：保持原有的组内ID
                UI::SensorParams_1_2 updatedParams = params;
                int originalGroupId = sensorIt.value().sensorId;  // 保存原有组内ID
                updatedParams.sensorId = originalGroupId;  // 保持组内ID不变
                
                sensorIt.value() = updatedParams;
                
                // 🆕 新增：同步更新组存储中的传感器数据
                if (groupStorage_.contains(groupId)) {
                    UI::SensorGroup_1_2& group = groupStorage_[groupId];
                    for (int i = 0; i < group.sensors.size(); ++i) {
                        if (group.sensors[i].params_sn == serialNumber) {
                            group.sensors[i] = updatedParams;
                            break;
                        }
                    }
                }
                
                clearError();
                
                // 🆕 新增：发出数据变化信号，通知详细信息面板更新
                // 🔧 修复：延迟发出信号，确保数据完全更新后再通知
                QTimer::singleShot(100, [this, serialNumber, groupId]() {
                    // 🆕 新增：检查对象是否仍然有效
                    if (!this) {
                        qWarning() << "❌ [SensorDataManager] 延迟发送信号时this指针无效，跳过";
                        return;
                    }
                    
                    // 🆕 新增：检查对象是否仍然有效（通过父对象检查）
                    try {
                        if (!this->parent() && !this->objectName().isNull()) {
                            qWarning() << "❌ [SensorDataManager] 延迟发送信号时对象已无效，跳过";
                            return;
                        }
                    } catch (...) {
                        qWarning() << "❌ [SensorDataManager] 延迟发送信号时对象状态检查失败，跳过";
                        return;
                    }
                    
                    // 🆕 新增：安全地发送信号
                    try {
                        emit sensorDataChanged(serialNumber, "update");
                        emit sensorGroupDataChanged(groupId, "update");
                        qDebug() << "✅ [SensorDataManager] 延迟信号发送成功: 传感器=" << serialNumber << "组ID=" << groupId;
                    } catch (const std::exception& e) {
                        qWarning() << "❌ [SensorDataManager] 延迟发送信号时发生异常:" << e.what();
                    } catch (...) {
                        qWarning() << "❌ [SensorDataManager] 延迟发送信号时发生未知异常";
                    }
                });
                
                return true;
            }
        }
        
        setError(QString(u8"在组 %1 中未找到序列号为 %2 的传感器").arg(groupId).arg(serialNumber));
        return false;
        
    } catch (const std::exception& e) {
        setError(QString(u8"更新组内传感器失败: %1").arg(e.what()));
        return false;
    } catch (...) {
        setError(QString(u8"更新组内传感器时发生未知异常"));
        return false;
    }
}

bool SensorDataManager_1_2::removeSensorInGroup(int groupId, const QString& serialNumber) {
    if (!isValidGroupId(groupId)) {
        setError(QString(u8"无效的组ID: %1").arg(groupId));
        return false;
    }
    
    try {
        bool removedFromGroupedStorage = false;
        bool removedFromGroupStorage = false;
        
        // 🔄 修复：从分层存储中删除传感器
        auto groupIt = groupedSensorStorage_.find(groupId);
        if (groupIt != groupedSensorStorage_.end()) {
            auto& groupSensors = groupIt.value();
            auto sensorIt = groupSensors.find(serialNumber);
            if (sensorIt != groupSensors.end()) {
                groupSensors.erase(sensorIt);
                removedFromGroupedStorage = true;
                
                // 如果该组没有传感器了，删除该组的条目
                if (groupSensors.isEmpty()) {
                    groupedSensorStorage_.remove(groupId);
                }
            }
        }
        
        // 🔄 修复：从组存储中删除传感器
        if (groupStorage_.contains(groupId)) {
            UI::SensorGroup_1_2& group = groupStorage_[groupId];
            for (int i = group.sensors.size() - 1; i >= 0; --i) {
                if (group.sensors[i].params_sn == serialNumber) {
                    group.sensors.removeAt(i);
                    removedFromGroupStorage = true;
                    break;
                }
            }
            
            // 🆕 新增：删除后重新分配组内ID，确保连续递增
            if (removedFromGroupStorage) {
                reassignSensorIdsInGroup(group);
                groupStorage_[groupId] = group;
                
                qDebug() << QString(u8"✅ 删除传感器后重新分配组内ID: 组ID=%1, 剩余传感器数=%2")
                            .arg(groupId).arg(group.sensors.size());
            }
        }
        
        // 检查删除结果一致性
        if (removedFromGroupedStorage != removedFromGroupStorage) {
            QString warning = QString(u8"⚠️ 数据一致性警告: 传感器 %1 在存储结构中的删除状态不一致 (分层存储:%2, 组存储:%3)")
                             .arg(serialNumber)
                             .arg(removedFromGroupedStorage ? "已删除" : "未找到")
                             .arg(removedFromGroupStorage ? "已删除" : "未找到");
            qWarning() << warning;
        }
        
        if (removedFromGroupedStorage || removedFromGroupStorage) {
            clearError();
            
            // 🆕 新增：发出数据变化信号，通知详细信息面板更新
            emit sensorDataChanged(serialNumber, "delete");
            emit sensorGroupDataChanged(groupId, "update");
            
            // notifyDataChanged(); // 暂时注释掉，待实现
            return true;
        } else {
            setError(QString(u8"在组 %1 中未找到序列号为 %2 的传感器").arg(groupId).arg(serialNumber));
            return false;
        }
        
    } catch (const std::exception& e) {
        setError(QString(u8"删除组内传感器失败: %1").arg(e.what()));
        return false;
    }
}

bool SensorDataManager_1_2::hasSensorInGroup(int groupId, const QString& serialNumber) const {
    if (!isValidGroupId(groupId)) {
        return false;
    }
    
    auto groupIt = groupedSensorStorage_.find(groupId);
    if (groupIt != groupedSensorStorage_.end()) {
        const auto& groupSensors = groupIt.value();
        return groupSensors.contains(serialNumber);
    }
    return false;
}

// 🆕 数据一致性验证方法实现
bool SensorDataManager_1_2::validateStorageConsistency() const {
    QStringList inconsistencies = detectStorageInconsistencies();
    if (!inconsistencies.isEmpty()) {
        qWarning() << QString(u8"🚨 检测到存储结构不一致问题:");
        for (const QString& issue : inconsistencies) {
            qWarning() << QString(u8"  - %1").arg(issue);
        }
        return false;
    }
    return true;
}

QStringList SensorDataManager_1_2::detectStorageInconsistencies() const {
    QStringList issues;
    
    // 检查组存储中的传感器是否在分层存储中存在
    for (auto groupIt = groupStorage_.begin(); groupIt != groupStorage_.end(); ++groupIt) {
        int groupId = groupIt.key();
        const UI::SensorGroup_1_2& group = groupIt.value();
        
        for (const auto& sensor : group.sensors) {
            if (!sensor.params_sn.isEmpty()) {
                // 检查分层存储中是否存在对应的传感器
                if (!groupedSensorStorage_.contains(groupId) || 
                    !groupedSensorStorage_[groupId].contains(sensor.params_sn)) {
                    issues.append(QString(u8"组存储中的传感器在分层存储中缺失: 组ID=%1, 序列号=%2")
                                 .arg(groupId).arg(sensor.params_sn));
                }
            }
        }
    }
    
    // 检查分层存储中的传感器是否在组存储中存在
    for (auto groupIt = groupedSensorStorage_.begin(); groupIt != groupedSensorStorage_.end(); ++groupIt) {
        int groupId = groupIt.key();
        const auto& groupSensors = groupIt.value();
        
        for (auto sensorIt = groupSensors.begin(); sensorIt != groupSensors.end(); ++sensorIt) {
            const QString& serialNumber = sensorIt.key();
            
            // 检查组存储中是否存在对应的传感器
            bool foundInGroupStorage = false;
            if (groupStorage_.contains(groupId)) {
                const UI::SensorGroup_1_2& group = groupStorage_[groupId];
                for (const auto& sensor : group.sensors) {
                    if (sensor.params_sn == serialNumber) {
                        foundInGroupStorage = true;
                        break;
                    }
                }
            }
            
            if (!foundInGroupStorage) {
                issues.append(QString(u8"分层存储中的传感器在组存储中缺失: 组ID=%1, 序列号=%2")
                             .arg(groupId).arg(serialNumber));
            }
        }
    }
    
    return issues;
}

void SensorDataManager_1_2::syncGroupStorageFromGroupedStorage() {
    qDebug() << QString(u8"🔄 开始同步组存储数据从分层存储...");
    
    // 清除组存储中所有传感器数据
    for (auto& group : groupStorage_) {
        group.sensors.clear();
    }
    
    // 从分层存储重建组存储
    for (auto groupIt = groupedSensorStorage_.begin(); groupIt != groupedSensorStorage_.end(); ++groupIt) {
        int groupId = groupIt.key();
        const auto& groupSensors = groupIt.value();
        
        // 确保组存储中存在对应的组
        if (!groupStorage_.contains(groupId)) {
            UI::SensorGroup_1_2 newGroup;
            newGroup.groupId = groupId;
            newGroup.groupName = QString(u8"未命名组_%1").arg(groupId);
            newGroup.groupType = QString(u8"载荷");
            newGroup.createTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
            newGroup.groupNotes = QString(u8"数据同步重建");
            groupStorage_[groupId] = newGroup;
        }
        
        // 将分层存储中的传感器添加到组存储
        for (auto sensorIt = groupSensors.begin(); sensorIt != groupSensors.end(); ++sensorIt) {
            groupStorage_[groupId].sensors.append(sensorIt.value());
        }
        
        // 重新分配组内ID
        for (int i = 0; i < groupStorage_[groupId].sensors.size(); ++i) {
            groupStorage_[groupId].sensors[i].sensorId = i + 1;
        }
        
        qDebug() << QString(u8"✅ 同步组 %1: %2 个传感器").arg(groupId).arg(groupSensors.size());
    }
    
    qDebug() << QString(u8"🎉 组存储数据同步完成");
}
