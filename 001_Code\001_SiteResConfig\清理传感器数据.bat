@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🧹 清理传感器数据 - 移除多余数据
echo ========================================
echo.

echo 📋 问题分析:
echo 当前Excel导出结果显示了多余的传感器数据：
echo - 前6行：测试数据（已删除自动创建）
echo - 后续行：界面操作产生的数据（组序号3640、3641、6490、6491等）
echo.

echo 🔍 数据来源:
echo 1. 测试数据：createTestSensorGroups() 方法创建（已禁用）
echo 2. 界面数据：用户在硬件树中添加传感器时自动创建
echo 3. 项目数据：从项目文件加载的传感器数据
echo.

echo 🎯 解决方案:
echo.
echo 方案1: 清理SensorDataManager中的所有数据
echo - 调用 sensorDataManager_-^>clearAllSensors()
echo - 调用 sensorDataManager_-^>clearAllSensorGroups()
echo - 重新启动应用程序
echo.
echo 方案2: 只保留需要的传感器组
echo - 手动删除不需要的传感器组
echo - 保留有效的传感器数据
echo.
echo 方案3: 修改数据过滤逻辑
echo - 在导出时过滤掉无效的传感器组
echo - 只导出有效的传感器数据
echo.

echo 🔧 实现步骤:
echo.
echo 步骤1: 在主窗口初始化时清理数据
echo - 修改 initializeSensorDataManager() 方法
echo - 添加数据清理调用
echo.
echo 步骤2: 验证数据来源
echo - 检查界面硬件树中的传感器节点
echo - 确认哪些数据是有效的
echo.
echo 步骤3: 重新导出验证
echo - 清理数据后重新导出
echo - 确认只有有效数据被导出
echo.

echo 💡 建议操作:
echo.
echo 1. 立即清理: 在 initializeSensorDataManager() 中添加清理代码
echo 2. 数据验证: 检查硬件树中的传感器组是否有效
echo 3. 重新测试: 清理后重新导出传感器详细配置
echo.

echo 📝 代码修改建议:
echo.
echo 在 MainWindow_Qt_Simple.cpp 的 initializeSensorDataManager() 中添加:
echo.
echo void CMyMainWindow::initializeSensorDataManager() {
echo     // 清理所有现有数据
echo     if (sensorDataManager_) {
echo         sensorDataManager_-^>clearAllSensors();
echo         sensorDataManager_-^>clearAllSensorGroups();
echo         AddLogEntry("INFO", u8"已清理所有传感器数据");
echo     }
echo }
echo.

echo ⚠️ 注意事项:
echo - 清理操作会删除所有传感器数据
echo - 确保重要数据已备份
echo - 清理后需要重新添加有效的传感器
echo.

echo 🚀 下一步:
echo 1. 修改 initializeSensorDataManager() 方法
echo 2. 重新启动应用程序
echo 3. 验证传感器数据已清理
echo 4. 重新导出传感器详细配置
echo 5. 确认只有有效数据被导出
echo.

pause
