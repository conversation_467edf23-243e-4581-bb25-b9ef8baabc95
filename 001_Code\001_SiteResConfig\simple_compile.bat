@echo off
echo Looking for Qt installation...

REM Try common Qt paths
if exist "C:\Qt\5.14.2\mingw73_32\bin\qmake.exe" (
    set QT_BIN=C:\Qt\5.14.2\mingw73_32\bin
    goto found_qt
)

if exist "C:\Qt\Qt5.14.2\5.14.2\mingw73_32\bin\qmake.exe" (
    set QT_BIN=C:\Qt\Qt5.14.2\5.14.2\mingw73_32\bin
    goto found_qt
)

if exist "D:\Qt\5.14.2\mingw73_32\bin\qmake.exe" (
    set QT_BIN=D:\Qt\5.14.2\mingw73_32\bin
    goto found_qt
)

echo Qt not found. Please use Qt Creator to compile.
pause
exit /b 1

:found_qt
echo Found Qt at: %QT_BIN%
set PATH=%QT_BIN%;%PATH%

echo Entering build directory...
cd /d "%~dp0\..\build-SiteResConfig_Simple-Desktop_Qt_5_14_2_MinGW_32_bit-Debug"

echo Compiling...
mingw32-make

if %ERRORLEVEL% EQU 0 (
    echo Compilation successful!
    cd debug
    SiteResConfig.exe
) else (
    echo Compilation failed!
    pause
)
