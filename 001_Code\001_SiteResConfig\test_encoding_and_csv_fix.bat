@echo off
chcp 65001 >nul
echo ========================================
echo  打印乱码和CSV存储问题修复测试
echo ========================================

echo.
echo 🔧 修复内容：
echo.
echo 📝 1. 打印信息乱码修复：
echo    ├─ 添加Windows控制台UTF-8编码支持
echo    ├─ 设置控制台代码页为65001 (UTF-8)
echo    ├─ 启用虚拟终端处理支持ANSI转义序列
echo    └─ 所有qDebug输出使用toLocal8Bit().data()转换
echo.
echo 📄 2. CSV存储问题修复：
echo    ├─ 放宽节点保存条件，避免过度筛选
echo    ├─ 只排除明确的根配置节点
echo    ├─ 保存所有有名称或内容的节点
echo    └─ 增强调试信息，便于问题诊断
echo.
echo 🎯 3. 调试信息优化：
echo    ├─ 详细的节点保存检查信息
echo    ├─ 中文调试信息正确显示
echo    ├─ 保存成功/跳过的明确标识
echo    └─ 完整的节点属性输出
echo.

echo 🔨 开始编译...
cd SiteResConfig
qmake SiteResConfig_Simple.pro
mingw32-make clean
mingw32-make

if exist "debug\SiteResConfig.exe" (
    echo.
    echo ✅ 编译成功！
    echo.
    echo 📋 测试步骤：
    echo.
    echo 1️⃣ 控制台输出测试：
    echo    ├─ 启动应用程序
    echo    ├─ 观察控制台中的中文调试信息
    echo    ├─ 验证中文字符是否正确显示
    echo    └─ 检查是否还有乱码问题
    echo.
    echo 2️⃣ CSV保存测试：
    echo    ├─ 创建新的实验工程
    echo    ├─ 添加硬件设备（作动器、传感器等）
    echo    ├─ 保存工程为CSV格式
    echo    ├─ 检查保存的数据完整性
    echo    └─ 多次保存验证数据一致性
    echo.
    echo 3️⃣ 调试信息验证：
    echo    ├─ 观察保存过程中的调试输出
    echo    ├─ 验证节点保存检查信息
    echo    ├─ 确认保存/跳过的逻辑正确
    echo    └─ 检查中文调试信息显示
    echo.
    
    echo 🚀 启动应用程序进行测试...
    echo.
    start debug\SiteResConfig.exe
    
    echo 💡 测试提示：
    echo ├─ 应用程序已在后台启动
    echo ├─ 请按照上述步骤进行测试
    echo ├─ 观察控制台输出的中文显示效果
    echo └─ 验证CSV保存功能的完整性
    echo.
    
) else (
    echo.
    echo ❌ 编译失败！
    echo 请检查编译错误信息并修复。
    echo.
)

echo 📊 预期修复效果：
echo.
echo ✅ 打印信息修复：
echo    ├─ 控制台中文字符正确显示
echo    ├─ 调试信息清晰可读
echo    ├─ 无乱码或问号字符
echo    └─ 支持彩色输出（如果终端支持）
echo.
echo ✅ CSV存储修复：
echo    ├─ 第一次保存：完整数据
echo    ├─ 后续保存：数据一致性
echo    ├─ 节点信息：完整保留
echo    └─ 调试输出：详细诊断信息
echo.

pause
