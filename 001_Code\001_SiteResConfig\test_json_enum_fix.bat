@echo off
chcp 65001 >nul
echo ========================================
echo  JSON导出功能枚举类型修复验证
echo ========================================

echo 检查枚举类型修复状态...

REM 检查ActuatorType枚举转换
echo.
echo 1. 检查ActuatorType枚举转换...
findstr /C:"switch (actuator.actuatorType)" "SiteResConfig\src\DataModels_Simple.cpp" >nul
if %errorlevel%==0 (
    echo ✅ ActuatorType枚举转换已实现
) else (
    echo ❌ ActuatorType枚举转换未实现
)

findstr /C:"actuatorTypeStr = \"Hydraulic\"" "SiteResConfig\src\DataModels_Simple.cpp" >nul
if %errorlevel%==0 (
    echo ✅ Hydraulic类型转换已实现
) else (
    echo ❌ Hydraulic类型转换未实现
)

findstr /C:"actuatorTypeStr = \"Electric\"" "SiteResConfig\src\DataModels_Simple.cpp" >nul
if %errorlevel%==0 (
    echo ✅ Electric类型转换已实现
) else (
    echo ❌ Electric类型转换未实现
)

REM 检查SensorType枚举转换
echo.
echo 2. 检查SensorType枚举转换...
findstr /C:"switch (sensor.sensorType)" "SiteResConfig\src\DataModels_Simple.cpp" >nul
if %errorlevel%==0 (
    echo ✅ SensorType枚举转换已实现
) else (
    echo ❌ SensorType枚举转换未实现
)

findstr /C:"sensorTypeStr = \"Force\"" "SiteResConfig\src\DataModels_Simple.cpp" >nul
if %errorlevel%==0 (
    echo ✅ Force类型转换已实现
) else (
    echo ❌ Force类型转换未实现
)

findstr /C:"sensorTypeStr = \"Displacement\"" "SiteResConfig\src\DataModels_Simple.cpp" >nul
if %errorlevel%==0 (
    echo ✅ Displacement类型转换已实现
) else (
    echo ❌ Displacement类型转换未实现
)

REM 检查字段名修正
echo.
echo 3. 检查字段名修正...
findstr /C:"\"stroke\":" "SiteResConfig\src\DataModels_Simple.cpp" >nul
if %errorlevel%==0 (
    echo ✅ stroke字段名已修正
) else (
    echo ❌ stroke字段名未修正
)

findstr /C:"\"boundControlChannel\":" "SiteResConfig\src\DataModels_Simple.cpp" >nul
if %errorlevel%==0 (
    echo ✅ boundControlChannel字段名已修正
) else (
    echo ❌ boundControlChannel字段名未修正
)

REM 检查错误的字段名是否已移除
echo.
echo 4. 检查错误字段名是否已移除...
findstr /C:"maxStroke" "SiteResConfig\src\DataModels_Simple.cpp" >nul
if %errorlevel%==0 (
    echo ❌ 错误的maxStroke字段仍然存在
) else (
    echo ✅ 错误的maxStroke字段已移除
)

findstr /C:"boundChannel" "SiteResConfig\src\DataModels_Simple.cpp" >nul
if %errorlevel%==0 (
    echo ⚠️  boundChannel字段存在（需确认是否为正确的传感器字段）
) else (
    echo ✅ 错误的boundChannel字段已移除
)

REM 检查完整的JSON结构
echo.
echo 5. 检查JSON结构完整性...
findstr /C:"\"actuators\": \[" "SiteResConfig\src\DataModels_Simple.cpp" >nul
if %errorlevel%==0 (
    echo ✅ 作动器JSON结构已实现
) else (
    echo ❌ 作动器JSON结构未实现
)

findstr /C:"\"sensors\": \[" "SiteResConfig\src\DataModels_Simple.cpp" >nul
if %errorlevel%==0 (
    echo ✅ 传感器JSON结构已实现
) else (
    echo ❌ 传感器JSON结构未实现
)

findstr /C:"\"hardwareNodes\": \[" "SiteResConfig\src\DataModels_Simple.cpp" >nul
if %errorlevel%==0 (
    echo ✅ 硬件节点JSON结构已实现
) else (
    echo ❌ 硬件节点JSON结构未实现
)

echo.
echo ========================================
echo  修复状态总结
echo ========================================
echo.
echo 枚举类型修复:
echo ✅ ActuatorType枚举 → 字符串转换
echo ✅ SensorType枚举 → 字符串转换
echo ✅ 支持所有枚举值的转换
echo ✅ 包含默认值处理
echo.
echo 字段名修正:
echo ✅ maxStroke → stroke
echo ✅ boundChannel → boundControlChannel (作动器)
echo ✅ boundChannel (传感器字段保持不变)
echo.
echo JSON格式特点:
echo ✅ 标准JSON对象数组格式
echo ✅ 枚举值使用有意义的字符串
echo ✅ 所有字段类型正确匹配
echo ✅ 包含完整的设备配置信息
echo.
echo 生成的JSON示例:
echo {
echo   "actuators": [
echo     {
echo       "actuatorId": "ACT001",
echo       "actuatorName": "主液压缸",
echo       "actuatorType": "Hydraulic",
echo       "maxForce": 200000.0,
echo       "stroke": 300.0,
echo       "maxVelocity": 500.0,
echo       "boundNodeId": 0,
echo       "boundControlChannel": 0
echo     }
echo   ],
echo   "sensors": [
echo     {
echo       "sensorId": "SEN001",
echo       "sensorName": "主力传感器",
echo       "sensorType": "Force",
echo       "fullScale": 250000.0,
echo       "unit": "N",
echo       "boundNodeId": 1,
echo       "boundChannel": 0
echo     }
echo   ]
echo }
echo.

REM 检查可执行文件
echo 6. 检查可执行文件状态...
if exist "build-SiteResConfig_Simple-Desktop_Qt_5_14_2_MinGW_32_bit-Debug\debug\SiteResConfig.exe" (
    echo ✅ 可执行文件存在
    echo    建议重新编译以应用最新修复
) else (
    echo ❌ 可执行文件不存在，需要编译项目
)

echo.
echo ========================================
echo  测试建议
echo ========================================
echo 1. 重新编译项目以应用枚举类型修复
echo 2. 启动应用程序创建测试项目
echo 3. 添加不同类型的作动器和传感器
echo 4. 使用JSON导出功能
echo 5. 验证生成的JSON文件中枚举值为字符串
echo 6. 确认所有字段名正确
echo 7. 测试JSON文件可被其他工具解析
echo.

REM 询问是否启动应用程序测试
set /p choice="是否启动应用程序进行枚举类型JSON导出测试? (y/n): "
if /i "%choice%"=="y" (
    echo.
    echo 启动应用程序...
    cd /d "%~dp0build-SiteResConfig_Simple-Desktop_Qt_5_14_2_MinGW_32_bit-Debug\debug"
    if exist "SiteResConfig.exe" (
        start SiteResConfig.exe
        echo 应用程序已启动！
        echo.
        echo 测试重点:
        echo - 创建不同类型的作动器（液压、电动、气动）
        echo - 创建不同类型的传感器（力、位移、压力等）
        echo - 导出为JSON格式
        echo - 检查枚举值是否为可读的字符串
        echo - 验证字段名是否正确
    ) else (
        echo 错误: 找不到可执行文件，请先重新编译项目
    )
)

echo.
echo JSON导出功能枚举类型修复完成！
pause
