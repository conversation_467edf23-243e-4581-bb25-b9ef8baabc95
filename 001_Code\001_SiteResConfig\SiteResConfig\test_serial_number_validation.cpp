/**
 * @file test_serial_number_validation.cpp
 * @brief 测试序列号验证功能修复
 * @details 验证序列号验证是否支持中文字符
 * <AUTHOR> Assistant
 * @date 2025-08-14
 * @version 1.0.0
 */

#include <iostream>
#include <cassert>
#include "ActuatorDataManager.h"
#include "DataModels_Fixed.h"

using namespace DataModels;

/**
 * @brief 测试序列号验证功能
 */
void testSerialNumberValidation() {
    std::cout << "=== 测试序列号验证功能 ===" << std::endl;
    
    // 创建测试项目和DataManager
    TestProject project;
    ActuatorDataManager manager(&project);
    
    // 测试用例：支持的序列号格式
    std::vector<std::pair<QString, bool>> testCases = {
        // 中文格式（应该通过）
        {"作动器_000001", true},
        {"传感器_000001", true},
        {"液压缸001", true},
        {"位移传感器A", true},
        
        // 英文格式（应该通过）
        {"ACT001", true},
        {"SENSOR_01", true},
        {"HYD-CYL-001", true},
        {"Actuator 001", true},
        
        // 混合格式（应该通过）
        {"作动器ACT_001", true},
        {"Sensor传感器_01", true},
        {"液压缸HYD-001", true},
        {"ACT 作动器 001", true},
        
        // 不支持的格式（应该失败）
        {"", false},                    // 空字符串
        {"ACT@001", false},             // 包含@符号
        {"作动器#001", false},          // 包含#符号
        {"ACT\n001", false},            // 包含换行符
        
        // 边界测试
        {"A", true},                    // 单字符
        {"作", true},                   // 单个中文字符
        {"123", true},                  // 纯数字
        {"_-_", true},                  // 特殊字符组合
    };
    
    std::cout << "开始测试序列号验证..." << std::endl;
    
    for (const auto& testCase : testCases) {
        QString serialNumber = testCase.first;
        bool expectedResult = testCase.second;
        
        // 创建测试作动器参数
        UI::ActuatorParams params;
        params.serialNumber = serialNumber;
        params.actuatorName = "测试作动器";
        params.actuatorType = "Hydraulic";
        params.type = "单出杆";
        
        // 尝试添加作动器
        bool actualResult = manager.addActuator(params);
        
        // 验证结果
        if (actualResult == expectedResult) {
            std::cout << "✓ 序列号 \"" << serialNumber.toStdString() 
                      << "\" 验证结果正确: " << (actualResult ? "通过" : "拒绝") << std::endl;
        } else {
            std::cout << "❌ 序列号 \"" << serialNumber.toStdString() 
                      << "\" 验证结果错误: 期望" << (expectedResult ? "通过" : "拒绝")
                      << "，实际" << (actualResult ? "通过" : "拒绝") << std::endl;
            if (!actualResult) {
                std::cout << "   错误信息: " << manager.getLastError().toStdString() << std::endl;
            }
            assert(false && "序列号验证结果不符合预期");
        }
        
        // 清理：如果添加成功，删除作动器
        if (actualResult) {
            manager.removeActuator(serialNumber);
        }
    }
    
    std::cout << "=== 序列号验证功能测试完成 ===" << std::endl << std::endl;
}

/**
 * @brief 测试作动器组保存功能
 */
void testActuatorGroupSave() {
    std::cout << "=== 测试作动器组保存功能 ===" << std::endl;
    
    // 创建测试项目和DataManager
    TestProject project;
    ActuatorDataManager manager(&project);
    
    // 创建包含中文序列号的作动器
    UI::ActuatorParams actuator1;
    actuator1.serialNumber = "作动器_000001";
    actuator1.actuatorName = "测试作动器1";
    actuator1.actuatorType = "Hydraulic";
    actuator1.type = "单出杆";
    
    UI::ActuatorParams actuator2;
    actuator2.serialNumber = "作动器_000002";
    actuator2.actuatorName = "测试作动器2";
    actuator2.actuatorType = "Electric";
    actuator2.type = "双出杆";
    
    // 添加作动器到DataManager
    bool result1 = manager.addActuator(actuator1);
    bool result2 = manager.addActuator(actuator2);
    
    assert(result1 && "添加第一个作动器应该成功");
    assert(result2 && "添加第二个作动器应该成功");
    std::cout << "✓ 成功添加包含中文序列号的作动器" << std::endl;
    
    // 获取带有正确ID的作动器参数
    UI::ActuatorParams actuatorWithId1 = manager.getActuator("作动器_000001");
    UI::ActuatorParams actuatorWithId2 = manager.getActuator("作动器_000002");
    
    assert(!actuatorWithId1.serialNumber.isEmpty() && "应该能获取第一个作动器");
    assert(!actuatorWithId2.serialNumber.isEmpty() && "应该能获取第二个作动器");
    std::cout << "✓ 成功获取包含中文序列号的作动器参数" << std::endl;
    
    // 创建作动器组
    UI::ActuatorGroup group;
    group.groupId = 1;
    group.groupName = "测试组";
    group.groupType = "液压";
    group.createTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
    group.groupNotes = "包含中文序列号的测试组";
    group.actuators.append(actuatorWithId1);
    group.actuators.append(actuatorWithId2);
    
    // 保存作动器组
    bool saveResult = manager.saveActuatorGroup(group);
    
    if (saveResult) {
        std::cout << "✅ 作动器组保存成功！" << std::endl;
        std::cout << "✓ 组ID: " << group.groupId << std::endl;
        std::cout << "✓ 组名: " << group.groupName.toStdString() << std::endl;
        std::cout << "✓ 作动器数量: " << group.actuators.size() << std::endl;
        std::cout << "✓ 作动器1序列号: " << group.actuators[0].serialNumber.toStdString() << std::endl;
        std::cout << "✓ 作动器2序列号: " << group.actuators[1].serialNumber.toStdString() << std::endl;
    } else {
        std::cout << "❌ 作动器组保存失败: " << manager.getLastError().toStdString() << std::endl;
        assert(false && "作动器组保存应该成功");
    }
    
    std::cout << "=== 作动器组保存功能测试完成 ===" << std::endl << std::endl;
}

/**
 * @brief 主测试函数
 */
int main() {
    std::cout << "开始序列号验证修复测试..." << std::endl << std::endl;
    
    try {
        testSerialNumberValidation();
        testActuatorGroupSave();
        
        std::cout << "🎉 所有测试通过！序列号验证修复成功！" << std::endl;
        std::cout << std::endl;
        std::cout << "✅ 修复确认:" << std::endl;
        std::cout << "- 支持中文字符序列号（如：作动器_000001）" << std::endl;
        std::cout << "- 支持英文字符序列号（如：ACT001）" << std::endl;
        std::cout << "- 支持混合字符序列号（如：作动器ACT_001）" << std::endl;
        std::cout << "- 正确拒绝非法字符序列号" << std::endl;
        std::cout << "- 作动器组保存功能正常" << std::endl;
        std::cout << "- 不再出现'无效的序列号'错误" << std::endl;
        
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "❌ 测试失败: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "❌ 测试失败: 未知错误" << std::endl;
        return 1;
    }
}
