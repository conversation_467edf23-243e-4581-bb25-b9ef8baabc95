/**
 * @file ExportManager.cpp
 * @brief 导入导出管理模块实现 - 基于现有导出器
 * @details 封装现有的XLSDataExporter_1_2和JSONDataExporter_1_2，提供Excel导入导出和JSON导出接口
 * <AUTHOR> Assistant
 * @date 2025-01-27
 * @version 3.4.0
 */

#include "ExportManager.h"
#include "XLSDataExporter_1_2.h"
#include "JSONDataExporter_1_2.h"
#include "SensorDataManager_1_2.h"
#include "ActuatorDataManager_1_2.h"
#include <QFileInfo>
#include <QProgressDialog>
#include <QDebug>

ExportManager::ExportManager(QObject* parent)
    : QObject(parent),
      actuatorDataManager_(nullptr),
      sensorDataManager_(nullptr),
      ctrlChanDataManager_(nullptr),
      hardwareNodeResDataManager_(nullptr) {
    initializeExporters();
}

ExportManager::~ExportManager() {
}

void ExportManager::setActuatorDataManager(ActuatorDataManager_1_2* manager) {
    actuatorDataManager_ = manager;
    // 重新初始化导出器以使用新的数据管理器
    initializeExporters();
}

void ExportManager::setSensorDataManager(SensorDataManager_1_2* manager) {
    sensorDataManager_ = manager;
    // 重新初始化导出器以使用新的数据管理器
    initializeExporters();
}

void ExportManager::setCtrlChanDataManager(CtrlChanDataManager* manager) {
    ctrlChanDataManager_ = manager;
    // 重新初始化导出器以使用新的数据管理器
    initializeExporters();
}

void ExportManager::setHardwareNodeResDataManager(HardwareNodeResDataManager* manager) {
    hardwareNodeResDataManager_ = manager;
}

// 使用现有XLSDataExporter_1_2进行Excel导入导出
bool ExportManager::importProjectFromExcel(const QString& filePath) {
    clearError();
    
    if (!xlsExporter_) {
        lastError_ = "Excel导出器未初始化";
        emit errorOccurred(lastError_);
        return false;
    }
    
    QFileInfo fileInfo(filePath);
    if (!fileInfo.exists()) {
        lastError_ = QString("文件不存在: %1").arg(filePath);
        emit errorOccurred(lastError_);
        return false;
    }
    
    QString extension = fileInfo.suffix().toLower();
    if (extension != "xlsx" && extension != "xls") {
        lastError_ = QString("不支持的Excel文件格式: %1").arg(extension);
        emit errorOccurred(lastError_);
        return false;
    }
    
    try {
        // 使用XLSDataExporter_1_2的导入功能
        bool success = xlsExporter_->importProject(filePath);
        
        if (!success) {
            lastError_ = xlsExporter_->getLastError();
            if (lastError_.isEmpty()) {
                lastError_ = "Excel导入失败，原因未知";
            }
            emit errorOccurred(lastError_);
        } else {
            emit importCompleted(filePath, true);
        }
        
        return success;
        
    } catch (const std::exception& e) {
        lastError_ = QString("Excel导入时发生异常: %1").arg(e.what());
        emit errorOccurred(lastError_);
        return false;
    } catch (...) {
        lastError_ = "Excel导入时发生未知异常";
        emit errorOccurred(lastError_);
        return false;
    }
}

bool ExportManager::exportProjectToExcel(const QString& filePath) {
    clearError();
    
    if (!xlsExporter_) {
        lastError_ = "Excel导出器未初始化";
        emit errorOccurred(lastError_);
        return false;
    }
    
    try {
        // 使用XLSDataExporter_1_2的导出功能
        bool success = xlsExporter_->exportCompleteProject(filePath);
        
        if (!success) {
            lastError_ = xlsExporter_->getLastError();
            if (lastError_.isEmpty()) {
                lastError_ = "Excel导出失败，原因未知";
            }
            emit errorOccurred(lastError_);
        } else {
            emit exportCompleted(filePath, true);
        }
        
        return success;
        
    } catch (const std::exception& e) {
        lastError_ = QString("Excel导出时发生异常: %1").arg(e.what());
        emit errorOccurred(lastError_);
        return false;
    } catch (...) {
        lastError_ = "Excel导出时发生未知异常";
        emit errorOccurred(lastError_);
        return false;
    }
}

// 使用现有JSONDataExporter_1_2进行JSON导出（仅导出，不支持导入）
bool ExportManager::exportProjectToJSON(const QString& filePath) {
    clearError();
    
    if (!jsonExporter_) {
        lastError_ = "JSON导出器未初始化";
        emit errorOccurred(lastError_);
        return false;
    }
    
    try {
        // 使用JSONDataExporter_1_2的导出功能
        bool success = jsonExporter_->exportCompleteProject(filePath);
        
        if (!success) {
            lastError_ = jsonExporter_->getLastError();
            if (lastError_.isEmpty()) {
                lastError_ = "JSON导出失败，原因未知";
            }
            emit errorOccurred(lastError_);
        } else {
            emit exportCompleted(filePath, true);
        }
        
        return success;
        
    } catch (const std::exception& e) {
        lastError_ = QString("JSON导出时发生异常: %1").arg(e.what());
        emit errorOccurred(lastError_);
        return false;
    } catch (...) {
        lastError_ = "JSON导出时发生未知异常";
        emit errorOccurred(lastError_);
        return false;
    }
}

bool ExportManager::exportChannelConfigToJSON(const QString& filePath) {
    clearError();
    
    if (!jsonExporter_) {
        lastError_ = "JSON导出器未初始化";
        emit errorOccurred(lastError_);
        return false;
    }
    
    try {
        // 使用JSONDataExporter_1_2的通道配置导出功能
        bool success = jsonExporter_->exportChannelConfig(filePath);
        
        if (!success) {
            lastError_ = jsonExporter_->getLastError();
            if (lastError_.isEmpty()) {
                lastError_ = "通道配置JSON导出失败，原因未知";
            }
            emit errorOccurred(lastError_);
        } else {
            emit exportCompleted(filePath, true);
        }
        
        return success;
        
    } catch (const std::exception& e) {
        lastError_ = QString("通道配置JSON导出时发生异常: %1").arg(e.what());
        emit errorOccurred(lastError_);
        return false;
    } catch (...) {
        lastError_ = "通道配置JSON导出时发生未知异常";
        emit errorOccurred(lastError_);
        return false;
    }
}

// 项目级导入导出（Excel导入，Excel/JSON导出）
bool ExportManager::importProject(const QString& filePath, QProgressDialog* progressDialog) {
    Q_UNUSED(progressDialog)
    clearError();
    
    QString extension = getFileExtension(filePath).toLower();
    
    if (extension == "xlsx" || extension == "xls") {
        return importProjectFromExcel(filePath);
    } else {
        lastError_ = QString("不支持的导入文件格式: %1（仅支持Excel格式）").arg(extension);
        emit errorOccurred(lastError_);
        return false;
    }
}

bool ExportManager::exportProject(const QString& filePath, QProgressDialog* progressDialog) {
    Q_UNUSED(progressDialog)
    clearError();
    
    QString extension = getFileExtension(filePath).toLower();
    
    if (extension == "xlsx" || extension == "xls") {
        return exportProjectToExcel(filePath);
    } else if (extension == "json") {
        return exportProjectToJSON(filePath);
    } else {
        lastError_ = QString("不支持的导出文件格式: %1（支持Excel和JSON格式）").arg(extension);
        emit errorOccurred(lastError_);
        return false;
    }
}

// 错误处理
QString ExportManager::getLastError() const {
    return lastError_;
}

void ExportManager::clearError() {
    lastError_.clear();
}

// 私有方法
void ExportManager::initializeExporters() {
    // 创建XLS导出器
    xlsExporter_ = std::make_unique<XLSDataExporter_1_2>(sensorDataManager_, actuatorDataManager_, ctrlChanDataManager_);
    
    // 创建JSON导出器
    jsonExporter_ = std::make_unique<JSONDataExporter_1_2>(sensorDataManager_);
    
    // 设置JSON导出器的XLS导出器（用于复用数据收集逻辑）
    if (jsonExporter_ && xlsExporter_) {
        jsonExporter_->setXLSExporter(xlsExporter_.get());
    }
    
    // 设置控制通道数据管理器
    if (jsonExporter_ && ctrlChanDataManager_) {
        jsonExporter_->setCtrlChanDataManager(ctrlChanDataManager_);
    }
}

QString ExportManager::getFileExtension(const QString& filePath) const {
    QFileInfo fileInfo(filePath);
    return fileInfo.suffix();
} 