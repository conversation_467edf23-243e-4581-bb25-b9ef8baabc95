@echo off
echo ========================================
echo  CSV管理器编译测试
echo ========================================

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

g++ --version
if errorlevel 1 (
    echo 错误: MinGW编译器未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目（包含CSV管理器）...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo.
    echo 可能的原因：
    echo 1. 缺少必要的头文件包含
    echo 2. Qt版本不兼容
    echo 3. 编译器配置问题
    echo.
    echo 请检查以下文件：
    echo - include/CSVManager.h
    echo - src/CSVManager.cpp
    echo - SiteResConfig_Simple.pro
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  编译成功！CSV管理器已集成
    echo ========================================
    echo.
    echo 新增功能：
    echo - CSV文件读写操作
    echo - 多种编码格式支持
    echo - 数据验证和格式检测
    echo - 批量处理功能
    echo - 进度回调支持
    echo.
    
    if exist "SiteResConfig.exe" (
        echo 可执行文件: SiteResConfig.exe
        echo.
        echo 是否启动程序测试CSV功能？(Y/N)
        set /p choice=
        if /i "%choice%"=="Y" (
            echo 启动程序...
            start SiteResConfig.exe
        )
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
        echo.
        echo 是否启动程序测试CSV功能？(Y/N)
        set /p choice=
        if /i "%choice%"=="Y" (
            echo 启动程序...
            start debug\SiteResConfig.exe
        )
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
        echo.
        echo 是否启动程序测试CSV功能？(Y/N)
        set /p choice=
        if /i "%choice%"=="Y" (
            echo 启动程序...
            start release\SiteResConfig.exe
        )
    ) else (
        echo 警告: 找不到可执行文件！
    )
)

echo.
echo ========================================
echo  CSV管理器集成完成
echo ========================================
echo.
echo 使用方法：
echo 1. 在代码中包含 #include "CSVManager.h"
echo 2. 创建 CSVManager 对象
echo 3. 调用相应的方法进行CSV操作
echo.
echo 示例代码位置：
echo - src/CSVManagerExample.cpp
echo.
pause
