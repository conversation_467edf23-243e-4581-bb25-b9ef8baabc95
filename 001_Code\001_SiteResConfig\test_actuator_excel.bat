@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo.
echo ========================================
echo  🚀 作动器Excel功能测试
echo ========================================
echo.

REM 设置Qt环境
set QTDIR=C:\Qt\5.15.2\msvc2019_64
set PATH=%QTDIR%\bin;%PATH%

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo 1. 清理构建文件...
if exist "test_actuator_excel.exe" del test_actuator_excel.exe >nul 2>&1
if exist "test_actuator_excel.obj" del test_actuator_excel.obj >nul 2>&1
if exist "moc_*.cpp" del moc_*.cpp >nul 2>&1

echo.
echo 2. 检查必要文件...
if not exist "test_actuator_excel.cpp" (
    echo ❌ 测试源文件不存在
    goto :error
)

if not exist "include\XLSDataExporter.h" (
    echo ❌ XLSDataExporter.h 不存在
    goto :error
)

if not exist "include\ActuatorDialog.h" (
    echo ❌ ActuatorDialog.h 不存在
    goto :error
)

echo ✅ 必要文件检查通过

echo.
echo 3. 生成qmake项目文件...
echo QT += core widgets > test_actuator_excel.pro
echo CONFIG += console >> test_actuator_excel.pro
echo TARGET = test_actuator_excel >> test_actuator_excel.pro
echo TEMPLATE = app >> test_actuator_excel.pro
echo. >> test_actuator_excel.pro
echo INCLUDEPATH += include >> test_actuator_excel.pro
echo INCLUDEPATH += ../../../vcpkg/installed/x64-windows/include >> test_actuator_excel.pro
echo. >> test_actuator_excel.pro
echo LIBS += -L../../../vcpkg/installed/x64-windows/lib >> test_actuator_excel.pro
echo LIBS += -lQXlsx >> test_actuator_excel.pro
echo. >> test_actuator_excel.pro
echo SOURCES += test_actuator_excel.cpp >> test_actuator_excel.pro
echo SOURCES += src/XLSDataExporter.cpp >> test_actuator_excel.pro
echo SOURCES += src/ActuatorDialog.cpp >> test_actuator_excel.pro
echo SOURCES += src/SensorDataManager.cpp >> test_actuator_excel.pro
echo SOURCES += src/DataExportManager.cpp >> test_actuator_excel.pro
echo. >> test_actuator_excel.pro
echo HEADERS += include/XLSDataExporter.h >> test_actuator_excel.pro
echo HEADERS += include/ActuatorDialog.h >> test_actuator_excel.pro
echo HEADERS += include/IDataExporter.h >> test_actuator_excel.pro
echo HEADERS += include/SensorDataManager.h >> test_actuator_excel.pro
echo HEADERS += include/DataExportManager.h >> test_actuator_excel.pro
echo. >> test_actuator_excel.pro
echo FORMS += ui/ActuatorDialog.ui >> test_actuator_excel.pro

echo ✅ 项目文件生成完成

echo.
echo 4. 运行qmake...
qmake test_actuator_excel.pro
if errorlevel 1 (
    echo ❌ qmake 失败
    goto :error
)

echo ✅ qmake 成功

echo.
echo 5. 编译项目...
nmake
if errorlevel 1 (
    echo ❌ 编译失败
    goto :error
)

echo ✅ 编译成功

echo.
echo 6. 运行测试程序...
if not exist "test_actuator_excel.exe" (
    echo ❌ 可执行文件不存在
    goto :error
)

echo.
echo ========================================
echo  🔧 执行作动器Excel功能测试
echo ========================================
echo.

test_actuator_excel.exe
set TEST_RESULT=%errorlevel%

echo.
echo ========================================
echo  📊 测试结果分析
echo ========================================
echo.

if %TEST_RESULT% equ 0 (
    echo ✅ 测试程序执行成功
    
    echo.
    echo 7. 检查生成的Excel文件...
    
    if exist "test_actuator_data.xlsx" (
        echo ✅ test_actuator_data.xlsx 已生成
        dir "test_actuator_data.xlsx" | findstr /C:".xlsx"
    ) else (
        echo ❌ test_actuator_data.xlsx 未生成
    )
    
    if exist "complete_project_with_actuators.xlsx" (
        echo ✅ complete_project_with_actuators.xlsx 已生成
        dir "complete_project_with_actuators.xlsx" | findstr /C:".xlsx"
    ) else (
        echo ❌ complete_project_with_actuators.xlsx 未生成
    )
    
    echo.
    echo 🎉 作动器Excel功能测试完成！
    echo.
    echo 📁 生成的文件:
    if exist "test_actuator_data.xlsx" echo   - test_actuator_data.xlsx (作动器工作表)
    if exist "complete_project_with_actuators.xlsx" echo   - complete_project_with_actuators.xlsx (完整项目)
    
) else (
    echo ❌ 测试程序执行失败，错误代码: %TEST_RESULT%
    goto :error
)

echo.
echo 8. 清理临时文件...
if exist "test_actuator_excel.pro" del test_actuator_excel.pro >nul 2>&1
if exist "Makefile*" del Makefile* >nul 2>&1
if exist "*.obj" del *.obj >nul 2>&1
if exist "moc_*.cpp" del moc_*.cpp >nul 2>&1

echo.
echo ========================================
echo  ✅ 作动器Excel功能测试成功完成！
echo ========================================
echo.
echo 📋 测试总结:
echo   1. ✅ 作动器数据结构定义正确
echo   2. ✅ Excel导出功能正常
echo   3. ✅ Excel导入功能正常
echo   4. ✅ 完整项目导出功能正常
echo   5. ✅ 17列作动器格式支持完整
echo.
echo 🎯 下一步建议:
echo   1. 在主程序中集成作动器Excel功能
echo   2. 添加作动器数据的UI管理界面
echo   3. 测试与现有传感器数据的兼容性
echo.

pause
goto :end

:error
echo.
echo ❌ 测试失败！
echo.
echo 🔍 可能的原因:
echo   1. Qt环境未正确配置
echo   2. vcpkg依赖库未安装
echo   3. 源代码编译错误
echo   4. 文件权限问题
echo.
echo 💡 解决建议:
echo   1. 检查Qt安装路径: %QTDIR%
echo   2. 确认QXlsx库已通过vcpkg安装
echo   3. 检查编译错误信息
echo   4. 确保有文件写入权限
echo.
pause
exit /b 1

:end
endlocal
