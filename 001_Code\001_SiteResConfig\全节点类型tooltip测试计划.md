# 🧪 全节点类型Tooltip测试计划

## 📋 **测试节点类型清单**

### **1. 硬件配置树节点**

#### **🏗️ 根节点和分类节点**
- [ ] **硬件配置** (根节点)
- [ ] **作动器** (分类根节点)
- [ ] **传感器** (分类根节点)
- [ ] **硬件节点资源** (分类根节点)

#### **⚙️ 作动器相关节点**
- [ ] **作动器组** (如: 50kN_作动器组, 自定义_作动器组)
- [ ] **作动器设备** (如: 作动器_000001, 作动器_000002) ✅ 已测试通过

#### **📡 传感器相关节点**
- [ ] **传感器组** (如: 载荷_传感器组, 自定义传感器组)
- [ ] **传感器设备** (如: 传感器_000001, 传感器_000002)

#### **🔌 硬件节点相关**
- [ ] **硬件节点** (如: LD-B1, LD-B2)
- [ ] **硬件通道** (如: CH1, CH2 - 作为硬件节点的子节点)

### **2. 试验配置树节点**

#### **🎛️ 控制通道**
- [ ] **CH1** (试验配置树中的控制通道)
- [ ] **CH2** (试验配置树中的控制通道)

#### **📊 传感器配置**
- [ ] **载荷1** (载荷传感器配置)
- [ ] **载荷2** (载荷传感器配置)
- [ ] **位置** (位置传感器配置)

#### **🎯 控制配置**
- [ ] **控制** (控制作动器配置)

## 🔍 **各节点类型的期望显示内容**

### **1. 作动器组节点**
**期望显示**:
```
═══ 50kN_作动器组 详细信息 ═══
组名称: 50kN_作动器组
设备数量: 2个
组类型: 作动器组
─────────────────────
功能: 管理50kN级别的液压作动器
组类型: 液压作动器组
创建时间: 2025-08-19 17:19:46

🔧 DEBUG信息 🔧 (Debug模式)
═══════════════════
🏷️ 作动器组DEBUG信息:
├─ 组名称: 50kN_作动器组
├─ 提取的组ID: 1
├─ 实际组ID: 1
├─ 组内作动器数量: 2个
├─ 组类型: 液压
├─ 创建时间: 2025-08-19 17:19:46
└─ 组备注: 50kN级别液压作动器组

📋 组内作动器列表:
├─ [1] ID:1 序列号:作动器_0000013213 类型:单出杆
└─ [2] ID:2 序列号:作动器_000001 类型:双出杆
```

### **2. 传感器组节点**
**期望显示**:
```
═══ 载荷_传感器组 详细信息 ═══
组名称: 载荷_传感器组
设备数量: 1个
组类型: 传感器组
─────────────────────
功能: 管理载荷类型的传感器设备
传感器类型: 载荷传感器、力传感器等
创建时间: 2025-08-19 17:19:46

🔧 DEBUG信息 🔧 (Debug模式)
═══════════════════
🏷️ 传感器组DEBUG信息:
├─ 组名称: 载荷_传感器组
├─ 组ID: 1
├─ 组内传感器数量: 1个
├─ 组类型: 载荷
└─ 创建时间: 2025-08-19 17:19:46

📋 组内传感器列表:
└─ [1] ID:1 序列号:传感器_000001 类型:载荷传感器
```

### **3. 传感器设备节点**
**期望显示**:
```
═══ 传感器_000001 传感器设备详细信息 ═══
设备名称: 传感器_000001
设备类型: 传感器设备
传感器ID: 1
─────────────────────
序列号: 传感器_000001
类型: 载荷传感器
量程: ±50kN
精度: 0.1%FS
单位: kN
─────────────────────
组信息:
│  所属组: 载荷_传感器组
│  组ID: 1
│  组类型: 载荷
│  组创建时间: 2025-08-19 17:19:46
─────────────────────
备注: 主要载荷传感器

🔧 DEBUG信息 🔧 (Debug模式)
═══════════════════
📋 基本ID信息:
├─ 传感器ID: 1
├─ 序列号: 传感器_000001
├─ 传感器类型: 载荷传感器
└─ 单位: kN

🏷️ 组织信息:
├─ 所属组ID: 1
├─ 所属组名: 载荷_传感器组
├─ 组内序号: 1/1
└─ 组创建时间: 2025-08-19 17:19:46
```

### **4. 硬件节点**
**期望显示**:
```
═══ LD-B1 硬件节点详细信息 ═══
节点名称: LD-B1
节点类型: 硬件节点
节点ID: 1
─────────────────────
IP地址: *************
端口: 8080
通道数量: 2个
状态: 在线
─────────────────────
通道信息:
│  CH1: IP=*************, Port=8081, 启用
│  CH2: IP=*************, Port=8082, 启用
─────────────────────

🔧 DEBUG信息 🔧 (Debug模式)
═══════════════════
📋 硬件节点信息:
├─ 节点ID: 1
├─ 节点名称: LD-B1
├─ IP地址: *************
├─ 端口: 8080
└─ 通道数量: 2个

📡 通道详细信息:
├─ CH1: ID=1, IP=*************, Port=8081, 启用
└─ CH2: ID=2, IP=*************, Port=8082, 启用
```

### **5. 硬件通道**
**期望显示**:
```
═══ LD-B1 - CH1 硬件通道详细信息 ═══
通道名称: CH1
通道类型: 硬件通道
通道ID: 1
所属节点: LD-B1
─────────────────────
IP地址: *************
端口: 8081
状态: 启用
通道类型: 输入/输出
─────────────────────

🔧 DEBUG信息 🔧 (Debug模式)
═══════════════════
📡 通道详细信息:
├─ 通道ID: 1
├─ 通道名称: CH1
├─ 所属硬件节点: LD-B1
├─ IP地址: *************
├─ 端口: 8081
└─ 状态: 启用
```

### **6. 试验配置树控制通道**
**期望显示**:
```
═══ CH1 控制通道详细信息 ═══
通道名称: CH1
通道类型: 控制通道
通道ID: 1
─────────────────────
功能: 作动器控制输出
输出类型: 模拟电压
输出范围: ±10V
采样频率: 1000Hz
状态: 激活
─────────────────────
关联设备: 作动器_000002
控制模式: 位置控制
```

### **7. 载荷传感器配置**
**期望显示**:
```
═══ 载荷1 载荷传感器详细信息 ═══
传感器名称: 载荷1
传感器类型: 载荷传感器
传感器ID: 1
─────────────────────
关联设备: 传感器_000001
量程: ±50kN
精度: 0.1%FS
采样频率: 1000Hz
状态: 激活
─────────────────────
校准信息:
│  零点: 0.000V
│  满量程: 10.000V
│  线性度: 0.05%FS
```

## 🧪 **测试步骤**

### **阶段1: 硬件配置树测试**
1. **编译Debug版本**
2. **打开工程文件**
3. **依次悬停测试**:
   - 硬件配置根节点
   - 作动器根节点
   - 传感器根节点
   - 硬件节点资源根节点
   - 各个作动器组
   - 各个传感器组
   - 各个传感器设备
   - 各个硬件节点
   - 硬件节点的通道

### **阶段2: 试验配置树测试**
1. **测试右侧试验配置区域**
2. **依次悬停测试**:
   - CH1、CH2控制通道
   - 载荷1、载荷2传感器
   - 位置传感器
   - 控制作动器

### **阶段3: Debug信息验证**
1. **验证Debug模式显示**:
   - 确认显示🔧 DEBUG信息分区
   - 验证所有ID信息完整
   - 检查格式化显示效果

2. **对比Release模式**:
   - 编译Release版本
   - 确认只显示基础信息
   - 验证无Debug信息显示

## 📊 **测试记录表**

| 节点类型 | 测试状态 | 显示正确 | Debug信息 | 备注 |
|---------|---------|---------|-----------|------|
| 硬件配置根节点 | ⏳ | ⏳ | ⏳ | |
| 作动器根节点 | ⏳ | ⏳ | ⏳ | |
| 传感器根节点 | ⏳ | ⏳ | ⏳ | |
| 硬件节点资源根节点 | ⏳ | ⏳ | ⏳ | |
| 作动器组 | ⏳ | ⏳ | ⏳ | |
| 作动器设备 | ✅ | ✅ | ✅ | 已验证完成 |
| 传感器组 | ⏳ | ⏳ | ⏳ | |
| 传感器设备 | ⏳ | ⏳ | ⏳ | |
| 硬件节点 | ⏳ | ⏳ | ⏳ | |
| 硬件通道 | ⏳ | ⏳ | ⏳ | |
| 试验配置CH1/CH2 | ⏳ | ⏳ | ⏳ | |
| 载荷传感器配置 | ⏳ | ⏳ | ⏳ | |
| 位置传感器配置 | ⏳ | ⏳ | ⏳ | |
| 控制作动器配置 | ⏳ | ⏳ | ⏳ | |

## 🎯 **预期结果**

完成测试后，所有节点类型都应该：
1. **正确识别节点类型**
2. **显示完整的基础信息**
3. **Debug模式下显示详细的调试信息**
4. **格式化显示美观清晰**
5. **数据准确无误**

请按照这个测试计划逐一验证各种节点类型的tooltip显示效果！
