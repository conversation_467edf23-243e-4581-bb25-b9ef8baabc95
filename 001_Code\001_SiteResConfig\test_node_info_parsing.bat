@echo off
echo ========================================
echo  节点信息解析功能测试
echo ========================================

echo 解析需求：
echo 1. "类型: 单出杆" → 名称="类型", 参数1="单出杆"
echo 2. "Frequency: 528.00 Hz" → 名称="Frequency", 参数1="528.00", 参数2="Hz"
echo 3. "Balance: 0.000 V" → 名称="Balance", 参数1="0.000", 参数2="V"
echo.

echo 解析模式：
echo 模式1 - 简单键值对：
echo   ├─ 正则表达式：^([^:]+):\s*(.+)$
echo   ├─ 示例："类型: 单出杆"
echo   └─ 结果：名称="类型", 参数1="单出杆"
echo.
echo 模式2 - 键值对+数值单位：
echo   ├─ 正则表达式：^([^:]+):\s*([0-9]+\.?[0-9]*)\s+(.+)$
echo   ├─ 示例："Frequency: 528.00 Hz"
echo   └─ 结果：名称="Frequency", 参数1="528.00", 参数2="Hz"
echo.
echo 模式3 - 直接数值单位：
echo   ├─ 正则表达式：^([0-9]+\.?[0-9]*)\s+(.+)$
echo   ├─ 示例："528.00 Hz"
echo   └─ 结果：保持原名称, 参数1="528.00", 参数2="Hz"
echo.

echo 技术实现：
echo ✅ ParseNodeInfo方法处理所有解析逻辑
echo ✅ 优先解析tooltip信息（更详细）
echo ✅ 支持多种格式的自动识别
echo ✅ 保持向后兼容性
echo ✅ 详细的调试输出
echo.

echo 解析优先级：
echo 1. 优先使用tooltip中的信息
echo 2. 如果tooltip为空，使用节点名称
echo 3. 按模式顺序尝试匹配
echo 4. 未匹配时保持原始格式
echo.

echo CSV输出格式：
echo 类型,名称,参数1,参数2,参数3
echo 硬件节点,类型,单出杆,,
echo 硬件节点,Frequency,528.00,Hz,
echo 硬件节点,Balance,0.000,V,
echo.

echo 调试信息：
echo - "解析结果: 名称=Frequency 参数1=528.00 参数2=Hz"
echo - "解析数值格式: 名称=CH1 参数1=100.0 参数2=kN"
echo - "未匹配解析模式，保持原始: 作动器组"
echo.

echo 测试步骤：
echo 1. 重新编译项目
echo 2. 确保节点有包含上述格式的名称或tooltip
echo 3. 保存工程配置为CSV文件
echo 4. 查看控制台解析调试信息
echo 5. 检查CSV文件中的参数列分布
echo.

echo 预期结果：
echo - 控制台显示详细的解析过程
echo - CSV文件中信息按列正确分布
echo - "类型: 单出杆"正确分解到名称和参数1列
echo - "Frequency: 528.00 Hz"正确分解到名称、参数1、参数2列
echo - 未匹配格式的节点保持原样
echo.

pause
