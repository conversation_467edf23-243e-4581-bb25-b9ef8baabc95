手动编译步骤（解决Qt环境变量问题）：

步骤1：找到Qt安装路径
运行脚本：find_qt_path.bat
或手动查找以下位置的qmake.exe：
- C:\Qt\5.14.2\mingw73_32\bin\qmake.exe
- C:\Qt\Qt5.14.2\5.14.2\mingw73_32\bin\qmake.exe
- D:\Qt\5.14.2\mingw73_32\bin\qmake.exe

步骤2：打开命令提示符（cmd）

步骤3：设置Qt环境变量（根据实际路径修改）：
示例1（如果Qt在C:\Qt\5.14.2）：
   set QTDIR=C:\Qt\5.14.2\mingw73_32
   set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

示例2（如果Qt在C:\Qt\Qt5.14.2）：
   set QTDIR=C:\Qt\Qt5.14.2\5.14.2\mingw73_32
   set PATH=%QTDIR%\bin;C:\Qt\Qt5.14.2\Tools\mingw730_32\bin;%PATH%

步骤4：验证工具可用：
   qmake -v
   mingw32-make --version

步骤5：进入项目目录：
   cd /d "D:\Work\Project\004_SiteResConfig_ProjectAll\001_Code\001_SiteResConfig\SiteResConfig"

步骤6：清理之前的编译：
   mingw32-make clean

步骤7：生成Makefile：
   qmake SiteResConfig_Simple.pro -spec win32-g++

步骤8：编译项目：
   mingw32-make -j4

步骤9：如果编译成功，运行程序：
   debug\SiteResConfig.exe

主要修复内容：
- 移除了Qt 5.14中已弃用的setCodecForCStrings API
- 简化了控制台编码设置
- 改进了界面刷新机制，避免卡死
- 增强了异常处理

测试重点：
1. 检查程序是否能正常启动
2. 尝试打开工程文件：C:\Users\<USER>\Desktop\20250818152156_实验工程.xlsx
3. 观察导入过程是否显示进度对话框
4. 确认导入后不再卡死
5. 查看导入完成提示对话框
