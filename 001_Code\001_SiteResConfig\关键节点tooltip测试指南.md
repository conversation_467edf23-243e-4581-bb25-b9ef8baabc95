# 🧪 关键节点Tooltip测试指南

## 🎯 **优先测试的关键节点类型**

基于当前工程文件的实际节点，我们重点测试以下几种关键节点类型：

### **1. 作动器组节点** 🔧
**测试节点**: `50kN_作动器组`, `自定义_作动器组`

**测试步骤**:
1. 悬停在"50kN_作动器组"上
2. 检查是否显示组详细信息
3. 验证Debug模式下的组ID、作动器列表等信息

**期望显示**:
```
═══ 50kN_作动器组 详细信息 ═══
组名称: 50kN_作动器组
设备数量: 2个
组类型: 作动器组
─────────────────────
功能: 管理50kN级别的液压作动器
组类型: 液压作动器组

🔧 DEBUG信息 🔧 (Debug模式)
═══════════════════
🏷️ 作动器组DEBUG信息:
├─ 组名称: 50kN_作动器组
├─ 提取的组ID: 1
├─ 实际组ID: 1
├─ 组内作动器数量: 2个
└─ 组类型: 液压

📋 组内作动器列表:
├─ [1] ID:1 序列号:作动器_0000013213 类型:单出杆
└─ [2] ID:2 序列号:作动器_000001 类型:双出杆
```

### **2. 传感器组节点** 📡
**测试节点**: `载荷_传感器组`, `自定义传感器组`

**测试步骤**:
1. 悬停在"载荷_传感器组"上
2. 检查是否显示传感器组详细信息
3. 验证Debug模式下的组信息

**期望显示**:
```
═══ 载荷_传感器组 详细信息 ═══
组名称: 载荷_传感器组
设备数量: 1个
组类型: 传感器组
─────────────────────
功能: 管理载荷类型的传感器设备
传感器类型: 载荷传感器、力传感器等

🔧 DEBUG信息 🔧 (Debug模式)
═══════════════════
🏷️ 传感器组DEBUG信息:
├─ 组名称: 载荷_传感器组
├─ 组ID: 1
├─ 组内传感器数量: 1个
└─ 组类型: 载荷

📋 组内传感器列表:
└─ [1] ID:1 序列号:传感器_000001 类型:载荷传感器
```

### **3. 传感器设备节点** 📊
**测试节点**: `传感器_000001`, `传感器_000001_直线_095739`

**测试步骤**:
1. 悬停在"传感器_000001"上
2. 检查是否显示传感器设备详细信息
3. 验证Debug模式下的传感器参数

**期望显示**:
```
═══ 传感器_000001 传感器设备详细信息 ═══
设备名称: 传感器_000001
设备类型: 传感器设备
─────────────────────
序列号: 传感器_000001
类型: 载荷传感器
量程: ±50kN
精度: 0.1%FS
单位: kN

🔧 DEBUG信息 🔧 (Debug模式)
═══════════════════
📋 基本ID信息:
├─ 传感器ID: 1
├─ 序列号: 传感器_000001
├─ 传感器类型: 载荷传感器
└─ 单位: kN

🏷️ 组织信息:
├─ 所属组ID: 1
├─ 所属组名: 载荷_传感器组
└─ 组内序号: 1/1
```

### **4. 硬件节点资源根节点** 🔌
**测试节点**: `硬件节点资源`

**测试步骤**:
1. 悬停在"硬件节点资源"根节点上
2. 检查是否显示硬件节点资源管理信息

**期望显示**:
```
═══ 硬件节点资源 详细信息 ═══
节点名称: 硬件节点资源
节点类型: 硬件节点管理根节点
硬件节点数量: 0个
─────────────────────
功能: 管理所有硬件控制器节点
设备类型: LD-B系列控制器等
```

### **5. 试验配置树控制通道** 🎛️
**测试节点**: 右侧试验配置区域的`CH1`, `CH2`

**测试步骤**:
1. 悬停在右侧试验配置区域的"CH1"上
2. 检查是否显示控制通道详细信息

**期望显示**:
```
═══ CH1 详细信息 ═══
通道名称: CH1
通道类型: 控制通道
子节点数量: 4个
─────────────────────
功能: 作动器控制输出通道
输出类型: 模拟电压信号
控制对象: 液压作动器
状态: 激活

子节点列表:
├─ 载荷1
├─ 载荷2
├─ 位置
└─ 控制
```

### **6. 载荷传感器配置** 📈
**测试节点**: 右侧试验配置区域的`载荷1`, `载荷2`

**测试步骤**:
1. 悬停在右侧试验配置区域的"载荷1"上
2. 检查是否显示载荷传感器配置信息

**期望显示**:
```
═══ 载荷1 详细信息 ═══
传感器名称: 载荷1
传感器类型: 载荷传感器
传感器ID: 1
─────────────────────
功能: 载荷信号采集
输入类型: 模拟电压信号
采样频率: 1000Hz
状态: 激活
─────────────────────
关联设备: 自定义传感器 - 传感器_000001
设备参数:
序列号: 传感器_000001
类型: 载荷传感器
量程: ±50kN
精度: 0.1%FS
```

## 🧪 **快速测试流程**

### **步骤1: 编译Debug版本**
确保当前编译为Debug模式，以便看到完整的debug信息。

### **步骤2: 打开工程文件**
打开`20250819171946_实验工程.xls`文件。

### **步骤3: 依次测试节点**
按照以下顺序测试：

1. **左侧硬件配置树**:
   - ✅ 作动器_000002 (已验证)
   - ⏳ 50kN_作动器组
   - ⏳ 自定义_作动器组
   - ⏳ 载荷_传感器组
   - ⏳ 传感器_000001
   - ⏳ 硬件节点资源

2. **右侧试验配置树**:
   - ⏳ CH1 (控制通道)
   - ⏳ CH2 (控制通道)
   - ⏳ 载荷1 (传感器配置)
   - ⏳ 载荷2 (传感器配置)
   - ⏳ 位置 (传感器配置)
   - ⏳ 控制 (作动器配置)

### **步骤4: 验证要点**

对每个节点验证：
- ✅ **基础信息正确**: 节点名称、类型、数量等
- ✅ **详细参数完整**: 技术参数、配置信息等
- ✅ **Debug信息显示**: Debug模式下的ID、序号、统计信息
- ✅ **格式化美观**: 分隔线、图标、缩进等
- ✅ **数据准确**: 与实际数据管理器中的数据一致

## 📊 **测试记录**

| 节点类型 | 节点名称 | 基础信息 | Debug信息 | 格式化 | 数据准确 | 状态 |
|---------|---------|---------|-----------|--------|----------|------|
| 作动器设备 | 作动器_000002 | ✅ | ✅ | ✅ | ✅ | ✅完成 |
| 作动器组 | 50kN_作动器组 | ⏳ | ⏳ | ⏳ | ⏳ | ⏳待测试 |
| 作动器组 | 自定义_作动器组 | ⏳ | ⏳ | ⏳ | ⏳ | ⏳待测试 |
| 传感器组 | 载荷_传感器组 | ⏳ | ⏳ | ⏳ | ⏳ | ⏳待测试 |
| 传感器设备 | 传感器_000001 | ⏳ | ⏳ | ⏳ | ⏳ | ⏳待测试 |
| 硬件节点资源 | 硬件节点资源 | ⏳ | ⏳ | ⏳ | ⏳ | ⏳待测试 |
| 控制通道 | CH1 | ⏳ | ⏳ | ⏳ | ⏳ | ⏳待测试 |
| 控制通道 | CH2 | ⏳ | ⏳ | ⏳ | ⏳ | ⏳待测试 |
| 载荷传感器 | 载荷1 | ⏳ | ⏳ | ⏳ | ⏳ | ⏳待测试 |
| 载荷传感器 | 载荷2 | ⏳ | ⏳ | ⏳ | ⏳ | ⏳待测试 |

## 🎯 **测试重点**

1. **节点识别准确性**: 确保每种节点类型都能被正确识别
2. **信息完整性**: 验证所有关键信息都正确显示
3. **Debug功能**: 确认Debug模式下显示额外的调试信息
4. **数据一致性**: 验证显示的数据与数据管理器中的数据一致
5. **用户体验**: 确保tooltip显示美观、易读

请按照这个测试指南逐一验证各种关键节点类型的tooltip显示效果，并记录测试结果！
