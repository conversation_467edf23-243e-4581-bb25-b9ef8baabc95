#ifndef CtrlChan_DATA_MANAGER_H
#define CtrlChan_DATA_MANAGER_H

/**
 * @file CtrlChanDataManager.h
 * @brief 控制通道数据管理器
 * @details 管理控制通道组和控制通道参数的存储、检索和操作
 * <AUTHOR> Agent
 * @date 2024-01-15
 * @version 1.0.0
 */

#include <QObject>
#include <QMap>
#include <QList>
#include <QString>
#include <QDateTime>
#include <QDebug>
#include <QMutex>
#include <QMutexLocker>
#include "DataModels_Fixed.h"

/**
 * @brief 控制通道数据管理器类
 * @details 提供控制通道组和控制通道参数的完整管理功能
 */
class CtrlChanDataManager : public QObject {
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父对象指针
     */
    explicit CtrlChanDataManager(QObject* parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~CtrlChanDataManager();

    // ============================================================================
    // 控制通道组管理方法
    // ============================================================================

    /**
     * @brief 创建控制通道组
     * @param group 控制通道组对象
     * @return 创建是否成功
     */
    bool createControlChannelGroup(const UI::ControlChannelGroup& group);

    /**
     * @brief 更新控制通道组
     * @param group 控制通道组对象
     * @return 更新是否成功
     */
    bool updateControlChannelGroup(const UI::ControlChannelGroup& group);

    /**
     * @brief 删除控制通道组
     * @param groupId 组ID
     * @return 删除是否成功
     */
    bool deleteControlChannelGroup(int groupId);

    /**
     * @brief 获取所有控制通道组
     * @return 控制通道组列表
     */
    QList<UI::ControlChannelGroup> getAllControlChannelGroups() const;

    /**
     * @brief 获取指定控制通道组
     * @param groupId 组ID
     * @return 控制通道组对象
     */
    UI::ControlChannelGroup getControlChannelGroup(int groupId) const;

    /**
     * @brief 检查控制通道组是否存在
     * @param groupId 组ID
     * @return 是否存在
     */
    bool hasControlChannelGroup(int groupId) const;

    // ============================================================================
    // 控制通道管理方法
    // ============================================================================

    /**
     * @brief 添加控制通道到组
     * @param groupId 组ID
     * @param channel 控制通道参数
     * @return 添加是否成功
     */
    bool addChannelToGroup(int groupId, const UI::ControlChannelParams& channel);

    /**
     * @brief 更新组内控制通道
     * @param groupId 组ID
     * @param channel 控制通道参数
     * @return 更新是否成功
     */
    bool updateChannelInGroup(int groupId, const UI::ControlChannelParams& channel);

    /**
     * @brief 从组中移除控制通道
     * @param groupId 组ID
     * @param channelId 通道ID
     * @return 移除是否成功
     */
    bool removeChannelFromGroup(int groupId, const std::string& channelId);

    /**
     * @brief 获取组内所有控制通道
     * @param groupId 组ID
     * @return 控制通道列表
     */
    std::vector<UI::ControlChannelParams> getChannelsInGroup(int groupId) const;

    // ============================================================================
    // 数据统计和查询方法
    // ============================================================================

    /**
     * @brief 获取控制通道组总数
     * @return 组总数
     */
    int getGroupCount() const;

    /**
     * @brief 获取控制通道总数
     * @return 通道总数
     */
    int getTotalChannelCount() const;

    /**
     * @brief 获取指定组的通道数量
     * @param groupId 组ID
     * @return 通道数量
     */
    int getChannelCountInGroup(int groupId) const;

    /**
     * @brief 清空所有数据
     */
    void clearAllData();

    // ============================================================================
    // 数据导入导出方法
    // ============================================================================

    /**
     * @brief 导出到CSV文件
     * @param filePath 文件路径
     * @return 导出是否成功
     */
    bool exportToCSV(const QString& filePath) const;

    /**
     * @brief 导出到JSON文件
     * @param filePath 文件路径
     * @return 导出是否成功
     */
    // 🚫 已注释：独立JSON导出功能已废弃
    // bool exportToJSON(const QString& filePath) const;

    /**
     * @brief 从JSON文件导入
     * @param filePath 文件路径
     * @return 导入是否成功
     */
    bool importFromJSON(const QString& filePath);

signals:
    /**
     * @brief 控制通道组创建信号
     * @param groupId 组ID
     */
    void controlChannelGroupCreated(int groupId);

    /**
     * @brief 控制通道组更新信号
     * @param groupId 组ID
     */
    void controlChannelGroupUpdated(int groupId);

    /**
     * @brief 控制通道组删除信号
     * @param groupId 组ID
     */
    void controlChannelGroupDeleted(int groupId);

    /**
     * @brief 控制通道添加信号
     * @param groupId 组ID
     * @param channelId 通道ID
     */
    void controlChannelAdded(int groupId, const QString& channelId);

    /**
     * @brief 控制通道更新信号
     * @param groupId 组ID
     * @param channelId 通道ID
     */
    void controlChannelUpdated(int groupId, const QString& channelId);

    /**
     * @brief 控制通道删除信号
     * @param groupId 组ID
     * @param channelId 通道ID
     */
    void controlChannelRemoved(int groupId, const QString& channelId);

private:
    // ============================================================================
    // 私有成员变量
    // ============================================================================

    QMap<int, UI::ControlChannelGroup> groupStorage_;  // 控制通道组存储
    int nextGroupId_;                                   // 下一个组ID
    mutable QMutex dataMutex_;                         // 数据访问互斥锁

    // ============================================================================
    // 私有辅助方法
    // ============================================================================

    /**
     * @brief 生成下一个组ID
     * @return 新的组ID
     */
    int generateNextGroupId();

    /**
     * @brief 按创建时间排序控制通道组
     * @param groups 控制通道组列表
     */
    void sortGroupsByCreateTime(QList<UI::ControlChannelGroup>& groups) const;

    /**
     * @brief 验证控制通道组数据
     * @param group 控制通道组
     * @return 验证是否通过
     */
    bool validateControlChannelGroup(const UI::ControlChannelGroup& group) const;

    /**
     * @brief 验证控制通道数据
     * @param channel 控制通道参数
     * @return 验证是否通过
     */
    bool validateControlChannelParams(const UI::ControlChannelParams& channel) const;
};

#endif // CtrlChan_DATA_MANAGER_H
