@echo off
echo.
echo ========================================
echo Actuator1_1 Complete Integration Test
echo ========================================
echo.

echo Integration completed:
echo 1. Added ActuatorDataManager1_1 to MainWindow
echo 2. Added all actuator1_1 operation slot functions
echo 3. Added menu items for actuator1_1 operations
echo 4. Connected signals and slots
echo.

echo New Features Available:
echo - Create Actuator1_1 (Ctrl+Alt+A)
echo - Edit/Delete Actuator1_1 (context menu)
echo - Export to JSON/Excel
echo - Import from JSON/Excel
echo - Show Statistics
echo.

if exist "SiteResConfig\SiteResConfig_Simple.pro" (
    echo Found project file, testing compilation...
    echo.
    
    cd SiteResConfig
    
    echo Cleaning old files...
    if exist "Makefile" del /Q "Makefile" 2>nul
    if exist "debug" rmdir /S /Q "debug" 2>nul
    if exist "release" rmdir /S /Q "release" 2>nul
    if exist "ui_*.h" del /Q "ui_*.h" 2>nul
    
    echo.
    echo Running qmake...
    qmake SiteResConfig_Simple.pro -spec win32-g++ "CONFIG+=debug" 2>&1
    
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo qmake successful!
        echo.
        echo Starting compilation...
        mingw32-make debug 2>&1
        
        if %ERRORLEVEL% EQU 0 (
            echo.
            echo *** COMPILATION SUCCESSFUL! ***
            echo.
            echo Actuator1_1 integration completed successfully!
            echo All new features are ready to use.
            echo.
            
            if exist "debug\SiteResConfig.exe" (
                echo Executable created successfully!
                echo.
                echo Available Actuator1_1 Features:
                echo 1. Menu: Hardware -^> Actuator1_1 Version
                echo 2. Create new actuators with advanced parameters
                echo 3. JSON import/export with full data structure
                echo 4. Excel import/export support
                echo 5. Statistical analysis and reporting
                echo.
                
                set /p choice="Launch program to test Actuator1_1 features? (y/n): "
                if /i "%choice%"=="y" (
                    echo Launching program...
                    start "" "debug\SiteResConfig.exe"
                    echo.
                    echo Test Instructions:
                    echo 1. Go to Hardware menu -^> Actuator1_1 Version
                    echo 2. Click "Create Actuator" to test dialog
                    echo 3. Test all 4 tabs in the dialog
                    echo 4. Try export/import functions
                    echo 5. Check statistics display
                )
            ) else (
                echo ERROR: Executable not found
            )
        ) else (
            echo.
            echo *** COMPILATION FAILED ***
            echo Please check the error messages above.
        )
    ) else (
        echo.
        echo *** QMAKE FAILED ***
        echo Please check Qt environment configuration.
    )
    
    cd ..
) else (
    echo ERROR: Project file not found
)

echo.
echo ========================================
echo Integration Test Completed
echo ========================================
echo.
pause
