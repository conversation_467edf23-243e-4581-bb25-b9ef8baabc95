@echo off
echo ========================================
echo  验证JSON格式错误和重复信息修复
echo ========================================
echo.

echo 1. 检查 CreateSensorDetailedConfigJSON 方法中的格式修复...
echo.

echo 检查基本信息字段格式是否正确...
findstr /n "serialObj\[\"# 实验工程配置文件\"\] = \"\"" "SiteResConfig\src\MainWindow_Qt_Simple.cpp"
if errorlevel 1 (
    echo ❌ 错误: 序列号字段格式不正确
    goto :error
) else (
    echo ✅ 成功: 序列号字段格式正确
)

findstr /n "serialObj\[\"field2\"\] = QString(u8\"  ├─ 序列号\")" "SiteResConfig\src\MainWindow_Qt_Simple.cpp"
if errorlevel 1 (
    echo ❌ 错误: 序列号标签位置不正确
    goto :error
) else (
    echo ✅ 成功: 序列号标签位置正确
)

findstr /n "serialObj\[\"field3\"\] = params.serialNumber" "SiteResConfig\src\MainWindow_Qt_Simple.cpp"
if errorlevel 1 (
    echo ❌ 错误: 序列号值位置不正确
    goto :error
) else (
    echo ✅ 成功: 序列号值位置正确
)
echo.

echo 2. 检查校准信息字段格式是否正确...
findstr /n "calibEnabledObj\[\"# 实验工程配置文件\"\] = \"\"" "SiteResConfig\src\MainWindow_Qt_Simple.cpp"
if errorlevel 1 (
    echo ❌ 错误: 校准启用字段格式不正确
    goto :error
) else (
    echo ✅ 成功: 校准启用字段格式正确
)

findstr /n "calibEnabledObj\[\"field2\"\] = QString(u8\"  ├─ 校准启用\")" "SiteResConfig\src\MainWindow_Qt_Simple.cpp"
if errorlevel 1 (
    echo ❌ 错误: 校准启用标签位置不正确
    goto :error
) else (
    echo ✅ 成功: 校准启用标签位置正确
)
echo.

echo 3. 检查信号调理字段格式是否正确...
findstr /n "polarityObj\[\"# 实验工程配置文件\"\] = \"\"" "SiteResConfig\src\MainWindow_Qt_Simple.cpp"
if errorlevel 1 (
    echo ❌ 错误: 极性字段格式不正确
    goto :error
) else (
    echo ✅ 成功: 极性字段格式正确
)

findstr /n "polarityObj\[\"field2\"\] = QString(u8\"  ├─ 极性\")" "SiteResConfig\src\MainWindow_Qt_Simple.cpp"
if errorlevel 1 (
    echo ❌ 错误: 极性标签位置不正确
    goto :error
) else (
    echo ✅ 成功: 极性标签位置正确
)
echo.

echo 4. 检查激励信息字段格式是否正确...
findstr /n "excitationEnabledObj\[\"# 实验工程配置文件\"\] = \"\"" "SiteResConfig\src\MainWindow_Qt_Simple.cpp"
if errorlevel 1 (
    echo ❌ 错误: 激励启用字段格式不正确
    goto :error
) else (
    echo ✅ 成功: 激励启用字段格式正确
)

findstr /n "excitationEnabledObj\[\"field2\"\] = QString(u8\"  ├─ 激励启用\")" "SiteResConfig\src\MainWindow_Qt_Simple.cpp"
if errorlevel 1 (
    echo ❌ 错误: 激励启用标签位置不正确
    goto :error
) else (
    echo ✅ 成功: 激励启用标签位置正确
)
echo.

echo 5. 检查编码器和反馈系数字段格式是否正确...
findstr /n "encoderObj\[\"# 实验工程配置文件\"\] = \"\"" "SiteResConfig\src\MainWindow_Qt_Simple.cpp"
if errorlevel 1 (
    echo ❌ 错误: 编码器字段格式不正确
    goto :error
) else (
    echo ✅ 成功: 编码器字段格式正确
)

findstr /n "positiveFeedbackObj\[\"# 实验工程配置文件\"\] = \"\"" "SiteResConfig\src\MainWindow_Qt_Simple.cpp"
if errorlevel 1 (
    echo ❌ 错误: 正向反馈系数字段格式不正确
    goto :error
) else (
    echo ✅ 成功: 正向反馈系数字段格式正确
)
echo.

echo 6. 检查重复信息是否已移除...
findstr /n "itemData\[\"detailedConfig\"\]" "SiteResConfig\src\MainWindow_Qt_Simple.cpp"
if not errorlevel 1 (
    echo ❌ 警告: 仍存在重复的详细配置添加
    echo 请检查 CollectTreeItemsInSpecificFormat 方法
) else (
    echo ✅ 成功: 重复信息已移除
)
echo.

echo 7. 检查注释是否正确添加...
findstr /n "传感器详细配置信息已通过硬件树结构集成" "SiteResConfig\src\MainWindow_Qt_Simple.cpp"
if errorlevel 1 (
    echo ❌ 错误: 未找到修复注释
    goto :error
) else (
    echo ✅ 成功: 修复注释已添加
)
echo.

echo ========================================
echo  🎉 JSON格式修复验证完成！
echo ========================================
echo.
echo 修复总结:
echo ✅ 所有JSON字段格式已修正为正确格式
echo ✅ field2 为标签，field3 为值的格式已统一
echo ✅ 重复信息已移除，数据清洁
echo ✅ 所有34个传感器配置参数格式正确
echo.
echo 正确的JSON格式示例:
echo {
echo     "# 实验工程配置文件": "",
echo     "field2": "  ├─ 序列号",
echo     "field3": "传感器_000001",
echo     "field4": "",
echo     "field5": ""
echo }
echo.
goto :end

:error
echo.
echo ========================================
echo  ❌ 验证失败！请检查修复是否正确
echo ========================================
echo.
echo 期望的JSON格式:
echo - "# 实验工程配置文件": "" (空字符串)
echo - "field2": "  ├─ 标签" (标签)
echo - "field3": "值" (实际值)
echo.
pause
exit /b 1

:end
echo JSON格式错误和重复信息问题现在完全修复！
echo.
pause
