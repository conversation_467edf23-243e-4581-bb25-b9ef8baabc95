@echo off
echo.
echo ========================================
echo Actuator1_1 UI Update Test
echo ========================================
echo.

echo UI updates implemented:
echo.
echo 1. Actuator Type Dropdown:
echo    - Single Rod (单出杆) = 1
echo    - Double Rod (双出杆) = 2
echo    - Uses getActuatorTypeOptions1_1()
echo.
echo 2. Serial Number Validation:
echo    - Real-time validation with regex ^[A-Za-z0-9]+$
echo    - Visual feedback (green/red border)
echo    - Tooltip messages for validation status
echo    - Default value: ABC123
echo.
echo 3. Measurement Unit Dropdown:
echo    - m (meter) = 1
echo    - mm (millimeter) = 2 (default)
echo    - cm (centimeter) = 3
echo    - inch = 4
echo    - Uses getMeasurementUnitOptions1_1()
echo.
echo 4. Polarity Dropdown:
echo    - Positive = 1 (default)
echo    - Negative = -1
echo    - Both = 9
echo    - Unknown = 0
echo    - Uses getPolarityOptions1_1()
echo.
echo 5. Preview Function:
echo    - Shows enum text instead of numbers
echo    - Type: "单出杆" instead of "1"
echo    - Polarity: "Positive" instead of "1"
echo    - Units: "mm" instead of "2"
echo.

if exist "SiteResConfig\SiteResConfig_Simple.pro" (
    echo Found project file, testing compilation...
    echo.
    
    cd SiteResConfig
    
    echo Cleaning old files...
    if exist "Makefile" del /Q "Makefile" 2>nul
    if exist "debug" rmdir /S /Q "debug" 2>nul
    if exist "release" rmdir /S /Q "release" 2>nul
    if exist "ui_*.h" del /Q "ui_*.h" 2>nul
    
    echo.
    echo Running qmake...
    qmake SiteResConfig_Simple.pro -spec win32-g++ "CONFIG+=debug" 2>&1
    
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo qmake successful!
        echo.
        echo Starting compilation and linking...
        mingw32-make debug 2>&1
        
        if %ERRORLEVEL% EQU 0 (
            echo.
            echo *** COMPILATION SUCCESSFUL! ***
            echo.
            echo UI updates implementation completed!
            echo All dropdown controls now use proper enums.
            echo.
            
            if exist "debug\SiteResConfig.exe" (
                echo Executable created successfully!
                echo.
                echo Updated UI features ready:
                echo 1. Type dropdown with 2 options (单出杆/双出杆)
                echo 2. Serial number with real-time validation
                echo 3. Measurement unit dropdown with 4 options
                echo 4. Polarity dropdown with 4 options
                echo 5. Preview shows human-readable text
                echo 6. Data validation with proper error messages
                echo.
                
                set /p choice="Launch program to test updated UI? (y/n): "
                if /i "%choice%"=="y" (
                    echo Launching program...
                    start "" "debug\SiteResConfig.exe"
                    echo.
                    echo Test the updated UI features:
                    echo [ ] Create actuator -^> Check type dropdown options
                    echo [ ] Enter invalid serial number -^> See red border
                    echo [ ] Enter valid serial number -^> See green border
                    echo [ ] Check measurement unit dropdown (m/mm/cm/inch)
                    echo [ ] Check polarity dropdown (Positive/Negative/Both/Unknown)
                    echo [ ] Click Preview -^> Verify text display (not numbers)
                    echo [ ] Save actuator -^> Verify data is stored correctly
                    echo [ ] Edit actuator -^> Verify dropdowns show correct values
                )
            ) else (
                echo ERROR: Executable not found
            )
        ) else (
            echo.
            echo *** COMPILATION FAILED ***
            echo Please check the error messages above.
        )
    ) else (
        echo.
        echo *** QMAKE FAILED ***
        echo Please check Qt environment configuration.
    )
    
    cd ..
) else (
    echo ERROR: Project file not found
)

echo.
echo ========================================
echo UI Update Test Completed
echo ========================================
echo.
pause
