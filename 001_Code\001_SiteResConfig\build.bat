@echo off
REM Build script for SiteResConfig project
REM This script builds the project using Visual Studio tools

echo ========================================
echo  SiteResConfig Build Script
echo ========================================
echo.

REM Check if vcpkg is available
if not exist "vcpkg\vcpkg.exe" (
    echo Error: vcpkg not found. Please install vcpkg first.
    echo.
    echo Installation steps:
    echo 1. git clone https://github.com/Microsoft/vcpkg.git
    echo 2. cd vcpkg
    echo 3. .\bootstrap-vcpkg.bat
    echo 4. .\vcpkg integrate install
    echo.
    pause
    exit /b 1
)

REM Set build configuration
set BUILD_CONFIG=Debug
set BUILD_PLATFORM=x86

if "%1"=="Release" set BUILD_CONFIG=Release
if "%2"=="x64" set BUILD_PLATFORM=x64

echo Build Configuration: %BUILD_CONFIG%
echo Build Platform: %BUILD_PLATFORM%
echo.

REM Install dependencies if needed
echo Checking dependencies...
vcpkg\vcpkg list | findstr "nlohmann-json" >nul
if errorlevel 1 (
    echo Installing nlohmann-json...
    vcpkg\vcpkg install nlohmann-json:%BUILD_PLATFORM%-windows
)

vcpkg\vcpkg list | findstr "sqlite3" >nul
if errorlevel 1 (
    echo Installing sqlite3...
    vcpkg\vcpkg install sqlite3:%BUILD_PLATFORM%-windows
)

vcpkg\vcpkg list | findstr "qt5-base" >nul
if errorlevel 1 (
    echo Installing qt5-base...
    vcpkg\vcpkg install qt5-base[widgets]:%BUILD_PLATFORM%-windows
)

vcpkg\vcpkg list | findstr "qt5-charts" >nul
if errorlevel 1 (
    echo Installing qt5-charts...
    vcpkg\vcpkg install qt5-charts:%BUILD_PLATFORM%-windows
)

echo Dependencies check completed.
echo.

REM Build the project
echo Building project...
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" SiteResConfig.sln /p:Configuration=%BUILD_CONFIG% /p:Platform=%BUILD_PLATFORM% /p:VcpkgEnabled=true
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2017\Professional\MSBuild\15.0\Bin\MSBuild.exe" (
    "C:\Program Files (x86)\Microsoft Visual Studio\2017\Professional\MSBuild\15.0\Bin\MSBuild.exe" SiteResConfig.sln /p:Configuration=%BUILD_CONFIG% /p:Platform=%BUILD_PLATFORM% /p:VcpkgEnabled=true
) else (
    echo Error: MSBuild not found. Please install Visual Studio 2017 or later.
    pause
    exit /b 1
)

if errorlevel 1 (
    echo.
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo Build completed successfully!
echo Output directory: bin\%BUILD_PLATFORM%\%BUILD_CONFIG%\
echo.

REM Run the application if requested
if "%3"=="run" (
    echo Running application...
    bin\%BUILD_PLATFORM%\%BUILD_CONFIG%\SiteResConfig.exe
)

pause
