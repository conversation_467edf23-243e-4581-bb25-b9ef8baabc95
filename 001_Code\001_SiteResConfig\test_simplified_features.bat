@echo off
echo.
echo ========================================
echo Simplified Features Test
echo ========================================
echo.

echo Simplified feature set:
echo [+] Excel Import - Full support
echo [+] Excel Export - Full support  
echo [+] JSON Export - Full support
echo [-] CSV Import/Export - Removed
echo [-] JSON Import - Removed
echo.

echo Remaining features:
echo 1. Create Actuator1_1 - 4-tab dialog
echo 2. Edit/Delete Actuator1_1 - Context menu
echo 3. Export to JSON - Complete data structure
echo 4. Export to Excel - Tab-separated format
echo 5. Import from Excel - Tab-separated format
echo 6. Statistics Display - Detailed analysis
echo.

if exist "SiteResConfig\SiteResConfig_Simple.pro" (
    echo Found project file, testing compilation...
    echo.
    
    cd SiteResConfig
    
    echo Cleaning old files...
    if exist "Makefile" del /Q "Makefile" 2>nul
    if exist "debug" rmdir /S /Q "debug" 2>nul
    if exist "release" rmdir /S /Q "release" 2>nul
    if exist "ui_*.h" del /Q "ui_*.h" 2>nul
    
    echo.
    echo Running qmake...
    qmake SiteResConfig_Simple.pro -spec win32-g++ "CONFIG+=debug" 2>&1
    
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo qmake successful!
        echo.
        echo Starting compilation and linking...
        mingw32-make debug 2>&1
        
        if %ERRORLEVEL% EQU 0 (
            echo.
            echo *** COMPILATION SUCCESSFUL! ***
            echo.
            echo Simplified Actuator1_1 features are ready!
            echo All undefined reference errors should be resolved.
            echo.
            
            if exist "debug\SiteResConfig.exe" (
                echo Executable created successfully!
                echo.
                echo Available features:
                echo 1. Hardware -^> Actuator1_1 Version -^> Create Actuator
                echo 2. Hardware -^> Actuator1_1 Version -^> Export to JSON
                echo 3. Hardware -^> Actuator1_1 Version -^> Export to Excel
                echo 4. Hardware -^> Actuator1_1 Version -^> Import from Excel
                echo 5. Hardware -^> Actuator1_1 Version -^> Statistics
                echo.
                
                set /p choice="Launch program to test simplified features? (y/n): "
                if /i "%choice%"=="y" (
                    echo Launching program...
                    start "" "debug\SiteResConfig.exe"
                    echo.
                    echo Test Instructions:
                    echo 1. Go to Hardware -^> Actuator1_1 Version
                    echo 2. Create a new actuator
                    echo 3. Export to JSON - check file content
                    echo 4. Export to Excel - check tab-separated format
                    echo 5. Import from Excel - test data parsing
                    echo 6. View statistics
                )
            ) else (
                echo ERROR: Executable not found
            )
        ) else (
            echo.
            echo *** COMPILATION FAILED ***
            echo Please check the error messages above.
        )
    ) else (
        echo.
        echo *** QMAKE FAILED ***
        echo Please check Qt environment configuration.
    )
    
    cd ..
) else (
    echo ERROR: Project file not found
)

echo.
echo ========================================
echo Simplified Features Test Completed
echo ========================================
echo.
pause
