@echo off
chcp 65001 > nul
echo ========================================
echo 序列号组内唯一性验证修复测试
echo ========================================
echo.

echo 🔧 正在编译修复后的代码...
cd SiteResConfig
qmake SiteResConfig_Simple.pro
mingw32-make clean
mingw32-make

if %ERRORLEVEL% NEQ 0 (
    echo ❌ 编译失败！
    pause
    exit /b 1
)

echo ✅ 编译成功！
echo.

echo 🚀 启动应用程序进行测试...
echo.
echo 📋 测试步骤：
echo.
echo 🎯 **修改内容总结**：
echo - 作动器：只在组内判断序列号唯一性（不全局判断）
echo - 传感器：只在组内判断序列号唯一性（不全局判断）
echo - Excel导入：只在组内检查重复，生成格式为"原序列号_组内重复_时间戳"
echo - 数据管理器：移除全局唯一性检查，允许跨组重复
echo.
echo 🧪 **测试用例1：作动器组内唯一性**
echo 1. 创建作动器组A，添加作动器（序列号：ACT001）→ 应该成功
echo 2. 在组A中再次添加作动器（序列号：ACT001）→ 应该失败，提示组内重复
echo 3. 创建作动器组B，添加作动器（序列号：ACT001）→ 应该成功（不同组允许重复）
echo 4. 编辑组A中的ACT001，改为ACT002 → 应该成功
echo 5. 编辑组A中的ACT002，改为组A中已存在的序列号 → 应该失败
echo.
echo 🧪 **测试用例2：传感器组内唯一性**
echo 1. 创建传感器组"载荷_传感器组"，添加传感器（序列号：SENSOR001）→ 应该成功
echo 2. 在同组中再次添加传感器（序列号：SENSOR001）→ 应该失败，提示组内重复
echo 3. 创建传感器组"位置_传感器组"，添加传感器（序列号：SENSOR001）→ 应该成功（不同组允许重复）
echo 4. 编辑"载荷_传感器组"中的SENSOR001，改为SENSOR002 → 应该成功
echo 5. 编辑"载荷_传感器组"中的SENSOR002，改为同组中已存在的序列号 → 应该失败
echo.
echo 🧪 **测试用例3：跨组重复验证**
echo 1. 在作动器组A中添加设备（序列号：DEVICE001）→ 应该成功
echo 2. 在作动器组B中添加设备（序列号：DEVICE001）→ 应该成功（不同组允许重复）
echo 3. 在传感器组中添加设备（序列号：DEVICE001）→ 应该成功（不同类型允许重复）
echo.
echo 🧪 **测试用例4：Excel导入验证**
echo 1. 导出当前工程为Excel文件
echo 2. 在Excel中同一组内添加重复序列号的设备
echo 3. 重新导入Excel文件
echo 4. 观察是否自动生成新序列号（格式：原序列号_组内重复_时间戳）
echo 5. 验证不同组间的相同序列号是否被保留
echo.
echo 🔍 **预期修复效果**：
echo ✅ 解决了"序列号已存在"的全局唯一性错误
echo ✅ 实现了真正的组内唯一性检查
echo ✅ 支持不同组间使用相同序列号
echo ✅ 编辑设备时正确验证组内唯一性
echo ✅ 创建设备时正确验证组内唯一性
echo.
echo 🔧 **修复的核心方法**：
echo - ActuatorDataManager::isSerialNumberUniqueInGroup()
echo - SensorDataManager::isSerialNumberUniqueInGroup()
echo - 编辑时使用excludeId参数排除自身
echo - 创建时检查组内是否存在重复
echo - Excel导入时只在组内检查重复（XLSDataExporter.cpp）
echo.
echo 📝 **修复的文件**：
echo - ActuatorDataManager.cpp/h - 数据管理器组内唯一性检查
echo - SensorDataManager.cpp/h - 数据管理器组内唯一性检查
echo - MainWindow_Qt_Simple.cpp - UI层验证逻辑
echo - XLSDataExporter.cpp - Excel导入时的验证逻辑
echo.

start "" "debug\SiteResConfig.exe"

echo 📊 应用程序已启动，请按照上述测试用例进行验证
echo.
echo ⚠️ **重要提示**：
echo 如果您看到的重复序列号格式仍然是"重复_时间戳"，
echo 这可能是之前导入的旧数据。请执行以下步骤：
echo.
echo 1. 创建新的工程文件
echo 2. 重新导入Excel数据
echo 3. 观察新生成的重复序列号格式应该是"原序列号_组内重复_时间戳"
echo.
echo 如果问题仍然存在，请查看控制台输出的调试信息
pause
