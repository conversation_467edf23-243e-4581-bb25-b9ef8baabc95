#ifndef DATASEQUENCEMANAGER_H
#define DATASEQUENCEMANAGER_H

#include <QObject>
#include <QMap>
#include <QSet>
#include <QStringList>
#include <QDebug>
#include <QDateTime>

/**
 * @brief 数据序号科学管理器
 * 
 * 负责管理打开工程时的数据序号，确保：
 * 1. 序号连续性（从1开始递增）
 * 2. 序号唯一性（无重复）
 * 3. 序号映射（Excel序号 -> 实际序号）
 * 4. 数据验证和报告
 */
class DataSequenceManager : public QObject
{
    Q_OBJECT

public:
    explicit DataSequenceManager(QObject *parent = nullptr);

    // ============================================================================
    // 序号映射管理
    // ============================================================================
    
    /**
     * @brief 重置序号映射器
     * @param dataType 数据类型（"作动器", "传感器", "硬件节点", "控制通道"）
     */
    void resetSequenceMapping(const QString& dataType);
    
    /**
     * @brief 获取或创建连续序号
     * @param dataType 数据类型
     * @param excelSequence Excel中的原始序号
     * @param groupName 组名称（用于日志）
     * @return 连续的实际序号
     */
    int getSequentialId(const QString& dataType, int excelSequence, const QString& groupName = QString());
    
    /**
     * @brief 获取序号映射关系
     * @param dataType 数据类型
     * @return Excel序号 -> 实际序号的映射表
     */
    QMap<int, int> getSequenceMapping(const QString& dataType) const;

    // ============================================================================
    // 数据验证
    // ============================================================================
    
    /**
     * @brief 验证序号连续性
     * @param dataType 数据类型
     * @param sequenceList 序号列表
     * @return 验证结果和报告
     */
    QPair<bool, QStringList> validateSequenceContinuity(const QString& dataType, const QList<int>& sequenceList);
    
    /**
     * @brief 验证序号唯一性
     * @param dataType 数据类型
     * @param sequenceList 序号列表
     * @return 验证结果和重复序号列表
     */
    QPair<bool, QList<int>> validateSequenceUniqueness(const QString& dataType, const QList<int>& sequenceList);
    
    /**
     * @brief 生成完整的验证报告
     * @param dataType 数据类型
     * @param totalGroups 总组数
     * @param totalItems 总项目数
     * @return 验证报告
     */
    QStringList generateValidationReport(const QString& dataType, int totalGroups, int totalItems);

    // ============================================================================
    // 序列号管理
    // ============================================================================
    
//    /**
//     * @brief 检查序列号唯一性
//     * @param dataType 数据类型
//     * @param serialNumber 序列号
//     * @return 是否唯一
//     */
//    bool isSerialNumberUnique(const QString& dataType, const QString& serialNumber);
    
    /**
     * @brief 注册序列号
     * @param dataType 数据类型
     * @param serialNumber 序列号
     */
    void registerSerialNumber(const QString& dataType, const QString& serialNumber);
    
    /**
     * @brief 生成唯一序列号
     * @param dataType 数据类型
     * @param originalSerialNumber 原始序列号
     * @return 唯一的序列号
     */
    QString generateUniqueSerialNumber(const QString& dataType, const QString& originalSerialNumber);

    // ============================================================================
    // 统计和报告
    // ============================================================================
    
    /**
     * @brief 获取统计信息
     * @param dataType 数据类型
     * @return 统计信息字符串
     */
    QString getStatistics(const QString& dataType) const;
    
    /**
     * @brief 清空所有数据
     */
    void clearAll();

signals:
    /**
     * @brief 序号映射创建信号
     * @param dataType 数据类型
     * @param excelId Excel序号
     * @param actualId 实际序号
     * @param groupName 组名称
     */
    void sequenceMapped(const QString& dataType, int excelId, int actualId, const QString& groupName);
    
    /**
     * @brief 序号验证完成信号
     * @param dataType 数据类型
     * @param isValid 是否有效
     * @param report 验证报告
     */
    void validationCompleted(const QString& dataType, bool isValid, const QStringList& report);

private:
    // 序号映射存储：数据类型 -> (Excel序号 -> 实际序号)
    QMap<QString, QMap<int, int>> sequenceMappings_;
    
    // 下一个可用序号：数据类型 -> 下一个序号
    QMap<QString, int> nextSequentialIds_;
    
    // 已注册的序列号：数据类型 -> 序列号集合
    QMap<QString, QSet<QString>> registeredSerialNumbers_;
    
    // 统计信息：数据类型 -> 统计数据
    QMap<QString, QMap<QString, int>> statistics_;
    
    /**
     * @brief 更新统计信息
     * @param dataType 数据类型
     * @param key 统计键
     * @param increment 增量
     */
    void updateStatistics(const QString& dataType, const QString& key, int increment = 1);
};

#endif // DATASEQUENCEMANAGER_H
