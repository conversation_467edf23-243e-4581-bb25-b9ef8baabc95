/**
 * @file CustomTreeWidgets.h
 * @brief 自定义树形控件，支持拖拽功能
 * <AUTHOR> Assistant
 * @date 2025-08-07
 * @version 1.0.0
 */

#ifndef CUSTOMTREEWIDGETS_H
#define CUSTOMTREEWIDGETS_H

#include <QtWidgets/QTreeWidget>
#include <QtWidgets/QTreeWidgetItem>
#include <QtGui/QDragEnterEvent>
#include <QtGui/QDragMoveEvent>
#include <QtGui/QDragLeaveEvent>
#include <QtGui/QDropEvent>
#include <QtGui/QMouseEvent>
#include <QtCore/QMimeData>
#include <QtCore/QTimer>

// 前向声明
class CMyMainWindow;

/**
 * @brief 自定义硬件树控件，支持拖拽发送
 */
class CustomHardwareTreeWidget : public QTreeWidget {
    Q_OBJECT

public:
    explicit CustomHardwareTreeWidget(QWidget* parent = nullptr);
    virtual ~CustomHardwareTreeWidget();
    void setMainWindow(CMyMainWindow* mainWindow);

    // 公共方法：强制恢复所有颜色
    void forceRestoreAllColors();

    // 设置目标节点高亮
    void setTargetNodesHighlight(QTreeWidgetItem* sourceItem);

    // 清除目标节点高亮
    void clearTargetNodesHighlight();

protected:
    QMimeData* mimeData(const QList<QTreeWidgetItem*> items) const override;
    void startDrag(Qt::DropActions supportedActions) override;
    void mousePressEvent(QMouseEvent* event) override;
    void mouseReleaseEvent(QMouseEvent* event) override;
    void dragEnterEvent(QDragEnterEvent* event) override;
    void dragLeaveEvent(QDragLeaveEvent* event) override;

private:
    CMyMainWindow* m_mainWindow;
    QBrush m_originalBackgroundColor;  // 保存原始背景颜色
    QBrush m_originalTextColor;        // 保存原始文字颜色
    QTreeWidgetItem* m_draggedItem;    // 当前被拖拽的项目

    // 恢复拖拽源颜色的辅助方法
    void restoreDraggedItemColor();
};

/**
 * @brief 自定义测试配置树控件，支持拖拽接收
 */
class CustomTestConfigTreeWidget : public QTreeWidget {
    Q_OBJECT

public:
    explicit CustomTestConfigTreeWidget(QWidget* parent = nullptr);
    virtual ~CustomTestConfigTreeWidget();
    void setMainWindow(CMyMainWindow* mainWindow);

    // 公共方法：强制恢复所有颜色
    void forceRestoreAllColors();

    // 设置节点高亮状态
    void setItemHighlight(QTreeWidgetItem* item, bool highlight);

    // 智能恢复节点颜色（如果是有效目标则恢复为基础绿色，否则恢复原色）
    void smartRestoreItemColor(QTreeWidgetItem* item);

protected:
    void dragEnterEvent(QDragEnterEvent* event) override;
    void dragMoveEvent(QDragMoveEvent* event) override;
    void dragLeaveEvent(QDragLeaveEvent* event) override;
    void dropEvent(QDropEvent* event) override;

private:
    CMyMainWindow* m_mainWindow;
    QTreeWidgetItem* m_lastHighlightedItem;     // 上次高亮的目标项目
    QBrush m_originalTargetBackgroundColor;     // 保存目标项目原始背景颜色
    QBrush m_originalTargetTextColor;           // 保存目标项目原始文字颜色
    QString m_currentDragSourceType;            // 当前拖拽的源类型

    // 恢复目标节点颜色的辅助方法
    void restoreTargetItemColor();
};

#endif // CUSTOMTREEWIDGETS_H
