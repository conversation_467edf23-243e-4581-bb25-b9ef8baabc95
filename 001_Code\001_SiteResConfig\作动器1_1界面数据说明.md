# 🔧 作动器1_1界面数据说明

## 📋 数据字段详细说明

### **1. 作动器类型 (type)**

#### **数据类型**: int
#### **可选值**:
- **1** = 单出杆 (SINGLE_ROD)
- **2** = 双出杆 (DOUBLE_ROD)

#### **界面显示**:
```cpp
// 下拉框选项
ComboBox选项:
├── 单出杆 (值: 1)
└── 双出杆 (值: 2)
```

#### **代码实现**:
```cpp
enum ActuatorType1_1 {
    SINGLE_ROD = 1,     // 单出杆
    DOUBLE_ROD = 2      // 双出杆
};

QString getActuatorTypeText1_1(int type);
QList<QPair<int, QString>> getActuatorTypeOptions1_1();
```

### **2. 序列号 (sn)**

#### **数据类型**: QString
#### **格式要求**: 只能包含字母和数字
#### **验证规则**: 正则表达式 `^[A-Za-z0-9]+$`

#### **有效示例**:
- ✅ "ABC123"
- ✅ "SN001"
- ✅ "12345"
- ✅ "A1B2C3"

#### **无效示例**:
- ❌ "ABC-123" (包含连字符)
- ❌ "SN_001" (包含下划线)
- ❌ "ABC 123" (包含空格)
- ❌ "ABC@123" (包含特殊字符)

#### **代码实现**:
```cpp
bool isValidSerialNumber1_1(const QString& sn) {
    if (sn.isEmpty()) return false;
    QRegExp regex("^[A-Za-z0-9]+$");
    return regex.exactMatch(sn);
}
```

### **3. 测量单位 (meas_unit)**

#### **数据类型**: int
#### **可选值**:
- **1** = m (米)
- **2** = mm (毫米)
- **3** = cm (厘米)
- **4** = inch (英寸)

#### **界面显示**:
```cpp
// 下拉框选项
ComboBox选项:
├── m (值: 1)
├── mm (值: 2)
├── cm (值: 3)
└── inch (值: 4)
```

#### **代码实现**:
```cpp
enum MeasurementUnit1_1 {
    UNIT_M = 1,         // m (米)
    UNIT_MM = 2,        // mm (毫米)
    UNIT_CM = 3,        // cm (厘米)
    UNIT_INCH = 4       // inch (英寸)
};

QString getMeasurementUnitText1_1(int unit);
QList<QPair<int, QString>> getMeasurementUnitOptions1_1();
```

### **4. 极性 (polarity)**

#### **数据类型**: int
#### **可选值**:
- **1** = Positive (正极性)
- **-1** = Negative (负极性)
- **9** = Both (双向)
- **0** = Unknown (未知)

#### **界面显示**:
```cpp
// 下拉框选项
ComboBox选项:
├── Positive (值: 1)
├── Negative (值: -1)
├── Both (值: 9)
└── Unknown (值: 0)
```

#### **代码实现**:
```cpp
enum Polarity1_1 {
    UNKNOWN = 0,        // Unknown
    POSITIVE = 1,       // Positive
    NEGATIVE = -1,      // Negative
    BOTH = 9           // Both
};

QString getPolarityText1_1(int polarity);
QList<QPair<int, QString>> getPolarityOptions1_1();
```

## 🎮 界面控件配置

### **1. 作动器类型下拉框**
```cpp
// 初始化下拉框
QList<QPair<int, QString>> typeOptions = getActuatorTypeOptions1_1();
for (const auto& option : typeOptions) {
    typeComboBox->addItem(option.second, option.first);
}

// 设置当前值
int index = typeComboBox->findData(params.type);
if (index >= 0) {
    typeComboBox->setCurrentIndex(index);
}

// 获取选中值
int selectedType = typeComboBox->currentData().toInt();
```

### **2. 序列号输入框**
```cpp
// 设置输入验证
QRegExpValidator* validator = new QRegExpValidator(QRegExp("^[A-Za-z0-9]*$"), snLineEdit);
snLineEdit->setValidator(validator);

// 实时验证
connect(snLineEdit, &QLineEdit::textChanged, [this](const QString& text) {
    if (isValidSerialNumber1_1(text) || text.isEmpty()) {
        snLineEdit->setStyleSheet(""); // 正常样式
    } else {
        snLineEdit->setStyleSheet("border: 2px solid red;"); // 错误样式
    }
});
```

### **3. 测量单位下拉框**
```cpp
// 初始化下拉框
QList<QPair<int, QString>> unitOptions = getMeasurementUnitOptions1_1();
for (const auto& option : unitOptions) {
    measUnitComboBox->addItem(option.second, option.first);
}

// 设置默认值为mm
int defaultIndex = measUnitComboBox->findData(UNIT_MM);
if (defaultIndex >= 0) {
    measUnitComboBox->setCurrentIndex(defaultIndex);
}
```

### **4. 极性下拉框**
```cpp
// 初始化下拉框
QList<QPair<int, QString>> polarityOptions = getPolarityOptions1_1();
for (const auto& option : polarityOptions) {
    polarityComboBox->addItem(option.second, option.first);
}

// 设置默认值为Positive
int defaultIndex = polarityComboBox->findData(POSITIVE);
if (defaultIndex >= 0) {
    polarityComboBox->setCurrentIndex(defaultIndex);
}
```

## 📊 数据验证规则

### **1. 必填字段验证**
```cpp
bool validateRequiredFields() {
    if (nameLineEdit->text().trimmed().isEmpty()) {
        QMessageBox::warning(this, "验证错误", "名称不能为空！");
        return false;
    }
    
    if (snLineEdit->text().trimmed().isEmpty()) {
        QMessageBox::warning(this, "验证错误", "序列号不能为空！");
        return false;
    }
    
    if (!isValidSerialNumber1_1(snLineEdit->text().trimmed())) {
        QMessageBox::warning(this, "验证错误", "序列号只能包含字母和数字！");
        return false;
    }
    
    return true;
}
```

### **2. 数值范围验证**
```cpp
bool validateRanges() {
    double minRange = measRangeMinSpinBox->value();
    double maxRange = measRangeMaxSpinBox->value();
    
    if (minRange >= maxRange) {
        QMessageBox::warning(this, "验证错误", "测量范围最小值必须小于最大值！");
        return false;
    }
    
    double precision = precisionSpinBox->value();
    if (precision <= 0.0) {
        QMessageBox::warning(this, "验证错误", "精度必须大于0！");
        return false;
    }
    
    return true;
}
```

## 🔧 实际应用示例

### **完整的数据设置示例**
```cpp
void setActuatorParams1_1(const UI::ActuatorParams1_1& params) {
    // 基本信息
    nameLineEdit->setText(params.name);
    
    // 设置作动器类型
    int typeIndex = typeComboBox->findData(params.type);
    if (typeIndex >= 0) {
        typeComboBox->setCurrentIndex(typeIndex);
    }
    
    zeroOffsetSpinBox->setValue(params.zero_offset);
    
    // 下位机配置
    lcIdSpinBox->setValue(params.lc_id);
    stationIdSpinBox->setValue(params.station_id);
    
    // 作动器详细参数
    modelLineEdit->setText(params.params.model);
    snLineEdit->setText(params.params.sn);
    kSpinBox->setValue(params.params.k);
    bSpinBox->setValue(params.params.b);
    precisionSpinBox->setValue(params.params.precision);
    
    // 设置极性
    int polarityIndex = polarityComboBox->findData(params.params.polarity);
    if (polarityIndex >= 0) {
        polarityComboBox->setCurrentIndex(polarityIndex);
    }
    
    // 设置测量单位
    int measUnitIndex = measUnitComboBox->findData(params.params.meas_unit);
    if (measUnitIndex >= 0) {
        measUnitComboBox->setCurrentIndex(measUnitIndex);
    }
    
    // 测量范围
    measRangeMinSpinBox->setValue(params.params.meas_range_min);
    measRangeMaxSpinBox->setValue(params.params.meas_range_max);
    
    // 输出信号配置
    int outputUnitIndex = outputUnitComboBox->findData(params.params.output_signal_unit);
    if (outputUnitIndex >= 0) {
        outputUnitComboBox->setCurrentIndex(outputUnitIndex);
    }
    
    outputRangeMinSpinBox->setValue(params.params.output_signal_range_min);
    outputRangeMaxSpinBox->setValue(params.params.output_signal_range_max);
}
```

### **完整的数据获取示例**
```cpp
UI::ActuatorParams1_1 getActuatorParams1_1() const {
    UI::ActuatorParams1_1 params;
    
    // 基本信息
    params.name = nameLineEdit->text().trimmed();
    params.type = typeComboBox->currentData().toInt();
    params.zero_offset = zeroOffsetSpinBox->value();
    
    // 下位机配置
    params.lc_id = lcIdSpinBox->value();
    params.station_id = stationIdSpinBox->value();
    
    // 板卡配置
    params.board_id_ao = aoIdSpinBox->value();
    params.board_type_ao = aoTypeSpinBox->value();
    params.port_id_ao = aoPortSpinBox->value();
    params.board_id_do = doIdSpinBox->value();
    params.board_type_do = doTypeSpinBox->value();
    params.port_id_do = doPortSpinBox->value();
    
    // 作动器详细参数
    params.params.model = modelLineEdit->text().trimmed();
    params.params.sn = snLineEdit->text().trimmed();
    params.params.k = kSpinBox->value();
    params.params.b = bSpinBox->value();
    params.params.precision = precisionSpinBox->value();
    params.params.polarity = polarityComboBox->currentData().toInt();
    params.params.meas_unit = measUnitComboBox->currentData().toInt();
    params.params.meas_range_min = measRangeMinSpinBox->value();
    params.params.meas_range_max = measRangeMaxSpinBox->value();
    params.params.output_signal_unit = outputUnitComboBox->currentData().toInt();
    params.params.output_signal_range_min = outputRangeMinSpinBox->value();
    params.params.output_signal_range_max = outputRangeMaxSpinBox->value();
    
    return params;
}
```

## ✅ 数据说明总结

**核心数据约束**:
- ✅ **作动器类型**: 1=单出杆, 2=双出杆
- ✅ **序列号**: 只能包含字母和数字
- ✅ **测量单位**: 1=m, 2=mm, 3=cm, 4=inch
- ✅ **极性**: 1=Positive, -1=Negative, 9=Both, 0=Unknown

**界面实现要点**:
- ✅ 使用下拉框提供预定义选项
- ✅ 实时验证用户输入
- ✅ 提供清晰的错误提示
- ✅ 支持数据的双向绑定

现在作动器1_1版本的界面数据说明已完整定义！🚀
