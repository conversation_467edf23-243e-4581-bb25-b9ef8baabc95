@echo off
echo ========================================
echo  测试重复作动器添加问题修复
echo ========================================

REM 设置Qt环境变量
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目（测试重复作动器添加修复）...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo 请检查上面的错误信息。
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！重复作动器添加问题已修复
    echo ========================================
    
    echo.
    echo ✅ 修复内容:
    echo - 修复了作动器重复添加到DataManager的问题
    echo - 避免了"作动器序列号已存在"错误
    echo - 确保作动器只添加一次到DataManager
    echo - 正确获取已保存作动器的分配ID
    echo.
    echo 🔧 修复原理:
    echo 1. 原问题: saveActuatorDetailedParams 和 createOrUpdateActuatorGroup 都调用 addActuator
    echo 2. 第一次调用: saveActuatorDetailedParams 成功添加作动器
    echo 3. 第二次调用: createOrUpdateActuatorGroup 尝试重复添加，失败
    echo 4. 修复方案: createOrUpdateActuatorGroup 检查作动器是否已存在
    echo 5. 如果存在: 直接获取已保存的参数（包含分配的ID）
    echo 6. 如果不存在: 才调用 addActuator 添加
    echo.
    echo 🎯 测试步骤:
    echo 1. 启动软件
    echo 2. 新建项目
    echo 3. 创建作动器组
    echo 4. 在组中添加作动器
    echo 5. 验证不再出现"作动器序列号已存在"错误
    echo.
    
    if exist "SiteResConfig.exe" (
        echo 启动程序验证修复...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 启动程序验证修复...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 启动程序验证修复...
        start release\SiteResConfig.exe
    )
)

echo.
echo 📖 验证指南:
echo.
echo 🎮 详细测试步骤:
echo 1. 启动软件后，新建一个项目
echo 2. 在硬件树中右键"作动器"节点
echo 3. 选择"新建" → "作动器组"
echo 4. 创建一个作动器组（如"测试组"）
echo 5. 在作动器组上右键，选择"新建作动器"
echo 6. 填写作动器参数（使用默认序列号）
echo 7. 点击保存
echo 8. 观察是否还会出现"作动器序列号已存在"错误
echo.
echo ✅ 预期结果:
echo - 不再出现"作动器序列号已存在"错误
echo - 作动器成功保存到DataManager
echo - 作动器成功添加到作动器组
echo - 作动器在硬件树中正确显示
echo - 日志显示正常的操作记录
echo.
echo 🔄 数据流程:
echo 1. 用户填写作动器参数
echo 2. saveActuatorDetailedParams 保存到DataManager（分配ID）
echo 3. createOrUpdateActuatorGroup 检查作动器已存在
echo 4. 获取已保存的作动器参数（包含分配的ID）
echo 5. 使用正确ID将作动器添加到组
echo 6. 保存作动器组成功
echo.
echo 🚨 如果仍有问题:
echo - 检查日志中的详细错误信息
echo - 确认作动器参数填写完整
echo - 验证序列号格式是否正确
echo.
pause
