@echo off
chcp 65001 >nul
echo ========================================
echo  CSV到JSON转换工具
echo ========================================

set CSV_FILE=C:\Users\<USER>\Desktop\20250812095644_实验工程.csv
set JSON_FILE=C:\Users\<USER>\Desktop\20250812095644_实验工程.json

echo 检查CSV文件是否存在...
if exist "%CSV_FILE%" (
    echo ✅ 找到CSV文件: %CSV_FILE%
) else (
    echo ❌ 找不到CSV文件: %CSV_FILE%
    echo.
    echo 请检查以下可能的原因:
    echo 1. 文件路径是否正确
    echo 2. 文件是否存在于桌面
    echo 3. 文件名是否完全匹配
    echo.
    echo 正在搜索桌面上的CSV文件...
    dir "C:\Users\<USER>\Desktop\*.csv" 2>nul
    if errorlevel 1 (
        echo 桌面上没有找到任何CSV文件
    )
    echo.
    pause
    exit /b 1
)

echo.
echo 开始转换...
echo 输入文件: %CSV_FILE%
echo 输出文件: %JSON_FILE%
echo.

REM 运行转换工具
csv_to_json_converter.exe "%CSV_FILE%" "%JSON_FILE%"

if %errorlevel%==0 (
    echo.
    echo ✅ 转换成功完成!
    echo 📁 JSON文件已保存到: %JSON_FILE%
    
    REM 检查生成的JSON文件
    if exist "%JSON_FILE%" (
        echo ✅ 确认JSON文件已创建
        
        REM 显示文件大小
        for %%I in ("%JSON_FILE%") do (
            echo 📊 文件大小: %%~zI 字节
        )
        
        echo.
        echo 是否要打开JSON文件查看内容? (y/n)
        set /p choice=
        if /i "%choice%"=="y" (
            notepad "%JSON_FILE%"
        )
    ) else (
        echo ❌ JSON文件创建失败
    )
) else (
    echo.
    echo ❌ 转换失败，错误代码: %errorlevel%
)

echo.
echo 转换过程完成。
pause
