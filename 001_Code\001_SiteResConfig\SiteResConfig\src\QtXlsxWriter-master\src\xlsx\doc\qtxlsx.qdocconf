include($QT_INSTALL_DOCS/global/qt-html-templates-offline.qdocconf)
include($QT_INSTALL_DOCS/global/qt-module-defaults.qdocconf)

project                 = QtXlsx
description             = Qt Xlsx Reference Documentation
url                     = http://qtxlsx.debao.me
version                 = $QT_VERSION

qhp.projects            = QtXlsx

qhp.QtXlsx.file                = qtxlsx.qhp
qhp.QtXlsx.namespace           = me.debao.qtxlsx.$QT_VERSION_TAG
qhp.QtXlsx.virtualFolder       = qtxlsx
qhp.QtXlsx.indexTitle          = Qt Xlsx
qhp.QtXlsx.indexRoot           =

qhp.QtXlsx.filterAttributes    = qtxlsx $QT_VERSION qtrefdoc
qhp.QtXlsx.customFilters.Qt.name = QtXlsx $QT_VERSION
qhp.QtXlsx.customFilters.Qt.filterAttributes = qtxlsx $QT_VERSION
qhp.QtXlsx.subprojects         = overviews classes qmltypes examples
qhp.QtXlsx.subprojects.overviews.title = Overview
qhp.QtXlsx.subprojects.overviews.indexTitle = Qt Xlsx
qhp.QtXlsx.subprojects.overviews.selectors = fake:page,group,module
qhp.QtXlsx.subprojects.classes.title = C++ Classes
qhp.QtXlsx.subprojects.classes.indexTitle = Qt Xlsx C++ Classes
qhp.QtXlsx.subprojects.classes.selectors = class fake:headerfile
qhp.QtXlsx.subprojects.classes.sortPages = true
qhp.QtXlsx.subprojects.examples.title = Examples
qhp.QtXlsx.subprojects.examples.indexTitle = Qt Xlsx Examples
qhp.QtXlsx.subprojects.examples.selectors = fake:example

tagfile                 = ../../../doc/qtxlsx/qtxlsx.tags

headerdirs  += .. 

sourcedirs  += .. 

exampledirs += ../../../examples/xlsx \
               snippets/

# Specify the install path under QT_INSTALL_EXAMPLES
examplesinstallpath = xlsx

imagedirs   += images

depends += qtcore qtdoc qtgui

HTML.footer = \
    "        </div>\n" \
    "       </div>\n" \
    "   </div>\n" \
    "   </div>\n" \
    "</div>\n" \
    "<div class=\"footer\">\n" \
    "        <div class=\"qt13a-copyright\" id=\"copyright\">\n" \
    "            <div class=\"qt13a-container\">\n" \
    "            <p>\n" \
    "              <acronym title=\"Copyright\">&copy;</acronym> 2013-2014 Debao Zhang. \n" \
    "              Documentation contributions included herein are the copyrights of\n" \
    "              their respective owners.</p>\n" \
    "            <p>\n" \
    "              The documentation provided herein is licensed under the terms of the\n" \
    "              <a href=\"http://www.gnu.org/licenses/fdl.html\">GNU Free Documentation\n" \
    "              License version 1.3</a> as published by the Free Software Foundation.</p>\n" \
    "            <p>\n" \
    "              Documentation sources may be obtained from <a href=\"https://github.com/dbzhang800/QtXlsxWriter\">\n" \
    "              github.com/dbzhang800</a>.</p>\n" \
    "            <p>\n" \
    "              Qt and their respective logos are trademarks of Digia Plc \n" \
    "              in Finland and/or other countries worldwide. All other trademarks are property\n" \
    "              of their respective owners. <a title=\"Privacy Policy\"\n" \
    "              href=\"http://en.gitorious.org/privacy_policy/\">Privacy Policy</a></p>\n" \
    "            </div>\n" \
    "        </div>\n" \
    "</div>\n" \
