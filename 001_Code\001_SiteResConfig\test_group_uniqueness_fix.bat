@echo off
echo ========================================
echo  测试组内名称唯一性修复
echo ========================================

REM 设置Qt环境变量
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目（测试组内名称唯一性修复）...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo 请检查上面的错误信息。
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！组内名称唯一性问题已修复
    echo ========================================
    
    echo.
    echo ✅ 修复的问题:
    echo - 解决了"作动器序列号已存在"的全局唯一性错误
    echo - 解决了"传感器序列号已存在"的全局唯一性错误
    echo - 实现了真正的组内唯一性检查
    echo - 支持不同组间使用相同序列号
    echo.
    echo 🔧 修复方案:
    echo 1. 保留UI层的组内唯一性检查
    echo 2. 修改数据保存逻辑，使用更新而不是添加
    echo 3. 新增saveOrUpdateActuatorDetailedParams()方法
    echo 4. 新增saveOrUpdateSensorDetailedParams()方法
    echo.
    echo 🎯 修复逻辑:
    echo - 如果序列号已存在 → 更新现有记录
    echo - 如果序列号不存在 → 添加新记录
    echo - UI层检查组内唯一性 → 防止同组重复
    echo - 数据层允许全局重复 → 支持跨组同名
    echo.
    echo 📝 修复的方法:
    echo - OnCreateActuator() → 使用saveOrUpdateActuatorDetailedParams()
    echo - OnCreateSensor() → 使用saveOrUpdateSensorDetailedParams()
    echo - 保留组内检查: isActuatorSerialNumberExistsInGroup()
    echo - 保留组内检查: isSensorSerialNumberExistsInGroup()
    echo.
    
    if exist "SiteResConfig.exe" (
        echo 启动程序验证修复...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 启动程序验证修复...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 启动程序验证修复...
        start release\SiteResConfig.exe
    )
)

echo.
echo 📖 详细测试指南:
echo.
echo 🎮 组内唯一性测试:
echo 1. 启动软件后，新建一个项目
echo 2. 创建作动器组A，添加作动器（序列号：ACT001）→ 应该成功
echo 3. 在作动器组A中再次添加作动器（序列号：ACT001）→ 应该提示"组内作动器名称重复"
echo 4. 创建作动器组B，添加作动器（序列号：ACT001）→ 应该成功（不同组）
echo 5. 对传感器组进行相同测试
echo.
echo 🎮 跨组重复测试:
echo 1. 创建多个作动器组
echo 2. 在每个组中使用相同的序列号创建作动器
echo 3. 验证每个组都能成功创建
echo 4. 验证不会出现"序列号已存在"的错误
echo.
echo 🎮 数据一致性测试:
echo 1. 创建相同序列号的设备在不同组中
echo 2. 验证每个设备都有唯一的ID
echo 3. 验证数据管理器正确处理重复序列号
echo 4. 验证UI显示正确
echo.
echo ✅ 预期结果:
echo - 组内不能有相同序列号的设备（UI层检查）
echo - 不同组间可以有相同序列号的设备（数据层支持）
echo - 不再出现"序列号已存在"的全局错误
echo - 设备创建和管理功能正常
echo.
echo 🚨 如果测试失败:
echo - 检查是否还有全局唯一性错误
echo - 验证组内检查是否正常工作
echo - 确认数据更新逻辑正确
echo.
pause
