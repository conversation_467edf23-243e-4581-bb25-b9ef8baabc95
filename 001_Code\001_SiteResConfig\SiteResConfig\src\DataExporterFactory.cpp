#include "DataExporterFactory.h"
#include "JSONDataExporter_1_2.h"
#include "XLSDataExporter_1_2.h"
#include <QFileInfo>

std::unique_ptr<IDataExporter> DataExporterFactory::createExporter(
    ExportFormat format, 
    SensorDataManager_1_2* sensorManager) {
    
    switch (format) {
        case ExportFormat::JSON:
            return std::make_unique<JSONDataExporter_1_2>(sensorManager);
        case ExportFormat::XLS:
            return std::make_unique<XLSDataExporter_1_2>(sensorManager);
        case ExportFormat::Unknown:
        default:
            return nullptr;
    }
}

std::unique_ptr<IDataExporter> DataExporterFactory::createExporterByExtension(
    const QString& extension, 
    SensorDataManager_1_2* sensorManager) {
    
    ExportFormat format = getFormatByExtension(extension);
    return createExporter(format, sensorManager);
}

std::unique_ptr<IDataExporter> DataExporterFactory::createExporterByFilePath(
    const QString& filePath, 
    SensorDataManager_1_2* sensorManager) {
    
    QString extension = extractExtension(filePath);
    return createExporterByExtension(extension, sensorManager);
}

QStringList DataExporterFactory::getSupportedFormats() {
    QStringList formats;
    formats << getFormatDescription(ExportFormat::JSON);
    formats << getFormatDescription(ExportFormat::XLS);
    return formats;
}

QString DataExporterFactory::getFileFilter() {
    QStringList filters;
    filters << getFormatDescription(ExportFormat::JSON);
    filters << getFormatDescription(ExportFormat::XLS);
    filters << QString(u8"所有支持的格式 (*.json *.xlsx)");
    filters << QString(u8"所有文件 (*.*)");

    return filters.join(";;");
}

DataExporterFactory::ExportFormat DataExporterFactory::getFormatByExtension(const QString& extension) {
    QString ext = extension.toLower();

    if (ext == "json") {
        return ExportFormat::JSON;
    } else if (ext == "xlsx" || ext == "xls") {
        return ExportFormat::XLS;
    } else {
        return ExportFormat::Unknown;
    }
}

QString DataExporterFactory::getExtensionByFormat(ExportFormat format) {
    switch (format) {
        case ExportFormat::JSON:
            return "json";
        case ExportFormat::XLS:
            return "xlsx";
        case ExportFormat::Unknown:
        default:
            return "";
    }
}

QString DataExporterFactory::getFormatDescription(ExportFormat format) {
    switch (format) {
        case ExportFormat::JSON:
            return QString(u8"JSON文件 (*.json)");
        case ExportFormat::XLS:
            return QString(u8"Excel文件 (*.xlsx)");
        case ExportFormat::Unknown:
        default:
            return QString(u8"未知格式");
    }
}

QString DataExporterFactory::extractExtension(const QString& filePath) {
    QFileInfo fileInfo(filePath);
    return fileInfo.suffix().toLower();
}
