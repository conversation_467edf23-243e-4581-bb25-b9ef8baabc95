// 🎯 作动器删除快速修复代码片段
// 📁 文件：SiteResConfig/src/MainWindow_Qt_Simple.cpp
// 🔍 定位：OnDeleteActuatorDevice() 函数

// ==========================================
// 🚀 快速修复版本（立即可用）
// ==========================================

void CMyMainWindow::OnDeleteActuatorDevice() {
    QTreeWidgetItem* item = ui->hardwareTreeWidget->currentItem();
    if (!item || !IsActuatorDeviceItem(item)) {
        LogMessage("WARNING", u8"⚠️ 请选择有效的作动器设备节点");
        return;
    }

    QString serialNumber = item->text(0);
    QTreeWidgetItem* groupItem = item->parent();
    if (!groupItem) {
        LogMessage("ERROR", u8"❌ 无法获取作动器组信息");
        return;
    }
    
    QString groupName = groupItem->text(0);
    
    // 🆕 增强：多组存在检测和警告
    QStringList allGroupNames = GetActuatorGroupNamesBySerialNumber(serialNumber);
    if (allGroupNames.size() > 1) {
        QString warning = QString(u8"⚠️ 检测到作动器 %1 在多个组中存在：%2\n"
                                 "当前操作仅会删除 %3 组中的设备，不影响其他组。")
                          .arg(serialNumber)
                          .arg(allGroupNames.join(", "))
                          .arg(groupName);
        LogMessage("WARNING", warning);
    }

    // 🆕 增强：详细的删除确认信息
    QString confirmMessage = QString(u8"确定要删除作动器设备吗？\n\n"
                                    "📋 设备信息：\n"
                                    "• 序列号：%1\n"
                                    "• 所属组：%2\n\n"
                                    "⚠️ 此操作将：\n"
                                    "• 删除该组中的设备数据\n"
                                    "• 清除相关控制通道关联\n"
                                    "• 此操作不可撤销\n\n"
                                    "是否继续？")
                            .arg(serialNumber)
                            .arg(groupName);

    QMessageBox::StandardButton reply = QMessageBox::question(
        this, u8"确认删除作动器", confirmMessage,
        QMessageBox::Yes | QMessageBox::No, QMessageBox::No);
    
    if (reply == QMessageBox::Yes) {
        LogMessage("INFO", QString(u8"🔄 开始删除作动器：组=%1, 序列号=%2").arg(groupName).arg(serialNumber));
        
        // 1. 执行数据删除
        if (actuatorViewModel1_2_->deleteActuatorDevice(serialNumber)) {
            LogMessage("SUCCESS", QString(u8"✅ 作动器设备删除成功"));
            
            // 🆕 修复1：直接移除UI节点（解决节点不消失问题）
            groupItem->removeChild(item);
            delete item;  // 释放内存
            LogMessage("INFO", u8"✅ UI节点移除完成");
            
            // 🆕 修复2：精准清理控制通道关联（解决过度清理问题）
            // 替换原来的多组循环清理，改为精准清理当前组
            UpdateControlChannelAssociationsAfterActuatorDelete(serialNumber, groupName);
            LogMessage("INFO", QString(u8"🎯 精准清理控制通道关联：仅限 %1 组").arg(groupName));
            
            // 🔄 刷新测试配置树显示，确保关联信息列同步更新
            RefreshTestConfigTreeFromDataManagers();
            
            // 🆕 新增：更新所有tooltip
            UpdateAllTreeWidgetTooltips();
            
            LogMessage("SUCCESS", QString(u8"🎉 作动器删除操作完成：%1").arg(serialNumber));
            
            // 🔄 保持原有的硬件树刷新作为备份（可选，性能考虑可以注释掉）
            RefreshHardwareTreeFromDataManagers();
            refreshAllDataFromManagers();
            
        } else {
            // 获取详细错误信息
            QString errorMessage = actuatorViewModel1_2_->getLastError();
            if (errorMessage.isEmpty()) {
                errorMessage = u8"未知错误";
            }
            LogMessage("ERROR", QString(u8"❌ 作动器设备删除失败：%1").arg(errorMessage));
            
            // 显示错误对话框给用户
            QMessageBox::warning(this, u8"删除失败", 
                QString(u8"删除作动器设备 '%1' 失败：\n%2").arg(serialNumber).arg(errorMessage));
        }
    } else {
        LogMessage("INFO", u8"📝 作动器删除已取消");
    }
}

// ==========================================
// 🔧 关键修改说明
// ==========================================

/*
核心修复点：

1. 🎯 节点删除修复：
   - 添加了 groupItem->removeChild(item); delete item; 
   - 确保UI节点立即从树中移除
   - 解决"删除后节点仍显示"的问题

2. 🎯 精准关联清理：
   - 移除了多组循环清理逻辑
   - 改为只清理当前组的关联：UpdateControlChannelAssociationsAfterActuatorDelete(serialNumber, groupName)
   - 解决"删除一个清理两个"的问题

3. 🎯 用户体验增强：
   - 添加了多组存在检测和警告
   - 增强了删除确认对话框信息
   - 提供了更详细的操作日志

4. 🎯 性能优化：
   - 优先使用局部刷新 RefreshTestConfigTreeFromDataManagers()
   - 全量刷新作为备份保留

预期效果：
✅ 作动器删除后节点立即消失
✅ 只清理对应组的控制通道关联信息
✅ 其他组的同序列号设备不受影响
✅ 更好的用户提示和确认体验
*/

// ==========================================
// 🧪 测试验证步骤
// ==========================================

/*
1. 单组删除测试：
   - 创建一个作动器组，添加作动器
   - 删除作动器，确认节点消失
   - 检查控制通道关联是否正确清理

2. 多组相同序列号测试：
   - 创建两个组，都添加序列号"A001"的作动器
   - 删除第一个组的"A001"，确认：
     * 第一个组的节点消失
     * 第二个组的"A001"节点仍存在
     * 只清理了第一个组相关的控制通道关联

3. 控制通道关联测试：
   - 设置控制通道关联到作动器（如"组1-A001"）
   - 删除作动器，确认关联被清理
   - 确保其他无关关联不受影响
*/ 