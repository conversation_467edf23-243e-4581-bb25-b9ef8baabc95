<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1400</width>
    <height>900</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>1200</width>
    <height>800</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>SiteResConfig - 灵动加载上位机管理软件</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout_4">
    <item>
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QGroupBox" name="groupBox">
        <property name="maximumSize">
         <size>
          <width>400</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="title">
         <string>硬件资源</string>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout">
         <item>
          <widget class="QTreeWidget" name="hardwareTreeWidget">
           <property name="alternatingRowColors">
            <bool>true</bool>
           </property>
           <property name="selectionMode">
            <enum>QAbstractItemView::SingleSelection</enum>
           </property>
           <property name="indentation">
            <number>20</number>
           </property>
           <property name="rootIsDecorated">
            <bool>false</bool>
           </property>
           <property name="itemsExpandable">
            <bool>true</bool>
           </property>
           <property name="animated">
            <bool>true</bool>
           </property>
           <property name="headerHidden">
            <bool>true</bool>
           </property>
           <column>
            <property name="text">
             <string>硬件资源</string>
            </property>
           </column>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QGroupBox" name="groupBox_2">
        <property name="title">
         <string>试验资源</string>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout">
         <item>
          <widget class="CustomTestConfigTreeWidget" name="testConfigTreeWidget">
           <property name="alternatingRowColors">
            <bool>true</bool>
           </property>
           <property name="selectionMode">
            <enum>QAbstractItemView::SingleSelection</enum>
           </property>
           <property name="indentation">
            <number>20</number>
           </property>
           <property name="rootIsDecorated">
            <bool>false</bool>
           </property>
           <property name="animated">
            <bool>true</bool>
           </property>
           <property name="headerHidden">
            <bool>false</bool>
           </property>
           <column>
            <property name="text">
             <string>试验配置</string>
            </property>
           </column>
           <column>
            <property name="text">
             <string>关联信息</string>
            </property>
           </column>
           <column>
            <property name="text">
             <string>下位机id</string>
            </property>
           </column>
           <column>
            <property name="text">
             <string>站点id</string>
            </property>
           </column>
           <column>
            <property name="text">
             <string>使能</string>
            </property>
           </column>
           <column>
            <property name="text">
             <string>极性</string>
            </property>
           </column>
          </widget>
         </item>
         <item>
          <widget class="QGroupBox" name="detailInfoGroupBox">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Fixed" vsizetype="Expanding">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>120</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="title">
            <string>详细信息</string>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_detail">
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>0</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <item>
             <widget class="QCheckBox" name="chkShowDetailInfoDlg">
              <property name="text">
               <string>显示详细信息</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QWidget" name="detailInfoWidget" native="true">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>400</height>
               </size>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="verticalSpacer">
              <property name="orientation">
               <enum>Qt::Vertical</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>0</width>
                <height>0</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </item>
    <item>
     <widget class="QSplitter" name="mainSplitter">
      <property name="orientation">
       <enum>Qt::Horizontal</enum>
      </property>
      <property name="handleWidth">
       <number>8</number>
      </property>
      <property name="childrenCollapsible">
       <bool>false</bool>
      </property>
      <widget class="QWidget" name="rightPanel">
       <property name="minimumSize">
        <size>
         <width>700</width>
         <height>0</height>
        </size>
       </property>
       <layout class="QVBoxLayout" name="rightPanelLayout">
        <property name="spacing">
         <number>8</number>
        </property>
        <property name="leftMargin">
         <number>4</number>
        </property>
        <property name="topMargin">
         <number>4</number>
        </property>
        <property name="rightMargin">
         <number>4</number>
        </property>
        <property name="bottomMargin">
         <number>4</number>
        </property>
        <item>
         <widget class="QGroupBox" name="groupBox_3">
          <property name="title">
           <string>日志</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_3">
           <item>
            <layout class="QHBoxLayout" name="logToolbarLayout">
             <property name="spacing">
              <number>8</number>
             </property>
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item>
              <widget class="QPushButton" name="clearLogButton">
               <property name="toolTip">
                <string>清空显示的日志</string>
               </property>
               <property name="text">
                <string>清空日志</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="saveLogButton">
               <property name="toolTip">
                <string>保存日志到文件</string>
               </property>
               <property name="text">
                <string>保存日志</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="logToolbarSpacer">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </item>
           <item>
            <widget class="QTextEdit" name="logTextEdit">
             <property name="font">
              <font>
               <family>Consolas</family>
               <pointsize>9</pointsize>
              </font>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
             <property name="html">
              <string>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:'Consolas'; font-size:9pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p style=&quot;-qt-paragraph-type:empty; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;br /&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1400</width>
     <height>23</height>
    </rect>
   </property>
   <widget class="QMenu" name="menuFile">
    <property name="title">
     <string>文件(&amp;F)</string>
    </property>
    <addaction name="actionNewProject"/>
    <addaction name="actionOpenProject"/>
    <addaction name="separator"/>
    <addaction name="actionSaveProject"/>
    <addaction name="actionSaveAsProject"/>
    <addaction name="separator"/>
    <addaction name="actionRecentProjects"/>
    <addaction name="separator"/>
    <addaction name="actionExit"/>
   </widget>
   <widget class="QMenu" name="menuHardware">
    <property name="title">
     <string>硬件(&amp;H)</string>
    </property>
   </widget>
   <widget class="QMenu" name="menuTest">
    <property name="title">
     <string>试验(&amp;T)</string>
    </property>
    <addaction name="separator"/>
    <addaction name="actionTestConfig"/>
   </widget>
   <widget class="QMenu" name="menuView">
    <property name="title">
     <string>视图(&amp;V)</string>
    </property>
    <addaction name="actionShowHardwarePanel"/>
    <addaction name="actionShowTestPanel"/>
    <addaction name="separator"/>
    <addaction name="actionFullScreen"/>
   </widget>
   <widget class="QMenu" name="menuTools">
    <property name="title">
     <string>工具(&amp;T)</string>
    </property>
    <addaction name="actionSystemSettings"/>
    <addaction name="actionCalibration"/>
    <addaction name="separator"/>
   </widget>
   <widget class="QMenu" name="menuHelp">
    <property name="title">
     <string>帮助(&amp;H)</string>
    </property>
    <addaction name="actionUserManual"/>
    <addaction name="actionTechnicalSupport"/>
    <addaction name="separator"/>
    <addaction name="actionRestoreColors"/>
    <addaction name="separator"/>
    <addaction name="actionAbout"/>
   </widget>
   <addaction name="menuFile"/>
   <addaction name="menuHardware"/>
   <addaction name="menuTest"/>
   <addaction name="menuView"/>
   <addaction name="menuTools"/>
   <addaction name="menuHelp"/>
  </widget>
  <widget class="QStatusBar" name="statusbar">
   <widget class="QLabel" name="statusLabel">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>100</width>
      <height>30</height>
     </rect>
    </property>
    <property name="text">
     <string>就绪</string>
    </property>
   </widget>
  </widget>
  <action name="actionNewProject">
   <property name="text">
    <string>新建工程(&amp;N)</string>
   </property>
   <property name="toolTip">
    <string>创建新的试验工程</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+N</string>
   </property>
  </action>
  <action name="actionOpenProject">
   <property name="text">
    <string>打开工程(&amp;O)</string>
   </property>
   <property name="toolTip">
    <string>打开已有的试验工程</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+O</string>
   </property>
  </action>
  <action name="actionSaveProject">
   <property name="text">
    <string>保存工程(&amp;S)</string>
   </property>
   <property name="toolTip">
    <string>保存当前工程</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+S</string>
   </property>
  </action>
  <action name="actionSaveAsProject">
   <property name="text">
    <string>导出工程(&amp;E)...</string>
   </property>
   <property name="toolTip">
    <string>导出试验配置为JSON文件</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+Shift+S</string>
   </property>
  </action>
  <action name="actionRecentProjects">
   <property name="text">
    <string>最近工程(&amp;R)</string>
   </property>
   <property name="toolTip">
    <string>打开最近使用的工程</string>
   </property>
  </action>
  <action name="actionExit">
   <property name="text">
    <string>退出(&amp;X)</string>
   </property>
   <property name="toolTip">
    <string>退出应用程序</string>
   </property>
   <property name="shortcut">
    <string>Alt+F4</string>
   </property>
  </action>
  <action name="actionTestConfig">
   <property name="text">
    <string>试验配置(&amp;C)</string>
   </property>
   <property name="toolTip">
    <string>配置试验参数</string>
   </property>
  </action>
  <action name="actionShowHardwarePanel">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="checked">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>硬件面板(&amp;H)</string>
   </property>
   <property name="toolTip">
    <string>显示/隐藏硬件面板</string>
   </property>
  </action>
  <action name="actionShowTestPanel">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="checked">
    <bool>true</bool>
   </property>
   <property name="text">
    <string>试验面板(&amp;T)</string>
   </property>
   <property name="toolTip">
    <string>显示/隐藏试验面板</string>
   </property>
  </action>
  <action name="actionFullScreen">
   <property name="text">
    <string>全屏模式(&amp;F)</string>
   </property>
   <property name="toolTip">
    <string>切换全屏模式</string>
   </property>
   <property name="shortcut">
    <string>F11</string>
   </property>
  </action>
  <action name="actionSystemSettings">
   <property name="text">
    <string>系统设置(&amp;S)</string>
   </property>
   <property name="toolTip">
    <string>配置系统设置</string>
   </property>
  </action>
  <action name="actionCalibration">
   <property name="text">
    <string>传感器标定(&amp;C)</string>
   </property>
   <property name="toolTip">
    <string>传感器标定功能</string>
   </property>
  </action>
  <action name="actionUserManual">
   <property name="text">
    <string>用户手册(&amp;U)</string>
   </property>
   <property name="toolTip">
    <string>查看用户手册</string>
   </property>
   <property name="shortcut">
    <string>F1</string>
   </property>
  </action>
  <action name="actionTechnicalSupport">
   <property name="text">
    <string>技术支持(&amp;T)</string>
   </property>
   <property name="toolTip">
    <string>联系技术支持</string>
   </property>
  </action>
  <action name="actionRestoreColors">
   <property name="text">
    <string>恢复颜色(&amp;R)</string>
   </property>
   <property name="toolTip">
    <string>恢复所有树形控件的颜色</string>
   </property>
  </action>
  <action name="actionAbout">
   <property name="text">
    <string>关于(&amp;A)</string>
   </property>
   <property name="toolTip">
    <string>关于本软件</string>
   </property>
  </action>
  <action name="actionNodeLD_B1">
   <property name="text">
    <string>LD-B1</string>
   </property>
   <property name="toolTip">
    <string>配置LD-B1节点</string>
   </property>
  </action>
  <action name="actionNodeLD_B2">
   <property name="text">
    <string>LD-B2</string>
   </property>
   <property name="toolTip">
    <string>配置LD-B2节点</string>
   </property>
  </action>
 </widget>
 <customwidgets>
  <customwidget>
   <class>CustomTestConfigTreeWidget</class>
   <extends>QTreeWidget</extends>
   <header location="global">CustomTreeWidgets.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
