# SiteResConfig 应用启动脚本

Write-Host "========================================" -ForegroundColor Green
Write-Host " 启动 SiteResConfig 应用程序" -ForegroundColor Green  
Write-Host "========================================" -ForegroundColor Green

Write-Host "当前目录: $(Get-Location)"
Write-Host ""

# 检查构建目录
$buildDir = "build-SiteResConfig_Simple-Desktop_Qt_5_14_2_MinGW_32_bit-Debug"
$appPath = "$buildDir\debug\SiteResConfig.exe"

if (-not (Test-Path $buildDir)) {
    Write-Host "❌ 错误: 找不到构建目录" -ForegroundColor Red
    Write-Host "请确保您在正确的项目目录中运行此脚本" -ForegroundColor Yellow
    Write-Host "当前目录应该包含: $buildDir" -ForegroundColor Yellow
    Read-Host "按回车键退出"
    exit 1
}

Write-Host "✅ 找到构建目录" -ForegroundColor Green

if (Test-Path $appPath) {
    Write-Host "✅ 找到应用程序: $appPath" -ForegroundColor Green
    Write-Host ""
    Write-Host "🔍 调试说明:" -ForegroundColor Cyan
    Write-Host "- 如果添加了调试代码，控制台会显示调试信息" -ForegroundColor White
    Write-Host "- 拖拽硬件节点时，观察控制台输出" -ForegroundColor White
    Write-Host "- 查找以 '=== dragMoveEvent START ===' 开头的消息" -ForegroundColor White
    Write-Host ""
    Write-Host "启动应用程序..." -ForegroundColor Yellow
    
    # 启动应用程序
    & $appPath
    
    Write-Host ""
    Write-Host "应用程序已关闭" -ForegroundColor Green
    
} else {
    Write-Host "❌ 找不到应用程序文件" -ForegroundColor Red
    Write-Host "预期位置: $appPath" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "请检查:" -ForegroundColor Yellow
    Write-Host "1. 项目是否已成功编译" -ForegroundColor White
    Write-Host "2. 构建配置是否正确 (Debug)" -ForegroundColor White
    Write-Host "3. 可执行文件是否在预期位置" -ForegroundColor White
    Write-Host ""
    
    $debugDir = "$buildDir\debug"
    if (Test-Path $debugDir) {
        Write-Host "当前 debug 目录内容:" -ForegroundColor Cyan
        Get-ChildItem $debugDir | Format-Table Name, Length -AutoSize
    } else {
        Write-Host "debug 目录不存在" -ForegroundColor Red
    }
}

Write-Host ""
Read-Host "按回车键退出"
