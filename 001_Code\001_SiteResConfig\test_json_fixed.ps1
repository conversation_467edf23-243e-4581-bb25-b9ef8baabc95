# JSON导出功能修复验证脚本
Write-Host "========================================" -ForegroundColor Green
Write-Host " JSON导出功能修复验证" -ForegroundColor Green  
Write-Host "========================================" -ForegroundColor Green

# 设置工作目录
$workDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $workDir

Write-Host "工作目录: $workDir" -ForegroundColor Yellow

# 检查修复状态
Write-Host ""
Write-Host "检查修复状态..." -ForegroundColor Cyan

# 1. 检查项目文件修复
$proFile = "SiteResConfig\SiteResConfig_Simple.pro"
if (Test-Path $proFile) {
    $proContent = Get-Content $proFile
    $jsonLine = $proContent | Where-Object { $_ -match "QT.*json" }
    
    if ($jsonLine) {
        Write-Host "❌ 项目文件仍包含json模块: $jsonLine" -ForegroundColor Red
    } else {
        Write-Host "✅ 项目文件已修复：移除了json模块" -ForegroundColor Green
    }
    
    $qtLine = $proContent | Where-Object { $_ -match "QT.*core.*widgets" }
    if ($qtLine) {
        Write-Host "✅ Qt模块配置正确: $qtLine" -ForegroundColor Green
    }
}

# 2. 检查头文件
$mainWindowFile = "SiteResConfig\src\MainWindow_Qt_Simple.cpp"
if (Test-Path $mainWindowFile) {
    $mainContent = Get-Content $mainWindowFile
    $jsonHeaders = $mainContent | Where-Object { $_ -match "#include.*QJson" }
    
    if ($jsonHeaders.Count -gt 0) {
        Write-Host "✅ MainWindow已包含JSON头文件:" -ForegroundColor Green
        foreach ($header in $jsonHeaders) {
            Write-Host "   $header" -ForegroundColor Gray
        }
    } else {
        Write-Host "❌ MainWindow缺少JSON头文件" -ForegroundColor Red
    }
}

# 3. 检查可执行文件
$exePath = "build-SiteResConfig_Simple-Desktop_Qt_5_14_2_MinGW_32_bit-Debug\debug\SiteResConfig.exe"
if (Test-Path $exePath) {
    $fileInfo = Get-Item $exePath
    Write-Host "✅ 可执行文件存在: $($fileInfo.Name)" -ForegroundColor Green
    Write-Host "   文件大小: $([math]::Round($fileInfo.Length / 1MB, 2)) MB" -ForegroundColor Gray
    Write-Host "   修改时间: $($fileInfo.LastWriteTime)" -ForegroundColor Gray
} else {
    Write-Host "❌ 可执行文件不存在: $exePath" -ForegroundColor Red
}

# 4. 检查示例文件
$sampleJson = "SiteResConfig\sample_configs\hardware_config.json"
$sampleCsv = "SiteResConfig\sample_configs\hardware_config.csv"

if (Test-Path $sampleJson) {
    Write-Host "✅ 示例JSON文件存在" -ForegroundColor Green
}

if (Test-Path $sampleCsv) {
    Write-Host "✅ 示例CSV文件存在" -ForegroundColor Green
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host " JSON导出功能测试说明" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "修复内容:" -ForegroundColor White
Write-Host "1. 移除了项目文件中错误的json模块引用" -ForegroundColor White
Write-Host "2. 添加了必要的JSON头文件到MainWindow" -ForegroundColor White
Write-Host "3. 保持了所有JSON导出功能的完整性" -ForegroundColor White
Write-Host ""
Write-Host "测试步骤:" -ForegroundColor White
Write-Host "1. 启动应用程序" -ForegroundColor White
Write-Host "2. 创建一些测试数据" -ForegroundColor White
Write-Host "3. 使用导出功能选择JSON格式" -ForegroundColor White
Write-Host "4. 验证先保存CSV再转换JSON的流程" -ForegroundColor White
Write-Host "5. 检查生成的JSON文件格式" -ForegroundColor White
Write-Host ""

# 询问是否启动应用程序
$response = Read-Host "是否启动应用程序进行测试? (y/n)"
if ($response -eq 'y' -or $response -eq 'Y') {
    if (Test-Path $exePath) {
        Write-Host "启动应用程序..." -ForegroundColor Yellow
        Start-Process -FilePath $exePath -WorkingDirectory (Split-Path $exePath)
        Write-Host "应用程序已启动！请测试JSON导出功能" -ForegroundColor Green
    } else {
        Write-Host "无法启动：可执行文件不存在" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "修复完成！JSON导出功能现在应该可以正常工作。" -ForegroundColor Green
Write-Host "按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
