/* ============================================================================
   QComboBox 样式方案集合 - 多种风格可选
   ============================================================================ */

/* 方案1：现代化扁平风格 (已应用到主样式表) */
/* 特点：大字体、大内边距、圆角、阴影效果 */

/* 方案2：Material Design 风格 */
QComboBox.material {
    font-family: "Microsoft YaHei", "微软雅黑", sans-serif;
    font-size: 12pt;
    color: #212121;
    background-color: #FFFFFF;
    border: none;
    border-bottom: 2px solid #E0E0E0;
    border-radius: 4px 4px 0 0;
    padding: 12px 16px;
    min-width: 200px;
    min-height: 40px;
}

QComboBox.material:hover {
    border-bottom-color: #2196F3;
    background-color: #FAFAFA;
}

QComboBox.material:focus {
    border-bottom-color: #1976D2;
    background-color: #FFFFFF;
    outline: none;
}

QComboBox.material::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 24px;
    border: none;
    background: transparent;
}

QComboBox.material::down-arrow {
    image: none;
    border: none;
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 8px solid #757575;
}

/* 方案3：Windows 11 风格 */
QComboBox.win11 {
    font-family: "Segoe UI", "Microsoft YaHei", sans-serif;
    font-size: 10pt;
    color: #323130;
    background-color: #FFFFFF;
    border: 1px solid #8A8886;
    border-radius: 4px;
    padding: 8px 12px;
    min-width: 120px;
    min-height: 32px;
}

QComboBox.win11:hover {
    border-color: #605E5C;
    background-color: #F3F2F1;
}

QComboBox.win11:focus {
    border-color: #0078D4;
    background-color: #FFFFFF;
    outline: none;
    box-shadow: inset 0 0 0 1px #0078D4;
}

QComboBox.win11::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 20px;
    border: none;
    background: transparent;
}

QComboBox.win11::down-arrow {
    image: none;
    border: none;
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 6px solid #605E5C;
}

/* 方案4：macOS 风格 */
QComboBox.macos {
    font-family: "SF Pro Display", "Microsoft YaHei", sans-serif;
    font-size: 11pt;
    color: #1D1D1F;
    background-color: #FFFFFF;
    border: 1px solid #D2D2D7;
    border-radius: 8px;
    padding: 10px 14px;
    min-width: 140px;
    min-height: 36px;
}

QComboBox.macos:hover {
    border-color: #007AFF;
    background-color: #F9F9F9;
}

QComboBox.macos:focus {
    border-color: #007AFF;
    background-color: #FFFFFF;
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.2);
}

QComboBox.macos::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 24px;
    border: none;
    background: transparent;
}

QComboBox.macos::down-arrow {
    image: none;
    border: none;
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 7px solid #8E8E93;
}

/* 方案5：深色主题风格 */
QComboBox.dark {
    font-family: "Microsoft YaHei", "微软雅黑", sans-serif;
    font-size: 11pt;
    color: #FFFFFF;
    background-color: #2D2D30;
    border: 2px solid #3F3F46;
    border-radius: 6px;
    padding: 10px 15px;
    min-width: 150px;
    min-height: 35px;
}

QComboBox.dark:hover {
    border-color: #007ACC;
    background-color: #383838;
}

QComboBox.dark:focus {
    border-color: #0E639C;
    background-color: #2D2D30;
    outline: none;
    box-shadow: 0 0 0 2px rgba(14, 99, 156, 0.3);
}

QComboBox.dark::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 28px;
    border-left: 1px solid #3F3F46;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
    background-color: #383838;
}

QComboBox.dark::down-arrow {
    image: none;
    border: none;
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 7px solid #CCCCCC;
}

QComboBox.dark QAbstractItemView {
    background-color: #2D2D30;
    border: 2px solid #007ACC;
    border-radius: 6px;
    selection-background-color: #007ACC;
    selection-color: #FFFFFF;
    color: #FFFFFF;
}

/* 方案6：彩色主题风格 */
QComboBox.colorful {
    font-family: "Microsoft YaHei", "微软雅黑", sans-serif;
    font-size: 11pt;
    color: #FFFFFF;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #667eea, stop:1 #764ba2);
    border: none;
    border-radius: 10px;
    padding: 12px 18px;
    min-width: 160px;
    min-height: 40px;
}

QComboBox.colorful:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #5a6fd8, stop:1 #6a4190);
}

QComboBox.colorful:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
}

QComboBox.colorful::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 30px;
    border: none;
    background: transparent;
}

QComboBox.colorful::down-arrow {
    image: none;
    border: none;
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 8px solid #FFFFFF;
}

/* 使用方法说明：
   在代码中为特定的QComboBox设置样式类：
   
   // 应用Material Design风格
   comboBox->setProperty("class", "material");
   
   // 应用Windows 11风格
   comboBox->setProperty("class", "win11");
   
   // 应用macOS风格
   comboBox->setProperty("class", "macos");
   
   // 应用深色主题
   comboBox->setProperty("class", "dark");
   
   // 应用彩色主题
   comboBox->setProperty("class", "colorful");
*/
