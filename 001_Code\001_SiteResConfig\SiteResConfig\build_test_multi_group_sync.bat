@echo off
echo ========================================
echo 作动器多组同步功能测试编译脚本
echo ========================================

cd /d "%~dp0"

echo 🔧 清理旧的编译文件...
if exist test_actuator_multi_group_sync.exe del test_actuator_multi_group_sync.exe

echo 🔨 编译测试程序...
g++ -std=c++17 -I"include" -I"C:/Qt/Qt5.12.12/5.12.12/mingw73_64/include" ^
    -I"C:/Qt/Qt5.12.12/5.12.12/mingw73_64/include/QtCore" ^
    -I"C:/Qt/Qt5.12.12/5.12.12/mingw73_64/include/QtWidgets" ^
    -I"C:/Qt/Qt5.12.12/5.12.12/mingw73_64/include/QtGui" ^
    test_actuator_multi_group_sync.cpp ^
    src/ActuatorDataManager_1_2.cpp ^
    src/ActuatorViewModel_1_2.cpp ^
    -L"C:/Qt/Qt5.12.12/5.12.12/mingw73_64/lib" ^
    -lQt5Core -lQt5Gui -lQt5Widgets ^
    -o test_actuator_multi_group_sync.exe

if %ERRORLEVEL% neq 0 (
    echo ❌ 编译失败！
    pause
    exit /b 1
)

echo ✅ 编译成功！

echo 🚀 运行测试程序...
echo.
test_actuator_multi_group_sync.exe

echo.
echo ========================================
echo 测试完成！
echo ========================================
pause 