#ifndef DETAILINFOPANEL_H
#define DETAILINFOPANEL_H

#include <QWidget>
#include <QTableWidget>
#include <QLabel>
#include <QPushButton>
#include <QGroupBox>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QMap>
#include <QList>
#include <QDateTime>
#include <QTimer>
#include <QElapsedTimer>
#include <QMessageBox>
#include <QApplication>
#include <QHeaderView>
#include <QTableWidgetItem>
#include <QTreeWidgetItem>  // 🆕 新增：支持QTreeWidgetItem
#include <QColor>
#include <QFont>
#include <QSize>
#include <QString>
#include <QVariant>

// 🆕 新增：包含数据模型头文件
#include "DataModels_Fixed.h"

// 🆕 新增：包含BasicInfoWidget头文件
#include "BasicInfoWidget.h"

// 前向声明
class QDockWidget;

// 🆕 新增：BasicInfoWidget中定义的数据结构可直接使用

// 详细信息面板主类
class DetailInfoPanel : public QWidget {
    Q_OBJECT
    
public:
    explicit DetailInfoPanel(QWidget *parent = nullptr);
    ~DetailInfoPanel();
    
    // 公共接口
    void setNodeInfo(const NodeInfo& nodeInfo);
    void clearInfo();
    void updateStatus(NodeStatus status);
    
    // 🆕 修改：更安全的getCurrentNodeInfo方法
    NodeInfo getCurrentNodeInfo() const { 
        // 返回当前节点信息的副本，避免外部修改
        return m_currentNodeInfo; 
    }
    
    // 🆕 新增：检查当前节点信息是否有效
    bool hasValidNodeInfo() const {
        return !m_currentNodeInfo.nodeName.isEmpty() || !m_currentNodeInfo.nodeType.isEmpty();
    }
    
    // 🆕 新增：设置测试数据
    void setTestData();
    
    // 🆕 新增：创建控制通道节点信息的静态方法
    static NodeInfo createControlChannelNodeInfo(const QString& channelName,
                                               const QString& channelId,
                                               const UI::ControlChannelParams& channelParams);
    
    // 🆕 新增：创建测试用的控制通道节点信息
    static NodeInfo createTestControlChannelNodeInfo();
    
    // 🆕 新增：设置控制通道信息
    void setControlChannelInfo(const QString& channelName, const QList<SubNodeInfo>& subNodes);
    
    // 🚫 已移除：HTML格式详细信息功能
    
    // 🆕 新增：设置表格选中行
    void setSelectedRow(int row);
    
    // 🆕 新增：设置控制通道根节点信息
    void setControlChannelRootInfo(const QString& rootName, 
                                  const QList<QTreeWidgetItem*>& childChannels);
    
    // 🆕 新增：添加控制通道行
    void addControlChannelRow(int row, const SubNodeInfo& channel);
    
    // 🆕 新增：添加控制子节点行
    void addControlSubNodeRow(int row, const QString& channelName, const SubNodeInfo& subNode);
    
private:
    // 界面初始化
    void initUI();
    void setupConnections();
    void applyStyles();
    
    // 🆕 修改：移除createBasicInfoSection方法，使用BasicInfoWidget
    
    // 🆕 新增：创建其他界面组件的方法
    void createOtherSections(QVBoxLayout* mainLayout);
    
    // 表格创建
    QTableWidget* createBasicInfoTable();
    
    // 数据更新
    void updateAllTables(const NodeInfo& nodeInfo);
    void updateLabels(const NodeInfo& nodeInfo);  // 🆕 新增：更新标签控件
    void updateBasicInfoTable(const NodeInfo& nodeInfo);
    void updateUIStatus();
    void updateStatusIndicator(NodeStatus status);
    
    // 辅助方法
    QString getStatusText(NodeStatus status);
    QString getStatusStateText(NodeStatus status);
    QString getPolarityText(int polarity);
    void adjustTableColumns();
    
    // 事件处理
    void resizeEvent(QResizeEvent* event) override;
    
private slots:
    
private:
    // 🆕 新增：BasicInfoWidget实例
    BasicInfoWidget* m_basicInfoWidget;
    
    // 🆕 修改：移除重复的UI组件声明，使用BasicInfoWidget中的
    
    // 数据
    NodeInfo m_currentNodeInfo;
};

#endif // DETAILINFOPANEL_H 