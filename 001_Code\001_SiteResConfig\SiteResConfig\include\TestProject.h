#pragma once

/**
 * @file TestProject.h
 * @brief 试验工程数据模型
 * @details 定义试验工程的完整数据结构和管理功能
 * <AUTHOR> Assistant
 * @date 2025-08-05
 * @version 1.0.0
 */

#include "DataModels.h"
#include <map>

namespace DataModels {

/**
 * @brief 载荷谱数据点
 * @details 表示载荷谱中的一个数据点
 */
struct LoadSpectrumPoint {
    double time;           // 时间 (s)
    double position;       // 位置 (mm)
    double velocity;       // 速度 (mm/s)
    double force;          // 载荷 (N)
    
    LoadSpectrumPoint() : time(0.0), position(0.0), velocity(0.0), force(0.0) {}
    LoadSpectrumPoint(double t, double pos, double vel, double f) 
        : time(t), position(pos), velocity(vel), force(f) {}
};

/**
 * @brief 载荷谱信息
 * @details 表示一个完整的载荷谱
 */
struct LoadSpectrum : public IDataModel {
    StringType spectrumId;         // 载荷谱ID
    StringType spectrumName;       // 载荷谱名称
    StringType spectrumType;       // 载荷谱类型（一般谱/静力谱/疲劳谱）
    StringType controlVariable;    // 控制变量（时间/位置/速度）
    
    std::vector<LoadSpectrumPoint> dataPoints; // 数据点列表
    
    // 谱参数
    double duration;               // 持续时间 (s)
    double maxValue;               // 最大值
    double minValue;               // 最小值
    int cycleCount;                // 循环次数
    
    LoadSpectrum() : duration(0.0), maxValue(0.0), minValue(0.0), cycleCount(1) {}
    
    json ToJson() const override;
    bool FromJson(const json& jsonData) override;
    bool IsValid() const override;
    
    /**
     * @brief 添加数据点
     * @param point 数据点
     */
    void AddDataPoint(const LoadSpectrumPoint& point);
    
    /**
     * @brief 清空数据点
     */
    void ClearDataPoints();
    
    /**
     * @brief 获取指定时间的插值数据
     * @param time 时间
     * @return 插值后的数据点
     */
    LoadSpectrumPoint GetInterpolatedPoint(double time) const;
};

/**
 * @brief 安全门限配置
 * @details 表示一个通道的安全门限配置
 */
struct SafetyLimitConfig : public IDataModel {
    StringType channelId;          // 关联的通道ID
    
    // 载荷门限
    struct LoadLimit {
        bool enabled;              // 使能
        double lowerLimit;         // 下限
        double upperLimit;         // 上限
        double duration;           // 持续时间 (s)
        StringType action;         // 触发动作
        
        LoadLimit() : enabled(false), lowerLimit(0.0), upperLimit(0.0), duration(0.0) {}
    };
    
    LoadLimit loadLimit1;          // 载荷门限1级
    LoadLimit loadLimit2;          // 载荷门限2级
    LoadLimit loadLimit3;          // 载荷门限3级
    
    // 位置门限
    LoadLimit positionLimit1;      // 位置门限1级
    LoadLimit positionLimit2;      // 位置门限2级
    LoadLimit positionLimit3;      // 位置门限3级
    
    // 误差门限
    LoadLimit errorLimit1;         // 误差门限1级
    LoadLimit errorLimit2;         // 误差门限2级
    LoadLimit errorLimit3;         // 误差门限3级
    
    // 踏步设定
    struct PacingConfig {
        bool enabled;              // 使能
        double threshold;          // 门限
        double duration;           // 持续时间 (s)
        double timeout;            // 超时时间 (s)
        StringType action;         // 触发动作
        
        PacingConfig() : enabled(false), threshold(0.0), duration(0.0), timeout(0.0) {}
    };
    
    PacingConfig staticPacing;     // 静踏步
    PacingConfig dynamicPacing;    // 动踏步
    
    SafetyLimitConfig() {}
    
    json ToJson() const override;
    bool FromJson(const json& jsonData) override;
    bool IsValid() const override;
};

/**
 * @brief 试验工程
 * @details 表示一个完整的试验工程配置
 */
class TestProject : public IDataModel {
public:
    // 基本信息
    StringType projectId;          // 工程ID
    StringType projectName;        // 工程名称
    StringType projectPath;        // 工程路径
    StringType description;        // 描述
    StringType createdDate;        // 创建日期
    StringType modifiedDate;       // 修改日期
    StringType version;            // 版本号
    
    // 硬件资源
    std::map<int, HardwareNode> hardwareNodes;           // 硬件节点映射
    std::map<StringType, ActuatorInfo> actuators;        // 作动器映射
    std::map<StringType, SensorInfo> sensors;            // 传感器映射
    
    // 试验配置
    std::map<StringType, LoadControlChannel> channels;   // 加载控制通道映射
    std::map<StringType, LoadSpectrum> loadSpectrums;    // 载荷谱映射
    std::map<StringType, SafetyLimitConfig> safetyConfigs; // 安全配置映射
    
    // 系统配置
    double sampleRate;             // 采样率 (Hz)
    double controlPeriod;          // 控制周期 (ms)
    bool autoSave;                 // 自动保存
    int autoSaveInterval;          // 自动保存间隔 (s)
    
public:
    TestProject();
    virtual ~TestProject() = default;
    
    // IDataModel接口实现
    json ToJson() const override;
    bool FromJson(const json& jsonData) override;
    bool IsValid() const override;
    
    // 硬件资源管理
    bool AddHardwareNode(const HardwareNode& node);
    bool RemoveHardwareNode(int nodeId);
    HardwareNode* GetHardwareNode(int nodeId);
    
    bool AddActuator(const ActuatorInfo& actuator);
    bool RemoveActuator(const StringType& actuatorId);
    ActuatorInfo* GetActuator(const StringType& actuatorId);
    
    bool AddSensor(const SensorInfo& sensor);
    bool RemoveSensor(const StringType& sensorId);
    SensorInfo* GetSensor(const StringType& sensorId);
    
    // 试验配置管理
    bool AddChannel(const LoadControlChannel& channel);
    bool RemoveChannel(const StringType& channelId);
    LoadControlChannel* GetChannel(const StringType& channelId);
    
    bool AddLoadSpectrum(const LoadSpectrum& spectrum);
    bool RemoveLoadSpectrum(const StringType& spectrumId);
    LoadSpectrum* GetLoadSpectrum(const StringType& spectrumId);
    
    bool AddSafetyConfig(const SafetyLimitConfig& config);
    bool RemoveSafetyConfig(const StringType& channelId);
    SafetyLimitConfig* GetSafetyConfig(const StringType& channelId);
    
    // 工程文件操作
    bool SaveToFile(const StringType& filePath) const;
    bool LoadFromFile(const StringType& filePath);
    
    // 验证工程完整性
    std::vector<StringType> ValidateProject() const;
    
private:
    /**
     * @brief 生成唯一ID
     * @param prefix 前缀
     * @return 唯一ID
     */
    StringType GenerateUniqueId(const StringType& prefix) const;
};

} // namespace DataModels
