QT += core
QT -= gui

CONFIG += c++14 console
CONFIG -= app_bundle

TARGET = compile_test
TEMPLATE = app

# 包含路径
INCLUDEPATH += include

# 源文件
SOURCES += \
    compile_test.cpp \
    src/HardwareNodeResDataManager.cpp \
    src/XLSDataExporter.cpp

# 头文件
HEADERS += \
    include/DataModels_Fixed.h \
    include/HardwareNodeResDataManager.h \
    include/XLSDataExporter.h \
    include/HardwareNodeStructs.h \
    include/NodeConfigDialog.h

# 库依赖
LIBS += -lQXlsx
