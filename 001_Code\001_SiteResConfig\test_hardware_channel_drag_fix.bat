@echo off
chcp 65001 > nul
echo ========================================
echo 硬件节点通道拖拽功能修复测试
echo ========================================
echo.

echo 🔧 正在编译修复后的代码...
cd SiteResConfig
qmake SiteResConfig_Simple.pro
mingw32-make clean
mingw32-make

if %ERRORLEVEL% NEQ 0 (
    echo ❌ 编译失败！
    pause
    exit /b 1
)

echo ✅ 编译成功！
echo.

echo 🚀 启动应用程序进行测试...
echo.
echo 📋 测试步骤：
echo.
echo 🎯 **修复内容总结**：
echo - 修复Excel导入时硬件节点通道的Qt::UserRole设置
echo - 从"硬件通道"修改为"硬件节点通道"
echo - 确保与拖拽检测逻辑一致
echo.
echo 🔍 **问题根源分析**：
echo - 新建工程时：channelItem->setData(0, Qt::UserRole, "硬件节点通道") ✅
echo - Excel导入时：channelItem->setData(0, Qt::UserRole, "硬件通道") ❌
echo - 拖拽检测期望：itemType == "硬件节点通道"
echo - 导致Excel导入的通道无法拖拽
echo.
echo 🧪 **测试用例1：新建工程拖拽验证（对照组）**
echo 1. 创建新工程
echo 2. 添加硬件节点（如LD-B1）
echo 3. 尝试拖拽"硬件节点资源" -> "LD-B1" -> "CH1"到控制通道
echo 4. 验证拖拽功能正常工作
echo.
echo 🧪 **测试用例2：Excel导入拖拽验证（修复验证）**
echo 1. 导入包含硬件节点的Excel文件
echo 2. 检查"硬件节点资源" -> "LD-B1" -> "CH1"、"CH2"
echo 3. 尝试拖拽这些通道到"实验配置" -> "控制通道" -> "CH1"或"CH2"
echo 4. 验证拖拽功能现在正常工作
echo.
echo 🧪 **测试用例3：拖拽关联信息验证**
echo 1. 成功拖拽硬件节点通道后
echo 2. 检查目标控制通道的关联信息是否正确显示
echo 3. 验证格式：如"LD-B1 - CH1"
echo 4. 检查日志是否记录拖拽操作
echo.
echo 🧪 **测试用例4：数据一致性验证**
echo 1. 拖拽关联后保存工程
echo 2. 重新导入工程
echo 3. 验证关联信息是否保持
echo 4. 再次测试拖拽功能是否正常
echo.
echo 🔍 **预期修复效果**：
echo ✅ Excel导入的硬件节点通道可以正常拖拽
echo ✅ 拖拽时显示正确的拖拽图标和提示
echo ✅ 拖拽到控制通道时正确设置关联信息
echo ✅ 与新建工程的拖拽行为完全一致
echo.
echo 🔧 **修复的核心问题**：
echo - 统一硬件节点通道的类型标识
echo - Excel导入和新建工程使用相同的Qt::UserRole值
echo - 确保拖拽检测逻辑能够正确识别通道类型
echo.
echo 📝 **修复的文件**：
echo - MainWindow_Qt_Simple.cpp - RefreshHardwareTreeFromDataManagers方法
echo   * 第4665行：setData(0, Qt::UserRole, "硬件节点通道")
echo.
echo ⚠️ **测试重点**：
echo 1. 对比新建工程和Excel导入的拖拽行为
echo 2. 验证拖拽时的视觉反馈（图标、提示）
echo 3. 确认拖拽后的关联信息正确设置
echo 4. 检查控制台日志输出
echo.

start "" "debug\SiteResConfig.exe"

echo 📊 应用程序已启动，请按照上述测试用例进行验证
echo.
echo 🔍 **拖拽操作步骤**：
echo 1. 在"硬件节点资源"中找到硬件节点（如LD-B1）
echo 2. 展开节点，找到通道（CH1或CH2）
echo 3. 按住鼠标左键拖拽通道
echo 4. 拖拽到"实验配置" -> "控制通道" -> "CH1"或"CH2"
echo 5. 释放鼠标，检查关联信息是否设置成功
echo.
echo 如果拖拽仍然不工作，请查看控制台输出的调试信息
pause
