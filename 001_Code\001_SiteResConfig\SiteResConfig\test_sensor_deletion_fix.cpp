/**
 * @file test_sensor_deletion_fix.cpp
 * @brief 传感器删除数据一致性修复验证测试
 * <AUTHOR> Assistant
 * @date 2025-01-01
 */

#include <QtCore/QCoreApplication>
#include <QtCore/QDebug>
#include <QtCore/QString>
#include "SensorDataManager_1_2.h"

/**
 * @brief 测试传感器删除修复效果
 */
void testSensorDeletionFix() {
    qDebug() << QString(u8"🧪 开始测试传感器删除修复效果...");
    
    // 1. 创建传感器数据管理器
    SensorDataManager_1_2 manager;
    
    // 2. 创建测试传感器组
    UI::SensorGroup_1_2 testGroup;
    testGroup.groupId = 1;
    testGroup.groupName = QString(u8"测试组");
    testGroup.groupType = QString(u8"载荷");
    testGroup.createTime = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
    testGroup.groupNotes = QString(u8"修复测试");
    
    // 3. 创建测试传感器
    UI::SensorParams_1_2 sensor1;
    sensor1.sensorId = 1;
    sensor1.params_sn = QString(u8"TEST_SENSOR_001");
    sensor1.params_model = QString(u8"测试传感器1");
    sensor1.params_type = QString(u8"力传感器");
    sensor1.params_range = QString(u8"0-1000N");
    sensor1.params_unitType = QString(u8"N");
    
    UI::SensorParams_1_2 sensor2;
    sensor2.sensorId = 2;
    sensor2.params_sn = QString(u8"TEST_SENSOR_002");
    sensor2.params_model = QString(u8"测试传感器2");
    sensor2.params_type = QString(u8"位移传感器");
    sensor2.params_range = QString(u8"0-50mm");
    sensor2.params_unitType = QString(u8"mm");
    
    testGroup.sensors.append(sensor1);
    testGroup.sensors.append(sensor2);
    
    // 4. 保存传感器组
    qDebug() << QString(u8"📋 保存测试传感器组...");
    if (!manager.saveSensorGroup(testGroup)) {
        qDebug() << QString(u8"❌ 保存传感器组失败: %1").arg(manager.getLastError());
        return;
    }
    
    // 5. 验证初始状态
    qDebug() << QString(u8"🔍 验证初始数据一致性...");
    if (!manager.validateStorageConsistency()) {
        qDebug() << QString(u8"❌ 初始数据不一致");
        return;
    }
    qDebug() << QString(u8"✅ 初始数据一致");
    
    // 6. 测试删除操作
    qDebug() << QString(u8"🗑️ 测试删除传感器 TEST_SENSOR_001...");
    if (!manager.removeSensorDetailedParamsInGroup(1, QString(u8"TEST_SENSOR_001"))) {
        qDebug() << QString(u8"❌ 删除传感器失败: %1").arg(manager.getLastError());
        return;
    }
    qDebug() << QString(u8"✅ 删除传感器成功");
    
    // 7. 验证删除后状态
    qDebug() << QString(u8"🔍 验证删除后数据一致性...");
    if (!manager.validateStorageConsistency()) {
        qDebug() << QString(u8"❌ 删除后数据不一致，需要修复");
        manager.syncGroupStorageFromGroupedStorage();
        if (manager.validateStorageConsistency()) {
            qDebug() << QString(u8"✅ 数据已修复并保持一致");
        } else {
            qDebug() << QString(u8"❌ 数据修复失败");
            return;
        }
    } else {
        qDebug() << QString(u8"✅ 删除后数据保持一致");
    }
    
    // 8. 验证剩余传感器
    QList<UI::SensorGroup_1_2> groups = manager.getAllSensorGroups();
    if (groups.size() == 1 && groups[0].sensors.size() == 1) {
        qDebug() << QString(u8"✅ 验证通过：剩余1个传感器，序列号=%1")
                    .arg(groups[0].sensors[0].params_sn);
    } else {
        qDebug() << QString(u8"❌ 验证失败：期望1个传感器，实际%1个")
                    .arg(groups.size() > 0 ? groups[0].sensors.size() : 0);
    }
    
    // 9. 测试Excel导出数据一致性
    qDebug() << QString(u8"📊 验证Excel导出数据...");
    QList<UI::SensorGroup_1_2> exportGroups = manager.getAllSensorGroups();
    int totalSensorsInExport = 0;
    for (const auto& group : exportGroups) {
        totalSensorsInExport += group.sensors.size();
    }
    qDebug() << QString(u8"📈 Excel导出将包含 %1 个传感器").arg(totalSensorsInExport);
    
    if (totalSensorsInExport == 1) {
        qDebug() << QString(u8"🎉 测试通过：Excel导出数据与实际数据一致！");
    } else {
        qDebug() << QString(u8"❌ 测试失败：Excel导出数据不一致，期望1个，实际%1个")
                    .arg(totalSensorsInExport);
    }
    
    qDebug() << QString(u8"🎯 传感器删除修复测试完成");
}

int main(int argc, char *argv[]) {
    QCoreApplication app(argc, argv);
    
    qDebug() << QString(u8"🚀 启动传感器删除修复验证测试");
    testSensorDeletionFix();
    
    return 0;
} 