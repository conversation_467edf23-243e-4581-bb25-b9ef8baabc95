@echo off
echo ========================================
echo  测试传感器组管理功能
echo ========================================

REM 设置Qt环境变量
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目（测试传感器组管理功能）...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo 请检查上面的错误信息。
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！传感器组管理功能已实现
    echo ========================================
    
    echo.
    echo ✅ 新增的功能:
    echo - SensorGroup结构体定义
    echo - 传感器组管理接口（增删改查）
    echo - 传感器ID自动分配功能
    echo - 组内传感器序列号唯一性验证
    echo - 传感器组数据导出功能
    echo - 序列号管理和统计功能
    echo.
    echo 🔧 参照ActuatorDataManager的设计:
    echo 1. 数据结构: SensorGroup（类似ActuatorGroup）
    echo 2. 存储管理: groupStorage_（传感器组存储）
    echo 3. ID管理: nextSensorId_, nextGroupId_（ID计数器）
    echo 4. 验证功能: validateSensorGroup()（组验证）
    echo 5. 导出功能: exportGroupsToCSVData(), exportGroupsToJSONArray()
    echo 6. 批量操作: getSensorsByGroup(), getAllSensorGroups()
    echo.
    echo 🆕 SensorDataManager新增接口:
    echo - saveSensorGroup(group)          // 保存传感器组
    echo - getSensorGroup(groupId)         // 获取传感器组
    echo - updateSensorGroup(groupId, group) // 更新传感器组
    echo - removeSensorGroup(groupId)      // 删除传感器组
    echo - getAllSensorGroups()            // 获取所有传感器组
    echo - hasSensorGroup(groupId)         // 检查传感器组是否存在
    echo - getSensorsByGroup(groupId)      // 获取组内传感器
    echo - validateSensorGroup(group)      // 验证传感器组
    echo - generateNextSerialNumber()      // 生成下一个序列号
    echo.
    echo 🎯 测试步骤:
    echo 1. 启动软件
    echo 2. 新建项目
    echo 3. 创建传感器组
    echo 4. 在组中添加传感器
    echo 5. 验证传感器ID自动分配
    echo 6. 验证组内序列号唯一性检查
    echo 7. 测试传感器组管理功能
    echo.
    
    if exist "SiteResConfig.exe" (
        echo 启动程序验证功能...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 启动程序验证功能...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 启动程序验证功能...
        start release\SiteResConfig.exe
    )
)

echo.
echo 📖 详细测试指南:
echo.
echo 🎮 传感器组管理测试:
echo 1. 启动软件后，新建一个项目
echo 2. 在硬件树中右键"传感器"节点
echo 3. 选择"新建" → "传感器组"，创建"载荷_传感器组"
echo 4. 在"载荷_传感器组"上右键，选择"新建传感器"
echo 5. 观察传感器序列号自动生成（如"传感器_000001"）
echo 6. 保存传感器，观察是否分配了唯一的传感器ID
echo 7. 再次添加传感器，验证ID递增分配
echo 8. 尝试添加重复序列号的传感器，验证唯一性检查
echo.
echo 🎮 传感器ID分配测试:
echo 1. 创建第一个传感器 → 应该分配ID=1
echo 2. 创建第二个传感器 → 应该分配ID=2
echo 3. 创建第三个传感器 → 应该分配ID=3
echo 4. 更新传感器信息 → ID应该保持不变
echo.
echo 🎮 组内唯一性测试:
echo 1. 在同一传感器组中添加传感器（序列号：SENSOR001）→ 成功
echo 2. 在同一传感器组中再次添加传感器（序列号：SENSOR001）→ 失败，提示重复
echo 3. 在不同传感器组中添加传感器（序列号：SENSOR001）→ 成功（不同组）
echo.
echo ✅ 预期结果:
echo - 传感器ID自动分配且唯一
echo - 组内传感器序列号唯一性检查正常
echo - 传感器组管理功能完整
echo - 数据验证和导出功能正常
echo - 与ActuatorDataManager功能对等
echo.
echo 🚨 如果测试失败:
echo - 检查编译错误信息
echo - 确认数据结构定义正确
echo - 验证方法实现完整
echo.
pause
