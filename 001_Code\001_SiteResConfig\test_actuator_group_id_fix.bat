@echo off
echo ========================================
echo  测试作动器组ID重复问题修复
echo ========================================

REM 设置Qt环境变量
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目（测试作动器组ID重复修复）...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo 请检查上面的错误信息。
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！作动器组ID重复问题已修复
    echo ========================================
    
    echo.
    echo ✅ 修复内容:
    echo - 修复了作动器添加到组时ID为0的问题
    echo - 确保作动器先保存到DataManager获取唯一ID
    echo - 然后使用带有正确ID的参数添加到组中
    echo - 避免了"组内作动器ID重复"错误
    echo.
    echo 🔧 修复原理:
    echo 1. 原问题: 作动器添加到组时使用原始参数(ID=0)
    echo 2. 修复方案: 先保存到DataManager获取分配的ID
    echo 3. 使用带有正确ID的参数添加到组中
    echo 4. 确保组内每个作动器都有唯一的ID
    echo.
    echo 🎯 测试步骤:
    echo 1. 启动软件
    echo 2. 新建项目
    echo 3. 创建作动器组
    echo 4. 在组中添加多个作动器
    echo 5. 验证不再出现"组内作动器ID重复"错误
    echo.
    
    if exist "SiteResConfig.exe" (
        echo 启动程序验证修复...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 启动程序验证修复...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 启动程序验证修复...
        start release\SiteResConfig.exe
    )
)

echo.
echo 📖 验证指南:
echo.
echo 🎮 详细测试步骤:
echo 1. 启动软件后，新建一个项目
echo 2. 在硬件树中右键"作动器"节点
echo 3. 选择"新建" → "作动器组"
echo 4. 创建一个作动器组（如"测试组"）
echo 5. 在作动器组上右键，选择"新建作动器"
echo 6. 填写作动器参数并保存
echo 7. 重复步骤5-6，添加第二个作动器
echo 8. 观察是否还会出现"组内作动器ID重复"错误
echo.
echo ✅ 预期结果:
echo - 不再出现"组内作动器ID重复"错误
echo - 每个作动器都有唯一的ID（1, 2, 3...）
echo - 作动器组保存成功
echo - 日志显示正常的操作记录
echo.
echo 🚨 如果仍有问题:
echo - 检查日志中的错误信息
echo - 确认作动器参数填写完整
echo - 验证序列号是否唯一
echo.
pause
