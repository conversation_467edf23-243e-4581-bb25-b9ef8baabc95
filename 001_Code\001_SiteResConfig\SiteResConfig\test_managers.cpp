/**
 * @file test_managers.cpp
 * @brief 测试数据管理器的基本功能
 * @details 简单测试ChanCtrlDataManager和HardwareNodeResDataManager的基本功能
 * <AUTHOR> Agent
 * @date 2024-01-15
 * @version 1.0.0
 */

#include <QCoreApplication>
#include <QDebug>
#include "ChanCtrlDataManager.h"
#include "HardwareNodeResDataManager.h"

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "=== 测试数据管理器 ===";
    
    // 测试控制通道数据管理器
    qDebug() << "\n--- 测试 ChanCtrlDataManager ---";
    ChanCtrlDataManager chanCtrlManager;
    
    // 创建测试控制通道组
    UI::ControlChannelGroup testGroup;
    testGroup.groupId = 1;
    testGroup.groupName = "测试控制通道组";
    testGroup.groupType = "控制通道";
    
    // 添加测试通道
    UI::ControlChannelParams testChannel;
    testChannel.channelId = "CH1";
    testChannel.channelName = "CH1";
    testChannel.hardwareAssociation = "LD-B1 - CH1";
    testChannel.load1Sensor = "传感器_000001";
    testChannel.controlActuator = "作动器_000001";
    testChannel.notes = "测试通道";
    
    testGroup.channels.push_back(testChannel);
    
    if (chanCtrlManager.createControlChannelGroup(testGroup)) {
        qDebug() << "✅ 控制通道组创建成功";
        
        auto groups = chanCtrlManager.getAllControlChannelGroups();
        qDebug() << QString("📊 获取到 %1 个控制通道组").arg(groups.size());
        
        if (!groups.isEmpty()) {
            const auto& group = groups.first();
            qDebug() << QString("📋 组名: %1, 通道数: %2")
                        .arg(QString::fromStdString(group.groupName))
                        .arg(group.channels.size());
        }
    } else {
        qDebug() << "❌ 控制通道组创建失败";
    }
    
    // 测试硬件节点资源数据管理器
    qDebug() << "\n--- 测试 HardwareNodeResDataManager ---";
    HardwareNodeResDataManager hardwareManager;
    
    // 创建测试硬件节点组
    UI::HardwareNodeGroup testHwGroup;
    testHwGroup.groupId = 1;
    testHwGroup.groupName = "测试硬件节点组";
    testHwGroup.groupType = "硬件节点";
    
    // 添加测试节点
    UI::HardwareNodeParams testNode;
    testNode.nodeId = "node_001";
    testNode.nodeName = "测试作动器";
    testNode.nodeType = "作动器设备";
    testNode.parentPath = "任务1";
    testNode.fullPath = "任务1/测试作动器";
    testNode.status = "在线";
    testNode.connectionType = "Ethernet";
    testNode.notes = "测试硬件节点";
    
    testHwGroup.nodes.push_back(testNode);
    
    if (hardwareManager.createHardwareNodeGroup(testHwGroup)) {
        qDebug() << "✅ 硬件节点组创建成功";
        
        auto hwGroups = hardwareManager.getAllHardwareNodeGroups();
        qDebug() << QString("📊 获取到 %1 个硬件节点组").arg(hwGroups.size());
        
        if (!hwGroups.isEmpty()) {
            const auto& hwGroup = hwGroups.first();
            qDebug() << QString("📋 组名: %1, 节点数: %2")
                        .arg(QString::fromStdString(hwGroup.groupName))
                        .arg(hwGroup.nodes.size());
            
            if (!hwGroup.nodes.empty()) {
                const auto& node = hwGroup.nodes.front();
                qDebug() << QString("🔧 节点: %1, 类型: %2, 状态: %3")
                            .arg(QString::fromStdString(node.nodeName))
                            .arg(QString::fromStdString(node.nodeType))
                            .arg(QString::fromStdString(node.status));
            }
        }
    } else {
        qDebug() << "❌ 硬件节点组创建失败";
    }
    
    // 测试统计功能
    qDebug() << "\n--- 统计信息 ---";
    qDebug() << QString("控制通道组数量: %1").arg(chanCtrlManager.getGroupCount());
    qDebug() << QString("控制通道总数: %1").arg(chanCtrlManager.getTotalChannelCount());
    qDebug() << QString("硬件节点组数量: %1").arg(hardwareManager.getGroupCount());
    qDebug() << QString("硬件节点总数: %1").arg(hardwareManager.getTotalNodeCount());
    
    qDebug() << "\n=== 测试完成 ===";
    
    return 0;
}
