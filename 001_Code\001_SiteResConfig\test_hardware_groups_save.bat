@echo off
echo ========================================
echo  硬件分组节点保存修复测试
echo ========================================

echo 问题分析：
echo 1. "作动器组"、"传感器组" - 分组节点，不包含"设备"或"通道"关键字
echo 2. "LD-B1" - 具体硬件节点，类型可能不包含"设备"或"通道"
echo 3. 原保存策略过于严格，只保存特定类型的节点
echo.

echo 修复策略：
echo 硬件配置保存条件（满足任一即保存）：
echo ├─ 分组节点：包含"组"、"Group"、"资源"关键字
echo ├─ 设备节点：包含"设备"、"通道"、"硬件"关键字  
echo ├─ 容器节点：有子节点的节点
echo ├─ 内容节点：有实际内容（名称+工具提示）的叶子节点
echo └─ 跳过根节点：只跳过"硬件配置"根节点
echo.

echo 技术改进：
echo ✅ 灵活的保存策略，覆盖更多节点类型
echo ✅ 支持分组节点（作动器组、传感器组等）
echo ✅ 支持具体硬件节点（LD-B1等）
echo ✅ 详细的调试输出，便于问题排查
echo ✅ 保持试验配置的修复不变
echo.

echo 保存逻辑：
echo 顶级节点：
echo   ├─ 保存有实际内容的节点
echo   └─ 跳过"硬件配置"根节点
echo.
echo 非顶级节点：
echo   ├─ 分组节点（名称包含"组"、"资源"等）
echo   ├─ 设备节点（类型包含"设备"、"通道"、"硬件"）
echo   ├─ 有子节点的容器节点
echo   └─ 有内容的叶子节点
echo.

echo 调试信息：
echo - 每个节点都会输出详细的分析信息
echo - 显示节点名称、类型、父节点、子节点数量
echo - 明确显示是否保存及原因
echo - 保存成功显示 ✅，跳过显示 ❌
echo.

echo 测试步骤：
echo 1. 重新编译项目
echo 2. 确保硬件树中有"作动器组"、"传感器组"、"LD-B1"等节点
echo 3. 保存工程配置为CSV文件
echo 4. 查看控制台调试输出
echo 5. 检查CSV文件中的[硬件配置]部分
echo.

echo 预期结果：
echo - "作动器组"应该被识别为分组节点并保存
echo - "传感器组"应该被识别为分组节点并保存  
echo - "LD-B1"应该被识别为硬件节点并保存
echo - 控制台显示详细的节点分析信息
echo - CSV文件包含完整的硬件配置信息
echo.

pause
