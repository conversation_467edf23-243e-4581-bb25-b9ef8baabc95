#ifndef TREEMANAGER_H
#define TREEMANAGER_H

#include <QObject>
#include <QTreeWidgetItem>
#include <QVariant>
#include <QString>

// 引用现有树形控件类
class CustomHardwareTreeWidget;
class CustomTestConfigTreeWidget;
class CMyMainWindow;

/**
 * @brief 树形控件管理器 - 基于现有树形控件组件
 * 
 * TreeManager基于现有的CustomHardwareTreeWidget和CustomTestConfigTreeWidget
 * 提供统一的树形控件管理接口。
 * 
 * 设计特点：
 * - 100%基于现有树形控件 (CustomHardwareTreeWidget, CustomTestConfigTreeWidget)
 * - 不创建任何新的树形控件
 * - 提供统一的树形控件操作接口
 * - 集成现有的树形控件交互逻辑
 */
class TreeManager : public QObject
{
    Q_OBJECT

public:
    explicit TreeManager(QObject* parent = nullptr);
    ~TreeManager();

    // 设置现有树形控件 (不创建新的)
    void setHardwareTreeWidget(CustomHardwareTreeWidget* treeWidget);
    void setTestConfigTreeWidget(CustomTestConfigTreeWidget* treeWidget);
    void setMainWindow(CMyMainWindow* mainWindow);

    // 硬件树管理
    void refreshHardwareTree();
    void addHardwareNode(const QString& nodeName);
    void removeHardwareNode(const QString& nodeName);
    void updateHardwareNode(const QString& nodeName);

    // 测试配置树管理
    void refreshTestConfigTree();
    void addTestConfigItem(const QString& itemName, const QVariant& data);
    void removeTestConfigItem(const QString& itemName);
    void updateTestConfigItem(const QString& itemName, const QVariant& data);

    // 树形控件交互
    void handleTreeItemSelection(QTreeWidgetItem* item);
    void handleTreeItemDoubleClick(QTreeWidgetItem* item);

    // 获取当前选中项
    QTreeWidgetItem* getCurrentHardwareItem() const;
    QTreeWidgetItem* getCurrentTestConfigItem() const;

    // 树形控件状态
    bool hasHardwareTreeWidget() const;
    bool hasTestConfigTreeWidget() const;

    // 刷新测试配置树
    void refreshTestConfigTree();
    
    // 获取选中的硬件节点
    QTreeWidgetItem* getSelectedHardwareNode() const;
    
    // 获取选中的测试配置项
    QTreeWidgetItem* getSelectedTestConfigItem() const;

    // 🆕 新增：树形控件事件处理方法
    void handleHardwareTreeContextMenu(const QPoint& pos);
    void handleTestConfigTreeContextMenu(const QPoint& pos);
    void handleTestConfigTreeItemDoubleClicked(QTreeWidgetItem* item, int column);
    void handleTestConfigTreeItemClicked(QTreeWidgetItem* item, int column);
    
    // 🆕 新增：树形控件显示管理方法
    void forceRestoreAllTreeColors();
    void updateTreeDisplay();
    void enableTestConfigTreeDragDrop();

signals:
    // 硬件树相关信号
    void hardwareTreeRefreshed();
    void hardwareNodeAdded(const QString& nodeName);
    void hardwareNodeRemoved(const QString& nodeName);
    
    // 测试配置树相关信号
    void testConfigTreeRefreshed();
    void testConfigItemAdded(const QString& itemName);
    void testConfigItemRemoved(const QString& itemName);
    
    // 🆕 新增：树形控件状态信号
    void treeColorsRestored();
    void treeDisplayUpdated();
    void treeDragDropEnabled();

private slots:
    void onHardwareTreeItemSelection();
    void onHardwareTreeItemDoubleClick();
    void onTestConfigTreeItemSelection();
    void onTestConfigTreeItemDoubleClick();

private:
    // 引用现有树形控件 (不创建新的)
    CustomHardwareTreeWidget* hardwareTreeWidget_;
    CustomTestConfigTreeWidget* testConfigTreeWidget_;
    CMyMainWindow* mainWindow_;

    void connectHardwareTreeSignals();
    void connectTestConfigTreeSignals();
    void disconnectTreeSignals();

    QTreeWidgetItem* findHardwareNode(const QString& nodeName);
    QTreeWidgetItem* findTestConfigItem(const QString& itemName);
};

#endif // TREEMANAGER_H 