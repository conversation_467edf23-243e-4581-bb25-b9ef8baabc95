#include <QApplication>
#include <QTreeWidget>
#include <QTreeWidgetItem>
#include <QVBoxLayout>
#include <QWidget>
#include <QLabel>
#include <QPushButton>
#include <QTimer>
#include <QDebug>

/**
 * @file test_tree_expand_functionality.cpp
 * @brief 测试树形控件完全展开功能
 * @details 验证expandAll()方法在各种情况下的正确性
 * <AUTHOR> Assistant
 * @date 2025-01-27
 * @version 1.0.0
 */

class TreeExpandTest : public QWidget {
    Q_OBJECT

public:
    TreeExpandTest(QWidget* parent = nullptr) : QWidget(parent) {
        setupUI();
        createTestData();
    }

private slots:
    void testExpandAll() {
        qDebug() << "🌲 测试 expandAll() 功能...";
        
        // 记录展开前状态
        qDebug() << "展开前状态:";
        logTreeState();
        
        // 执行完全展开
        hardwareTree->expandAll();
        testConfigTree->expandAll();
        
        // 记录展开后状态
        qDebug() << "展开后状态:";
        logTreeState();
        
        // 验证展开状态
        bool allExpanded = verifyAllExpanded();
        
        if (allExpanded) {
            statusLabel->setText("✅ 测试通过：所有树形控件节点已完全展开");
            statusLabel->setStyleSheet("color: green; font-weight: bold;");
            qDebug() << "✅ 树形控件展开功能测试通过";
        } else {
            statusLabel->setText("❌ 测试失败：部分节点未能展开");
            statusLabel->setStyleSheet("color: red; font-weight: bold;");
            qDebug() << "❌ 树形控件展开功能测试失败";
        }
    }

    void testCollapseAll() {
        qDebug() << "🔽 测试 collapseAll() 功能...";
        
        hardwareTree->collapseAll();
        testConfigTree->collapseAll();
        
        statusLabel->setText("🔽 所有节点已收缩");
        statusLabel->setStyleSheet("color: blue;");
    }

    void testAutoExpand() {
        qDebug() << "⏰ 模拟打开工程时的自动展开...";
        
        // 先收缩所有节点
        hardwareTree->collapseAll();
        testConfigTree->collapseAll();
        
        // 模拟延迟展开（类似OnProjectOpened中的处理）
        QTimer::singleShot(100, [this]() {
            hardwareTree->expandAll();
            testConfigTree->expandAll();
            
            bool allExpanded = verifyAllExpanded();
            if (allExpanded) {
                statusLabel->setText("✅ 自动展开测试通过：模拟打开工程功能正常");
                statusLabel->setStyleSheet("color: green; font-weight: bold;");
            } else {
                statusLabel->setText("❌ 自动展开测试失败");
                statusLabel->setStyleSheet("color: red; font-weight: bold;");
            }
        });
        
        statusLabel->setText("⏰ 正在执行自动展开测试...");
        statusLabel->setStyleSheet("color: orange;");
    }

private:
    void setupUI() {
        setWindowTitle("SiteResConfig - 树形控件展开功能测试");
        setMinimumSize(800, 600);
        
        QVBoxLayout* mainLayout = new QVBoxLayout(this);
        
        // 状态标签
        statusLabel = new QLabel("准备就绪，点击按钮测试树形控件展开功能", this);
        statusLabel->setAlignment(Qt::AlignCenter);
        statusLabel->setStyleSheet("padding: 10px; border: 1px solid gray; background-color: #f0f0f0;");
        mainLayout->addWidget(statusLabel);
        
        // 创建硬件配置树
        QLabel* hardwareLabel = new QLabel("硬件配置树:", this);
        hardwareLabel->setStyleSheet("font-weight: bold; color: #0066cc;");
        mainLayout->addWidget(hardwareLabel);
        
        hardwareTree = new QTreeWidget(this);
        hardwareTree->setHeaderLabel("硬件配置");
        hardwareTree->setMaximumHeight(200);
        mainLayout->addWidget(hardwareTree);
        
        // 创建实验配置树
        QLabel* testConfigLabel = new QLabel("实验配置树:", this);
        testConfigLabel->setStyleSheet("font-weight: bold; color: #0066cc;");
        mainLayout->addWidget(testConfigLabel);
        
        testConfigTree = new QTreeWidget(this);
        testConfigTree->setHeaderLabel("实验配置");
        testConfigTree->setMaximumHeight(200);
        mainLayout->addWidget(testConfigTree);
        
        // 按钮区域
        QWidget* buttonWidget = new QWidget(this);
        QVBoxLayout* buttonLayout = new QVBoxLayout(buttonWidget);
        
        QPushButton* expandButton = new QPushButton("🌲 测试完全展开 (expandAll)", this);
        expandButton->setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 10px; }");
        connect(expandButton, &QPushButton::clicked, this, &TreeExpandTest::testExpandAll);
        buttonLayout->addWidget(expandButton);
        
        QPushButton* collapseButton = new QPushButton("🔽 测试完全收缩 (collapseAll)", this);
        collapseButton->setStyleSheet("QPushButton { background-color: #ff9800; color: white; font-weight: bold; padding: 10px; }");
        connect(collapseButton, &QPushButton::clicked, this, &TreeExpandTest::testCollapseAll);
        buttonLayout->addWidget(collapseButton);
        
        QPushButton* autoExpandButton = new QPushButton("⏰ 测试自动展开 (模拟打开工程)", this);
        autoExpandButton->setStyleSheet("QPushButton { background-color: #2196F3; color: white; font-weight: bold; padding: 10px; }");
        connect(autoExpandButton, &QPushButton::clicked, this, &TreeExpandTest::testAutoExpand);
        buttonLayout->addWidget(autoExpandButton);
        
        mainLayout->addWidget(buttonWidget);
    }
    
    void createTestData() {
        // 创建硬件配置树测试数据
        QTreeWidgetItem* hardwareRoot = new QTreeWidgetItem(hardwareTree);
        hardwareRoot->setText(0, "硬件配置");
        
        QTreeWidgetItem* actuatorRoot = new QTreeWidgetItem(hardwareRoot);
        actuatorRoot->setText(0, "作动器");
        
        QTreeWidgetItem* actuatorGroup1 = new QTreeWidgetItem(actuatorRoot);
        actuatorGroup1->setText(0, "作动器组1");
        for (int i = 1; i <= 3; ++i) {
            QTreeWidgetItem* device = new QTreeWidgetItem(actuatorGroup1);
            device->setText(0, QString("设备%1").arg(i));
        }
        
        QTreeWidgetItem* sensorRoot = new QTreeWidgetItem(hardwareRoot);
        sensorRoot->setText(0, "传感器");
        
        QTreeWidgetItem* sensorGroup1 = new QTreeWidgetItem(sensorRoot);
        sensorGroup1->setText(0, "传感器组1");
        for (int i = 1; i <= 2; ++i) {
            QTreeWidgetItem* sensor = new QTreeWidgetItem(sensorGroup1);
            sensor->setText(0, QString("传感器%1").arg(i));
        }
        
        QTreeWidgetItem* hardwareNodeRoot = new QTreeWidgetItem(hardwareRoot);
        hardwareNodeRoot->setText(0, "硬件节点资源");
        
        // 创建实验配置树测试数据
        QTreeWidgetItem* testRoot = new QTreeWidgetItem(testConfigTree);
        testRoot->setText(0, "实验");
        
        QTreeWidgetItem* commandRoot = new QTreeWidgetItem(testRoot);
        commandRoot->setText(0, "指令");
        
        QTreeWidgetItem* spectrumRoot = new QTreeWidgetItem(testRoot);
        spectrumRoot->setText(0, "载荷谱");
        
        QTreeWidgetItem* channelRoot = new QTreeWidgetItem(testRoot);
        channelRoot->setText(0, "控制通道");
        
        for (int ch = 1; ch <= 2; ++ch) {
            QTreeWidgetItem* channelItem = new QTreeWidgetItem(channelRoot);
            channelItem->setText(0, QString("CH%1").arg(ch));
            
            QStringList subItems = {"载荷1", "载荷2", "位置", "控制"};
            for (const QString& subItem : subItems) {
                QTreeWidgetItem* subNode = new QTreeWidgetItem(channelItem);
                subNode->setText(0, subItem);
            }
        }
        
        qDebug() << "✅ 测试数据创建完成";
        qDebug() << "硬件配置树节点数:" << countAllItems(hardwareTree);
        qDebug() << "实验配置树节点数:" << countAllItems(testConfigTree);
    }
    
    void logTreeState() {
        qDebug() << "硬件配置树展开状态:" << getExpandedItemCount(hardwareTree) 
                 << "/" << countAllItems(hardwareTree);
        qDebug() << "实验配置树展开状态:" << getExpandedItemCount(testConfigTree) 
                 << "/" << countAllItems(testConfigTree);
    }
    
    bool verifyAllExpanded() {
        int hardwareTotal = countAllItems(hardwareTree);
        int hardwareExpanded = getExpandedItemCount(hardwareTree);
        
        int testConfigTotal = countAllItems(testConfigTree);
        int testConfigExpanded = getExpandedItemCount(testConfigTree);
        
        qDebug() << QString("硬件配置树：%1/%2 已展开").arg(hardwareExpanded).arg(hardwareTotal);
        qDebug() << QString("实验配置树：%1/%2 已展开").arg(testConfigExpanded).arg(testConfigTotal);
        
        // 只有包含子节点的项目需要检查展开状态
        return verifyTreeExpanded(hardwareTree) && verifyTreeExpanded(testConfigTree);
    }
    
    bool verifyTreeExpanded(QTreeWidget* tree) {
        return verifyItemExpanded(tree->invisibleRootItem());
    }
    
    bool verifyItemExpanded(QTreeWidgetItem* item) {
        if (!item) return true;
        
        for (int i = 0; i < item->childCount(); ++i) {
            QTreeWidgetItem* child = item->child(i);
            if (child->childCount() > 0 && !child->isExpanded()) {
                qDebug() << "发现未展开的节点:" << child->text(0);
                return false;
            }
            if (!verifyItemExpanded(child)) {
                return false;
            }
        }
        return true;
    }
    
    int countAllItems(QTreeWidget* tree) {
        return countItems(tree->invisibleRootItem());
    }
    
    int countItems(QTreeWidgetItem* item) {
        if (!item) return 0;
        
        int count = 0;
        for (int i = 0; i < item->childCount(); ++i) {
            count += 1 + countItems(item->child(i));
        }
        return count;
    }
    
    int getExpandedItemCount(QTreeWidget* tree) {
        return getExpandedItems(tree->invisibleRootItem());
    }
    
    int getExpandedItems(QTreeWidgetItem* item) {
        if (!item) return 0;
        
        int count = 0;
        for (int i = 0; i < item->childCount(); ++i) {
            QTreeWidgetItem* child = item->child(i);
            if (child->isExpanded()) count++;
            count += getExpandedItems(child);
        }
        return count;
    }

private:
    QTreeWidget* hardwareTree;
    QTreeWidget* testConfigTree;
    QLabel* statusLabel;
};

int main(int argc, char *argv[]) {
    QApplication app(argc, argv);
    
    TreeExpandTest window;
    window.show();
    
    qDebug() << "🚀 SiteResConfig 树形控件展开功能测试程序启动";
    qDebug() << "===================================================";
    qDebug() << "本程序测试以下功能：";
    qDebug() << "1. expandAll() - 完全展开所有树形控件节点";
    qDebug() << "2. collapseAll() - 完全收缩所有树形控件节点";
    qDebug() << "3. 模拟打开工程时的自动展开功能";
    qDebug() << "===================================================";
    
    return app.exec();
}

#include "test_tree_expand_functionality.moc" 