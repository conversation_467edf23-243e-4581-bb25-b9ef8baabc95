/**
 * @file ActuatorDataManager_1_2.h
 * @brief 作动器数据管理器
 * @details 封装作动器详细参数的管理操作
 * <AUTHOR> Assistant
 * @date 2025-08-14
 * @version 1.0.0
 */

#ifndef ACTUATORDATAMANAGER_H
#define ACTUATORDATAMANAGER_H

#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QJsonObject>
#include <QtCore/QJsonArray>
#include <QtCore/QVector>
#include <QtCore/QMap>
#include <QtCore/QList>
#include "ActuatorDialog_1_2.h"

// 前向声明
namespace DataModels {
    class TestProject;
}

/**
 * @brief 作动器数据管理器类
 * @details 封装作动器详细参数的管理操作，提供统一的数据访问接口
 */
class ActuatorDataManager_1_2 : public QObject {
    Q_OBJECT

public:
    explicit ActuatorDataManager_1_2();
    ~ActuatorDataManager_1_2();

    // 🆕 作动器数据管理接口（与传感器对等）
    bool saveActuatorDetailedParams(const UI::ActuatorParams_1_2& params, int groupId); // 🔄 修改：添加组ID参数
    //UI::ActuatorParams_1_2 getActuatorDetailedParams(const QString& serialNumber) const; // ❌ 注释：不再使用无组ID版本
    UI::ActuatorParams_1_2 getActuatorDetailedParams(const QString& serialNumber, int groupId) const; // 🆕 新增：指定组ID的查询方法
    bool updateActuatorDetailedParams(const QString& serialNumber, const UI::ActuatorParams_1_2& params, int groupId); // 🔄 修改：添加组ID参数
    bool removeActuatorDetailedParams(const QString& serialNumber, int groupId); // 🔄 修改：添加组ID参数
    QStringList getAllActuatorSerialNumbers() const;
    QList<UI::ActuatorParams_1_2> getAllActuatorDetailedParams() const;

    // 🆕 新增类型转换辅助方法
    static QString actuatorTypeToString(UI::ActuatorType_1_2 type);
    static UI::ActuatorType_1_2 stringToActuatorType(const QString& str);
    static QString polarityToString(UI::Polarity_1_2 polarity);
    static UI::Polarity_1_2 stringToPolarity(const QString& str);
    static QString measurementUnitToString(UI::MeasurementUnit_1_2 unit);
    static UI::MeasurementUnit_1_2 stringToMeasurementUnit(const QString& str);

    // 作动器数据操作（基础接口）
    //bool addActuator(const UI::ActuatorParams_1_2& params); // ❌ 注释：不再使用无组ID版本
    bool addActuator(const UI::ActuatorParams_1_2& params, int groupId); // 🆕 新增：指定组ID的添加方法
    //UI::ActuatorParams_1_2 getActuator(const QString& serialNumber) const; // ❌ 注释：不再使用无组ID版本
    UI::ActuatorParams_1_2 getActuator(const QString& serialNumber, int groupId) const; // 🆕 新增：指定组ID的查询方法
    bool updateActuator(const QString& serialNumber, const UI::ActuatorParams_1_2& params, int groupId); // 🔄 修改：添加组ID参数
    bool removeActuator(const QString& serialNumber, int groupId); // 🔄 修改：添加组ID参数
    //bool hasActuator(const QString& serialNumber) const; // ❌ 注释：不再使用无组ID版本
    bool hasActuator(const QString& serialNumber, int groupId) const; // 🆕 新增：指定组ID的存在性检查方法
    
    // 🆕 新增：获取作动器所在的组ID
    int getActuatorGroupId(const QString& serialNumber) const;

    // 作动器组管理接口
    bool saveActuatorGroup(const UI::ActuatorGroup_1_2& group);
    UI::ActuatorGroup_1_2 getActuatorGroup(int groupId) const;
    bool updateActuatorGroup(int groupId, const UI::ActuatorGroup_1_2& group);
    bool removeActuatorGroup(int groupId);
    QList<UI::ActuatorGroup_1_2> getAllActuatorGroups() const;
    bool hasActuatorGroup(int groupId) const;

    // 批量操作
    QList<UI::ActuatorParams_1_2> getAllActuators() const;
    QList<UI::ActuatorParams_1_2> getActuatorsByType(const QString& actuatorType) const;
    QList<UI::ActuatorParams_1_2> getActuatorsByGroup(int groupId) const;
    QList<UI::ActuatorParams_1_2> getActuatorsByUnitType(const QString& unitType) const;
    int getActuatorCount() const;
    int getActuatorGroupCount() const;

    // 数据验证
    bool validateActuatorParams(const UI::ActuatorParams_1_2& params) const;
    bool validateActuatorGroup(const UI::ActuatorGroup_1_2& group) const;
    bool validateActuatorInGroup(const UI::ActuatorParams_1_2& actuator, const UI::ActuatorGroup_1_2& group) const; // 🆕 新增：验证单个作动器在组内是否冲突
    QStringList validateAllActuators() const;
    QStringList validateAllActuatorGroups() const;

    // 数据导出
    QVector<QStringList> exportToCSVData() const;
    QVector<QStringList> exportGroupsToCSVData() const;
    // 🚫 已注释：独立JSON导出功能已废弃
    // QJsonArray exportToJSONArray() const;

    // 数据统计
    QMap<QString, int> getActuatorTypeStatistics() const;
    QMap<QString, int> getUnitTypeStatistics() const;
    QMap<QString, int> getPolarityStatistics() const;
    QStringList getUsedActuatorTypes() const;
    QStringList getUsedUnitTypes() const;

    // 序列号管理
    //QString generateNextSerialNumber(const QString& prefix = "ACT") const;
    QString generateNextSerialNumberInGroup(int groupId, const QString& prefix = "作动器_") const;  // 🆕 新增：组内序列号生成
    //bool isSerialNumberUnique(const QString& serialNumber) const;  // 全局唯一性检查（向后兼容）
    bool isSerialNumberUniqueInGroup(const QString& serialNumber, int groupId) const;  // 🆕 新增：组内唯一性检查
    bool isSerialNumberUniqueInGroup(const QString& serialNumber, int groupId, int excludeActuatorId) const;  // 🆕 新增：组内唯一性检查（排除指定ID）
    QStringList findDuplicateSerialNumbers() const;

    // 错误处理
    QString getLastError() const;
    bool hasError() const;

    // 数据清理
    void clearAllActuators();
    void clearAllActuatorGroups();
    void clearAll();

private:
    mutable QString lastError_;
    
    // 🔄 修改：分层存储架构 - 按组分层存储作动器数据
    QMap<int, QMap<QString, UI::ActuatorParams_1_2>> groupedActuatorStorage_;  // groupId -> {serialNumber -> ActuatorParams}
    QMap<int, UI::ActuatorGroup_1_2> groupStorage_;
    int nextGroupId_;
    int nextActuatorId_;  // 🆕 作动器ID计数器

    // 辅助方法
    void clearError() const;
    void setError(const QString& error) const;
    bool isValidSerialNumber(const QString& serialNumber) const;
    bool isValidGroupId(int groupId) const;
    void initializeStorage();
    void updateIdCounters();  // 🆕 新增：更新ID计数器
    
    // 🆕 分层存储辅助方法
    int findGroupIdBySerialNumber(const QString& serialNumber) const;
    QList<UI::ActuatorParams_1_2> getActuatorsByGroupId(int groupId) const;
    
    // 🆕 新增：获取下一个可用的组内作动器ID
    int getNextActuatorIdInGroup(int groupId) const;

signals:
    // 🆕 新增：作动器数据变化信号
    void actuatorDataChanged(const QString& serialNumber, const QString& operation);
    void actuatorGroupDataChanged(int groupId, const QString& operation);
    void actuatorAssociationChanged(const QString& serialNumber, const QString& channelName);
    
    // 🆕 新增：错误信号
    void errorOccurred(const QString& error);
};

#endif // ACTUATORDATAMANAGER_H
