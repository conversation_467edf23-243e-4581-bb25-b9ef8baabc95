/**
 * @file CustomTreeWidgets.cpp
 * @brief 自定义树形控件实现
 * <AUTHOR> Assistant
 * @date 2025-08-07
 * @version 1.0.0
 */

#include "CustomTreeWidgets.h"
#include "MainWindow_Qt_Simple.h"
#include <QtWidgets/QAbstractItemView>
#include <QtWidgets/QApplication>
#include <QtGui/QClipboard>
#include <QTimer>
#include <QMouseEvent>
#include <QDebug>

// ============================================================================
// CustomHardwareTreeWidget Implementation
// ============================================================================

CustomHardwareTreeWidget::CustomHardwareTreeWidget(QWidget* parent)
    : QTreeWidget(parent), m_mainWindow(nullptr), m_draggedItem(nullptr) {
    setDragEnabled(true);
    setDragDropMode(QAbstractItemView::DragOnly);
    setDefaultDropAction(Qt::CopyAction);
}

CustomHardwareTreeWidget::~CustomHardwareTreeWidget() {
    // 确保在析构时恢复任何可能残留的颜色变化
    restoreDraggedItemColor();
}

// 设置主窗口引用
void CustomHardwareTreeWidget::setMainWindow(CMyMainWindow* mainWindow) {
    m_mainWindow = mainWindow;
}

QMimeData* CustomHardwareTreeWidget::mimeData(const QList<QTreeWidgetItem*> items) const {
    if (items.isEmpty() || !m_mainWindow) {
        return nullptr;
    }

    QTreeWidgetItem* item = items.first();
    if (!m_mainWindow->canDragItemPublic(item)) {
        return nullptr;
    }

    QString itemText = item->text(0);
    QString itemType = m_mainWindow->getItemTypePublic(item);

    // 获取父节点信息以确保唯一性
    QString parentText = "";
    if (item->parent()) {
        parentText = item->parent()->text(0);
    }

    QMimeData* mimeData = new QMimeData;
    // 新格式：通道名|类型|父节点名，例如："CH1|硬件节点通道|LD-B1"
    mimeData->setText(QString("%1|%2|%3").arg(itemText).arg(itemType).arg(parentText));

    return mimeData;
}

void CustomHardwareTreeWidget::startDrag(Qt::DropActions supportedActions) {
    // 先恢复之前可能存在的拖拽项目颜色
    restoreDraggedItemColor();

    QList<QTreeWidgetItem*> items = selectedItems();
    if (items.isEmpty() || !m_mainWindow) {
        return;
    }

    QTreeWidgetItem* item = items.first();
    if (!m_mainWindow->canDragItemPublic(item)) {
        return;
    }

    // 记录当前拖拽项目
    m_draggedItem = item;

    // 保存原始颜色
    QBrush originalBg = item->background(0);
    QBrush originalFg = item->foreground(0);

    // 如果原始颜色无效，使用默认透明背景
    m_originalBackgroundColor = originalBg.style() != Qt::NoBrush ? originalBg : QBrush();
    m_originalTextColor = originalFg.style() != Qt::NoBrush ? originalFg : QBrush();

    // 设置拖拽时的颜色（浅蓝色背景，深蓝色文字）
    item->setBackground(0, QColor(173, 216, 230)); // 浅蓝色背景
    item->setForeground(0, QColor(0, 0, 139));     // 深蓝色文字

    // 立即设置目标节点为红色高亮
    setTargetNodesHighlight(item);

    // 开始拖拽
    QTreeWidget::startDrag(supportedActions);

    // 拖拽完成后立即恢复原始颜色
    restoreDraggedItemColor();

    // 清除目标节点的红色高亮
    clearTargetNodesHighlight();

    // 使用多级延迟恢复确保在所有情况下都能恢复颜色
    QTimer::singleShot(100, this, [this]() {
        restoreDraggedItemColor();
        clearTargetNodesHighlight();
        forceRestoreAllColors();
        // 通过主窗口强制恢复所有树控件的颜色
        if (m_mainWindow) {
            m_mainWindow->ForceRestoreAllTreeColors();
        }
    });

    // 最终保险恢复
    QTimer::singleShot(300, this, [this]() {
        restoreDraggedItemColor();
        clearTargetNodesHighlight();
        forceRestoreAllColors();
        // 再次通过主窗口强制恢复
        if (m_mainWindow) {
            m_mainWindow->ForceRestoreAllTreeColors();
        }
    });
}

void CustomHardwareTreeWidget::restoreDraggedItemColor() {
    if (m_draggedItem) {
        // 恢复为保存的原始颜色，如果原始颜色无效则使用透明背景
        if (m_originalBackgroundColor.style() != Qt::NoBrush) {
            m_draggedItem->setBackground(0, m_originalBackgroundColor);
        } else {
            m_draggedItem->setBackground(0, QBrush()); // 透明背景
        }

        if (m_originalTextColor.style() != Qt::NoBrush) {
            m_draggedItem->setForeground(0, m_originalTextColor);
        } else {
            m_draggedItem->setForeground(0, QBrush()); // 默认文字颜色
        }

        m_draggedItem = nullptr;
    }
}

void CustomHardwareTreeWidget::forceRestoreAllColors() {
    // 强制恢复拖拽源颜色
    restoreDraggedItemColor();

    // 遍历所有项目，强制恢复为默认颜色（清除所有自定义颜色）
    QTreeWidgetItemIterator it(this);
    while (*it) {
        QTreeWidgetItem* item = *it;
        // 恢复为默认颜色（透明背景，使用系统默认）
        item->setBackground(0, QBrush());
        item->setForeground(0, QBrush());
        ++it;
    }
}

void CustomHardwareTreeWidget::setTargetNodesHighlight(QTreeWidgetItem* sourceItem) {
    if (!m_mainWindow || !sourceItem) {
        return;
    }

    // 获取源节点的类型
    QString sourceType = m_mainWindow->getItemTypePublic(sourceItem);
    qDebug() << "Setting target highlight for source type:" << sourceType;

    // 获取测试配置树控件
    CustomTestConfigTreeWidget* testConfigTree = m_mainWindow->getTestConfigTreeWidget();
    if (!testConfigTree) {
        qDebug() << "ERROR: Cannot get test config tree widget";
        return;
    }

    // 遍历测试配置树，找到所有可接受的目标节点并设置红色高亮
    QTreeWidgetItemIterator it(testConfigTree);
    while (*it) {
        QTreeWidgetItem* targetItem = *it;
        if (m_mainWindow->canAcceptDropPublic(targetItem, sourceType)) {
            qDebug() << "Setting red highlight for target:" << targetItem->text(0);

            // 通知测试配置树设置此节点为红色高亮
            testConfigTree->setItemHighlight(targetItem, true);
        }
        ++it;
    }
}

void CustomHardwareTreeWidget::clearTargetNodesHighlight() {
    if (!m_mainWindow) {
        return;
    }

    qDebug() << "Clearing target nodes highlight";

    // 获取测试配置树控件
    CustomTestConfigTreeWidget* testConfigTree = m_mainWindow->getTestConfigTreeWidget();
    if (!testConfigTree) {
        qDebug() << "ERROR: Cannot get test config tree widget for clearing highlight";
        return;
    }

    // 遍历测试配置树，清除所有节点的高亮
    QTreeWidgetItemIterator it(testConfigTree);
    while (*it) {
        QTreeWidgetItem* targetItem = *it;
        testConfigTree->setItemHighlight(targetItem, false);
        ++it;
    }
}

void CustomHardwareTreeWidget::mousePressEvent(QMouseEvent* event) {
    // 鼠标按下时，如果有拖拽项目，先恢复颜色
    if (m_draggedItem) {
        restoreDraggedItemColor();
    }
    QTreeWidget::mousePressEvent(event);
}

void CustomHardwareTreeWidget::mouseReleaseEvent(QMouseEvent* event) {
    // 鼠标释放时，确保恢复颜色（补偿dragEndEvent的功能）
    QTimer::singleShot(50, this, [this]() {
        restoreDraggedItemColor();
        forceRestoreAllColors();
        if (m_mainWindow) {
            m_mainWindow->ForceRestoreAllTreeColors();
        }
    });

    // 延迟再次恢复，确保拖拽结束后颜色完全恢复
    QTimer::singleShot(200, this, [this]() {
        restoreDraggedItemColor();
        forceRestoreAllColors();
        if (m_mainWindow) {
            m_mainWindow->ForceRestoreAllTreeColors();
        }
    });

    QTreeWidget::mouseReleaseEvent(event);
}

void CustomHardwareTreeWidget::dragEnterEvent(QDragEnterEvent* event) {
    // 拖拽进入时确保恢复之前的颜色
    restoreDraggedItemColor();
    QTreeWidget::dragEnterEvent(event);
}

void CustomHardwareTreeWidget::dragLeaveEvent(QDragLeaveEvent* event) {
    // 拖拽离开时确保恢复颜色
    restoreDraggedItemColor();
    QTreeWidget::dragLeaveEvent(event);
}



// ============================================================================
// CustomTestConfigTreeWidget Implementation
// ============================================================================

CustomTestConfigTreeWidget::CustomTestConfigTreeWidget(QWidget* parent)
    : QTreeWidget(parent), m_mainWindow(nullptr), m_lastHighlightedItem(nullptr) {
    setAcceptDrops(true);
    setDragDropMode(QAbstractItemView::DropOnly);
    setDefaultDropAction(Qt::CopyAction);
}

CustomTestConfigTreeWidget::~CustomTestConfigTreeWidget() {
    // 确保在析构时恢复任何可能残留的颜色变化
    restoreTargetItemColor();
}

// 设置主窗口引用
void CustomTestConfigTreeWidget::setMainWindow(CMyMainWindow* mainWindow) {
    m_mainWindow = mainWindow;
}

void CustomTestConfigTreeWidget::dragEnterEvent(QDragEnterEvent* event) {
    if (event->mimeData()->hasText()) {
        event->acceptProposedAction();
    } else {
        event->ignore();
    }
}

void CustomTestConfigTreeWidget::dragMoveEvent(QDragMoveEvent* event) {
    qDebug() << "=== dragMoveEvent START ===";

    if (!m_mainWindow) {
        qDebug() << "ERROR: m_mainWindow is null!";
        event->ignore();
        return;
    }
    qDebug() << "m_mainWindow is valid";

    // 智能恢复之前高亮的项目（如果是有效目标则恢复为基础绿色）
    if (m_lastHighlightedItem) {
        smartRestoreItemColor(m_lastHighlightedItem);
        m_lastHighlightedItem = nullptr;
    }
    qDebug() << "Previous target color restored";

    QTreeWidgetItem* targetItem = itemAt(event->pos());
    qDebug() << "targetItem:" << (targetItem ? targetItem->text(0) : "NULL");

    if (targetItem && event->mimeData()->hasText()) {
        QString sourceData = event->mimeData()->text();
        qDebug() << "sourceData:" << sourceData;

        QStringList parts = sourceData.split("|");
        qDebug() << "parts count:" << parts.size();

        if (parts.size() >= 2) {
            QString sourceType = parts[1];
            qDebug() << "sourceType:" << sourceType;

            // 保存当前拖拽的源类型
            m_currentDragSourceType = sourceType;

            bool canAccept = m_mainWindow->canAcceptDropPublic(targetItem, sourceType);
            qDebug() << "canAcceptDropPublic result:" << canAccept;

            if (canAccept) {
                qDebug() << "Setting highlight colors for target:" << targetItem->text(0);

                // 保存原始颜色并设置高亮
                QBrush originalBg = targetItem->background(0);
                QBrush originalFg = targetItem->foreground(0);

                qDebug() << "Original background style:" << originalBg.style();
                qDebug() << "Original foreground style:" << originalFg.style();

                // 如果原始颜色无效，使用默认透明背景
                m_originalTargetBackgroundColor = originalBg.style() != Qt::NoBrush ? originalBg : QBrush();
                m_originalTargetTextColor = originalFg.style() != Qt::NoBrush ? originalFg : QBrush();
                m_lastHighlightedItem = targetItem;

                // 设置悬停时的高亮颜色（深绿色背景，白色文字 - 与基础绿色形成明显对比）
                targetItem->setBackground(0, QColor(34, 139, 34)); // 深绿色背景
                targetItem->setForeground(0, QColor(255, 255, 255)); // 白色文字

                qDebug() << "Hover highlight colors set - Background: QColor(34, 139, 34), Foreground: QColor(255, 255, 255)";

                // 强制刷新控件显示
                this->update();
                this->repaint();

                // 也尝试刷新特定项目
                QRect itemRect = this->visualItemRect(targetItem);
                this->update(itemRect);

                qDebug() << "Forced widget refresh - update() and repaint() called";

                // 验证颜色是否设置成功
                QBrush newBg = targetItem->background(0);
                QBrush newFg = targetItem->foreground(0);
                qDebug() << "Verification - New background color:" << newBg.color();
                qDebug() << "Verification - New foreground color:" << newFg.color();

                event->acceptProposedAction();
                qDebug() << "Event accepted";
                return;
            } else {
                qDebug() << "Cannot accept drop for this target";
            }
        } else {
            qDebug() << "Invalid parts size:" << parts.size();
        }
    } else {
        qDebug() << "No target item or no text data";
    }

    qDebug() << "Event ignored";
    event->ignore();
    qDebug() << "=== dragMoveEvent END ===";
}

void CustomTestConfigTreeWidget::dragLeaveEvent(QDragLeaveEvent* event) {
    // 恢复高亮项目的颜色
    restoreTargetItemColor();
    QTreeWidget::dragLeaveEvent(event);
}

void CustomTestConfigTreeWidget::dropEvent(QDropEvent* event) {
    // 恢复高亮项目的颜色
    restoreTargetItemColor();

    if (!m_mainWindow) {
        event->ignore();
        return;
    }

    QTreeWidgetItem* targetItem = itemAt(event->pos());
    if (targetItem && event->mimeData()->hasText()) {
        QString sourceData = event->mimeData()->text();
        QStringList parts = sourceData.split("|");
        if (parts.size() >= 2) {
            QString sourceText = parts[0];
            QString sourceType = parts[1];
            QString parentText = parts.size() >= 3 ? parts[2] : "";

            if (m_mainWindow->canAcceptDropPublic(targetItem, sourceType)) {
                // 传递完整的拖拽信息，包括父节点信息
                m_mainWindow->handleDragDropAssociationWithParentPublic(targetItem, sourceText, sourceType, parentText);
                event->acceptProposedAction();

                // 拖拽成功后，使用多级延迟恢复确保所有颜色都正确恢复
                QTimer::singleShot(50, this, [this]() {
                    restoreTargetItemColor();
                    forceRestoreAllColors();
                    if (m_mainWindow) {
                        m_mainWindow->ForceRestoreAllTreeColors();
                    }
                });

                QTimer::singleShot(150, this, [this]() {
                    restoreTargetItemColor();
                    forceRestoreAllColors();
                    if (m_mainWindow) {
                        m_mainWindow->ForceRestoreAllTreeColors();
                    }
                });

                QTimer::singleShot(300, this, [this]() {
                    restoreTargetItemColor();
                    forceRestoreAllColors();
                    if (m_mainWindow) {
                        m_mainWindow->ForceRestoreAllTreeColors();
                    }
                });
                return;
            }
        }
    }

    // 拖拽失败时也要确保颜色恢复
    QTimer::singleShot(50, this, [this]() {
        restoreTargetItemColor();
        forceRestoreAllColors();
        if (m_mainWindow) {
            m_mainWindow->ForceRestoreAllTreeColors();
        }
    });

    QTimer::singleShot(200, this, [this]() {
        restoreTargetItemColor();
        forceRestoreAllColors();
        if (m_mainWindow) {
            m_mainWindow->ForceRestoreAllTreeColors();
        }
    });
    event->ignore();
}

void CustomTestConfigTreeWidget::restoreTargetItemColor() {
    if (m_lastHighlightedItem) {
        // 恢复为保存的原始颜色，如果原始颜色无效则使用透明背景
        if (m_originalTargetBackgroundColor.style() != Qt::NoBrush) {
            m_lastHighlightedItem->setBackground(0, m_originalTargetBackgroundColor);
        } else {
            m_lastHighlightedItem->setBackground(0, QBrush()); // 透明背景
        }

        if (m_originalTargetTextColor.style() != Qt::NoBrush) {
            m_lastHighlightedItem->setForeground(0, m_originalTargetTextColor);
        } else {
            m_lastHighlightedItem->setForeground(0, QBrush()); // 默认文字颜色
        }

        // 强制刷新控件显示
        this->update();
        this->repaint();

        m_lastHighlightedItem = nullptr;
    }

    // 清除拖拽状态
    m_currentDragSourceType.clear();
}

void CustomTestConfigTreeWidget::forceRestoreAllColors() {
    // 强制恢复目标节点颜色
    restoreTargetItemColor();

    // 遍历所有项目，强制恢复为默认颜色（清除所有自定义颜色）
    QTreeWidgetItemIterator it(this);
    while (*it) {
        QTreeWidgetItem* item = *it;
        // 恢复为默认颜色（透明背景，使用系统默认）
        item->setBackground(0, QBrush());
        item->setForeground(0, QBrush());
        ++it;
    }
}

void CustomTestConfigTreeWidget::setItemHighlight(QTreeWidgetItem* item, bool highlight) {
    if (!item) {
        return;
    }

    if (highlight) {
        // 保存原始颜色
        QBrush originalBg = item->background(0);
        QBrush originalFg = item->foreground(0);

        // 设置绿色高亮（拖拽开始时的基础高亮）
        item->setBackground(0, QColor(144, 238, 144)); // 浅绿色背景
        item->setForeground(0, QColor(0, 100, 0));     // 深绿色文字

        qDebug() << "Set green highlight for item:" << item->text(0);

        // 强制刷新显示
        this->update();
        this->repaint();

    } else {
        // 恢复原始颜色
        item->setBackground(0, QBrush());
        item->setForeground(0, QBrush());

        qDebug() << "Restored original color for item:" << item->text(0);

        // 强制刷新显示
        this->update();
        this->repaint();
    }
}

void CustomTestConfigTreeWidget::smartRestoreItemColor(QTreeWidgetItem* item) {
    if (!item || !m_mainWindow) {
        return;
    }

    // 检查当前是否有拖拽操作正在进行
    // 使用保存的拖拽源类型来判断
    if (!m_currentDragSourceType.isEmpty()) {
        // 如果当前节点是有效目标，恢复为基础绿色
        if (m_mainWindow->canAcceptDropPublic(item, m_currentDragSourceType)) {
            item->setBackground(0, QColor(144, 238, 144)); // 基础绿色
            item->setForeground(0, QColor(0, 100, 0));     // 深绿色文字
            qDebug() << "Restored to base green highlight for:" << item->text(0);
            this->update();
            return;
        }
    }

    // 如果不是有效目标或没有拖拽操作，恢复原色
    item->setBackground(0, QBrush());
    item->setForeground(0, QBrush());
    qDebug() << "Restored to original color for:" << item->text(0);
    this->update();
}
