# 硬件节点编辑功能使用指南

## 功能概述

硬件节点编辑功能允许您轻松修改现有硬件节点的配置参数，包括节点名称、通道数量、IP地址和端口设置。

## 使用步骤

### 1. 定位硬件节点
- 在主界面的**硬件树**中找到需要编辑的硬件节点
- 硬件节点通常显示为 `LD-B1`、`LD-B2` 等名称

### 2. 打开编辑对话框
- 在硬件节点上**右键点击**
- 从弹出的上下文菜单中选择 **"编辑硬件节点"**

### 3. 修改配置参数
编辑对话框将显示当前的配置，您可以修改：

#### 基本信息
- **节点名称**：硬件节点的标识名称（如 LD-B1, LD-B2）
- **通道数量**：硬件节点包含的通道数目（1-32）

#### 通道配置
对于每个通道，您可以设置：
- **IP地址**：通道的网络地址
- **端口**：通信端口号
- **启用状态**：是否启用该通道

### 4. 保存更改
- 检查所有参数设置是否正确
- 点击 **"保存"** 按钮确认更改
- 点击 **"取消"** 按钮放弃修改

### 5. 确认结果
- 系统将显示操作成功的确认消息
- 硬件树中的节点显示将自动更新
- 所有相关的配置关联将同步更新

## 注意事项

### 🔍 节点名称验证
- 节点名称必须唯一，不能与现有节点重复
- 如果输入重复名称，系统会提示错误并要求重新输入

### 📡 通道配置建议
- **IP地址格式**：确保使用有效的IP地址格式（如 *************）
- **端口范围**：建议使用 1024-65535 范围内的端口号
- **网络规划**：确保不同通道使用不同的IP地址和端口

### ⚠️ 操作安全
- 编辑硬件节点会影响相关的试验配置关联
- 建议在修改前备份重要配置
- 大幅修改前确认不会影响正在进行的试验

## 常见操作示例

### 示例1：更改节点名称
1. 右键点击节点 `LD-B1`
2. 选择"编辑硬件节点"
3. 将节点名称改为 `LD-B3`
4. 点击"保存"

### 示例2：添加通道
1. 右键点击目标硬件节点
2. 选择"编辑硬件节点"
3. 将通道数量从 2 增加到 3
4. 为新通道配置IP和端口
5. 点击"保存"

### 示例3：修改通道IP
1. 右键点击硬件节点
2. 选择"编辑硬件节点"
3. 修改 CH1 的IP地址为新的网络地址
4. 点击"保存"

## 故障排除

### Q: 编辑菜单没有出现？
**A:** 确认您右键点击的是硬件节点，而不是通道或其他类型的节点。

### Q: 提示"节点不存在"？
**A:** 可能是节点已被删除或数据同步问题，请刷新界面或重启软件。

### Q: 保存失败？
**A:** 检查以下项目：
- 节点名称是否唯一
- IP地址格式是否正确
- 端口号是否在有效范围内
- 数据管理器是否正常初始化

### Q: 更改后界面没有更新？
**A:** 通常系统会自动更新，如果没有显示变化，请检查操作日志或重启软件。

## 相关功能

- **创建硬件节点**：通过硬件树根节点的右键菜单
- **删除硬件节点**：通过硬件节点的右键菜单
- **导入/导出配置**：通过主菜单的文件操作
- **试验配置关联**：在试验配置树中进行拖拽关联

## 技术支持

如果在使用过程中遇到问题，请：
1. 查看软件日志中的相关信息
2. 确认操作步骤是否正确
3. 检查网络和硬件连接状态
4. 联系技术支持团队获取帮助 