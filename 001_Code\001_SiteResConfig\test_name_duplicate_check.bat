@echo off
echo ========================================
echo  名称重复检查功能测试
echo ========================================

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o
if exist "ui_*.h" del ui_*.h

echo.
echo 生成UI头文件...
uic ui\MainWindow.ui -o ui_MainWindow.h
if errorlevel 1 (
    echo UI文件生成失败！
    pause
    exit /b 1
) else (
    echo ✅ UI头文件生成成功: ui_MainWindow.h
)

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo.
    echo 请检查以下可能的问题：
    echo 1. 名称检查函数声明和定义
    echo 2. IsNameExistsInTree函数实现
    echo 3. QMessageBox头文件包含
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！名称重复检查功能已实现
    echo ========================================
    
    if exist "SiteResConfig.exe" (
        echo 可执行文件: SiteResConfig.exe
        echo.
        echo ✅ 名称重复检查功能已实现！
        echo.
        echo 🔍 作动器组名称检查:
        echo ├─ 检查函数: IsActuatorGroupNameExists()
        echo ├─ 检查范围: 作动器根节点下的所有子组
        echo ├─ 触发时机: 创建作动器组时
        echo ├─ 预定义组: 50kN_作动器组, 100kN_作动器组等
        echo └─ 自定义组: 用户输入时实时检查
        echo.
        echo 🔍 传感器组名称检查:
        echo ├─ 检查函数: IsSensorGroupNameExists()
        echo ├─ 检查范围: 传感器根节点下的所有子组
        echo ├─ 触发时机: 创建传感器组时
        echo ├─ 预定义组: 载荷_传感器组, 位置_传感器组等
        echo └─ 自定义组: 用户输入时实时检查
        echo.
        echo 🔍 硬件节点名称检查:
        echo ├─ 检查函数: IsHardwareNodeNameExists()
        echo ├─ 检查范围: 硬件节点资源下的所有子节点
        echo ├─ 触发时机: 创建硬件节点时
        echo ├─ 智能生成: GenerateNextHardwareNodeName()
        echo └─ 格式: LD-B1, LD-B2, LD-B3...
        echo.
        echo 🛡️ 重复检查机制:
        echo ├─ 核心函数: IsNameExistsInTree()
        echo ├─ 检查方式: 遍历指定父节点的所有子项
        echo ├─ 比较方式: 精确字符串匹配
        echo ├─ 错误提示: QMessageBox::warning()
        echo └─ 日志记录: AddLogEntry("WARNING", ...)
        echo.
        echo 📋 测试步骤:
        echo.
        echo 🎯 作动器组重复测试:
        echo 1. 右键"作动器" → 新建 → 作动器组
        echo 2. 选择"50kN_作动器组"（第一次创建）
        echo 3. 再次右键"作动器" → 新建 → 作动器组
        echo 4. 再次选择"50kN_作动器组"（应该显示重复警告）
        echo 5. 选择"自定义..." → 输入"测试组"
        echo 6. 再次自定义输入"测试组"（应该显示重复警告）
        echo.
        echo 🎯 传感器组重复测试:
        echo 1. 右键"传感器" → 新建 → 传感器组
        echo 2. 选择"载荷"（创建"载荷_传感器组"）
        echo 3. 再次右键"传感器" → 新建 → 传感器组
        echo 4. 再次选择"载荷"（应该显示重复警告）
        echo 5. 选择"自定义..." → 输入"压力"（创建"压力_传感器组"）
        echo 6. 再次自定义输入"压力"（应该显示重复警告）
        echo.
        echo 🎯 硬件节点重复测试:
        echo 1. 右键"硬件节点资源" → 新建硬件节点
        echo 2. 使用默认名称"LD-B1"创建
        echo 3. 再次右键"硬件节点资源" → 新建硬件节点
        echo 4. 默认名称应该自动变为"LD-B2"
        echo 5. 手动修改名称为"LD-B1"（应该显示重复警告）
        echo.
        echo 🔍 验证要点:
        echo ├─ 重复名称被正确检测
        echo ├─ 警告对话框正确显示
        echo ├─ 用户可以重新输入名称
        echo ├─ 日志记录重复警告
        echo ├─ 智能名称生成避免重复
        echo └─ 不同类型的节点名称可以相同
        echo.
        echo 💡 设计特点:
        echo ├─ 分类检查: 不同类型节点独立检查
        echo ├─ 实时验证: 用户输入时即时检查
        echo ├─ 友好提示: 清晰的错误信息
        echo ├─ 智能生成: 自动避免重复的名称
        echo └─ 循环输入: 重复时可重新输入
        echo.
        echo 🚀 技术实现:
        echo ├─ IsNameExistsInTree(): 通用名称检查函数
        echo ├─ IsActuatorGroupNameExists(): 作动器组专用检查
        echo ├─ IsSensorGroupNameExists(): 传感器组专用检查
        echo ├─ IsHardwareNodeNameExists(): 硬件节点专用检查
        echo ├─ GenerateNextHardwareNodeName(): 智能名称生成
        echo └─ while循环: 重复输入直到名称有效
        echo.
        echo 启动程序测试名称重复检查功能...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
        echo 启动程序...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
        echo 启动程序...
        start release\SiteResConfig.exe
    ) else (
        echo 警告: 找不到可执行文件！
    )
)

echo.
echo 📖 名称重复检查功能详细测试指南:
echo.
echo 🎯 作动器组重复检查测试:
echo 1. 创建第一个作动器组
echo    - 右键"作动器"节点
echo    - 选择"新建" → "作动器组"
echo    - 选择"50kN_作动器组"
echo    - 验证创建成功
echo.
echo 2. 尝试创建重复的作动器组
echo    - 再次右键"作动器"节点
echo    - 选择"新建" → "作动器组"
echo    - 再次选择"50kN_作动器组"
echo    - 应该显示警告: "作动器组名称 '50kN_作动器组' 已存在！"
echo    - 验证组没有被重复创建
echo.
echo 3. 自定义作动器组重复检查
echo    - 选择"自定义..."
echo    - 输入"测试作动器组"
echo    - 验证创建成功
echo    - 再次自定义输入"测试作动器组"
echo    - 应该显示重复警告并要求重新输入
echo.
echo 🎯 传感器组重复检查测试:
echo 1. 创建第一个传感器组
echo    - 右键"传感器"节点
echo    - 选择"新建" → "传感器组"
echo    - 选择"载荷"（创建"载荷_传感器组"）
echo    - 验证创建成功
echo.
echo 2. 尝试创建重复的传感器组
echo    - 再次选择"载荷"
echo    - 应该显示警告: "传感器组名称 '载荷_传感器组' 已存在！"
echo    - 验证组没有被重复创建
echo.
echo 🎯 硬件节点重复检查测试:
echo 1. 创建第一个硬件节点
echo    - 右键"硬件节点资源"
echo    - 选择"新建硬件节点"
echo    - 使用默认名称"LD-B1"
echo    - 验证创建成功
echo.
echo 2. 智能名称生成测试
echo    - 再次创建硬件节点
echo    - 默认名称应该自动变为"LD-B2"
echo    - 验证智能避免重复
echo.
echo 3. 手动重复名称测试
echo    - 在对话框中手动修改名称为"LD-B1"
echo    - 应该显示重复警告
echo    - 验证重复检查有效
echo.
echo 🔍 验证清单:
echo ✓ 作动器组名称不能重复
echo ✓ 传感器组名称不能重复  
echo ✓ 硬件节点名称不能重复
echo ✓ 重复时显示警告对话框
echo ✓ 用户可以重新输入名称
echo ✓ 日志记录重复警告信息
echo ✓ 智能名称生成避免重复
echo ✓ 不同类型节点可以同名
echo.
pause
