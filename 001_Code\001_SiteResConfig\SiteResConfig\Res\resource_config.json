{"resource_paths": {"templates": {"base_path": ".", "fallback_paths": ["../Res", "../../Res", "./Res", "Res", "../SiteResConfig/Res", "../../SiteResConfig/Res"], "templates": {"detail_panel": "detail_panel.html", "error_page": "error_page.html", "loading_page": "loading_page.html"}}, "styles": {"base_path": ".", "fallback_paths": ["../Res", "../../Res", "./Res", "Res"], "files": ["style.css"]}, "images": {"base_path": ".", "fallback_paths": ["../Res", "../../Res", "./Res", "Res"], "files": ["plus.png"]}}, "loading_strategy": {"primary": "qt_resource", "fallback": "file_system", "cache_enabled": false, "retry_attempts": 3, "retry_delay_ms": 1000}, "html_templates": {"detail_panel": {"description": "设备详细信息页面模板", "version": "1.0", "dependencies": ["style.css"], "data_injection": {"target_tag": "</head>", "script_name": "qtNodeData", "fallback_name": "nodeData"}}}}