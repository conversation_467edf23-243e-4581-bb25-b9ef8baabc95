/****************************************************************************
** Meta object code from reading C++ file 'DataChangeListener.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../001_SiteResConfig_OldStruct/SiteResConfig/include/DataChangeListener.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'DataChangeListener.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_DataChangeListener_t {
    QByteArrayData data[15];
    char stringdata0[249];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_DataChangeListener_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_DataChangeListener_t qt_meta_stringdata_DataChangeListener = {
    {
QT_MOC_LITERAL(0, 0, 18), // "DataChangeListener"
QT_MOC_LITERAL(1, 19, 19), // "onSensorDataChanged"
QT_MOC_LITERAL(2, 39, 0), // ""
QT_MOC_LITERAL(3, 40, 12), // "serialNumber"
QT_MOC_LITERAL(4, 53, 9), // "operation"
QT_MOC_LITERAL(5, 63, 24), // "onSensorGroupDataChanged"
QT_MOC_LITERAL(6, 88, 7), // "groupId"
QT_MOC_LITERAL(7, 96, 26), // "onSensorAssociationChanged"
QT_MOC_LITERAL(8, 123, 11), // "channelName"
QT_MOC_LITERAL(9, 135, 21), // "onActuatorDataChanged"
QT_MOC_LITERAL(10, 157, 26), // "onActuatorGroupDataChanged"
QT_MOC_LITERAL(11, 184, 28), // "onActuatorAssociationChanged"
QT_MOC_LITERAL(12, 213, 13), // "onSensorError"
QT_MOC_LITERAL(13, 227, 5), // "error"
QT_MOC_LITERAL(14, 233, 15) // "onActuatorError"

    },
    "DataChangeListener\0onSensorDataChanged\0"
    "\0serialNumber\0operation\0"
    "onSensorGroupDataChanged\0groupId\0"
    "onSensorAssociationChanged\0channelName\0"
    "onActuatorDataChanged\0onActuatorGroupDataChanged\0"
    "onActuatorAssociationChanged\0onSensorError\0"
    "error\0onActuatorError"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_DataChangeListener[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       8,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    2,   54,    2, 0x08 /* Private */,
       5,    2,   59,    2, 0x08 /* Private */,
       7,    2,   64,    2, 0x08 /* Private */,
       9,    2,   69,    2, 0x08 /* Private */,
      10,    2,   74,    2, 0x08 /* Private */,
      11,    2,   79,    2, 0x08 /* Private */,
      12,    1,   84,    2, 0x08 /* Private */,
      14,    1,   87,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void, QMetaType::QString, QMetaType::QString,    3,    4,
    QMetaType::Void, QMetaType::Int, QMetaType::QString,    6,    4,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,    3,    8,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,    3,    4,
    QMetaType::Void, QMetaType::Int, QMetaType::QString,    6,    4,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,    3,    8,
    QMetaType::Void, QMetaType::QString,   13,
    QMetaType::Void, QMetaType::QString,   13,

       0        // eod
};

void DataChangeListener::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<DataChangeListener *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->onSensorDataChanged((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 1: _t->onSensorGroupDataChanged((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 2: _t->onSensorAssociationChanged((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 3: _t->onActuatorDataChanged((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 4: _t->onActuatorGroupDataChanged((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 5: _t->onActuatorAssociationChanged((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 6: _t->onSensorError((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 7: _t->onActuatorError((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        default: ;
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject DataChangeListener::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_DataChangeListener.data,
    qt_meta_data_DataChangeListener,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *DataChangeListener::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *DataChangeListener::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_DataChangeListener.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int DataChangeListener::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 8)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 8;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 8)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 8;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
