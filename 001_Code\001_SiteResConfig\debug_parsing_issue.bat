@echo off
echo ========================================
echo  解析问题排查指南
echo ========================================

echo 问题现象：
echo - 之前存储了详细信息，这次没有存储
echo - 可能的原因需要逐一排查
echo.

echo 排查步骤：
echo.
echo 1. 检查控制台输出
echo    启动程序后查看控制台是否显示：
echo    - "=== 开始解析功能测试 ==="
echo    - "=== 调试树控件节点信息 ==="
echo    - 各种解析调试信息
echo.
echo 2. 检查节点数据
echo    保存CSV时控制台应显示：
echo    - "硬件树顶级项目数: X"
echo    - "节点: XXX"
echo    - "类型: XXX"  
echo    - "提示: XXX"
echo.
echo 3. 检查保存条件
echo    每个节点应显示：
echo    - "节点保存检查: XXX shouldSave=true/false"
echo    - "开始保存节点: XXX"（如果shouldSave=true）
echo.
echo 4. 检查解析过程
echo    对于每个保存的节点应显示：
echo    - "=== 解析节点信息 ==="
echo    - "节点名称: XXX"
echo    - "工具提示: XXX"
echo    - "源文本: XXX"
echo.

echo 可能的问题原因：
echo.
echo A. 节点信息为空
echo    - 节点名称为空
echo    - 工具提示为空
echo    - 导致没有可解析的内容
echo.
echo B. 保存条件不满足
echo    - 节点被过滤规则排除
echo    - shouldSave=false
echo    - 节点类型不匹配保存条件
echo.
echo C. 解析格式不匹配
echo    - 节点信息格式与解析规则不符
echo    - 没有冒号分隔符
echo    - 不是数值+单位格式
echo.
echo D. 调试输出被关闭
echo    - qDebug输出被禁用
echo    - 控制台没有显示调试信息
echo.

echo 解决方案：
echo.
echo 1. 如果没有调试输出：
echo    - 检查Qt Creator的"应用程序输出"面板
echo    - 确保qDebug()输出被启用
echo.
echo 2. 如果节点信息为空：
echo    - 检查节点是否有名称和工具提示
echo    - 确认节点数据正确设置
echo.
echo 3. 如果保存条件不满足：
echo    - 查看"shouldSave=false"的原因
echo    - 检查节点类型和保存规则
echo.
echo 4. 如果解析格式不匹配：
echo    - 确认节点信息包含":"分隔符
echo    - 或者是"数值 单位"格式
echo    - 检查具体的源文本内容
echo.

echo 测试建议：
echo 1. 重新编译并运行程序
echo 2. 查看启动时的解析测试输出
echo 3. 添加一些测试节点（确保有工具提示）
echo 4. 保存CSV并观察完整的调试输出
echo 5. 根据调试信息定位具体问题
echo.

echo 如果问题仍然存在，请提供：
echo - 完整的控制台调试输出
echo - 具体的节点名称和工具提示内容
echo - CSV文件的实际内容
echo.

pause
