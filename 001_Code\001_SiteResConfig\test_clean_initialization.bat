@echo off
echo ========================================
echo  空白初始化测试 - 无默认示例数据
echo ========================================

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o
if exist "ui_*.h" del ui_*.h

echo.
echo 生成UI头文件...
uic ui\MainWindow.ui -o ui_MainWindow.h
if errorlevel 1 (
    echo UI文件生成失败！
    pause
    exit /b 1
) else (
    echo ✅ UI头文件生成成功: ui_MainWindow.h
)

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo.
    echo 修改内容：
    echo ✅ 移除初始化时的默认示例数据
    echo ✅ 保持树形控件空白状态
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！空白初始化功能已实现
    echo ========================================
    
    if exist "SiteResConfig.exe" (
        echo 可执行文件: SiteResConfig.exe
        echo.
        echo ✅ 空白初始化功能已实现！
        echo.
        echo 🌳 初始化后的树形结构:
        echo 任务1
        echo ├─ 作动器 (类型: "作动器") - 空白，无默认设备
        echo ├─ 传感器 (类型: "传感器") - 空白，无默认设备
        echo └─ 硬件节点资源 (类型: "硬件节点资源") - 空白，无默认设备
        echo.
        echo ❌ 移除的默认示例数据:
        echo - 控制器节点1 (192.168.1.100)
        echo - 控制器节点2 (192.168.1.101)
        echo - 作动器1 (100kN, 单杆)
        echo - 作动器2 (50kN, 单杆)
        echo - 力传感器1 (100kN, 力)
        echo - 位移传感器1 (200mm, 位移)
        echo - 通道1 (50kN, 力控制)
        echo - 通道2 (30kN, 位移控制)
        echo.
        echo ✅ 保留的功能:
        echo - 右键菜单创建组和设备
        echo - 配置文件加载时添加设备
        echo - 手动配置时添加设备
        echo - 项目管理功能
        echo.
        echo 🎯 用户操作流程:
        echo.
        echo 1️⃣ 启动程序:
        echo   - 程序启动后显示空白的树形结构
        echo   - 只有三个根节点：作动器、传感器、硬件节点资源
        echo   - 没有任何默认的示例设备
        echo.
        echo 2️⃣ 创建作动器组:
        echo   - 右键"作动器"节点
        echo   - 选择"新建" → "作动器组"
        echo   - 选择规格或自定义名称
        echo.
        echo 3️⃣ 创建作动器设备:
        echo   - 右键创建的作动器组
        echo   - 选择"新建" → "作动器"
        echo   - 填写详细参数
        echo.
        echo 4️⃣ 创建其他设备:
        echo   - 类似地创建传感器组和传感器
        echo   - 创建硬件节点组和硬件节点
        echo.
        echo 💡 空白初始化的优势:
        echo.
        echo ✅ 干净的起始状态:
        echo - 用户看到的是完全空白的工作环境
        echo - 没有无关的示例数据干扰
        echo - 用户可以从零开始构建自己的配置
        echo.
        echo ✅ 专业的用户体验:
        echo - 符合专业软件的使用习惯
        echo - 用户完全控制数据的创建
        echo - 避免需要删除不需要的示例数据
        echo.
        echo ✅ 性能优化:
        echo - 启动时不需要创建示例数据
        echo - 减少初始化时间
        echo - 降低内存占用
        echo.
        echo ✅ 数据纯净性:
        echo - 确保所有数据都是用户有意创建的
        echo - 避免示例数据与真实数据混淆
        echo - 便于项目管理和数据导出
        echo.
        echo 🔧 技术实现:
        echo - LoadInitialData()方法中移除AddSample调用
        echo - 保留树形结构的基本框架
        echo - 保留所有创建功能的完整性
        echo - 添加日志提示用户可以手动创建设备
        echo.
        echo 📊 对比效果:
        echo.
        echo 之前的初始化:
        echo 任务1
        echo ├─ 作动器
        echo │  ├─ 作动器1 [100kN]
        echo │  └─ 作动器2 [50kN]
        echo ├─ 传感器
        echo │  ├─ 力传感器1 [100kN]
        echo │  └─ 位移传感器1 [200mm]
        echo └─ 硬件节点资源
        echo    ├─ 控制器节点1 [离线]
        echo    └─ 控制器节点2 [离线]
        echo.
        echo 现在的初始化:
        echo 任务1
        echo ├─ 作动器 (空白)
        echo ├─ 传感器 (空白)
        echo └─ 硬件节点资源 (空白)
        echo.
        echo 启动程序验证空白初始化...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
        echo 启动程序...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
        echo 启动程序...
        start release\SiteResConfig.exe
    ) else (
        echo 警告: 找不到可执行文件！
    )
)

echo.
echo 📖 空白初始化测试指南:
echo.
echo 🎯 验证要点:
echo.
echo 1️⃣ 启动验证:
echo   - 启动程序后检查硬件资源树
echo   - 验证只有三个根节点存在
echo   - 验证所有根节点下都是空白的
echo   - 验证没有任何示例设备显示
echo.
echo 2️⃣ 功能验证:
echo   - 验证右键菜单功能正常
echo   - 验证可以创建作动器组
echo   - 验证可以创建作动器设备
echo   - 验证所有创建功能都正常工作
echo.
echo 3️⃣ 日志验证:
echo   - 检查系统日志
echo   - 验证显示"硬件树初始化完成，等待用户创建设备"
echo   - 验证没有示例数据创建的日志
echo.
echo 4️⃣ 配置文件验证:
echo   - 测试加载配置文件功能
echo   - 验证配置文件加载时会添加相应设备
echo   - 验证这些设备是基于配置文件内容的
echo.
echo 🔍 测试步骤:
echo 1. 启动程序，观察初始树形结构
echo 2. 右键"作动器"创建作动器组
echo 3. 右键作动器组创建作动器设备
echo 4. 验证创建的设备正确显示
echo 5. 测试传感器和硬件节点的创建
echo 6. 验证所有功能都正常工作
echo.
pause
