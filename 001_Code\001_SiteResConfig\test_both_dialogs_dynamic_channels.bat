@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🔧 双对话框动态通道配置功能测试
echo ========================================
echo.

echo 📋 实现的功能:
echo.
echo ✅ NodeConfigDialog (节点配置对话框):
echo    ├─ 支持1-32个动态通道配置
echo    ├─ 通过channelCountSpinBox值改变触发
echo    ├─ 滚动区域支持大量通道显示
echo    ├─ 自动IP和端口分配
echo    └─ 完整的数据验证和保存
echo.
echo ✅ CreateHardwareNodeDialog (创建硬件节点对话框):
echo    ├─ 支持1-32个动态通道配置
echo    ├─ 通过channelCountSpinBox值改变触发
echo    ├─ 滚动区域支持大量通道显示
echo    ├─ 自动IP和端口分配
echo    ├─ 节点名称输入验证
echo    └─ 完整的数据验证和保存
echo.

echo 🎯 核心技术特性:
echo.
echo 1. 统一的通道配置结构:
echo    struct ChannelConfigWidget {
echo        QGroupBox* groupBox;
echo        QGridLayout* layout;
echo        QLabel* ipLabel;
echo        QLineEdit* ipEdit;
echo        QLabel* portLabel;
echo        QSpinBox* portSpinBox;
echo        QCheckBox* enabledCheckBox;
echo    };
echo.
echo 2. 动态生成逻辑:
echo    - clearChannelWidgets() 清理旧控件
echo    - createChannelWidget() 创建新控件
echo    - updateChannelUI() 更新界面
echo    - 自动调整窗口大小
echo.
echo 3. 滚动区域支持:
echo    - QScrollArea + QWidget + QVBoxLayout
echo    - 最大高度800px，超出时显示滚动条
echo    - 水平和垂直滚动按需显示
echo.

echo 💡 测试步骤:
echo.
echo 【NodeConfigDialog测试】:
echo 1. 启动程序
echo 2. 右键点击硬件节点 (如LD-B1)
echo 3. 选择"配置节点"
echo 4. 修改"通道数量"值 (1-32)
echo 5. 观察界面动态生成对应数量的通道配置
echo 6. 测试数据保存功能
echo.
echo 【CreateHardwareNodeDialog测试】:
echo 1. 启动程序
echo 2. 右键点击"硬件节点资源"
echo 3. 选择"创建硬件节点"
echo 4. 输入节点名称 (如LD-B3)
echo 5. 修改"通道数量"值 (1-32)
echo 6. 观察界面动态生成对应数量的通道配置
echo 7. 测试节点创建功能
echo.

echo 🔧 默认配置:
echo.
echo IP地址分配:
echo    CH1: *************
echo    CH2: *************
echo    CH3: *************
echo    ...
echo.
echo 端口分配:
echo    CH1: 8080
echo    CH2: 8081
echo    CH3: 8082
echo    ...
echo.
echo 启用状态:
echo    所有通道默认启用
echo.

echo 🔄 编译测试:
echo.

if exist "SiteResConfig\SiteResConfig_Simple.pro" (
    echo 找到项目文件，开始编译...
    echo.
    
    cd SiteResConfig
    
    echo 清理旧文件...
    if exist "Makefile" del /Q "Makefile" 2>nul
    if exist "debug" rmdir /S /Q "debug" 2>nul
    if exist "release" rmdir /S /Q "release" 2>nul
    
    echo.
    echo 生成Makefile...
    qmake SiteResConfig_Simple.pro -spec win32-g++ "CONFIG+=debug"
    
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo 开始编译...
        mingw32-make debug
        
        if %ERRORLEVEL% EQU 0 (
            echo.
            echo ✅ 编译成功！
            echo.
            if exist "debug\SiteResConfig.exe" (
                echo 🚀 启动程序测试双对话框动态通道配置...
                echo.
                echo 测试建议:
                echo.
                echo 📊 通道数量测试:
                echo 1. 设置为 1 - 测试单通道显示
                echo 2. 设置为 4 - 测试多通道显示
                echo 3. 设置为 8 - 测试中等数量通道
                echo 4. 设置为 16 - 测试滚动功能
                echo 5. 设置为 32 - 测试最大通道数
                echo.
                echo 🔧 功能测试:
                echo 1. 修改IP地址和端口
                echo 2. 切换启用/禁用状态
                echo 3. 保存和加载配置
                echo 4. 窗口大小自适应
                echo 5. 滚动条显示
                echo.
                start "" "debug\SiteResConfig.exe"
            ) else (
                echo ❌ 可执行文件未找到
            )
        ) else (
            echo ❌ 编译失败
        )
    ) else (
        echo ❌ qmake失败
    )
    
    cd ..
) else (
    echo ❌ 项目文件未找到
    echo 请确保在正确的目录中运行此脚本
)

echo.
echo ========================================
echo 🔧 双对话框动态通道配置已完成！
echo ========================================
echo.
echo 实现的对话框:
echo ✅ NodeConfigDialog - 节点配置对话框
echo ✅ CreateHardwareNodeDialog - 创建硬件节点对话框
echo.
echo 主要特性:
echo ✅ 支持1-32个动态通道
echo ✅ 滚动区域支持大量通道
echo ✅ 自动IP和端口分配
echo ✅ 完整的数据验证和保存
echo ✅ 统一的用户界面设计
echo ✅ 高效的内存管理
echo.
pause
