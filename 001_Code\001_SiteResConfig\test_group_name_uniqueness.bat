@echo off
echo ========================================
echo  测试组内名称唯一性检查功能
echo ========================================

REM 设置Qt环境变量
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目（测试组内名称唯一性检查）...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo 请检查上面的错误信息。
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！组内名称唯一性检查功能已实现
    echo ========================================
    
    echo.
    echo ✅ 实现的功能:
    echo - 作动器组内序列号唯一性检查
    echo - 传感器组内序列号唯一性检查
    echo - 使用serialNumber字段作为名称进行检查
    echo - 只在同一组内检查重复，不同组间可以同名
    echo.
    echo 🔧 实现原理:
    echo 1. 作动器组内检查: isActuatorSerialNumberExistsInGroup()
    echo 2. 传感器组内检查: isSensorSerialNumberExistsInGroup()
    echo 3. 检查时机: 用户创建作动器/传感器时
    echo 4. 检查范围: 仅限当前组内的子设备
    echo 5. 检查字段: serialNumber（序列号）
    echo.
    echo 🎯 测试步骤:
    echo 1. 启动软件
    echo 2. 新建项目
    echo 3. 创建作动器组A
    echo 4. 在组A中添加作动器（序列号：ACT001）
    echo 5. 在组A中再次添加作动器（序列号：ACT001）→ 应该提示重复
    echo 6. 创建作动器组B
    echo 7. 在组B中添加作动器（序列号：ACT001）→ 应该成功（不同组）
    echo 8. 对传感器组进行相同测试
    echo.
    
    if exist "SiteResConfig.exe" (
        echo 启动程序验证功能...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 启动程序验证功能...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 启动程序验证功能...
        start release\SiteResConfig.exe
    )
)

echo.
echo 📖 详细测试指南:
echo.
echo 🎮 作动器组内名称唯一性测试:
echo 1. 启动软件后，新建一个项目
echo 2. 在硬件树中右键"作动器"节点
echo 3. 选择"新建" → "作动器组"，创建"测试组A"
echo 4. 在"测试组A"上右键，选择"新建作动器"
echo 5. 输入序列号"ACT001"，保存 → 应该成功
echo 6. 再次在"测试组A"上右键，选择"新建作动器"
echo 7. 输入相同序列号"ACT001"，保存 → 应该提示"组内作动器名称重复"
echo 8. 创建"测试组B"
echo 9. 在"测试组B"中添加序列号"ACT001"的作动器 → 应该成功（不同组）
echo.
echo 🎮 传感器组内名称唯一性测试:
echo 1. 在硬件树中右键"传感器"节点
echo 2. 选择"新建" → "传感器组"，创建"载荷_传感器组"
echo 3. 在"载荷_传感器组"上右键，选择"新建传感器"
echo 4. 输入序列号"SENSOR001"，保存 → 应该成功
echo 5. 再次在"载荷_传感器组"上右键，选择"新建传感器"
echo 6. 输入相同序列号"SENSOR001"，保存 → 应该提示"组内传感器名称重复"
echo 7. 创建"位置_传感器组"
echo 8. 在"位置_传感器组"中添加序列号"SENSOR001"的传感器 → 应该成功（不同组）
echo.
echo ✅ 预期结果:
echo - 同一组内不能有相同序列号的设备
echo - 不同组间可以有相同序列号的设备
echo - 提示信息清晰明确
echo - 日志记录操作结果
echo.
echo 🚨 如果测试失败:
echo - 检查错误提示信息
echo - 查看日志中的详细记录
echo - 确认序列号输入正确
echo.
pause
