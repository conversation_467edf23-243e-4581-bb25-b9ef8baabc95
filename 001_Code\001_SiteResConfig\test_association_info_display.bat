@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 测试实验资源关联信息显示功能
echo ========================================
echo.

echo 🎯 测试目标：
echo - 验证"新建工程"后实验资源树显示关联信息
echo - 验证"打开工程"后实验资源树显示关联信息
echo - 确保两种情况下的显示结构一致
echo.

echo 📋 测试步骤：
echo.
echo 1. 启动程序
echo 2. 执行"新建工程"菜单
echo 3. 观察"实验资源"树形控件显示：
echo    ├── 实验
echo    │   ├── 指令
echo    │   ├── DI
echo    │   ├── DO
echo    │   └── 控制通道
echo    │       ├── CH1                    ^| LD-B1 - CH1
echo    │       │   ├── 载荷1              ^| 载荷_传感器组 - 传感器_000001
echo    │       │   ├── 载荷2              ^| 载荷_传感器组 - 传感器_000002
echo    │       │   ├── 位置               ^| 位移_传感器组 - 传感器_000003
echo    │       │   └── 控制               ^| 50kN_作动器组 - 作动器_000001
echo    │       └── CH2                    ^| LD-B1 - CH2
echo    │           ├── 载荷1              ^| [关联信息]
echo    │           ├── 载荷2              ^| [关联信息]
echo    │           ├── 位置               ^| [关联信息]
echo    │           └── 控制               ^| [关联信息]
echo.
echo 4. 保存工程为测试文件
echo 5. 执行"打开工程"菜单，选择刚保存的文件
echo 6. 验证"实验资源"树形控件显示结构与步骤3一致
echo.

echo ✅ 期望结果：
echo - 新建工程和打开工程后的实验资源树结构完全一致
echo - 每个节点都显示正确的关联信息
echo - CH1和CH2节点显示硬件节点关联信息
echo - 载荷1、载荷2、位置、控制子节点显示具体的设备关联信息
echo.

echo 🚀 启动程序进行测试...
echo.

cd build-SiteResConfig_Simple-Desktop_Qt_5_14_2_MinGW_32_bit-Debug\debug
if exist "SiteResConfig.exe" (
    echo 找到可执行文件，启动程序...
    start SiteResConfig.exe
    echo.
    echo 程序已启动，请按照上述步骤进行测试。
    echo.
    echo 📝 测试要点：
    echo - 关注实验资源树的第二列是否显示关联信息
    echo - 验证新建工程和打开工程的显示一致性
    echo - 检查关联信息的格式是否正确
    echo.
) else (
    echo ❌ 错误：找不到可执行文件 SiteResConfig.exe
    echo 请先编译项目。
)

pause
