# 🔧 组内ID按序分配功能实现报告

## 📋 需求描述

用户要求在打开工程时，所有组内的设备ID都按照组内排序重新分配，即每个组内的设备ID都从1开始递增，而不是全局唯一的ID。

### 🎯 预期效果

```
组1
├─ 设备1
├─ 设备ID = 1
├─ 设备2
├─ 设备ID = 2

组2
├─ 设备1
├─ 设备ID = 1
├─ 设备2
├─ 设备ID = 2
```

## ✅ 实现方案

### 1. **修改作动器导入逻辑**

**文件**: `XLSDataExporter.cpp`
**函数**: `importActuatorDetails()`
**位置**: 第2255-2271行

```cpp
// 🆕 新增：重新分配组内ID，确保每个组内的设备ID从1开始按序排列
for (auto it = groupMap.begin(); it != groupMap.end(); ++it) {
    UI::ActuatorGroup group = it.value();
    
    // 重新分配组内作动器ID，从1开始按序排列
    for (int i = 0; i < group.actuators.size(); ++i) {
        group.actuators[i].actuatorId = i + 1; // 组内ID从1开始
    }
    
    if (!actuatorDataManager_->saveActuatorGroup(group)) {
        importError_ = QString(u8"保存作动器组失败: %1").arg(group.groupName);
        return false;
    }
    
    qDebug() << QString(u8"✅ 作动器组 %1 (组ID: %2) 重新分配组内ID完成，设备数量: %3")
                .arg(group.groupName).arg(group.groupId).arg(group.actuators.size());
}
```

### 2. **修改传感器导入逻辑**

**文件**: `XLSDataExporter.cpp`
**函数**: `importSensorDetails()`
**位置**: 第2460-2476行

```cpp
// 🆕 新增：重新分配组内ID，确保每个组内的设备ID从1开始按序排列
for (auto it = groupMap.begin(); it != groupMap.end(); ++it) {
    UI::SensorGroup group = it.value();
    
    // 重新分配组内传感器ID，从1开始按序排列
    for (int i = 0; i < group.sensors.size(); ++i) {
        group.sensors[i].sensorId = i + 1; // 组内ID从1开始
    }
    
    if (!sensorDataManager_->saveSensorGroup(group)) {
        importError_ = QString(u8"保存传感器组失败: %1").arg(group.groupName);
        return false;
    }
    
    qDebug() << QString(u8"✅ 传感器组 %1 (组ID: %2) 重新分配组内ID完成，设备数量: %3")
                .arg(group.groupName).arg(group.groupId).arg(group.sensors.size());
}
```

## 🔧 技术实现细节

### **核心算法**
1. **保持组ID不变** - 只修改组内设备的ID
2. **按序重新分配** - 组内设备ID从1开始，按照在组内的位置递增
3. **独立分配** - 每个组内的ID分配是独立的，不同组可以有相同的设备ID

### **执行时机**
- 在`importActuatorDetails()`和`importSensorDetails()`函数中
- 在保存到数据管理器之前执行ID重新分配
- 确保保存到数据管理器的数据已经是重新分配后的ID

### **日志输出**
每个组完成ID重新分配后，都会输出详细的日志信息：
```
✅ 作动器组 液压_作动器组 (组ID: 1) 重新分配组内ID完成，设备数量: 2
✅ 传感器组 载荷_传感器组 (组ID: 1) 重新分配组内ID完成，设备数量: 2
```

## 🎯 功能效果

### **作动器组示例**
```
组ID: 1
ID: 1, 序号: 1
ID: 2, 序号: 2
```

### **传感器组示例**
```
组ID: 1
ID: 1, 序号: 1
ID: 2, 序号: 2
```

### **设备节点示例**
```
组ID: 1, ID: 2, 序号: 2
```

## 📊 验证方法

### 1. **DEBUG信息验证**
- 鼠标悬停在组节点上，查看DEBUG信息
- 确认每个组内的设备ID都从1开始
- 验证序号与ID的对应关系

### 2. **日志验证**
- 查看程序启动后的导入日志
- 搜索"重新分配组内ID完成"关键字
- 确认每个组都执行了ID重新分配

### 3. **界面验证**
- 查看树形控件中的设备显示
- 确认tooltip中显示的ID信息正确
- 验证组内设备的ID确实从1开始

## 🔄 工作流程

1. **用户打开工程** → `OnOpenProject()`
2. **调用XLS导入** → `LoadProjectFromXLS()`
3. **执行工程导入** → `xlsDataExporter_->importProject()`
4. **导入作动器数据** → `importActuatorDetails()`
5. **重新分配组内ID** → 作动器ID从1开始
6. **导入传感器数据** → `importSensorDetails()`
7. **重新分配组内ID** → 传感器ID从1开始
8. **刷新界面显示** → `refreshAllDataFromManagers()`

## 🎉 实现优势

✅ **用户友好** - 组内ID从1开始，符合用户习惯
✅ **逻辑清晰** - 每个组内的设备ID都是连续的
✅ **DEBUG简洁** - tooltip信息更加直观易懂
✅ **自动执行** - 在打开工程时自动重新分配，无需手动操作
✅ **保持兼容** - 不影响现有的数据结构和功能

## 📁 修改的文件

- **源文件**: `SiteResConfig/src/XLSDataExporter.cpp`
  - 修改了`importActuatorDetails()`函数
  - 修改了`importSensorDetails()`函数
  - 添加了组内ID重新分配逻辑

## 🎯 完成状态

✅ **作动器组内ID重新分配** - 已实现
✅ **传感器组内ID重新分配** - 已实现
✅ **日志输出功能** - 已实现
✅ **与现有DEBUG信息兼容** - 已验证

现在打开工程时，所有组内的设备ID都会按照组内排序从1开始重新分配！
