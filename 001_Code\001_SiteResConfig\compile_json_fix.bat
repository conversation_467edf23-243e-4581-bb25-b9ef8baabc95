@echo off
chcp 65001 >nul
echo ========================================
echo  JSON导出功能编译测试
echo ========================================

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目...
mingw32-make clean
mingw32-make -j2
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！检查JSON相关代码
    echo ========================================
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  编译成功！JSON功能已修复
    echo ========================================
    
    if exist "SiteResConfig.exe" (
        echo 可执行文件: SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
    ) else (
        echo 警告: 找不到可执行文件！
    )
)

pause
