# MainWindow作动器代码迁移至ActuatorViewModel1_2方案

## 🎯 迁移目标

将MainWindow_Qt_Simple中剩余的与作动器交互的代码迁移到ActuatorViewModel1_2中，实现完整的MVVM架构分离。

## 📊 当前状况分析

### MainWindow中的作动器相关代码统计
通过分析发现，MainWindow中还有**559处**与作动器相关的代码，主要分为以下几类：

#### 1. UI操作方法（需要迁移的核心逻辑）
- `OnCreateActuatorGroup()` - 创建作动器组
- `OnCreateActuator()` - 创建作动器
- `OnEditActuatorDevice()` - 编辑作动器设备
- `OnDeleteActuatorDevice()` - 删除作动器设备
- `createOrUpdateActuatorGroup()` - 创建或更新作动器组

#### 2. 数据管理方法（已部分迁移，需要完善）
- `saveActuatorDetailedParams()` - 保存作动器参数
- `getActuatorDetailedParams()` - 获取作动器参数
- `getAllActuatorGroups_MainDlg()` - 获取所有作动器组

#### 3. UI辅助方法（需要保留在MainWindow）
- `CreateActuatorDevice()` - 创建UI节点
- `GenerateActuatorDeviceDetailedInfo()` - 生成详细信息
- `AddActuatorGroupDebugInfo()` - 调试信息

#### 4. 验证和工具方法（可以迁移）
- `IsActuatorGroupNameExists()` - 检查组名是否存在
- `isActuatorSerialNumberExistsInGroup()` - 检查序列号重复
- `extractActuatorGroupIdFromItem()` - 提取组ID

## 🏗️ 迁移架构设计

### 方案1：完全迁移模式（推荐）

#### A. ActuatorViewModel1_2扩展
```cpp
class ActuatorViewModel1_2 {
public:
    // ==================== UI业务逻辑接口 ====================
    
    // 作动器组管理
    bool createActuatorGroup(const QString& groupName);
    bool deleteActuatorGroup(int groupId);
    bool isActuatorGroupNameExists(const QString& groupName) const;
    
    // 作动器设备管理
    bool createActuatorDevice(int groupId, const UI::ActuatorParams& params);
    bool editActuatorDevice(const QString& serialNumber, const UI::ActuatorParams& newParams);
    bool deleteActuatorDevice(const QString& serialNumber);
    
    // 验证方法
    bool isSerialNumberUniqueInGroup(const QString& serialNumber, int groupId) const;
    bool validateActuatorParams(const UI::ActuatorParams& params) const;
    
    // 查询方法
    int extractGroupIdFromName(const QString& groupName) const;
    QString generateDetailedInfo(const QString& serialNumber) const;
    QStringList getGroupNames() const;
    
    // ==================== 信号 ====================
signals:
    void actuatorGroupCreated(const QString& groupName, int groupId);
    void actuatorDeviceCreated(const QString& serialNumber, int groupId);
    void actuatorDeviceEdited(const QString& serialNumber);
    void actuatorDeviceDeleted(const QString& serialNumber);
    void validationError(const QString& error);
};
```

#### B. MainWindow简化
```cpp
class CMyMainWindow {
private:
    // 只保留UI相关的方法
    void OnCreateActuatorGroup();           // UI交互 → 调用ViewModel
    void OnCreateActuator(QTreeWidgetItem*); // UI交互 → 调用ViewModel
    void OnEditActuatorDevice(QTreeWidgetItem*); // UI交互 → 调用ViewModel
    void OnDeleteActuatorDevice(QTreeWidgetItem*); // UI交互 → 调用ViewModel
    
    // UI节点操作（保留）
    void CreateActuatorDevice(QTreeWidgetItem*, const QString&, ...); // 纯UI操作
    void UpdateActuatorDeviceDisplay(const QString& serialNumber);     // 界面更新
    
    // 信息显示（保留）
    QString GenerateActuatorDeviceDetailedInfo(const QString&);       // UI显示
    void AddActuatorGroupDebugInfo(QString&, const QString&);         // 调试显示
};
```

### 方案2：混合模式

#### A. 核心业务逻辑迁移到ViewModel
```cpp
// 迁移到ActuatorViewModel1_2
- 数据验证逻辑
- 业务规则检查
- 数据持久化
- 错误处理

// 保留在MainWindow
- UI交互处理
- 对话框显示
- 树节点操作
- 界面更新
```

## 🔧 具体迁移计划

### 第一阶段：核心业务逻辑迁移

#### 1. 在ActuatorViewModel1_2中添加业务方法
```cpp
// 在ActuatorViewModel1_2.h中添加
class ActuatorViewModel1_2 {
public:
    // ==================== 业务逻辑接口 ====================
    
    /**
     * @brief 创建作动器组
     * @param groupName 组名称
     * @return 成功返回组ID，失败返回-1
     */
    int createActuatorGroupBusiness(const QString& groupName);
    
    /**
     * @brief 创建作动器设备
     * @param groupId 组ID
     * @param params 作动器参数
     * @return 成功返回true
     */
    bool createActuatorDeviceBusiness(int groupId, const UI::ActuatorParams& params);
    
    /**
     * @brief 编辑作动器设备
     * @param serialNumber 序列号
     * @param newParams 新参数
     * @return 成功返回true
     */
    bool editActuatorDeviceBusiness(const QString& serialNumber, const UI::ActuatorParams& newParams);
    
    /**
     * @brief 删除作动器设备
     * @param serialNumber 序列号
     * @return 成功返回true
     */
    bool deleteActuatorDeviceBusiness(const QString& serialNumber);
    
    /**
     * @brief 检查组名是否存在
     * @param groupName 组名称
     * @return 存在返回true
     */
    bool isActuatorGroupNameExistsBusiness(const QString& groupName) const;
    
    /**
     * @brief 检查序列号在组内是否唯一
     * @param serialNumber 序列号
     * @param groupId 组ID
     * @param excludeId 排除的作动器ID（编辑时使用）
     * @return 唯一返回true
     */
    bool isSerialNumberUniqueInGroupBusiness(const QString& serialNumber, int groupId, int excludeId = -1) const;
    
    /**
     * @brief 从组名提取组ID
     * @param groupName 组名称
     * @return 组ID，失败返回-1
     */
    int extractGroupIdFromNameBusiness(const QString& groupName) const;
    
    /**
     * @brief 生成作动器详细信息
     * @param serialNumber 序列号
     * @return 详细信息字符串
     */
    QString generateActuatorDetailedInfoBusiness(const QString& serialNumber) const;
    
    /**
     * @brief 获取所有组名列表
     * @return 组名列表
     */
    QStringList getActuatorGroupNamesBusiness() const;
    
    /**
     * @brief 验证作动器参数
     * @param params 作动器参数
     * @return 验证通过返回true
     */
    bool validateActuatorParamsBusiness(const UI::ActuatorParams& params) const;

signals:
    // 业务事件信号
    void actuatorGroupCreatedBusiness(const QString& groupName, int groupId);
    void actuatorDeviceCreatedBusiness(const QString& serialNumber, int groupId);
    void actuatorDeviceEditedBusiness(const QString& serialNumber);
    void actuatorDeviceDeletedBusiness(const QString& serialNumber);
    void businessValidationError(const QString& error);
};
```

#### 2. 修改MainWindow中的方法调用
```cpp
// 修改前
void CMyMainWindow::OnCreateActuatorGroup() {
    // 大量的业务逻辑代码...
    if (IsActuatorGroupNameExists(customName)) {
        // 错误处理...
    }
    CreateActuatorGroup(customName);
}

// 修改后
void CMyMainWindow::OnCreateActuatorGroup() {
    // UI交互部分
    QStringList items;
    items << tr("50kN_作动器组") << tr("100kN_作动器组") << tr("自定义...");
    
    QString selectedItem = QInputDialog::getItem(this, tr("新建作动器组"), 
                                               tr("请选择作动器组类型:"), items, 0, false, &ok);
    
    if (ok && !selectedItem.isEmpty()) {
        QString groupName = selectedItem;
        if (selectedItem == tr("自定义...")) {
            groupName = QInputDialog::getText(this, tr("自定义作动器组"), 
                                            tr("请输入自定义作动器组名称:"));
        }
        
        // 调用ViewModel业务逻辑
        int groupId = actuatorViewModel1_2_->createActuatorGroupBusiness(groupName);
        if (groupId > 0) {
            // 创建UI节点
            CreateActuatorGroupUI(groupName, groupId);
            AddLogEntry("INFO", QString("作动器组创建成功: %1").arg(groupName));
        } else {
            QMessageBox::warning(this, tr("创建失败"), 
                                actuatorViewModel1_2_->getLastError());
        }
    }
}
```

### 第二阶段：UI方法重构

#### 1. 分离UI操作和业务逻辑
```cpp
// 新增纯UI方法
void CMyMainWindow::CreateActuatorGroupUI(const QString& groupName, int groupId) {
    // 纯UI操作：创建树节点
    QTreeWidgetItem* taskRoot = GetTaskRootItem();
    QTreeWidgetItem* actuatorRoot = taskRoot->child(0);
    
    QTreeWidgetItem* groupItem = new QTreeWidgetItem(actuatorRoot);
    groupItem->setText(0, groupName);
    groupItem->setData(0, Qt::UserRole, "作动器组");
    groupItem->setData(1, Qt::UserRole, groupId); // 存储组ID
    groupItem->setExpanded(true);
    
    UpdateAllTreeWidgetTooltips();
}

void CMyMainWindow::CreateActuatorDeviceUI(const QString& serialNumber, const QString& groupName) {
    // 纯UI操作：创建设备节点
    QTreeWidgetItem* groupItem = FindActuatorGroupItem(groupName);
    if (groupItem) {
        QTreeWidgetItem* deviceItem = new QTreeWidgetItem(groupItem);
        deviceItem->setText(0, serialNumber);
        deviceItem->setData(0, Qt::UserRole, "作动器设备");
        
        // 设置详细信息
        QString detailInfo = actuatorViewModel1_2_->generateActuatorDetailedInfoBusiness(serialNumber);
        deviceItem->setToolTip(0, detailInfo);
        
        groupItem->setExpanded(true);
        UpdateAllTreeWidgetTooltips();
    }
}
```

### 第三阶段：信号连接

#### 1. 连接ViewModel信号到MainWindow
```cpp
void CMyMainWindow::connectActuatorViewModelSignals() {
    // 连接业务事件信号
    connect(actuatorViewModel1_2_.get(), &ActuatorViewModel1_2::actuatorGroupCreatedBusiness,
            this, &CMyMainWindow::onActuatorGroupCreated);
    
    connect(actuatorViewModel1_2_.get(), &ActuatorViewModel1_2::actuatorDeviceCreatedBusiness,
            this, &CMyMainWindow::onActuatorDeviceCreated);
    
    connect(actuatorViewModel1_2_.get(), &ActuatorViewModel1_2::actuatorDeviceEditedBusiness,
            this, &CMyMainWindow::onActuatorDeviceEdited);
    
    connect(actuatorViewModel1_2_.get(), &ActuatorViewModel1_2::actuatorDeviceDeletedBusiness,
            this, &CMyMainWindow::onActuatorDeviceDeleted);
    
    connect(actuatorViewModel1_2_.get(), &ActuatorViewModel1_2::businessValidationError,
            this, &CMyMainWindow::onActuatorValidationError);
}

// 信号处理方法
void CMyMainWindow::onActuatorGroupCreated(const QString& groupName, int groupId) {
    CreateActuatorGroupUI(groupName, groupId);
    AddLogEntry("INFO", QString("作动器组已创建: %1 (ID: %2)").arg(groupName).arg(groupId));
}

void CMyMainWindow::onActuatorDeviceCreated(const QString& serialNumber, int groupId) {
    QString groupName = actuatorViewModel1_2_->getActuatorGroupName(groupId);
    CreateActuatorDeviceUI(serialNumber, groupName);
    AddLogEntry("INFO", QString("作动器设备已创建: %1").arg(serialNumber));
}
```

## 📋 迁移优先级

### 高优先级（核心业务逻辑）
1. `createOrUpdateActuatorGroup()` - 作动器组管理核心逻辑
2. `OnCreateActuator()` - 作动器创建核心逻辑
3. 数据验证方法：`isActuatorSerialNumberExistsInGroup()`, `IsActuatorGroupNameExists()`

### 中优先级（辅助业务逻辑）
4. `OnEditActuatorDevice()` - 编辑功能
5. `OnDeleteActuatorDevice()` - 删除功能
6. `extractActuatorGroupIdFromItem()` - 工具方法

### 低优先级（UI辅助功能）
7. `GenerateActuatorDeviceDetailedInfo()` - 信息生成（可以调用ViewModel）
8. `AddActuatorGroupDebugInfo()` - 调试信息（可以调用ViewModel）
9. UI创建方法 - 保留在MainWindow但简化

## ✅ 迁移收益

### 1. 架构清晰
- **MainWindow**：专注于UI交互和显示
- **ActuatorViewModel1_2**：专注于业务逻辑和数据管理

### 2. 可测试性
- 业务逻辑可以独立测试
- UI逻辑与业务逻辑分离

### 3. 可维护性
- 业务规则集中管理
- 减少MainWindow的复杂度

### 4. 可扩展性
- 新增业务功能只需扩展ViewModel
- UI变更不影响业务逻辑

## 🚀 实施建议

### 第一步：创建业务接口
在ActuatorViewModel1_2中添加所有业务逻辑方法的声明

### 第二步：实现业务逻辑
将MainWindow中的业务逻辑代码迁移到ViewModel中

### 第三步：重构MainWindow
简化MainWindow中的方法，只保留UI交互部分

### 第四步：建立信号连接
通过信号槽机制连接ViewModel和MainWindow

### 第五步：测试验证
确保所有功能正常工作，业务逻辑正确

这个迁移方案将实现完整的MVVM架构，使代码更加清晰、可维护和可测试。
