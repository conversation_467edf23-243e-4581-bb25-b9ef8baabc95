#pragma once

/**
 * @file MockHardware.h
 * @brief 模拟硬件设备定义 - 已弃用
 * @details 用于测试的模拟硬件设备实现 - 数据模拟功能已弃用，整个文件不再使用
 * <AUTHOR> Assistant
 * @date 2025-08-05
 * @version 1.0.0
 * @deprecated 数据模拟功能已弃用，此文件不再使用
 */

// ============================================================================
// 注意：此文件中的所有代码已被弃用
// 数据模拟相关功能不再使用，整个MockHardware模块已停用
// ============================================================================

#include "HardwareAbstraction.h"
#include <random>

namespace Hardware {

/**
 * @brief 模拟伺服控制器
 * @details 用于测试的模拟伺服控制器实现
 */
class MockServoController : public IServoController {
private:
    int nodeId_;
    StringType deviceName_;
    CommunicationStatus connectionStatus_;
    DeviceStatus deviceStatus_;
    
    // 通道配置
    int channelCount_;
    double sampleRate_;
    std::vector<ControlMode> channelModes_;
    std::vector<bool> channelEnabled_;
    std::vector<double> commandValues_;
    std::vector<double> feedbackValues_;
    std::vector<double> positionValues_;
    
    // PID参数
    struct PIDParams {
        double kp, ki, kd, ks;
        PIDParams() : kp(1.0), ki(0.0), kd(0.0), ks(0.0) {}
    };
    std::vector<PIDParams> pidParams_;
    
    // 安全限制
    struct SafetyLimits {
        double lowerLimit, upperLimit;
        SafetyLimits() : lowerLimit(-1000.0), upperLimit(1000.0) {}
    };
    std::vector<SafetyLimits> safetyLimits_;
    
    // 回调函数
    HardwareEventCallback dataCallback_;
    StatusChangeCallback statusCallback_;
    ErrorCallback errorCallback_;
    
    // 模拟数据生成
    mutable std::mt19937 randomGenerator_;
    mutable std::uniform_real_distribution<double> noiseDistribution_;
    
    // 数据采集线程
    std::thread acquisitionThread_;
    std::atomic<bool> acquisitionRunning_;
    mutable std::mutex dataMutex_;
    
public:
    MockServoController(int nodeId, const StringType& deviceName, int channelCount = 4);
    virtual ~MockServoController();
    
    // IHardwareDevice接口实现
    bool Connect() override;
    void Disconnect() override;
    CommunicationStatus GetConnectionStatus() const override;
    DeviceStatus GetDeviceStatus() const override;
    StringType GetDeviceInfo() const override;
    
    bool SendCommand(int channelId, double commandValue) override;
    bool ReadFeedback(int channelId, double& feedbackValue) override;
    bool SetControlMode(int channelId, ControlMode mode) override;
    bool SetChannelEnabled(int channelId, bool enabled) override;
    bool SetPIDParameters(int channelId, double kp, double ki, double kd, double ks) override;
    bool SetSafetyLimits(int channelId, double lowerLimit, double upperLimit) override;
    bool EmergencyStop() override;
    bool Reset() override;
    
    void SetDataCallback(HardwareEventCallback callback) override;
    void SetStatusCallback(StatusChangeCallback callback) override;
    void SetErrorCallback(ErrorCallback callback) override;
    
    // IServoController接口实现
    int GetChannelCount() const override;
    double GetSampleRate() const override;
    bool SetSampleRate(double sampleRate) override;
    bool StartAcquisition() override;
    bool StopAcquisition() override;
    bool CalibrateChannel(int channelId, const StringType& calibrationType, double calibrationValue) override;
    
private:
    /**
     * @brief 验证通道ID有效性
     * @param channelId 通道ID
     * @return 有效返回true
     */
    bool IsValidChannelId(int channelId) const;
    
    /**
     * @brief 模拟数据采集线程函数
     */
    void AcquisitionThreadFunction();
    
    /**
     * @brief 生成模拟反馈数据
     * @param channelId 通道ID
     * @return 模拟反馈值
     */
    double GenerateMockFeedback(int channelId) const;
    
    /**
     * @brief 生成模拟位置数据
     * @param channelId 通道ID
     * @return 模拟位置值
     */
    double GenerateMockPosition(int channelId) const;
    
    /**
     * @brief 发送数据回调
     * @param channelId 通道ID
     */
    void SendDataCallback(int channelId) const;
    
    /**
     * @brief 改变设备状态
     * @param newStatus 新状态
     */
    void ChangeDeviceStatus(DeviceStatus newStatus);
};

/**
 * @brief 模拟数据采集器
 * @details 用于测试的模拟数据采集器实现
 */
class MockDataAcquisition : public IDataAcquisition {
private:
    int nodeId_;
    StringType deviceName_;
    CommunicationStatus connectionStatus_;
    DeviceStatus deviceStatus_;
    
    // 输入通道配置
    int inputChannelCount_;
    std::vector<double> channelRanges_;
    std::vector<StringType> channelUnits_;
    std::vector<double> channelValues_;
    
    // 触发配置
    int triggerChannelId_;
    double triggerLevel_;
    bool triggerSlope_;
    
    // 回调函数
    HardwareEventCallback dataCallback_;
    StatusChangeCallback statusCallback_;
    ErrorCallback errorCallback_;
    
    // 模拟数据生成
    mutable std::mt19937 randomGenerator_;
    mutable std::uniform_real_distribution<double> dataDistribution_;
    
    mutable std::mutex dataMutex_;
    
public:
    MockDataAcquisition(int nodeId, const StringType& deviceName, int inputChannelCount = 8);
    virtual ~MockDataAcquisition();
    
    // IHardwareDevice接口实现
    bool Connect() override;
    void Disconnect() override;
    CommunicationStatus GetConnectionStatus() const override;
    DeviceStatus GetDeviceStatus() const override;
    StringType GetDeviceInfo() const override;
    
    bool SendCommand(int channelId, double commandValue) override;
    bool ReadFeedback(int channelId, double& feedbackValue) override;
    bool SetControlMode(int channelId, ControlMode mode) override;
    bool SetChannelEnabled(int channelId, bool enabled) override;
    bool SetPIDParameters(int channelId, double kp, double ki, double kd, double ks) override;
    bool SetSafetyLimits(int channelId, double lowerLimit, double upperLimit) override;
    bool EmergencyStop() override;
    bool Reset() override;
    
    void SetDataCallback(HardwareEventCallback callback) override;
    void SetStatusCallback(StatusChangeCallback callback) override;
    void SetErrorCallback(ErrorCallback callback) override;
    
    // IDataAcquisition接口实现
    int GetInputChannelCount() const override;
    bool ConfigureInputChannel(int channelId, double range, const StringType& unit) override;
    bool ReadMultipleChannels(const std::vector<int>& channelIds, std::vector<double>& values) override;
    bool SetTrigger(int channelId, double triggerLevel, bool triggerSlope) override;
    
private:
    /**
     * @brief 验证输入通道ID有效性
     * @param channelId 通道ID
     * @return 有效返回true
     */
    bool IsValidInputChannelId(int channelId) const;
    
    /**
     * @brief 生成模拟输入数据
     * @param channelId 通道ID
     * @return 模拟输入值
     */
    double GenerateMockInputData(int channelId) const;
    
    /**
     * @brief 改变设备状态
     * @param newStatus 新状态
     */
    void ChangeDeviceStatus(DeviceStatus newStatus);
};

/**
 * @brief 模拟硬件工厂
 * @details 用于创建模拟硬件设备的工厂类
 */
class MockHardwareFactory {
public:
    /**
     * @brief 创建模拟伺服控制器
     * @param nodeId 节点ID
     * @param deviceName 设备名称
     * @param channelCount 通道数量
     * @return 设备对象指针
     */
    static SharedPtr<IServoController> CreateMockServoController(
        int nodeId, const StringType& deviceName, int channelCount = 4);
    
    /**
     * @brief 创建模拟数据采集器
     * @param nodeId 节点ID
     * @param deviceName 设备名称
     * @param inputChannelCount 输入通道数量
     * @return 设备对象指针
     */
    static SharedPtr<IDataAcquisition> CreateMockDataAcquisition(
        int nodeId, const StringType& deviceName, int inputChannelCount = 8);
    
    /**
     * @brief 创建模拟硬件节点配置
     * @param nodeId 节点ID
     * @param nodeName 节点名称
     * @param nodeType 节点类型
     * @return 硬件节点配置
     */
    static DataModels::HardwareNode CreateMockNodeConfig(
        int nodeId, const StringType& nodeName, const StringType& nodeType);
};

} // namespace Hardware
