/**
 * @file TreeInteractionHandler.h
 * @brief 树形控件交互处理类
 * @details 实现树形控件节点的多级详细信息显示功能
 * <AUTHOR> Assistant
 * @date 2025-01-23
 * @version 1.0.0
 */

#ifndef TREEINTERACTIONHANDLER_H
#define TREEINTERACTIONHANDLER_H

#include <QtWidgets/QTreeWidget>
#include <QtWidgets/QTreeWidgetItem>
#include <QtWidgets/QTextEdit>
#include <QtWidgets/QToolTip>
#include <QtWidgets/QMenu>
#include <QtWidgets/QLabel>
#include <QtWidgets/QFrame>
#include <QtCore/QTimer>
#include <QtCore/QPoint>
#include <QtCore/QString>
#include <QtCore/QEvent>
#include <QtGui/QMouseEvent>
#include <QtGui/QScreen>
#include <QtWidgets/QApplication>

// 🆕 新增：包含BasicInfoWidget和DetailInfoPanel头文件
#include "BasicInfoWidget.h"
#include "DetailInfoPanel.h"

// 前向声明
class CMyMainWindow;

/**
 * @brief 树形控件交互处理类
 * @details 管理树形控件节点的多级详细信息显示功能
 */
class TreeInteractionHandler : public QObject {
    Q_OBJECT

public:
    explicit TreeInteractionHandler(QTreeWidget* treeWidget, DetailInfoPanel* detailPanel, CMyMainWindow* mainWindow, QObject* parent = nullptr);
    virtual ~TreeInteractionHandler();

    /**
     * @brief 初始化交互处理
     */
    void initializeInteraction();

    /**
     * @brief 设置详细信息面板
     * @param detailPanel 详细信息面板控件
     */
    void setDetailPanel(DetailInfoPanel* detailPanel);

    /**
     * @brief 更新节点详细信息
     * @param item 树形控件节点
     */
    void updateNodeDetailInfo(QTreeWidgetItem* item);

private slots:
    /**
     * @brief 鼠标悬停事件处理 - Layer 1 (基础信息)
     */
    void onItemEntered(QTreeWidgetItem* item, int column);

    /**
     * @brief 单击事件处理 - Layer 2 (详细信息面板)
     */
    void onItemClicked(QTreeWidgetItem* item, int column);

    /**
     * @brief 右键菜单事件处理 - Layer 3 (专门对话框)
     */
    void onContextMenuRequested(const QPoint& pos);

    /**
     * @brief 双击事件处理 - 保留之前的操作，不做修改
     */
    void onItemDoubleClicked(QTreeWidgetItem* item, int column);

    /**
     * @brief 延迟隐藏工具提示
     */
    void hideTooltipDelayed();

    /**
     * @brief 编辑通道配置
     */
    void editChannelConfig();

    /**
     * @brief 删除关联信息
     */
    void clearAssociation();

    /**
     * @brief 删除所有关联信息
     */
    void clearAllAssociation();

    /**
     * @brief 删除通道
     */
    void deleteChannel();

    /**
     * @brief 新建控制通道
     */
    void createChannel();

private:
    QTreeWidget* m_treeWidget;          ///< 树形控件
    DetailInfoPanel* m_detailPanel;     ///< 详细信息面板
    CMyMainWindow* m_mainWindow;        ///< 主窗口指针
    QTimer* m_tooltipTimer;             ///< 工具提示计时器
    QTreeWidgetItem* m_currentItem;     ///< 当前选中的节点

    /**
     * @brief 生成Layer 1基础信息 (工具提示)
     * @param item 树形控件节点
     * @return 基础信息字符串
     */
    QString generateLayer1Info(QTreeWidgetItem* item);

    /**
     * @brief 生成Layer 2详细信息 (信息面板)
     * @param item 树形控件节点
     * @return 详细信息HTML字符串
     */
    QString generateLayer2Info(QTreeWidgetItem* item);

    /**
     * @brief 生成控制通道详细信息
     * @param item 控制通道节点
     * @return 详细信息HTML字符串
     */
    QString generateControlChannelInfo(QTreeWidgetItem* item);

    /**
     * @brief 生成传感器节点详细信息
     * @param item 传感器节点
     * @return 详细信息HTML字符串
     */
    QString generateSensorNodeInfo(QTreeWidgetItem* item);

    /**
     * @brief 生成作动器节点详细信息
     * @param item 作动器节点
     * @return 详细信息HTML字符串
     */
    QString generateActuatorNodeInfo(QTreeWidgetItem* item);

    /**
     * @brief 生成硬件节点详细信息
     * @param item 硬件节点
     * @return 详细信息HTML字符串
     */
    QString generateHardwareNodeInfo(QTreeWidgetItem* item);

    /**
     * @brief 获取节点类型
     * @param item 树形控件节点
     * @return 节点类型字符串
     */
    QString getNodeType(QTreeWidgetItem* item);

    /**
     * @brief 获取节点名称
     * @param item 树形控件节点
     * @return 节点名称
     */
    QString getNodeName(QTreeWidgetItem* item);

    /**
     * @brief 获取关联信息
     * @param item 树形控件节点
     * @return 关联信息字符串
     */
    QString getAssociationInfo(QTreeWidgetItem* item);

    /**
     * @brief 显示右键菜单选项
     * @param item 树形控件节点
     * @param pos 鼠标位置
     */
    void showContextMenu(QTreeWidgetItem* item, const QPoint& pos);

    /**
     * @brief 智能检测：判断是否为控制通道的子节点
     * @param item 树形控件节点
     * @return 是否为控制通道子节点
     */
    bool isControlChannelSubNode(QTreeWidgetItem* item);

    /**
     * @brief 智能菜单生成：为控制通道子节点生成完整功能菜单
     * @param item 控制通道子节点
     * @param contextMenu 右键菜单对象
     */
    void generateFullControlChannelMenu(QTreeWidgetItem* item, QMenu& contextMenu);

    // 已删除：不再需要统一详细信息对话框功能

    /**
     * @brief 生成子节点详细信息HTML
     * @param item 父节点
     * @param childNodeTitle 子节点区域标题（如"子节点详细信息"、"通道详细信息"等）
     * @return 子节点信息HTML字符串
     */
    QString generateChildNodesInfo(QTreeWidgetItem* item, const QString& childNodeTitle = "子节点详细信息");

    /**
     * @brief 生成子节点表格详细信息HTML
     * @param item 父节点
     * @param childNodeTitle 子节点区域标题
     * @return 子节点表格信息HTML字符串
     */
    QString generateChildNodesTableInfo(QTreeWidgetItem* item, const QString& childNodeTitle = "子节点详细信息");

    /**
     * @brief 格式化HTML信息
     * @param title 标题
     * @param content 内容
     * @return 格式化的HTML字符串
     */
    QString formatHtmlInfo(const QString& title, const QString& content);

    /**
     * @brief 生成错误信息HTML页面
     * @param nodeName 节点名称
     * @param nodeType 节点类型
     * @param errorMessage 错误信息
     * @return 错误信息HTML字符串
     */
    QString generateErrorHtml(const QString& nodeName, const QString& nodeType, const QString& errorMessage);

private:
    // 持久提示相关
    QFrame* m_persistentTooltip;          ///< 持久提示窗口
    QLabel* m_tooltipLabel;               ///< 提示文本标签
    QTreeWidgetItem* m_currentHoverItem;  ///< 当前悬停的节点
    
    /**
     * @brief 创建持久提示窗口
     */
    void createPersistentTooltip();
    
    /**
     * @brief 显示持久提示信息
     * @param text 提示文本
     * @param position 显示位置
     */
    void showPersistentTooltip(const QString& text, const QPoint& position);
    
    /**
     * @brief 隐藏持久提示信息
     */
    void hidePersistentTooltip();
    
    /**
     * @brief 更新持久提示信息
     * @param text 新的提示文本
     */
    void updatePersistentTooltip(const QString& text);

protected:
    /**
     * @brief 事件过滤器
     * @param obj 事件对象
     * @param event 事件
     * @return 是否处理了该事件
     */
    bool eventFilter(QObject* obj, QEvent* event) override;

private slots:
    /**
     * @brief 鼠标离开树形控件事件
     */
    void onMouseLeave();
};

#endif // TREEINTERACTIONHANDLER_H 