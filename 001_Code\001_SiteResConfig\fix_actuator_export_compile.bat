@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo.
echo ========================================
echo  🔧 修复作动器导出编译错误
echo ========================================
echo.

REM 设置Qt环境
set QTDIR=C:\Qt\5.15.2\msvc2019_64
set PATH=%QTDIR%\bin;%PATH%

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo 1. 清理所有构建文件...
if exist "Makefile*" del Makefile* >nul 2>&1
if exist "debug" rmdir /s /q debug >nul 2>&1
if exist "release" rmdir /s /q release >nul 2>&1
if exist "*.o" del *.o >nul 2>&1
if exist "ui_*.h" del ui_*.h >nul 2>&1
if exist "moc_*.cpp" del moc_*.cpp >nul 2>&1
if exist "moc_*.h" del moc_*.h >nul 2>&1

echo 清理构建目录...
cd /d "%~dp0"
if exist "build-SiteResConfig_Simple-Desktop_Qt_5_14_2_MinGW_32_bit-Debug" (
    rmdir /s /q "build-SiteResConfig_Simple-Desktop_Qt_5_14_2_MinGW_32_bit-Debug" >nul 2>&1
)

cd /d "%~dp0\SiteResConfig"

echo.
echo 2. 重新生成UI头文件...
uic ui\MainWindow.ui -o ui_MainWindow.h
if errorlevel 1 (
    echo ❌ UI头文件生成失败
    goto :error
)

echo 检查生成的UI头文件...
findstr /C:"actionExportActuatorDetailsToExcel" "ui_MainWindow.h" >nul
if errorlevel 1 (
    echo ❌ UI头文件中缺少actionExportActuatorDetailsToExcel
    goto :error
) else (
    echo ✅ UI头文件生成成功，包含actionExportActuatorDetailsToExcel
)

echo.
echo 3. 验证代码完整性...

echo 检查XLSDataExporter.h中的exportActuatorDetails声明...
findstr /C:"exportActuatorDetails" "include\XLSDataExporter.h" >nul
if errorlevel 1 (
    echo ❌ XLSDataExporter.h中缺少exportActuatorDetails声明
    goto :error
) else (
    echo ✅ XLSDataExporter.h中有exportActuatorDetails声明
)

echo 检查XLSDataExporter.cpp中的exportActuatorDetails实现...
findstr /C:"XLSDataExporter::exportActuatorDetails" "src\XLSDataExporter.cpp" >nul
if errorlevel 1 (
    echo ❌ XLSDataExporter.cpp中缺少exportActuatorDetails实现
    goto :error
) else (
    echo ✅ XLSDataExporter.cpp中有exportActuatorDetails实现
)

echo 检查MainWindow_Qt_Simple.h中的OnExportActuatorDetailsToExcel声明...
findstr /C:"OnExportActuatorDetailsToExcel" "include\MainWindow_Qt_Simple.h" >nul
if errorlevel 1 (
    echo ❌ MainWindow_Qt_Simple.h中缺少OnExportActuatorDetailsToExcel声明
    goto :error
) else (
    echo ✅ MainWindow_Qt_Simple.h中有OnExportActuatorDetailsToExcel声明
)

echo 检查MainWindow_Qt_Simple.cpp中的OnExportActuatorDetailsToExcel实现...
findstr /C:"CMyMainWindow::OnExportActuatorDetailsToExcel" "src\MainWindow_Qt_Simple.cpp" >nul
if errorlevel 1 (
    echo ❌ MainWindow_Qt_Simple.cpp中缺少OnExportActuatorDetailsToExcel实现
    goto :error
) else (
    echo ✅ MainWindow_Qt_Simple.cpp中有OnExportActuatorDetailsToExcel实现
)

echo.
echo 4. 生成qmake项目文件...
echo QT += core widgets > SiteResConfig_Simple.pro
echo CONFIG += console >> SiteResConfig_Simple.pro
echo TARGET = SiteResConfig >> SiteResConfig_Simple.pro
echo TEMPLATE = app >> SiteResConfig_Simple.pro
echo. >> SiteResConfig_Simple.pro
echo INCLUDEPATH += include >> SiteResConfig_Simple.pro
echo INCLUDEPATH += ../../../vcpkg/installed/x64-windows/include >> SiteResConfig_Simple.pro
echo. >> SiteResConfig_Simple.pro
echo LIBS += -L../../../vcpkg/installed/x64-windows/lib >> SiteResConfig_Simple.pro
echo LIBS += -lQXlsx >> SiteResConfig_Simple.pro
echo. >> SiteResConfig_Simple.pro
echo SOURCES += src/main_qt.cpp >> SiteResConfig_Simple.pro
echo SOURCES += src/MainWindow_Qt_Simple.cpp >> SiteResConfig_Simple.pro
echo SOURCES += src/XLSDataExporter.cpp >> SiteResConfig_Simple.pro
echo SOURCES += src/ActuatorDialog.cpp >> SiteResConfig_Simple.pro
echo SOURCES += src/SensorDataManager.cpp >> SiteResConfig_Simple.pro
echo SOURCES += src/DataExportManager.cpp >> SiteResConfig_Simple.pro
echo SOURCES += src/CSVDataExporter.cpp >> SiteResConfig_Simple.pro
echo SOURCES += src/JSONDataExporter.cpp >> SiteResConfig_Simple.pro
echo SOURCES += src/DataExporterFactory.cpp >> SiteResConfig_Simple.pro
echo SOURCES += src/SensorDialog.cpp >> SiteResConfig_Simple.pro
echo SOURCES += src/CreateHardwareNodeDialog.cpp >> SiteResConfig_Simple.pro
echo SOURCES += src/HardwareConfigDialog.cpp >> SiteResConfig_Simple.pro
echo SOURCES += src/NodeConfigDialog.cpp >> SiteResConfig_Simple.pro
echo SOURCES += src/ControlModeDialog.cpp >> SiteResConfig_Simple.pro
echo SOURCES += src/PIDParametersDialog.cpp >> SiteResConfig_Simple.pro
echo SOURCES += src/CustomTreeWidgets.cpp >> SiteResConfig_Simple.pro
echo SOURCES += src/ConfigManager_Simple.cpp >> SiteResConfig_Simple.pro
echo SOURCES += src/DataModels_Simple.cpp >> SiteResConfig_Simple.pro
echo SOURCES += src/Utils_Fixed.cpp >> SiteResConfig_Simple.pro
echo. >> SiteResConfig_Simple.pro
echo HEADERS += include/MainWindow_Qt_Simple.h >> SiteResConfig_Simple.pro
echo HEADERS += include/XLSDataExporter.h >> SiteResConfig_Simple.pro
echo HEADERS += include/ActuatorDialog.h >> SiteResConfig_Simple.pro
echo HEADERS += include/IDataExporter.h >> SiteResConfig_Simple.pro
echo HEADERS += include/SensorDataManager.h >> SiteResConfig_Simple.pro
echo HEADERS += include/DataExportManager.h >> SiteResConfig_Simple.pro
echo HEADERS += include/CSVDataExporter.h >> SiteResConfig_Simple.pro
echo HEADERS += include/JSONDataExporter.h >> SiteResConfig_Simple.pro
echo HEADERS += include/DataExporterFactory.h >> SiteResConfig_Simple.pro
echo HEADERS += include/SensorDialog.h >> SiteResConfig_Simple.pro
echo HEADERS += include/CreateHardwareNodeDialog.h >> SiteResConfig_Simple.pro
echo HEADERS += include/HardwareConfigDialog.h >> SiteResConfig_Simple.pro
echo HEADERS += include/NodeConfigDialog.h >> SiteResConfig_Simple.pro
echo HEADERS += include/ControlModeDialog.h >> SiteResConfig_Simple.pro
echo HEADERS += include/PIDParametersDialog.h >> SiteResConfig_Simple.pro
echo HEADERS += include/CustomTreeWidgets.h >> SiteResConfig_Simple.pro
echo HEADERS += include/Common_Fixed.h >> SiteResConfig_Simple.pro
echo HEADERS += include/ConfigManager_Fixed.h >> SiteResConfig_Simple.pro
echo HEADERS += include/DataModels_Fixed.h >> SiteResConfig_Simple.pro
echo. >> SiteResConfig_Simple.pro
echo FORMS += ui/MainWindow.ui >> SiteResConfig_Simple.pro
echo FORMS += ui/ActuatorDialog.ui >> SiteResConfig_Simple.pro
echo FORMS += ui/SensorDialog.ui >> SiteResConfig_Simple.pro
echo FORMS += ui/CreateHardwareNodeDialog.ui >> SiteResConfig_Simple.pro
echo FORMS += ui/HardwareConfigDialog.ui >> SiteResConfig_Simple.pro
echo FORMS += ui/NodeConfigDialog.ui >> SiteResConfig_Simple.pro
echo FORMS += ui/ControlModeDialog.ui >> SiteResConfig_Simple.pro
echo FORMS += ui/PIDParametersDialog.ui >> SiteResConfig_Simple.pro

echo ✅ 项目文件生成完成

echo.
echo 5. 运行qmake...
qmake SiteResConfig_Simple.pro
if errorlevel 1 (
    echo ❌ qmake 失败
    goto :error
)

echo ✅ qmake 成功

echo.
echo 6. 编译项目...
nmake clean >nul 2>&1
nmake
if errorlevel 1 (
    echo ❌ 编译失败
    echo.
    echo 显示编译错误详情:
    nmake 2>&1 | findstr /C:"error"
    goto :error
) else (
    echo ✅ 编译成功
)

echo.
echo ========================================
echo  ✅ 作动器导出编译错误修复完成！
echo ========================================
echo.
echo 📋 修复内容:
echo   1. ✅ 清理了所有旧的构建文件
echo   2. ✅ 重新生成了UI头文件
echo   3. ✅ 验证了所有代码完整性
echo   4. ✅ 重新编译了整个项目
echo.
echo 🎯 现在可以使用的功能:
echo   - 菜单: 数据导出 → 导出作动器详细信息到Excel
echo   - 快捷键: Ctrl+D → 选择"导出作动器详细信息到Excel"
echo.

echo 清理临时文件...
if exist "SiteResConfig_Simple.pro" del SiteResConfig_Simple.pro >nul 2>&1

pause
goto :end

:error
echo.
echo ❌ 修复失败！
echo.
echo 🔍 可能的问题:
echo   1. Qt环境配置问题
echo   2. vcpkg依赖库未安装
echo   3. 代码语法错误
echo   4. 文件权限问题
echo.
echo 💡 解决建议:
echo   1. 检查Qt安装路径: %QTDIR%
echo   2. 确认QXlsx库已通过vcpkg安装
echo   3. 检查编译错误信息
echo   4. 确保有文件写入权限
echo.

if exist "SiteResConfig_Simple.pro" del SiteResConfig_Simple.pro >nul 2>&1

pause
exit /b 1

:end
endlocal
