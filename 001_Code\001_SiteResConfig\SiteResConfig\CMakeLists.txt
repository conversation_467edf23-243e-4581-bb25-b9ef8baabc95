cmake_minimum_required(VERSION 3.16)

project(SiteResConfig VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 14)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find Qt5 components
find_package(Qt5 REQUIRED COMPONENTS Core Widgets)

# Set Qt5 properties
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)

# Source files
set(SOURCES
    src/main_simple.cpp
    src/MainWindow_Qt_Simple.cpp
    src/ActuatorDialog.cpp
    src/SensorDialog.cpp
    src/HardwareConfigDialog.cpp
    src/PIDParametersDialog.cpp
    src/ControlModeDialog.cpp
    src/NodeConfigDialog.cpp
    src/CreateHardwareNodeDialog.cpp
    src/ControlChannelEditDialog.cpp
    src/ConfigManager_Simple.cpp
    src/DataModels_Simple.cpp
    src/DataModels_Fixed.cpp
    src/Utils_Fixed.cpp
    src/HardwareAbstraction.cpp
    # 注释掉MockHardware编译 - 数据模拟已弃用
    # src/MockHardware.cpp
    src/TestProject.cpp
)

# Header files
set(HEADERS
    include/MainWindow_Qt_Simple.h
    include/ActuatorDialog.h
    include/SensorDialog.h
    include/HardwareConfigDialog.h
    include/PIDParametersDialog.h
    include/ControlModeDialog.h
    include/NodeConfigDialog.h
    include/CreateHardwareNodeDialog.h
    include/ControlChannelEditDialog.h
    include/ConfigManager_Fixed.h
    include/DataModels_Fixed.h
    include/Common_Fixed.h
    include/HardwareAbstraction.h
    # 注释掉MockHardware头文件 - 数据模拟已弃用
    # include/MockHardware.h
    include/TestProject.h
)

# UI files
set(UI_FILES
    ui/MainWindow.ui
    ui/ActuatorDialog.ui
    ui/SensorDialog.ui
    ui/HardwareConfigDialog.ui
    ui/PIDParametersDialog.ui
    ui/ControlModeDialog.ui
    ui/NodeConfigDialog.ui
    ui/CreateHardwareNodeDialog.ui
)

# Create executable
add_executable(SiteResConfig ${SOURCES} ${HEADERS} ${UI_FILES})

# Link Qt5 libraries
target_link_libraries(SiteResConfig Qt5::Core Qt5::Widgets)

# Set output directory
set_target_properties(SiteResConfig PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# Windows specific settings
if(WIN32)
    # Set Windows subsystem
    set_target_properties(SiteResConfig PROPERTIES
        WIN32_EXECUTABLE TRUE
    )
    
    # Add Windows resource file if exists
    if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/SiteResConfig.rc)
        target_sources(SiteResConfig PRIVATE SiteResConfig.rc)
    endif()
endif()

# Compiler specific settings
if(MSVC)
    # MSVC specific settings
    target_compile_definitions(SiteResConfig PRIVATE
        _CRT_SECURE_NO_WARNINGS
        NOMINMAX
    )
    
    # Set warning level
    target_compile_options(SiteResConfig PRIVATE /W3)
    
elseif(CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
    # GCC/MinGW specific settings
    target_compile_options(SiteResConfig PRIVATE
        -Wall
        -Wextra
        -pedantic
    )
    
    # Enable UTF-8 support
    target_compile_options(SiteResConfig PRIVATE
        -finput-charset=UTF-8
        -fexec-charset=UTF-8
    )
endif()

# Debug/Release specific settings
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_definitions(SiteResConfig PRIVATE DEBUG)
else()
    target_compile_definitions(SiteResConfig PRIVATE NDEBUG)
endif()

# Install rules
install(TARGETS SiteResConfig
    RUNTIME DESTINATION bin
)

# Install sample configs
install(DIRECTORY sample_configs/
    DESTINATION share/SiteResConfig/configs
    FILES_MATCHING PATTERN "*.json" PATTERN "*.xml" PATTERN "*.csv"
)

# CPack configuration for packaging
set(CPACK_PACKAGE_NAME "SiteResConfig")
set(CPACK_PACKAGE_VERSION "${PROJECT_VERSION}")
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "SiteResConfig - 灵动加载上位机管理软件")
set(CPACK_PACKAGE_VENDOR "SiteResConfig Team")

if(WIN32)
    set(CPACK_GENERATOR "NSIS")
    set(CPACK_NSIS_DISPLAY_NAME "SiteResConfig")
    set(CPACK_NSIS_PACKAGE_NAME "SiteResConfig")
    set(CPACK_NSIS_CONTACT "<EMAIL>")
endif()

include(CPack)

# Print configuration summary
message(STATUS "")
message(STATUS "SiteResConfig Configuration Summary:")
message(STATUS "  Version: ${PROJECT_VERSION}")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Qt5 version: ${Qt5_VERSION}")
message(STATUS "  Install prefix: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "")
