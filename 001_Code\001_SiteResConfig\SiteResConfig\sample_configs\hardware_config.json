{"project_info": {"name": "示例硬件配置", "version": "1.0.0", "description": "SiteResConfig示例配置文件", "created_date": "2025-08-05", "author": "系统管理员"}, "hardware_nodes": [{"node_id": 0, "name": "主控制器", "type": "ServoController", "ip_address": "*************", "port": 8080, "channel_count": 8, "max_sample_rate": 10000, "firmware_version": "v2.1.0"}, {"node_id": 1, "name": "数据采集器", "type": "DataAcquisition", "ip_address": "*************", "port": 8080, "channel_count": 16, "max_sample_rate": 50000, "firmware_version": "v1.8.0"}, {"node_id": 2, "name": "辅助控制器", "type": "AuxController", "ip_address": "*************", "port": 8080, "channel_count": 4, "max_sample_rate": 5000, "firmware_version": "v1.5.0"}], "actuators": [{"actuator_id": "ACT001", "name": "主液压缸", "type": "Hydraulic", "max_force": 200000, "stroke": 300, "max_velocity": 500, "bound_node_id": 0, "bound_channel": 0}, {"actuator_id": "ACT002", "name": "辅助液压缸", "type": "Hydraulic", "max_force": 100000, "stroke": 200, "max_velocity": 300, "bound_node_id": 0, "bound_channel": 1}, {"actuator_id": "ACT003", "name": "精密电动缸", "type": "Electric", "max_force": 50000, "stroke": 100, "max_velocity": 100, "bound_node_id": 0, "bound_channel": 2}, {"actuator_id": "ACT004", "name": "旋转电机", "type": "Electric", "max_force": 30000, "stroke": 360, "max_velocity": 1800, "bound_node_id": 2, "bound_channel": 0}], "sensors": [{"sensor_id": "SEN001", "name": "主力传感器", "type": "Force", "full_scale": 250000, "unit": "N", "sensitivity": 2.0, "bound_node_id": 1, "bound_channel": 0}, {"sensor_id": "SEN002", "name": "主位移传感器", "type": "Displacement", "full_scale": 350, "unit": "mm", "sensitivity": 10.0, "bound_node_id": 1, "bound_channel": 1}, {"sensor_id": "SEN003", "name": "辅助力传感器", "type": "Force", "full_scale": 120000, "unit": "N", "sensitivity": 2.5, "bound_node_id": 1, "bound_channel": 2}, {"sensor_id": "SEN004", "name": "压力传感器", "type": "Pressure", "full_scale": 350, "unit": "bar", "sensitivity": 0.1, "bound_node_id": 1, "bound_channel": 3}, {"sensor_id": "SEN005", "name": "温度传感器", "type": "Temperature", "full_scale": 150, "unit": "°C", "sensitivity": 0.1, "bound_node_id": 1, "bound_channel": 4}, {"sensor_id": "SEN006", "name": "应变传感器", "type": "Strain", "full_scale": 5000, "unit": "με", "sensitivity": 1.0, "bound_node_id": 1, "bound_channel": 5}], "load_channels": [{"channel_id": "CH001", "name": "主加载通道", "max_force": 200000, "max_velocity": 500, "control_mode": "Force", "pid_p": 1.0, "pid_i": 0.1, "pid_d": 0.01, "safety_enabled": true, "position_limit_low": -300, "position_limit_high": 300, "load_limit_low": -220000, "load_limit_high": 220000}, {"channel_id": "CH002", "name": "辅助加载通道", "max_force": 100000, "max_velocity": 300, "control_mode": "Position", "pid_p": 0.8, "pid_i": 0.05, "pid_d": 0.005, "safety_enabled": true, "position_limit_low": -200, "position_limit_high": 200, "load_limit_low": -110000, "load_limit_high": 110000}], "test_parameters": {"sample_rate": 1000, "max_test_duration": 3600, "auto_save": true, "data_path": "./test_data/"}}