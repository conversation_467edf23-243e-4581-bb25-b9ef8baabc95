# 🗂️ 多数据存储解决方案

## 📋 问题描述

用户反馈：作动器、传感器数据全部存储在内存（DataManager.h）当中，不能存储多份数据。

## 🔍 问题分析

### ❌ 当前问题

1. **单一内存存储**：
   ```cpp
   // SensorDataManager
   QMap<QString, UI::SensorParams> sensorStorage_;  // 只能存一份数据
   
   // ActuatorDataManager  
   QMap<QString, UI::ActuatorParams> actuatorStorage_;  // 只能存一份数据
   QMap<int, UI::ActuatorGroup> groupStorage_;  // 只能存一份数据
   ```

2. **数据覆盖问题**：
   - 创建新项目时，内存数据被清空
   - 无法同时管理多个项目的数据
   - 切换项目时数据丢失

3. **缺少持久化**：
   - 应用关闭后数据丢失
   - 无法保存和加载多个配置文件

## 🎯 解决方案：双存储机制 + 项目文件管理

### 核心设计理念

**双存储机制**：
- **内存存储**：用于快速访问和UI操作
- **项目文件存储**：用于持久化和多项目管理

### 📊 数据流转架构

```
┌─────────────────┐    同步    ┌─────────────────┐    保存    ┌─────────────────┐
│   内存存储      │ ←────────→ │   项目对象      │ ────────→ │   项目文件      │
│ (DataManager)   │            │ (TestProject)   │           │ (.json/.xlsx)   │
└─────────────────┘            └─────────────────┘           └─────────────────┘
       ↑                              ↑                              ↑
   快速访问                        数据中转                      持久化存储
   UI操作                         验证处理                      多项目管理
```

## 🔧 实现方案

### 1. **数据同步方法**

#### 1.1 方法声明

**文件**: `MainWindow_Qt_Simple.h`

```cpp
// 🆕 新增：数据同步方法
void syncMemoryDataToProject();    // 内存 → 项目
void syncProjectDataToMemory();    // 项目 → 内存
void clearMemoryData();            // 清空内存数据
```

#### 1.2 内存数据同步到项目

```cpp
void CMyMainWindow::syncMemoryDataToProject() {
    if (!currentProject_) return;
    
    // 同步传感器数据
    if (sensorDataManager_) {
        QList<UI::SensorParams> sensors = sensorDataManager_->getAllSensors();
        for (const auto& sensor : sensors) {
            currentProject_->addSensorDetailedParams(sensor.serialNumber.toStdString(), sensor);
        }
    }
    
    // 同步作动器数据
    if (actuatorDataManager_) {
        QList<UI::ActuatorParams> actuators = actuatorDataManager_->getAllActuatorDetailedParams();
        for (const auto& actuator : actuators) {
            currentProject_->addActuatorDetailedParams(actuator.serialNumber.toStdString(), actuator);
        }
        
        QList<UI::ActuatorGroup> groups = actuatorDataManager_->getAllActuatorGroups();
        for (const auto& group : groups) {
            currentProject_->addActuatorGroup(group.groupId, group);
        }
    }
}
```

#### 1.3 项目数据同步到内存

```cpp
void CMyMainWindow::syncProjectDataToMemory() {
    if (!currentProject_) return;
    
    // 清空内存数据
    clearMemoryData();
    
    // 同步传感器数据到内存
    if (sensorDataManager_) {
        auto sensorSerialNumbers = currentProject_->getAllSensorSerialNumbers();
        for (const auto& serialNumber : sensorSerialNumbers) {
            UI::SensorParams params = currentProject_->getSensorDetailedParams(serialNumber);
            if (!params.serialNumber.isEmpty()) {
                sensorDataManager_->addSensor(params);
            }
        }
    }
    
    // 同步作动器数据到内存
    if (actuatorDataManager_) {
        auto actuatorSerialNumbers = currentProject_->getAllActuatorSerialNumbers();
        for (const auto& serialNumber : actuatorSerialNumbers) {
            UI::ActuatorParams params = currentProject_->getActuatorDetailedParams(serialNumber);
            if (!params.serialNumber.isEmpty()) {
                actuatorDataManager_->addActuator(params);
            }
        }
        
        auto actuatorGroups = currentProject_->getAllActuatorGroups();
        for (const auto& pair : actuatorGroups) {
            actuatorDataManager_->saveActuatorGroup(pair.second);
        }
    }
}
```

### 2. **关键时机的数据同步**

#### 2.1 创建新项目时

```cpp
// 创建新项目后
updateSensorDataManagerProject();
updateActuatorDataManagerProject();
syncMemoryDataToProject();  // 🆕 新增：同步内存数据到项目
```

#### 2.2 加载项目时

```cpp
// 加载项目成功后
updateSensorDataManagerProject();
updateActuatorDataManagerProject();
syncProjectDataToMemory();  // 🆕 新增：同步项目数据到内存
```

#### 2.3 保存项目时

```cpp
bool CMyMainWindow::SaveProject(const StringType& filePath) {
    if (!currentProject_) return false;
    
    // 🆕 新增：保存前同步内存数据到项目
    syncMemoryDataToProject();
    
    return currentProject_->SaveToFile(filePath);
}
```

### 3. **清理方法增强**

#### 3.1 SensorDataManager清理方法

**文件**: `SensorDataManager.h`

```cpp
// 🆕 新增：数据清理
void clearAllSensors();
```

**文件**: `SensorDataManager.cpp`

```cpp
void SensorDataManager::clearAllSensors() {
    try {
        if (project_) {
            // 清空项目中的传感器数据
            auto serialNumbers = project_->getAllSensorSerialNumbers();
            for (const auto& serialNumber : serialNumbers) {
                project_->removeSensorDetailedParams(serialNumber);
            }
        } else {
            // 清空内存存储
            sensorStorage_.clear();
        }
        clearError();
    } catch (const std::exception& e) {
        setError(QString(u8"清空传感器数据失败: %1").arg(e.what()));
    }
}
```

## ✅ 解决方案优势

### 1. **多项目支持** ✅

```
项目A.json ←→ 内存存储 ←→ 项目B.json
     ↓              ↓              ↓
  传感器A         当前数据        传感器B
  作动器A                        作动器B
```

### 2. **数据持久化** ✅

- **自动保存**：项目保存时自动同步内存数据
- **自动加载**：项目加载时自动同步到内存
- **数据安全**：双重存储，防止数据丢失

### 3. **性能优化** ✅

- **内存访问**：UI操作直接访问内存，响应快速
- **批量同步**：只在关键时机进行数据同步
- **按需加载**：只加载当前项目的数据到内存

### 4. **向后兼容** ✅

- **现有功能**：不影响现有的DataManager功能
- **渐进升级**：可以逐步迁移到新的存储机制
- **数据格式**：兼容现有的JSON/XLSX格式

## 🎯 使用场景

### 场景1：多项目管理

```cpp
// 保存当前项目
SaveProject("项目A.json");

// 切换到新项目
LoadProjectFromJSON("项目B.json");  // 自动清空内存并加载项目B数据

// 再次切换回项目A
LoadProjectFromJSON("项目A.json");  // 自动清空内存并加载项目A数据
```

### 场景2：数据备份与恢复

```cpp
// 工作过程中自动保存
syncMemoryDataToProject();  // 内存数据同步到项目
SaveProject("backup.json");  // 保存备份

// 意外情况恢复
LoadProjectFromJSON("backup.json");  // 从备份恢复
syncProjectDataToMemory();  // 恢复到内存
```

### 场景3：批量数据处理

```cpp
// 批量处理多个项目文件
QStringList projectFiles = {"项目1.json", "项目2.json", "项目3.json"};
for (const QString& file : projectFiles) {
    LoadProjectFromJSON(file);     // 加载项目
    // 处理数据...
    syncMemoryDataToProject();     // 同步修改
    SaveProject(file);             // 保存修改
}
```

## 🎉 总结

通过实现双存储机制和数据同步方法，现在系统支持：

1. **多项目管理** ✅：可以保存和加载多个项目文件
2. **数据持久化** ✅：数据不会因为应用关闭而丢失
3. **快速访问** ✅：UI操作仍然使用内存存储，保持高性能
4. **数据安全** ✅：双重存储机制，防止数据丢失
5. **向后兼容** ✅：不影响现有功能，平滑升级

**现在您可以创建多个项目文件，每个文件包含独立的传感器和作动器配置，实现真正的多数据存储管理！**
