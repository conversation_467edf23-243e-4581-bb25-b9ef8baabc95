/****************************************************************************
** Meta object code from reading C++ file 'SensorDialog_1_2.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../001_SiteResConfig_OldStruct/SiteResConfig/include/SensorDialog_1_2.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'SensorDialog_1_2.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_UI__SensorDialog_1_2_t {
    QByteArrayData data[18];
    char stringdata0[309];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_UI__SensorDialog_1_2_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_UI__SensorDialog_1_2_t qt_meta_stringdata_UI__SensorDialog_1_2 = {
    {
QT_MOC_LITERAL(0, 0, 20), // "UI::SensorDialog_1_2"
QT_MOC_LITERAL(1, 21, 19), // "sensorParamsChanged"
QT_MOC_LITERAL(2, 41, 0), // ""
QT_MOC_LITERAL(3, 42, 16), // "SensorParams_1_2"
QT_MOC_LITERAL(4, 59, 6), // "params"
QT_MOC_LITERAL(5, 66, 17), // "sensorTypeChanged"
QT_MOC_LITERAL(6, 84, 10), // "sensorType"
QT_MOC_LITERAL(7, 95, 23), // "validationStatusChanged"
QT_MOC_LITERAL(8, 119, 7), // "isValid"
QT_MOC_LITERAL(9, 127, 15), // "onSensorChanged"
QT_MOC_LITERAL(10, 143, 19), // "onSensorTypeChanged"
QT_MOC_LITERAL(11, 163, 17), // "onUnitTypeChanged"
QT_MOC_LITERAL(12, 181, 30), // "onCalibrationDateButtonClicked"
QT_MOC_LITERAL(13, 212, 20), // "onLoginButtonClicked"
QT_MOC_LITERAL(14, 233, 15), // "onAcceptClicked"
QT_MOC_LITERAL(15, 249, 15), // "onRejectClicked"
QT_MOC_LITERAL(16, 265, 20), // "onResetButtonClicked"
QT_MOC_LITERAL(17, 286, 22) // "onPreviewButtonClicked"

    },
    "UI::SensorDialog_1_2\0sensorParamsChanged\0"
    "\0SensorParams_1_2\0params\0sensorTypeChanged\0"
    "sensorType\0validationStatusChanged\0"
    "isValid\0onSensorChanged\0onSensorTypeChanged\0"
    "onUnitTypeChanged\0onCalibrationDateButtonClicked\0"
    "onLoginButtonClicked\0onAcceptClicked\0"
    "onRejectClicked\0onResetButtonClicked\0"
    "onPreviewButtonClicked"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_UI__SensorDialog_1_2[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      12,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       3,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   74,    2, 0x06 /* Public */,
       5,    1,   77,    2, 0x06 /* Public */,
       7,    1,   80,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
       9,    0,   83,    2, 0x08 /* Private */,
      10,    0,   84,    2, 0x08 /* Private */,
      11,    0,   85,    2, 0x08 /* Private */,
      12,    0,   86,    2, 0x08 /* Private */,
      13,    0,   87,    2, 0x08 /* Private */,
      14,    0,   88,    2, 0x08 /* Private */,
      15,    0,   89,    2, 0x08 /* Private */,
      16,    0,   90,    2, 0x08 /* Private */,
      17,    0,   91,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, QMetaType::QString,    6,
    QMetaType::Void, QMetaType::Bool,    8,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void UI::SensorDialog_1_2::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<SensorDialog_1_2 *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->sensorParamsChanged((*reinterpret_cast< const SensorParams_1_2(*)>(_a[1]))); break;
        case 1: _t->sensorTypeChanged((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 2: _t->validationStatusChanged((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 3: _t->onSensorChanged(); break;
        case 4: _t->onSensorTypeChanged(); break;
        case 5: _t->onUnitTypeChanged(); break;
        case 6: _t->onCalibrationDateButtonClicked(); break;
        case 7: _t->onLoginButtonClicked(); break;
        case 8: _t->onAcceptClicked(); break;
        case 9: _t->onRejectClicked(); break;
        case 10: _t->onResetButtonClicked(); break;
        case 11: _t->onPreviewButtonClicked(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (SensorDialog_1_2::*)(const SensorParams_1_2 & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SensorDialog_1_2::sensorParamsChanged)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (SensorDialog_1_2::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SensorDialog_1_2::sensorTypeChanged)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (SensorDialog_1_2::*)(bool );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&SensorDialog_1_2::validationStatusChanged)) {
                *result = 2;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject UI::SensorDialog_1_2::staticMetaObject = { {
    QMetaObject::SuperData::link<QDialog::staticMetaObject>(),
    qt_meta_stringdata_UI__SensorDialog_1_2.data,
    qt_meta_data_UI__SensorDialog_1_2,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *UI::SensorDialog_1_2::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *UI::SensorDialog_1_2::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_UI__SensorDialog_1_2.stringdata0))
        return static_cast<void*>(this);
    return QDialog::qt_metacast(_clname);
}

int UI::SensorDialog_1_2::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QDialog::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 12)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 12;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 12)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 12;
    }
    return _id;
}

// SIGNAL 0
void UI::SensorDialog_1_2::sensorParamsChanged(const SensorParams_1_2 & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void UI::SensorDialog_1_2::sensorTypeChanged(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void UI::SensorDialog_1_2::validationStatusChanged(bool _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
