/**
 * @file MockHardware.cpp
 * @brief 模拟硬件设备实现 - 已弃用
 * @details 实现用于测试的模拟硬件设备 - 数据模拟功能已弃用，整个文件不再使用
 * <AUTHOR> Assistant
 * @date 2025-08-05
 * @version 1.0.0
 * @deprecated 数据模拟功能已弃用，此文件不再使用
 */

// ============================================================================
// 注意：此文件中的所有代码已被弃用
// 数据模拟相关功能不再使用，整个MockHardware模块已停用
// ============================================================================

#include "MockHardware.h"
#include <iostream>
#include <chrono>
#include <thread>

namespace Hardware {

// ============================================================================
// MockServoController 实现
// ============================================================================

MockServoController::MockServoController(int nodeId, const StringType& deviceName, int channelCount)
    : nodeId_(nodeId)
    , deviceName_(deviceName)
    , connectionStatus_(CommunicationStatus::Disconnected)
    , deviceStatus_(DeviceStatus::Unknown)
    , channelCount_(channelCount)
    , sampleRate_(1000.0)
    , randomGenerator_(std::random_device{}())
    , noiseDistribution_(-0.1, 0.1)
    , acquisitionRunning_(false) {
    
    // 初始化通道数据
    channelModes_.resize(channelCount_, ControlMode::Manual);
    channelEnabled_.resize(channelCount_, false);
    commandValues_.resize(channelCount_, 0.0);
    feedbackValues_.resize(channelCount_, 0.0);
    positionValues_.resize(channelCount_, 0.0);
    pidParams_.resize(channelCount_);
    safetyLimits_.resize(channelCount_);
    
    std::wcout << L"创建模拟伺服控制器: " << Utils::Utf8ToWide(deviceName_).c_str() 
              << L", 节点ID=" << nodeId_ << L", 通道数=" << channelCount_ << std::endl;
}

MockServoController::~MockServoController() {
    StopAcquisition();
    Disconnect();
    std::wcout << L"销毁模拟伺服控制器: " << Utils::Utf8ToWide(deviceName_).c_str() << std::endl;
}

bool MockServoController::Connect() {
    std::wcout << L"连接模拟伺服控制器: " << Utils::Utf8ToWide(deviceName_).c_str() << std::endl;
    
    // 模拟连接延迟
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    connectionStatus_ = CommunicationStatus::Connected;
    ChangeDeviceStatus(DeviceStatus::Ready);
    
    std::wcout << L"✓ 模拟伺服控制器连接成功" << std::endl;
    return true;
}

void MockServoController::Disconnect() {
    std::wcout << L"断开模拟伺服控制器: " << Utils::Utf8ToWide(deviceName_).c_str() << std::endl;
    
    StopAcquisition();
    connectionStatus_ = CommunicationStatus::Disconnected;
    ChangeDeviceStatus(DeviceStatus::Unknown);
    
    std::wcout << L"模拟伺服控制器已断开连接" << std::endl;
}

CommunicationStatus MockServoController::GetConnectionStatus() const {
    return connectionStatus_;
}

DeviceStatus MockServoController::GetDeviceStatus() const {
    return deviceStatus_;
}

StringType MockServoController::GetDeviceInfo() const {
    return deviceName_ + u8" (模拟设备) - 节点ID: " + std::to_string(nodeId_) + 
           u8", 通道数: " + std::to_string(channelCount_);
}

bool MockServoController::SendCommand(int channelId, double commandValue) {
    if (!IsValidChannelId(channelId)) {
        return false;
    }
    
    if (connectionStatus_ != CommunicationStatus::Connected) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(dataMutex_);
    commandValues_[channelId] = commandValue;
    
    // 模拟指令响应延迟
    std::this_thread::sleep_for(std::chrono::microseconds(100));
    
    return true;
}

bool MockServoController::ReadFeedback(int channelId, double& feedbackValue) {
    if (!IsValidChannelId(channelId)) {
        return false;
    }
    
    if (connectionStatus_ != CommunicationStatus::Connected) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(dataMutex_);
    feedbackValue = GenerateMockFeedback(channelId);
    feedbackValues_[channelId] = feedbackValue;
    
    return true;
}

bool MockServoController::SetControlMode(int channelId, ControlMode mode) {
    if (!IsValidChannelId(channelId)) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(dataMutex_);
    channelModes_[channelId] = mode;
    
    std::wcout << L"设置通道 " << channelId << L" 控制模式: " << static_cast<int>(mode) << std::endl;
    return true;
}

bool MockServoController::SetChannelEnabled(int channelId, bool enabled) {
    if (!IsValidChannelId(channelId)) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(dataMutex_);
    channelEnabled_[channelId] = enabled;
    
    std::wcout << L"设置通道 " << channelId << L" 使能状态: " << (enabled ? L"启用" : L"禁用") << std::endl;
    return true;
}

bool MockServoController::SetPIDParameters(int channelId, double kp, double ki, double kd, double ks) {
    if (!IsValidChannelId(channelId)) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(dataMutex_);
    pidParams_[channelId].kp = kp;
    pidParams_[channelId].ki = ki;
    pidParams_[channelId].kd = kd;
    pidParams_[channelId].ks = ks;
    
    std::wcout << L"设置通道 " << channelId << L" PID参数: Kp=" << kp 
              << L", Ki=" << ki << L", Kd=" << kd << L", Ks=" << ks << std::endl;
    return true;
}

bool MockServoController::SetSafetyLimits(int channelId, double lowerLimit, double upperLimit) {
    if (!IsValidChannelId(channelId)) {
        return false;
    }
    
    if (lowerLimit >= upperLimit) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(dataMutex_);
    safetyLimits_[channelId].lowerLimit = lowerLimit;
    safetyLimits_[channelId].upperLimit = upperLimit;
    
    std::wcout << L"设置通道 " << channelId << L" 安全限制: [" << lowerLimit 
              << L", " << upperLimit << L"]" << std::endl;
    return true;
}

bool MockServoController::EmergencyStop() {
    std::wcout << L"模拟伺服控制器紧急停止" << std::endl;
    
    std::lock_guard<std::mutex> lock(dataMutex_);
    
    // 清零所有指令值
    for (auto& cmd : commandValues_) {
        cmd = 0.0;
    }
    
    // 禁用所有通道
    for (auto& enabled : channelEnabled_) {
        enabled = false;
    }
    
    ChangeDeviceStatus(DeviceStatus::Error);
    return true;
}

bool MockServoController::Reset() {
    std::wcout << L"复位模拟伺服控制器" << std::endl;
    
    std::lock_guard<std::mutex> lock(dataMutex_);
    
    // 重置所有数据
    std::fill(commandValues_.begin(), commandValues_.end(), 0.0);
    std::fill(feedbackValues_.begin(), feedbackValues_.end(), 0.0);
    std::fill(positionValues_.begin(), positionValues_.end(), 0.0);
    
    ChangeDeviceStatus(DeviceStatus::Ready);
    return true;
}

void MockServoController::SetDataCallback(HardwareEventCallback callback) {
    dataCallback_ = callback;
}

void MockServoController::SetStatusCallback(StatusChangeCallback callback) {
    statusCallback_ = callback;
}

void MockServoController::SetErrorCallback(ErrorCallback callback) {
    errorCallback_ = callback;
}

int MockServoController::GetChannelCount() const {
    return channelCount_;
}

double MockServoController::GetSampleRate() const {
    return sampleRate_;
}

bool MockServoController::SetSampleRate(double sampleRate) {
    if (sampleRate <= 0.0 || sampleRate > 10000.0) {
        return false;
    }
    
    sampleRate_ = sampleRate;
    std::wcout << L"设置采样率: " << sampleRate << L" Hz" << std::endl;
    return true;
}

bool MockServoController::StartAcquisition() {
    if (acquisitionRunning_.load()) {
        return true; // 已经在运行
    }
    
    std::wcout << L"开始数据采集" << std::endl;
    
    acquisitionRunning_.store(true);
    acquisitionThread_ = std::thread(&MockServoController::AcquisitionThreadFunction, this);
    
    ChangeDeviceStatus(DeviceStatus::Running);
    return true;
}

bool MockServoController::StopAcquisition() {
    if (!acquisitionRunning_.load()) {
        return true; // 已经停止
    }
    
    std::wcout << L"停止数据采集" << std::endl;
    
    acquisitionRunning_.store(false);
    
    if (acquisitionThread_.joinable()) {
        acquisitionThread_.join();
    }
    
    ChangeDeviceStatus(DeviceStatus::Ready);
    return true;
}

bool MockServoController::CalibrateChannel(int channelId, const StringType& calibrationType, double calibrationValue) {
    if (!IsValidChannelId(channelId)) {
        return false;
    }
    
    std::wcout << L"校准通道 " << channelId << L": 类型=" << Utils::Utf8ToWide(calibrationType).c_str() 
              << L", 值=" << calibrationValue << std::endl;
    
    // 模拟校准延迟
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    
    return true;
}

bool MockServoController::IsValidChannelId(int channelId) const {
    return channelId >= 0 && channelId < channelCount_;
}

void MockServoController::AcquisitionThreadFunction() {
    auto lastTime = std::chrono::steady_clock::now();
    const auto sampleInterval = std::chrono::microseconds(static_cast<int64_t>(1000000.0 / sampleRate_));
    
    while (acquisitionRunning_.load()) {
        auto currentTime = std::chrono::steady_clock::now();
        
        if (currentTime - lastTime >= sampleInterval) {
            // 更新所有通道的数据
            for (int channelId = 0; channelId < channelCount_; ++channelId) {
                if (channelEnabled_[channelId]) {
                    SendDataCallback(channelId);
                }
            }
            
            lastTime = currentTime;
        }
        
        // 短暂休眠以避免过度占用CPU
        std::this_thread::sleep_for(std::chrono::microseconds(100));
    }
}

double MockServoController::GenerateMockFeedback(int channelId) const {
    // 模拟反馈值 = 指令值 + 小量噪声
    double noise = noiseDistribution_(randomGenerator_);
    return commandValues_[channelId] * 0.95 + noise; // 95%跟随度 + 噪声
}

double MockServoController::GenerateMockPosition(int channelId) const {
    // 模拟位置值基于反馈值积分
    static std::vector<double> integratedPosition(channelCount_, 0.0);
    
    double dt = 1.0 / sampleRate_;
    integratedPosition[channelId] += feedbackValues_[channelId] * dt;
    
    return integratedPosition[channelId];
}

void MockServoController::SendDataCallback(int channelId) const {
    if (dataCallback_) {
        HardwareDataPacket packet;
        packet.timestamp = static_cast<uint32_t>(Utils::GetCurrentTimestamp());
        packet.nodeId = static_cast<uint16_t>(nodeId_);
        packet.channelId = static_cast<uint16_t>(channelId);
        packet.commandValue = commandValues_[channelId];
        packet.feedbackValue = GenerateMockFeedback(channelId);
        packet.positionValue = GenerateMockPosition(channelId);
        packet.statusFlags = channelEnabled_[channelId] ? 1 : 0;
        
        dataCallback_(packet);
    }
}

void MockServoController::ChangeDeviceStatus(DeviceStatus newStatus) {
    DeviceStatus oldStatus = deviceStatus_;
    deviceStatus_ = newStatus;

    if (statusCallback_ && oldStatus != newStatus) {
        statusCallback_(nodeId_, oldStatus, newStatus);
    }
}

// ============================================================================
// MockDataAcquisition 实现
// ============================================================================

MockDataAcquisition::MockDataAcquisition(int nodeId, const StringType& deviceName, int inputChannelCount)
    : nodeId_(nodeId)
    , deviceName_(deviceName)
    , connectionStatus_(CommunicationStatus::Disconnected)
    , deviceStatus_(DeviceStatus::Unknown)
    , inputChannelCount_(inputChannelCount)
    , triggerChannelId_(-1)
    , triggerLevel_(0.0)
    , triggerSlope_(true)
    , randomGenerator_(std::random_device{}())
    , dataDistribution_(-10.0, 10.0) {

    // 初始化输入通道数据
    channelRanges_.resize(inputChannelCount_, 10.0);
    channelUnits_.resize(inputChannelCount_, u8"V");
    channelValues_.resize(inputChannelCount_, 0.0);

    std::wcout << L"创建模拟数据采集器: " << Utils::Utf8ToWide(deviceName_).c_str()
              << L", 节点ID=" << nodeId_ << L", 输入通道数=" << inputChannelCount_ << std::endl;
}

MockDataAcquisition::~MockDataAcquisition() {
    Disconnect();
    std::wcout << L"销毁模拟数据采集器: " << Utils::Utf8ToWide(deviceName_).c_str() << std::endl;
}

bool MockDataAcquisition::Connect() {
    std::wcout << L"连接模拟数据采集器: " << Utils::Utf8ToWide(deviceName_).c_str() << std::endl;

    // 模拟连接延迟
    std::this_thread::sleep_for(std::chrono::milliseconds(50));

    connectionStatus_ = CommunicationStatus::Connected;
    ChangeDeviceStatus(DeviceStatus::Ready);

    std::wcout << L"✓ 模拟数据采集器连接成功" << std::endl;
    return true;
}

void MockDataAcquisition::Disconnect() {
    std::wcout << L"断开模拟数据采集器: " << Utils::Utf8ToWide(deviceName_).c_str() << std::endl;

    connectionStatus_ = CommunicationStatus::Disconnected;
    ChangeDeviceStatus(DeviceStatus::Unknown);

    std::wcout << L"模拟数据采集器已断开连接" << std::endl;
}

CommunicationStatus MockDataAcquisition::GetConnectionStatus() const {
    return connectionStatus_;
}

DeviceStatus MockDataAcquisition::GetDeviceStatus() const {
    return deviceStatus_;
}

StringType MockDataAcquisition::GetDeviceInfo() const {
    return deviceName_ + u8" (模拟设备) - 节点ID: " + std::to_string(nodeId_) +
           u8", 输入通道数: " + std::to_string(inputChannelCount_);
}

bool MockDataAcquisition::SendCommand(int channelId, double commandValue) {
    // 数据采集器通常不支持发送指令
    return false;
}

bool MockDataAcquisition::ReadFeedback(int channelId, double& feedbackValue) {
    if (!IsValidInputChannelId(channelId)) {
        return false;
    }

    if (connectionStatus_ != CommunicationStatus::Connected) {
        return false;
    }

    std::lock_guard<std::mutex> lock(dataMutex_);
    feedbackValue = GenerateMockInputData(channelId);
    channelValues_[channelId] = feedbackValue;

    return true;
}

bool MockDataAcquisition::SetControlMode(int channelId, ControlMode mode) {
    // 数据采集器通常不支持控制模式设置
    return false;
}

bool MockDataAcquisition::SetChannelEnabled(int channelId, bool enabled) {
    if (!IsValidInputChannelId(channelId)) {
        return false;
    }

    std::wcout << L"设置输入通道 " << channelId << L" 使能状态: " << (enabled ? L"启用" : L"禁用") << std::endl;
    return true;
}

bool MockDataAcquisition::SetPIDParameters(int channelId, double kp, double ki, double kd, double ks) {
    // 数据采集器通常不支持PID参数设置
    return false;
}

bool MockDataAcquisition::SetSafetyLimits(int channelId, double lowerLimit, double upperLimit) {
    if (!IsValidInputChannelId(channelId)) {
        return false;
    }

    std::wcout << L"设置输入通道 " << channelId << L" 安全限制: [" << lowerLimit
              << L", " << upperLimit << L"]" << std::endl;
    return true;
}

bool MockDataAcquisition::EmergencyStop() {
    std::wcout << L"模拟数据采集器紧急停止" << std::endl;
    ChangeDeviceStatus(DeviceStatus::Error);
    return true;
}

bool MockDataAcquisition::Reset() {
    std::wcout << L"复位模拟数据采集器" << std::endl;

    std::lock_guard<std::mutex> lock(dataMutex_);
    std::fill(channelValues_.begin(), channelValues_.end(), 0.0);

    ChangeDeviceStatus(DeviceStatus::Ready);
    return true;
}

void MockDataAcquisition::SetDataCallback(HardwareEventCallback callback) {
    dataCallback_ = callback;
}

void MockDataAcquisition::SetStatusCallback(StatusChangeCallback callback) {
    statusCallback_ = callback;
}

void MockDataAcquisition::SetErrorCallback(ErrorCallback callback) {
    errorCallback_ = callback;
}

int MockDataAcquisition::GetInputChannelCount() const {
    return inputChannelCount_;
}

bool MockDataAcquisition::ConfigureInputChannel(int channelId, double range, const StringType& unit) {
    if (!IsValidInputChannelId(channelId)) {
        return false;
    }

    std::lock_guard<std::mutex> lock(dataMutex_);
    channelRanges_[channelId] = range;
    channelUnits_[channelId] = unit;

    std::wcout << L"配置输入通道 " << channelId << L": 量程=" << range
              << L", 单位=" << Utils::Utf8ToWide(unit).c_str() << std::endl;
    return true;
}

bool MockDataAcquisition::ReadMultipleChannels(const std::vector<int>& channelIds, std::vector<double>& values) {
    if (connectionStatus_ != CommunicationStatus::Connected) {
        return false;
    }

    values.clear();
    values.reserve(channelIds.size());

    std::lock_guard<std::mutex> lock(dataMutex_);

    for (int channelId : channelIds) {
        if (IsValidInputChannelId(channelId)) {
            values.push_back(GenerateMockInputData(channelId));
        } else {
            values.push_back(0.0);
        }
    }

    return true;
}

bool MockDataAcquisition::SetTrigger(int channelId, double triggerLevel, bool triggerSlope) {
    if (!IsValidInputChannelId(channelId)) {
        return false;
    }

    std::lock_guard<std::mutex> lock(dataMutex_);
    triggerChannelId_ = channelId;
    triggerLevel_ = triggerLevel;
    triggerSlope_ = triggerSlope;

    std::wcout << L"设置触发: 通道=" << channelId << L", 电平=" << triggerLevel
              << L", 斜率=" << (triggerSlope ? L"上升沿" : L"下降沿") << std::endl;
    return true;
}

bool MockDataAcquisition::IsValidInputChannelId(int channelId) const {
    return channelId >= 0 && channelId < inputChannelCount_;
}

double MockDataAcquisition::GenerateMockInputData(int channelId) const {
    // 生成基于通道ID的模拟数据
    double baseValue = std::sin(Utils::GetCurrentTimestamp() * 0.001 + channelId) * channelRanges_[channelId] * 0.5;
    double noise = dataDistribution_(randomGenerator_) * 0.1;
    return baseValue + noise;
}

void MockDataAcquisition::ChangeDeviceStatus(DeviceStatus newStatus) {
    DeviceStatus oldStatus = deviceStatus_;
    deviceStatus_ = newStatus;

    if (statusCallback_ && oldStatus != newStatus) {
        statusCallback_(nodeId_, oldStatus, newStatus);
    }
}

// ============================================================================
// MockHardwareFactory 实现
// ============================================================================

SharedPtr<IServoController> MockHardwareFactory::CreateMockServoController(
    int nodeId, const StringType& deviceName, int channelCount) {
    return std::make_shared<MockServoController>(nodeId, deviceName, channelCount);
}

SharedPtr<IDataAcquisition> MockHardwareFactory::CreateMockDataAcquisition(
    int nodeId, const StringType& deviceName, int inputChannelCount) {
    return std::make_shared<MockDataAcquisition>(nodeId, deviceName, inputChannelCount);
}

DataModels::HardwareNode MockHardwareFactory::CreateMockNodeConfig(
    int nodeId, const StringType& nodeName, const StringType& nodeType) {
    DataModels::HardwareNode config;
    config.nodeId = nodeId;
    config.nodeName = nodeName;
    config.nodeType = nodeType;
    config.ipAddress = u8"192.168.1." + std::to_string(100 + nodeId);
    config.port = 8080 + nodeId;
    config.isConnected = false;
    config.firmwareVersion = u8"v2.1.0 (模拟)";

    return config;
}

} // namespace Hardware
