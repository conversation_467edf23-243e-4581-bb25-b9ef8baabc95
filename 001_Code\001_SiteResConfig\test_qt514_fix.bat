@echo off
chcp 65001 > nul
echo ========================================
echo Qt 5.14兼容性修复测试
echo ========================================
echo.

echo [INFO] 设置Qt环境变量...
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo [INFO] 进入项目目录...
cd /d "%~dp0\SiteResConfig"

echo [INFO] 清理之前的编译文件...
if exist "Makefile" del Makefile
if exist "*.o" del *.o
mingw32-make clean > nul 2>&1

echo [INFO] 重新生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo [ERROR] qmake失败！
    pause
    exit /b 1
)

echo [INFO] 开始编译（修复Qt 5.14兼容性问题）...
mingw32-make -j4

if errorlevel 1 (
    echo.
    echo [ERROR] 编译失败！
    echo [INFO] 主要修复内容：
    echo   - 移除了Qt 5.14中已弃用的setCodecForCStrings
    echo   - 简化了控制台编码设置
    echo   - 改进了界面刷新机制
    echo.
    echo [INFO] 请检查上面的错误信息
    pause
    exit /b 1
)

echo.
echo [SUCCESS] 编译成功！
echo.
echo [INFO] 修复内容总结：
echo   1. ✅ 修复Qt 5.14兼容性问题
echo   2. ✅ 简化控制台编码设置
echo   3. ✅ 改进界面刷新机制，避免卡死
echo   4. ✅ 增强异常处理
echo.
echo [INFO] 启动应用程序测试...

start "" "debug\SiteResConfig.exe"

echo.
echo [INFO] 应用程序已启动
echo [INFO] 请测试工程导入功能：
echo        文件: C:\Users\<USER>\Desktop\20250818152156_实验工程.xlsx
echo.
echo [测试重点]
echo   - 观察控制台输出是否正常
echo   - 检查导入过程是否显示进度
echo   - 确认导入后不再卡死
echo   - 查看导入完成提示对话框
echo.
pause
