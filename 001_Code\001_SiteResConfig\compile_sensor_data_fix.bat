@echo off
echo ========================================
echo 编译传感器数据存储修复版本
echo ========================================

REM 设置Qt环境变量
set QT_DIR=D:\Qt\Qt5.14.2
set QT_VERSION=5.14.2
set MINGW_VERSION=mingw73_32
set MINGW_TOOLS=mingw730_32

REM 设置PATH环境变量
set PATH=%QT_DIR%\%QT_VERSION%\%MINGW_VERSION%\bin;%QT_DIR%\Tools\%MINGW_TOOLS%\bin;%PATH%

echo Qt环境设置完成
echo Qt路径: %QT_DIR%\%QT_VERSION%\%MINGW_VERSION%\bin
echo MinGW路径: %QT_DIR%\Tools\%MINGW_TOOLS%\bin
echo.

REM 检查qmake是否可用
qmake -version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 错误: qmake未找到，请检查Qt安装路径
    echo 当前PATH: %PATH%
    pause
    exit /b 1
)

echo ✅ qmake检查通过
qmake -version
echo.

REM 检查mingw32-make是否可用
mingw32-make --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 错误: mingw32-make未找到，请检查MinGW安装
    pause
    exit /b 1
)

echo ✅ mingw32-make检查通过
echo.

REM 进入项目目录
cd SiteResConfig
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 错误: 无法进入SiteResConfig目录
    pause
    exit /b 1
)

echo 当前目录: %CD%
echo.

REM 清理旧文件
echo [1/4] 清理旧的编译文件...
if exist Makefile (
    mingw32-make clean >nul 2>&1
    echo 清理完成
) else (
    echo 无需清理
)
echo.

REM 生成Makefile
echo [2/4] 生成Makefile...
qmake SiteResConfig_Simple.pro
if %ERRORLEVEL% NEQ 0 (
    echo ❌ qmake失败
    pause
    exit /b 1
)
echo ✅ Makefile生成成功
echo.

REM 编译项目
echo [3/4] 开始编译项目...
echo 使用4个并行任务编译...
mingw32-make -j4
set COMPILE_RESULT=%ERRORLEVEL%

echo.
echo [4/4] 编译结果检查...

if %COMPILE_RESULT% EQU 0 (
    echo.
    echo ✅ 编译成功！
    echo.
    echo 📁 输出文件:
    if exist debug\SiteResConfig.exe (
        echo   - debug\SiteResConfig.exe
        dir debug\SiteResConfig.exe | findstr SiteResConfig.exe
    )
    if exist release\SiteResConfig.exe (
        echo   - release\SiteResConfig.exe  
        dir release\SiteResConfig.exe | findstr SiteResConfig.exe
    )
    echo.
    echo 🎯 修复内容:
    echo   - 修复了std::map不完整类型错误
    echo   - 修复了SensorDataManager链接错误
    echo   - 添加了SensorDataManager.cpp到项目文件
    echo   - 传感器详细参数现在可以完整保存
    echo   - 支持58个字段的完整数据存储
    echo.
    echo 📋 测试建议:
    echo   1. 启动应用: debug\SiteResConfig.exe
    echo   2. 创建传感器并填写完整参数
    echo   3. 保存项目验证数据完整性
    echo.
    
    REM 询问是否启动应用
    set /p START_APP="是否启动应用进行测试? (y/n): "
    if /i "%START_APP%"=="y" (
        echo 启动应用...
        start "" "debug\SiteResConfig.exe"
    )
    
) else (
    echo.
    echo ❌ 编译失败！
    echo.
    echo 🔍 可能的问题:
    echo   1. 头文件包含问题
    echo   2. 链接器错误
    echo   3. 语法错误
    echo.
    echo 💡 建议:
    echo   1. 检查编译错误信息
    echo   2. 验证所有头文件路径
    echo   3. 确认Qt和MinGW版本兼容性
    echo.
)

echo.
echo ========================================
echo 编译过程完成
echo ========================================

pause
