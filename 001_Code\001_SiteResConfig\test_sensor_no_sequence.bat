@echo off
echo ========================================
echo  测试传感器详细配置去掉传感器序号列
echo ========================================

REM 设置Qt环境变量
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目（测试传感器详细配置去掉传感器序号列）...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo 请检查上面的错误信息。
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！传感器序号列已去掉
    echo ========================================
    
    echo.
    echo ✅ 修改完成的内容:
    echo.
    echo 📊 传感器详细配置修改:
    echo - 修改前: 34列（组序号, 传感器组名称, 传感器序号, 传感器序列号, ...）
    echo - 修改后: 33列（组序号, 传感器组名称, 传感器序列号, ...）
    echo - 变化: ❌ 去掉了"传感器序号"列
    echo.
    echo 🎯 传感器详细配置（33列）:
    echo 第1列: 组序号（从1开始递增）
    echo 第2列: 传感器组名称
    echo 第3列: 传感器序列号（去掉了传感器序号）
    echo 第4列: 传感器类型
    echo 第5列: EDS标识
    echo 第6列: 尺寸
    echo 第7列: 型号
    echo 第8列: 量程
    echo 第9列: 精度
    echo 第10列: 单位
    echo 第11列: 灵敏度
    echo 第12列: 校准启用
    echo 第13列: 校准日期
    echo 第14列: 校准执行人
    echo 第15列: 单位类型
    echo 第16列: 单位值
    echo 第17列: 输入范围
    echo 第18列: 满量程最大值
    echo 第19列: 满量程最大值单位
    echo 第20列: 满量程最小值
    echo 第21列: 满量程最小值单位
    echo 第22列: 极性
    echo 第23列: 前置放大增益
    echo 第24列: 后置放大增益
    echo 第25列: 总增益
    echo 第26列: Delta K增益
    echo 第27列: 比例因子
    echo 第28列: 启用激励
    echo 第29列: 激励电压
    echo 第30列: 激励平衡
    echo 第31列: 激励频率
    echo 第32列: 相位
    echo 第33列: 编码器分辨率
    echo.
    echo 📝 Excel导出格式示例:
    echo.
    echo 组序号 ^| 传感器组名称 ^| 传感器序列号 ^| 传感器类型 ^| EDS标识 ^| ...
    echo ------|------------|------------|----------|---------|----
    echo 1     ^| 载荷_传感器组 ^| SEN001     ^| 载荷传感器 ^| EDS001  ^| ...
    echo 1     ^|            ^| SEN002     ^| 载荷传感器 ^| EDS002  ^| ...
    echo 1     ^|            ^| SEN003     ^| 载荷传感器 ^| EDS003  ^| ...
    echo 2     ^| 位置_传感器组 ^| SEN004     ^| 位置传感器 ^| EDS004  ^| ...
    echo 2     ^|            ^| SEN005     ^| 位置传感器 ^| EDS005  ^| ...
    echo 3     ^| 温度_传感器组 ^| SEN006     ^| 温度传感器 ^| EDS006  ^| ...
    echo.
    echo 🔧 技术实现:
    echo 1. 表头修改: 去掉"传感器序号"列
    echo 2. 数据写入: 删除sensor.sensorId的写入
    echo 3. 列号调整: 所有后续列号减1
    echo 4. 列宽调整: 从34列改为33列
    echo.
    echo 💡 结构关系:
    echo - 一个传感器组名称包括多个传感器信息
    echo - 组序号从1开始递增（1, 2, 3, 4...）
    echo - 组名称只在每组第一行显示
    echo - 传感器序列号作为唯一标识
    echo.
    
    if exist "SiteResConfig.exe" (
        echo 启动程序验证修改...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 启动程序验证修改...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 启动程序验证修改...
        start release\SiteResConfig.exe
    )
)

echo.
echo 📖 验证步骤:
echo.
echo 🎮 传感器详细配置验证:
echo 1. 启动软件，新建项目
echo 2. 创建多个传感器组
echo 3. 在每个组中添加传感器
echo 4. 导出传感器详细信息到Excel
echo 5. 验证Excel文件：
echo    - 总共33列（不是34列）
echo    - 第1列：组序号（1, 2, 3...）
echo    - 第2列：传感器组名称
echo    - 第3列：传感器序列号（没有传感器序号列）
echo    - 第4列：传感器类型
echo    - 第33列：编码器分辨率
echo.
echo 🎮 结构关系验证:
echo 1. 验证组序号从1开始递增
echo 2. 验证组名称只在每组第一行显示
echo 3. 验证一个组包含多个传感器信息
echo 4. 验证传感器序列号作为唯一标识
echo.
echo 🎮 数据完整性验证:
echo 1. 验证所有传感器数据都正确导出
echo 2. 确认没有传感器序号列
echo 3. 验证列对应关系正确
echo 4. 检查Excel格式专业性
echo.
echo ✅ 预期结果:
echo - 传感器详细配置为33列格式
echo - 没有"传感器序号"列
echo - 结构关系正确：一个组名称包括多个传感器信息
echo - 组序号从1开始递增
echo - 所有传感器数据完整导出
echo.
echo 🚨 如果测试失败:
echo - 检查Excel文件是否为33列
echo - 验证是否还有"传感器序号"列
echo - 确认列对应关系是否正确
echo - 检查数据完整性
echo.
pause
