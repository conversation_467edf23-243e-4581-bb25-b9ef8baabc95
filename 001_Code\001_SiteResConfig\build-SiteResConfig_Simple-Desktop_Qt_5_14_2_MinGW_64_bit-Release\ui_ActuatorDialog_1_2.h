/********************************************************************************
** Form generated from reading UI file 'ActuatorDialog_1_2.ui'
**
** Created by: Qt User Interface Compiler version 5.14.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_ACTUATORDIALOG_1_2_H
#define UI_ACTUATORDIALOG_1_2_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QDialog>
#include <QtWidgets/QDoubleSpinBox>
#include <QtWidgets/QFormLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QTabWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_ActuatorDialog_1_2
{
public:
    QVBoxLayout *mainLayout;
    QLabel *titleLabel;
    QTabWidget *tabWidget;
    QWidget *basicTab;
    QFormLayout *basicFormLayout;
    QLabel *nameLabel;
    QLineEdit *nameEdit;
    QLabel *typeLabel;
    QComboBox *typeCombo;
    QLabel *zeroOffsetLabel;
    QDoubleSpinBox *zeroOffsetSpinBox;
    QLabel *serialLabel;
    QLineEdit *serialEdit;
    QLabel *modelLabel;
    QLineEdit *modelEdit;
    QWidget *hardwareTab;
    QFormLayout *hardwareFormLayout;
    QLabel *lcIdLabel;
    QSpinBox *lcIdSpinBox;
    QLabel *stationIdLabel;
    QSpinBox *stationIdSpinBox;
    QLabel *aoConfigLabel;
    QLabel *boardIdAoLabel;
    QSpinBox *boardIdAoSpinBox;
    QLabel *boardTypeAoLabel;
    QSpinBox *boardTypeAoSpinBox;
    QLabel *portIdAoLabel;
    QSpinBox *portIdAoSpinBox;
    QLabel *doConfigLabel;
    QLabel *boardIdDoLabel;
    QSpinBox *boardIdDoSpinBox;
    QLabel *boardTypeDoLabel;
    QSpinBox *boardTypeDoSpinBox;
    QLabel *portIdDoLabel;
    QSpinBox *portIdDoSpinBox;
    QWidget *paramsTab;
    QFormLayout *paramsFormLayout;
    QLabel *kLabel;
    QDoubleSpinBox *kSpinBox;
    QLabel *bLabel;
    QDoubleSpinBox *bSpinBox;
    QLabel *precisionLabel;
    QDoubleSpinBox *precisionSpinBox;
    QLabel *polarityLabel;
    QComboBox *polarityCombo;
    QLabel *measUnitLabel;
    QComboBox *measUnitCombo;
    QLabel *measRangeLabel;
    QLabel *measRangeMinLabel;
    QDoubleSpinBox *measRangeMinSpinBox;
    QLabel *measRangeMaxLabel;
    QDoubleSpinBox *measRangeMaxSpinBox;
    QWidget *outputTab;
    QFormLayout *outputFormLayout;
    QLabel *outputSignalUnitLabel;
    QSpinBox *outputSignalUnitSpinBox;
    QLabel *outputRangeLabel;
    QLabel *outputRangeMinLabel;
    QDoubleSpinBox *outputRangeMinSpinBox;
    QLabel *outputRangeMaxLabel;
    QDoubleSpinBox *outputRangeMaxSpinBox;
    QHBoxLayout *buttonLayout;
    QSpacerItem *horizontalSpacer;
    QPushButton *okButton;
    QPushButton *cancelButton;

    void setupUi(QDialog *ActuatorDialog_1_2)
    {
        if (ActuatorDialog_1_2->objectName().isEmpty())
            ActuatorDialog_1_2->setObjectName(QString::fromUtf8("ActuatorDialog_1_2"));
        ActuatorDialog_1_2->resize(900, 650);
        ActuatorDialog_1_2->setModal(true);
        mainLayout = new QVBoxLayout(ActuatorDialog_1_2);
        mainLayout->setObjectName(QString::fromUtf8("mainLayout"));
        titleLabel = new QLabel(ActuatorDialog_1_2);
        titleLabel->setObjectName(QString::fromUtf8("titleLabel"));
        QFont font;
        font.setPointSize(14);
        font.setBold(true);
        font.setWeight(75);
        titleLabel->setFont(font);
        titleLabel->setAlignment(Qt::AlignCenter);

        mainLayout->addWidget(titleLabel);

        tabWidget = new QTabWidget(ActuatorDialog_1_2);
        tabWidget->setObjectName(QString::fromUtf8("tabWidget"));
        basicTab = new QWidget();
        basicTab->setObjectName(QString::fromUtf8("basicTab"));
        basicFormLayout = new QFormLayout(basicTab);
        basicFormLayout->setObjectName(QString::fromUtf8("basicFormLayout"));
        nameLabel = new QLabel(basicTab);
        nameLabel->setObjectName(QString::fromUtf8("nameLabel"));

        basicFormLayout->setWidget(0, QFormLayout::LabelRole, nameLabel);

        nameEdit = new QLineEdit(basicTab);
        nameEdit->setObjectName(QString::fromUtf8("nameEdit"));

        basicFormLayout->setWidget(0, QFormLayout::FieldRole, nameEdit);

        typeLabel = new QLabel(basicTab);
        typeLabel->setObjectName(QString::fromUtf8("typeLabel"));

        basicFormLayout->setWidget(1, QFormLayout::LabelRole, typeLabel);

        typeCombo = new QComboBox(basicTab);
        typeCombo->addItem(QString());
        typeCombo->addItem(QString());
        typeCombo->setObjectName(QString::fromUtf8("typeCombo"));

        basicFormLayout->setWidget(1, QFormLayout::FieldRole, typeCombo);

        zeroOffsetLabel = new QLabel(basicTab);
        zeroOffsetLabel->setObjectName(QString::fromUtf8("zeroOffsetLabel"));

        basicFormLayout->setWidget(2, QFormLayout::LabelRole, zeroOffsetLabel);

        zeroOffsetSpinBox = new QDoubleSpinBox(basicTab);
        zeroOffsetSpinBox->setObjectName(QString::fromUtf8("zeroOffsetSpinBox"));
        zeroOffsetSpinBox->setDecimals(3);
        zeroOffsetSpinBox->setMinimum(-1000.000000000000000);
        zeroOffsetSpinBox->setMaximum(1000.000000000000000);

        basicFormLayout->setWidget(2, QFormLayout::FieldRole, zeroOffsetSpinBox);

        serialLabel = new QLabel(basicTab);
        serialLabel->setObjectName(QString::fromUtf8("serialLabel"));

        basicFormLayout->setWidget(3, QFormLayout::LabelRole, serialLabel);

        serialEdit = new QLineEdit(basicTab);
        serialEdit->setObjectName(QString::fromUtf8("serialEdit"));

        basicFormLayout->setWidget(3, QFormLayout::FieldRole, serialEdit);

        modelLabel = new QLabel(basicTab);
        modelLabel->setObjectName(QString::fromUtf8("modelLabel"));

        basicFormLayout->setWidget(4, QFormLayout::LabelRole, modelLabel);

        modelEdit = new QLineEdit(basicTab);
        modelEdit->setObjectName(QString::fromUtf8("modelEdit"));

        basicFormLayout->setWidget(4, QFormLayout::FieldRole, modelEdit);

        tabWidget->addTab(basicTab, QString());
        hardwareTab = new QWidget();
        hardwareTab->setObjectName(QString::fromUtf8("hardwareTab"));
        hardwareFormLayout = new QFormLayout(hardwareTab);
        hardwareFormLayout->setObjectName(QString::fromUtf8("hardwareFormLayout"));
        lcIdLabel = new QLabel(hardwareTab);
        lcIdLabel->setObjectName(QString::fromUtf8("lcIdLabel"));

        hardwareFormLayout->setWidget(0, QFormLayout::LabelRole, lcIdLabel);

        lcIdSpinBox = new QSpinBox(hardwareTab);
        lcIdSpinBox->setObjectName(QString::fromUtf8("lcIdSpinBox"));
        lcIdSpinBox->setMinimum(1);
        lcIdSpinBox->setMaximum(255);
        lcIdSpinBox->setValue(1);

        hardwareFormLayout->setWidget(0, QFormLayout::FieldRole, lcIdSpinBox);

        stationIdLabel = new QLabel(hardwareTab);
        stationIdLabel->setObjectName(QString::fromUtf8("stationIdLabel"));

        hardwareFormLayout->setWidget(1, QFormLayout::LabelRole, stationIdLabel);

        stationIdSpinBox = new QSpinBox(hardwareTab);
        stationIdSpinBox->setObjectName(QString::fromUtf8("stationIdSpinBox"));
        stationIdSpinBox->setMinimum(1);
        stationIdSpinBox->setMaximum(255);
        stationIdSpinBox->setValue(1);

        hardwareFormLayout->setWidget(1, QFormLayout::FieldRole, stationIdSpinBox);

        aoConfigLabel = new QLabel(hardwareTab);
        aoConfigLabel->setObjectName(QString::fromUtf8("aoConfigLabel"));

        hardwareFormLayout->setWidget(2, QFormLayout::LabelRole, aoConfigLabel);

        boardIdAoLabel = new QLabel(hardwareTab);
        boardIdAoLabel->setObjectName(QString::fromUtf8("boardIdAoLabel"));

        hardwareFormLayout->setWidget(3, QFormLayout::LabelRole, boardIdAoLabel);

        boardIdAoSpinBox = new QSpinBox(hardwareTab);
        boardIdAoSpinBox->setObjectName(QString::fromUtf8("boardIdAoSpinBox"));
        boardIdAoSpinBox->setMinimum(1);
        boardIdAoSpinBox->setMaximum(255);
        boardIdAoSpinBox->setValue(1);

        hardwareFormLayout->setWidget(3, QFormLayout::FieldRole, boardIdAoSpinBox);

        boardTypeAoLabel = new QLabel(hardwareTab);
        boardTypeAoLabel->setObjectName(QString::fromUtf8("boardTypeAoLabel"));

        hardwareFormLayout->setWidget(4, QFormLayout::LabelRole, boardTypeAoLabel);

        boardTypeAoSpinBox = new QSpinBox(hardwareTab);
        boardTypeAoSpinBox->setObjectName(QString::fromUtf8("boardTypeAoSpinBox"));
        boardTypeAoSpinBox->setMinimum(1);
        boardTypeAoSpinBox->setMaximum(255);
        boardTypeAoSpinBox->setValue(1);

        hardwareFormLayout->setWidget(4, QFormLayout::FieldRole, boardTypeAoSpinBox);

        portIdAoLabel = new QLabel(hardwareTab);
        portIdAoLabel->setObjectName(QString::fromUtf8("portIdAoLabel"));

        hardwareFormLayout->setWidget(5, QFormLayout::LabelRole, portIdAoLabel);

        portIdAoSpinBox = new QSpinBox(hardwareTab);
        portIdAoSpinBox->setObjectName(QString::fromUtf8("portIdAoSpinBox"));
        portIdAoSpinBox->setMinimum(1);
        portIdAoSpinBox->setMaximum(255);
        portIdAoSpinBox->setValue(1);

        hardwareFormLayout->setWidget(5, QFormLayout::FieldRole, portIdAoSpinBox);

        doConfigLabel = new QLabel(hardwareTab);
        doConfigLabel->setObjectName(QString::fromUtf8("doConfigLabel"));

        hardwareFormLayout->setWidget(6, QFormLayout::LabelRole, doConfigLabel);

        boardIdDoLabel = new QLabel(hardwareTab);
        boardIdDoLabel->setObjectName(QString::fromUtf8("boardIdDoLabel"));

        hardwareFormLayout->setWidget(7, QFormLayout::LabelRole, boardIdDoLabel);

        boardIdDoSpinBox = new QSpinBox(hardwareTab);
        boardIdDoSpinBox->setObjectName(QString::fromUtf8("boardIdDoSpinBox"));
        boardIdDoSpinBox->setMinimum(1);
        boardIdDoSpinBox->setMaximum(255);
        boardIdDoSpinBox->setValue(1);

        hardwareFormLayout->setWidget(7, QFormLayout::FieldRole, boardIdDoSpinBox);

        boardTypeDoLabel = new QLabel(hardwareTab);
        boardTypeDoLabel->setObjectName(QString::fromUtf8("boardTypeDoLabel"));

        hardwareFormLayout->setWidget(8, QFormLayout::LabelRole, boardTypeDoLabel);

        boardTypeDoSpinBox = new QSpinBox(hardwareTab);
        boardTypeDoSpinBox->setObjectName(QString::fromUtf8("boardTypeDoSpinBox"));
        boardTypeDoSpinBox->setMinimum(1);
        boardTypeDoSpinBox->setMaximum(255);
        boardTypeDoSpinBox->setValue(1);

        hardwareFormLayout->setWidget(8, QFormLayout::FieldRole, boardTypeDoSpinBox);

        portIdDoLabel = new QLabel(hardwareTab);
        portIdDoLabel->setObjectName(QString::fromUtf8("portIdDoLabel"));

        hardwareFormLayout->setWidget(9, QFormLayout::LabelRole, portIdDoLabel);

        portIdDoSpinBox = new QSpinBox(hardwareTab);
        portIdDoSpinBox->setObjectName(QString::fromUtf8("portIdDoSpinBox"));
        portIdDoSpinBox->setMinimum(1);
        portIdDoSpinBox->setMaximum(255);
        portIdDoSpinBox->setValue(1);

        hardwareFormLayout->setWidget(9, QFormLayout::FieldRole, portIdDoSpinBox);

        tabWidget->addTab(hardwareTab, QString());
        paramsTab = new QWidget();
        paramsTab->setObjectName(QString::fromUtf8("paramsTab"));
        paramsFormLayout = new QFormLayout(paramsTab);
        paramsFormLayout->setObjectName(QString::fromUtf8("paramsFormLayout"));
        kLabel = new QLabel(paramsTab);
        kLabel->setObjectName(QString::fromUtf8("kLabel"));

        paramsFormLayout->setWidget(0, QFormLayout::LabelRole, kLabel);

        kSpinBox = new QDoubleSpinBox(paramsTab);
        kSpinBox->setObjectName(QString::fromUtf8("kSpinBox"));
        kSpinBox->setDecimals(6);
        kSpinBox->setMinimum(0.000001000000000);
        kSpinBox->setMaximum(1000.000000000000000);
        kSpinBox->setValue(1.000000000000000);

        paramsFormLayout->setWidget(0, QFormLayout::FieldRole, kSpinBox);

        bLabel = new QLabel(paramsTab);
        bLabel->setObjectName(QString::fromUtf8("bLabel"));

        paramsFormLayout->setWidget(1, QFormLayout::LabelRole, bLabel);

        bSpinBox = new QDoubleSpinBox(paramsTab);
        bSpinBox->setObjectName(QString::fromUtf8("bSpinBox"));
        bSpinBox->setDecimals(6);
        bSpinBox->setMinimum(-1000.000000000000000);
        bSpinBox->setMaximum(1000.000000000000000);
        bSpinBox->setValue(0.000000000000000);

        paramsFormLayout->setWidget(1, QFormLayout::FieldRole, bSpinBox);

        precisionLabel = new QLabel(paramsTab);
        precisionLabel->setObjectName(QString::fromUtf8("precisionLabel"));

        paramsFormLayout->setWidget(2, QFormLayout::LabelRole, precisionLabel);

        precisionSpinBox = new QDoubleSpinBox(paramsTab);
        precisionSpinBox->setObjectName(QString::fromUtf8("precisionSpinBox"));
        precisionSpinBox->setDecimals(6);
        precisionSpinBox->setMinimum(0.000001000000000);
        precisionSpinBox->setMaximum(100.000000000000000);
        precisionSpinBox->setValue(0.100000000000000);

        paramsFormLayout->setWidget(2, QFormLayout::FieldRole, precisionSpinBox);

        polarityLabel = new QLabel(paramsTab);
        polarityLabel->setObjectName(QString::fromUtf8("polarityLabel"));

        paramsFormLayout->setWidget(3, QFormLayout::LabelRole, polarityLabel);

        polarityCombo = new QComboBox(paramsTab);
        polarityCombo->addItem(QString());
        polarityCombo->addItem(QString());
        polarityCombo->addItem(QString());
        polarityCombo->addItem(QString());
        polarityCombo->setObjectName(QString::fromUtf8("polarityCombo"));

        paramsFormLayout->setWidget(3, QFormLayout::FieldRole, polarityCombo);

        measUnitLabel = new QLabel(paramsTab);
        measUnitLabel->setObjectName(QString::fromUtf8("measUnitLabel"));

        paramsFormLayout->setWidget(4, QFormLayout::LabelRole, measUnitLabel);

        measUnitCombo = new QComboBox(paramsTab);
        measUnitCombo->addItem(QString());
        measUnitCombo->addItem(QString());
        measUnitCombo->addItem(QString());
        measUnitCombo->addItem(QString());
        measUnitCombo->setObjectName(QString::fromUtf8("measUnitCombo"));

        paramsFormLayout->setWidget(4, QFormLayout::FieldRole, measUnitCombo);

        measRangeLabel = new QLabel(paramsTab);
        measRangeLabel->setObjectName(QString::fromUtf8("measRangeLabel"));

        paramsFormLayout->setWidget(5, QFormLayout::LabelRole, measRangeLabel);

        measRangeMinLabel = new QLabel(paramsTab);
        measRangeMinLabel->setObjectName(QString::fromUtf8("measRangeMinLabel"));

        paramsFormLayout->setWidget(6, QFormLayout::LabelRole, measRangeMinLabel);

        measRangeMinSpinBox = new QDoubleSpinBox(paramsTab);
        measRangeMinSpinBox->setObjectName(QString::fromUtf8("measRangeMinSpinBox"));
        measRangeMinSpinBox->setDecimals(3);
        measRangeMinSpinBox->setMinimum(-10000.000000000000000);
        measRangeMinSpinBox->setMaximum(10000.000000000000000);
        measRangeMinSpinBox->setValue(-100.000000000000000);

        paramsFormLayout->setWidget(6, QFormLayout::FieldRole, measRangeMinSpinBox);

        measRangeMaxLabel = new QLabel(paramsTab);
        measRangeMaxLabel->setObjectName(QString::fromUtf8("measRangeMaxLabel"));

        paramsFormLayout->setWidget(7, QFormLayout::LabelRole, measRangeMaxLabel);

        measRangeMaxSpinBox = new QDoubleSpinBox(paramsTab);
        measRangeMaxSpinBox->setObjectName(QString::fromUtf8("measRangeMaxSpinBox"));
        measRangeMaxSpinBox->setDecimals(3);
        measRangeMaxSpinBox->setMinimum(-10000.000000000000000);
        measRangeMaxSpinBox->setMaximum(10000.000000000000000);
        measRangeMaxSpinBox->setValue(100.000000000000000);

        paramsFormLayout->setWidget(7, QFormLayout::FieldRole, measRangeMaxSpinBox);

        tabWidget->addTab(paramsTab, QString());
        outputTab = new QWidget();
        outputTab->setObjectName(QString::fromUtf8("outputTab"));
        outputFormLayout = new QFormLayout(outputTab);
        outputFormLayout->setObjectName(QString::fromUtf8("outputFormLayout"));
        outputSignalUnitLabel = new QLabel(outputTab);
        outputSignalUnitLabel->setObjectName(QString::fromUtf8("outputSignalUnitLabel"));

        outputFormLayout->setWidget(0, QFormLayout::LabelRole, outputSignalUnitLabel);

        outputSignalUnitSpinBox = new QSpinBox(outputTab);
        outputSignalUnitSpinBox->setObjectName(QString::fromUtf8("outputSignalUnitSpinBox"));
        outputSignalUnitSpinBox->setMinimum(1);
        outputSignalUnitSpinBox->setMaximum(10);
        outputSignalUnitSpinBox->setValue(1);

        outputFormLayout->setWidget(0, QFormLayout::FieldRole, outputSignalUnitSpinBox);

        outputRangeLabel = new QLabel(outputTab);
        outputRangeLabel->setObjectName(QString::fromUtf8("outputRangeLabel"));

        outputFormLayout->setWidget(1, QFormLayout::LabelRole, outputRangeLabel);

        outputRangeMinLabel = new QLabel(outputTab);
        outputRangeMinLabel->setObjectName(QString::fromUtf8("outputRangeMinLabel"));

        outputFormLayout->setWidget(2, QFormLayout::LabelRole, outputRangeMinLabel);

        outputRangeMinSpinBox = new QDoubleSpinBox(outputTab);
        outputRangeMinSpinBox->setObjectName(QString::fromUtf8("outputRangeMinSpinBox"));
        outputRangeMinSpinBox->setDecimals(3);
        outputRangeMinSpinBox->setMinimum(-100.000000000000000);
        outputRangeMinSpinBox->setMaximum(100.000000000000000);
        outputRangeMinSpinBox->setValue(-10.000000000000000);

        outputFormLayout->setWidget(2, QFormLayout::FieldRole, outputRangeMinSpinBox);

        outputRangeMaxLabel = new QLabel(outputTab);
        outputRangeMaxLabel->setObjectName(QString::fromUtf8("outputRangeMaxLabel"));

        outputFormLayout->setWidget(3, QFormLayout::LabelRole, outputRangeMaxLabel);

        outputRangeMaxSpinBox = new QDoubleSpinBox(outputTab);
        outputRangeMaxSpinBox->setObjectName(QString::fromUtf8("outputRangeMaxSpinBox"));
        outputRangeMaxSpinBox->setDecimals(3);
        outputRangeMaxSpinBox->setMinimum(-100.000000000000000);
        outputRangeMaxSpinBox->setMaximum(100.000000000000000);
        outputRangeMaxSpinBox->setValue(10.000000000000000);

        outputFormLayout->setWidget(3, QFormLayout::FieldRole, outputRangeMaxSpinBox);

        tabWidget->addTab(outputTab, QString());

        mainLayout->addWidget(tabWidget);

        buttonLayout = new QHBoxLayout();
        buttonLayout->setObjectName(QString::fromUtf8("buttonLayout"));
        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        buttonLayout->addItem(horizontalSpacer);

        okButton = new QPushButton(ActuatorDialog_1_2);
        okButton->setObjectName(QString::fromUtf8("okButton"));

        buttonLayout->addWidget(okButton);

        cancelButton = new QPushButton(ActuatorDialog_1_2);
        cancelButton->setObjectName(QString::fromUtf8("cancelButton"));

        buttonLayout->addWidget(cancelButton);


        mainLayout->addLayout(buttonLayout);


        retranslateUi(ActuatorDialog_1_2);
        QObject::connect(okButton, SIGNAL(clicked()), ActuatorDialog_1_2, SLOT(accept()));
        QObject::connect(cancelButton, SIGNAL(clicked()), ActuatorDialog_1_2, SLOT(reject()));

        tabWidget->setCurrentIndex(0);
        okButton->setDefault(true);


        QMetaObject::connectSlotsByName(ActuatorDialog_1_2);
    } // setupUi

    void retranslateUi(QDialog *ActuatorDialog_1_2)
    {
        ActuatorDialog_1_2->setWindowTitle(QCoreApplication::translate("ActuatorDialog_1_2", "\344\275\234\345\212\250\345\231\250\351\205\215\347\275\256 - \346\226\260\351\234\200\346\261\202\346\240\274\345\274\217", nullptr));
        titleLabel->setText(QCoreApplication::translate("ActuatorDialog_1_2", "\344\275\234\345\212\250\345\231\250\351\205\215\347\275\256 - \346\226\260\351\234\200\346\261\202\346\240\274\345\274\217", nullptr));
        nameLabel->setText(QCoreApplication::translate("ActuatorDialog_1_2", "\346\216\247\345\210\266\351\207\217\345\220\215\347\247\260:", nullptr));
        typeLabel->setText(QCoreApplication::translate("ActuatorDialog_1_2", "\344\275\234\345\212\250\345\231\250\347\261\273\345\236\213:", nullptr));
        typeCombo->setItemText(0, QCoreApplication::translate("ActuatorDialog_1_2", "\345\215\225\345\207\272\346\235\206", nullptr));
        typeCombo->setItemText(1, QCoreApplication::translate("ActuatorDialog_1_2", "\345\217\214\345\207\272\346\235\206", nullptr));

        zeroOffsetLabel->setText(QCoreApplication::translate("ActuatorDialog_1_2", "\351\233\266\345\201\217:", nullptr));
        serialLabel->setText(QCoreApplication::translate("ActuatorDialog_1_2", "\345\272\217\345\210\227\345\217\267:", nullptr));
        modelLabel->setText(QCoreApplication::translate("ActuatorDialog_1_2", "\345\236\213\345\217\267:", nullptr));
        modelEdit->setText(QCoreApplication::translate("ActuatorDialog_1_2", "MD500", nullptr));
        tabWidget->setTabText(tabWidget->indexOf(basicTab), QCoreApplication::translate("ActuatorDialog_1_2", "\345\237\272\346\234\254\344\277\241\346\201\257", nullptr));
        lcIdLabel->setText(QCoreApplication::translate("ActuatorDialog_1_2", "\344\270\213\344\275\215\346\234\272ID:", nullptr));
        stationIdLabel->setText(QCoreApplication::translate("ActuatorDialog_1_2", "\347\253\231\347\202\271ID:", nullptr));
        aoConfigLabel->setText(QCoreApplication::translate("ActuatorDialog_1_2", "AO\346\235\277\345\215\241\351\205\215\347\275\256:", nullptr));
        boardIdAoLabel->setText(QCoreApplication::translate("ActuatorDialog_1_2", "  \346\235\277\345\215\241ID:", nullptr));
        boardTypeAoLabel->setText(QCoreApplication::translate("ActuatorDialog_1_2", "  \346\235\277\345\215\241\347\261\273\345\236\213:", nullptr));
        portIdAoLabel->setText(QCoreApplication::translate("ActuatorDialog_1_2", "  \347\253\257\345\217\243ID:", nullptr));
        doConfigLabel->setText(QCoreApplication::translate("ActuatorDialog_1_2", "DO\346\235\277\345\215\241\351\205\215\347\275\256:", nullptr));
        boardIdDoLabel->setText(QCoreApplication::translate("ActuatorDialog_1_2", "  \346\235\277\345\215\241ID:", nullptr));
        boardTypeDoLabel->setText(QCoreApplication::translate("ActuatorDialog_1_2", "  \346\235\277\345\215\241\347\261\273\345\236\213:", nullptr));
        portIdDoLabel->setText(QCoreApplication::translate("ActuatorDialog_1_2", "  \347\253\257\345\217\243ID:", nullptr));
        tabWidget->setTabText(tabWidget->indexOf(hardwareTab), QCoreApplication::translate("ActuatorDialog_1_2", "\347\241\254\344\273\266\351\205\215\347\275\256", nullptr));
        kLabel->setText(QCoreApplication::translate("ActuatorDialog_1_2", "K\347\263\273\346\225\260:", nullptr));
        bLabel->setText(QCoreApplication::translate("ActuatorDialog_1_2", "B\347\263\273\346\225\260:", nullptr));
        precisionLabel->setText(QCoreApplication::translate("ActuatorDialog_1_2", "\347\262\276\345\272\246:", nullptr));
        polarityLabel->setText(QCoreApplication::translate("ActuatorDialog_1_2", "\346\236\201\346\200\247:", nullptr));
        polarityCombo->setItemText(0, QCoreApplication::translate("ActuatorDialog_1_2", "\346\255\243\346\236\201\346\200\247 (+)", nullptr));
        polarityCombo->setItemText(1, QCoreApplication::translate("ActuatorDialog_1_2", "\350\264\237\346\236\201\346\200\247 (-)", nullptr));
        polarityCombo->setItemText(2, QCoreApplication::translate("ActuatorDialog_1_2", "\345\217\214\346\236\201\346\200\247 (\302\261)", nullptr));
        polarityCombo->setItemText(3, QCoreApplication::translate("ActuatorDialog_1_2", "\346\227\240\346\236\201\346\200\247", nullptr));

        measUnitLabel->setText(QCoreApplication::translate("ActuatorDialog_1_2", "\346\265\213\351\207\217\345\215\225\344\275\215:", nullptr));
        measUnitCombo->setItemText(0, QCoreApplication::translate("ActuatorDialog_1_2", "mm", nullptr));
        measUnitCombo->setItemText(1, QCoreApplication::translate("ActuatorDialog_1_2", "m", nullptr));
        measUnitCombo->setItemText(2, QCoreApplication::translate("ActuatorDialog_1_2", "cm", nullptr));
        measUnitCombo->setItemText(3, QCoreApplication::translate("ActuatorDialog_1_2", "inch", nullptr));

        measRangeLabel->setText(QCoreApplication::translate("ActuatorDialog_1_2", "\346\265\213\351\207\217\350\214\203\345\233\264:", nullptr));
        measRangeMinLabel->setText(QCoreApplication::translate("ActuatorDialog_1_2", "  \344\270\213\351\231\220:", nullptr));
        measRangeMaxLabel->setText(QCoreApplication::translate("ActuatorDialog_1_2", "  \344\270\212\351\231\220:", nullptr));
        tabWidget->setTabText(tabWidget->indexOf(paramsTab), QCoreApplication::translate("ActuatorDialog_1_2", "\350\257\246\347\273\206\345\217\202\346\225\260", nullptr));
        outputSignalUnitLabel->setText(QCoreApplication::translate("ActuatorDialog_1_2", "\350\276\223\345\207\272\344\277\241\345\217\267\345\215\225\344\275\215:", nullptr));
        outputRangeLabel->setText(QCoreApplication::translate("ActuatorDialog_1_2", "\350\276\223\345\207\272\344\277\241\345\217\267\350\214\203\345\233\264:", nullptr));
        outputRangeMinLabel->setText(QCoreApplication::translate("ActuatorDialog_1_2", "  \344\270\213\351\231\220:", nullptr));
        outputRangeMaxLabel->setText(QCoreApplication::translate("ActuatorDialog_1_2", "  \344\270\212\351\231\220:", nullptr));
        tabWidget->setTabText(tabWidget->indexOf(outputTab), QCoreApplication::translate("ActuatorDialog_1_2", "\350\276\223\345\207\272\344\277\241\345\217\267", nullptr));
        okButton->setText(QCoreApplication::translate("ActuatorDialog_1_2", "\347\241\256\345\256\232", nullptr));
        cancelButton->setText(QCoreApplication::translate("ActuatorDialog_1_2", "\345\217\226\346\266\210", nullptr));
    } // retranslateUi

};

namespace Ui {
    class ActuatorDialog_1_2: public Ui_ActuatorDialog_1_2 {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_ACTUATORDIALOG_1_2_H
