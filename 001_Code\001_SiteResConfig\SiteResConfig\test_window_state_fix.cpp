#include <QApplication>
#include <QMainWindow>
#include <QTreeWidget>
#include <QVBoxLayout>
#include <QWidget>
#include <QPushButton>
#include <QMessageBox>
#include <QDebug>

// 模拟主窗口类
class MockMainWindow : public QMainWindow {
    Q_OBJECT
    
public:
    MockMainWindow(QWidget *parent = nullptr) : QMainWindow(parent) {
        setWindowTitle("窗口状态修复测试");
        resize(800, 600);
        
        // 创建中心部件
        QWidget *centralWidget = new QWidget(this);
        setCentralWidget(centralWidget);
        
        QVBoxLayout *layout = new QVBoxLayout(centralWidget);
        
        // 创建树形控件
        m_treeWidget = new QTreeWidget(this);
        m_treeWidget->setHeaderLabels({"名称", "类型", "下位机ID", "站点ID", "状态", "极性"});
        layout->addWidget(m_treeWidget);
        
        // 创建测试按钮
        QPushButton *testButton = new QPushButton("测试窗口状态", this);
        layout->addWidget(testButton);
        
        // 连接信号
        connect(testButton, &QPushButton::clicked, this, &MockMainWindow::testWindowState);
        connect(m_treeWidget, &QTreeWidget::itemClicked, this, &MockMainWindow::onItemClicked);
        
        // 初始化树形控件数据
        setupTreeData();
    }
    
private slots:
    void testWindowState() {
        // 测试窗口最大化
        showMaximized();
        QMessageBox::information(this, "测试", "窗口已最大化，现在点击树形控件节点测试修复效果");
    }
    
    void onItemClicked(QTreeWidgetItem *item, int column) {
        Q_UNUSED(column)
        
        if (!item) return;
        
        QString itemText = item->text(0);
        QString itemType = item->text(1);
        
        qDebug() << "点击节点:" << itemText << "类型:" << itemType;
        
        // 模拟修复后的逻辑
        if (itemText == "控制通道") {
            qDebug() << "🎯 检测到控制通道根节点，显示控制通道组信息";
            showControlChannelGroupInfo();
        } else if (itemText.startsWith("CH")) {
            qDebug() << "🎛️ 检测到控制通道子节点:" << itemText << "，显示通道详细信息";
            showControlChannelDetailInfo(itemText);
        } else if (item->parent() && item->parent()->text(0).startsWith("CH")) {
            QString parentChannelName = item->parent()->text(0);
            qDebug() << "🎯 检测到控制通道子节点:" << itemText << "，父通道:" << parentChannelName;
            qDebug() << "调用主窗口显示父通道信息";
            showControlChannelDetailInfo(parentChannelName);
        } else {
            qDebug() << "📋 其他类型节点，使用默认处理";
            if (itemText.contains("传感器") || itemText.contains("作动器")) {
                qDebug() << "对于传感器和作动器节点，显示基本信息";
                showControlChannelDetailInfo(itemText);
            } else {
                qDebug() << "跳过未知节点类型，避免窗口状态变化";
            }
        }
        
        // 检查窗口状态
        if (isMaximized()) {
            qDebug() << "✅ 窗口仍然保持最大化状态";
        } else {
            qDebug() << "❌ 窗口状态已改变，修复失败";
        }
    }
    
private:
    void setupTreeData() {
        // 创建实验节点
        QTreeWidgetItem *experimentItem = new QTreeWidgetItem(m_treeWidget);
        experimentItem->setText(0, "实验");
        experimentItem->setText(1, "试验节点");
        
        // 创建控制通道节点
        QTreeWidgetItem *controlChannelItem = new QTreeWidgetItem(experimentItem);
        controlChannelItem->setText(0, "控制通道");
        controlChannelItem->setText(1, "控制通道");
        
        // 创建CH1节点
        QTreeWidgetItem *ch1Item = new QTreeWidgetItem(controlChannelItem);
        ch1Item->setText(0, "CH1");
        ch1Item->setText(1, "控制通道");
        ch1Item->setText(2, "1");
        ch1Item->setText(3, "1");
        ch1Item->setText(4, "启用");
        ch1Item->setText(5, "正极性");
        
        // 创建CH1的子节点
        QTreeWidgetItem *load1Item = new QTreeWidgetItem(ch1Item);
        load1Item->setText(0, "载荷1");
        load1Item->setText(1, "载荷传感器");
        
        QTreeWidgetItem *load2Item = new QTreeWidgetItem(ch1Item);
        load2Item->setText(0, "载荷2");
        load2Item->setText(1, "载荷传感器");
        
        QTreeWidgetItem *positionItem = new QTreeWidgetItem(ch1Item);
        positionItem->setText(0, "位置");
        positionItem->setText(1, "位置传感器");
        
        QTreeWidgetItem *controlItem = new QTreeWidgetItem(ch1Item);
        controlItem->setText(0, "控制");
        controlItem->setText(1, "控制作动器");
        
        // 创建CH2节点
        QTreeWidgetItem *ch2Item = new QTreeWidgetItem(controlChannelItem);
        ch2Item->setText(0, "CH2");
        ch2Item->setText(1, "控制通道");
        ch2Item->setText(2, "2");
        ch2Item->setText(3, "2");
        ch2Item->setText(4, "启用");
        ch2Item->setText(5, "正极性");
        
        // 创建CH2的子节点
        QTreeWidgetItem *load1Item2 = new QTreeWidgetItem(ch2Item);
        load1Item2->setText(0, "载荷1");
        load1Item2->setText(1, "载荷传感器");
        
        QTreeWidgetItem *load2Item2 = new QTreeWidgetItem(ch2Item);
        load2Item2->setText(0, "载荷2");
        load2Item2->setText(1, "载荷传感器");
        
        QTreeWidgetItem *positionItem2 = new QTreeWidgetItem(ch2Item);
        positionItem2->setText(0, "位置");
        positionItem2->setText(1, "位置传感器");
        
        QTreeWidgetItem *controlItem2 = new QTreeWidgetItem(ch2Item);
        controlItem2->setText(0, "控制");
        controlItem2->setText(1, "控制作动器");
        
        // 展开所有节点
        m_treeWidget->expandAll();
    }
    
    void showControlChannelGroupInfo() {
        qDebug() << "显示控制通道组汇总信息";
        // 这里应该调用实际的详细信息显示方法
    }
    
    void showControlChannelDetailInfo(const QString &channelName) {
        qDebug() << "显示控制通道详细信息:" << channelName;
        // 这里应该调用实际的详细信息显示方法
    }
    
    QTreeWidget *m_treeWidget;
};

int main(int argc, char *argv[]) {
    QApplication app(argc, argv);
    
    MockMainWindow window;
    window.show();
    
    return app.exec();
}

#include "test_window_state_fix.moc" 