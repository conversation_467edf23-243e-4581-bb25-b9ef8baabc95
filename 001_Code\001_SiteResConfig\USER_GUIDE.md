# 📖 SiteResConfig 用户使用指南

## 🚀 **快速开始**

### **1. 编译运行软件**
```batch
# 双击运行编译脚本
compile_config_version.bat
```

### **2. 启动软件**
编译成功后，软件会自动启动，显示现代化的Qt图形界面。

## 🎯 **主要功能**

### **配置管理**
- ✅ **加载配置** - 从文件导入或手动配置硬件
- ✅ **清空配置** - 清除当前所有配置
- ✅ **配置验证** - 自动验证配置参数的合法性

### **数据管理**
- ✅ **实时数据采集** - 20Hz频率的模拟数据生成
- ✅ **数据导出** - CSV格式的完整数据导出
- ✅ **数据统计** - 实时显示数据行数和采集状态

### **试验控制**
- ✅ **试验流程** - 完整的开始/暂停/停止控制
- ✅ **参数设置** - PID参数和安全限制配置
- ✅ **状态监控** - 实时显示系统和试验状态

## 📁 **配置文件使用**

### **方法1: 使用示例配置文件**

软件提供了三种格式的示例配置文件：

#### **JSON格式示例**
```
SiteResConfig/sample_configs/hardware_config.json
```
- 包含3个硬件节点
- 4个作动器（液压缸、电动缸、旋转电机）
- 6个传感器（力、位移、压力、温度、应变）
- 2个加载通道配置

#### **XML格式示例**
```
SiteResConfig/sample_configs/hardware_config.xml
```
- 包含2个硬件节点
- 3个作动器配置
- 4个传感器配置
- 2个加载通道

#### **CSV格式示例**
```
SiteResConfig/sample_configs/hardware_config.csv
```
- 简单的表格格式
- 15行设备配置数据
- 包含节点、作动器、传感器信息

### **方法2: 创建自定义配置文件**

#### **JSON格式模板**
```json
{
  "hardware_nodes": [
    {
      "name": "节点名称",
      "ip_address": "*************",
      "channel_count": 8
    }
  ],
  "actuators": [
    {
      "name": "作动器名称",
      "max_force": 100000,
      "type": "Hydraulic"
    }
  ],
  "sensors": [
    {
      "name": "传感器名称",
      "full_scale": 100000,
      "unit": "N",
      "type": "Force"
    }
  ]
}
```

#### **CSV格式模板**
```csv
设备名称,设备类型,参数1,参数2,参数3,备注
控制器1,节点,*************,8通道,10000Hz,主控制器
液压缸1,作动器,100kN,200mm,300mm/s,主加载设备
力传感器1,传感器,150kN,±0.1%,2mV/V,高精度测量
```

## 🎮 **操作流程**

### **步骤1: 加载配置**
1. 点击 **"加载配置"** 按钮
2. 选择配置方式：
   - **"Yes"** - 从文件导入配置
   - **"No"** - 手动配置硬件

#### **从文件导入**
1. 选择配置文件（JSON/XML/CSV）
2. 系统自动解析文件内容
3. 在硬件树中显示配置结果

#### **手动配置**
1. 设置硬件节点数量（1-10个）
2. 设置每节点通道数（1-16个）
3. 设置作动器数量（1-20个）
4. 设置最大力值（1-1000kN）
5. 设置传感器数量（1-50个）
6. 点击"确定"完成配置

### **步骤2: 配置通道**
1. 在左侧"试验配置"标签页
2. 点击 **"添加通道"** 按钮
3. 输入通道名称
4. 选择通道进行配置：
   - 最大力值设置
   - 最大速度设置
   - 控制模式选择

### **步骤3: 开始试验**
1. 确保配置已加载（状态显示"已配置"）
2. 点击 **"开始试验"** 按钮
3. 确认开始试验
4. 系统自动开始数据采集

### **步骤4: 监控数据**
1. 切换到 **"实时数据"** 标签页
2. 查看实时数据表格（8列数据）
3. 观察数据采集状态和行数统计

### **步骤5: 导出数据**
1. 在"实时数据"标签页
2. 点击 **"导出数据"** 按钮
3. 选择保存位置和文件名
4. 等待导出完成

## 🔧 **高级功能**

### **PID参数设置**
1. 在硬件资源标签页
2. 点击 **"PID"** 按钮
3. 设置比例、积分、微分系数
4. 点击"设置"应用参数

### **安全限制设置**
1. 在硬件资源标签页
2. 点击 **"安全"** 按钮
3. 设置最大力值、位移、速度限制
4. 点击"设置"应用限制

### **手动指令控制**
1. 在系统概览标签页
2. 点击 **"发送指令"** 按钮
3. 输入指令值
4. 确认发送

## 📊 **数据分析**

### **实时数据表格说明**
- **时间** - 数据采集时间戳
- **节点** - 硬件节点ID
- **通道** - 通道编号
- **指令值** - 发送给硬件的指令
- **反馈值** - 硬件返回的实际值
- **位移(mm)** - 位移传感器数据
- **载荷(N)** - 力传感器数据
- **状态** - 通道运行状态

### **数据导出格式**
导出的CSV文件包含所有表格数据，可用于：
- Excel分析
- MATLAB处理
- Python数据分析
- 其他数据分析工具

## 🎯 **使用技巧**

### **配置文件技巧**
1. **JSON格式** - 适合复杂的结构化配置
2. **XML格式** - 适合标准化的配置交换
3. **CSV格式** - 适合简单的设备列表

### **性能优化**
1. **数据采集** - 系统自动限制表格行数防止内存溢出
2. **文件导出** - 大数据量导出时显示进度条
3. **界面响应** - 长时间操作使用异步处理

### **故障排除**
1. **配置加载失败** - 检查文件格式和内容
2. **试验无法开始** - 确保已加载配置
3. **数据导出失败** - 检查磁盘空间和权限

## 🎉 **开始使用**

现在您已经了解了所有功能，可以：

1. **运行编译脚本** - `compile_config_version.bat`
2. **尝试示例配置** - 使用提供的示例文件
3. **创建自定义配置** - 根据实际需求配置
4. **体验完整流程** - 从配置到数据导出

**享受使用SiteResConfig配置文件版本！** 🚀
