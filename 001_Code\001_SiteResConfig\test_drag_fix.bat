@echo off
echo ========================================
echo 测试拖拽关联信息动态生成修复
echo ========================================

cd SiteResConfig

echo [1/2] 编译项目...
qmake SiteResConfig_Simple.pro >nul 2>&1
mingw32-make -j4

if %ERRORLEVEL% EQU 0 (
    echo ✅ 编译成功！
    echo.
    echo 🎯 修复说明：
    echo - 修改拖拽数据格式，包含父节点信息
    echo - 新格式：通道名^|类型^|父节点名
    echo - 例如："CH1^|硬件节点通道^|LD-B1"
    echo.
    echo 📋 测试步骤：
    echo 1. 创建硬件节点 LD-B1 和 LD-B2
    echo 2. 拖拽 LD-B1 的 CH1 到试验配置 → 应显示 "LD-B1 - CH1"
    echo 3. 拖拽 LD-B2 的 CH1 到试验配置 → 应显示 "LD-B2 - CH1"
    echo 4. 验证关联信息是否正确动态生成
    echo.
    echo [2/2] 启动应用...
    start "" "debug\SiteResConfig.exe"
) else (
    echo ❌ 编译失败！
)

pause
