@echo off
echo Compiling Qt 5.14 Fix...

set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

cd /d "%~dp0\SiteResConfig"

mingw32-make clean
qmake SiteResConfig_Simple.pro -spec win32-g++
mingw32-make -j4

if errorlevel 1 (
    echo Compilation FAILED!
) else (
    echo Compilation SUCCESS!
    echo Starting application...
    start "" "debug\SiteResConfig.exe"
)

pause
