/**
 * @file test_json_export_with_memory_data.cpp
 * @brief 测试JSON导出功能是否正确使用了来自内存数据管理器的CH1、CH2数据
 */

#include <QCoreApplication>
#include <QDebug>
#include <QFile>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include "CtrlChanDataManager.h"
#include "JSONDataExporter_1_2.h"

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "=== 测试JSON导出功能使用内存数据 ===";
    
    // 创建控制通道数据管理器
    CtrlChanDataManager ctrlChanManager;
    
    // 创建测试控制通道组
    UI::ControlChannelGroup testGroup;
    testGroup.groupId = 1;
    testGroup.groupName = "测试控制通道组";
    testGroup.groupType = "控制通道";
    testGroup.createTime = "2025-08-25 10:00:00";
    testGroup.groupNotes = "测试用控制通道组";
    
    // 添加CH1通道
    UI::ControlChannelParams ch1;
    ch1.channelId = "CH1";
    ch1.channelName = "CH1";
    ch1.hardwareAssociation = "LD-B1 - CH1";
    ch1.load1Sensor = "载荷传感器1";
    ch1.load2Sensor = "载荷传感器2";
    ch1.positionSensor = "位置传感器";
    ch1.controlActuator = "加载电机";
    ch1.notes = "CH1测试通道";
    testGroup.channels.push_back(ch1);
    
    // 添加CH2通道
    UI::ControlChannelParams ch2;
    ch2.channelId = "CH2";
    ch2.channelName = "CH2";
    ch2.hardwareAssociation = "LD-B2 - CH2";
    ch2.load1Sensor = "载荷传感器1";
    ch2.load2Sensor = "载荷传感器2";
    ch2.positionSensor = "位置传感器";
    ch2.controlActuator = "位移电机";
    ch2.notes = "CH2测试通道";
    testGroup.channels.push_back(ch2);
    
    // 创建控制通道组
    if (ctrlChanManager.createControlChannelGroup(testGroup)) {
        qDebug() << "✅ 控制通道组创建成功";
    } else {
        qDebug() << "❌ 控制通道组创建失败";
        return -1;
    }
    
    // 验证控制通道组数据
    auto groups = ctrlChanManager.getAllControlChannelGroups();
    qDebug() << QString("📊 获取到 %1 个控制通道组").arg(groups.size());
    
    // 创建JSON导出器
    JSONDataExporter_1_2 jsonExporter;
    
    // 设置控制通道数据管理器
    jsonExporter.setCtrlChanDataManager(&ctrlChanManager);
    
    // 导出到JSON文件
    QString testFilePath = "test_export_from_memory.json";
    if (jsonExporter.exportToJSON_1_2(testFilePath)) {
        qDebug() << "✅ JSON导出成功:" << testFilePath;
        
        // 读取并验证导出的JSON文件
        QFile file(testFilePath);
        if (file.open(QIODevice::ReadOnly)) {
            QByteArray jsonData = file.readAll();
            QJsonDocument doc = QJsonDocument::fromJson(jsonData);
            
            if (!doc.isNull() && doc.isObject()) {
                QJsonObject rootObj = doc.object();
                if (rootObj.contains("通道配置数据") && rootObj["通道配置数据"].isArray()) {
                    QJsonArray channelArray = rootObj["通道配置数据"].toArray();
                    qDebug() << QString("📋 导出的通道数: %1").arg(channelArray.size());
                    
                    for (int i = 0; i < channelArray.size(); ++i) {
                        QJsonObject channelObj = channelArray[i].toObject();
                        qDebug() << QString("   通道%1 - ID: %2, 名称: %3")
                                    .arg(i+1)
                                    .arg(channelObj["通道ID"].toString())
                                    .arg(channelObj["通道名称"].toString());
                    }
                    
                    qDebug() << "✅ JSON文件内容验证通过";
                } else {
                    qDebug() << "❌ JSON文件格式不正确：缺少通道配置数据";
                }
            } else {
                qDebug() << "❌ JSON文件解析失败";
            }
            file.close();
        } else {
            qDebug() << "❌ 无法读取导出的JSON文件";
        }
    } else {
        qDebug() << "❌ JSON导出失败";
        return -1;
    }
    
    qDebug() << "✅ 测试完成，JSON导出功能已正确使用内存数据中的CH1、CH2配置";
    
    return 0;
}