@echo off
echo.
echo ========================================
echo Actuator1_1 Data Specifications Test
echo ========================================
echo.

echo Data specifications implemented:
echo.
echo 1. Actuator Type:
echo    - 1 = Single Rod (单出杆)
echo    - 2 = Double Rod (双出杆)
echo.
echo 2. Serial Number:
echo    - Only letters and numbers allowed
echo    - Regex validation: ^[A-Za-z0-9]+$
echo    - Examples: ABC123, SN001, A1B2C3
echo.
echo 3. Measurement Unit:
echo    - 1 = m (meter)
echo    - 2 = mm (millimeter)
echo    - 3 = cm (centimeter)
echo    - 4 = inch
echo.
echo 4. Polarity:
echo    - 1 = Positive
echo    - -1 = Negative
echo    - 9 = Both
echo    - 0 = Unknown
echo.

echo Helper functions added:
echo - getActuatorTypeText1_1()
echo - getPolarityText1_1()
echo - getMeasurementUnitText1_1()
echo - isValidSerialNumber1_1()
echo - getActuatorTypeOptions1_1()
echo - getPolarityOptions1_1()
echo - getMeasurementUnitOptions1_1()
echo.

if exist "SiteResConfig\SiteResConfig_Simple.pro" (
    echo Found project file, testing compilation...
    echo.
    
    cd SiteResConfig
    
    echo Cleaning old files...
    if exist "Makefile" del /Q "Makefile" 2>nul
    if exist "debug" rmdir /S /Q "debug" 2>nul
    if exist "release" rmdir /S /Q "release" 2>nul
    if exist "ui_*.h" del /Q "ui_*.h" 2>nul
    
    echo.
    echo Running qmake...
    qmake SiteResConfig_Simple.pro -spec win32-g++ "CONFIG+=debug" 2>&1
    
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo qmake successful!
        echo.
        echo Starting compilation and linking...
        mingw32-make debug 2>&1
        
        if %ERRORLEVEL% EQU 0 (
            echo.
            echo *** COMPILATION SUCCESSFUL! ***
            echo.
            echo Data specifications implementation completed!
            echo All enums and helper functions are ready.
            echo.
            
            if exist "debug\SiteResConfig.exe" (
                echo Executable created successfully!
                echo.
                echo Data specifications ready for use:
                echo 1. Actuator type dropdown with 2 options
                echo 2. Serial number validation (letters/numbers only)
                echo 3. Measurement unit dropdown with 4 options
                echo 4. Polarity dropdown with 4 options
                echo 5. Helper functions for text conversion
                echo 6. Validation functions for data integrity
                echo.
                
                set /p choice="Launch program to test data specifications? (y/n): "
                if /i "%choice%"=="y" (
                    echo Launching program...
                    start "" "debug\SiteResConfig.exe"
                    echo.
                    echo Test the following data specifications:
                    echo [ ] Create actuator -^> Test type dropdown (单出杆/双出杆)
                    echo [ ] Enter serial number -^> Test validation (ABC123 valid, ABC-123 invalid)
                    echo [ ] Test measurement unit dropdown (m/mm/cm/inch)
                    echo [ ] Test polarity dropdown (Positive/Negative/Both/Unknown)
                    echo [ ] Verify data is saved correctly with proper values
                    echo [ ] Check tooltips show correct information
                )
            ) else (
                echo ERROR: Executable not found
            )
        ) else (
            echo.
            echo *** COMPILATION FAILED ***
            echo Please check the error messages above.
        )
    ) else (
        echo.
        echo *** QMAKE FAILED ***
        echo Please check Qt environment configuration.
    )
    
    cd ..
) else (
    echo ERROR: Project file not found
)

echo.
echo ========================================
echo Data Specifications Test Completed
echo ========================================
echo.
pause
