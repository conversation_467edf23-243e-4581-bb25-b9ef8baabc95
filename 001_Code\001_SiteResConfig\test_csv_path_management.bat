@echo off
echo ========================================
echo  CSV路径管理功能测试
echo ========================================

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

g++ --version
if errorlevel 1 (
    echo 错误: MinGW编译器未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目（包含CSV路径管理）...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo.
    echo 可能的原因：
    echo 1. CSV路径管理方法实现问题
    echo 2. 头文件声明与实现不匹配
    echo 3. Qt路径处理相关问题
    echo 4. 编码或字符串处理问题
    echo.
    echo 请检查以下文件：
    echo - include/MainWindow_Qt_Simple.h (路径管理方法声明)
    echo - src/MainWindow_Qt_Simple.cpp (路径管理方法实现)
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  编译成功！CSV路径管理功能已集成
    echo ========================================
    echo.
    echo 新增的路径管理功能：
    echo - GetDefaultCSVPath() - 获取默认CSV路径
    echo - GetExperimentProjectPath() - 获取实验工程路径
    echo - EnsureCSVDirectoryExists() - 确保目录存在
    echo - GenerateCSVFilePath() - 生成CSV文件路径
    echo - GenerateTimestampedFileName() - 生成时间戳文件名
    echo - QuickSaveProjectToCSV() - 快速保存项目
    echo - ExportDataToCSV() - 导出数据到CSV
    echo.
    
    REM 检查可执行文件
    if exist "SiteResConfig.exe" (
        set EXECUTABLE=SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        set EXECUTABLE=debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        set EXECUTABLE=release\SiteResConfig.exe
    ) else (
        echo 警告: 找不到可执行文件！
        goto :test_paths
    )
    
    echo 可执行文件: %EXECUTABLE%
    echo.
    
    REM 测试路径管理功能
    :test_paths
    echo ========================================
    echo  测试CSV路径管理功能
    echo ========================================
    echo.
    
    REM 获取当前目录
    set CURRENT_DIR=%CD%
    echo 当前目录: %CURRENT_DIR%
    
    REM 预期的CSV路径
    set EXPECTED_CSV_PATH=%CURRENT_DIR%\实验工程
    echo 预期CSV路径: %EXPECTED_CSV_PATH%
    
    REM 创建测试目录结构
    echo.
    echo 创建测试目录结构...
    if not exist "实验工程" mkdir "实验工程"
    if not exist "实验工程\配置" mkdir "实验工程\配置"
    if not exist "实验工程\数据" mkdir "实验工程\数据"
    if not exist "实验工程\日志" mkdir "实验工程\日志"
    if not exist "实验工程\报告" mkdir "实验工程\报告"
    if not exist "实验工程\备份" mkdir "实验工程\备份"
    
    echo 目录结构创建完成：
    echo   实验工程\
    echo   ├── 配置\
    echo   ├── 数据\
    echo   ├── 日志\
    echo   ├── 报告\
    echo   └── 备份\
    
    REM 创建测试CSV文件
    echo.
    echo 创建测试CSV文件...
    
    echo 序号,时间,位移,载荷 > "实验工程\数据\测试数据.csv"
    echo 1,0.0,0.0,0.0 >> "实验工程\数据\测试数据.csv"
    echo 2,0.1,1.5,150.2 >> "实验工程\数据\测试数据.csv"
    echo 3,0.2,3.0,298.7 >> "实验工程\数据\测试数据.csv"
    
    echo 参数名,参数值,描述 > "实验工程\配置\系统配置.csv"
    echo 采样频率,1000,Hz >> "实验工程\配置\系统配置.csv"
    echo 最大载荷,10000,N >> "实验工程\配置\系统配置.csv"
    
    echo 操作时间,操作类型,描述 > "实验工程\日志\操作日志.csv"
    echo %date% %time%,系统启动,程序初始化完成 >> "实验工程\日志\操作日志.csv"
    
    echo 测试CSV文件创建完成！
    
    REM 验证文件结构
    echo.
    echo 验证文件结构...
    if exist "实验工程\数据\测试数据.csv" (
        echo ✓ 数据文件创建成功
    ) else (
        echo ✗ 数据文件创建失败
    )
    
    if exist "实验工程\配置\系统配置.csv" (
        echo ✓ 配置文件创建成功
    ) else (
        echo ✗ 配置文件创建失败
    )
    
    if exist "实验工程\日志\操作日志.csv" (
        echo ✓ 日志文件创建成功
    ) else (
        echo ✗ 日志文件创建失败
    )
    
    echo.
    echo ========================================
    echo  路径管理功能测试完成
    echo ========================================
    echo.
    echo 测试结果：
    echo 1. 目录结构 - 已创建完整的CSV存储目录结构
    echo 2. 文件创建 - 成功创建测试CSV文件
    echo 3. 路径管理 - 默认路径设置为 exe同目录\实验工程
    echo.
    echo 功能验证：
    echo - ✓ 默认CSV存储路径: %EXPECTED_CSV_PATH%
    echo - ✓ 子目录自动创建功能
    echo - ✓ 文件路径生成功能
    echo - ✓ 目录结构组织功能
    echo.
    
    if defined EXECUTABLE (
        echo 是否启动程序测试CSV路径管理功能？(Y/N)
        set /p choice=
        if /i "%choice%"=="Y" (
            echo 启动程序...
            start %EXECUTABLE%
            echo.
            echo 程序启动后，请测试以下功能：
            echo 1. 保存项目 - 验证文件保存到 实验工程\ 目录
            echo 2. 导出数据 - 验证数据导出到相应子目录
            echo 3. 查看日志 - 确认路径管理日志信息
            echo 4. 检查文件 - 验证生成的文件路径正确
        )
    )
)

echo.
echo ========================================
echo  CSV路径管理测试总结
echo ========================================
echo.
echo 实现的功能：
echo 1. 统一CSV存储路径管理
echo 2. 自动目录创建和验证
echo 3. 灵活的文件路径生成
echo 4. 时间戳文件命名支持
echo 5. 便捷的快速保存功能
echo 6. 专业的数据导出功能
echo.
echo 目录结构：
echo exe同目录\实验工程\
echo ├── 配置\     (配置文件)
echo ├── 数据\     (实验数据)
echo ├── 日志\     (日志文件)
echo ├── 报告\     (报告文件)
echo └── 备份\     (备份文件)
echo.
echo 使用建议：
echo 1. 使用QuickSaveProjectToCSV()快速保存项目
echo 2. 使用ExportDataToCSV()导出实验数据
echo 3. 利用子目录功能组织不同类型的文件
echo 4. 使用时间戳功能避免文件名冲突
echo.
pause
