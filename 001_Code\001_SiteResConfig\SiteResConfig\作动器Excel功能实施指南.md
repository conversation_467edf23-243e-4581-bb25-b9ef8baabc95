# 📊 作动器Excel功能实施指南

## 🎯 实施概述

本指南详细说明如何在您的Excel文件中添加作动器数据，使用标准的17列格式，并测试系统的导入功能。

## ✅ 第一步：在Excel文件中创建"作动器"工作表

### 1.1 打开现有Excel文件
- 打开您当前的Excel文件（包含传感器数据的文件）
- 或创建新的Excel文件

### 1.2 创建新工作表
1. 右键点击工作表标签
2. 选择"插入工作表"
3. 将工作表命名为：**作动器**

## 📋 第二步：使用标准17列格式添加作动器数据

### 2.1 表头信息区域 (第1-4行)

在新创建的"作动器"工作表中，按以下格式填入：

```
A1: 作动器配置数据表
A2: 导出时间: 2025-08-14 15:30:00
A3: 说明: 包含作动器组及其作动器的完整配置信息
A4: (空行)
```

### 2.2 数据表头 (第5行) - 17列完整格式

在第5行填入以下表头：

| 列 | A | B | C | D | E | F | G | H | I |
|---|---|---|---|---|---|---|---|---|---|
| **表头** | 组序号 | 作动器组名称 | 作动器序号 | 作动器序列号 | 作动器类型 | Unit类型 | Unit名称 | 行程(m) | 位移(m) |

| 列 | J | K | L | M | N | O | P | Q |
|---|---|---|---|---|---|---|---|---|
| **表头** | 拉伸面积(m²) | 压缩面积(m²) | 极性 | Deliver(V) | 频率(Hz) | 输出倍数 | 平衡(V) | 备注 |

### 2.3 示例数据 (第6-9行)

复制以下示例数据到Excel中：

#### 第6行 (第一个作动器组的第一个作动器)
```
A6: 1
B6: 主作动器组
C6: 1
D6: ACT001
E6: 单出杆
F6: m
G6: 米
H6: 0.15
I6: 0.05
J6: 0.0314
K6: 0.0254
L6: Positive
M6: 5.0
N6: 50.0
O6: 1.0
P6: 2.5
Q6: 主控制作动器
```

#### 第7行 (第一个作动器组的第二个作动器)
```
A7: 1
B7: (空白)
C7: 2
D7: ACT002
E7: 单出杆
F7: m
G7: 米
H7: 0.15
I7: 0.05
J7: 0.0314
K7: 0.0254
L7: Positive
M7: 5.0
N7: 50.0
O7: 1.0
P7: 2.5
Q7: 备用作动器
```

#### 第8行 (第二个作动器组的第一个作动器)
```
A8: 2
B8: 辅助作动器组
C8: 1
D8: ACT003
E8: 双出杆
F8: mm
G8: 毫米
H8: 100.0
I8: 30.0
J8: 0.0201
K8: 0.0154
L8: Negative
M8: 3.5
N8: 25.0
O8: 0.8
P8: 1.8
Q8: 辅助控制
```

#### 第9行 (第二个作动器组的第二个作动器)
```
A9: 2
B9: (空白)
C9: 2
D9: ACT004
E9: 单出杆
F9: cm
G9: 厘米
H9: 20.0
I9: 8.0
J9: 0.0283
K9: 0.0226
L9: Positive
M9: 4.2
N9: 40.0
O9: 1.2
P9: 2.1
Q9: 精密控制
```

## 🎨 第三步：应用样式格式（可选）

### 3.1 表头样式
- 选中第5行（表头行）
- 设置字体：粗体，11号字
- 设置背景色：深蓝色 (RGB: 68, 114, 196)
- 设置字体颜色：白色
- 设置对齐：水平居中，垂直居中

### 3.2 数据样式
- 组名称行（第6行、第8行）：浅蓝色背景 (RGB: 231, 243, 255)，粗体
- 普通数据行：白色背景，细线边框
- 数值列：右对齐，保留2位小数

## 🔧 第四步：测试导入功能

### 4.1 保存Excel文件
- 确保文件保存为 `.xlsx` 格式
- 建议文件名：`项目配置_含作动器.xlsx`

### 4.2 验证数据完整性
检查以下要点：
- ✅ 工作表名称为"作动器"
- ✅ 17列数据完整
- ✅ 组序号连续（1, 2）
- ✅ 作动器序号在组内连续（1, 2）
- ✅ 作动器序列号唯一（ACT001, ACT002, ACT003, ACT004）
- ✅ Unit类型和Unit名称匹配

### 4.3 测试系统导入
1. 在主程序中选择"导入Excel文件"
2. 选择包含作动器工作表的Excel文件
3. 验证系统是否正确识别作动器数据
4. 检查导入的作动器是否显示在界面中

## 📊 数据验证规则

### 必填字段验证
- **组序号**: 必须为正整数
- **作动器序号**: 组内必须唯一
- **作动器序列号**: 全局必须唯一
- **作动器类型**: 必须为"单出杆"或"双出杆"

### 数值范围验证
- **行程、位移**: 必须 > 0
- **拉伸面积、压缩面积**: 必须 > 0
- **频率**: 必须 > 0
- **输出倍数**: 必须 > 0

### 枚举值验证
- **Unit类型**: "m", "mm", "cm", "inch"
- **Unit名称**: "米", "毫米", "厘米", "英寸"
- **极性**: "Positive", "Negative"

## 🚀 高级功能

### 批量数据创建
如果需要创建大量作动器数据：
1. 使用Excel的填充功能自动生成序列号
2. 使用公式计算物理参数
3. 使用数据验证确保输入正确性

### 数据导入导出
- **导出**: 系统支持将作动器数据导出为Excel格式
- **导入**: 系统支持从Excel文件读取作动器数据
- **格式转换**: 支持CSV和Excel格式互转

## 🔍 故障排除

### 常见问题
1. **工作表名称错误**: 确保工作表名称为"作动器"
2. **列数不匹配**: 确保有完整的17列数据
3. **数据类型错误**: 确保数值列包含有效数字
4. **编码问题**: 确保中文字符正确显示

### 解决方案
1. 检查Excel文件格式和编码
2. 验证数据完整性
3. 查看系统错误日志
4. 联系技术支持

## 📝 总结

通过以上步骤，您已经成功：
1. ✅ 在Excel文件中创建了"作动器"工作表
2. ✅ 使用了标准的17列格式添加作动器数据
3. ✅ 准备好测试系统的导入功能

现在您可以使用这个Excel文件来测试系统的作动器数据导入功能，验证所有功能是否正常工作。
