<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>BasicInfoWidget</class>
 <widget class="QWidget" name="BasicInfoWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>基本信息控件</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <property name="topMargin">
      <number>0</number>
     </property>
     <item>
      <widget class="QLabel" name="lblSummaryInfo">
       <property name="text">
        <string>子通道数量: 0个</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QGroupBox" name="basicInfoGroup">
     <property name="title">
      <string>子通道详细信息</string>
     </property>
     <layout class="QVBoxLayout" name="basicInfoLayout">
      <item>
       <widget class="QTableWidget" name="basicInfoTable">
        <property name="editTriggers">
         <set>QAbstractItemView::NoEditTriggers</set>
        </property>
        <property name="alternatingRowColors">
         <bool>true</bool>
        </property>
        <property name="selectionBehavior">
         <enum>QAbstractItemView::SelectRows</enum>
        </property>
        <property name="showGrid">
         <bool>true</bool>
        </property>
        <property name="gridStyle">
         <enum>Qt::SolidLine</enum>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
