#include <QApplication>
#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QGroupBox>
#include <QLabel>
#include <QDebug>
#include <QTimer>

#include "DetailInfoPanel.h"
#include "BasicInfoWidget.h"
#include "NodeInfo.h"

class SubChannelDetailDisplayTest : public QMainWindow
{
    Q_OBJECT

public:
    SubChannelDetailDisplayTest(QWidget *parent = nullptr)
        : QMainWindow(parent)
        , m_detailPanel(nullptr)
        , m_basicInfoWidget(nullptr)
    {
        setWindowTitle("子通道详细信息显示测试");
        setGeometry(100, 100, 1200, 800);
        
        initUI();
        setupConnections();
        
        qDebug() << "=== 子通道详细信息显示测试程序启动 ===";
    }

private slots:
    void testControlChannelRoot() {
        qDebug() << "\n🎛️ 测试1：控制通道根节点显示";
        
        // 创建控制通道根节点信息
        NodeInfo rootInfo;
        rootInfo.nodeName = "控制通道";
        rootInfo.nodeType = "控制通道组";
        rootInfo.status = NodeStatus::Online;
        
        // 添加子通道信息
        SubNodeInfo channel1;
        channel1.name = "111";
        channel1.type = "控制通道";
        channel1.deviceName = "LD-B1";
        channel1.deviceId = "CH1";
        channel1.isConnected = true;
        channel1.setProperty("载荷1传感器选择", "载荷传感器_001");
        channel1.setProperty("载荷2传感器选择", "载荷传感器_002");
        channel1.setProperty("位置传感器选择", "位置传感器_001");
        channel1.setProperty("控制作动器选择", "伺服作动器_001");
        channel1.setProperty("下位机ID", "1");
        channel1.setProperty("站点ID", "1");
        channel1.setProperty("使能状态", "✅");
        channel1.setProperty("控制作动器极性", "正向");
        channel1.setProperty("载荷1传感器极性", "正向");
        channel1.setProperty("载荷2传感器极性", "正向");
        channel1.setProperty("位置传感器极性", "正向");
        rootInfo.addSubNode(channel1);
        
        SubNodeInfo channel2;
        channel2.name = "2222";
        channel2.type = "控制通道";
        channel2.deviceName = "LD-B2";
        channel2.deviceId = "CH2";
        channel2.isConnected = true;
        channel2.setProperty("载荷1传感器选择", "载荷传感器_003");
        channel2.setProperty("载荷2传感器选择", "载荷传感器_004");
        channel2.setProperty("位置传感器选择", "位置传感器_002");
        channel2.setProperty("控制作动器选择", "伺服作动器_002");
        channel2.setProperty("下位机ID", "2");
        channel2.setProperty("站点ID", "2");
        channel2.setProperty("使能状态", "✅");
        channel2.setProperty("控制作动器极性", "正向");
        channel2.setProperty("载荷1传感器极性", "正向");
        channel2.setProperty("载荷2传感器极性", "正向");
        channel2.setProperty("位置传感器极性", "正向");
        rootInfo.addSubNode(channel2);
        
        // 设置到详细信息面板
        m_detailPanel->setNodeInfo(rootInfo);
        
        qDebug() << "✅ 控制通道根节点信息已设置，包含" << rootInfo.subNodes.size() << "个子通道";
    }
    
    void testSubChannel111() {
        qDebug() << "\n🎯 测试2：子通道111详细信息显示（高亮第0行）";
        
        // 创建控制通道根节点信息
        NodeInfo rootInfo;
        rootInfo.nodeName = "控制通道";
        rootInfo.nodeType = "控制通道组";
        rootInfo.status = NodeStatus::Online;
        
        // 添加子通道信息
        SubNodeInfo channel1;
        channel1.name = "111";
        channel1.type = "控制通道";
        channel1.deviceName = "LD-B1";
        channel1.deviceId = "CH1";
        channel1.isConnected = true;
        channel1.setProperty("载荷1传感器选择", "载荷传感器_001");
        channel1.setProperty("载荷2传感器选择", "载荷传感器_002");
        channel1.setProperty("位置传感器选择", "位置传感器_001");
        channel1.setProperty("控制作动器选择", "伺服作动器_001");
        channel1.setProperty("下位机ID", "1");
        channel1.setProperty("站点ID", "1");
        channel1.setProperty("使能状态", "✅");
        channel1.setProperty("控制作动器极性", "正向");
        channel1.setProperty("载荷1传感器极性", "正向");
        channel1.setProperty("载荷2传感器极性", "正向");
        channel1.setProperty("位置传感器极性", "正向");
        rootInfo.addSubNode(channel1);
        
        SubNodeInfo channel2;
        channel2.name = "2222";
        channel2.type = "控制通道";
        channel2.deviceName = "LD-B2";
        channel2.deviceId = "CH2";
        channel2.isConnected = true;
        channel2.setProperty("载荷1传感器选择", "载荷传感器_003");
        channel2.setProperty("载荷2传感器选择", "载荷传感器_004");
        channel2.setProperty("位置传感器选择", "位置传感器_002");
        channel2.setProperty("控制作动器选择", "伺服作动器_002");
        channel2.setProperty("下位机ID", "2");
        channel2.setProperty("站点ID", "2");
        channel2.setProperty("使能状态", "✅");
        channel2.setProperty("控制作动器极性", "正向");
        channel2.setProperty("载荷1传感器极性", "正向");
        channel2.setProperty("载荷2传感器极性", "正向");
        channel2.setProperty("位置传感器极性", "正向");
        rootInfo.addSubNode(channel2);
        
        // 🆕 设置选中行索引（第0行，对应子通道111）
        rootInfo.setBasicProperty("selectedRow", 0);
        
        // 设置到详细信息面板
        m_detailPanel->setNodeInfo(rootInfo);
        
        qDebug() << "✅ 子通道111信息已设置，高亮第0行";
    }
    
    void testSubChannel2222() {
        qDebug() << "\n🎯 测试3：子通道2222详细信息显示（高亮第1行）";
        
        // 创建控制通道根节点信息
        NodeInfo rootInfo;
        rootInfo.nodeName = "控制通道";
        rootInfo.nodeType = "控制通道组";
        rootInfo.status = NodeStatus::Online;
        
        // 添加子通道信息
        SubNodeInfo channel1;
        channel1.name = "111";
        channel1.type = "控制通道";
        channel1.deviceName = "LD-B1";
        channel1.deviceId = "CH1";
        channel1.isConnected = true;
        channel1.setProperty("载荷1传感器选择", "载荷传感器_001");
        channel1.setProperty("载荷2传感器选择", "载荷传感器_002");
        channel1.setProperty("位置传感器选择", "位置传感器_001");
        channel1.setProperty("控制作动器选择", "伺服作动器_001");
        channel1.setProperty("下位机ID", "1");
        channel1.setProperty("站点ID", "1");
        channel1.setProperty("使能状态", "✅");
        channel1.setProperty("控制作动器极性", "正向");
        channel1.setProperty("载荷1传感器极性", "正向");
        channel1.setProperty("载荷2传感器极性", "正向");
        channel1.setProperty("位置传感器极性", "正向");
        rootInfo.addSubNode(channel1);
        
        SubNodeInfo channel2;
        channel2.name = "2222";
        channel2.type = "控制通道";
        channel2.deviceName = "LD-B2";
        channel2.deviceId = "CH2";
        channel2.isConnected = true;
        channel2.setProperty("载荷1传感器选择", "载荷传感器_003");
        channel2.setProperty("载荷2传感器选择", "载荷传感器_004");
        channel2.setProperty("位置传感器选择", "位置传感器_002");
        channel2.setProperty("控制作动器选择", "伺服作动器_002");
        channel2.setProperty("下位机ID", "2");
        channel2.setProperty("站点ID", "2");
        channel2.setProperty("使能状态", "✅");
        channel2.setProperty("控制作动器极性", "正向");
        channel2.setProperty("载荷1传感器极性", "正向");
        channel2.setProperty("载荷2传感器极性", "正向");
        channel2.setProperty("位置传感器极性", "正向");
        rootInfo.addSubNode(channel2);
        
        // 🆕 设置选中行索引（第1行，对应子通道2222）
        rootInfo.setBasicProperty("selectedRow", 1);
        
        // 设置到详细信息面板
        m_detailPanel->setNodeInfo(rootInfo);
        
        qDebug() << "✅ 子通道2222信息已设置，高亮第1行";
    }
    
    void testRowHighlighting() {
        qDebug() << "\n🎨 测试4：行高亮功能测试";
        
        // 测试不同的行高亮
        QTimer::singleShot(1000, [this]() {
            qDebug() << "🎯 高亮第0行（子通道111）";
            m_detailPanel->setSelectedRow(0);
        });
        
        QTimer::singleShot(2000, [this]() {
            qDebug() << "🎯 高亮第1行（子通道2222）";
            m_detailPanel->setSelectedRow(1);
        });
        
        QTimer::singleShot(3000, [this]() {
            qDebug() << "🎯 清除高亮";
            m_detailPanel->setSelectedRow(-1);
        });
        
        qDebug() << "✅ 行高亮测试已启动，将依次高亮第0行、第1行，然后清除高亮";
    }
    
    void clearInfo() {
        qDebug() << "\n🧹 清空信息";
        m_detailPanel->clearInfo();
        qDebug() << "✅ 信息已清空";
    }

private:
    void initUI() {
        QWidget* centralWidget = new QWidget(this);
        setCentralWidget(centralWidget);
        
        QVBoxLayout* mainLayout = new QVBoxLayout(centralWidget);
        
        // 测试按钮组
        QGroupBox* testGroup = new QGroupBox("测试功能");
        QHBoxLayout* buttonLayout = new QHBoxLayout(testGroup);
        
        QPushButton* testRootBtn = new QPushButton("测试控制通道根节点");
        QPushButton* test111Btn = new QPushButton("测试子通道111");
        QPushButton* test2222Btn = new QPushButton("测试子通道2222");
        QPushButton* testHighlightBtn = new QPushButton("测试行高亮");
        QPushButton* clearBtn = new QPushButton("清空信息");
        
        buttonLayout->addWidget(testRootBtn);
        buttonLayout->addWidget(test111Btn);
        buttonLayout->addWidget(test2222Btn);
        buttonLayout->addWidget(testHighlightBtn);
        buttonLayout->addWidget(clearBtn);
        
        mainLayout->addWidget(testGroup);
        
        // 详细信息面板
        m_detailPanel = new DetailInfoPanel(this);
        mainLayout->addWidget(m_detailPanel);
        
        // 连接信号
        connect(testRootBtn, &QPushButton::clicked, this, &SubChannelDetailDisplayTest::testControlChannelRoot);
        connect(test111Btn, &QPushButton::clicked, this, &SubChannelDetailDisplayTest::testSubChannel111);
        connect(test2222Btn, &QPushButton::clicked, this, &SubChannelDetailDisplayTest::testSubChannel2222);
        connect(testHighlightBtn, &QPushButton::clicked, this, &SubChannelDetailDisplayTest::testRowHighlighting);
        connect(clearBtn, &QPushButton::clicked, this, &SubChannelDetailDisplayTest::clearInfo);
    }
    
    void setupConnections() {
        // 可以添加其他连接
    }

private:
    DetailInfoPanel* m_detailPanel;
    BasicInfoWidget* m_basicInfoWidget;
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    SubChannelDetailDisplayTest testWindow;
    testWindow.show();
    
    qDebug() << "=== 子通道详细信息显示测试程序启动完成 ===";
    qDebug() << "请点击测试按钮来验证功能：";
    qDebug() << "1. 测试控制通道根节点显示";
    qDebug() << "2. 测试子通道111详细信息显示（高亮第0行）";
    qDebug() << "3. 测试子通道2222详细信息显示（高亮第1行）";
    qDebug() << "4. 测试行高亮功能";
    qDebug() << "5. 清空信息";
    
    return app.exec();
}

#include "test_subchannel_detail_display.moc" 