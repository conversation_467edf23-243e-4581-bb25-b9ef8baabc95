@echo off
chcp 65001 >nul
echo ========================================
echo 🔧 控制通道详细信息显示修复程序编译脚本
echo ========================================
echo.

:: 检查当前目录
echo 📁 当前工作目录: %CD%
echo.

:: 检查Qt环境
echo 🔍 检查Qt环境...
where qmake >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到qmake，请确保Qt环境已正确配置
    echo 💡 请运行Qt命令行或设置PATH环境变量
    pause
    exit /b 1
)

echo ✅ qmake已找到
echo.

:: 检查源文件
echo 🔍 检查源文件...
if not exist "控制通道详细信息显示修复程序.cpp" (
    echo ❌ 源文件不存在: 控制通道详细信息显示修复程序.cpp
    pause
    exit /b 1
)

if not exist "控制通道详细信息显示修复程序.pro" (
    echo ❌ 项目文件不存在: 控制通道详细信息显示修复程序.pro
    pause
    exit /b 1
)

echo ✅ 源文件和项目文件检查通过
echo.

:: 创建输出目录
echo 📁 创建输出目录...
if not exist "debug" mkdir debug
echo ✅ 输出目录已创建
echo.

:: 清理之前的构建文件
echo 🧹 清理之前的构建文件...
if exist "Makefile" del /q Makefile
if exist "Makefile.Debug" del /q Makefile.Debug
if exist "Makefile.Release" del /q Makefile.Release
if exist "object_script.*" del /q object_script.*
if exist "debug\*.exe" del /q debug\*.exe
if exist "debug\*.obj" del /q debug\*.obj
echo ✅ 清理完成
echo.

:: 生成Makefile
echo 🔨 生成Makefile...
qmake "控制通道详细信息显示修复程序.pro" -o Makefile
if %errorlevel% neq 0 (
    echo ❌ qmake失败，无法生成Makefile
    pause
    exit /b 1
)
echo ✅ Makefile生成成功
echo.

:: 编译程序
echo 🔨 开始编译...
make
if %errorlevel% neq 0 (
    echo ❌ 编译失败
    echo 💡 请检查编译错误信息
    pause
    exit /b 1
)
echo ✅ 编译成功
echo.

:: 检查输出文件
echo 🔍 检查输出文件...
if exist "debug\控制通道详细信息显示修复程序.exe" (
    echo ✅ 可执行文件已生成: debug\控制通道详细信息显示修复程序.exe
    echo.
    echo 🎉 编译完成！可以运行程序了
    echo.
    echo 💡 运行方法:
    echo    1. 双击 debug\控制通道详细信息显示修复程序.exe
    echo    2. 或在命令行中运行: debug\控制通道详细信息显示修复程序.exe
    echo.
) else (
    echo ❌ 可执行文件未生成
    echo 💡 请检查编译过程是否有错误
)

echo ========================================
echo 📋 编译过程完成
echo ========================================
pause 