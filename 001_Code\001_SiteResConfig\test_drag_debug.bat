@echo off
echo ========================================
echo  Drag Color Debug Test
echo ========================================

echo Starting application with debug output...

set EXE_PATH=build-SiteResConfig_Simple-Desktop_Qt_5_14_2_MinGW_32_bit-Debug\debug\SiteResConfig.exe

echo Checking for executable at: %EXE_PATH%

if exist "%EXE_PATH%" (
    echo Found executable, starting with console output...
    echo.
    echo Debug Information Added:
    echo - dragMoveEvent execution flow tracking
    echo - Variable values logging (targetItem, sourceType, etc.)
    echo - canAcceptDropPublic result verification
    echo - Color setting verification
    echo - Event handling status tracking
    echo.
    echo Test Instructions:
    echo 1. Watch the console output for debug messages
    echo 2. Start dragging a hardware node
    echo 3. Move mouse over test config tree items
    echo 4. Look for "=== dragMoveEvent START ===" messages
    echo 5. Check if "Setting highlight colors" appears
    echo 6. Verify color values in debug output
    echo.
    echo Starting application...
    echo (Debug output will appear in this console)
    echo.

    REM Start the application and keep console open to see debug output
    "%EXE_PATH%"

) else (
    echo Executable not found!
    echo.
    echo Checked path: %EXE_PATH%
    echo.
    echo Please build the project first or check if the path is correct.
)

echo.
echo Debug session ended.
pause
