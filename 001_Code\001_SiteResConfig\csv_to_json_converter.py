#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV到JSON转换工具
专门用于转换实验工程配置CSV文件为JSON格式
"""

import csv
import json
import sys
import os
from collections import OrderedDict

def parse_csv_to_json(csv_file_path, json_file_path):
    """
    将CSV文件转换为JSON格式
    
    Args:
        csv_file_path: CSV文件路径
        json_file_path: 输出JSON文件路径
    """
    
    try:
        # 读取CSV文件
        with open(csv_file_path, 'r', encoding='utf-8-sig') as csvfile:
            content = csvfile.read()
        
        print(f"成功读取CSV文件: {csv_file_path}")
        print(f"文件大小: {len(content)} 字符")
        
        # 解析CSV内容
        lines = content.split('\n')
        
        # 创建JSON结构
        json_data = OrderedDict()
        
        # 解析项目基本信息
        project_info = OrderedDict()
        current_section = None
        current_data = []
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue
                
            print(f"处理第 {line_num} 行: {line[:50]}...")
            
            if line.startswith('#'):
                # 处理头部注释信息
                if '工程名称' in line:
                    parts = line.split(',', 1)
                    if len(parts) > 1:
                        project_info['projectName'] = parts[1].strip()
                elif '创建日期' in line:
                    parts = line.split(',', 1)
                    if len(parts) > 1:
                        project_info['createdDate'] = parts[1].strip()
                elif '版本' in line:
                    parts = line.split(',', 1)
                    if len(parts) > 1:
                        project_info['version'] = parts[1].strip()
                elif '描述' in line:
                    parts = line.split(',', 1)
                    if len(parts) > 1:
                        project_info['description'] = parts[1].strip()
                        
            elif line.startswith('[') and line.endswith(']'):
                # 处理节标题
                if current_section and current_data:
                    json_data[current_section] = current_data
                
                current_section = line[1:-1]  # 移除方括号
                current_data = []
                print(f"开始处理节: {current_section}")
                
            elif '类型,名称,参数1,参数2,参数3' in line:
                # 跳过表头
                continue
                
            elif line and not line.startswith('#'):
                # 处理数据行
                fields = [field.strip() for field in line.split(',')]
                
                if len(fields) >= 1 and fields[0]:
                    # 这是一个设备类型行
                    device_type = fields[0]
                    device_data = OrderedDict()
                    device_data['deviceType'] = device_type
                    device_data['parameters'] = OrderedDict()
                    current_data.append(device_data)
                    
                elif len(fields) >= 3 and fields[1].strip().startswith('├─') or fields[1].strip().startswith('└─'):
                    # 这是一个参数行
                    if current_data:
                        param_name = fields[1].strip().replace('├─ ', '').replace('└─ ', '').replace('└─────────────────────────', '')
                        if param_name and len(fields) >= 3:
                            param_value = fields[2].strip()
                            unit = fields[3].strip() if len(fields) > 3 else ''
                            
                            if unit:
                                current_data[-1]['parameters'][param_name] = f"{param_value} {unit}"
                            else:
                                current_data[-1]['parameters'][param_name] = param_value
        
        # 添加最后一个节的数据
        if current_section and current_data:
            json_data[current_section] = current_data
        
        # 添加项目信息到JSON开头
        final_json = OrderedDict()
        final_json['projectInfo'] = project_info
        final_json.update(json_data)
        
        # 写入JSON文件
        with open(json_file_path, 'w', encoding='utf-8') as jsonfile:
            json.dump(final_json, jsonfile, ensure_ascii=False, indent=2)
        
        print(f"✅ 成功转换CSV为JSON")
        print(f"📁 输入文件: {csv_file_path}")
        print(f"📁 输出文件: {json_file_path}")
        print(f"📊 项目信息: {project_info.get('projectName', 'N/A')}")
        print(f"📊 包含节数: {len(json_data)}")
        
        # 统计设备数量
        total_devices = 0
        for section_name, section_data in json_data.items():
            device_count = len(section_data)
            total_devices += device_count
            print(f"📊 {section_name}: {device_count} 个设备")
        
        print(f"📊 总设备数: {total_devices}")
        
        return True
        
    except FileNotFoundError:
        print(f"❌ 错误: 找不到CSV文件 {csv_file_path}")
        return False
    except UnicodeDecodeError as e:
        print(f"❌ 编码错误: {e}")
        print("💡 尝试使用不同的编码格式...")
        
        # 尝试其他编码
        encodings = ['gbk', 'gb2312', 'utf-8', 'utf-16']
        for encoding in encodings:
            try:
                with open(csv_file_path, 'r', encoding=encoding) as csvfile:
                    content = csvfile.read()
                print(f"✅ 使用 {encoding} 编码成功读取文件")
                # 递归调用，但这次文件已经用正确编码读取
                return parse_csv_to_json_with_encoding(csv_file_path, json_file_path, encoding)
            except:
                continue
        
        print("❌ 无法使用任何编码读取文件")
        return False
    except Exception as e:
        print(f"❌ 转换过程中发生错误: {e}")
        return False

def parse_csv_to_json_with_encoding(csv_file_path, json_file_path, encoding):
    """使用指定编码转换CSV到JSON"""
    try:
        with open(csv_file_path, 'r', encoding=encoding) as csvfile:
            content = csvfile.read()
        
        # 重新处理内容...
        # (这里可以复用上面的逻辑)
        return True
    except Exception as e:
        print(f"❌ 使用编码 {encoding} 转换失败: {e}")
        return False

def main():
    """主函数"""
    print("🔄 CSV到JSON转换工具")
    print("=" * 50)
    
    # 检查命令行参数
    if len(sys.argv) != 3:
        print("使用方法: python csv_to_json_converter.py <CSV文件路径> <JSON文件路径>")
        print("示例: python csv_to_json_converter.py input.csv output.json")
        
        # 使用默认路径
        csv_path = r"C:\Users\<USER>\Desktop\20250812095644_实验工程.csv"
        json_path = r"C:\Users\<USER>\Desktop\20250812095644_实验工程.json"
        
        print(f"\n使用默认路径:")
        print(f"📁 CSV文件: {csv_path}")
        print(f"📁 JSON文件: {json_path}")
        
        if not os.path.exists(csv_path):
            print(f"❌ 默认CSV文件不存在: {csv_path}")
            print("请提供正确的文件路径作为命令行参数")
            return False
    else:
        csv_path = sys.argv[1]
        json_path = sys.argv[2]
    
    # 检查输入文件是否存在
    if not os.path.exists(csv_path):
        print(f"❌ CSV文件不存在: {csv_path}")
        return False
    
    # 确保输出目录存在
    output_dir = os.path.dirname(json_path)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"📁 创建输出目录: {output_dir}")
    
    # 执行转换
    success = parse_csv_to_json(csv_path, json_path)
    
    if success:
        print("\n🎉 转换完成!")
        print(f"✅ JSON文件已保存到: {json_path}")
    else:
        print("\n❌ 转换失败!")
    
    return success

if __name__ == "__main__":
    main()
