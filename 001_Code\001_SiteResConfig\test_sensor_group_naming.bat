@echo off
echo ========================================
echo  传感器组命名格式测试
echo ========================================

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o
if exist "ui_*.h" del ui_*.h

echo.
echo 生成UI头文件...
uic ui\MainWindow.ui -o ui_MainWindow.h
if errorlevel 1 (
    echo UI文件生成失败！
    pause
    exit /b 1
) else (
    echo ✅ UI头文件生成成功: ui_MainWindow.h
)

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo.
    echo 修改内容：
    echo ✅ 更新传感器组命名格式
    echo ✅ 适配新格式的型号选择逻辑
    echo ✅ 更新传感器类型自动设置
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！传感器组命名格式已更新
    echo ========================================
    
    if exist "SiteResConfig.exe" (
        echo 可执行文件: SiteResConfig.exe
        echo.
        echo ✅ 传感器组命名格式已更新！
        echo.
        echo 📋 新的传感器组命名格式:
        echo.
        echo 🏷️ 标准传感器组格式: "类型_传感器组"
        echo ├─ 载荷_传感器组 (选择"载荷"时创建)
        echo ├─ 位置_传感器组 (选择"位置"时创建)
        echo ├─ 压力_传感器组 (选择"压力"时创建)
        echo ├─ 温度_传感器组 (选择"温度"时创建)
        echo ├─ 振动_传感器组 (选择"振动"时创建)
        echo ├─ 应变_传感器组 (选择"应变"时创建)
        echo └─ 角度_传感器组 (选择"角度"时创建)
        echo.
        echo 🏷️ 自定义传感器组格式: "自定义类型_传感器组"
        echo ├─ 液压_传感器组 (输入"液压"时创建)
        echo ├─ 电流_传感器组 (输入"电流"时创建)
        echo ├─ 电压_传感器组 (输入"电压"时创建)
        echo └─ 流量_传感器组 (输入"流量"时创建)
        echo.
        echo 🌳 创建后的树形结构示例:
        echo 任务1
        echo ├─ 作动器
        echo ├─ 传感器 (类型: "传感器")
        echo │  ├─ 载荷_传感器组 (类型: "传感器组")
        echo │  │  ├─ 传感器_000001 [载荷传感器] (类型: "传感器设备")
        echo │  │  └─ 传感器_000002 [载荷传感器] (类型: "传感器设备")
        echo │  ├─ 位置_传感器组 (类型: "传感器组")
        echo │  │  └─ 传感器_000001 [位置传感器] (类型: "传感器设备")
        echo │  ├─ 液压_传感器组 (类型: "传感器组") - 自定义
        echo │  │  └─ 传感器_000001 [液压传感器] (类型: "传感器设备")
        echo │  └─ 电流_传感器组 (类型: "传感器组") - 自定义
        echo │     └─ 传感器_000001 [电流传感器] (类型: "传感器设备")
        echo └─ 硬件节点资源
        echo.
        echo 🎯 传感器参数输入界面更新:
        echo.
        echo 示例1 - 载荷传感器组:
        echo ┌─────────────────────────────────────────────────────┐
        echo │                新建传感器                            │
        echo ├─────────────────────────────────────────────────────┤
        echo │ 传感器组（选择要添加传感器的传感器组名称）：          │
        echo │           载荷_传感器组\传感器_000001               │
        echo ├─────────────────────────────────────────────────────┤
        echo │ 序列号: [传感器_000001                    ]         │
        echo │ 类型:   [载荷传感器                      ] (只读)   │
        echo │ 型号:   [LCF-100kN ▼]                              │
        echo │ 量程:   [±100kN                          ]         │
        echo │ 精度:   [±0.1%FS                         ]         │
        echo │                                                     │
        echo │                    [确定] [取消]                    │
        echo └─────────────────────────────────────────────────────┘
        echo.
        echo 示例2 - 自定义液压传感器组:
        echo ┌─────────────────────────────────────────────────────┐
        echo │                新建传感器                            │
        echo ├─────────────────────────────────────────────────────┤
        echo │ 传感器组（选择要添加传感器的传感器组名称）：          │
        echo │           液压_传感器组\传感器_000001               │
        echo ├─────────────────────────────────────────────────────┤
        echo │ 序列号: [传感器_000001                    ]         │
        echo │ 类型:   [液压传感器                      ] (只读)   │
        echo │ 型号:   [标准型号 ▼]                               │
        echo │ 量程:   [0-300bar                        ]         │
        echo │ 精度:   [±0.5%FS                         ]         │
        echo │                                                     │
        echo │                    [确定] [取消]                    │
        echo └─────────────────────────────────────────────────────┘
        echo.
        echo 🔧 技术实现改进:
        echo.
        echo ✅ 命名格式统一:
        echo - 标准组: QString("%1_传感器组").arg(selectedItem)
        echo - 自定义组: QString("%1_传感器组").arg(customType)
        echo - 格式一致，便于管理和识别
        echo.
        echo ✅ 类型提取逻辑:
        echo - 自动提取组名中的类型部分
        echo - 去掉"_传感器组"后缀获取纯类型名
        echo - 用于型号选择和传感器类型设置
        echo.
        echo ✅ 智能型号匹配:
        echo - 根据提取的类型名匹配对应型号
        echo - 载荷类型显示载荷传感器型号
        echo - 位置类型显示位移传感器型号
        echo - 其他类型显示通用型号
        echo.
        echo ✅ 传感器类型自动设置:
        echo - 提取类型名 + "传感器"
        echo - 载荷_传感器组 → 载荷传感器
        echo - 液压_传感器组 → 液压传感器
        echo - 保持命名一致性
        echo.
        echo 💡 命名格式优势:
        echo.
        echo 🏷️ 规范统一:
        echo - 所有传感器组都有统一的命名格式
        echo - 便于用户理解和记忆
        echo - 符合专业软件的命名规范
        echo.
        echo 🏷️ 类型明确:
        echo - 组名直接体现传感器类型
        echo - 便于快速识别和分类
        echo - 支持自定义类型扩展
        echo.
        echo 🏷️ 层次清晰:
        echo - 传感器组与传感器设备层次分明
        echo - 便于树形结构的管理
        echo - 支持复杂的设备分类
        echo.
        echo 启动程序测试传感器组命名格式...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
        echo 启动程序...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
        echo 启动程序...
        start release\SiteResConfig.exe
    ) else (
        echo 警告: 找不到可执行文件！
    )
)

echo.
echo 📖 传感器组命名格式测试指南:
echo.
echo 🎯 完整测试流程:
echo.
echo 1️⃣ 测试标准传感器组命名:
echo   - 右键"传感器" → "新建" → "传感器组"
echo   - 选择"载荷"
echo   - 验证创建的组名为"载荷_传感器组"
echo   - 选择"位置"
echo   - 验证创建的组名为"位置_传感器组"
echo.
echo 2️⃣ 测试自定义传感器组命名:
echo   - 选择"自定义..."
echo   - 输入"液压"
echo   - 验证创建的组名为"液压_传感器组"
echo   - 输入"电流"
echo   - 验证创建的组名为"电流_传感器组"
echo.
echo 3️⃣ 测试传感器创建:
echo   - 右键"载荷_传感器组" → "新建" → "传感器"
echo   - 验证显示信息为"载荷_传感器组\传感器_000001"
echo   - 验证类型自动设置为"载荷传感器"
echo   - 验证型号选项为载荷传感器型号
echo.
echo 4️⃣ 测试自定义组传感器创建:
echo   - 右键"液压_传感器组" → "新建" → "传感器"
echo   - 验证显示信息为"液压_传感器组\传感器_000001"
echo   - 验证类型自动设置为"液压传感器"
echo   - 验证型号选项为通用型号
echo.
echo 🔍 验证要点:
echo - 传感器组命名格式正确
echo - 自定义组命名格式正确
echo - 类型提取逻辑正常工作
echo - 型号选择匹配正确类型
echo - 传感器类型自动设置正确
echo - 显示信息格式正确
echo.
pause
