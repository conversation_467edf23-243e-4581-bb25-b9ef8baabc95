/**
 * @file UIManager.h
 * @brief UI管理器 - 负责主界面的UI初始化和样式设置
 * @details v3.4架构：将UI相关逻辑从主界面分离到专门的管理器
 * <AUTHOR> Assistant
 * @date 2025-01-27
 * @version 3.4.0
 */

#ifndef UIMANAGER_H
#define UIMANAGER_H

#include <QObject>
#include <QMainWindow>
#include <QTreeWidget>
#include <QTimer>

// 前向声明
class CMyMainWindow;
namespace Ui { class MainWindow; }

/**
 * @brief UI管理器类
 * @details 负责主界面的UI初始化、样式设置、布局管理等
 */
class UIManager : public QObject {
    Q_OBJECT

public:
    explicit UIManager(CMyMainWindow* mainWindow, QObject* parent = nullptr);
    ~UIManager();

    // 主要初始化方法
    void setupUI();
    void connectUISignals();
    void loadInitialData();

    // 样式相关方法
    void loadStyleSheetFromFile();
    void setupTreeWidgetIcons();
    void setupSimpleTreeIcons();
    void setupCustomTreeStyle();
    void restoreColors();

    // 树形控件初始化
    void initializeHardwareTree();
    void initializeTestConfigTree();
    void enableTestConfigTreeDragDrop();

    // UI状态管理
    void updateOperationAreaState(bool hasProject);
    void hideUnusedMenuItems();
    void clearInterfaceData();
    void setDefaultEmptyInterface();

    // UI更新方法
    void updateUI();
    void updateTreeDisplay();
    void refreshHardwareTreeFromDataManagers();
    void forceRestoreAllTreeColors();

public slots:
    void onShowDetailInfoDlgChanged(bool checked);

private:
    CMyMainWindow* mainWindow_;
    Ui::MainWindow* ui_;

    // 辅助方法
    void setupTreeStyles();
    void setupMenusAndToolbars();
    void setupStatusBar();
    void setupSplitters();
    void configureTreeWidgets();
};

#endif // UIMANAGER_H 
