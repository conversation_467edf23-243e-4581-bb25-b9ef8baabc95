#pragma once

/**
 * @file ActuatorDialog_1_2.h
 * @brief 作动器创建对话框类定义
 * @details 使用Qt Designer设计的作动器参数输入对话框
 * <AUTHOR> Assistant
 * @date 2025-08-06
 * @version 1.0.0
 */

#include <QtWidgets/QDialog>
#include <QtCore/QString>

QT_BEGIN_NAMESPACE
class QTreeWidgetItem;
QT_END_NAMESPACE

// 前向声明
class ActuatorDataManager_1_2;

namespace Ui {
class ActuatorDialog_1_2;
}

namespace UI {

// 作动器类型枚举
enum class ActuatorType_1_2 {
    SingleRod = 1,    // 单出杆
    DoubleRod = 2     // 双出杆
};

// 极性枚举
enum class Polarity_1_2 {
    Unknown = 0,      // 未知
    Positive = 1,     // 正极性
    Negative = -1,    // 负极性
    Both = 9          // 双极性
};

// 测量单位枚举
enum class MeasurementUnit_1_2 {
    Meter = 1,        // 米
    Millimeter = 2,   // 毫米
    Centimeter = 3,   // 厘米
    Inch = 4          // 英寸
};

/**
 * @brief 作动器详细参数结构体
 * @details 存储作动器的详细配置参数
 */
struct ActuatorDetailParams_1_2 {
    QString model;                    // 型号
    QString sn;                      // 序列号
    double k;                        // k系数
    double b;                        // b系数
    double precision;                // 精度
    Polarity_1_2 polarity;          // 极性
    MeasurementUnit_1_2 meas_unit;   // 测量单位
    double meas_range_min;           // 测量下限
    double meas_range_max;           // 测量上限
    int output_signal_unit;          // 输出信号单位
    double output_signal_range_min;  // 输出信号下限
    double output_signal_range_max;  // 输出信号上限

    // 默认构造函数
    ActuatorDetailParams_1_2() : k(1.0), b(0.0), precision(0.1),
                                polarity(Polarity_1_2::Positive),
                                meas_unit(MeasurementUnit_1_2::Millimeter),
                                meas_range_min(-100.0), meas_range_max(100.0),
                                output_signal_unit(1),
                                output_signal_range_min(-100.0),
                                output_signal_range_max(100.0) {}
};

/**
 * @brief 作动器参数结构体
 * @details 存储作动器的所有配置参数
 */
struct ActuatorParams_1_2 {
    // 基本信息
    int actuatorId;                  // 内部ID（保持兼容）
    QString name;                    // 控制量名称
    ActuatorType_1_2 type;          // 作动器类型
    double zero_offset;              // 零偏

    // 硬件配置
    int lc_id;                      // 下位机ID
    int station_id;                 // 站点ID
    int board_id_ao;                // 板卡AO ID
    int board_type_ao;              // 板卡AO类型
    int port_id_ao;                 // 端口AO ID
    int board_id_do;                // 板卡DO ID
    int board_type_do;              // 板卡DO类型
    int port_id_do;                 // 端口DO ID

    // 详细参数
    ActuatorDetailParams_1_2 params; // 作动器详细参数

    // 默认构造函数
    ActuatorParams_1_2() : actuatorId(0), type(ActuatorType_1_2::SingleRod),
                          zero_offset(0.0), lc_id(1), station_id(1),
                          board_id_ao(1), board_type_ao(1), port_id_ao(1),
                          board_id_do(1), board_type_do(1), port_id_do(1) {
        name = "控制量";
    }
};

/**
 * @brief 作动器组结构体
 * @details 存储作动器组及其包含的作动器列表
 */
struct ActuatorGroup_1_2 {
    int groupId;                          // 组序号
    QString groupName;                    // 作动器组名称
    QList<ActuatorParams_1_2> actuators;      // 作动器列表
    QString groupType;                    // 组类型 (液压/电动/气动/伺服)
    QString createTime;                   // 创建时间
    QString groupNotes;                   // 组备注
};

/**
 * @brief 作动器创建对话框类
 * @details 使用.ui文件设计的标准Qt对话框
 */
class ActuatorDialog_1_2 : public QDialog {
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param groupName 作动器组名称
     * @param autoNumber 自动生成的编号
     * @param parent 父窗口
     */
    explicit ActuatorDialog_1_2(const QString& groupName, const QString& autoNumber, QWidget* parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    virtual ~ActuatorDialog_1_2();

    /**
     * @brief 获取作动器参数
     * @return 作动器参数结构体
     */
    ActuatorParams_1_2 getActuatorParams() const;

    /**
     * @brief 设置作动器参数（用于编辑模式）
     * @param params 作动器参数结构体
     */
    void setActuatorParams(const ActuatorParams_1_2& params);

    /**
     * @brief 验证输入参数
     * @return 验证是否通过
     */
    bool validateInput();

    // 🆕 新增：编辑模式相关功能
    /**
     * @brief 设置编辑模式
     * @param enabled 是否启用编辑模式
     * @param originalSerialNumber 原始序列号（用于编辑模式下的唯一性检查）
     */
    void setEditMode(bool enabled, const QString& originalSerialNumber = QString());

    /**
     * @brief 检查是否为编辑模式
     * @return 是否为编辑模式
     */
    bool isEditMode() const;

    /**
     * @brief 设置当前组ID
     * @param groupId 组ID
     */
    void setCurrentGroupId(int groupId);

    /**
     * @brief 获取当前组ID
     * @return 组ID
     */
    int getCurrentGroupId() const;

    /**
     * @brief 设置数据管理器（用于验证）
     * @param manager 数据管理器指针
     */
    void setDataManager(ActuatorDataManager_1_2* manager);

    /**
     * @brief 应用参数模板
     * @param templateName 模板名称
     */
    void applyParameterTemplate(const QString& templateName);

    /**
     * @brief 导出当前参数为模板
     * @return 模板字符串
     */
    QString exportParameterTemplate() const;

    /**
     * @brief 重置表单为默认值
     */
    void resetForm();

private slots:
    /**
     * @brief Auto Balance按钮点击槽函数
     */
    void onAutoBalanceClicked();

    /**
     * @brief 保存按钮点击前的验证
     */
    void onAcceptClicked();

//    /**
//     * @brief 预览配置按钮点击槽函数
//     */
//    void onPreviewClicked();

//    /**
//     * @brief 帮助按钮点击槽函数
//     */
//    void onHelpClicked();

//    /**
//     * @brief 信息提示按钮点击槽函数
//     */
//    void onInfoClicked();

//    /**
//     * @brief 作动器分类选择变化槽函数
//     */
//    void onCategoryChanged(const QString& category);

private:
    Ui::ActuatorDialog_1_2* ui;
    QString groupName_;
    QString autoNumber_;
    
    // 🆕 新增：编辑模式相关成员变量
    bool editMode_;                    // 编辑模式标志
    QString originalSerialNumber_;     // 原始序列号（编辑模式使用）
    int currentGroupId_;              // 当前组ID
    ActuatorDataManager_1_2* dataManager_;  // 数据管理器指针

    /**
     * @brief 初始化界面
     */
    void initializeUI();

    /**
     * @brief 连接信号槽
     */
    void connectSignals();

    /**
     * @brief 作动器类型变化处理
     * @param type 新的作动器类型
     */
    void onTypeChanged(const QString& type);

    // 🚫 删除不再需要的计算方法声明（因为移除了兼容性字段）
};

} // namespace UI
