/**
 * @file test_control_channel_excel_export.cpp
 * @brief 测试控制通道数据在Excel导出中的表现
 * @details 模拟实际使用场景，创建控制通道数据并测试Excel导出功能
 * <AUTHOR> Assistant
 * @date 2025-08-22
 * @version 1.0.0
 */

#include <QCoreApplication>
#include <QDebug>
#include <QFile>
#include <QDir>
#include <QFileInfo>
#include "CtrlChanDataManager.h"
#include "XLSDataExporter_1_2.h"
#include "SensorDataManager_1_2.h"
#include "ActuatorDataManager_1_2.h"

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "=== 控制通道Excel导出功能测试 ===";
    
    // 1. 创建数据管理器
    qDebug() << "\n1. 创建数据管理器...";
    auto sensorManager = std::make_unique<SensorDataManager_1_2>();
    auto actuatorManager = std::make_unique<ActuatorDataManager_1_2>();
    auto ctrlChanManager = std::make_unique<CtrlChanDataManager>();
    
    qDebug() << "✅ 数据管理器创建完成";
    
    // 2. 创建测试控制通道数据
    qDebug() << "\n2. 创建测试控制通道数据...";
    UI::ControlChannelGroup testGroup;
    testGroup.groupId = 1;
    testGroup.groupName = "测试控制通道组";
    testGroup.groupType = "控制通道";
    testGroup.createTime = "2025-08-22 15:30:00";
    testGroup.groupNotes = "Excel导出测试用控制通道组";
    
    // 添加CH1通道
    UI::ControlChannelParams ch1;
    ch1.channelId = "CH1";
    ch1.channelName = "CH1";
    ch1.hardwareAssociation = "LD-B1 - CH1";
    ch1.load1Sensor = "载荷传感器_001";
    ch1.load2Sensor = "载荷传感器_002";
    ch1.positionSensor = "位置传感器_001";
    ch1.controlActuator = "加载电机_001";
    ch1.notes = "CH1测试通道";
    testGroup.channels.push_back(ch1);
    
    // 添加CH2通道
    UI::ControlChannelParams ch2;
    ch2.channelId = "CH2";
    ch2.channelName = "CH2";
    ch2.hardwareAssociation = "LD-B2 - CH2";
    ch2.load1Sensor = "载荷传感器_003";
    ch2.load2Sensor = "载荷传感器_004";
    ch2.positionSensor = "位置传感器_002";
    ch2.controlActuator = "位移电机_001";
    ch2.notes = "CH2测试通道";
    testGroup.channels.push_back(ch2);
    
    // 添加CH3通道
    UI::ControlChannelParams ch3;
    ch3.channelId = "CH3";
    ch3.channelName = "CH3";
    ch3.hardwareAssociation = "LD-B3 - CH3";
    ch3.load1Sensor = "载荷传感器_005";
    ch3.load2Sensor = "载荷传感器_006";
    ch3.positionSensor = "位置传感器_003";
    ch3.controlActuator = "旋转电机_001";
    ch3.notes = "CH3测试通道";
    testGroup.channels.push_back(ch3);
    
    // 创建控制通道组
    if (ctrlChanManager->createControlChannelGroup(testGroup)) {
        qDebug() << "✅ 控制通道组创建成功";
        qDebug() << QString("   组名: %1").arg(QString::fromStdString(testGroup.groupName));
        qDebug() << QString("   通道数: %1").arg(testGroup.channels.size());
    } else {
        qDebug() << "❌ 控制通道组创建失败";
        return -1;
    }
    
    // 验证数据是否正确保存
    auto groups = ctrlChanManager->getAllControlChannelGroups();
    qDebug() << QString("📊 数据管理器中的控制通道组数量: %1").arg(groups.size());
    
    if (groups.isEmpty()) {
        qDebug() << "❌ 控制通道数据管理器中没有数据";
        return -1;
    }
    
    // 3. 创建XLS导出器并设置数据管理器
    qDebug() << "\n3. 创建XLS导出器...";
    auto xlsExporter = std::make_unique<XLSDataExporter_1_2>(
        sensorManager.get(), 
        actuatorManager.get(), 
        ctrlChanManager.get()
    );
    
    qDebug() << "✅ XLS导出器创建完成";
    
    // 4. 导出Excel文件
    qDebug() << "\n4. 导出Excel文件...";
    QString testFilePath = QDir::currentPath() + "/test_control_channel_export.xlsx";
    
    // 删除已存在的测试文件
    if (QFile::exists(testFilePath)) {
        QFile::remove(testFilePath);
        qDebug() << "🗑️ 删除已存在的测试文件";
    }
    
    if (xlsExporter->exportCompleteProject(testFilePath)) {
        qDebug() << "✅ Excel文件导出成功";
        qDebug() << QString("   文件路径: %1").arg(testFilePath);
        
        // 验证文件是否真正创建
        QFileInfo fileInfo(testFilePath);
        if (fileInfo.exists()) {
            qDebug() << QString("✅ 文件确实存在，大小: %1 bytes").arg(fileInfo.size());
        } else {
            qDebug() << "❌ 文件未创建";
            return -1;
        }
    } else {
        qDebug() << "❌ Excel文件导出失败";
        qDebug() << QString("   错误信息: %1").arg(xlsExporter->getLastError());
        return -1;
    }
    
    // 5. 验证Excel文件内容（简单验证）
    qDebug() << "\n5. 验证完成";
    qDebug() << "=== 测试总结 ===";
    qDebug() << "✅ 控制通道数据创建: 成功";
    qDebug() << "✅ 数据管理器存储: 成功";
    qDebug() << "✅ Excel导出器设置: 成功";
    qDebug() << "✅ Excel文件导出: 成功";
    qDebug() << "";
    qDebug() << "🎯 请手动打开生成的Excel文件验证是否包含控制通道工作表";
    qDebug() << QString("📁 文件路径: %1").arg(testFilePath);
    
    return 0;
} 