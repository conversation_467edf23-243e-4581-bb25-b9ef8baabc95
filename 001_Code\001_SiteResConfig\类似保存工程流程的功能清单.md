# 与"保存工程"→保存XLSX类似的流程功能清单

## 📋 **概述**

本报告列出了系统中所有与"保存工程"→保存XLSX类似的保存/导出流程，这些功能都具有相似的用户交互模式和文件操作逻辑。

## 🔄 **类似流程功能清单**

### **1. 文件菜单相关流程**

#### **1.1 保存工程 (OnSaveProject)** ⭐ **主要流程**
```
菜单: 文件 → 保存工程
方法: OnSaveProject() → SaveProjectToXLS()
格式: 强制XLSX格式
特点: 智能路径管理，包含作动器详细配置
```

#### **1.2 另存为工程 (OnSaveAsProject)**
```
菜单: 文件 → 另存为工程 (实际显示为"导出工程")
方法: OnSaveAsProject() → SaveProjectToJSON/CSV()
格式: JSON或CSV格式选择
特点: 弹出格式选择对话框
```

#### **1.3 打开工程 (OnOpenProject)**
```
菜单: 文件 → 打开工程
方法: OnOpenProject() → LoadProjectFromXLS()
格式: 强制XLSX格式
特点: 与保存工程对应的加载流程
```

### **2. 数据导出菜单相关流程**

#### **2.1 数据导出主菜单 (OnDataExport)**
```
菜单: 工具 → 数据导出 (或快捷键 Ctrl+D)
方法: OnDataExport() → showExportOptionsDialog()
选项: 7种导出选择
特点: 统一的导出选项对话框
```

#### **2.2 导出到Excel (OnExportToExcel)**
```
选项: "导出到Excel"
方法: OnExportToExcel() → xlsDataExporter_->exportHardwareTree()
格式: XLSX格式
特点: 基础的硬件树导出
```

#### **2.3 导出硬件树到Excel (OnExportHardwareTreeToExcel)**
```
菜单: 工具 → 数据导出 → 导出硬件树到Excel
方法: OnExportHardwareTreeToExcel() → xlsDataExporter_->exportHardwareTree()
格式: XLSX格式
特点: 专门的硬件树导出
```

#### **2.4 导出传感器详细信息到Excel (OnExportSensorDetailsToExcel)**
```
菜单: 工具 → 数据导出 → 导出传感器详细信息到Excel
方法: OnExportSensorDetailsToExcel() → xlsDataExporter_->exportSensorGroupDetails()
格式: XLSX格式，33列传感器参数
特点: 包含完整传感器配置
```

#### **2.5 导出作动器详细信息到Excel (OnExportActuatorDetailsToExcel)**
```
菜单: 工具 → 数据导出 → 导出作动器详细信息到Excel
方法: OnExportActuatorDetailsToExcel() → xlsDataExporter_->exportActuatorDetails()
格式: XLSX格式，16列作动器参数
特点: 包含完整作动器配置
```

#### **2.6 导出完整项目到Excel (OnExportCompleteProjectToExcel)**
```
菜单: 工具 → 数据导出 → 导出完整项目到Excel
方法: OnExportCompleteProjectToExcel() → xlsDataExporter_->exportCompleteProject()
格式: XLSX格式，多工作表
特点: 包含硬件+传感器+作动器的完整项目
```

#### **2.7 批量导出多种格式 (OnBatchExportToMultipleFormats)**
```
菜单: 工具 → 数据导出 → 批量导出多种格式
方法: OnBatchExportToMultipleFormats() → 多格式导出
格式: CSV + JSON + XLSX
特点: 一次操作导出三种格式
```

### **3. 新的导出系统相关流程**

#### **3.1 新导出系统示例 (onSaveProjectActionTriggered)**
```
方法: onSaveProjectActionTriggered() → dataExportManager_->exportCompleteProject()
格式: 支持多种格式
特点: 使用DataExportManager统一管理
```

#### **3.2 统一导出方法 (exportProjectData)**
```
方法: exportProjectData() → dataExportManager_->exportCompleteProject()
格式: 根据文件扩展名自动选择
特点: 工厂模式创建导出器
```

## 📊 **流程特征分析**

### **相似特征**

1. **文件对话框模式**
   - 都使用 `QFileDialog::getSaveFileName()` 
   - 都有默认文件名生成逻辑
   - 都有文件过滤器设置

2. **路径管理**
   - 使用 `lastUsedCSVPath_` 记住上次路径
   - 通过 `QFileInfo(fileName).absolutePath()` 更新路径

3. **错误处理**
   - 统一的成功/失败消息提示
   - 使用 `AddLogEntry()` 记录日志
   - 通过 `handleExportResult()` 处理结果

4. **数据获取**
   - 从DataManager获取数据
   - 检查数据是否为空
   - 验证导出器是否初始化

### **差异特征**

1. **数据范围**
   - 硬件树：基础结构数据
   - 传感器详细：33列完整参数
   - 作动器详细：16列完整参数
   - 完整项目：多工作表综合数据

2. **文件格式**
   - XLSX：主要格式，支持多工作表
   - CSV：简单格式，单一表格
   - JSON：结构化格式，层次数据

3. **触发方式**
   - 直接菜单：单一功能入口
   - 选项对话框：多选择统一入口
   - 批量操作：一次多格式导出

## 🎯 **核心流程模式**

### **标准保存/导出流程**
```cpp
void OnXXXExport() {
    // 1. 验证前置条件
    if (!validator_) return;
    
    // 2. 获取数据
    auto data = getDataFromManager();
    if (data.isEmpty()) return;
    
    // 3. 显示文件对话框
    QString fileName = QFileDialog::getSaveFileName(...);
    if (fileName.isEmpty()) return;
    
    // 4. 记住路径
    lastUsedPath_ = QFileInfo(fileName).absolutePath();
    
    // 5. 执行导出
    bool success = exporter_->exportData(data, fileName);
    
    // 6. 处理结果
    handleExportResult(success, fileName, "操作描述", error);
}
```

### **共同的辅助方法**
- `handleExportResult()` - 统一结果处理
- `AddLogEntry()` - 日志记录
- `QFileDialog::getSaveFileName()` - 文件选择
- `QDateTime::currentDateTime().toString()` - 时间戳生成

## 🔧 **技术实现统一性**

### **导出器架构**
- `XLSDataExporter` - Excel格式导出
- `CSVDataExporter` - CSV格式导出  
- `JSONDataExporter` - JSON格式导出
- `DataExportManager` - 统一管理器

### **数据管理器**
- `SensorDataManager` - 传感器数据
- `ActuatorDataManager` - 作动器数据
- `TestProject` - 项目数据

### **工厂模式**
- `DataExporterFactory` - 根据格式创建导出器
- 支持扩展名自动识别
- 统一的接口 `IDataExporter`

## 📋 **总结**

系统中共有 **12个** 与"保存工程"→保存XLSX类似的流程：

**文件操作类 (3个)**：
1. 保存工程 (OnSaveProject) ⭐
2. 另存为工程 (OnSaveAsProject)  
3. 打开工程 (OnOpenProject)

**数据导出类 (7个)**：
4. 数据导出主菜单 (OnDataExport)
5. 导出到Excel (OnExportToExcel)
6. 导出硬件树到Excel (OnExportHardwareTreeToExcel)
7. 导出传感器详细信息到Excel (OnExportSensorDetailsToExcel)
8. 导出作动器详细信息到Excel (OnExportActuatorDetailsToExcel)
9. 导出完整项目到Excel (OnExportCompleteProjectToExcel)
10. 批量导出多种格式 (OnBatchExportToMultipleFormats)

**新系统类 (2个)**：
11. 新导出系统示例 (onSaveProjectActionTriggered)
12. 统一导出方法 (exportProjectData)

这些流程都遵循相似的设计模式，具有统一的用户体验和技术架构。
