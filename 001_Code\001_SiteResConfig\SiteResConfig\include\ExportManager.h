/**
 * @file ExportManager.h
 * @brief 导入导出管理模块 - 基于现有导出器
 * @details 封装现有的XLSDataExporter_1_2和JSONDataExporter_1_2，提供Excel导入导出和JSON导出接口
 * <AUTHOR> Assistant
 * @date 2025-01-27
 * @version 3.4.0
 */

#ifndef EXPORTMANAGER_H
#define EXPORTMANAGER_H

#include <QObject>
#include <QString>
#include <memory>

// 前向声明
class XLSDataExporter_1_2;
class JSONDataExporter_1_2;
class ActuatorDataManager_1_2;
class SensorDataManager_1_2;
class CtrlChanDataManager;
class HardwareNodeResDataManager;
class QProgressDialog;

/**
 * @brief 导入导出管理器类 - 基于现有导出器
 * @details 封装现有的XLSDataExporter_1_2和JSONDataExporter_1_2，提供Excel导入导出和JSON导出接口
 */
class ExportManager : public QObject {
    Q_OBJECT
    
public:
    explicit ExportManager(QObject* parent = nullptr);
    ~ExportManager();
    
    // 设置现有数据管理器 (不创建新的)
    void setActuatorDataManager(ActuatorDataManager_1_2* manager);
    void setSensorDataManager(SensorDataManager_1_2* manager);
    void setCtrlChanDataManager(CtrlChanDataManager* manager);
    void setHardwareNodeResDataManager(HardwareNodeResDataManager* manager);
    
    // 使用现有XLSDataExporter_1_2进行Excel导入导出
    bool importProjectFromExcel(const QString& filePath);
    bool exportProjectToExcel(const QString& filePath);
    
    // 使用现有JSONDataExporter_1_2进行JSON导出（仅导出，不支持导入）
    bool exportProjectToJSON(const QString& filePath);
    bool exportChannelConfigToJSON(const QString& filePath);
    
    // 项目级导入导出（Excel导入，Excel/JSON导出）
    bool importProject(const QString& filePath, QProgressDialog* progressDialog = nullptr);
    bool exportProject(const QString& filePath, QProgressDialog* progressDialog = nullptr);
    
    // 错误处理
    QString getLastError() const;
    void clearError();
    
signals:
    void importCompleted(const QString& filePath, bool success);
    void exportCompleted(const QString& filePath, bool success);
    void errorOccurred(const QString& error);
    
private:
    // 使用现有导出器 (不创建新的)
    std::unique_ptr<XLSDataExporter_1_2> xlsExporter_;
    std::unique_ptr<JSONDataExporter_1_2> jsonExporter_;
    
    // 引用现有数据管理器 (不创建新的)
    ActuatorDataManager_1_2* actuatorDataManager_;
    SensorDataManager_1_2* sensorDataManager_;
    CtrlChanDataManager* ctrlChanDataManager_;
    HardwareNodeResDataManager* hardwareNodeResDataManager_;
    
    QString lastError_;
    
    // 私有方法
    void initializeExporters();
    QString getFileExtension(const QString& filePath) const;
};

#endif // EXPORTMANAGER_H 