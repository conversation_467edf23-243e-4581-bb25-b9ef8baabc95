QT += core gui widgets

TARGET = test_control_channel_detail_info
TEMPLATE = app

# 源文件
SOURCES += test_control_channel_detail_info.cpp

# 头文件
HEADERS += \
    include/DetailInfoPanel.h \
    include/DataModels_Fixed.h

# 包含路径
INCLUDEPATH += \
    include \
    src

# 库文件路径
LIBS += -L$$PWD/lib

# 编译选项
CONFIG += c++17
CONFIG += debug

# 调试信息
DEFINES += DEBUG

# 输出目录
DESTDIR = bin
OBJECTS_DIR = build/obj
MOC_DIR = build/moc
RCC_DIR = build/rcc
UI_DIR = build/ui

# 清理目标
QMAKE_CLEAN += -r bin build 