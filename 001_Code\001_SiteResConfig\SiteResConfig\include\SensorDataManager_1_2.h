/**
 * @file SensorDataManager_1_2.h
 * @brief 传感器数据管理器1_2版本
 * @details 基于MVVM架构设计的增强版传感器数据管理器，参照ActuatorDataManager_1_2架构
 * <AUTHOR> Assistant
 * @date 2025-08-23
 * @version 1.2.0
 */

#ifndef SENSORDATAMANAGER_1_2_H
#define SENSORDATAMANAGER_1_2_H

#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QJsonObject>
#include <QtCore/QJsonArray>
#include <QtCore/QVector>
#include <QtCore/QMap>
#include <QtCore/QList>
#include <QtCore/QObject>
#include <memory>
#include "SensorDialog_1_2.h"

// 前向声明SensorViewModel_1_2
class SensorViewModel_1_2;

// 前向声明
namespace DataModels {
    class TestProject;
}

/**
 * @brief 传感器数据管理器1_2版本类
 * @details 基于MVVM架构的增强版传感器数据管理器，提供与ActuatorDataManager_1_2对等的接口
 */
class SensorDataManager_1_2 : public QObject {
    Q_OBJECT

public:
    explicit SensorDataManager_1_2();
    ~SensorDataManager_1_2();

    // 🆕 传感器数据管理接口（与作动器对等）
    // bool saveSensorDetailedParams(const UI::SensorParams_1_2& params);
    // UI::SensorParams_1_2 getSensorDetailedParams(const QString& serialNumber) const;
    // bool updateSensorDetailedParams(const QString& serialNumber, const UI::SensorParams_1_2& params);
    // bool removeSensorDetailedParams(const QString& serialNumber);
    // QStringList getAllSensorSerialNumbers() const;
    // QList<UI::SensorParams_1_2> getAllSensorDetailedParams() const;

    // 🆕 新增：带组ID的传感器详细参数管理接口
    bool saveSensorDetailedParamsInGroup(int groupId, const UI::SensorParams_1_2& params);
    UI::SensorParams_1_2 getSensorDetailedParamsInGroup(int groupId, const QString& serialNumber, bool &isHasData) const;
    bool updateSensorDetailedParamsInGroup(int groupId, const QString& serialNumber, const UI::SensorParams_1_2& params);
    bool removeSensorDetailedParamsInGroup(int groupId, const QString& serialNumber);
    QStringList getAllSensorSerialNumbersInGroup(int groupId) const;  // 🆕 新增：组内获取所有序列号
    QList<UI::SensorParams_1_2> getAllSensorDetailedParamsInGroup(int groupId) const;  // 🆕 新增：组内获取所有详细参数

    // 🆕 新增类型转换辅助方法
    static QString sensorTypeToString(const QString& type);
    static QString stringToSensorType(const QString& str);
    static QString unitTypeToString(const QString& unit);
    static QString stringToUnitType(const QString& str);
    static QString polarityToString(const QString& polarity);
    static QString stringToPolarity(const QString& str);

    // 传感器数据操作（基础接口）
    // bool addSensor(const UI::SensorParams_1_2& params);
    bool addSensor(const UI::SensorParams_1_2& params, int groupId);  // 🆕 重载：指定组ID
    // UI::SensorParams_1_2 getSensor(const QString& serialNumber) const;
    // bool updateSensor(const QString& serialNumber, const UI::SensorParams_1_2& params);
    // bool removeSensor(const QString& serialNumber);
    // bool hasSensor(const QString& serialNumber) const;
    
    // 🆕 新增：带组ID的基础传感器操作接口
    UI::SensorParams_1_2 getSensorInGroup(int groupId, const QString& serialNumber) const;
    bool updateSensorInGroup(int groupId, const QString& serialNumber, const UI::SensorParams_1_2& params);
    bool removeSensorInGroup(int groupId, const QString& serialNumber);
    bool hasSensorInGroup(int groupId, const QString& serialNumber) const;

    // 🆕 新增：传感器组管理接口
    bool saveSensorGroup(const UI::SensorGroup_1_2& group);
    UI::SensorGroup_1_2 getSensorGroup(int groupId) const;
    bool updateSensorGroup(int groupId, const UI::SensorGroup_1_2& group);
    bool removeSensorGroup(int groupId);
    QList<UI::SensorGroup_1_2> getAllSensorGroups() const;
    bool hasSensorGroup(int groupId) const;

    // 批量操作
    QList<UI::SensorParams_1_2> getAllSensors() const;
    QList<UI::SensorParams_1_2> getSensorsByType(const QString& sensorType) const;
    QList<UI::SensorParams_1_2> getSensorsByGroup(int groupId) const;
    QList<UI::SensorParams_1_2> getSensorsByUnitType(const QString& unitType) const;
    int getSensorCount() const;
    int getSensorGroupCount() const;

    // 数据验证
    bool validateSensorParams(const UI::SensorParams_1_2& params) const;
    bool validateSensorGroup(const UI::SensorGroup_1_2& group) const;
    bool validateSensorInGroup(const UI::SensorParams_1_2& sensor, const UI::SensorGroup_1_2& group) const; // 🆕 新增：验证单个传感器在组内是否冲突
    QStringList validateAllSensors() const;
    QStringList validateAllSensorGroups() const;

    // 数据导出
    QVector<QStringList> exportToCSVData() const;
    QVector<QStringList> exportGroupsToCSVData() const;
    // 🚫 已注释：独立JSON导出功能已废弃
    // QJsonArray exportToJSONArray() const;

    // 数据统计
    QMap<QString, int> getSensorTypeStatistics() const;
    QMap<QString, int> getUnitTypeStatistics() const;
    QMap<QString, int> getPolarityStatistics() const;
    QStringList getUsedSensorTypes() const;
    QStringList getUsedUnitTypes() const;

    // 🆕 新增：序列号管理
    //QString generateNextSerialNumber(const QString& prefix = "SENSOR") const;
    QString generateNextSerialNumberInGroup(int groupId, const QString& prefix = "传感器_") const;  // 🆕 新增：组内序列号生成
    //bool isSerialNumberUnique(const QString& serialNumber) const;  // 全局唯一性检查（向后兼容）
    bool isSerialNumberUniqueInGroup(const QString& serialNumber, int groupId) const;  // 🆕 新增：组内唯一性检查
    bool isSerialNumberUniqueInGroup(const QString& serialNumber, int groupId, int excludeSensorId) const;  // 🆕 新增：组内唯一性检查（排除指定ID）
    QStringList findDuplicateSerialNumbers() const;

    // 错误处理
    QString getLastError() const;
    bool hasError() const;

    // 数据清理
    void clearAllSensors();
    void clearAllSensorGroups();
    void clearAll();

    // 🆕 组内ID分配方法
    int assignSensorIdInGroup(const UI::SensorGroup_1_2& group) const;
    int findMaxSensorIdInGroup(const UI::SensorGroup_1_2& group) const;

    // 🆕 新增：组内ID递增管理方法
    int getNextSensorIdInGroup(int groupId) const;
    void reassignSensorIdsInGroup(UI::SensorGroup_1_2& group) const;
    int getSensorCountInGroup(int groupId) const;

    // 🆕 新增：数据一致性验证方法（公共接口）
    bool validateStorageConsistency() const;
    QStringList detectStorageInconsistencies() const;
    void syncGroupStorageFromGroupedStorage();

    // 🆕 新增：ID连续性验证方法
    bool validateSensorIdSequence(const UI::SensorGroup_1_2& group) const;
    QStringList validateAllSensorIdSequences() const;

signals:
    // 🆕 新增：传感器数据变化信号
    void sensorDataChanged(const QString& serialNumber, const QString& operation);
    void sensorGroupDataChanged(int groupId, const QString& operation);
    void sensorAssociationChanged(const QString& serialNumber, const QString& channelName);
    
    // 🆕 新增：错误信号
    void errorOccurred(const QString& error);

private:
    mutable QString lastError_;

    // 🔄 修改：分层存储架构 - 按组分层存储传感器数据
    QMap<int, QMap<QString, UI::SensorParams_1_2>> groupedSensorStorage_;  // groupId -> {serialNumber -> SensorParams}
    QMap<int, UI::SensorGroup_1_2> groupStorage_;
    int nextGroupId_;
    int nextSensorId_;  // 🆕 传感器ID计数器

    // 辅助方法
    void clearError() const;
    void setError(const QString& error) const;
    bool isValidSerialNumber(const QString& serialNumber) const;
    bool isValidGroupId(int groupId) const;
    void initializeStorage();
    void updateIdCounters();  // 🆕 新增：更新ID计数器
    
    // 🆕 分层存储辅助方法
    int findGroupIdBySerialNumber(const QString& serialNumber) const;
    QList<UI::SensorParams_1_2> getSensorsByGroupId(int groupId) const;
};

#endif // SENSORDATAMANAGER_1_2_H
