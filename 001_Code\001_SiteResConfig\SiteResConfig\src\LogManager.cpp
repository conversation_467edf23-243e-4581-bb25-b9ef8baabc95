/**
 * @file LogManager.cpp
 * @brief 日志管理模块实现 - 基于现有AddLogEntry方法
 * @details 封装现有的AddLogEntry方法，提供统一的日志管理接口
 * <AUTHOR> Assistant
 * @date 2025-01-27
 * @version 3.4.0
 */

#include "LogManager.h"
#include "MainWindow_Qt_Simple.h"
#include <QDateTime>
#include <QDebug>
#include <QFile>
#include <QTextStream>

LogManager::LogManager(QObject* parent)
    : QObject(parent), mainWindow_(nullptr) {
}

LogManager::~LogManager() {
}

void LogManager::setMainWindow(CMyMainWindow* mainWindow) {
    mainWindow_ = mainWindow;
}

void LogManager::debug(const QString& message) {
    if (mainWindow_) {
        // 调用现有的公共LogMessage方法
        mainWindow_->LogMessage("DEBUG", message);
    }
    
    logs_.append(formatLogEntry("DEBUG", message));
    emit logAdded("DEBUG", message);
}

void LogManager::info(const QString& message) {
    if (mainWindow_) {
        // 调用现有的公共LogMessage方法
        mainWindow_->LogMessage("INFO", message);
    }
    
    logs_.append(formatLogEntry("INFO", message));
    emit logAdded("INFO", message);
}

void LogManager::warning(const QString& message) {
    if (mainWindow_) {
        // 调用现有的公共LogMessage方法
        mainWindow_->LogMessage("WARNING", message);
    }
    
    logs_.append(formatLogEntry("WARNING", message));
    emit logAdded("WARNING", message);
}

void LogManager::error(const QString& message) {
    if (mainWindow_) {
        // 调用现有的公共LogMessage方法
        mainWindow_->LogMessage("ERROR", message);
    }
    
    logs_.append(formatLogEntry("ERROR", message));
    emit logAdded("ERROR", message);
}

void LogManager::success(const QString& message) {
    if (mainWindow_) {
        // 调用现有的公共LogMessage方法
        mainWindow_->LogMessage("SUCCESS", message);
    }
    
    logs_.append(formatLogEntry("SUCCESS", message));
    emit logAdded("SUCCESS", message);
}

// 🆕 新增：清理日志功能
void LogManager::clearLogs() {
    logs_.clear();
    
    // 委托给主界面清理日志显示
    if (mainWindow_) {
        mainWindow_->OnClearLog();
    }
    
    emit logsCleared();
}

// 🆕 新增：保存日志功能
bool LogManager::saveLogs() {
    if (!mainWindow_) {
        return false;
    }
    
    // 委托给主界面保存日志
    mainWindow_->OnSaveLog();
    emit logsSaved();
    return true;
}

// 🆕 新增：保存日志到指定文件
bool LogManager::saveLogsToFile(const QString& filePath) {
    try {
        QFile file(filePath);
        if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
            emit logError(QString("无法打开文件进行写入: %1").arg(filePath));
            return false;
        }
        
        QTextStream out(&file);
        out.setCodec("UTF-8");
        
        // 写入日志头部信息
        out << "=== SiteResConfig 日志文件 ===" << endl;
        out << "生成时间: " << QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss") << endl;
        out << "日志条数: " << logs_.size() << endl;
        out << "==============================" << endl << endl;
        
        // 写入所有日志条目
        for (const QString& log : logs_) {
            out << log << endl;
        }
        
        file.close();
        emit logsSavedToFile(filePath);
        return true;
        
    } catch (const std::exception& e) {
        emit logError(QString("保存日志文件时发生异常: %1").arg(e.what()));
        return false;
    }
}

// 🆕 新增：获取特定级别的日志
QStringList LogManager::getLogsByLevel(const QString& level) const {
    QStringList filteredLogs;
    QString searchPattern = QString("] %1: ").arg(level);
    
    for (const QString& log : logs_) {
        if (log.contains(searchPattern)) {
            filteredLogs.append(log);
        }
    }
    
    return filteredLogs;
}

// 🆕 新增：获取日志统计信息
LogManager::LogStats LogManager::getLogStats() const {
    LogStats stats;
    stats.totalLogs = logs_.size();
    stats.debugLogs = getLogsByLevel("DEBUG").size();
    stats.infoLogs = getLogsByLevel("INFO").size();
    stats.warningLogs = getLogsByLevel("WARNING").size();
    stats.errorLogs = getLogsByLevel("ERROR").size();
    stats.successLogs = getLogsByLevel("SUCCESS").size();
    
    return stats;
}

// 🆕 新增：搜索日志
QStringList LogManager::searchLogs(const QString& keyword) const {
    QStringList matchedLogs;
    
    for (const QString& log : logs_) {
        if (log.contains(keyword, Qt::CaseInsensitive)) {
            matchedLogs.append(log);
        }
    }
    
    return matchedLogs;
}

// 🆕 新增：设置日志级别过滤
void LogManager::setLogLevelFilter(const QStringList& enabledLevels) {
    enabledLogLevels_ = enabledLevels;
    emit logLevelFilterChanged(enabledLevels);
}

// 🆕 新增：检查日志级别是否启用
bool LogManager::isLogLevelEnabled(const QString& level) const {
    return enabledLogLevels_.isEmpty() || enabledLogLevels_.contains(level);
}

QStringList LogManager::getLogs() const {
    return logs_;
}

QString LogManager::formatLogEntry(const QString& level, const QString& message) {
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
    return QString("[%1] %2: %3").arg(timestamp).arg(level).arg(message);
} 