#ifndef TREELINESTYLE_H
#define TREELINESTYLE_H

#include <QProxyStyle>
#include <QStyleOptionViewItem>
#include <QTreeView>
#include <QPainter>

/**
 * @brief 自定义树形控件样式类
 * @details 实现Windows经典风格的树形控件，包括：
 *          - 点状虚线连接
 *          - 加号/减号展开指示器
 *          - 虚线穿过图标中心
 *          - 完全的Windows原生外观
 */
class TreeLineStyle : public QProxyStyle
{
    Q_OBJECT
public:
    explicit TreeLineStyle(QStyle *base = nullptr)
        : QProxyStyle(base)
    {
    }

    // 重写绘制方法
    void drawPrimitive(PrimitiveElement elem,
                       const QStyleOption *opt,
                       QPainter *p,
                       const QWidget *w) const override;

private:
    // 绘制树形连接线
    void drawTreeLines(QPainter *p, const QRect &rect,
                      const QStyleOptionViewItem *vopt,
                      const QTreeView *treeView, bool hasChildren) const;

    // 绘制展开/折叠指示器
    void drawExpandCollapseIndicator(QPainter *p, const QRect &rect, bool expanded) const;
};

#endif // TREELINESTYLE_H
