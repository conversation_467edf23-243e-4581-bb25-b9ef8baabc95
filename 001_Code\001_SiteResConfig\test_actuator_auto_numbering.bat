@echo off
echo ========================================
echo  作动器自动编号和显示信息测试
echo ========================================

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o
if exist "ui_*.h" del ui_*.h

echo.
echo 生成UI头文件...
uic ui\MainWindow.ui -o ui_MainWindow.h
if errorlevel 1 (
    echo UI文件生成失败！
    pause
    exit /b 1
) else (
    echo ✅ UI头文件生成成功: ui_MainWindow.h
)

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo.
    echo 新增功能：
    echo ✅ 作动器参数界面显示信息
    echo ✅ 自动编号生成功能
    echo ✅ 序列号默认值设置
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！作动器自动编号功能已实现
    echo ========================================
    
    if exist "SiteResConfig.exe" (
        echo 可执行文件: SiteResConfig.exe
        echo.
        echo ✅ 作动器自动编号和显示信息功能已实现！
        echo.
        echo 📋 参数输入界面改进:
        echo.
        echo 🏷️ 显示信息格式:
        echo "作动器组名称\作动器_000001"
        echo.
        echo 示例显示信息:
        echo ├─ "作动器组（选择要添加作动器的作动器组名称）：50kN_作动器\作动器_000001"
        echo ├─ "作动器组（选择要添加作动器的作动器组名称）：100kN_作动器\作动器_000002"
        echo ├─ "作动器组（选择要添加作动器的作动器组名称）：200kN_作动器\作动器_000003"
        echo └─ "作动器组（选择要添加作动器的作动器组名称）：500kN_作动器\作动器_000004"
        echo.
        echo 🔢 自动编号规则:
        echo - 格式: "作动器_XXXXXX" (6位数字，前导零填充)
        echo - 编号: 根据当前组中作动器数量自动递增
        echo - 起始: 作动器_000001
        echo - 递增: 作动器_000002, 作动器_000003, ...
        echo.
        echo 🎨 界面设计:
        echo ┌─────────────────────────────────────────────────────┐
        echo │                新建作动器                            │
        echo ├─────────────────────────────────────────────────────┤
        echo │ 作动器组（选择要添加作动器的作动器组名称）：          │
        echo │           100kN_作动器\作动器_000001                │
        echo ├─────────────────────────────────────────────────────┤
        echo │ 序列号: [作动器_000001            ]     │
        echo │ 类型:   [单出杆 ▼]                     │
        echo │ 缸径:   [0.10] m                       │
        echo │ 杆径:   [0.05] m                       │
        echo │ 行程:   [0.20] m                       │
        echo │                                         │
        echo │              [确定] [取消]              │
        echo └─────────────────────────────────────────┘
        echo.
        echo 🎯 测试场景:
        echo.
        echo 场景1: 在空的作动器组中创建第一个作动器
        echo - 显示: "100kN_作动器\作动器_000001"
        echo - 默认序列号: "作动器_000001"
        echo.
        echo 场景2: 在已有1个作动器的组中创建第二个
        echo - 显示: "100kN_作动器\作动器_000002"
        echo - 默认序列号: "作动器_000002"
        echo.
        echo 场景3: 在已有5个作动器的组中创建第六个
        echo - 显示: "100kN_作动器\作动器_000006"
        echo - 默认序列号: "作动器_000006"
        echo.
        echo 🔧 技术实现:
        echo - 动态获取作动器组名称: groupItem->text(0)
        echo - 计算当前数量: groupItem->childCount() + 1
        echo - 格式化编号: QString("作动器_%1").arg(count, 6, 10, QChar('0'))
        echo - 显示格式: QString("%1\\%2").arg(groupName).arg(autoNumber)
        echo.
        echo 💡 用户体验优势:
        echo - 清晰显示当前位置和编号
        echo - 自动生成唯一序列号
        echo - 避免手动输入错误
        echo - 保持编号连续性
        echo - 视觉层次清晰
        echo.
        echo 启动程序测试作动器自动编号功能...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
        echo 启动程序...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
        echo 启动程序...
        start release\SiteResConfig.exe
    ) else (
        echo 警告: 找不到可执行文件！
    )
)

echo.
echo 📖 作动器自动编号测试指南:
echo.
echo 🎯 完整测试流程:
echo.
echo 1️⃣ 创建作动器组:
echo   - 右键"作动器"节点
echo   - 选择"新建" → "作动器组"
echo   - 选择"100kN_作动器"
echo   - 验证组创建成功
echo.
echo 2️⃣ 创建第一个作动器:
echo   - 右键"100kN_作动器"组
echo   - 选择"新建" → "作动器"
echo   - 验证显示信息: "作动器组（选择要添加作动器的作动器组名称）：100kN_作动器\作动器_000001"
echo   - 验证序列号默认值: "作动器_000001"
echo   - 填写其他参数并确定
echo.
echo 3️⃣ 创建第二个作动器:
echo   - 再次右键"100kN_作动器"组
echo   - 选择"新建" → "作动器"
echo   - 验证显示信息: "100kN_作动器\作动器_000002"
echo   - 验证序列号默认值: "作动器_000002"
echo   - 确定创建
echo.
echo 4️⃣ 创建第三个作动器:
echo   - 重复上述步骤
echo   - 验证显示信息: "100kN_作动器\作动器_000003"
echo   - 验证编号递增正确
echo.
echo 🔍 验证要点:
echo - 显示信息格式正确 (组名\作动器_XXXXXX)
echo - 编号自动递增 (000001, 000002, 000003...)
echo - 序列号默认值与编号一致
echo - 不同组的编号独立计算
echo - 界面布局美观清晰
echo - 分隔线和样式正确显示
echo.
echo 🎨 界面验证:
echo - 显示信息居中对齐
echo - 蓝色粗体字体样式
echo - 分隔线正确显示
echo - 对话框尺寸适当 (450x350)
echo - 控件布局合理
echo.
pause
