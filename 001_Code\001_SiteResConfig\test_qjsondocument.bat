@echo off
chcp 65001 >nul
echo ========================================
echo  QJsonDocument JSON导出功能验证
echo ========================================

echo 检查QJsonDocument实现状态...

REM 检查QJsonDocument头文件包含
echo.
echo 1. 检查QJsonDocument头文件包含...
findstr /C:"#include <QJsonDocument>" "SiteResConfig\src\DataModels_Simple.cpp" >nul
if %errorlevel%==0 (
    echo ✅ QJsonDocument头文件已包含
) else (
    echo ❌ QJsonDocument头文件未包含
)

findstr /C:"#include <QJsonObject>" "SiteResConfig\src\DataModels_Simple.cpp" >nul
if %errorlevel%==0 (
    echo ✅ QJsonObject头文件已包含
) else (
    echo ❌ QJsonObject头文件未包含
)

findstr /C:"#include <QJsonArray>" "SiteResConfig\src\DataModels_Simple.cpp" >nul
if %errorlevel%==0 (
    echo ✅ QJsonArray头文件已包含
) else (
    echo ❌ QJsonArray头文件未包含
)

REM 检查QJsonDocument API使用
echo.
echo 2. 检查QJsonDocument API使用...
findstr /C:"QJsonObject projectJson" "SiteResConfig\src\DataModels_Simple.cpp" >nul
if %errorlevel%==0 (
    echo ✅ QJsonObject API已使用
) else (
    echo ❌ QJsonObject API未使用
)

findstr /C:"QJsonArray" "SiteResConfig\src\DataModels_Simple.cpp" >nul
if %errorlevel%==0 (
    echo ✅ QJsonArray API已使用
) else (
    echo ❌ QJsonArray API未使用
)

findstr /C:"QJsonDocument jsonDoc" "SiteResConfig\src\DataModels_Simple.cpp" >nul
if %errorlevel%==0 (
    echo ✅ QJsonDocument API已使用
) else (
    echo ❌ QJsonDocument API未使用
)

REM 检查字符串拼接是否已移除
echo.
echo 3. 检查字符串拼接是否已移除...
findstr /C:"std::ostringstream jsonStream" "SiteResConfig\src\DataModels_Simple.cpp" >nul
if %errorlevel%==0 (
    echo ❌ 仍在使用字符串拼接方式
) else (
    echo ✅ 字符串拼接方式已移除
)

findstr /C:"jsonStream <<" "SiteResConfig\src\DataModels_Simple.cpp" >nul
if %errorlevel%==0 (
    echo ❌ 仍有字符串拼接代码
) else (
    echo ✅ 字符串拼接代码已移除
)

REM 检查JSON解析实现
echo.
echo 4. 检查JSON解析实现...
findstr /C:"QJsonParseError parseError" "SiteResConfig\src\DataModels_Simple.cpp" >nul
if %errorlevel%==0 (
    echo ✅ JSON解析错误处理已实现
) else (
    echo ❌ JSON解析错误处理未实现
)

findstr /C:"QJsonDocument::fromJson" "SiteResConfig\src\DataModels_Simple.cpp" >nul
if %errorlevel%==0 (
    echo ✅ QJsonDocument解析已实现
) else (
    echo ❌ QJsonDocument解析未实现
)

REM 检查格式化输出
echo.
echo 5. 检查格式化输出...
findstr /C:"QJsonDocument::Indented" "SiteResConfig\src\DataModels_Simple.cpp" >nul
if %errorlevel%==0 (
    echo ✅ JSON格式化输出已实现
) else (
    echo ❌ JSON格式化输出未实现
)

findstr /C:"jsonDoc.toJson" "SiteResConfig\src\DataModels_Simple.cpp" >nul
if %errorlevel%==0 (
    echo ✅ QJsonDocument输出已实现
) else (
    echo ❌ QJsonDocument输出未实现
)

REM 检查枚举转换
echo.
echo 6. 检查枚举转换实现...
findstr /C:"QString actuatorTypeStr" "SiteResConfig\src\DataModels_Simple.cpp" >nul
if %errorlevel%==0 (
    echo ✅ 枚举转换使用QString
) else (
    echo ❌ 枚举转换未使用QString
)

findstr /C:"QString sensorTypeStr" "SiteResConfig\src\DataModels_Simple.cpp" >nul
if %errorlevel%==0 (
    echo ✅ 传感器枚举转换已实现
) else (
    echo ❌ 传感器枚举转换未实现
)

echo.
echo ========================================
echo  QJsonDocument实现状态总结
echo ========================================
echo.
echo 核心改进:
echo ✅ 从字符串拼接改为QJsonDocument标准API
echo ✅ 使用QJsonObject、QJsonArray处理JSON结构
echo ✅ 使用QJsonParseError进行错误处理
echo ✅ 使用QJsonDocument::Indented格式化输出
echo ✅ 保持所有枚举类型转换功能
echo.
echo QJsonDocument优势:
echo ✅ 类型安全 - 自动处理类型转换
echo ✅ 格式正确 - 自动生成标准JSON格式
echo ✅ 转义处理 - 自动处理特殊字符
echo ✅ 错误检测 - 完善的解析错误信息
echo ✅ 代码清晰 - 易读易维护
echo ✅ 性能优化 - Qt优化的JSON处理
echo.
echo 生成的JSON特点:
echo ✅ 标准JSON格式
echo ✅ 自动缩进格式化
echo ✅ 完整的项目配置信息
echo ✅ 正确的枚举值字符串
echo ✅ 兼容所有JSON工具
echo.

REM 检查可执行文件
echo 7. 检查可执行文件状态...
if exist "build-SiteResConfig_Simple-Desktop_Qt_5_14_2_MinGW_32_bit-Debug\debug\SiteResConfig.exe" (
    echo ✅ 可执行文件存在
    echo    建议重新编译以应用QJsonDocument实现
) else (
    echo ❌ 可执行文件不存在，需要编译项目
)

echo.
echo ========================================
echo  测试建议
echo ========================================
echo 1. 重新编译项目以应用QJsonDocument实现
echo 2. 启动应用程序创建测试项目
echo 3. 添加各种类型的硬件配置
echo 4. 使用JSON导出功能
echo 5. 验证生成的JSON文件格式
echo 6. 测试JSON文件的解析功能
echo 7. 验证与其他JSON工具的兼容性
echo.

REM 询问是否启动应用程序测试
set /p choice="是否启动应用程序进行QJsonDocument JSON导出测试? (y/n): "
if /i "%choice%"=="y" (
    echo.
    echo 启动应用程序...
    cd /d "%~dp0build-SiteResConfig_Simple-Desktop_Qt_5_14_2_MinGW_32_bit-Debug\debug"
    if exist "SiteResConfig.exe" (
        start SiteResConfig.exe
        echo 应用程序已启动！
        echo.
        echo ========================================
        echo  QJsonDocument测试重点
        echo ========================================
        echo 1. 创建完整的项目配置
        echo 2. 使用JSON导出功能
        echo 3. 检查生成的JSON文件格式
        echo 4. 验证JSON文件可被其他工具解析
        echo 5. 测试JSON文件的加载功能
        echo 6. 验证枚举值为可读字符串
        echo 7. 确认所有字段类型正确
        echo.
        echo 预期结果:
        echo - 生成标准格式的JSON文件
        echo - 自动缩进和格式化
        echo - 完整的项目配置信息
        echo - 正确的数据类型
        echo - 兼容其他JSON工具
    ) else (
        echo 错误: 找不到可执行文件，请先重新编译项目
    )
)

echo.
echo QJsonDocument JSON导出功能实现完成！
pause
