@echo off
chcp 65001 > nul
echo ========================================
echo Windows加号/减号树形控件测试
echo ========================================
echo.

echo 🔧 正在编译加号/减号样式...
cd SiteResConfig
qmake SiteResConfig_Simple.pro
mingw32-make clean
mingw32-make

if %ERRORLEVEL% NEQ 0 (
    echo ❌ 编译失败！
    pause
    exit /b 1
)

echo ✅ 编译成功！
echo.

echo 🚀 启动应用程序进行加号/减号图标测试...
echo.
echo 📋 **Windows经典加号/减号树形控件设计**：
echo.
echo 🎯 **设计特点**：
echo - 使用传统的Windows加号/减号展开指示器
echo - 保持Windows原生的方形按钮样式
echo - 经典的白色按钮背景，黑色符号
echo.
echo 🎨 **加号/减号按钮特征**：
echo.
echo 1️⃣ **按钮外观**
echo    - 9x9像素方形按钮
echo    - 白色背景 (#FFFFFF)
echo    - 灰色边框 (#808080)
echo    - 无圆角（Windows经典风格）
echo.
echo 2️⃣ **符号设计**
echo    - 折叠状态：黑色加号 (+)
echo    - 展开状态：黑色减号 (-)
echo    - 使用Courier New等宽字体
echo    - 7pt字体大小，粗体显示
echo    - 居中对齐
echo.
echo 3️⃣ **交互效果**
echo    - 悬停时：浅蓝色背景 (#E1ECFF)
echo    - 悬停时：蓝色边框 (#0078D4)
echo    - 点击响应：展开/折叠节点
echo    - 平滑的视觉反馈
echo.
echo 4️⃣ **禁用状态**
echo    - 按钮背景：浅灰色 (#F5F5F5)
echo    - 边框颜色：灰色 (#C0C0C0)
echo    - 符号颜色：浅灰色 (#A0A0A0)
echo    - 不响应悬停和点击
echo.
echo 5️⃣ **连接线系统**
echo    - 细实线连接 (1px #C0C0C0)
echo    - 垂直线和水平线
echo    - L型连接线
echo    - 与按钮完美对齐
echo.

start "" "debug\SiteResConfig.exe"

echo 📊 应用程序已启动！
echo.
echo 🔍 **详细验证清单**：
echo.
echo ☐ 1. **加号按钮验证**
echo      - 有子节点的折叠节点显示方形按钮
echo      - 按钮内显示黑色加号 (+)
echo      - 按钮为9x9像素大小
echo      - 白色背景，灰色边框
echo.
echo ☐ 2. **减号按钮验证**
echo      - 有子节点的展开节点显示方形按钮
echo      - 按钮内显示黑色减号 (-)
echo      - 按钮大小与加号按钮一致
echo      - 样式保持一致
echo.
echo ☐ 3. **交互功能验证**
echo      - 点击加号按钮可展开节点
echo      - 点击减号按钮可折叠节点
echo      - 悬停时按钮背景变为浅蓝色
echo      - 悬停时边框变为蓝色
echo.
echo ☐ 4. **符号显示验证**
echo      - 加号和减号居中显示
echo      - 符号清晰可见
echo      - 字体为等宽字体（Courier New）
echo      - 符号大小适中，不会溢出按钮
echo.
echo ☐ 5. **连接线验证**
echo      - 连接线与按钮正确对齐
echo      - 垂直线和水平线连接正确
echo      - L型连接线显示正确
echo      - 线条颜色为浅灰色
echo.
echo ☐ 6. **禁用状态验证**
echo      - 禁用的树形控件按钮显示灰色
echo      - 禁用状态下符号为浅灰色
echo      - 禁用按钮不响应悬停
echo      - 禁用按钮不响应点击
echo.
echo 💡 **Windows经典特征**：
echo - ✅ 方形按钮（无圆角）
echo - ✅ 加号/减号符号
echo - ✅ 白色按钮背景
echo - ✅ 灰色边框
echo - ✅ 蓝色悬停效果
echo - ✅ 等宽字体符号
echo.
echo 🎉 **成功标志**：
echo - 所有有子节点的节点显示加号/减号按钮
echo - 按钮样式符合Windows经典设计
echo - 交互效果流畅自然
echo - 符号清晰可见，居中对齐
echo - 与整体样式协调统一
echo.
echo 🎉 **设计优势**：
echo - 经典的Windows用户体验
echo - 清晰的视觉层次结构
echo - 直观的展开/折叠指示
echo - 专业的企业级外观
echo.
echo 🎉 如果以上验证都通过，说明加号/减号树形控件完美实现！
echo.
pause
