/********************************************************************************
** Form generated from reading UI file 'SensorDialog_1_2.ui'
**
** Created by: Qt User Interface Compiler version 5.14.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_SENSORDIALOG_1_2_H
#define UI_SENSORDIALOG_1_2_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QCheckBox>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QDialog>
#include <QtWidgets/QDoubleSpinBox>
#include <QtWidgets/QFormLayout>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QVBoxLayout>

QT_BEGIN_NAMESPACE

class Ui_SensorDialog_1_2
{
public:
    QVBoxLayout *verticalLayout;
    QLabel *infoLabel;
    QGroupBox *basicGroupBox;
    QFormLayout *basicFormLayout;
    QLabel *zeroOffsetLabel;
    QDoubleSpinBox *zeroOffsetSpinBox;
    QLabel *enableLabel;
    QCheckBox *enableCheckBox;
    QGroupBox *paramsGroupBox;
    QFormLayout *paramsFormLayout;
    QLabel *paramsModelLabel;
    QLineEdit *paramsModelEdit;
    QLabel *paramsSnLabel;
    QLineEdit *paramsSnEdit;
    QLabel *paramsKLabel;
    QDoubleSpinBox *paramsKSpinBox;
    QLabel *paramsBLabel;
    QDoubleSpinBox *paramsBSpinBox;
    QLabel *paramsPrecisionLabel;
    QDoubleSpinBox *paramsPrecisionSpinBox;
    QLabel *paramsPolarityLabel;
    QComboBox *paramsPolarityCombo;
    QLabel *paramsMeasUnitLabel;
    QSpinBox *paramsMeasUnitSpinBox;
    QLabel *paramsMeasRangeMinLabel;
    QDoubleSpinBox *paramsMeasRangeMinSpinBox;
    QLabel *paramsMeasRangeMaxLabel;
    QDoubleSpinBox *paramsMeasRangeMaxSpinBox;
    QLabel *paramsOutputSignalUnitLabel;
    QSpinBox *paramsOutputSignalUnitSpinBox;
    QLabel *paramsOutputSignalRangeMinLabel;
    QDoubleSpinBox *paramsOutputSignalRangeMinSpinBox;
    QLabel *paramsOutputSignalRangeMaxLabel;
    QDoubleSpinBox *paramsOutputSignalRangeMaxSpinBox;
    QHBoxLayout *buttonLayout;
    QSpacerItem *horizontalSpacer;
    QPushButton *okButton;
    QPushButton *cancelButton;

    void setupUi(QDialog *SensorDialog_1_2)
    {
        if (SensorDialog_1_2->objectName().isEmpty())
            SensorDialog_1_2->setObjectName(QString::fromUtf8("SensorDialog_1_2"));
        SensorDialog_1_2->resize(522, 568);
        SensorDialog_1_2->setModal(true);
        verticalLayout = new QVBoxLayout(SensorDialog_1_2);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        infoLabel = new QLabel(SensorDialog_1_2);
        infoLabel->setObjectName(QString::fromUtf8("infoLabel"));
        QFont font;
        font.setBold(true);
        font.setWeight(75);
        infoLabel->setFont(font);
        infoLabel->setAlignment(Qt::AlignCenter);

        verticalLayout->addWidget(infoLabel);

        basicGroupBox = new QGroupBox(SensorDialog_1_2);
        basicGroupBox->setObjectName(QString::fromUtf8("basicGroupBox"));
        basicFormLayout = new QFormLayout(basicGroupBox);
        basicFormLayout->setObjectName(QString::fromUtf8("basicFormLayout"));
        zeroOffsetLabel = new QLabel(basicGroupBox);
        zeroOffsetLabel->setObjectName(QString::fromUtf8("zeroOffsetLabel"));

        basicFormLayout->setWidget(0, QFormLayout::LabelRole, zeroOffsetLabel);

        zeroOffsetSpinBox = new QDoubleSpinBox(basicGroupBox);
        zeroOffsetSpinBox->setObjectName(QString::fromUtf8("zeroOffsetSpinBox"));
        zeroOffsetSpinBox->setDecimals(6);
        zeroOffsetSpinBox->setMinimum(-999999.000000000000000);
        zeroOffsetSpinBox->setMaximum(999999.000000000000000);
        zeroOffsetSpinBox->setValue(0.000000000000000);

        basicFormLayout->setWidget(0, QFormLayout::FieldRole, zeroOffsetSpinBox);

        enableLabel = new QLabel(basicGroupBox);
        enableLabel->setObjectName(QString::fromUtf8("enableLabel"));

        basicFormLayout->setWidget(1, QFormLayout::LabelRole, enableLabel);

        enableCheckBox = new QCheckBox(basicGroupBox);
        enableCheckBox->setObjectName(QString::fromUtf8("enableCheckBox"));
        enableCheckBox->setChecked(true);

        basicFormLayout->setWidget(1, QFormLayout::FieldRole, enableCheckBox);


        verticalLayout->addWidget(basicGroupBox);

        paramsGroupBox = new QGroupBox(SensorDialog_1_2);
        paramsGroupBox->setObjectName(QString::fromUtf8("paramsGroupBox"));
        paramsFormLayout = new QFormLayout(paramsGroupBox);
        paramsFormLayout->setObjectName(QString::fromUtf8("paramsFormLayout"));
        paramsModelLabel = new QLabel(paramsGroupBox);
        paramsModelLabel->setObjectName(QString::fromUtf8("paramsModelLabel"));

        paramsFormLayout->setWidget(0, QFormLayout::LabelRole, paramsModelLabel);

        paramsModelEdit = new QLineEdit(paramsGroupBox);
        paramsModelEdit->setObjectName(QString::fromUtf8("paramsModelEdit"));

        paramsFormLayout->setWidget(0, QFormLayout::FieldRole, paramsModelEdit);

        paramsSnLabel = new QLabel(paramsGroupBox);
        paramsSnLabel->setObjectName(QString::fromUtf8("paramsSnLabel"));

        paramsFormLayout->setWidget(1, QFormLayout::LabelRole, paramsSnLabel);

        paramsSnEdit = new QLineEdit(paramsGroupBox);
        paramsSnEdit->setObjectName(QString::fromUtf8("paramsSnEdit"));

        paramsFormLayout->setWidget(1, QFormLayout::FieldRole, paramsSnEdit);

        paramsKLabel = new QLabel(paramsGroupBox);
        paramsKLabel->setObjectName(QString::fromUtf8("paramsKLabel"));

        paramsFormLayout->setWidget(2, QFormLayout::LabelRole, paramsKLabel);

        paramsKSpinBox = new QDoubleSpinBox(paramsGroupBox);
        paramsKSpinBox->setObjectName(QString::fromUtf8("paramsKSpinBox"));
        paramsKSpinBox->setDecimals(6);
        paramsKSpinBox->setMinimum(-999999.000000000000000);
        paramsKSpinBox->setMaximum(999999.000000000000000);
        paramsKSpinBox->setValue(20.000000000000000);

        paramsFormLayout->setWidget(2, QFormLayout::FieldRole, paramsKSpinBox);

        paramsBLabel = new QLabel(paramsGroupBox);
        paramsBLabel->setObjectName(QString::fromUtf8("paramsBLabel"));

        paramsFormLayout->setWidget(3, QFormLayout::LabelRole, paramsBLabel);

        paramsBSpinBox = new QDoubleSpinBox(paramsGroupBox);
        paramsBSpinBox->setObjectName(QString::fromUtf8("paramsBSpinBox"));
        paramsBSpinBox->setDecimals(6);
        paramsBSpinBox->setMinimum(-999999.000000000000000);
        paramsBSpinBox->setMaximum(999999.000000000000000);
        paramsBSpinBox->setValue(0.000000000000000);

        paramsFormLayout->setWidget(3, QFormLayout::FieldRole, paramsBSpinBox);

        paramsPrecisionLabel = new QLabel(paramsGroupBox);
        paramsPrecisionLabel->setObjectName(QString::fromUtf8("paramsPrecisionLabel"));

        paramsFormLayout->setWidget(4, QFormLayout::LabelRole, paramsPrecisionLabel);

        paramsPrecisionSpinBox = new QDoubleSpinBox(paramsGroupBox);
        paramsPrecisionSpinBox->setObjectName(QString::fromUtf8("paramsPrecisionSpinBox"));
        paramsPrecisionSpinBox->setDecimals(6);
        paramsPrecisionSpinBox->setMinimum(0.000001000000000);
        paramsPrecisionSpinBox->setMaximum(999999.000000000000000);
        paramsPrecisionSpinBox->setValue(0.100000000000000);

        paramsFormLayout->setWidget(4, QFormLayout::FieldRole, paramsPrecisionSpinBox);

        paramsPolarityLabel = new QLabel(paramsGroupBox);
        paramsPolarityLabel->setObjectName(QString::fromUtf8("paramsPolarityLabel"));

        paramsFormLayout->setWidget(5, QFormLayout::LabelRole, paramsPolarityLabel);

        paramsPolarityCombo = new QComboBox(paramsGroupBox);
        paramsPolarityCombo->setObjectName(QString::fromUtf8("paramsPolarityCombo"));

        paramsFormLayout->setWidget(5, QFormLayout::FieldRole, paramsPolarityCombo);

        paramsMeasUnitLabel = new QLabel(paramsGroupBox);
        paramsMeasUnitLabel->setObjectName(QString::fromUtf8("paramsMeasUnitLabel"));

        paramsFormLayout->setWidget(6, QFormLayout::LabelRole, paramsMeasUnitLabel);

        paramsMeasUnitSpinBox = new QSpinBox(paramsGroupBox);
        paramsMeasUnitSpinBox->setObjectName(QString::fromUtf8("paramsMeasUnitSpinBox"));
        paramsMeasUnitSpinBox->setMinimum(0);
        paramsMeasUnitSpinBox->setMaximum(999);
        paramsMeasUnitSpinBox->setValue(1);

        paramsFormLayout->setWidget(6, QFormLayout::FieldRole, paramsMeasUnitSpinBox);

        paramsMeasRangeMinLabel = new QLabel(paramsGroupBox);
        paramsMeasRangeMinLabel->setObjectName(QString::fromUtf8("paramsMeasRangeMinLabel"));

        paramsFormLayout->setWidget(7, QFormLayout::LabelRole, paramsMeasRangeMinLabel);

        paramsMeasRangeMinSpinBox = new QDoubleSpinBox(paramsGroupBox);
        paramsMeasRangeMinSpinBox->setObjectName(QString::fromUtf8("paramsMeasRangeMinSpinBox"));
        paramsMeasRangeMinSpinBox->setDecimals(3);
        paramsMeasRangeMinSpinBox->setMinimum(-999999.000000000000000);
        paramsMeasRangeMinSpinBox->setMaximum(999999.000000000000000);
        paramsMeasRangeMinSpinBox->setValue(-100.000000000000000);

        paramsFormLayout->setWidget(7, QFormLayout::FieldRole, paramsMeasRangeMinSpinBox);

        paramsMeasRangeMaxLabel = new QLabel(paramsGroupBox);
        paramsMeasRangeMaxLabel->setObjectName(QString::fromUtf8("paramsMeasRangeMaxLabel"));

        paramsFormLayout->setWidget(8, QFormLayout::LabelRole, paramsMeasRangeMaxLabel);

        paramsMeasRangeMaxSpinBox = new QDoubleSpinBox(paramsGroupBox);
        paramsMeasRangeMaxSpinBox->setObjectName(QString::fromUtf8("paramsMeasRangeMaxSpinBox"));
        paramsMeasRangeMaxSpinBox->setDecimals(3);
        paramsMeasRangeMaxSpinBox->setMinimum(-999999.000000000000000);
        paramsMeasRangeMaxSpinBox->setMaximum(999999.000000000000000);
        paramsMeasRangeMaxSpinBox->setValue(100.000000000000000);

        paramsFormLayout->setWidget(8, QFormLayout::FieldRole, paramsMeasRangeMaxSpinBox);

        paramsOutputSignalUnitLabel = new QLabel(paramsGroupBox);
        paramsOutputSignalUnitLabel->setObjectName(QString::fromUtf8("paramsOutputSignalUnitLabel"));

        paramsFormLayout->setWidget(9, QFormLayout::LabelRole, paramsOutputSignalUnitLabel);

        paramsOutputSignalUnitSpinBox = new QSpinBox(paramsGroupBox);
        paramsOutputSignalUnitSpinBox->setObjectName(QString::fromUtf8("paramsOutputSignalUnitSpinBox"));
        paramsOutputSignalUnitSpinBox->setMinimum(0);
        paramsOutputSignalUnitSpinBox->setMaximum(999);
        paramsOutputSignalUnitSpinBox->setValue(1);

        paramsFormLayout->setWidget(9, QFormLayout::FieldRole, paramsOutputSignalUnitSpinBox);

        paramsOutputSignalRangeMinLabel = new QLabel(paramsGroupBox);
        paramsOutputSignalRangeMinLabel->setObjectName(QString::fromUtf8("paramsOutputSignalRangeMinLabel"));

        paramsFormLayout->setWidget(10, QFormLayout::LabelRole, paramsOutputSignalRangeMinLabel);

        paramsOutputSignalRangeMinSpinBox = new QDoubleSpinBox(paramsGroupBox);
        paramsOutputSignalRangeMinSpinBox->setObjectName(QString::fromUtf8("paramsOutputSignalRangeMinSpinBox"));
        paramsOutputSignalRangeMinSpinBox->setDecimals(3);
        paramsOutputSignalRangeMinSpinBox->setMinimum(-999999.000000000000000);
        paramsOutputSignalRangeMinSpinBox->setMaximum(999999.000000000000000);
        paramsOutputSignalRangeMinSpinBox->setValue(-100.000000000000000);

        paramsFormLayout->setWidget(10, QFormLayout::FieldRole, paramsOutputSignalRangeMinSpinBox);

        paramsOutputSignalRangeMaxLabel = new QLabel(paramsGroupBox);
        paramsOutputSignalRangeMaxLabel->setObjectName(QString::fromUtf8("paramsOutputSignalRangeMaxLabel"));

        paramsFormLayout->setWidget(11, QFormLayout::LabelRole, paramsOutputSignalRangeMaxLabel);

        paramsOutputSignalRangeMaxSpinBox = new QDoubleSpinBox(paramsGroupBox);
        paramsOutputSignalRangeMaxSpinBox->setObjectName(QString::fromUtf8("paramsOutputSignalRangeMaxSpinBox"));
        paramsOutputSignalRangeMaxSpinBox->setDecimals(3);
        paramsOutputSignalRangeMaxSpinBox->setMinimum(-999999.000000000000000);
        paramsOutputSignalRangeMaxSpinBox->setMaximum(999999.000000000000000);
        paramsOutputSignalRangeMaxSpinBox->setValue(100.000000000000000);

        paramsFormLayout->setWidget(11, QFormLayout::FieldRole, paramsOutputSignalRangeMaxSpinBox);


        verticalLayout->addWidget(paramsGroupBox);

        buttonLayout = new QHBoxLayout();
        buttonLayout->setObjectName(QString::fromUtf8("buttonLayout"));
        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        buttonLayout->addItem(horizontalSpacer);

        okButton = new QPushButton(SensorDialog_1_2);
        okButton->setObjectName(QString::fromUtf8("okButton"));

        buttonLayout->addWidget(okButton);

        cancelButton = new QPushButton(SensorDialog_1_2);
        cancelButton->setObjectName(QString::fromUtf8("cancelButton"));

        buttonLayout->addWidget(cancelButton);


        verticalLayout->addLayout(buttonLayout);


        retranslateUi(SensorDialog_1_2);
        QObject::connect(okButton, SIGNAL(clicked()), SensorDialog_1_2, SLOT(accept()));
        QObject::connect(cancelButton, SIGNAL(clicked()), SensorDialog_1_2, SLOT(reject()));

        okButton->setDefault(true);


        QMetaObject::connectSlotsByName(SensorDialog_1_2);
    } // setupUi

    void retranslateUi(QDialog *SensorDialog_1_2)
    {
        SensorDialog_1_2->setWindowTitle(QCoreApplication::translate("SensorDialog_1_2", "\344\274\240\346\204\237\345\231\250\351\205\215\347\275\256 1_2 - \346\226\260\351\234\200\346\261\202", nullptr));
        infoLabel->setText(QCoreApplication::translate("SensorDialog_1_2", "\344\274\240\346\204\237\345\231\250\347\273\204\\\344\274\240\346\204\237\345\231\250_000001", nullptr));
        basicGroupBox->setTitle(QCoreApplication::translate("SensorDialog_1_2", "\345\237\272\347\241\200\351\205\215\347\275\256 (2\345\255\227\346\256\265)", nullptr));
        zeroOffsetLabel->setText(QCoreApplication::translate("SensorDialog_1_2", "\351\233\266\347\202\271\345\201\217\347\247\273:", nullptr));
        enableLabel->setText(QCoreApplication::translate("SensorDialog_1_2", "\344\275\277\350\203\275\347\212\266\346\200\201:", nullptr));
        enableCheckBox->setText(QCoreApplication::translate("SensorDialog_1_2", "\345\220\257\347\224\250\344\274\240\346\204\237\345\231\250", nullptr));
        paramsGroupBox->setTitle(QCoreApplication::translate("SensorDialog_1_2", "\344\274\240\346\204\237\345\231\250\345\217\202\346\225\260\351\205\215\347\275\256 (12\345\255\227\346\256\265)", nullptr));
        paramsModelLabel->setText(QCoreApplication::translate("SensorDialog_1_2", "\345\236\213\345\217\267:", nullptr));
        paramsModelEdit->setPlaceholderText(QCoreApplication::translate("SensorDialog_1_2", "\350\257\267\350\276\223\345\205\245\344\274\240\346\204\237\345\231\250\345\236\213\345\217\267 (\345\246\202: AKD-8A)", nullptr));
        paramsSnLabel->setText(QCoreApplication::translate("SensorDialog_1_2", "\345\272\217\345\210\227\345\217\267:", nullptr));
        paramsSnEdit->setPlaceholderText(QCoreApplication::translate("SensorDialog_1_2", "\350\257\267\350\276\223\345\205\245\345\272\217\345\210\227\345\217\267 (\345\246\202: 2223)", nullptr));
        paramsKLabel->setText(QCoreApplication::translate("SensorDialog_1_2", "\347\263\273\346\225\260K:", nullptr));
        paramsBLabel->setText(QCoreApplication::translate("SensorDialog_1_2", "\347\263\273\346\225\260B:", nullptr));
        paramsPrecisionLabel->setText(QCoreApplication::translate("SensorDialog_1_2", "\347\262\276\345\272\246:", nullptr));
        paramsPolarityLabel->setText(QCoreApplication::translate("SensorDialog_1_2", "\346\236\201\346\200\247:", nullptr));
        paramsMeasUnitLabel->setText(QCoreApplication::translate("SensorDialog_1_2", "\346\265\213\351\207\217\345\215\225\344\275\215:", nullptr));
        paramsMeasRangeMinLabel->setText(QCoreApplication::translate("SensorDialog_1_2", "\346\265\213\351\207\217\350\214\203\345\233\264\346\234\200\345\260\217\345\200\274:", nullptr));
        paramsMeasRangeMaxLabel->setText(QCoreApplication::translate("SensorDialog_1_2", "\346\265\213\351\207\217\350\214\203\345\233\264\346\234\200\345\244\247\345\200\274:", nullptr));
        paramsOutputSignalUnitLabel->setText(QCoreApplication::translate("SensorDialog_1_2", "\350\276\223\345\207\272\344\277\241\345\217\267\345\215\225\344\275\215:", nullptr));
        paramsOutputSignalRangeMinLabel->setText(QCoreApplication::translate("SensorDialog_1_2", "\350\276\223\345\207\272\344\277\241\345\217\267\350\214\203\345\233\264\346\234\200\345\260\217\345\200\274:", nullptr));
        paramsOutputSignalRangeMaxLabel->setText(QCoreApplication::translate("SensorDialog_1_2", "\350\276\223\345\207\272\344\277\241\345\217\267\350\214\203\345\233\264\346\234\200\345\244\247\345\200\274:", nullptr));
        okButton->setText(QCoreApplication::translate("SensorDialog_1_2", "\347\241\256\345\256\232", nullptr));
        cancelButton->setText(QCoreApplication::translate("SensorDialog_1_2", "\345\217\226\346\266\210", nullptr));
    } // retranslateUi

};

namespace Ui {
    class SensorDialog_1_2: public Ui_SensorDialog_1_2 {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_SENSORDIALOG_1_2_H
