#include <QApplication>
#include <QMainWindow>
#include <QVBoxLayout>
#include <QPushButton>
#include <QTextEdit>
#include <QDebug>
#include "DataChangeListener.h"
#include "SensorDataManager_1_2.h"
#include "ActuatorDataManager_1_2.h"
#include "DetailInfoPanel.h"

class TestWindow : public QMainWindow
{
    Q_OBJECT

public:
    TestWindow(QWidget *parent = nullptr) : QMainWindow(parent)
    {
        setWindowTitle("数据变化监听器测试");
        setGeometry(100, 100, 800, 600);

        // 创建中央部件
        QWidget *centralWidget = new QWidget(this);
        setCentralWidget(centralWidget);

        // 创建布局
        QVBoxLayout *layout = new QVBoxLayout(centralWidget);

        // 创建测试按钮
        QPushButton *addSensorBtn = new QPushButton("添加传感器", this);
        QPushButton *updateSensorBtn = new QPushButton("更新传感器", this);
        QPushButton *deleteSensorBtn = new QPushButton("删除传感器", this);
        QPushButton *addActuatorBtn = new QPushButton("添加作动器", this);
        QPushButton *updateActuatorBtn = new QPushButton("更新作动器", this);
        QPushButton *deleteActuatorBtn = new QPushButton("删除作动器", this);

        // 创建日志显示区域
        logText = new QTextEdit(this);
        logText->setReadOnly(true);

        // 添加到布局
        layout->addWidget(addSensorBtn);
        layout->addWidget(updateSensorBtn);
        layout->addWidget(deleteSensorBtn);
        layout->addWidget(addActuatorBtn);
        layout->addWidget(updateActuatorBtn);
        layout->addWidget(deleteActuatorBtn);
        layout->addWidget(logText);

        // 创建数据管理器
        sensorManager = new SensorDataManager_1_2(this);
        actuatorManager = new ActuatorDataManager_1_2(this);

        // 创建详细信息面板
        detailPanel = new DetailInfoPanel(this);

        // 创建数据变化监听器
        dataChangeListener = new DataChangeListener(this);
        dataChangeListener->setDetailInfoPanel(detailPanel);
        dataChangeListener->connectSensorDataManager(sensorManager);
        dataChangeListener->connectActuatorDataManager(actuatorManager);

        // 连接按钮信号
        connect(addSensorBtn, &QPushButton::clicked, this, &TestWindow::onAddSensor);
        connect(updateSensorBtn, &QPushButton::clicked, this, &TestWindow::onUpdateSensor);
        connect(deleteSensorBtn, &QPushButton::clicked, this, &TestWindow::onDeleteSensor);
        connect(addActuatorBtn, &QPushButton::clicked, this, &TestWindow::onAddActuator);
        connect(updateActuatorBtn, &QPushButton::clicked, this, &TestWindow::onUpdateActuator);
        connect(deleteActuatorBtn, &QPushButton::clicked, this, &TestWindow::onDeleteActuator);

        // 连接数据变化监听器信号
        connect(dataChangeListener, &DataChangeListener::sensorDataChanged,
                this, &TestWindow::onSensorDataChanged);
        connect(dataChangeListener, &DataChangeListener::actuatorDataChanged,
                this, &TestWindow::onActuatorDataChanged);

        logMessage("测试窗口已创建，数据变化监听器已初始化");
    }

private slots:
    void onAddSensor()
    {
        logMessage("🔄 正在添加传感器...");
        
        // 创建测试传感器数据
        UI::SensorParams_1_2 sensor;
        sensor.params_sn = QString("TEST_SENSOR_%1").arg(QDateTime::currentMSecsSinceEpoch());
        sensor.sensorType = "载荷传感器";
        sensor.model = "LC-100kN";
        sensor.range = "±100kN";
        sensor.accuracy = "0.1%FS";
        sensor.unit = "N";
        sensor.sensitivity = "2.000";
        
        // 添加到数据管理器
        bool success = sensorManager->addSensor(sensor, 1);
        if (success) {
            logMessage("✅ 传感器添加成功: " + sensor.params_sn);
        } else {
            logMessage("❌ 传感器添加失败: " + sensorManager->getLastError());
        }
    }

    void onUpdateSensor()
    {
        logMessage("🔄 正在更新传感器...");
        
        // 获取第一个传感器并更新
        QStringList serialNumbers = sensorManager->getAllSensorSerialNumbers();
        if (!serialNumbers.isEmpty()) {
            QString serialNumber = serialNumbers.first();
            UI::SensorParams_1_2 sensor = sensorManager->getSensor(serialNumber, 1);
            if (!sensor.params_sn.isEmpty()) {
                sensor.accuracy = "0.05%FS"; // 更新精度
                bool success = sensorManager->updateSensorInGroup(1, serialNumber, sensor);
                if (success) {
                    logMessage("✅ 传感器更新成功: " + serialNumber);
                } else {
                    logMessage("❌ 传感器更新失败: " + sensorManager->getLastError());
                }
            } else {
                logMessage("⚠️ 没有找到可更新的传感器");
            }
        } else {
            logMessage("⚠️ 没有传感器可更新");
        }
    }

    void onDeleteSensor()
    {
        logMessage("🔄 正在删除传感器...");
        
        // 删除第一个传感器
        QStringList serialNumbers = sensorManager->getAllSensorSerialNumbers();
        if (!serialNumbers.isEmpty()) {
            QString serialNumber = serialNumbers.first();
            bool success = sensorManager->removeSensorInGroup(1, serialNumber);
            if (success) {
                logMessage("✅ 传感器删除成功: " + serialNumber);
            } else {
                logMessage("❌ 传感器删除失败: " + sensorManager->getLastError());
            }
        } else {
            logMessage("⚠️ 没有传感器可删除");
        }
    }

    void onAddActuator()
    {
        logMessage("🔄 正在添加作动器...");
        
        // 创建测试作动器数据
        UI::ActuatorParams_1_2 actuator;
        actuator.params.sn = QString("TEST_ACTUATOR_%1").arg(QDateTime::currentMSecsSinceEpoch());
        actuator.params.type = "液压作动器";
        actuator.params.model = "HY-50kN";
        actuator.params.range = "±50kN";
        actuator.params.accuracy = "0.2%FS";
        actuator.params.unit = "N";
        
        // 添加到数据管理器
        bool success = actuatorManager->addActuator(actuator, 1);
        if (success) {
            logMessage("✅ 作动器添加成功: " + actuator.params.sn);
        } else {
            logMessage("❌ 作动器添加失败: " + actuatorManager->getLastError());
        }
    }

    void onUpdateActuator()
    {
        logMessage("🔄 正在更新作动器...");
        
        // 获取第一个作动器并更新
        QStringList serialNumbers = actuatorManager->getAllActuatorSerialNumbers();
        if (!serialNumbers.isEmpty()) {
            QString serialNumber = serialNumbers.first();
            UI::ActuatorParams_1_2 actuator = actuatorManager->getActuator(serialNumber, 1);
            if (!actuator.params.sn.isEmpty()) {
                actuator.params.accuracy = "0.1%FS"; // 更新精度
                bool success = actuatorManager->updateActuatorInGroup(1, serialNumber, actuator);
                if (success) {
                    logMessage("✅ 作动器更新成功: " + serialNumber);
                } else {
                    logMessage("❌ 作动器更新失败: " + actuatorManager->getLastError());
                }
            } else {
                logMessage("⚠️ 没有找到可更新的作动器");
            }
        } else {
            logMessage("⚠️ 没有作动器可更新");
        }
    }

    void onDeleteActuator()
    {
        logMessage("🔄 正在删除作动器...");
        
        // 删除第一个作动器
        QStringList serialNumbers = actuatorManager->getAllActuatorSerialNumbers();
        if (!serialNumbers.isEmpty()) {
            QString serialNumber = serialNumbers.first();
            bool success = actuatorManager->removeActuatorInGroup(1, serialNumber);
            if (success) {
                logMessage("✅ 作动器删除成功: " + serialNumber);
            } else {
                logMessage("❌ 作动器删除失败: " + actuatorManager->getLastError());
            }
        } else {
            logMessage("⚠️ 没有作动器可删除");
        }
    }

    void onSensorDataChanged(const QString& serialNumber, const QString& operation)
    {
        logMessage("📡 传感器数据变化: " + serialNumber + " 操作: " + operation);
    }

    void onActuatorDataChanged(const QString& serialNumber, const QString& operation)
    {
        logMessage("📡 作动器数据变化: " + serialNumber + " 操作: " + operation);
    }

    void logMessage(const QString& message)
    {
        QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss.zzz");
        QString logEntry = QString("[%1] %2").arg(timestamp).arg(message);
        logText->append(logEntry);
        qDebug() << logEntry;
    }

private:
    QTextEdit *logText;
    SensorDataManager_1_2 *sensorManager;
    ActuatorDataManager_1_2 *actuatorManager;
    DetailInfoPanel *detailPanel;
    DataChangeListener *dataChangeListener;
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    TestWindow window;
    window.show();
    
    return app.exec();
}

#include "test_data_change_listener.moc" 