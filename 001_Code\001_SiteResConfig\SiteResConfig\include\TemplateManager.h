#ifndef TEMPLATEMANAGER_H
#define TEMPLATEMANAGER_H

#include <QObject>
#include <QString>
#include <QJsonObject>
#include <QJsonArray>
#include <QMap>
#include <QMutex>
#include <QTimer>

class QNetworkAccessManager;
class QNetworkReply;

/**
 * @brief 模板管理器
 * 提供完整的HTML模板管理功能，包括加载、缓存、热重载等
 */
class TemplateManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 获取单例实例
     * @return 模板管理器实例
     */
    static TemplateManager* getInstance();
    
    /**
     * @brief 加载模板
     * @param templateName 模板名称
     * @param forceReload 是否强制重新加载
     * @return 模板内容
     */
    QString loadTemplate(const QString& templateName, bool forceReload = false);
    
    /**
     * @brief 生成包含数据的HTML
     * @param templateName 模板名称
     * @param data 数据对象
     * @return 完整的HTML内容
     */
    QString generateHtml(const QString& templateName, const QJsonObject& data);
    
    /**
     * @brief 注册模板
     * @param templateName 模板名称
     * @param templatePath 模板路径
     * @param description 模板描述
     */
    void registerTemplate(const QString& templateName, const QString& templatePath, const QString& description = "");
    
    /**
     * @brief 清除缓存
     */
    void clearCache();
    
    /**
     * @brief 重新加载所有模板
     */
    void reloadAllTemplates();
    
    /**
     * @brief 获取模板状态信息
     * @return 状态信息JSON对象
     */
    QJsonObject getTemplateStatus();
    
    /**
     * @brief 启用/禁用热重载
     * @param enabled 是否启用
     */
    void setHotReloadEnabled(bool enabled);
    
    /**
     * @brief 设置模板目录
     * @param directory 目录路径
     */
    void setTemplateDirectory(const QString& directory);

signals:
    /**
     * @brief 模板加载状态变化信号
     * @param templateName 模板名称
     * @param success 是否成功
     * @param message 状态消息
     */
    void templateStatusChanged(const QString& templateName, bool success, const QString& message);
    
    /**
     * @brief 模板缓存更新信号
     * @param templateName 模板名称
     */
    void templateCacheUpdated(const QString& templateName);

private slots:
    /**
     * @brief 检查模板文件变化
     */
    void checkTemplateChanges();
    
    /**
     * @brief 网络请求完成处理
     * @param reply 网络回复
     */
    void onNetworkReplyFinished(QNetworkReply* reply);

private:
    explicit TemplateManager(QObject* parent = nullptr);
    ~TemplateManager();
    
    // 禁用拷贝构造和赋值
    TemplateManager(const TemplateManager&) = delete;
    TemplateManager& operator=(const TemplateManager&) = delete;
    
    /**
     * @brief 初始化模板管理器
     */
    void initialize();
    
    /**
     * @brief 加载配置文件
     */
    void loadConfiguration();
    
    /**
     * @brief 从文件系统加载模板
     * @param templateName 模板名称
     * @return 模板内容
     */
    QString loadFromFileSystem(const QString& templateName);
    
    /**
     * @brief 从Qt资源系统加载模板
     * @param templateName 模板名称
     * @return 模板内容
     */
    QString loadFromQtResource(const QString& templateName);
    
    /**
     * @brief 从网络加载模板
     * @param templateName 模板名称
     * @return 模板内容
     */
    QString loadFromNetwork(const QString& templateName);
    
    /**
     * @brief 验证模板内容
     * @param content 模板内容
     * @return 是否有效
     */
    bool validateTemplate(const QString& content);
    
    /**
     * @brief 注入数据到模板
     * @param templateContent 模板内容
     * @param data 数据对象
     * @return 注入数据后的内容
     */
    QString injectData(const QString& templateContent, const QJsonObject& data);
    
    /**
     * @brief 生成错误模板
     * @param errorMessage 错误信息
     * @return 错误模板内容
     */
    QString generateErrorTemplate(const QString& errorMessage);

private:
    static TemplateManager* s_instance;                 ///< 单例实例
    static QMutex s_mutex;                             ///< 线程安全锁
    
    QMap<QString, QString> m_templateCache;            ///< 模板缓存
    QMap<QString, QString> m_templatePaths;            ///< 模板路径映射
    QMap<QString, QString> m_templateDescriptions;     ///< 模板描述
    QMap<QString, QDateTime> m_templateLastModified;   ///< 模板最后修改时间
    
    QString m_templateDirectory;                        ///< 模板目录
    QJsonObject m_configuration;                        ///< 配置信息
    bool m_hotReloadEnabled;                            ///< 热重载是否启用
    QTimer* m_fileWatcherTimer;                         ///< 文件监视定时器
    
    QNetworkAccessManager* m_networkManager;            ///< 网络管理器
    
    // 配置选项
    bool m_cacheEnabled;                                ///< 缓存是否启用
    int m_cacheTimeout;                                 ///< 缓存超时时间（秒）
    bool m_networkEnabled;                              ///< 网络加载是否启用
    QString m_networkBaseUrl;                           ///< 网络基础URL
};

#endif // TEMPLATEMANAGER_H 