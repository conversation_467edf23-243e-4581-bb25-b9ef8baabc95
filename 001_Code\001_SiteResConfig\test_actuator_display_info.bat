@echo off
echo ========================================
echo  作动器显示信息格式测试
echo ========================================

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o
if exist "ui_*.h" del ui_*.h

echo.
echo 生成UI头文件...
uic ui\MainWindow.ui -o ui_MainWindow.h
if errorlevel 1 (
    echo UI文件生成失败！
    pause
    exit /b 1
) else (
    echo ✅ UI头文件生成成功: ui_MainWindow.h
)

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo.
    echo 修改内容：
    echo ✅ 更新显示信息格式
    echo ✅ 调整对话框尺寸
    echo ✅ 添加文本换行支持
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！作动器显示信息格式已更新
    echo ========================================
    
    if exist "SiteResConfig.exe" (
        echo 可执行文件: SiteResConfig.exe
        echo.
        echo ✅ 作动器显示信息格式已更新！
        echo.
        echo 📋 新的显示信息格式:
        echo "作动器组（选择要添加作动器的作动器组名称）：组名称\作动器_000001"
        echo.
        echo 🎯 显示信息示例:
        echo.
        echo 示例1 - 50kN作动器组:
        echo "作动器组（选择要添加作动器的作动器组名称）：50kN_作动器\作动器_000001"
        echo.
        echo 示例2 - 100kN作动器组:
        echo "作动器组（选择要添加作动器的作动器组名称）：100kN_作动器\作动器_000002"
        echo.
        echo 示例3 - 200kN作动器组:
        echo "作动器组（选择要添加作动器的作动器组名称）：200kN_作动器\作动器_000003"
        echo.
        echo 示例4 - 自定义作动器组:
        echo "作动器组（选择要添加作动器的作动器组名称）：液压_作动器\作动器_000001"
        echo.
        echo 🎨 界面设计改进:
        echo ┌─────────────────────────────────────────────────────┐
        echo │                新建作动器                            │
        echo ├─────────────────────────────────────────────────────┤
        echo │ 作动器组（选择要添加作动器的作动器组名称）：          │
        echo │           100kN_作动器\作动器_000001                │
        echo ├─────────────────────────────────────────────────────┤
        echo │ 序列号: [作动器_000001                    ]         │
        echo │ 类型:   [单出杆 ▼]                                 │
        echo │ 缸径:   [0.10] m                                   │
        echo │ 杆径:   [0.05] m                                   │
        echo │ 行程:   [0.20] m                                   │
        echo │                                                     │
        echo │                    [确定] [取消]                    │
        echo └─────────────────────────────────────────────────────┘
        echo.
        echo 🔧 技术改进:
        echo - 显示信息更加明确和详细
        echo - 对话框尺寸调整为500x380像素
        echo - 字体大小调整为12px以适应更长文本
        echo - 添加文本换行支持（setWordWrap(true)）
        echo - 保持居中对齐和蓝色粗体样式
        echo.
        echo 💡 用户体验改进:
        echo - 明确说明这是选择要添加作动器的组
        echo - 用户更容易理解当前操作的上下文
        echo - 显示信息更加专业和规范
        echo - 避免用户对操作目标的困惑
        echo.
        echo 📊 显示信息结构分析:
        echo.
        echo 🏷️ 信息组成:
        echo ├─ 说明文字: "作动器组（选择要添加作动器的作动器组名称）："
        echo ├─ 组名称: 实际选择的作动器组名称
        echo ├─ 分隔符: "\" (反斜杠)
        echo └─ 自动编号: "作动器_000001" (根据数量变化)
        echo.
        echo 🎯 不同场景下的显示效果:
        echo.
        echo 场景1 - 标准规格组:
        echo "作动器组（选择要添加作动器的作动器组名称）：50kN_作动器\作动器_000001"
        echo.
        echo 场景2 - 自定义组:
        echo "作动器组（选择要添加作动器的作动器组名称）：液压_作动器\作动器_000001"
        echo.
        echo 场景3 - 多个作动器:
        echo 第1个: "...：100kN_作动器\作动器_000001"
        echo 第2个: "...：100kN_作动器\作动器_000002"
        echo 第3个: "...：100kN_作动器\作动器_000003"
        echo.
        echo 启动程序测试新的显示信息格式...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
        echo 启动程序...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
        echo 启动程序...
        start release\SiteResConfig.exe
    ) else (
        echo 警告: 找不到可执行文件！
    )
)

echo.
echo 📖 作动器显示信息格式测试指南:
echo.
echo 🎯 完整测试流程:
echo.
echo 1️⃣ 创建作动器组:
echo   - 右键"作动器"节点
echo   - 选择"新建" → "作动器组"
echo   - 选择"100kN_作动器"
echo   - 验证组创建成功
echo.
echo 2️⃣ 测试显示信息:
echo   - 右键"100kN_作动器"组
echo   - 选择"新建" → "作动器"
echo   - 验证显示信息格式:
echo     "作动器组（选择要添加作动器的作动器组名称）：100kN_作动器\作动器_000001"
echo   - 验证文本换行和居中对齐
echo   - 验证蓝色粗体样式
echo.
echo 3️⃣ 测试不同组:
echo   - 创建其他规格的作动器组
echo   - 验证显示信息中的组名称正确更新
echo   - 验证编号独立计算
echo.
echo 4️⃣ 测试自定义组:
echo   - 创建自定义名称的作动器组
echo   - 验证显示信息中显示自定义组名
echo   - 验证整体格式保持一致
echo.
echo 🔍 验证要点:
echo - 显示信息格式完整准确
echo - 说明文字清晰明确
echo - 组名称动态更新
echo - 编号自动递增
echo - 对话框尺寸适当
echo - 文本换行正常工作
echo - 样式美观统一
echo.
echo 💡 用户理解验证:
echo - 用户能清楚知道这是在选择组
echo - 用户明白当前操作的目标
echo - 显示信息提供足够的上下文
echo - 操作流程逻辑清晰
echo.
pause
