@echo off
echo ========================================
echo  测试项目状态管理功能
echo ========================================

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    echo 请确保Qt 5.14.2 MinGW版本已正确安装
    pause
    exit /b 1
)

g++ --version
if errorlevel 1 (
    echo 错误: MinGW编译器未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目（测试项目状态管理功能）...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo 请检查上面的错误信息。
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  编译成功！项目状态管理功能已集成
    echo ========================================
    
    if exist "SiteResConfig.exe" (
        echo 可执行文件: SiteResConfig.exe
        echo.
        echo 🎯 项目状态管理功能:
        echo ✅ 软件启动时操作区禁用，显示"没有项目"提示
        echo ✅ 新建项目后操作区启用，显示"项目已就绪"
        echo ✅ 打开项目后操作区启用，显示项目信息
        echo ✅ 项目关闭后操作区禁用，重新显示提示
        echo.
        echo 🎮 测试步骤:
        echo 1. 启动软件 - 观察操作区是否禁用，是否显示提示
        echo 2. 新建项目 - 观察操作区是否启用，提示是否消失
        echo 3. 关闭项目 - 观察操作区是否重新禁用，提示是否重新显示
        echo.
        echo 启动程序进行测试...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
        echo 启动程序进行测试...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
        echo 启动程序进行测试...
        start release\SiteResConfig.exe
    ) else (
        echo 警告: 找不到可执行文件！
    )
)

echo.
echo 📖 功能说明:
echo.
echo 🔧 项目状态管理:
echo - 软件启动后，没有项目时操作区禁用
echo - 显示明显提示："没有项目，请新建项目"
echo - 新建或打开项目后，操作区启用
echo - 项目关闭后，操作区重新禁用并显示提示
echo.
echo 🎨 界面提示:
echo - 系统概览区域显示醒目的状态信息
echo - 状态栏显示当前项目状态
echo - 窗口标题反映项目状态
echo.
echo 🎯 禁用的控件:
echo - 硬件树和试验配置树
echo - 所有工具栏按钮
echo - 数据操作相关控件
echo - 试验控制相关控件
echo - 相关菜单项
echo.
pause
