/**
 * @file CreateHardwareNodeDialog.cpp
 * @brief 创建硬件节点对话框类实现
 * @details 使用Qt Designer设计的硬件节点创建对话框实现
 * <AUTHOR> Assistant
 * @date 2025-08-06
 * @version 1.0.0
 */

#include "CreateHardwareNodeDialog.h"
#include "ui_CreateHardwareNodeDialog.h"
#include <QtWidgets/QMessageBox>
#include <QtWidgets/QScrollArea>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QCheckBox>

namespace UI {

CreateHardwareNodeDialog::CreateHardwareNodeDialog(const QString& suggestedName, QWidget* parent)
    : QDialog(parent)
    , ui(new Ui::CreateHardwareNodeDialog)
    , scrollArea_(nullptr)
    , scrollWidget_(nullptr)
    , scrollLayout_(nullptr) {

    ui->setupUi(this);

    // 设置建议的节点名称
    ui->nodeNameEdit->setText(suggestedName);

    initializeUI();
    connectSignals();
}

CreateHardwareNodeDialog::~CreateHardwareNodeDialog() {
    clearChannelWidgets();
    delete ui;
}

void CreateHardwareNodeDialog::initializeUI() {
    // 设置窗口标题
    setWindowTitle(tr("创建硬件节点"));

    // 设置窗口大小
    resize(500, 600);

    // 设置通道数量范围 - 支持更多通道
    ui->channelCountSpinBox->setMinimum(1);
    ui->channelCountSpinBox->setMaximum(32); // 支持最多32个通道
    ui->channelCountSpinBox->setValue(2);

    // 创建滚动区域来容纳动态通道控件
    setupScrollArea();

    // 初始化通道界面
    updateChannelUI();
}

void CreateHardwareNodeDialog::connectSignals() {
    // 连接通道数量改变信号
    connect(ui->channelCountSpinBox, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &CreateHardwareNodeDialog::onChannelCountChanged);
    
    // 重新连接确定按钮，添加验证
    disconnect(ui->okButton, &QPushButton::clicked, this, &QDialog::accept);
    connect(ui->okButton, &QPushButton::clicked, this, &CreateHardwareNodeDialog::onAcceptClicked);
}

void CreateHardwareNodeDialog::onChannelCountChanged() {
    updateChannelUI();
}

void CreateHardwareNodeDialog::updateChannelUI() {
    int channelCount = ui->channelCountSpinBox->value();

    // 清理现有的通道控件
    clearChannelWidgets();

    // 创建新的通道控件
    for (int i = 1; i <= channelCount; ++i) {
        ChannelConfigWidget channelWidget = createChannelWidget(i);
        channelWidgets_.append(channelWidget);
        scrollLayout_->addWidget(channelWidget.groupBox);
    }

    // 添加弹性空间
    scrollLayout_->addStretch();

    // 调整窗口大小 - 根据通道数量动态调整
    int baseHeight = 250; // 基础高度（包含节点名称输入）
    int channelHeight = 120; // 每个通道的高度
    int maxHeight = 800; // 最大高度

    int newHeight = qMin(baseHeight + channelCount * channelHeight, maxHeight);
    resize(500, newHeight);
}

void CreateHardwareNodeDialog::onAcceptClicked() {
    // 验证节点名称
    QString nodeName = ui->nodeNameEdit->text().trimmed();
    if (nodeName.isEmpty()) {
        QMessageBox::warning(this, tr("输入错误"), tr("请输入节点名称！"));
        ui->nodeNameEdit->setFocus();
        return;
    }

    // 验证所有通道的IP地址格式
    for (int i = 0; i < channelWidgets_.size(); ++i) {
        const ChannelConfigWidget& widget = channelWidgets_[i];
        QString ip = widget.ipEdit->text().trimmed();

        if (ip.isEmpty()) {
            QMessageBox::warning(this, tr("输入错误"),
                               tr("请输入CH%1的IP地址！").arg(i + 1));
            widget.ipEdit->setFocus();
            return;
        }

        // 可以添加更多的IP地址格式验证
        // TODO: 添加IP地址格式验证逻辑
    }

    accept();
}

CreateHardwareNodeParams CreateHardwareNodeDialog::getCreateHardwareNodeParams() const {
    CreateHardwareNodeParams params;

    params.nodeName = ui->nodeNameEdit->text().trimmed();
    params.channelCount = ui->channelCountSpinBox->value();
    params.channels.clear();

    // 获取所有通道配置
    for (int i = 0; i < channelWidgets_.size(); ++i) {
        const ChannelConfigWidget& widget = channelWidgets_[i];

        ChannelInfo channelInfo;
        channelInfo.channelId = i + 1;
        channelInfo.ipAddress = widget.ipEdit->text().trimmed();
        channelInfo.port = widget.portSpinBox->value();
        channelInfo.enabled = widget.enabledCheckBox->isChecked();

        params.channels.append(channelInfo);
    }

    return params;
}

void CreateHardwareNodeDialog::setupScrollArea() {
    // 隐藏原有的固定通道控件
    ui->channel1GroupBox->setVisible(false);
    ui->channel2GroupBox->setVisible(false);

    // 创建滚动区域
    scrollArea_ = ui->scrollArea;//new QScrollArea(this);
    scrollArea_->setWidgetResizable(true);
    scrollArea_->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    scrollArea_->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);

    // 创建滚动内容区域
    scrollWidget_ = new  QWidget();
    scrollLayout_ = new QVBoxLayout(scrollWidget_);
    scrollLayout_->setSpacing(10);
    scrollLayout_->setContentsMargins(10, 10, 10, 10);

    scrollArea_->setWidget(scrollWidget_);

    // 将滚动区域添加到主布局中（在通道数量控件之后）
    ui->verticalLayout->insertWidget(3, scrollArea_); // 在节点名称和通道数量之后
}

ChannelConfigWidget CreateHardwareNodeDialog::createChannelWidget(int channelId) {
    ChannelConfigWidget widget;

    // 创建组框
    widget.groupBox = new QGroupBox(QString("CH%1 通道配置").arg(channelId));
    widget.layout = new QGridLayout(widget.groupBox);

    // 创建IP地址控件
    widget.ipLabel = new QLabel("IP地址:");
    widget.ipEdit = new QLineEdit();
    widget.ipEdit->setText(QString("192.168.1.%1").arg(100 + channelId - 1)); // 默认IP
    widget.ipEdit->setPlaceholderText("请输入IP地址");

    // 创建端口控件
    widget.portLabel = new QLabel("端口:");
    widget.portSpinBox = new QSpinBox();
    widget.portSpinBox->setMinimum(1);
    widget.portSpinBox->setMaximum(65535);
    widget.portSpinBox->setValue(8080 + channelId - 1); // 默认端口

    // 创建启用复选框
    widget.enabledCheckBox = new QCheckBox("启用通道");
    widget.enabledCheckBox->setChecked(true); // 默认启用

    // 布局控件
    widget.layout->addWidget(widget.ipLabel, 0, 0);
    widget.layout->addWidget(widget.ipEdit, 0, 1);
    widget.layout->addWidget(widget.portLabel, 1, 0);
    widget.layout->addWidget(widget.portSpinBox, 1, 1);
    widget.layout->addWidget(widget.enabledCheckBox, 2, 0, 1, 2);

    return widget;
}

void CreateHardwareNodeDialog::clearChannelWidgets() {
    // 清理所有动态创建的通道控件
    for (const ChannelConfigWidget& widget : channelWidgets_) {
        if (widget.groupBox) {
            scrollLayout_->removeWidget(widget.groupBox);
            delete widget.groupBox; // 这会自动删除所有子控件
        }
    }
    channelWidgets_.clear();

    // 清理布局中的弹性空间
    QLayoutItem* item;
    while ((item = scrollLayout_->takeAt(0)) != nullptr) {
        if (item->spacerItem()) {
            delete item;
        }
    }
}

void CreateHardwareNodeDialog::setHardwareNodeParams(const CreateHardwareNodeParams& params) {
    // 设置节点名称
    ui->nodeNameEdit->setText(params.nodeName);
    
    // 设置通道数量
    ui->channelCountSpinBox->setValue(params.channelCount);
    
    // 更新通道界面
    updateChannelUI();
    
    // 设置各个通道的参数
    for (int i = 0; i < params.channels.size() && i < channelWidgets_.size(); ++i) {
        const ChannelInfo& channel = params.channels[i];
        ChannelConfigWidget& widget = channelWidgets_[i];
        
        if (widget.ipEdit) {
            widget.ipEdit->setText(channel.ipAddress);
        }
        if (widget.portSpinBox) {
            widget.portSpinBox->setValue(channel.port);
        }
        if (widget.enabledCheckBox) {
            widget.enabledCheckBox->setChecked(channel.enabled);
        }
    }
}

void CreateHardwareNodeDialog::setEditMode(bool isEditMode) {
    if (isEditMode) {
        setWindowTitle(tr("编辑硬件节点"));
        if (ui->okButton) {
            ui->okButton->setText(tr("保存"));
        }
    } else {
        setWindowTitle(tr("创建硬件节点"));
        if (ui->okButton) {
            ui->okButton->setText(tr("创建"));
        }
    }
}

} // namespace UI
