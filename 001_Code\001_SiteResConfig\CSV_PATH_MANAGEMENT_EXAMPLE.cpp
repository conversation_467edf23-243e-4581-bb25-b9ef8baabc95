/**
 * @file CSV_PATH_MANAGEMENT_EXAMPLE.cpp
 * @brief CSV路径管理使用示例
 * @details 演示如何使用新的CSV路径管理功能
 * <AUTHOR> Assistant
 * @date 2025-08-11
 * @version 1.0.0
 */

#include "MainWindow_Qt_Simple.h"
#include <QtCore/QDebug>

/**
 * @brief CSV路径管理使用示例
 * @details 这些示例展示了如何在MainWindow中使用CSV路径管理功能
 */

// 示例1：获取默认CSV路径
void ExampleGetDefaultPath(CMyMainWindow* mainWindow) {
    qDebug() << "=== 示例1：获取默认CSV路径 ===";
    
    QString defaultPath = mainWindow->GetDefaultCSVPath();
    qDebug() << "默认CSV路径:" << defaultPath;
    
    QString projectPath = mainWindow->GetExperimentProjectPath();
    qDebug() << "实验工程路径:" << projectPath;
}

// 示例2：确保目录存在
void ExampleEnsureDirectory(CMyMainWindow* mainWindow) {
    qDebug() << "=== 示例2：确保目录存在 ===";
    
    bool success = mainWindow->EnsureCSVDirectoryExists();
    if (success) {
        qDebug() << "CSV目录创建/验证成功";
    } else {
        qDebug() << "CSV目录创建失败";
    }
}

// 示例3：生成文件路径
void ExampleGenerateFilePath(CMyMainWindow* mainWindow) {
    qDebug() << "=== 示例3：生成文件路径 ===";
    
    // 基础文件路径
    QString basicPath = mainWindow->GenerateCSVFilePath("test.csv");
    qDebug() << "基础文件路径:" << basicPath;
    
    // 带子目录的文件路径
    QString subDirPath = mainWindow->GenerateCSVFilePath("config.csv", "配置文件");
    qDebug() << "子目录文件路径:" << subDirPath;
    
    // 数据文件路径
    QString dataPath = mainWindow->GenerateCSVFilePath("experiment_data.csv", "数据");
    qDebug() << "数据文件路径:" << dataPath;
}

// 示例4：生成带时间戳的文件名
void ExampleTimestampedFileName(CMyMainWindow* mainWindow) {
    qDebug() << "=== 示例4：生成带时间戳的文件名 ===";
    
    // 包含日期和时间
    QString fullTimestamp = mainWindow->GenerateTimestampedFileName("实验数据", true);
    qDebug() << "完整时间戳文件名:" << fullTimestamp;
    
    // 只包含日期
    QString dateOnly = mainWindow->GenerateTimestampedFileName("配置备份", false);
    qDebug() << "日期文件名:" << dateOnly;
    
    // 自定义基础名称
    QString customName = mainWindow->GenerateTimestampedFileName("用户配置", true);
    qDebug() << "自定义文件名:" << customName;
}

// 示例5：快速保存项目
void ExampleQuickSaveProject(CMyMainWindow* mainWindow) {
    qDebug() << "=== 示例5：快速保存项目 ===";
    
    QString savedPath;
    
    // 使用默认设置快速保存
    bool success1 = mainWindow->QuickSaveProjectToCSV(&savedPath);
    if (success1) {
        qDebug() << "快速保存成功:" << savedPath;
    }
    
    // 使用自定义名称保存
    bool success2 = mainWindow->QuickSaveProjectToCSV(&savedPath, "我的实验", true);
    if (success2) {
        qDebug() << "自定义保存成功:" << savedPath;
    }
    
    // 不使用时间戳保存
    bool success3 = mainWindow->QuickSaveProjectToCSV(&savedPath, "标准配置", false);
    if (success3) {
        qDebug() << "标准保存成功:" << savedPath;
    }
}

// 示例6：导出数据到CSV
void ExampleExportData(CMyMainWindow* mainWindow) {
    qDebug() << "=== 示例6：导出数据到CSV ===";
    
    // 创建示例数据
    QVector<QStringList> testData;
    testData.append(QStringList() << "时间" << "位移" << "载荷" << "应变");
    testData.append(QStringList() << "0.0" << "0.0" << "0.0" << "0.0");
    testData.append(QStringList() << "0.1" << "1.5" << "150.2" << "0.001");
    testData.append(QStringList() << "0.2" << "3.0" << "298.7" << "0.002");
    
    // 导出到主目录
    bool success1 = mainWindow->ExportDataToCSV(testData, "实验数据.csv");
    if (success1) {
        qDebug() << "主目录导出成功";
    }
    
    // 导出到子目录
    bool success2 = mainWindow->ExportDataToCSV(testData, "测试数据.csv", "测试结果");
    if (success2) {
        qDebug() << "子目录导出成功";
    }
}

// 示例7：完整的工作流程
void ExampleCompleteWorkflow(CMyMainWindow* mainWindow) {
    qDebug() << "=== 示例7：完整的工作流程 ===";
    
    // 1. 初始化路径
    QString csvPath = mainWindow->GetDefaultCSVPath();
    qDebug() << "1. CSV存储路径:" << csvPath;
    
    // 2. 确保目录存在
    if (!mainWindow->EnsureCSVDirectoryExists()) {
        qDebug() << "目录创建失败，退出";
        return;
    }
    qDebug() << "2. 目录验证完成";
    
    // 3. 生成不同类型的文件路径
    QString configPath = mainWindow->GenerateCSVFilePath("系统配置.csv", "配置");
    QString dataPath = mainWindow->GenerateCSVFilePath("实验数据.csv", "数据");
    QString logPath = mainWindow->GenerateCSVFilePath("操作日志.csv", "日志");
    
    qDebug() << "3. 生成的路径:";
    qDebug() << "   配置文件:" << configPath;
    qDebug() << "   数据文件:" << dataPath;
    qDebug() << "   日志文件:" << logPath;
    
    // 4. 快速保存当前项目
    QString projectPath;
    if (mainWindow->QuickSaveProjectToCSV(&projectPath, "当前实验", true)) {
        qDebug() << "4. 项目保存成功:" << projectPath;
    }
    
    // 5. 导出示例数据
    QVector<QStringList> sampleData;
    sampleData.append(QStringList() << "项目" << "状态" << "进度");
    sampleData.append(QStringList() << "实验A" << "进行中" << "75%");
    sampleData.append(QStringList() << "实验B" << "已完成" << "100%");
    
    if (mainWindow->ExportDataToCSV(sampleData, "项目状态.csv", "报告")) {
        qDebug() << "5. 数据导出成功";
    }
    
    qDebug() << "=== 工作流程完成 ===";
}

// 示例8：错误处理和边界情况
void ExampleErrorHandling(CMyMainWindow* mainWindow) {
    qDebug() << "=== 示例8：错误处理和边界情况 ===";
    
    // 空数据导出
    QVector<QStringList> emptyData;
    bool result1 = mainWindow->ExportDataToCSV(emptyData, "empty.csv");
    qDebug() << "空数据导出结果:" << (result1 ? "成功" : "失败");
    
    // 特殊字符文件名
    QString specialPath = mainWindow->GenerateCSVFilePath("测试@#$%文件.csv");
    qDebug() << "特殊字符路径:" << specialPath;
    
    // 长文件名
    QString longName = mainWindow->GenerateTimestampedFileName("这是一个非常长的文件名用于测试系统的处理能力", true);
    qDebug() << "长文件名:" << longName;
    
    // 多级子目录
    QString deepPath = mainWindow->GenerateCSVFilePath("deep.csv", "level1/level2/level3");
    qDebug() << "深层目录路径:" << deepPath;
}

/**
 * @brief 主测试函数
 * @param mainWindow 主窗口指针
 */
void RunAllCSVPathExamples(CMyMainWindow* mainWindow) {
    if (!mainWindow) {
        qDebug() << "错误：主窗口指针为空";
        return;
    }
    
    qDebug() << "========================================";
    qDebug() << "       CSV路径管理功能演示";
    qDebug() << "========================================";
    
    try {
        ExampleGetDefaultPath(mainWindow);
        qDebug() << "";
        
        ExampleEnsureDirectory(mainWindow);
        qDebug() << "";
        
        ExampleGenerateFilePath(mainWindow);
        qDebug() << "";
        
        ExampleTimestampedFileName(mainWindow);
        qDebug() << "";
        
        ExampleQuickSaveProject(mainWindow);
        qDebug() << "";
        
        ExampleExportData(mainWindow);
        qDebug() << "";
        
        ExampleCompleteWorkflow(mainWindow);
        qDebug() << "";
        
        ExampleErrorHandling(mainWindow);
        
    } catch (const std::exception& e) {
        qDebug() << "示例运行出错:" << e.what();
    }
    
    qDebug() << "";
    qDebug() << "========================================";
    qDebug() << "       所有示例运行完成";
    qDebug() << "========================================";
}
