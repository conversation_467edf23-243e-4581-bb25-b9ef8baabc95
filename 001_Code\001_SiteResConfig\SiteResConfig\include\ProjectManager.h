/**
 * @file ProjectManager.h
 * @brief 项目管理模块 - 基于实际需求
 * @details 提供项目生命周期管理接口
 * <AUTHOR> Assistant
 * @date 2025-01-27
 * @version 3.4.0
 */

#ifndef PROJECTMANAGER_H
#define PROJECTMANAGER_H

#include <QObject>
#include <QString>

// 前向声明
class CMyMainWindow;

/**
 * @brief 项目管理器类 - 基于实际需求
 * @details 提供项目生命周期管理接口
 */
class ProjectManager : public QObject {
    Q_OBJECT
    
public:
    explicit ProjectManager(QObject* parent = nullptr);
    ~ProjectManager();
    
    // 设置主窗口引用 (v3.4架构：需要访问主窗口的数据和方法)
    void setMainWindow(CMyMainWindow* mainWindow);
    
    // 项目生命周期管理
    bool createNewProject(); // v3.4架构：无参数版本，使用默认名称
    bool createNewProject(const QString& projectName);
    bool openProject(const QString& filePath);
    bool saveProject();
    bool saveAsProject(const QString& filePath);
    void closeProject();
    
    // v3.4架构：文件对话框处理
    bool openProjectWithDialog();
    bool saveProjectWithDialog();
    bool saveAsProjectWithDialog();
    
    // 项目状态管理
    bool hasActiveProject() const;
    QString getCurrentProjectPath() const;
    QString getCurrentProjectName() const;
    bool hasUnsavedChanges() const;
    void markAsChanged(); // v3.4架构：标记项目有变更
    
    // 保存检查
    bool promptSaveIfNeeded();
    
signals:
    void projectOpened(const QString& path, const QString& name);
    void projectClosed();
    void projectSaved();
    void projectError(const QString& error);
    void projectMessage(const QString& message);
    
private:
    QString currentProjectPath_;
    QString currentProjectName_;
    bool hasUnsavedChanges_;
    
    // v3.4架构：主窗口引用
    CMyMainWindow* mainWindow_;
    
    // v3.4架构：内部辅助方法
    bool loadProjectFromXLS(const QString& filePath);
    bool saveProjectToXLS(const QString& filePath);
    bool saveProjectToJSON(const QString& filePath);
    void clearInterfaceData();
    void setDefaultEmptyInterface();
    void updateWindowTitle();
};

#endif // PROJECTMANAGER_H 