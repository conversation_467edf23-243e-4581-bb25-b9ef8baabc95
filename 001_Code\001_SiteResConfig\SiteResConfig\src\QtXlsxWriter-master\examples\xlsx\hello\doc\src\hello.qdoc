/*!
    \example hello
    \title Hello QtXlsx Example
    \brief This is a simplest Qt Xlsx example.
    \ingroup qtxlsx-examples

    This example demonstrates how to create a new
     .xlsx file containing some basic data and calculations
    with Qt Xlsx Library. So lets see how this is achieved.
    \image hello.png

    This creates a new instance of the all important Document
    class which gives you access to the Excel workbook and worksheets.
    \snippet hello/main.cpp 0

    A default worksheet have been created by Document. Let's start
    by adding some basic data.
    \snippet hello/main.cpp 1

    Now save the file and all its components.
    \snippet hello/main.cpp 2
*/
