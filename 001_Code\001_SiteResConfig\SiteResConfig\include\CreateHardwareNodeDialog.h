#pragma once

/**
 * @file CreateHardwareNodeDialog.h
 * @brief 创建硬件节点对话框类定义
 * @details 使用Qt Designer设计的硬件节点创建对话框
 * <AUTHOR> Assistant
 * @date 2025-08-06
 * @version 1.0.0
 */

#include <QtWidgets/QDialog>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QCheckBox>
#include <QtWidgets/QScrollArea>
#include <QtCore/QString>
#include <QtCore/QList>
#include "NodeConfigDialog.h"  // 包含ChannelInfo和ChannelConfigWidget定义

QT_BEGIN_NAMESPACE
QT_END_NAMESPACE

namespace Ui {
class CreateHardwareNodeDialog;
}

namespace UI {

// 使用NodeConfigDialog.h中已定义的ChannelInfo

/**
 * @brief 硬件节点创建参数结构体
 * @details 存储硬件节点的所有创建参数
 */
struct CreateHardwareNodeParams {
    QString nodeName;           // 节点名称 (如 LD-B1, LD-B2)
    int channelCount;          // 通道数量 (1-32) - 支持更多通道
    QList<ChannelInfo> channels; // 通道列表

    CreateHardwareNodeParams()
        : nodeName("LD-B1"), channelCount(2) {
        // 默认添加2个通道
        channels.append(ChannelInfo(1, "192.168.1.100", 8080));
        channels.append(ChannelInfo(2, "192.168.1.101", 8081));
    }
};

/**
 * @brief 创建硬件节点对话框类
 * @details 支持动态生成任意数量的通道配置
 */
class CreateHardwareNodeDialog : public QDialog {
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param suggestedName 建议的节点名称
     * @param parent 父窗口
     */
    explicit CreateHardwareNodeDialog(const QString& suggestedName = "LD-B1", QWidget* parent = nullptr);

    /**
     * @brief 析构函数
     */
    virtual ~CreateHardwareNodeDialog();

    /**
     * @brief 获取硬件节点创建参数
     * @return 硬件节点创建参数结构体
     */
    CreateHardwareNodeParams getCreateHardwareNodeParams() const;

    /**
     * @brief 设置硬件节点参数（用于编辑模式）
     * @param params 硬件节点参数
     */
    void setHardwareNodeParams(const CreateHardwareNodeParams& params);

    /**
     * @brief 设置为编辑模式
     * @param isEditMode 是否为编辑模式
     */
    void setEditMode(bool isEditMode = true);

private slots:
    /**
     * @brief 通道数量改变槽函数
     */
    void onChannelCountChanged();

    /**
     * @brief 确定按钮点击前的验证
     */
    void onAcceptClicked();

private:
    Ui::CreateHardwareNodeDialog* ui;
    QList<ChannelConfigWidget> channelWidgets_; // 动态通道控件列表
    QScrollArea* scrollArea_; // 滚动区域
    QWidget* scrollWidget_; // 滚动内容区域
    QVBoxLayout* scrollLayout_; // 滚动区域布局

    /**
     * @brief 初始化界面
     */
    void initializeUI();

    /**
     * @brief 连接信号槽
     */
    void connectSignals();

    /**
     * @brief 更新通道界面
     */
    void updateChannelUI();

    /**
     * @brief 设置滚动区域
     */
    void setupScrollArea();

    /**
     * @brief 创建单个通道配置控件
     * @param channelId 通道ID
     * @return 通道配置控件结构体
     */
    ChannelConfigWidget createChannelWidget(int channelId);

    /**
     * @brief 清理所有通道控件
     */
    void clearChannelWidgets();
};

} // namespace UI
