@echo off
echo ========================================
echo  测试去掉SensorParams中的sensorName字段
echo ========================================

REM 设置Qt环境变量
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目（测试去掉sensorName字段）...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo 请检查上面的错误信息。
    echo.
    echo 🔍 可能的问题:
    echo 1. 还有代码在使用params.sensorName
    echo 2. 结构体定义不一致
    echo 3. 头文件包含问题
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！sensorName字段已成功去掉
    echo ========================================
    
    echo.
    echo ✅ 修改完成的内容:
    echo 1. SensorParams结构体：去掉QString sensorName字段
    echo 2. XLSDataExporter：去掉传感器名称列，调整列号
    echo 3. 完整项目导出：更新表头和列宽
    echo 4. 序列号用途：用于显示和组内唯一性检查
    echo.
    echo 🔧 修改详情:
    echo.
    echo 📋 SensorParams结构体修改:
    echo - 修改前: int sensorId; QString sensorName; QString serialNumber;
    echo - 修改后: int sensorId; QString serialNumber; // 序列号（用于显示和组内唯一性检查）
    echo.
    echo 📊 Excel导出格式修改:
    echo.
    echo 传感器详细配置（单独导出）:
    echo - 修改前: 34列（组序号, 传感器组名称, 传感器序号, 传感器序列号, ...）
    echo - 修改后: 34列（组序号, 传感器组名称, 传感器序号, 传感器序列号, ...）
    echo - 说明: 保持34列不变，没有传感器名称列
    echo.
    echo 传感器详细配置（完整项目导出）:
    echo - 修改前: 33列（传感器ID, 传感器名称, 序列号, ...）
    echo - 修改后: 32列（传感器ID, 序列号, ...）
    echo - 说明: 去掉传感器名称列，总列数减1
    echo.
    echo 🎯 序列号的新用途:
    echo - 显示标识: 在UI中显示传感器时使用序列号
    echo - 唯一性检查: 在组内检查传感器唯一性时使用序列号
    echo - 数据存储: 作为传感器的主要标识符
    echo - 导出格式: 在Excel和CSV导出中作为主要标识
    echo.
    echo 📝 数据结构对比:
    echo.
    echo UI::SensorParams（界面用）:
    echo - 修改前: sensorId, sensorName, serialNumber, sensorType, ...
    echo - 修改后: sensorId, serialNumber, sensorType, ...
    echo.
    echo DataModels::SensorInfo（项目数据用）:
    echo - 保持不变: sensorId, sensorName, serialNumber, ...
    echo - 说明: 项目数据模型保持原有结构
    echo.
    
    if exist "SiteResConfig.exe" (
        echo 启动程序验证修改...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 启动程序验证修改...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 启动程序验证修改...
        start release\SiteResConfig.exe
    )
)

echo.
echo 📖 验证步骤:
echo.
echo 🎮 传感器创建和管理验证:
echo 1. 启动软件，新建项目
echo 2. 创建传感器组，添加传感器
echo 3. 验证传感器显示使用序列号
echo 4. 验证组内唯一性检查使用序列号
echo 5. 确认没有传感器名称相关的界面元素
echo.
echo 🎮 Excel导出验证:
echo 1. 导出传感器详细信息到Excel（单独导出）
echo 2. 验证表头为34列，没有"传感器名称"列
echo 3. 验证数据正确，序列号作为主要标识
echo 4. 导出完整项目到Excel
echo 5. 验证传感器工作表为32列，没有"传感器名称"列
echo.
echo 🎮 数据一致性验证:
echo 1. 创建多个传感器，使用不同序列号
echo 2. 验证序列号唯一性检查正常
echo 3. 验证传感器数据保存和读取正常
echo 4. 验证CSV导出功能正常
echo.
echo ✅ 预期结果:
echo - 编译成功，无sensorName相关错误
echo - 传感器功能正常，使用序列号作为标识
echo - Excel导出格式正确，列数正确
echo - 数据完整性保持，功能无缺失
echo.
echo 🚨 如果测试失败:
echo - 检查是否还有代码使用params.sensorName
echo - 验证Excel导出的列数和表头
echo - 确认序列号唯一性检查正常
echo - 检查数据保存和读取功能
echo.
pause
