@echo off
echo.
echo ========================================
echo Actuator Workflow Replacement Test
echo ========================================
echo.

echo Actuator workflow has been replaced:
echo.
echo 1. OnCreateActuator() - Now uses ActuatorDialog1_1
echo 2. OnEditActuatorDevice() - Now uses ActuatorDialog1_1
echo 3. OnDeleteActuatorDevice() - Now uses ActuatorDataManager1_1
echo 4. CreateActuatorDevice1_1() - New method for creating nodes
echo.
echo No new menus or actions added - only existing workflow replaced.
echo All operations use the same right-click context menus.
echo.

if exist "SiteResConfig\SiteResConfig_Simple.pro" (
    echo Found project file, testing compilation...
    echo.
    
    cd SiteResConfig
    
    echo Cleaning old files...
    if exist "Makefile" del /Q "Makefile" 2>nul
    if exist "debug" rmdir /S /Q "debug" 2>nul
    if exist "release" rmdir /S /Q "release" 2>nul
    if exist "ui_*.h" del /Q "ui_*.h" 2>nul
    
    echo.
    echo Running qmake...
    qmake SiteResConfig_Simple.pro -spec win32-g++ "CONFIG+=debug" 2>&1
    
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo qmake successful!
        echo.
        echo Starting compilation and linking...
        mingw32-make debug 2>&1
        
        if %ERRORLEVEL% EQU 0 (
            echo.
            echo *** COMPILATION SUCCESSFUL! ***
            echo.
            echo Actuator workflow replacement completed!
            echo All existing operations now use Actuator1_1 system.
            echo.
            
            if exist "debug\SiteResConfig.exe" (
                echo Executable created successfully!
                echo.
                echo Replaced workflow features:
                echo 1. Right-click actuator group -^> Create uses ActuatorDialog1_1
                echo 2. Right-click actuator device -^> Edit uses ActuatorDialog1_1
                echo 3. Right-click actuator device -^> Delete uses ActuatorDataManager1_1
                echo 4. Tree nodes show format: Name [Model]
                echo 5. Tooltips show complete 23-field information
                echo.
                
                set /p choice="Launch program to test replaced workflow? (y/n): "
                if /i "%choice%"=="y" (
                    echo Launching program...
                    start "" "debug\SiteResConfig.exe"
                    echo.
                    echo Test Instructions:
                    echo 1. Right-click actuator group -^> Create actuator
                    echo 2. Test 4-tab ActuatorDialog1_1 interface
                    echo 3. Verify tree node shows "Name [Model]" format
                    echo 4. Right-click created actuator -^> Edit
                    echo 5. Verify data is pre-filled correctly
                    echo 6. Right-click actuator -^> Delete
                    echo 7. Verify deletion works with confirmation
                )
            ) else (
                echo ERROR: Executable not found
            )
        ) else (
            echo.
            echo *** COMPILATION FAILED ***
            echo Please check the error messages above.
        )
    ) else (
        echo.
        echo *** QMAKE FAILED ***
        echo Please check Qt environment configuration.
    )
    
    cd ..
) else (
    echo ERROR: Project file not found
)

echo.
echo ========================================
echo Workflow Replacement Test Completed
echo ========================================
echo.
pause
