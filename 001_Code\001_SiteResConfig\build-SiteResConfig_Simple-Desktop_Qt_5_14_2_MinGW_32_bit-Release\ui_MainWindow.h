/********************************************************************************
** Form generated from reading UI file 'MainWindow.ui'
**
** Created by: Qt User Interface Compiler version 5.14.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MAINWINDOW_H
#define UI_MAINWINDOW_H

#include <CustomTreeWidgets.h>
#include <QtCore/QVariant>
#include <QtWidgets/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QCheckBox>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QMenu>
#include <QtWidgets/QMenuBar>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QSplitter>
#include <QtWidgets/QStatusBar>
#include <QtWidgets/QTextEdit>
#include <QtWidgets/QTreeWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>
#include <include/ControlChannelEditDialog.h>

QT_BEGIN_NAMESPACE

class Ui_MainWindow
{
public:
    QAction *actionNewProject;
    QAction *actionOpenProject;
    QAction *actionSaveProject;
    QAction *actionSaveAsProject;
    QAction *actionRecentProjects;
    QAction *actionExit;
    QAction *actionTestConfig;
    QAction *actionShowHardwarePanel;
    QAction *actionShowTestPanel;
    QAction *actionFullScreen;
    QAction *actionSystemSettings;
    QAction *actionCalibration;
    QAction *actionUserManual;
    QAction *actionTechnicalSupport;
    QAction *actionRestoreColors;
    QAction *actionAbout;
    QAction *actionNodeLD_B1;
    QAction *actionNodeLD_B2;
    QWidget *centralwidget;
    QVBoxLayout *verticalLayout_4;
    QHBoxLayout *horizontalLayout_2;
    QGroupBox *groupBox;
    QVBoxLayout *verticalLayout;
    QTreeWidget *hardwareTreeWidget;
    QGroupBox *groupBox_2;
    QHBoxLayout *horizontalLayout;
    CustomTestConfigTreeWidget *testConfigTreeWidget;
    QGroupBox *groupBox_4;
    QVBoxLayout *verticalLayout_2;
    UI::ControlChannelEditDialog *widgetChanSet;
    QGroupBox *detailInfoGroupBox;
    QVBoxLayout *verticalLayout_detail;
    QCheckBox *chkShowDetailInfoDlg;
    QWidget *detailInfoWidget;
    QSpacerItem *verticalSpacer;
    QSplitter *mainSplitter;
    QWidget *rightPanel;
    QVBoxLayout *rightPanelLayout;
    QGroupBox *groupBox_3;
    QVBoxLayout *verticalLayout_3;
    QHBoxLayout *logToolbarLayout;
    QPushButton *clearLogButton;
    QPushButton *saveLogButton;
    QSpacerItem *logToolbarSpacer;
    QTextEdit *logTextEdit;
    QMenuBar *menubar;
    QMenu *menuFile;
    QMenu *menuHardware;
    QMenu *menuTest;
    QMenu *menuView;
    QMenu *menuTools;
    QMenu *menuHelp;
    QStatusBar *statusbar;
    QLabel *statusLabel;

    void setupUi(QMainWindow *MainWindow)
    {
        if (MainWindow->objectName().isEmpty())
            MainWindow->setObjectName(QString::fromUtf8("MainWindow"));
        MainWindow->resize(1400, 900);
        MainWindow->setMinimumSize(QSize(1200, 800));
        actionNewProject = new QAction(MainWindow);
        actionNewProject->setObjectName(QString::fromUtf8("actionNewProject"));
        actionOpenProject = new QAction(MainWindow);
        actionOpenProject->setObjectName(QString::fromUtf8("actionOpenProject"));
        actionSaveProject = new QAction(MainWindow);
        actionSaveProject->setObjectName(QString::fromUtf8("actionSaveProject"));
        actionSaveAsProject = new QAction(MainWindow);
        actionSaveAsProject->setObjectName(QString::fromUtf8("actionSaveAsProject"));
        actionRecentProjects = new QAction(MainWindow);
        actionRecentProjects->setObjectName(QString::fromUtf8("actionRecentProjects"));
        actionExit = new QAction(MainWindow);
        actionExit->setObjectName(QString::fromUtf8("actionExit"));
        actionTestConfig = new QAction(MainWindow);
        actionTestConfig->setObjectName(QString::fromUtf8("actionTestConfig"));
        actionShowHardwarePanel = new QAction(MainWindow);
        actionShowHardwarePanel->setObjectName(QString::fromUtf8("actionShowHardwarePanel"));
        actionShowHardwarePanel->setCheckable(true);
        actionShowHardwarePanel->setChecked(true);
        actionShowTestPanel = new QAction(MainWindow);
        actionShowTestPanel->setObjectName(QString::fromUtf8("actionShowTestPanel"));
        actionShowTestPanel->setCheckable(true);
        actionShowTestPanel->setChecked(true);
        actionFullScreen = new QAction(MainWindow);
        actionFullScreen->setObjectName(QString::fromUtf8("actionFullScreen"));
        actionSystemSettings = new QAction(MainWindow);
        actionSystemSettings->setObjectName(QString::fromUtf8("actionSystemSettings"));
        actionCalibration = new QAction(MainWindow);
        actionCalibration->setObjectName(QString::fromUtf8("actionCalibration"));
        actionUserManual = new QAction(MainWindow);
        actionUserManual->setObjectName(QString::fromUtf8("actionUserManual"));
        actionTechnicalSupport = new QAction(MainWindow);
        actionTechnicalSupport->setObjectName(QString::fromUtf8("actionTechnicalSupport"));
        actionRestoreColors = new QAction(MainWindow);
        actionRestoreColors->setObjectName(QString::fromUtf8("actionRestoreColors"));
        actionAbout = new QAction(MainWindow);
        actionAbout->setObjectName(QString::fromUtf8("actionAbout"));
        actionNodeLD_B1 = new QAction(MainWindow);
        actionNodeLD_B1->setObjectName(QString::fromUtf8("actionNodeLD_B1"));
        actionNodeLD_B2 = new QAction(MainWindow);
        actionNodeLD_B2->setObjectName(QString::fromUtf8("actionNodeLD_B2"));
        centralwidget = new QWidget(MainWindow);
        centralwidget->setObjectName(QString::fromUtf8("centralwidget"));
        verticalLayout_4 = new QVBoxLayout(centralwidget);
        verticalLayout_4->setObjectName(QString::fromUtf8("verticalLayout_4"));
        horizontalLayout_2 = new QHBoxLayout();
        horizontalLayout_2->setObjectName(QString::fromUtf8("horizontalLayout_2"));
        horizontalLayout_2->setContentsMargins(0, -1, -1, -1);
        groupBox = new QGroupBox(centralwidget);
        groupBox->setObjectName(QString::fromUtf8("groupBox"));
        groupBox->setMaximumSize(QSize(400, 16777215));
        verticalLayout = new QVBoxLayout(groupBox);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        hardwareTreeWidget = new QTreeWidget(groupBox);
        hardwareTreeWidget->setObjectName(QString::fromUtf8("hardwareTreeWidget"));
        hardwareTreeWidget->setAlternatingRowColors(true);
        hardwareTreeWidget->setSelectionMode(QAbstractItemView::SingleSelection);
        hardwareTreeWidget->setIndentation(20);
        hardwareTreeWidget->setRootIsDecorated(false);
        hardwareTreeWidget->setItemsExpandable(true);
        hardwareTreeWidget->setAnimated(true);
        hardwareTreeWidget->setHeaderHidden(true);

        verticalLayout->addWidget(hardwareTreeWidget);


        horizontalLayout_2->addWidget(groupBox);

        groupBox_2 = new QGroupBox(centralwidget);
        groupBox_2->setObjectName(QString::fromUtf8("groupBox_2"));
        horizontalLayout = new QHBoxLayout(groupBox_2);
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        testConfigTreeWidget = new CustomTestConfigTreeWidget(groupBox_2);
        testConfigTreeWidget->setObjectName(QString::fromUtf8("testConfigTreeWidget"));
        testConfigTreeWidget->setAlternatingRowColors(true);
        testConfigTreeWidget->setSelectionMode(QAbstractItemView::SingleSelection);
        testConfigTreeWidget->setIndentation(20);
        testConfigTreeWidget->setRootIsDecorated(false);
        testConfigTreeWidget->setAnimated(true);
        testConfigTreeWidget->setHeaderHidden(false);

        horizontalLayout->addWidget(testConfigTreeWidget);

        groupBox_4 = new QGroupBox(groupBox_2);
        groupBox_4->setObjectName(QString::fromUtf8("groupBox_4"));
        groupBox_4->setMinimumSize(QSize(0, 0));
        verticalLayout_2 = new QVBoxLayout(groupBox_4);
        verticalLayout_2->setObjectName(QString::fromUtf8("verticalLayout_2"));
        widgetChanSet = new UI::ControlChannelEditDialog(groupBox_4);
        widgetChanSet->setObjectName(QString::fromUtf8("widgetChanSet"));

        verticalLayout_2->addWidget(widgetChanSet);


        horizontalLayout->addWidget(groupBox_4);

        detailInfoGroupBox = new QGroupBox(groupBox_2);
        detailInfoGroupBox->setObjectName(QString::fromUtf8("detailInfoGroupBox"));
        QSizePolicy sizePolicy(QSizePolicy::Fixed, QSizePolicy::Expanding);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(detailInfoGroupBox->sizePolicy().hasHeightForWidth());
        detailInfoGroupBox->setSizePolicy(sizePolicy);
        detailInfoGroupBox->setMinimumSize(QSize(0, 0));
        detailInfoGroupBox->setMaximumSize(QSize(120, 16777215));
        verticalLayout_detail = new QVBoxLayout(detailInfoGroupBox);
        verticalLayout_detail->setObjectName(QString::fromUtf8("verticalLayout_detail"));
        verticalLayout_detail->setContentsMargins(0, 0, 0, 0);
        chkShowDetailInfoDlg = new QCheckBox(detailInfoGroupBox);
        chkShowDetailInfoDlg->setObjectName(QString::fromUtf8("chkShowDetailInfoDlg"));

        verticalLayout_detail->addWidget(chkShowDetailInfoDlg);

        detailInfoWidget = new QWidget(detailInfoGroupBox);
        detailInfoWidget->setObjectName(QString::fromUtf8("detailInfoWidget"));
        QSizePolicy sizePolicy1(QSizePolicy::Expanding, QSizePolicy::Expanding);
        sizePolicy1.setHorizontalStretch(0);
        sizePolicy1.setVerticalStretch(0);
        sizePolicy1.setHeightForWidth(detailInfoWidget->sizePolicy().hasHeightForWidth());
        detailInfoWidget->setSizePolicy(sizePolicy1);
        detailInfoWidget->setMinimumSize(QSize(0, 400));

        verticalLayout_detail->addWidget(detailInfoWidget);

        verticalSpacer = new QSpacerItem(0, 0, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_detail->addItem(verticalSpacer);


        horizontalLayout->addWidget(detailInfoGroupBox);


        horizontalLayout_2->addWidget(groupBox_2);


        verticalLayout_4->addLayout(horizontalLayout_2);

        mainSplitter = new QSplitter(centralwidget);
        mainSplitter->setObjectName(QString::fromUtf8("mainSplitter"));
        mainSplitter->setOrientation(Qt::Horizontal);
        mainSplitter->setHandleWidth(8);
        mainSplitter->setChildrenCollapsible(false);
        rightPanel = new QWidget(mainSplitter);
        rightPanel->setObjectName(QString::fromUtf8("rightPanel"));
        rightPanel->setMinimumSize(QSize(700, 0));
        rightPanelLayout = new QVBoxLayout(rightPanel);
        rightPanelLayout->setSpacing(8);
        rightPanelLayout->setObjectName(QString::fromUtf8("rightPanelLayout"));
        rightPanelLayout->setContentsMargins(4, 4, 4, 4);
        groupBox_3 = new QGroupBox(rightPanel);
        groupBox_3->setObjectName(QString::fromUtf8("groupBox_3"));
        verticalLayout_3 = new QVBoxLayout(groupBox_3);
        verticalLayout_3->setObjectName(QString::fromUtf8("verticalLayout_3"));
        logToolbarLayout = new QHBoxLayout();
        logToolbarLayout->setSpacing(8);
        logToolbarLayout->setObjectName(QString::fromUtf8("logToolbarLayout"));
        logToolbarLayout->setContentsMargins(0, 0, 0, 0);
        clearLogButton = new QPushButton(groupBox_3);
        clearLogButton->setObjectName(QString::fromUtf8("clearLogButton"));

        logToolbarLayout->addWidget(clearLogButton);

        saveLogButton = new QPushButton(groupBox_3);
        saveLogButton->setObjectName(QString::fromUtf8("saveLogButton"));

        logToolbarLayout->addWidget(saveLogButton);

        logToolbarSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        logToolbarLayout->addItem(logToolbarSpacer);


        verticalLayout_3->addLayout(logToolbarLayout);

        logTextEdit = new QTextEdit(groupBox_3);
        logTextEdit->setObjectName(QString::fromUtf8("logTextEdit"));
        QFont font;
        font.setFamily(QString::fromUtf8("Consolas"));
        font.setPointSize(9);
        logTextEdit->setFont(font);
        logTextEdit->setReadOnly(true);

        verticalLayout_3->addWidget(logTextEdit);


        rightPanelLayout->addWidget(groupBox_3);

        mainSplitter->addWidget(rightPanel);

        verticalLayout_4->addWidget(mainSplitter);

        MainWindow->setCentralWidget(centralwidget);
        menubar = new QMenuBar(MainWindow);
        menubar->setObjectName(QString::fromUtf8("menubar"));
        menubar->setGeometry(QRect(0, 0, 1400, 26));
        menuFile = new QMenu(menubar);
        menuFile->setObjectName(QString::fromUtf8("menuFile"));
        menuHardware = new QMenu(menubar);
        menuHardware->setObjectName(QString::fromUtf8("menuHardware"));
        menuTest = new QMenu(menubar);
        menuTest->setObjectName(QString::fromUtf8("menuTest"));
        menuView = new QMenu(menubar);
        menuView->setObjectName(QString::fromUtf8("menuView"));
        menuTools = new QMenu(menubar);
        menuTools->setObjectName(QString::fromUtf8("menuTools"));
        menuHelp = new QMenu(menubar);
        menuHelp->setObjectName(QString::fromUtf8("menuHelp"));
        MainWindow->setMenuBar(menubar);
        statusbar = new QStatusBar(MainWindow);
        statusbar->setObjectName(QString::fromUtf8("statusbar"));
        statusLabel = new QLabel(statusbar);
        statusLabel->setObjectName(QString::fromUtf8("statusLabel"));
        statusLabel->setGeometry(QRect(0, 0, 100, 30));
        MainWindow->setStatusBar(statusbar);

        menubar->addAction(menuFile->menuAction());
        menubar->addAction(menuHardware->menuAction());
        menubar->addAction(menuTest->menuAction());
        menubar->addAction(menuView->menuAction());
        menubar->addAction(menuTools->menuAction());
        menubar->addAction(menuHelp->menuAction());
        menuFile->addAction(actionNewProject);
        menuFile->addAction(actionOpenProject);
        menuFile->addSeparator();
        menuFile->addAction(actionSaveProject);
        menuFile->addAction(actionSaveAsProject);
        menuFile->addSeparator();
        menuFile->addAction(actionRecentProjects);
        menuFile->addSeparator();
        menuFile->addAction(actionExit);
        menuTest->addSeparator();
        menuTest->addAction(actionTestConfig);
        menuView->addAction(actionShowHardwarePanel);
        menuView->addAction(actionShowTestPanel);
        menuView->addSeparator();
        menuView->addAction(actionFullScreen);
        menuTools->addAction(actionSystemSettings);
        menuTools->addAction(actionCalibration);
        menuTools->addSeparator();
        menuHelp->addAction(actionUserManual);
        menuHelp->addAction(actionTechnicalSupport);
        menuHelp->addSeparator();
        menuHelp->addAction(actionRestoreColors);
        menuHelp->addSeparator();
        menuHelp->addAction(actionAbout);

        retranslateUi(MainWindow);

        QMetaObject::connectSlotsByName(MainWindow);
    } // setupUi

    void retranslateUi(QMainWindow *MainWindow)
    {
        MainWindow->setWindowTitle(QCoreApplication::translate("MainWindow", "SiteResConfig - \347\201\265\345\212\250\345\212\240\350\275\275\344\270\212\344\275\215\346\234\272\347\256\241\347\220\206\350\275\257\344\273\266", nullptr));
        actionNewProject->setText(QCoreApplication::translate("MainWindow", "\346\226\260\345\273\272\345\267\245\347\250\213(&N)", nullptr));
#if QT_CONFIG(tooltip)
        actionNewProject->setToolTip(QCoreApplication::translate("MainWindow", "\345\210\233\345\273\272\346\226\260\347\232\204\350\257\225\351\252\214\345\267\245\347\250\213", nullptr));
#endif // QT_CONFIG(tooltip)
#if QT_CONFIG(shortcut)
        actionNewProject->setShortcut(QCoreApplication::translate("MainWindow", "Ctrl+N", nullptr));
#endif // QT_CONFIG(shortcut)
        actionOpenProject->setText(QCoreApplication::translate("MainWindow", "\346\211\223\345\274\200\345\267\245\347\250\213(&O)", nullptr));
#if QT_CONFIG(tooltip)
        actionOpenProject->setToolTip(QCoreApplication::translate("MainWindow", "\346\211\223\345\274\200\345\267\262\346\234\211\347\232\204\350\257\225\351\252\214\345\267\245\347\250\213", nullptr));
#endif // QT_CONFIG(tooltip)
#if QT_CONFIG(shortcut)
        actionOpenProject->setShortcut(QCoreApplication::translate("MainWindow", "Ctrl+O", nullptr));
#endif // QT_CONFIG(shortcut)
        actionSaveProject->setText(QCoreApplication::translate("MainWindow", "\344\277\235\345\255\230\345\267\245\347\250\213(&S)", nullptr));
#if QT_CONFIG(tooltip)
        actionSaveProject->setToolTip(QCoreApplication::translate("MainWindow", "\344\277\235\345\255\230\345\275\223\345\211\215\345\267\245\347\250\213", nullptr));
#endif // QT_CONFIG(tooltip)
#if QT_CONFIG(shortcut)
        actionSaveProject->setShortcut(QCoreApplication::translate("MainWindow", "Ctrl+S", nullptr));
#endif // QT_CONFIG(shortcut)
        actionSaveAsProject->setText(QCoreApplication::translate("MainWindow", "\345\257\274\345\207\272\345\267\245\347\250\213(&E)...", nullptr));
#if QT_CONFIG(tooltip)
        actionSaveAsProject->setToolTip(QCoreApplication::translate("MainWindow", "\345\257\274\345\207\272\350\257\225\351\252\214\351\205\215\347\275\256\344\270\272JSON\346\226\207\344\273\266", nullptr));
#endif // QT_CONFIG(tooltip)
#if QT_CONFIG(shortcut)
        actionSaveAsProject->setShortcut(QCoreApplication::translate("MainWindow", "Ctrl+Shift+S", nullptr));
#endif // QT_CONFIG(shortcut)
        actionRecentProjects->setText(QCoreApplication::translate("MainWindow", "\346\234\200\350\277\221\345\267\245\347\250\213(&R)", nullptr));
#if QT_CONFIG(tooltip)
        actionRecentProjects->setToolTip(QCoreApplication::translate("MainWindow", "\346\211\223\345\274\200\346\234\200\350\277\221\344\275\277\347\224\250\347\232\204\345\267\245\347\250\213", nullptr));
#endif // QT_CONFIG(tooltip)
        actionExit->setText(QCoreApplication::translate("MainWindow", "\351\200\200\345\207\272(&X)", nullptr));
#if QT_CONFIG(tooltip)
        actionExit->setToolTip(QCoreApplication::translate("MainWindow", "\351\200\200\345\207\272\345\272\224\347\224\250\347\250\213\345\272\217", nullptr));
#endif // QT_CONFIG(tooltip)
#if QT_CONFIG(shortcut)
        actionExit->setShortcut(QCoreApplication::translate("MainWindow", "Alt+F4", nullptr));
#endif // QT_CONFIG(shortcut)
        actionTestConfig->setText(QCoreApplication::translate("MainWindow", "\350\257\225\351\252\214\351\205\215\347\275\256(&C)", nullptr));
#if QT_CONFIG(tooltip)
        actionTestConfig->setToolTip(QCoreApplication::translate("MainWindow", "\351\205\215\347\275\256\350\257\225\351\252\214\345\217\202\346\225\260", nullptr));
#endif // QT_CONFIG(tooltip)
        actionShowHardwarePanel->setText(QCoreApplication::translate("MainWindow", "\347\241\254\344\273\266\351\235\242\346\235\277(&H)", nullptr));
#if QT_CONFIG(tooltip)
        actionShowHardwarePanel->setToolTip(QCoreApplication::translate("MainWindow", "\346\230\276\347\244\272/\351\232\220\350\227\217\347\241\254\344\273\266\351\235\242\346\235\277", nullptr));
#endif // QT_CONFIG(tooltip)
        actionShowTestPanel->setText(QCoreApplication::translate("MainWindow", "\350\257\225\351\252\214\351\235\242\346\235\277(&T)", nullptr));
#if QT_CONFIG(tooltip)
        actionShowTestPanel->setToolTip(QCoreApplication::translate("MainWindow", "\346\230\276\347\244\272/\351\232\220\350\227\217\350\257\225\351\252\214\351\235\242\346\235\277", nullptr));
#endif // QT_CONFIG(tooltip)
        actionFullScreen->setText(QCoreApplication::translate("MainWindow", "\345\205\250\345\261\217\346\250\241\345\274\217(&F)", nullptr));
#if QT_CONFIG(tooltip)
        actionFullScreen->setToolTip(QCoreApplication::translate("MainWindow", "\345\210\207\346\215\242\345\205\250\345\261\217\346\250\241\345\274\217", nullptr));
#endif // QT_CONFIG(tooltip)
#if QT_CONFIG(shortcut)
        actionFullScreen->setShortcut(QCoreApplication::translate("MainWindow", "F11", nullptr));
#endif // QT_CONFIG(shortcut)
        actionSystemSettings->setText(QCoreApplication::translate("MainWindow", "\347\263\273\347\273\237\350\256\276\347\275\256(&S)", nullptr));
#if QT_CONFIG(tooltip)
        actionSystemSettings->setToolTip(QCoreApplication::translate("MainWindow", "\351\205\215\347\275\256\347\263\273\347\273\237\350\256\276\347\275\256", nullptr));
#endif // QT_CONFIG(tooltip)
        actionCalibration->setText(QCoreApplication::translate("MainWindow", "\344\274\240\346\204\237\345\231\250\346\240\207\345\256\232(&C)", nullptr));
#if QT_CONFIG(tooltip)
        actionCalibration->setToolTip(QCoreApplication::translate("MainWindow", "\344\274\240\346\204\237\345\231\250\346\240\207\345\256\232\345\212\237\350\203\275", nullptr));
#endif // QT_CONFIG(tooltip)
        actionUserManual->setText(QCoreApplication::translate("MainWindow", "\347\224\250\346\210\267\346\211\213\345\206\214(&U)", nullptr));
#if QT_CONFIG(tooltip)
        actionUserManual->setToolTip(QCoreApplication::translate("MainWindow", "\346\237\245\347\234\213\347\224\250\346\210\267\346\211\213\345\206\214", nullptr));
#endif // QT_CONFIG(tooltip)
#if QT_CONFIG(shortcut)
        actionUserManual->setShortcut(QCoreApplication::translate("MainWindow", "F1", nullptr));
#endif // QT_CONFIG(shortcut)
        actionTechnicalSupport->setText(QCoreApplication::translate("MainWindow", "\346\212\200\346\234\257\346\224\257\346\214\201(&T)", nullptr));
#if QT_CONFIG(tooltip)
        actionTechnicalSupport->setToolTip(QCoreApplication::translate("MainWindow", "\350\201\224\347\263\273\346\212\200\346\234\257\346\224\257\346\214\201", nullptr));
#endif // QT_CONFIG(tooltip)
        actionRestoreColors->setText(QCoreApplication::translate("MainWindow", "\346\201\242\345\244\215\351\242\234\350\211\262(&R)", nullptr));
#if QT_CONFIG(tooltip)
        actionRestoreColors->setToolTip(QCoreApplication::translate("MainWindow", "\346\201\242\345\244\215\346\211\200\346\234\211\346\240\221\345\275\242\346\216\247\344\273\266\347\232\204\351\242\234\350\211\262", nullptr));
#endif // QT_CONFIG(tooltip)
        actionAbout->setText(QCoreApplication::translate("MainWindow", "\345\205\263\344\272\216(&A)", nullptr));
#if QT_CONFIG(tooltip)
        actionAbout->setToolTip(QCoreApplication::translate("MainWindow", "\345\205\263\344\272\216\346\234\254\350\275\257\344\273\266", nullptr));
#endif // QT_CONFIG(tooltip)
        actionNodeLD_B1->setText(QCoreApplication::translate("MainWindow", "LD-B1", nullptr));
#if QT_CONFIG(tooltip)
        actionNodeLD_B1->setToolTip(QCoreApplication::translate("MainWindow", "\351\205\215\347\275\256LD-B1\350\212\202\347\202\271", nullptr));
#endif // QT_CONFIG(tooltip)
        actionNodeLD_B2->setText(QCoreApplication::translate("MainWindow", "LD-B2", nullptr));
#if QT_CONFIG(tooltip)
        actionNodeLD_B2->setToolTip(QCoreApplication::translate("MainWindow", "\351\205\215\347\275\256LD-B2\350\212\202\347\202\271", nullptr));
#endif // QT_CONFIG(tooltip)
        groupBox->setTitle(QCoreApplication::translate("MainWindow", "\347\241\254\344\273\266\350\265\204\346\272\220", nullptr));
        QTreeWidgetItem *___qtreewidgetitem = hardwareTreeWidget->headerItem();
        ___qtreewidgetitem->setText(0, QCoreApplication::translate("MainWindow", "\347\241\254\344\273\266\350\265\204\346\272\220", nullptr));
        groupBox_2->setTitle(QCoreApplication::translate("MainWindow", "\350\257\225\351\252\214\350\265\204\346\272\220", nullptr));
        QTreeWidgetItem *___qtreewidgetitem1 = testConfigTreeWidget->headerItem();
        ___qtreewidgetitem1->setText(5, QCoreApplication::translate("MainWindow", "\346\236\201\346\200\247", nullptr));
        ___qtreewidgetitem1->setText(4, QCoreApplication::translate("MainWindow", "\344\275\277\350\203\275", nullptr));
        ___qtreewidgetitem1->setText(3, QCoreApplication::translate("MainWindow", "\347\253\231\347\202\271id", nullptr));
        ___qtreewidgetitem1->setText(2, QCoreApplication::translate("MainWindow", "\344\270\213\344\275\215\346\234\272id", nullptr));
        ___qtreewidgetitem1->setText(1, QCoreApplication::translate("MainWindow", "\345\205\263\350\201\224\344\277\241\346\201\257", nullptr));
        ___qtreewidgetitem1->setText(0, QCoreApplication::translate("MainWindow", "\350\257\225\351\252\214\351\205\215\347\275\256", nullptr));
        groupBox_4->setTitle(QCoreApplication::translate("MainWindow", "\351\200\232\351\201\223\350\256\276\347\275\256", nullptr));
        detailInfoGroupBox->setTitle(QCoreApplication::translate("MainWindow", "\350\257\246\347\273\206\344\277\241\346\201\257", nullptr));
        chkShowDetailInfoDlg->setText(QCoreApplication::translate("MainWindow", "\346\230\276\347\244\272\350\257\246\347\273\206\344\277\241\346\201\257", nullptr));
        groupBox_3->setTitle(QCoreApplication::translate("MainWindow", "\346\227\245\345\277\227", nullptr));
#if QT_CONFIG(tooltip)
        clearLogButton->setToolTip(QCoreApplication::translate("MainWindow", "\346\270\205\347\251\272\346\230\276\347\244\272\347\232\204\346\227\245\345\277\227", nullptr));
#endif // QT_CONFIG(tooltip)
        clearLogButton->setText(QCoreApplication::translate("MainWindow", "\346\270\205\347\251\272\346\227\245\345\277\227", nullptr));
#if QT_CONFIG(tooltip)
        saveLogButton->setToolTip(QCoreApplication::translate("MainWindow", "\344\277\235\345\255\230\346\227\245\345\277\227\345\210\260\346\226\207\344\273\266", nullptr));
#endif // QT_CONFIG(tooltip)
        saveLogButton->setText(QCoreApplication::translate("MainWindow", "\344\277\235\345\255\230\346\227\245\345\277\227", nullptr));
        logTextEdit->setHtml(QCoreApplication::translate("MainWindow", "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n"
"<html><head><meta name=\"qrichtext\" content=\"1\" /><style type=\"text/css\">\n"
"p, li { white-space: pre-wrap; }\n"
"</style></head><body style=\" font-family:'Consolas'; font-size:9pt; font-weight:400; font-style:normal;\">\n"
"<p style=\"-qt-paragraph-type:empty; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><br /></p></body></html>", nullptr));
        menuFile->setTitle(QCoreApplication::translate("MainWindow", "\346\226\207\344\273\266(&F)", nullptr));
        menuHardware->setTitle(QCoreApplication::translate("MainWindow", "\347\241\254\344\273\266(&H)", nullptr));
        menuTest->setTitle(QCoreApplication::translate("MainWindow", "\350\257\225\351\252\214(&T)", nullptr));
        menuView->setTitle(QCoreApplication::translate("MainWindow", "\350\247\206\345\233\276(&V)", nullptr));
        menuTools->setTitle(QCoreApplication::translate("MainWindow", "\345\267\245\345\205\267(&T)", nullptr));
        menuHelp->setTitle(QCoreApplication::translate("MainWindow", "\345\270\256\345\212\251(&H)", nullptr));
        statusLabel->setText(QCoreApplication::translate("MainWindow", "\345\260\261\347\273\252", nullptr));
    } // retranslateUi

};

namespace Ui {
    class MainWindow: public Ui_MainWindow {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MAINWINDOW_H
