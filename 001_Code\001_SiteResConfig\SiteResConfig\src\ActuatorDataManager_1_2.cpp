/**
 * @file ActuatorDataManager_1_2.cpp
 * @brief 作动器数据管理器实现
 * <AUTHOR> Assistant
 * @date 2025-08-14
 * @version 1.0.0
 */

#include "ActuatorDataManager_1_2.h"
#include "DataModels_Fixed.h"
#include <QtCore/QDebug>
#include <QtCore/QDateTime>
#include <QtCore/QRegularExpression>
#include <QtCore/QSet>
#include <algorithm>
#include <QtCore/QTimer> // 🆕 新增：用于延迟信号发送

ActuatorDataManager_1_2::ActuatorDataManager_1_2()
    : nextGroupId_(1)
    , nextActuatorId_(1) // 🆕 新增：初始化作动器ID计数器
{
    clearError();
    initializeStorage();
}

ActuatorDataManager_1_2::~ActuatorDataManager_1_2()
{
}

// 🆕 作动器数据管理接口（与传感器对等）
bool ActuatorDataManager_1_2::saveActuatorDetailedParams(const UI::ActuatorParams_1_2& params, int groupId) {
    return addActuator(params, groupId);
}

//UI::ActuatorParams_1_2 ActuatorDataManager_1_2::getActuatorDetailedParams(const QString& serialNumber) const {
//    // 获取作动器所属的组ID
//    int groupId = getActuatorGroupId(serialNumber);
//    if (groupId == -1) {
//        return UI::ActuatorParams_1_2(); // 返回空参数
//    }
//    return getActuator(serialNumber, groupId);
//} // ❌ 注释：不再使用无组ID版本

UI::ActuatorParams_1_2 ActuatorDataManager_1_2::getActuatorDetailedParams(const QString& serialNumber, int groupId) const {
    return getActuator(serialNumber, groupId);
}

bool ActuatorDataManager_1_2::updateActuatorDetailedParams(const QString& serialNumber, const UI::ActuatorParams_1_2& params, int groupId) {
    return updateActuator(serialNumber, params, groupId);
}

bool ActuatorDataManager_1_2::removeActuatorDetailedParams(const QString& serialNumber, int groupId) {
    return removeActuator(serialNumber, groupId);
}

QStringList ActuatorDataManager_1_2::getAllActuatorSerialNumbers() const {
    QStringList serialNumbers; 
    {
        // 🔄 修改：从分层存储中收集所有序列号
        for (auto groupIt = groupedActuatorStorage_.begin(); groupIt != groupedActuatorStorage_.end(); ++groupIt) {
            const auto& groupActuators = groupIt.value();
            for (auto actuatorIt = groupActuators.begin(); actuatorIt != groupActuators.end(); ++actuatorIt) {
                serialNumbers.append(actuatorIt.key());
            }
        }
    }
    
    serialNumbers.sort();
    return serialNumbers;
}

QList<UI::ActuatorParams_1_2> ActuatorDataManager_1_2::getAllActuatorDetailedParams() const {
    return getAllActuators();
}

// 🆕 新增类型转换辅助方法
QString ActuatorDataManager_1_2::actuatorTypeToString(UI::ActuatorType_1_2 type) {
    switch (type) {
        case UI::ActuatorType_1_2::SingleRod: return "单出杆";
        case UI::ActuatorType_1_2::DoubleRod: return "双出杆";
        default: return "未知";
    }
}

UI::ActuatorType_1_2 ActuatorDataManager_1_2::stringToActuatorType(const QString& str) {
    if (str == "单出杆" || str == "1") return UI::ActuatorType_1_2::SingleRod;
    if (str == "双出杆" || str == "2") return UI::ActuatorType_1_2::DoubleRod;
    return UI::ActuatorType_1_2::SingleRod; // 默认值
}

QString ActuatorDataManager_1_2::polarityToString(UI::Polarity_1_2 polarity) {
    // 极性存储必须使用数字格式，不再使用文本描述
    switch (polarity) {
        case UI::Polarity_1_2::Positive: return "1";
        case UI::Polarity_1_2::Negative: return "-1";
        case UI::Polarity_1_2::Both: return "9";
        default: return "0";
    }
}

UI::Polarity_1_2 ActuatorDataManager_1_2::stringToPolarity(const QString& str) {
    // 只支持新格式和数字格式，完全移除旧格式兼容
    if (str.contains("正极性") || str == "1") return UI::Polarity_1_2::Positive;
    if (str.contains("负极性") || str == "-1") return UI::Polarity_1_2::Negative;
    if (str.contains("双极性") || str == "9") return UI::Polarity_1_2::Both;
    if (str.contains("未知") || str == "0") return UI::Polarity_1_2::Unknown;
    return UI::Polarity_1_2::Positive; // 默认正极性
}

QString ActuatorDataManager_1_2::measurementUnitToString(UI::MeasurementUnit_1_2 unit) {
    switch (unit) {
        case UI::MeasurementUnit_1_2::Meter: return "m";
        case UI::MeasurementUnit_1_2::Millimeter: return "mm";
        case UI::MeasurementUnit_1_2::Centimeter: return "cm";
        case UI::MeasurementUnit_1_2::Inch: return "inch";
        default: return "mm";
    }
}

UI::MeasurementUnit_1_2 ActuatorDataManager_1_2::stringToMeasurementUnit(const QString& str) {
    if (str == "m" || str == "1") return UI::MeasurementUnit_1_2::Meter;
    if (str == "mm" || str == "2") return UI::MeasurementUnit_1_2::Millimeter;
    if (str == "cm" || str == "3") return UI::MeasurementUnit_1_2::Centimeter;
    if (str == "inch" || str == "4") return UI::MeasurementUnit_1_2::Inch;
    return UI::MeasurementUnit_1_2::Millimeter; // 默认值
}

// 作动器数据操作（基础接口）
//bool ActuatorDataManager_1_2::addActuator(const UI::ActuatorParams_1_2& params) {
//    // 🔄 修改：默认使用组ID 1（向后兼容）
//    return addActuator(params, 1);
//} // ❌ 注释：不再使用无组ID版本

// 🆕 新增：指定组ID的addActuator方法
bool ActuatorDataManager_1_2::addActuator(const UI::ActuatorParams_1_2& params, int groupId) {
    if (!validateActuatorParams(params)) {
        return false;
    }

    if (!isValidGroupId(groupId)) {
        setError(QString(u8"无效的组ID: %1").arg(groupId));
        return false;
    }

    try {
        // 🆕 修改：使用组内递增ID分配策略
        UI::ActuatorParams_1_2 actuatorWithId = params;
        if (actuatorWithId.actuatorId == 0) {
            // 分配组内递增ID（从1开始）
            actuatorWithId.actuatorId = getNextActuatorIdInGroup(groupId);
        }
        // 如果已经有ID，则直接使用（支持手动指定组内ID）

        // 🔄 修改：检查组内序列号唯一性
        if (groupedActuatorStorage_.contains(groupId) && 
            groupedActuatorStorage_[groupId].contains(params.params.sn)) {
            setError(QString(u8"组内作动器序列号重复: %1").arg(params.params.sn));
            return false;
        }

        // 🔄 修改：保存到分层存储中
        groupedActuatorStorage_[groupId][params.params.sn] = actuatorWithId;
        
        // 🆕 新增：同步更新到组存储中（确保数据一致性）
        if (groupStorage_.contains(groupId)) {
            // 如果组已存在，将作动器添加到组中
            UI::ActuatorGroup_1_2& group = groupStorage_[groupId];
            
            // 检查作动器是否已在组中存在
            bool actuatorExists = false;
            for (int i = 0; i < group.actuators.size(); ++i) {
                if (group.actuators[i].params.sn == actuatorWithId.params.sn) {
                    group.actuators[i] = actuatorWithId; // 更新现有作动器
                    actuatorExists = true;
                    break;
                }
            }
            
            if (!actuatorExists) {
                group.actuators.append(actuatorWithId); // 添加新作动器
            }
        }
        
        clearError();
        
        // 🆕 新增：发出数据变化信号，通知详细信息面板更新
        // 🔧 修复：延迟发出信号，确保数据完全保存后再通知
        QTimer::singleShot(100, [this, serialNumber = params.params.sn, groupId]() {
            // 🆕 新增：检查对象是否仍然有效
//            if (!this) {
//                qWarning() << "❌ [ActuatorDataManager] 延迟发送信号时this指针无效，跳过";
//                return;
//            }
            
            // 🆕 新增：检查对象是否仍然有效（通过父对象检查）
            try {
                if (!this->parent() && !this->objectName().isNull()) {
                    qWarning() << "❌ [ActuatorDataManager] 延迟发送信号时对象已无效，跳过";
                    return;
                }
            } catch (...) {
                qWarning() << "❌ [ActuatorDataManager] 延迟发送信号时对象状态检查失败，跳过";
                return;
            }
            
            // 🆕 新增：安全地发送信号
            try {
                emit actuatorDataChanged(serialNumber, "create");
                emit actuatorGroupDataChanged(groupId, "create");

                qDebug() << "✅ [ActuatorDataManager] 延迟信号发送成功: 作动器=" << serialNumber << "组ID=" << groupId << "\r\n\r\n\r\n\r\n\r\n\r\n";
                qDebug() << "✅ [ActuatorDataManager] 延迟信号发送成功: 作动器=" << serialNumber << "组ID=" << groupId << "\r\n\r\n\r\n\r\n\r\n\r\n";
                qDebug() << "✅ [ActuatorDataManager] 延迟信号发送成功: 作动器=" << serialNumber << "组ID=" << groupId << "\r\n\r\n\r\n\r\n\r\n\r\n";
                qDebug() << "✅ [ActuatorDataManager] 延迟信号发送成功: 作动器=" << serialNumber << "组ID=" << groupId << "\r\n\r\n\r\n\r\n\r\n\r\n";
                qDebug() << "✅ [ActuatorDataManager] 延迟信号发送成功: 作动器=" << serialNumber << "组ID=" << groupId << "\r\n\r\n\r\n\r\n\r\n\r\n";

                qDebug() << "✅ [ActuatorDataManager] 延迟信号发送成功: 作动器=" << serialNumber << "组ID=" << groupId;
            } catch (const std::exception& e) {
                qWarning() << "❌ [ActuatorDataManager] 延迟发送信号时发生异常:" << e.what();
            } catch (...) {
                qWarning() << "❌ [ActuatorDataManager] 延迟发送信号时发生未知异常";
            }
        });
        
        return true;
        
    } catch (const std::exception& e) {
        setError(QString(u8"添加作动器失败: %1").arg(e.what()));
        return false;
    } catch (...) {
        setError(QString(u8"添加作动器时发生未知异常"));
        return false;
    }
}

//UI::ActuatorParams_1_2 ActuatorDataManager_1_2::getActuator(const QString& serialNumber) const {
//    if (!isValidSerialNumber(serialNumber)) {
//        setError(u8"无效的序列号");
//        return UI::ActuatorParams_1_2();
//    }
//
//    try {
//        // 🔄 修改：在所有组中查找序列号
//        for (auto groupIt = groupedActuatorStorage_.begin(); groupIt != groupedActuatorStorage_.end(); ++groupIt) {
//            const auto& groupActuators = groupIt.value();
//            auto actuatorIt = groupActuators.find(serialNumber);
//            if (actuatorIt != groupActuators.end()) {
//                return actuatorIt.value();
//            }
//        }
//        setError(QString(u8"作动器不存在: %1").arg(serialNumber));
//        return UI::ActuatorParams_1_2();
//    } catch (const std::exception& e) {
//        setError(QString(u8"获取作动器失败: %1").arg(e.what()));
//        return UI::ActuatorParams_1_2();
//    }
//} // ❌ 注释：不再使用无组ID版本

UI::ActuatorParams_1_2 ActuatorDataManager_1_2::getActuator(const QString& serialNumber, int groupId) const {
    if (!isValidGroupId(groupId)) {
        setError(u8"无效的组ID");
        return UI::ActuatorParams_1_2();
    }

    if (!isValidSerialNumber(serialNumber)) {
        setError(u8"无效的序列号");
        return UI::ActuatorParams_1_2();
    }

    try {
        // 🔄 修改：在指定组中查找序列号
        if (groupedActuatorStorage_.contains(groupId)) {
            const auto& groupActuators = groupedActuatorStorage_[groupId];
            auto actuatorIt = groupActuators.find(serialNumber);
            if (actuatorIt != groupActuators.end()) {
                return actuatorIt.value();
            }
        }
        setError(QString(u8"作动器不存在: %1").arg(serialNumber));
        return UI::ActuatorParams_1_2();
    } catch (const std::exception& e) {
        setError(QString(u8"获取作动器失败: %1").arg(e.what()));
        return UI::ActuatorParams_1_2();
    }
}

// 🆕 新增：获取作动器所在的组ID
int ActuatorDataManager_1_2::getActuatorGroupId(const QString& serialNumber) const {
    if (!isValidSerialNumber(serialNumber)) {
        setError(u8"无效的序列号");
        return -1;
    }

    try {
        // 在所有组中查找序列号，返回找到的组ID
        for (auto groupIt = groupedActuatorStorage_.begin(); groupIt != groupedActuatorStorage_.end(); ++groupIt) {
            int groupId = groupIt.key();
            const auto& groupActuators = groupIt.value();
            auto actuatorIt = groupActuators.find(serialNumber);
            if (actuatorIt != groupActuators.end()) {
                clearError();
                return groupId;
            }
        }
        setError(QString(u8"作动器不存在: %1").arg(serialNumber));
        return -1;
    } catch (const std::exception& e) {
        setError(QString(u8"获取作动器组ID失败: %1").arg(e.what()));
        return -1;
    }
}

//bool ActuatorDataManager_1_2::updateActuator(const QString& serialNumber, const UI::ActuatorParams_1_2& params) {
//    if (!isValidSerialNumber(serialNumber)) {
//        setError(u8"无效的序列号");
//        return false;
//    }
//
//    if (!validateActuatorParams(params)) {
//        return false; // 错误信息已在validateActuatorParams中设置
//    }
//
//    if (!hasActuator(serialNumber)) {
//        setError(QString(u8"作动器不存在: %1").arg(serialNumber));
//        return false;
//    }
//
//    try {
//        // 🔄 修改：在分层存储中查找并更新作动器
//        UI::ActuatorParams_1_2 updatedParams = params;
//
//        // 查找作动器所在的组并获取原有ID
//        for (auto groupIt = groupedActuatorStorage_.begin(); groupIt != groupedActuatorStorage_.end(); ++groupIt) {
//            auto& groupActuators = groupIt.value();
//            auto actuatorIt = groupActuators.find(serialNumber);
//            if (actuatorIt != groupActuators.end()) {
//                int originalId = actuatorIt.value().actuatorId;
//                if (updatedParams.actuatorId == 0 || updatedParams.actuatorId != originalId) {
//                    updatedParams.actuatorId = originalId; // 保持原有ID
//                }
//                // 更新分层存储中的数据
//                actuatorIt.value() = updatedParams;
//                clearError();
//                return true;
//            }
//        }
//
//        setError(QString(u8"作动器不存在: %1").arg(serialNumber));
//        return false;
//    } catch (const std::exception& e) {
//        setError(QString(u8"更新作动器失败: %1").arg(e.what()));
//        return false;
//    }
//} // ❌ 注释：不再使用无组ID版本

bool ActuatorDataManager_1_2::updateActuator(const QString& serialNumber, const UI::ActuatorParams_1_2& params, int groupId) {
    if (!isValidGroupId(groupId)) {
        setError(u8"无效的组ID");
        return false;
    }

    if (!isValidSerialNumber(serialNumber)) {
        setError(u8"无效的序列号");
        return false;
    }

    if (!validateActuatorParams(params)) {
        return false; // 错误信息已在validateActuatorParams中设置
    }

    if (!hasActuator(serialNumber, groupId)) {
        setError(QString(u8"作动器不存在: %1").arg(serialNumber));
        return false;
    }

    try {
        // 🔄 修改：在分层存储中查找并更新作动器
        UI::ActuatorParams_1_2 updatedParams = params;

        // 查找作动器所在的组并获取原有ID
        if (groupedActuatorStorage_.contains(groupId)) {
            auto& groupActuators = groupedActuatorStorage_[groupId];
            auto actuatorIt = groupActuators.find(serialNumber);
            if (actuatorIt != groupActuators.end()) {
                int originalId = actuatorIt.value().actuatorId;
                if (updatedParams.actuatorId == 0 || updatedParams.actuatorId != originalId) {
                    updatedParams.actuatorId = originalId; // 保持原有ID
                }
                // 更新分层存储中的数据
                actuatorIt.value() = updatedParams;
                
                // 🔧 修复：同步更新到组存储中
                if (groupStorage_.contains(groupId)) {
                    auto& group = groupStorage_[groupId];
                    for (auto& actuator : group.actuators) {
                        if (actuator.params.sn == serialNumber) {
                            actuator = updatedParams;
                            break;
                        }
                    }
                }
                
                clearError();
                return true;
            }
        }

        setError(QString(u8"作动器不存在: %1").arg(serialNumber));
        return false;
    } catch (const std::exception& e) {
        setError(QString(u8"更新作动器失败: %1").arg(e.what()));
        return false;
    }
}

//bool ActuatorDataManager_1_2::removeActuator(const QString& serialNumber) {
//    if (!isValidSerialNumber(serialNumber)) {
//        setError(u8"无效的序列号");
//        return false;
//    }
//
//    if (!hasActuator(serialNumber)) {
//        setError(QString(u8"作动器不存在: %1").arg(serialNumber));
//        return false;
//    }
//
//    try {
//        // 🔄 修改：从分层存储中删除作动器
//        for (auto groupIt = groupedActuatorStorage_.begin(); groupIt != groupedActuatorStorage_.end(); ++groupIt) {
//            auto& groupActuators = groupIt.value();
//            auto actuatorIt = groupActuators.find(serialNumber);
//            if (actuatorIt != groupActuators.end()) {
//                groupActuators.erase(actuatorIt);
//                clearError();
//                return true;
//            }
//        }
//        
//        setError(QString(u8"作动器不存在: %1").arg(serialNumber));
//        return false;
//    } catch (const std::exception& e) {
//        setError(QString(u8"删除作动器失败: %1").arg(e.what()));
//        return false;
//    }
//} // ❌ 注释：不再使用无组ID版本

bool ActuatorDataManager_1_2::removeActuator(const QString& serialNumber, int groupId) {
    if (!isValidGroupId(groupId)) {
        setError(u8"无效的组ID");
        return false;
    }

    if (!isValidSerialNumber(serialNumber)) {
        setError(u8"无效的序列号");
        return false;
    }

    if (!hasActuator(serialNumber, groupId)) {
        setError(QString(u8"作动器不存在: %1").arg(serialNumber));
        return false;
    }

    try {
        // 🔄 修改：从分层存储中删除作动器
        if (groupedActuatorStorage_.contains(groupId)) {
            auto& groupActuators = groupedActuatorStorage_[groupId];
            auto actuatorIt = groupActuators.find(serialNumber);
            if (actuatorIt != groupActuators.end()) {
                groupActuators.erase(actuatorIt);
                
                // 🎯 关键修复：同时从组存储中删除作动器，保持数据一致性
                if (groupStorage_.contains(groupId)) {
                    UI::ActuatorGroup_1_2& group = groupStorage_[groupId];
                    auto groupActuatorIt = std::find_if(group.actuators.begin(), group.actuators.end(),
                        [&serialNumber](const UI::ActuatorParams_1_2& actuator) {
                            return actuator.params.sn == serialNumber;
                        });
                    
                    if (groupActuatorIt != group.actuators.end()) {
                        group.actuators.erase(groupActuatorIt);
                        qDebug() << QString(u8"✅ 作动器已从组存储删除: 序列号=%1, 组ID=%2").arg(serialNumber).arg(groupId);
                    }
                }
                
                clearError();
                return true;
            }
        }
        
        setError(QString(u8"作动器不存在: %1").arg(serialNumber));
        return false;
    } catch (const std::exception& e) {
        setError(QString(u8"删除作动器失败: %1").arg(e.what()));
        return false;
    }
}

//bool ActuatorDataManager_1_2::hasActuator(const QString& serialNumber) const {
//    if (!isValidSerialNumber(serialNumber)) {
//        return false;
//    }
//
//    try {
//        // 🔄 修改：在所有组中查找序列号
//        for (auto groupIt = groupedActuatorStorage_.begin(); groupIt != groupedActuatorStorage_.end(); ++groupIt) {
//            const auto& groupActuators = groupIt.value();
//            if (groupActuators.contains(serialNumber)) {
//                return true;
//            }
//        }
//        return false;
//    } catch (const std::exception& e) {
//        setError(QString(u8"检查作动器存在性失败: %1").arg(e.what()));
//        return false;
//    }
//} // ❌ 注释：不再使用无组ID版本

bool ActuatorDataManager_1_2::hasActuator(const QString& serialNumber, int groupId) const {
    if (!isValidGroupId(groupId)) {
        return false;
    }

    if (!isValidSerialNumber(serialNumber)) {
        return false;
    }

    try {
        // 🔄 修改：在指定组中查找序列号
        if (groupedActuatorStorage_.contains(groupId)) {
            const auto& groupActuators = groupedActuatorStorage_[groupId];
            if (groupActuators.contains(serialNumber)) {
                return true;
            }
        }
        return false;
    } catch (const std::exception& e) {
        setError(QString(u8"检查作动器存在性失败: %1").arg(e.what()));
        return false;
    }
}

// 作动器组管理接口
bool ActuatorDataManager_1_2::saveActuatorGroup(const UI::ActuatorGroup_1_2& group) {
    if (!validateActuatorGroup(group)) {
        return false; // 错误信息已在validateActuatorGroup中设置
    }

    try {
        groupStorage_[group.groupId] = group;
        if (group.groupId >= nextGroupId_) {
            nextGroupId_ = group.groupId + 1;
        }

        // 🔄 修改：将组内的作动器保存到分层存储中
        for (const auto& actuator : group.actuators) {
            if (!actuator.params.sn.isEmpty()) {
                groupedActuatorStorage_[group.groupId][actuator.params.sn] = actuator;
                qDebug() << QString(u8"✅ 作动器已保存到分层存储: 序列号=%1, 组ID=%2")
                            .arg(actuator.params.sn).arg(group.groupId);
            }
        }

        qDebug() << QString(u8"✅ 作动器组保存完成: 组ID=%1, 组名=%2, 作动器数量=%3, 总作动器数=%4")
                    .arg(group.groupId).arg(group.groupName).arg(group.actuators.size()).arg(getActuatorCount());

        clearError();
        return true;
    } catch (const std::exception& e) {
        setError(QString(u8"保存作动器组失败: %1").arg(e.what()));
        return false;
    }
}

UI::ActuatorGroup_1_2 ActuatorDataManager_1_2::getActuatorGroup(int groupId) const {
    if (!isValidGroupId(groupId)) {
        setError(u8"无效的组ID");
        return UI::ActuatorGroup_1_2();
    }

    try {
        {
            // 从内存存储获取
            if (groupStorage_.contains(groupId)) {
                return groupStorage_[groupId];
            } else {
                setError(QString(u8"作动器组不存在: %1").arg(groupId));
                return UI::ActuatorGroup_1_2();
            }
        }
    } catch (const std::exception& e) {
        setError(QString(u8"获取作动器组失败: %1").arg(e.what()));
        return UI::ActuatorGroup_1_2();
    }
}

bool ActuatorDataManager_1_2::updateActuatorGroup(int groupId, const UI::ActuatorGroup_1_2& group) {
    if (!isValidGroupId(groupId)) {
        setError(u8"无效的组ID");
        return false;
    }

    if (!validateActuatorGroup(group)) {
        return false; // 错误信息已在validateActuatorGroup中设置
    }

    if (!hasActuatorGroup(groupId)) {
        setError(QString(u8"作动器组不存在: %1").arg(groupId));
        return false;
    }

    try {
        {
            // 🔄 修改：清空该组的分层存储数据
            if (groupedActuatorStorage_.contains(groupId)) {
                groupedActuatorStorage_[groupId].clear();
            }

            // 更新组存储
            groupStorage_[groupId] = group;

            // 🔄 修改：将更新后组内的作动器保存到分层存储中
            for (const auto& actuator : group.actuators) {
                if (!actuator.params.sn.isEmpty()) {
                    groupedActuatorStorage_[groupId][actuator.params.sn] = actuator;
                }
            }
        }
        
        clearError();
        return true;
    } catch (const std::exception& e) {
        setError(QString(u8"更新作动器组失败: %1").arg(e.what()));
        return false;
    }
}

bool ActuatorDataManager_1_2::removeActuatorGroup(int groupId) {
    if (!isValidGroupId(groupId)) {
        setError(u8"无效的组ID");
        return false;
    }

    if (!hasActuatorGroup(groupId)) {
        setError(QString(u8"作动器组不存在: %1").arg(groupId));
        return false;
    }

    try {
        {
            // 🆕 修复：先移除组内的作动器
            if (groupStorage_.contains(groupId)) {
                const UI::ActuatorGroup_1_2& group = groupStorage_[groupId];
                // 🔄 修改：从分层存储中删除该组的所有作动器
                if (groupedActuatorStorage_.contains(groupId)) {
                    groupedActuatorStorage_.remove(groupId);
                }
            }

            // 从组存储删除
            groupStorage_.remove(groupId);
        }
        
        clearError();
        return true;
    } catch (const std::exception& e) {
        setError(QString(u8"删除作动器组失败: %1").arg(e.what()));
        return false;
    }
}

QList<UI::ActuatorGroup_1_2> ActuatorDataManager_1_2::getAllActuatorGroups() const {
    QList<UI::ActuatorGroup_1_2> groups;
    
    try {
//        if (project_) {
//            // 从项目获取
//            auto groupVector = project_->getAllActuatorGroups();
//            for (const auto& group : groupVector) {
//                groups.append(group);
//            }
//        } else
        {
            // 从内存存储获取
            for (auto it = groupStorage_.begin(); it != groupStorage_.end(); ++it) {
                groups.append(it.value());
            }
        }
        
        // 🔄 修改：按创建时间排序，确保组序号按创建顺序分配
        std::sort(groups.begin(), groups.end(), [](const UI::ActuatorGroup_1_2& a, const UI::ActuatorGroup_1_2& b) {
            // 首先尝试按创建时间排序
            if (!a.createTime.isEmpty() && !b.createTime.isEmpty()) {
                QDateTime timeA = QDateTime::fromString(a.createTime, "yyyy-MM-dd hh:mm:ss");
                QDateTime timeB = QDateTime::fromString(b.createTime, "yyyy-MM-dd hh:mm:ss");
                if (timeA.isValid() && timeB.isValid()) {
                    return timeA < timeB; // 早创建的排在前面
                }
            }

            // 如果创建时间无效或为空，则按组ID排序作为备选方案
            return a.groupId < b.groupId;
        });
        
    } catch (const std::exception& e) {
        setError(QString(u8"获取所有作动器组失败: %1").arg(e.what()));
    }
    
    return groups;
}

bool ActuatorDataManager_1_2::hasActuatorGroup(int groupId) const {
    if (!isValidGroupId(groupId)) {
        return false;
    }

    try {
//        if (project_) {
//            // 检查项目中是否存在
//            return project_->hasActuatorGroup(groupId);
//        } else
        {
            // 检查内存存储
            return groupStorage_.contains(groupId);
        }
    } catch (const std::exception& e) {
        setError(QString(u8"检查作动器组存在性失败: %1").arg(e.what()));
        return false;
    }
}

// 批量操作
QList<UI::ActuatorParams_1_2> ActuatorDataManager_1_2::getAllActuators() const {
    QList<UI::ActuatorParams_1_2> actuators;

    try {
//        if (project_) {
//            // 从项目获取
//            auto serialNumbers = project_->getAllActuatorSerialNumbers();
//            for (const auto& sn : serialNumbers) {
//                auto actuator = project_->getActuatorDetailedParams(sn);
//                if (!actuator.params.sn.isEmpty()) {
//                    actuators.append(actuator);
//                }
//            }
//        } else
        {
            // 🔄 修改：从分层存储获取所有作动器
            for (auto groupIt = groupedActuatorStorage_.begin(); groupIt != groupedActuatorStorage_.end(); ++groupIt) {
                const auto& groupActuators = groupIt.value();
                for (auto actuatorIt = groupActuators.begin(); actuatorIt != groupActuators.end(); ++actuatorIt) {
                    actuators.append(actuatorIt.value());
                }
            }
        }

        // 按序列号排序
        std::sort(actuators.begin(), actuators.end(), [](const UI::ActuatorParams_1_2& a, const UI::ActuatorParams_1_2& b) {
            return a.params.sn < b.params.sn;
        });

    } catch (const std::exception& e) {
        setError(QString(u8"获取所有作动器失败: %1").arg(e.what()));
    }

    return actuators;
}

QList<UI::ActuatorParams_1_2> ActuatorDataManager_1_2::getActuatorsByType(const QString& actuatorType) const {
    QList<UI::ActuatorParams_1_2> result;
    QList<UI::ActuatorParams_1_2> allActuators = getAllActuators();

    for (const auto& actuator : allActuators) {
        if (actuatorTypeToString(actuator.type) == actuatorType) {
            result.append(actuator);
        }
    }

    return result;
}

QList<UI::ActuatorParams_1_2> ActuatorDataManager_1_2::getActuatorsByGroup(int groupId) const {
    QList<UI::ActuatorParams_1_2> result;

    if (hasActuatorGroup(groupId)) {
        UI::ActuatorGroup_1_2 group = getActuatorGroup(groupId);
        result = group.actuators;
    }

    return result;
}

QList<UI::ActuatorParams_1_2> ActuatorDataManager_1_2::getActuatorsByUnitType(const QString& unitType) const {
    QList<UI::ActuatorParams_1_2> result;
    QList<UI::ActuatorParams_1_2> allActuators = getAllActuators();

    for (const auto& actuator : allActuators) {
        if (measurementUnitToString(actuator.params.meas_unit) == unitType) {
            result.append(actuator);
        }
    }

    return result;
}

int ActuatorDataManager_1_2::getActuatorCount() const {
    try {
//        if (project_) {
//            return project_->getActuatorCount();
//        } else
        {
            // 🔄 修改：计算分层存储中所有作动器的数量
            int count = 0;
            for (auto groupIt = groupedActuatorStorage_.begin(); groupIt != groupedActuatorStorage_.end(); ++groupIt) {
                count += groupIt.value().size();
            }
            return count;
        }
    } catch (const std::exception& e) {
        setError(QString(u8"获取作动器数量失败: %1").arg(e.what()));
        return 0;
    }
}

int ActuatorDataManager_1_2::getActuatorGroupCount() const {
    try {
//        if (project_) {
//            return project_->getActuatorGroupCount();
//        } else
        {
            return groupStorage_.size();
        }
    } catch (const std::exception& e) {
        setError(QString(u8"获取作动器组数量失败: %1").arg(e.what()));
        return 0;
    }
}

// 数据验证
bool ActuatorDataManager_1_2::validateActuatorParams(const UI::ActuatorParams_1_2& params) const {
    // 检查控制量名称
    if (params.name.isEmpty()) {
        setError(u8"控制量名称不能为空");
        return false;
    }

    if (params.name.length() > 50) {
        setError(u8"控制量名称长度不能超过50个字符");
        return false;
    }

    // 检查序列号（从params.sn获取）
    if (params.params.sn.isEmpty()) {
        setError(u8"作动器序列号不能为空");
        return false;
    }

//    // 验证序列号格式：只能包含字母和数字
//    QRegularExpression regex("^[A-Za-z0-9]+$");
//    if (!regex.match(params.params.sn).hasMatch()) {
//        setError(u8"序列号只能包含字母和数字");
//        return false;
//    }

    if (params.params.sn.length() > 50) {
        setError(u8"作动器序列号长度不能超过50个字符");
        return false;
    }

    // 检查作动器类型
    if (static_cast<int>(params.type) != 1 && static_cast<int>(params.type) != 2) {
        setError(u8"作动器类型必须为单出杆(1)或双出杆(2)");
        return false;
    }

    // 检查极性
    int polarity = static_cast<int>(params.params.polarity);
    if (polarity != 0 && polarity != 1 && polarity != -1 && polarity != 9) {
        setError(u8"极性值无效，必须为0、1、-1或9");
        return false;
    }

    // 检查测量单位
    int measUnit = static_cast<int>(params.params.meas_unit);
    if (measUnit < 1 || measUnit > 4) {
        setError(u8"测量单位无效，必须为1-4");
        return false;
    }

//    // 检查测量范围
//    if (params.params.meas_range_min >= params.params.meas_range_max) {
//        setError(u8"测量下限必须小于测量上限");
//        return false;
//    }

//    // 检查输出信号范围
//    if (params.params.output_signal_range_min >= params.params.output_signal_range_max) {
//        setError(u8"输出信号下限必须小于输出信号上限");
//        return false;
//    }

//    // 检查精度
//    if (params.params.precision <= 0) {
//        setError(u8"精度必须大于0");
//        return false;
//    }

//    // 检查Unit类型
//    QStringList validUnitTypes = {"m", "mm", "cm", "inch"};
//    if (!validUnitTypes.contains(params.unitValue)) {
//        setError(u8"Unit类型必须为: m, mm, cm, inch");
//        return false;
//    }

//    // 检查数值范围
//    if (params.stroke <= 0) {
//        setError(u8"行程必须大于0");
//        return false;
//    }

//    if (params.displacement < 0) {
//        setError(u8"位移不能为负数");
//        return false;
//    }

//    if (params.tensionArea <= 0) {
//        setError(u8"拉伸面积必须大于0");
//        return false;
//    }

//    if (params.compressionArea <= 0) {
//        setError(u8"压缩面积必须大于0");
//        return false;
//    }

//    if (params.tensionArea <= params.compressionArea) {
//        setError(u8"拉伸面积必须大于压缩面积");
//        return false;
//    }

//    // 检查极性
//    if (params.polarity != "Positive" && params.polarity != "Negative") {
//        setError(u8"极性必须为'Positive'或'Negative'");
//        return false;
//    }

//    // 检查频率
//    if (params.frequency <= 0) {
//        setError(u8"频率必须大于0");
//        return false;
//    }

//    // 检查输出倍数
//    if (params.outputMultiplier <= 0) {
//        setError(u8"输出倍数必须大于0");
//        return false;
//    }

    clearError();
    return true;
}

bool ActuatorDataManager_1_2::validateActuatorGroup(const UI::ActuatorGroup_1_2& group) const {
    // 检查组ID
    if (group.groupId <= 0) {
        setError(u8"作动器组ID必须大于0");
        return false;
    }

    // 检查组名称
    if (group.groupName.isEmpty()) {
        setError(u8"作动器组名称不能为空");
        return false;
    }

    if (group.groupName.length() > 100) {
        setError(u8"作动器组名称长度不能超过100个字符");
        return false;
    }

//    // 检查作动器列表
//    if (group.actuators.isEmpty()) {
//        setError(u8"作动器组必须包含至少一个作动器");
//        return false;
//    }

    // 🔧 修改：简化验证，只验证基本参数，不验证组内冲突
    // 组内冲突检查移到专门的函数中
    for (int i = 0; i < group.actuators.size(); ++i) {
        const UI::ActuatorParams_1_2& actuator = group.actuators[i];

        // 只验证作动器参数本身
        if (!validateActuatorParams(actuator)) {
            setError(QString(u8"组内作动器%1验证失败: %2").arg(i + 1).arg(getLastError()));
            return false;
        }
    }

    clearError();
    return true;
}

// 🆕 新增：验证单个作动器在指定组内是否有冲突
bool ActuatorDataManager_1_2::validateActuatorInGroup(const UI::ActuatorParams_1_2& actuator, const UI::ActuatorGroup_1_2& group) const {
    // 验证作动器参数本身
    if (!validateActuatorParams(actuator)) {
        return false; // 错误信息已在validateActuatorParams中设置
    }

    // 检查在组内是否有序列号冲突
    for (const UI::ActuatorParams_1_2& existingActuator : group.actuators) {
        // 跳过自己（如果是更新操作）
        if (existingActuator.params.sn == actuator.params.sn) {
            continue;
        }

        // 检查序列号冲突
        if (existingActuator.params.sn == actuator.params.sn) {
            setError(QString(u8"组内作动器名称重复: %1").arg(actuator.params.sn));
            return false;
        }

        // 检查ID冲突
        if (existingActuator.actuatorId == actuator.actuatorId) {
            setError(QString(u8"组内作动器ID重复: %1").arg(actuator.actuatorId));
            return false;
        }
    }

    clearError();
    return true;
}

QStringList ActuatorDataManager_1_2::validateAllActuators() const {
    QStringList errors;
    QList<UI::ActuatorParams_1_2> allActuators = getAllActuators();

    for (const auto& actuator : allActuators) {
        if (!validateActuatorParams(actuator)) {
            errors.append(QString(u8"作动器 %1: %2").arg(actuator.params.sn).arg(getLastError()));
        }
    }

    // 检查序列号唯一性
    QStringList duplicates = findDuplicateSerialNumbers();
    for (const auto& duplicate : duplicates) {
        errors.append(QString(u8"重复的序列号: %1").arg(duplicate));
    }

    return errors;
}

QStringList ActuatorDataManager_1_2::validateAllActuatorGroups() const {
    QStringList errors;
    QList<UI::ActuatorGroup_1_2> allGroups = getAllActuatorGroups();

    for (const auto& group : allGroups) {
        if (!validateActuatorGroup(group)) {
            errors.append(QString(u8"作动器组 %1: %2").arg(group.groupId).arg(getLastError()));
        }
    }

    return errors;
}

// 数据导出
QVector<QStringList> ActuatorDataManager_1_2::exportToCSVData() const {
    QVector<QStringList> csvData;

    // 添加表头
    QStringList headers;
    headers << u8"序列号" << u8"类型" << u8"Unit类型" << u8"Unit值" << u8"行程(m)"
            << u8"位移(m)" << u8"拉伸面积(m²)" << u8"压缩面积(m²)" << u8"极性"
            << u8"Deliver(V)" << u8"频率(Hz)" << u8"输出倍数" << u8"平衡(V)" << u8"备注";
    csvData.append(headers);

    // 添加数据行
    QList<UI::ActuatorParams_1_2> allActuators = getAllActuators();
    for (const auto& actuator : allActuators) {
        QStringList row;
        row << actuator.params.sn
            << actuatorTypeToString(actuator.type)
            << measurementUnitToString(actuator.params.meas_unit)  // unitType
            << measurementUnitToString(actuator.params.meas_unit)  // unitValue
            << QString::number(actuator.params.meas_range_max, 'f', 4)  // stroke
            << QString::number(actuator.zero_offset, 'f', 4)  // displacement
            << QString::number(actuator.params.meas_range_max, 'f', 6)  // tensionArea
            << QString::number(actuator.params.meas_range_min, 'f', 6)  // compressionArea
            << QString::number(static_cast<int>(actuator.params.polarity))  // polarity
            << QString::number(actuator.params.k, 'f', 2)  // dither
            << QString::number(actuator.params.precision, 'f', 1)  // frequency
            << QString::number(actuator.params.output_signal_unit, 'f', 2)  // outputMultiplier
            << QString::number(actuator.params.b, 'f', 2)  // balance
            << actuator.name;  // notes字段替换为name字段
        csvData.append(row);
    }

    return csvData;
}

QVector<QStringList> ActuatorDataManager_1_2::exportGroupsToCSVData() const {
    QVector<QStringList> csvData;

    // 添加表头（17列完整格式）
    QStringList headers;
    headers << u8"组序号" << u8"作动器组名称" << u8"作动器序列号"
            << u8"作动器类型" << u8"Unit类型" << u8"Unit值" << u8"行程(m)" << u8"位移(m)"
            << u8"拉伸面积(m²)" << u8"压缩面积(m²)" << u8"极性" << u8"Deliver(V)"
            << u8"频率(Hz)" << u8"输出倍数" << u8"平衡(V)" << u8"备注";
    csvData.append(headers);

    // 添加数据行
    QList<UI::ActuatorGroup_1_2> allGroups = getAllActuatorGroups();
    for (const auto& group : allGroups) {
        for (int i = 0; i < group.actuators.size(); ++i) {
            const UI::ActuatorParams_1_2& actuator = group.actuators[i];
            QStringList row;
            row << QString::number(group.groupId)
                << (i == 0 ? group.groupName : QString()) // 只在第一行显示组名称
                //<< QString::number(actuator.actuatorId)
                << actuator.params.sn
                << actuatorTypeToString(actuator.type)
                << measurementUnitToString(actuator.params.meas_unit)
                << measurementUnitToString(actuator.params.meas_unit)
                << QString::number(actuator.params.meas_range_max, 'f', 4)
                << QString::number(actuator.zero_offset, 'f', 4)
                << QString::number(actuator.params.meas_range_max, 'f', 6)
                << QString::number(actuator.params.meas_range_min, 'f', 6)
                << QString::number(static_cast<int>(actuator.params.polarity))
                << QString::number(actuator.params.k, 'f', 2)
                << QString::number(actuator.params.precision, 'f', 1)
                << QString::number(actuator.params.output_signal_unit, 'f', 2)
                << QString::number(actuator.params.b, 'f', 2)
                << actuator.name;  // notes字段替换为name字段
            csvData.append(row);
        }
    }

    return csvData;
}

// 🚫 已注释：独立JSON导出功能已废弃
//QJsonArray ActuatorDataManager_1_2::exportToJSONArray() const {
//    QJsonArray jsonArray;
//    QList<UI::ActuatorParams_1_2> allActuators = getAllActuators();
//
//    for (const auto& actuator : allActuators) {
//        QJsonObject actuatorObj;
//        actuatorObj["serialNumber"] = actuator.params.sn;
//        actuatorObj["type"] = actuatorTypeToString(actuator.type);
//        actuatorObj["unitType"] = measurementUnitToString(actuator.params.meas_unit);
//        actuatorObj["unitValue"] = measurementUnitToString(actuator.params.meas_unit);
//        actuatorObj["stroke"] = actuator.params.meas_range_max;
//        actuatorObj["displacement"] = actuator.zero_offset;
//        actuatorObj["tensionArea"] = actuator.params.meas_range_max;
//        actuatorObj["compressionArea"] = actuator.params.meas_range_min;
//        actuatorObj["polarity"] = polarityToString(actuator.params.polarity);
//        actuatorObj["dither"] = actuator.params.k;
//        actuatorObj["frequency"] = actuator.params.precision;
//        actuatorObj["outputMultiplier"] = actuator.params.output_signal_unit;
//        actuatorObj["balance"] = actuator.params.b;
//        actuatorObj["cylinderDiameter"] = actuator.params.meas_range_max;  // 使用测量范围上限代替
//        actuatorObj["rodDiameter"] = actuator.params.meas_range_min;     // 使用测量范围下限代替
//        actuatorObj["notes"] = actuator.name;  // 使用name字段代替notes
//
//        jsonArray.append(actuatorObj);
//    }
//
//    return jsonArray;
//}

// 数据统计
QMap<QString, int> ActuatorDataManager_1_2::getActuatorTypeStatistics() const {
    QMap<QString, int> statistics;
    QList<UI::ActuatorParams_1_2> allActuators = getAllActuators();

    for (const auto& actuator : allActuators) {
        statistics[actuatorTypeToString(actuator.type)]++;
    }

    return statistics;
}

QMap<QString, int> ActuatorDataManager_1_2::getUnitTypeStatistics() const {
    QMap<QString, int> statistics;
    QList<UI::ActuatorParams_1_2> allActuators = getAllActuators();

    for (const auto& actuator : allActuators) {
        statistics[measurementUnitToString(actuator.params.meas_unit)]++;
    }

    return statistics;
}

QMap<QString, int> ActuatorDataManager_1_2::getPolarityStatistics() const {
    QMap<QString, int> statistics;
    QList<UI::ActuatorParams_1_2> allActuators = getAllActuators();

    for (const auto& actuator : allActuators) {
        statistics[QString::number(static_cast<int>(actuator.params.polarity))]++;
    }

    return statistics;
}

QStringList ActuatorDataManager_1_2::getUsedActuatorTypes() const {
    QSet<QString> types;
    QList<UI::ActuatorParams_1_2> allActuators = getAllActuators();

    for (const auto& actuator : allActuators) {
        types.insert(actuatorTypeToString(actuator.type));
    }

    QStringList result = types.toList();
    result.sort();
    return result;
}

QStringList ActuatorDataManager_1_2::getUsedUnitTypes() const {
    QSet<QString> unitTypes;
    QList<UI::ActuatorParams_1_2> allActuators = getAllActuators();

    for (const auto& actuator : allActuators) {
        unitTypes.insert(measurementUnitToString(actuator.params.meas_unit));
    }

    QStringList result = unitTypes.toList();
    result.sort();
    return result;
}

// 序列号管理
//QString ActuatorDataManager_1_2::generateNextSerialNumber(const QString& prefix) const {
//    QStringList existingNumbers = getAllActuatorSerialNumbers();
//    int maxNumber = 0;

//    QRegularExpression regex(QString("^%1(\\d+)$").arg(QRegularExpression::escape(prefix)));

//    for (const QString& serialNumber : existingNumbers) {
//        QRegularExpressionMatch match = regex.match(serialNumber);
//        if (match.hasMatch()) {
//            int number = match.captured(1).toInt();
//            if (number > maxNumber) {
//                maxNumber = number;
//            }
//        }
//    }

//    return QString("%1%2").arg(prefix).arg(maxNumber + 1, 3, 10, QChar('0'));
//}

// 🆕 新增：组内序列号生成
QString ActuatorDataManager_1_2::generateNextSerialNumberInGroup(int groupId, const QString& prefix) const {
    if (!isValidGroupId(groupId)) {
        // 如果组ID无效，返回默认的第一个序列号
        return QString("%1%2").arg(prefix).arg(1, 6, 10, QChar('0'));
    }
    
    // 获取组内所有现有的序列号
    QStringList existingNumbers;
    
    // 从分层存储中获取组内序列号
    if (groupedActuatorStorage_.contains(groupId)) {
        const auto& groupActuators = groupedActuatorStorage_[groupId];
        for (auto it = groupActuators.begin(); it != groupActuators.end(); ++it) {
            existingNumbers.append(it.key());  // key是序列号
        }
    }
    
    // 从组存储中获取序列号（双重保险）
    if (groupStorage_.contains(groupId)) {
        const UI::ActuatorGroup_1_2& group = groupStorage_[groupId];
        for (const auto& actuator : group.actuators) {
            if (!existingNumbers.contains(actuator.params.sn)) {
                existingNumbers.append(actuator.params.sn);
            }
        }
    }
    
    // 查找最大编号
    int maxNumber = 0;
    QRegularExpression regex(QString("^%1(\\d+)$").arg(QRegularExpression::escape(prefix)));
    
    for (const QString& serialNumber : existingNumbers) {
        QRegularExpressionMatch match = regex.match(serialNumber);
        if (match.hasMatch()) {
            int number = match.captured(1).toInt();
            if (number > maxNumber) {
                maxNumber = number;
            }
        }
    }
    
    // 返回下一个可用的序列号
    return QString("%1%2").arg(prefix).arg(maxNumber + 1, 6, 10, QChar('0'));
}

// 🆕 新增：获取下一个可用的组内作动器ID
int ActuatorDataManager_1_2::getNextActuatorIdInGroup(int groupId) const {
    if (!isValidGroupId(groupId)) {
        return 1; // 无效组ID，返回默认ID
    }

    int maxId = 0;
    if (groupedActuatorStorage_.contains(groupId)) {
        for (const auto& actuator : groupedActuatorStorage_[groupId]) {
            if (actuator.actuatorId > maxId) {
                maxId = actuator.actuatorId;
            }
        }
    }
    return maxId + 1;
}

//bool ActuatorDataManager_1_2::isSerialNumberUnique(const QString& serialNumber) const {
//    // 🔄 修改：作动器序列号改为全局唯一性检查（保持向后兼容）
//    // 实际的组内唯一性检查在 isSerialNumberUniqueInGroup 中实现
    
//    // 在所有组中查找，确保全局唯一性
//    for (auto groupIt = groupedActuatorStorage_.begin(); groupIt != groupedActuatorStorage_.end(); ++groupIt) {
//        const auto& groupActuators = groupIt.value();
//        if (groupActuators.contains(serialNumber)) {
//            return false; // 找到了，不唯一
//        }
//    }
//    return true; // 未找到，全局唯一
//}

// 🆕 新增：检查序列号在指定组内是否唯一
bool ActuatorDataManager_1_2::isSerialNumberUniqueInGroup(const QString& serialNumber, int groupId) const {
    if (!isValidGroupId(groupId) || !groupStorage_.contains(groupId)) {
        return true; // 组不存在，认为唯一
    }

    const UI::ActuatorGroup_1_2& group = groupStorage_[groupId];
    for (const auto& actuator : group.actuators) {
        if (actuator.params.sn == serialNumber) {
            return false; // 在组内找到重复
        }
    }

    return true; // 在组内唯一
}

// 🆕 新增：检查序列号在指定组内是否唯一（排除指定的作动器ID）
bool ActuatorDataManager_1_2::isSerialNumberUniqueInGroup(const QString& serialNumber, int groupId, int excludeActuatorId) const {
    if (!isValidGroupId(groupId) || !groupStorage_.contains(groupId)) {
        return true; // 组不存在，认为唯一
    }

    const UI::ActuatorGroup_1_2& group = groupStorage_[groupId];
    for (const auto& actuator : group.actuators) {
        // 排除指定的作动器ID（用于更新操作）
        if (actuator.actuatorId == excludeActuatorId) {
            continue;
        }

        if (actuator.params.sn == serialNumber) {
            return false; // 在组内找到重复
        }
    }

    return true; // 在组内唯一
}

QStringList ActuatorDataManager_1_2::findDuplicateSerialNumbers() const {
    QStringList allSerialNumbers = getAllActuatorSerialNumbers();
    QStringList duplicates;
    QSet<QString> seen;

    for (const QString& serialNumber : allSerialNumbers) {
        if (seen.contains(serialNumber)) {
            if (!duplicates.contains(serialNumber)) {
                duplicates.append(serialNumber);
            }
        } else {
            seen.insert(serialNumber);
        }
    }

    return duplicates;
}

// 错误处理
QString ActuatorDataManager_1_2::getLastError() const {
    return lastError_;
}

bool ActuatorDataManager_1_2::hasError() const {
    return !lastError_.isEmpty();
}

// 数据清理
void ActuatorDataManager_1_2::clearAllActuators() {
    try {
//        if (project_) {
//            project_->clearAllActuators();
//        } else
        {
            // 🔄 修改：清空分层存储
            groupedActuatorStorage_.clear();
        }
        clearError();
    } catch (const std::exception& e) {
        setError(QString(u8"清理所有作动器失败: %1").arg(e.what()));
    }
}

void ActuatorDataManager_1_2::clearAllActuatorGroups() {
    try {
//        if (project_) {
//            project_->clearAllActuatorGroups();
//        } else
        {
            groupStorage_.clear();
            nextGroupId_ = 1;
        }
        clearError();
    } catch (const std::exception& e) {
        setError(QString(u8"清理所有作动器组失败: %1").arg(e.what()));
    }
}

void ActuatorDataManager_1_2::clearAll() {
    clearAllActuators();
    clearAllActuatorGroups();
}

// 辅助方法
void ActuatorDataManager_1_2::clearError() const {
    lastError_.clear();
}

void ActuatorDataManager_1_2::setError(const QString& error) const {
    lastError_ = error;
    qDebug() << u8"ActuatorDataManager错误:" << error;
}

bool ActuatorDataManager_1_2::isValidSerialNumber(const QString& serialNumber) const {
    if (serialNumber.isEmpty()) {
        return false;
    }

    if (serialNumber.length() > 50) {
        return false;
    }

    // 🔄 修复：允许中文字符、字母、数字、下划线和连字符
    // 检查是否包含控制字符或其他非法字符
    for (int i = 0; i < serialNumber.length(); ++i) {
        QChar ch = serialNumber.at(i);
        // 允许：中文字符、字母、数字、下划线、连字符、空格
        if (!ch.isLetterOrNumber() && ch != '_' && ch != '-' && ch != ' ') {
            return false;
        }
    }

    return true;
}

bool ActuatorDataManager_1_2::isValidGroupId(int groupId) const {
    return groupId > 0;
}

void ActuatorDataManager_1_2::initializeStorage() {
    // 🔄 修改：初始化分层存储
    groupedActuatorStorage_.clear();
    groupStorage_.clear();
    nextGroupId_ = 1;
    nextActuatorId_ = 1;
}

void ActuatorDataManager_1_2::updateIdCounters() {
    int maxActuatorId = 0;
    int maxGroupId = 0;

    // 🔧 修改：保持全局计数器用于兼容性（主要用于组内ID分配）
    // 注意：现在主要使用组内ID分配，此计数器仅用于避免ID冲突
    for (auto groupIt = groupedActuatorStorage_.begin(); groupIt != groupedActuatorStorage_.end(); ++groupIt) {
        const auto& groupActuators = groupIt.value();
        for (auto actuatorIt = groupActuators.begin(); actuatorIt != groupActuators.end(); ++actuatorIt) {
            if (actuatorIt.value().actuatorId > maxActuatorId) {
                maxActuatorId = actuatorIt.value().actuatorId;
            }
        }
    }

    for (auto it = groupStorage_.begin(); it != groupStorage_.end(); ++it) {
        if (it.key() > maxGroupId) {
            maxGroupId = it.key();
        }
    }

    // 🆕 新增：确保全局计数器不与现有ID冲突（用于兼容性）
    nextActuatorId_ = maxActuatorId + 1;
    nextGroupId_ = maxGroupId + 1;
}

//void ActuatorDataManager_1_2::syncActuatorToProject(const UI::ActuatorParams_1_2& params) {
//    if (project_) {
//        try {
//            project_->addActuatorDetailedParams(params.serialNumber.toStdString(), params);
//        } catch (const std::exception& e) {
//            setError(QString(u8"同步作动器到项目失败: %1").arg(e.what()));
//        }
//    }
//}

//void ActuatorDataManager_1_2::syncGroupToProject(const UI::ActuatorGroup_1_2& group) {
//    if (project_) {
//        try {
//            project_->addActuatorGroup(group.groupId, group);
//        } catch (const std::exception& e) {
//            setError(QString(u8"同步作动器组到项目失败: %1").arg(e.what()));
//        }
//    }
//}

//void ActuatorDataManager_1_2::removeActuatorFromProject(const QString& serialNumber) {
//    if (project_) {
//        try {
//            project_->removeActuatorDetailedParams(serialNumber.toStdString());
//        } catch (const std::exception& e) {
//            setError(QString(u8"从项目中删除作动器失败: %1").arg(e.what()));
//        }
//    }
//}

//void ActuatorDataManager_1_2::removeGroupFromProject(int groupId) {
//    if (project_) {
//        try {
//            project_->removeActuatorGroup(groupId);
//        } catch (const std::exception& e) {
//            setError(QString(u8"从项目中删除作动器组失败: %1").arg(e.what()));
//        }
//    }
//}

// 🆕 新增：分层存储辅助方法
int ActuatorDataManager_1_2::findGroupIdBySerialNumber(const QString& serialNumber) const {
    // 在分层存储中查找序列号所属的组ID
    for (auto groupIt = groupedActuatorStorage_.begin(); groupIt != groupedActuatorStorage_.end(); ++groupIt) {
        const auto& groupActuators = groupIt.value();
        if (groupActuators.contains(serialNumber)) {
            return groupIt.key();
        }
    }
    
    // 如果在分层存储中没找到，尝试从组信息中查找
    for (auto groupIt = groupStorage_.begin(); groupIt != groupStorage_.end(); ++groupIt) {
        const UI::ActuatorGroup_1_2& group = groupIt.value();
        for (const auto& actuator : group.actuators) {
            if (actuator.params.sn == serialNumber) {
                return groupIt.key();
            }
        }
    }
    
    return -1; // 未找到
}

QList<UI::ActuatorParams_1_2> ActuatorDataManager_1_2::getActuatorsByGroupId(int groupId) const {
    QList<UI::ActuatorParams_1_2> result;
    
    if (groupedActuatorStorage_.contains(groupId)) {
        const auto& groupActuators = groupedActuatorStorage_[groupId];
        for (auto it = groupActuators.begin(); it != groupActuators.end(); ++it) {
            result.append(it.value());
        }
    }
    
    return result;
}
