@echo off
echo.
echo ========================================
echo Actuator1_1 Workflow Integration Test
echo ========================================
echo.

echo New Actuator1_1 workflow has been integrated:
echo.
echo 1. Create Actuator Flow:
echo    - OnCreateActuator() now uses ActuatorDialog1_1
echo    - Uses ActuatorDataManager1_1 for data storage
echo    - Creates nodes with CreateActuatorDevice1_1()
echo    - Supports full 23-field data structure
echo.
echo 2. Edit Actuator Flow:
echo    - OnEditActuatorDevice() now uses ActuatorDialog1_1
echo    - Loads data from ActuatorDataManager1_1
echo    - Updates tree node display and tooltips
echo    - Preserves all parameter changes
echo.
echo 3. Delete Actuator Flow:
echo    - OnDeleteActuatorDevice() now uses ActuatorDataManager1_1
echo    - Removes from both tree and data manager
echo    - Provides confirmation dialog
echo    - Updates interface after deletion
echo.
echo 4. Menu Integration:
echo    - Hardware -^> Actuator1_1 Version menu available
echo    - Ctrl+Alt+A shortcut for quick creation
echo    - Context menu for edit/delete operations
echo    - All operations use new data structure
echo.

if exist "SiteResConfig\SiteResConfig_Simple.pro" (
    echo Found project file, testing compilation...
    echo.
    
    cd SiteResConfig
    
    echo Cleaning old files...
    if exist "Makefile" del /Q "Makefile" 2>nul
    if exist "debug" rmdir /S /Q "debug" 2>nul
    if exist "release" rmdir /S /Q "release" 2>nul
    if exist "ui_*.h" del /Q "ui_*.h" 2>nul
    
    echo.
    echo Running qmake...
    qmake SiteResConfig_Simple.pro -spec win32-g++ "CONFIG+=debug" 2>&1
    
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo qmake successful!
        echo.
        echo Starting compilation and linking...
        mingw32-make debug 2>&1
        
        if %ERRORLEVEL% EQU 0 (
            echo.
            echo *** COMPILATION SUCCESSFUL! ***
            echo.
            echo Actuator1_1 workflow integration completed!
            echo All create/edit/delete operations now use new system.
            echo.
            
            if exist "debug\SiteResConfig.exe" (
                echo Executable created successfully!
                echo.
                echo New workflow features ready:
                echo 1. Right-click actuator group -^> Create uses ActuatorDialog1_1
                echo 2. Right-click actuator device -^> Edit uses ActuatorDialog1_1
                echo 3. Right-click actuator device -^> Delete uses ActuatorDataManager1_1
                echo 4. Hardware menu -^> Actuator1_1 Version for all operations
                echo 5. Ctrl+Alt+A shortcut for quick actuator creation
                echo.
                
                set /p choice="Launch program to test new workflow? (y/n): "
                if /i "%choice%"=="y" (
                    echo Launching program...
                    start "" "debug\SiteResConfig.exe"
                    echo.
                    echo Complete Workflow Test:
                    echo [ ] Right-click actuator group -^> Create actuator
                    echo [ ] Test all 4 tabs in ActuatorDialog1_1
                    echo [ ] Verify actuator appears in tree with new format
                    echo [ ] Right-click created actuator -^> Edit
                    echo [ ] Modify parameters and save changes
                    echo [ ] Verify tree node updates correctly
                    echo [ ] Right-click actuator -^> Delete
                    echo [ ] Confirm deletion removes from tree and data
                    echo [ ] Test Hardware -^> Actuator1_1 Version menu
                    echo [ ] Test Ctrl+Alt+A shortcut
                    echo [ ] Test export/import functions
                    echo [ ] Verify statistics display
                )
            ) else (
                echo ERROR: Executable not found
            )
        ) else (
            echo.
            echo *** COMPILATION FAILED ***
            echo Please check the error messages above.
        )
    ) else (
        echo.
        echo *** QMAKE FAILED ***
        echo Please check Qt environment configuration.
    )
    
    cd ..
) else (
    echo ERROR: Project file not found
)

echo.
echo ========================================
echo Workflow Integration Test Completed
echo ========================================
echo.
pause
