@echo off
echo.
echo ========================================
echo Redefinition Error Fix Test
echo ========================================
echo.

echo Fixed redefinition errors:
echo 1. Removed duplicate getExcelHeaders1_1() implementation
echo 2. Removed duplicate actuatorToExcelRow1_1() implementation
echo 3. Removed duplicate excelRowToActuator1_1() implementation
echo.

echo Each method now has only one implementation:
echo - getExcelHeaders1_1() - Returns 23 column headers
echo - actuatorToExcelRow1_1() - Converts actuator to Excel row
echo - excelRowToActuator1_1() - Parses Excel row to actuator
echo.

if exist "SiteResConfig\SiteResConfig_Simple.pro" (
    echo Found project file, testing compilation...
    echo.
    
    cd SiteResConfig
    
    echo Cleaning old files...
    if exist "Makefile" del /Q "Makefile" 2>nul
    if exist "debug" rmdir /S /Q "debug" 2>nul
    if exist "release" rmdir /S /Q "release" 2>nul
    if exist "ui_*.h" del /Q "ui_*.h" 2>nul
    
    echo.
    echo Running qmake...
    qmake SiteResConfig_Simple.pro -spec win32-g++ "CONFIG+=debug" 2>&1
    
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo qmake successful!
        echo.
        echo Starting compilation and linking...
        mingw32-make debug 2>&1
        
        if %ERRORLEVEL% EQU 0 (
            echo.
            echo *** COMPILATION SUCCESSFUL! ***
            echo.
            echo All redefinition errors have been fixed!
            echo Actuator1_1 features are now fully functional.
            echo.
            
            if exist "debug\SiteResConfig.exe" (
                echo Executable created successfully!
                echo.
                echo All features now working:
                echo 1. Create Actuator1_1 - 4-tab dialog
                echo 2. Edit/Delete Actuator1_1 - Context menu
                echo 3. Export to JSON - Complete data structure
                echo 4. Export to Excel - Tab-separated format
                echo 5. Import from Excel - Tab-separated parsing
                echo 6. Statistics Display - Detailed analysis
                echo.
                
                set /p choice="Launch program to test all features? (y/n): "
                if /i "%choice%"=="y" (
                    echo Launching program...
                    start "" "debug\SiteResConfig.exe"
                    echo.
                    echo Final Test Checklist:
                    echo [ ] Hardware -^> Actuator1_1 Version menu works
                    echo [ ] Create Actuator dialog opens correctly
                    echo [ ] All 4 tabs in dialog function properly
                    echo [ ] Data validation works correctly
                    echo [ ] Export to JSON creates valid file
                    echo [ ] Export to Excel creates tab-separated file
                    echo [ ] Import from Excel parses data correctly
                    echo [ ] Statistics display shows correct information
                    echo [ ] Edit/Delete operations work properly
                )
            ) else (
                echo ERROR: Executable not found
            )
        ) else (
            echo.
            echo *** COMPILATION FAILED ***
            echo Please check the error messages above for remaining issues.
        )
    ) else (
        echo.
        echo *** QMAKE FAILED ***
        echo Please check Qt environment configuration.
    )
    
    cd ..
) else (
    echo ERROR: Project file not found
)

echo.
echo ========================================
echo Redefinition Fix Test Completed
echo ========================================
echo.
pause
