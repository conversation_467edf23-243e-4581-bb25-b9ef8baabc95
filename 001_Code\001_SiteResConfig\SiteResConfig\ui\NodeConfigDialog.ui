<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>NodeConfigDialog</class>
 <widget class="QDialog" name="NodeConfigDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>450</width>
    <height>423</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>节点配置</string>
  </property>
  <property name="modal">
   <bool>true</bool>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QHBoxLayout" name="nodeInfoLayout">
     <item>
      <widget class="QLabel" name="nodeLabel">
       <property name="text">
        <string>节点名称:</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="nodeNameLabel">
       <property name="text">
        <string>LD-B1</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="channelCountLayout">
     <item>
      <widget class="QLabel" name="channelCountLabel">
       <property name="text">
        <string>通道数量:</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QSpinBox" name="channelCountSpinBox">
       <property name="minimumSize">
        <size>
         <width>80</width>
         <height>0</height>
        </size>
       </property>
       <property name="minimum">
        <number>1</number>
       </property>
       <property name="maximum">
        <number>200</number>
       </property>
       <property name="value">
        <number>2</number>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer_2">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QGroupBox" name="channel1GroupBox">
     <property name="title">
      <string>CH1 通道配置</string>
     </property>
     <layout class="QGridLayout" name="ch1GridLayout">
      <item row="0" column="0">
       <widget class="QLabel" name="ch1IpLabel">
        <property name="text">
         <string>IP地址:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QLineEdit" name="ch1IpEdit">
        <property name="text">
         <string>*************</string>
        </property>
        <property name="placeholderText">
         <string>请输入IP地址</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="ch1PortLabel">
        <property name="text">
         <string>端口:</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QSpinBox" name="ch1PortSpinBox">
        <property name="minimum">
         <number>1</number>
        </property>
        <property name="maximum">
         <number>65535</number>
        </property>
        <property name="value">
         <number>8080</number>
        </property>
       </widget>
      </item>
      <item row="2" column="0" colspan="2">
       <widget class="QCheckBox" name="ch1EnabledCheckBox">
        <property name="text">
         <string>启用此通道</string>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="channel2GroupBox">
     <property name="title">
      <string>CH2 通道配置</string>
     </property>
     <layout class="QGridLayout" name="ch2GridLayout">
      <item row="0" column="0">
       <widget class="QLabel" name="ch2IpLabel">
        <property name="text">
         <string>IP地址:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QLineEdit" name="ch2IpEdit">
        <property name="text">
         <string>*************</string>
        </property>
        <property name="placeholderText">
         <string>请输入IP地址</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="ch2PortLabel">
        <property name="text">
         <string>端口:</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QSpinBox" name="ch2PortSpinBox">
        <property name="minimum">
         <number>1</number>
        </property>
        <property name="maximum">
         <number>65535</number>
        </property>
        <property name="value">
         <number>8081</number>
        </property>
       </widget>
      </item>
      <item row="2" column="0" colspan="2">
       <widget class="QCheckBox" name="ch2EnabledCheckBox">
        <property name="text">
         <string>启用此通道</string>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <layout class="QHBoxLayout" name="buttonLayout">
     <item>
      <spacer name="horizontalSpacer_3">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="okButton">
       <property name="text">
        <string>确定</string>
       </property>
       <property name="default">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="cancelButton">
       <property name="text">
        <string>取消</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>cancelButton</sender>
   <signal>clicked()</signal>
   <receiver>NodeConfigDialog</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>400</x>
     <y>320</y>
    </hint>
    <hint type="destinationlabel">
     <x>225</x>
     <y>175</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
