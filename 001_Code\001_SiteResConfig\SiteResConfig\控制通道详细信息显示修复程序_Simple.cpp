#include <QApplication>
#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QTreeWidget>
#include <QGroupBox>
#include <QMessageBox>
#include <QDebug>
#include <QHeaderView>
#include <QTableWidget>
#include <QTableWidgetItem>
#include <QFrame>
#include <QSpacerItem>
#include <QFont>
#include <QColor>
#include <QSize>

// 包含项目头文件
#include "include/DetailInfoPanel.h"
#include "include/BasicInfoWidget.h"
#include "include/Common_Fixed.h"

class TestControlChannelWindow : public QMainWindow
{
    Q_OBJECT

public:
    TestControlChannelWindow(QWidget *parent = nullptr)
        : QMainWindow(parent)
        , m_detailInfoPanel(nullptr)
        , m_basicInfoWidget(nullptr)
    {
        setWindowTitle("🧪 控制通道详细信息显示修复测试程序 - SiteResConfig_Simple");
        setMinimumSize(1200, 800);
        
        setupUI();
        setupTestData();
        setupConnections();
    }

private slots:
    void onTestFixedInfoCreation()
    {
        qDebug() << "🧪 测试修复后的信息创建...";
        
        // 创建测试用的控制通道参数
        UI::ControlChannelParams testParams;
        testParams.channelId = "CH1";
        testParams.channelName = "CH1";
        testParams.hardwareAssociation = "LD-B1 - CH1";
        testParams.load1Sensor = "载荷传感器组 - 传感器_000001";
        testParams.load2Sensor = "载荷传感器组 - 传感器_000002";
        testParams.positionSensor = "位置传感器组 - 传感器_000003";
        testParams.controlActuator = "伺服作动器组 - 伺服作动器1_1";
        testParams.lc_id = 1;
        testParams.station_id = 1;
        testParams.enable = true;
        testParams.control_mode = 4; // 力控制
        testParams.servo_control_polarity = 1;
        testParams.payload_sensor1_polarity = 1;
        testParams.payload_sensor2_polarity = 1;
        testParams.position_sensor_polarity = 1;
        
        // 使用DetailInfoPanel的静态方法创建NodeInfo
        NodeInfo nodeInfo = DetailInfoPanel::createControlChannelNodeInfo(
            "CH1", 
            QString::fromStdString(testParams.channelId), 
            testParams
        );
        
        // 设置详细信息到面板
        m_detailInfoPanel->setNodeInfo(nodeInfo);
        
        QMessageBox::information(this, "测试完成", 
            "✅ 修复后的控制通道信息创建完成！\n\n"
            "请检查详细信息面板中的13列数据是否正确显示：\n"
            "• 载荷1传感器选择列\n"
            "• 载荷2传感器选择列\n"
            "• 位置传感器选择列\n"
            "• 控制作动器选择列\n"
            "• 设备关联状态\n\n"
            "如果所有列都显示正确数据，说明修复成功！");
    }
    
    void onTestChannelGroupInfo()
    {
        qDebug() << "🧪 测试控制通道组信息创建...";
        
        // 创建控制通道组信息（包含CH1和CH2）
        NodeInfo groupInfo;
        groupInfo.nodeName = "控制通道组";
        groupInfo.nodeType = "控制通道组";
        groupInfo.status = NodeStatus::Online;
        
        // 添加CH1子通道
        SubNodeInfo ch1Info;
        ch1Info.name = "CH1";
        ch1Info.type = "控制通道";
        ch1Info.status = "✅ 已启用";
        ch1Info.setProperty("硬件关联选择", "LD-B1 - CH1");
        ch1Info.setProperty("载荷1传感器选择", "载荷传感器组 - 传感器_000001");
        ch1Info.setProperty("载荷2传感器选择", "载荷传感器组 - 传感器_000002");
        ch1Info.setProperty("位置传感器选择", "位置传感器组 - 传感器_000003");
        ch1Info.setProperty("控制作动器选择", "伺服作动器组 - 伺服作动器1_1");
        ch1Info.setProperty("下位机ID", "1");
        ch1Info.setProperty("站点ID", "1");
        ch1Info.setProperty("使能状态", "✅ 已启用");
        ch1Info.setProperty("控制作动器极性", "1 (正极性)");
        ch1Info.setProperty("载荷1传感器极性", "1 (正极性)");
        ch1Info.setProperty("载荷2传感器极性", "1 (正极性)");
        ch1Info.setProperty("位置传感器极性", "1 (正极性)");
        
        // 添加CH2子通道
        SubNodeInfo ch2Info;
        ch2Info.name = "CH2";
        ch2Info.type = "控制通道";
        ch2Info.status = "✅ 已启用";
        ch2Info.setProperty("硬件关联选择", "LD-B1 - CH2");
        ch2Info.setProperty("载荷1传感器选择", "载荷传感器组 - 传感器_000004");
        ch2Info.setProperty("载荷2传感器选择", "载荷传感器组 - 传感器_000005");
        ch2Info.setProperty("位置传感器选择", "位置传感器组 - 传感器_000006");
        ch2Info.setProperty("控制作动器选择", "伺服作动器组 - 伺服作动器1_2");
        ch2Info.setProperty("下位机ID", "1");
        ch2Info.setProperty("站点ID", "1");
        ch2Info.setProperty("使能状态", "✅ 已启用");
        ch2Info.setProperty("控制作动器极性", "1 (正极性)");
        ch2Info.setProperty("载荷1传感器极性", "1 (正极性)");
        ch2Info.setProperty("载荷2传感器极性", "1 (正极性)");
        ch2Info.setProperty("位置传感器极性", "1 (正极性)");
        
        groupInfo.subNodes.append(ch1Info);
        groupInfo.subNodes.append(ch2Info);
        
        // 设置到详细信息面板
        m_detailInfoPanel->setNodeInfo(groupInfo);
        
        QMessageBox::information(this, "测试完成", 
            "✅ 控制通道组信息创建完成！\n\n"
            "请检查详细信息面板中是否显示2行数据（CH1和CH2），\n"
            "每行都包含完整的13列信息。\n\n"
            "如果显示正确，说明控制通道组信息创建功能正常！");
    }
    
    void onEditConfigRequested()
    {
        QMessageBox::information(this, "编辑配置", "编辑配置功能已触发");
    }
    
    void onViewHistoryRequested()
    {
        QMessageBox::information(this, "查看历史", "查看历史功能已触发");
    }
    
    void onExportInfoRequested()
    {
        QMessageBox::information(this, "导出信息", "导出信息功能已触发");
    }

private:
    void setupUI()
    {
        QWidget* centralWidget = new QWidget(this);
        setCentralWidget(centralWidget);
        
        QVBoxLayout* mainLayout = new QVBoxLayout(centralWidget);
        
        // 标题
        QLabel* titleLabel = new QLabel("🧪 控制通道详细信息显示修复测试程序");
        titleLabel->setAlignment(Qt::AlignCenter);
        QFont titleFont = titleFont.font();
        titleFont.setPointSize(16);
        titleFont.setBold(true);
        titleLabel->setFont(titleFont);
        mainLayout->addWidget(titleLabel);
        
        // 测试按钮区域
        QGroupBox* testGroupBox = new QGroupBox("测试功能");
        QHBoxLayout* buttonLayout = new QHBoxLayout(testGroupBox);
        
        QPushButton* testFixedButton = new QPushButton("🧪 测试修复后的信息创建");
        testFixedButton->setMinimumHeight(40);
        testFixedButton->setStyleSheet("QPushButton { font-size: 12px; font-weight: bold; }");
        
        QPushButton* testGroupButton = new QPushButton("🧪 测试控制通道组信息");
        testGroupButton->setMinimumHeight(40);
        testGroupButton->setStyleSheet("QPushButton { font-size: 12px; font-weight: bold; }");
        
        buttonLayout->addWidget(testFixedButton);
        buttonLayout->addWidget(testGroupButton);
        buttonLayout->addStretch();
        
        mainLayout->addWidget(testGroupBox);
        
        // 连接按钮信号
        connect(testFixedButton, &QPushButton::clicked, this, &TestControlChannelWindow::onTestFixedInfoCreation);
        connect(testGroupButton, &QPushButton::clicked, this, &TestControlChannelWindow::onTestChannelGroupInfo);
        
        // 详细信息面板
        m_detailInfoPanel = new DetailInfoPanel(this);
        mainLayout->addWidget(m_detailInfoPanel);
        
        // 设置样式
        setStyleSheet("QMainWindow { background-color: #f5f5f5; }"
                     "QGroupBox { font-weight: bold; border: 2px solid #bdc3c7; border-radius: 5px; margin-top: 1ex; }"
                     "QGroupBox::title { subcontrol-origin: margin; left: 10px; padding: 0 5px 0 5px; }"
                     "QPushButton { background-color: #3498db; color: white; border: none; border-radius: 5px; padding: 8px 16px; }"
                     "QPushButton:hover { background-color: #2980b9; }"
                     "QPushButton:pressed { background-color: #21618c; }");
    }
    
    void setupTestData()
    {
        // 初始状态为空
        m_detailInfoPanel->clearInfo();
    }
    
    void setupConnections()
    {
        // 连接详细信息面板信号
        connect(m_detailInfoPanel, &DetailInfoPanel::editConfigRequested,
                this, &TestControlChannelWindow::onEditConfigRequested);
        connect(m_detailInfoPanel, &DetailInfoPanel::viewHistoryRequested,
                this, &TestControlChannelWindow::onViewHistoryRequested);
        connect(m_detailInfoPanel, &DetailInfoPanel::exportInfoRequested,
                this, &TestControlChannelWindow::onExportInfoRequested);
    }

private:
    DetailInfoPanel* m_detailInfoPanel;
    BasicInfoWidget* m_basicInfoWidget;
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // 设置应用程序信息
    app.setApplicationName("控制通道详细信息显示修复测试程序");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("SiteResConfig");
    
    TestControlChannelWindow window;
    window.show();
    
    return app.exec();
}

#include "控制通道详细信息显示修复程序_Simple.moc" 