@echo off
chcp 65001 >nul
echo ========================================
echo 窗口状态修复测试程序编译和运行
echo ========================================
echo.

echo 正在检查Qt环境...
where qmake >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未找到qmake，请确保Qt环境已正确配置
    echo 请检查PATH环境变量是否包含Qt的bin目录
    pause
    exit /b 1
)

echo ✅ Qt环境检查通过
echo.

echo 正在清理旧的编译文件...
if exist Makefile del /q Makefile
if exist Makefile.Debug del /q Makefile.Debug
if exist Makefile.Release del /q Makefile.Release
if exist object_script.* del /q object_script.*
if exist .qmake.stash del /q .qmake.stash
if exist debug\ del /q /s debug\
if exist release\ del /q /s release\
echo ✅ 清理完成
echo.

echo 正在生成Makefile...
qmake test_window_state_fix.pro
if %errorlevel% neq 0 (
    echo ❌ 错误：qmake失败
    pause
    exit /b 1
)
echo ✅ Makefile生成成功
echo.

echo 正在编译测试程序...
mingw32-make
if %errorlevel% neq 0 (
    echo ❌ 错误：编译失败
    echo.
    echo 可能的解决方案：
    echo 1. 检查是否安装了MinGW编译器
    echo 2. 检查Qt版本是否与编译器兼容
    echo 3. 检查项目文件配置是否正确
    pause
    exit /b 1
)
echo ✅ 编译成功
echo.

echo 正在运行测试程序...
if exist debug\test_window_state_fix.exe (
    echo 🚀 启动测试程序...
    start debug\test_window_state_fix.exe
    echo.
    echo 📋 测试说明：
    echo 1. 程序启动后，点击"测试窗口状态"按钮
    echo 2. 窗口将最大化，然后点击树形控件中的"载荷1"、"载荷2"等节点
    echo 3. 观察窗口是否仍然保持最大化状态
    echo 4. 查看控制台输出，确认修复逻辑正确执行
    echo.
    echo ✅ 测试程序已启动，请按照上述步骤进行测试
) else (
    echo ❌ 错误：可执行文件未找到
    echo 预期路径：debug\test_window_state_fix.exe
    pause
    exit /b 1
)

echo.
echo ========================================
echo 测试完成
echo ========================================
pause 