/**
 * @file test_actuator_data_manager.cpp
 * @brief 作动器数据管理器功能测试程序
 * @details 测试作动器数据管理器的所有功能
 * <AUTHOR> Assistant
 * @date 2025-08-14
 * @version 1.0.0
 */

#include <QtCore/QCoreApplication>
#include <QtCore/QDebug>
#include <QtCore/QDateTime>
#include <QtCore/QJsonDocument>
#include <QtCore/QJsonObject>
#include <QtCore/QJsonArray>

#include "ActuatorDataManager.h"

/**
 * @brief 创建测试作动器参数
 * @param serialNumber 序列号
 * @param type 类型
 * @return 作动器参数
 */
UI::ActuatorParams createTestActuator(const QString& serialNumber, const QString& type = u8"单出杆") {
    UI::ActuatorParams actuator;
    actuator.actuatorId = 1;
    actuator.serialNumber = serialNumber;
    actuator.type = type;
    actuator.unitType = "m";
    actuator.unitName = u8"米";
    actuator.stroke = 0.15;
    actuator.displacement = 0.05;
    actuator.tensionArea = 0.0314;
    actuator.compressionArea = 0.0254;
    actuator.polarity = "Positive";
    actuator.dither = 5.0;
    actuator.frequency = 50.0;
    actuator.outputMultiplier = 1.0;
    actuator.balance = 2.5;
    actuator.cylinderDiameter = 0.2;
    actuator.rodDiameter = 0.1;
    actuator.notes = u8"测试作动器";
    return actuator;
}

/**
 * @brief 创建测试作动器组
 * @param groupId 组ID
 * @param groupName 组名称
 * @param actuatorCount 作动器数量
 * @return 作动器组
 */
UI::ActuatorGroup createTestActuatorGroup(int groupId, const QString& groupName, int actuatorCount = 2) {
    UI::ActuatorGroup group;
    group.groupId = groupId;
    group.groupName = groupName;
    group.groupType = u8"液压";
    group.createTime = QDateTime::currentDateTime().toString("yyyy-MM-dd");
    group.groupNotes = u8"测试作动器组";
    
    for (int i = 0; i < actuatorCount; ++i) {
        UI::ActuatorParams actuator = createTestActuator(QString("TEST%1_%2").arg(groupId, 2, 10, QChar('0')).arg(i + 1, 2, 10, QChar('0')));
        actuator.actuatorId = i + 1;
        group.actuators.append(actuator);
    }
    
    return group;
}

/**
 * @brief 测试基础作动器操作
 * @param manager 作动器数据管理器
 * @return 测试是否成功
 */
bool testBasicActuatorOperations(ActuatorDataManager& manager) {
    qDebug() << u8"🔧 测试基础作动器操作...";
    
    // 1. 添加作动器
    UI::ActuatorParams actuator1 = createTestActuator("ACT001");
    if (!manager.saveActuatorDetailedParams(actuator1)) {
        qDebug() << u8"❌ 添加作动器失败:" << manager.getLastError();
        return false;
    }
    qDebug() << u8"✅ 添加作动器成功: ACT001";
    
    // 2. 获取作动器
    UI::ActuatorParams retrieved = manager.getActuatorDetailedParams("ACT001");
    if (retrieved.serialNumber != "ACT001") {
        qDebug() << u8"❌ 获取作动器失败";
        return false;
    }
    qDebug() << u8"✅ 获取作动器成功: ACT001";
    
    // 3. 更新作动器
    retrieved.notes = u8"已更新的测试作动器";
    if (!manager.updateActuatorDetailedParams("ACT001", retrieved)) {
        qDebug() << u8"❌ 更新作动器失败:" << manager.getLastError();
        return false;
    }
    qDebug() << u8"✅ 更新作动器成功: ACT001";
    
    // 4. 验证更新
    UI::ActuatorParams updated = manager.getActuatorDetailedParams("ACT001");
    if (updated.notes != u8"已更新的测试作动器") {
        qDebug() << u8"❌ 验证更新失败";
        return false;
    }
    qDebug() << u8"✅ 验证更新成功";
    
    // 5. 添加更多作动器
    UI::ActuatorParams actuator2 = createTestActuator("ACT002", u8"双出杆");
    manager.saveActuatorDetailedParams(actuator2);
    
    UI::ActuatorParams actuator3 = createTestActuator("ACT003");
    manager.saveActuatorDetailedParams(actuator3);
    
    // 6. 获取所有序列号
    QStringList serialNumbers = manager.getAllActuatorSerialNumbers();
    if (serialNumbers.size() != 3) {
        qDebug() << u8"❌ 获取所有序列号失败，期望3个，实际" << serialNumbers.size() << u8"个";
        return false;
    }
    qDebug() << u8"✅ 获取所有序列号成功:" << serialNumbers;
    
    // 7. 获取所有作动器
    QList<UI::ActuatorParams> allActuators = manager.getAllActuatorDetailedParams();
    if (allActuators.size() != 3) {
        qDebug() << u8"❌ 获取所有作动器失败，期望3个，实际" << allActuators.size() << u8"个";
        return false;
    }
    qDebug() << u8"✅ 获取所有作动器成功，共" << allActuators.size() << u8"个";
    
    // 8. 删除作动器
    if (!manager.removeActuatorDetailedParams("ACT002")) {
        qDebug() << u8"❌ 删除作动器失败:" << manager.getLastError();
        return false;
    }
    qDebug() << u8"✅ 删除作动器成功: ACT002";
    
    // 9. 验证删除
    if (manager.getAllActuatorSerialNumbers().size() != 2) {
        qDebug() << u8"❌ 验证删除失败";
        return false;
    }
    qDebug() << u8"✅ 验证删除成功";
    
    return true;
}

/**
 * @brief 测试作动器组操作
 * @param manager 作动器数据管理器
 * @return 测试是否成功
 */
bool testActuatorGroupOperations(ActuatorDataManager& manager) {
    qDebug() << u8"🔧 测试作动器组操作...";
    
    // 1. 添加作动器组
    UI::ActuatorGroup group1 = createTestActuatorGroup(1, u8"主作动器组");
    if (!manager.saveActuatorGroup(group1)) {
        qDebug() << u8"❌ 添加作动器组失败:" << manager.getLastError();
        return false;
    }
    qDebug() << u8"✅ 添加作动器组成功: 主作动器组";
    
    // 2. 获取作动器组
    UI::ActuatorGroup retrieved = manager.getActuatorGroup(1);
    if (retrieved.groupName != u8"主作动器组") {
        qDebug() << u8"❌ 获取作动器组失败";
        return false;
    }
    qDebug() << u8"✅ 获取作动器组成功: 主作动器组";
    
    // 3. 添加更多作动器组
    UI::ActuatorGroup group2 = createTestActuatorGroup(2, u8"辅助作动器组", 3);
    manager.saveActuatorGroup(group2);
    
    // 4. 获取所有作动器组
    QList<UI::ActuatorGroup> allGroups = manager.getAllActuatorGroups();
    if (allGroups.size() != 2) {
        qDebug() << u8"❌ 获取所有作动器组失败，期望2个，实际" << allGroups.size() << u8"个";
        return false;
    }
    qDebug() << u8"✅ 获取所有作动器组成功，共" << allGroups.size() << u8"个";
    
    // 5. 更新作动器组
    retrieved.groupNotes = u8"已更新的作动器组";
    if (!manager.updateActuatorGroup(1, retrieved)) {
        qDebug() << u8"❌ 更新作动器组失败:" << manager.getLastError();
        return false;
    }
    qDebug() << u8"✅ 更新作动器组成功";
    
    return true;
}

/**
 * @brief 测试数据统计功能
 * @param manager 作动器数据管理器
 * @return 测试是否成功
 */
bool testStatistics(ActuatorDataManager& manager) {
    qDebug() << u8"📊 测试数据统计功能...";
    
    // 1. 获取类型统计
    QMap<QString, int> typeStats = manager.getActuatorTypeStatistics();
    qDebug() << u8"✅ 作动器类型统计:" << typeStats;
    
    // 2. 获取Unit类型统计
    QMap<QString, int> unitStats = manager.getUnitTypeStatistics();
    qDebug() << u8"✅ Unit类型统计:" << unitStats;
    
    // 3. 获取极性统计
    QMap<QString, int> polarityStats = manager.getPolarityStatistics();
    qDebug() << u8"✅ 极性统计:" << polarityStats;
    
    // 4. 获取使用的类型
    QStringList usedTypes = manager.getUsedActuatorTypes();
    qDebug() << u8"✅ 使用的作动器类型:" << usedTypes;
    
    return true;
}

/**
 * @brief 测试数据导出功能
 * @param manager 作动器数据管理器
 * @return 测试是否成功
 */
bool testDataExport(ActuatorDataManager& manager) {
    qDebug() << u8"📤 测试数据导出功能...";
    
    // 1. 导出到CSV
    QVector<QStringList> csvData = manager.exportToCSVData();
    qDebug() << u8"✅ CSV导出成功，共" << csvData.size() << u8"行";
    
    // 2. 导出作动器组到CSV
    QVector<QStringList> groupCsvData = manager.exportGroupsToCSVData();
    qDebug() << u8"✅ 作动器组CSV导出成功，共" << groupCsvData.size() << u8"行";
    
    // 3. 导出到JSON
    QJsonArray jsonArray = manager.exportToJSONArray();
    qDebug() << u8"✅ JSON数组导出成功，共" << jsonArray.size() << u8"个作动器";
    
    // 4. 导出到JSON对象
    QJsonObject jsonObj = manager.exportToJSONObject();
    qDebug() << u8"✅ JSON对象导出成功";
    
    // 5. 导出作动器组到JSON
    QJsonArray groupJsonArray = manager.exportGroupsToJSONArray();
    qDebug() << u8"✅ 作动器组JSON导出成功，共" << groupJsonArray.size() << u8"个组";
    
    return true;
}

/**
 * @brief 测试序列号管理功能
 * @param manager 作动器数据管理器
 * @return 测试是否成功
 */
bool testSerialNumberManagement(ActuatorDataManager& manager) {
    qDebug() << u8"🔢 测试序列号管理功能...";
    
    // 1. 生成下一个序列号
    QString nextSerial = manager.generateNextSerialNumber("ACT");
    qDebug() << u8"✅ 生成下一个序列号:" << nextSerial;
    
    // 2. 检查序列号唯一性
    bool isUnique = manager.isSerialNumberUnique("NEW001");
    qDebug() << u8"✅ 检查序列号唯一性 NEW001:" << (isUnique ? u8"唯一" : u8"重复");
    
    // 3. 查找重复序列号
    QStringList duplicates = manager.findDuplicateSerialNumbers();
    qDebug() << u8"✅ 查找重复序列号:" << (duplicates.isEmpty() ? u8"无重复" : duplicates.join(", "));
    
    return true;
}

int main(int argc, char *argv[]) {
    QCoreApplication app(argc, argv);
    
    qDebug() << u8"========================================";
    qDebug() << u8"🚀 作动器数据管理器功能测试";
    qDebug() << u8"========================================";
    
    // 创建作动器数据管理器（不关联项目，使用内存存储）
    ActuatorDataManager manager;
    
    bool allTestsPassed = true;
    
    // 执行各项测试
    allTestsPassed &= testBasicActuatorOperations(manager);
    qDebug() << "";
    
    allTestsPassed &= testActuatorGroupOperations(manager);
    qDebug() << "";
    
    allTestsPassed &= testStatistics(manager);
    qDebug() << "";
    
    allTestsPassed &= testDataExport(manager);
    qDebug() << "";
    
    allTestsPassed &= testSerialNumberManagement(manager);
    qDebug() << "";
    
    qDebug() << u8"========================================";
    if (allTestsPassed) {
        qDebug() << u8"🎉 所有测试通过！";
        qDebug() << u8"✅ 作动器数据管理器功能完全正常";
    } else {
        qDebug() << u8"❌ 部分测试失败！";
        qDebug() << u8"⚠️ 请检查错误信息并修复问题";
    }
    qDebug() << u8"========================================";
    
    return allTestsPassed ? 0 : 1;
}
