@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🔧 数据序号管理功能编译测试
echo ========================================
echo.

echo 📋 编译目标：
echo   ✅ DataSequenceManager类编译
echo   ✅ XLSDataExporter序号重映射功能
echo   ✅ 数据验证和报告功能
echo   ✅ 项目文件更新完成
echo.

echo 🔍 新增文件检查：
echo.
if exist "SiteResConfig\include\DataSequenceManager.h" (
    echo ✅ DataSequenceManager.h 存在
) else (
    echo ❌ DataSequenceManager.h 不存在
    goto :error
)

if exist "SiteResConfig\src\DataSequenceManager.cpp" (
    echo ✅ DataSequenceManager.cpp 存在
) else (
    echo ❌ DataSequenceManager.cpp 不存在
    goto :error
)

echo.
echo 🔧 项目文件检查：
findstr /C:"DataSequenceManager" "SiteResConfig\SiteResConfig_Simple.pro" >nul
if %errorlevel%==0 (
    echo ✅ 项目文件已更新，包含DataSequenceManager
) else (
    echo ❌ 项目文件未更新
    goto :error
)

echo.
echo 🚀 开始编译测试...
echo.

cd SiteResConfig

echo 步骤1: 清理旧的编译文件
if exist "Makefile" del "Makefile"
if exist "debug" rmdir /s /q "debug"
if exist "release" rmdir /s /q "release"
if exist "*.o" del "*.o"

echo.
echo 步骤2: 生成Makefile
qmake SiteResConfig_Simple.pro
if %errorlevel% neq 0 (
    echo ❌ qmake失败
    goto :error
)

echo.
echo 步骤3: 编译项目
make
if %errorlevel% neq 0 (
    echo ❌ 编译失败，检查错误信息
    goto :error
)

echo.
echo ✅ 编译成功！
echo.

echo 🔍 检查生成的可执行文件：
if exist "debug\SiteResConfig.exe" (
    echo ✅ Debug版本生成成功
) else (
    echo ⚠️  Debug版本未生成
)

if exist "release\SiteResConfig.exe" (
    echo ✅ Release版本生成成功
) else (
    echo ⚠️  Release版本未生成
)

echo.
echo ========================================
echo 🎉 编译测试完成
echo ========================================
echo.
echo 📋 测试结果：
echo   ✅ DataSequenceManager类编译成功
echo   ✅ XLSDataExporter修复编译成功
echo   ✅ 项目文件配置正确
echo   ✅ 可执行文件生成成功
echo.
echo 🚀 下一步：运行功能验证
echo   1. 启动应用程序
echo   2. 运行"数据序号科学管理验证.bat"
echo   3. 测试打开工程功能
echo.
goto :end

:error
echo.
echo ========================================
echo ❌ 编译测试失败
echo ========================================
echo.
echo 🔧 可能的解决方案：
echo.
echo 1. 检查Qt环境：
echo    - 确保qmake在PATH中
echo    - 确保Qt版本兼容
echo.
echo 2. 检查文件完整性：
echo    - 确保所有新增文件存在
echo    - 检查项目文件配置
echo.
echo 3. 手动编译测试：
echo    cd SiteResConfig
echo    qmake SiteResConfig_Simple.pro
echo    make
echo.
echo 4. 检查编译错误：
echo    - 查看具体的错误信息
echo    - 检查头文件包含路径
echo    - 验证语法错误
echo.

:end
cd ..
pause
