@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🔧 根节点DEBUG信息修复测试
echo ========================================
echo.

echo 📋 修复的问题:
echo.
echo ❌ 修复前:
echo    "作动器"根节点 → 作动器数据未找到
echo    "传感器"根节点 → 传感器数据未找到
echo.
echo ✅ 修复后:
echo    "作动器"根节点 → 显示统计信息
echo    "传感器"根节点 → 显示统计信息
echo.

echo 🔧 修复内容:
echo.
echo 1. 识别根节点 (parent == nullptr)
echo 2. 排除根节点的设备数据查找
echo 3. 为根节点显示合适的统计信息
echo.

echo 🎯 预期DEBUG信息:
echo.
echo "作动器"根节点:
echo    🔧 DEBUG信息 🔧
echo    ═══════════════════
echo    🔍 节点: 作动器, 类型: 未设置
echo    📊 作动器统计:
echo    组数量: 2
echo    设备数量: 5
echo.
echo "传感器"根节点:
echo    🔧 DEBUG信息 🔧
echo    ═══════════════════
echo    🔍 节点: 传感器, 类型: 未设置
echo    📊 传感器统计:
echo    组数量: 3
echo    设备数量: 8
echo.

echo 💡 测试步骤:
echo.
echo 1. 编译Debug版本
echo 2. 启动程序
echo 3. 创建一些作动器组和传感器组
echo 4. 鼠标悬停在"作动器"根节点上
echo 5. 检查是否显示统计信息而不是"数据未找到"
echo 6. 鼠标悬停在"传感器"根节点上
echo 7. 检查是否显示统计信息而不是"数据未找到"
echo.

echo 🔄 编译测试:
echo.

if exist "SiteResConfig\SiteResConfig_Simple.pro" (
    echo 找到项目文件，开始编译...
    echo.
    
    cd SiteResConfig
    
    echo 清理旧文件...
    if exist "Makefile" del /Q "Makefile" 2>nul
    if exist "debug" rmdir /S /Q "debug" 2>nul
    if exist "release" rmdir /S /Q "release" 2>nul
    
    echo.
    echo 生成Makefile...
    qmake SiteResConfig_Simple.pro -spec win32-g++ "CONFIG+=debug"
    
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo 开始编译...
        mingw32-make debug
        
        if %ERRORLEVEL% EQU 0 (
            echo.
            echo ✅ 编译成功！
            echo.
            if exist "debug\SiteResConfig.exe" (
                echo 🚀 启动程序进行测试...
                echo.
                start "" "debug\SiteResConfig.exe"
            ) else (
                echo ❌ 可执行文件未找到
            )
        ) else (
            echo ❌ 编译失败
        )
    ) else (
        echo ❌ qmake失败
    )
    
    cd ..
) else (
    echo ❌ 项目文件未找到
    echo 请确保在正确的目录中运行此脚本
)

echo.
echo ========================================
echo 🔧 根节点DEBUG信息已修复！
echo ========================================
echo.
echo 现在根节点将显示有用的统计信息，
echo 而不是错误的"数据未找到"消息。
echo.
pause
