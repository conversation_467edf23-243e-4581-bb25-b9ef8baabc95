/********************************************************************************
** Form generated from reading UI file 'ControlChannelEditDialog.ui'
**
** Created by: Qt User Interface Compiler version 5.14.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_CONTROLCHANNELEDITDIALOG_H
#define UI_CONTROLCHANNELEDITDIALOG_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QCheckBox>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QDialog>
#include <QtWidgets/QFormLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QTabWidget>
#include <QtWidgets/QTextEdit>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_ControlChannelEditDialog
{
public:
    QVBoxLayout *mainLayout;
    QTabWidget *tabWidget;
    QWidget *basicInfoTab;
    QFormLayout *basicInfoLayout;
    QLabel *channelNameLabel;
    QLineEdit *channelNameEdit;
    QLabel *lcIdLabel;
    QSpinBox *lcIdSpinBox;
    QLabel *stationIdLabel;
    QSpinBox *stationIdSpinBox;
    QLabel *enableLabel;
    QCheckBox *enableCheckBox;
    QLabel *controlModeLabel;
    QComboBox *controlModeCombo;
    QWidget *associationTab;
    QFormLayout *associationLayout;
    QLabel *hardwareLabel;
    QHBoxLayout *hardwareLayout;
    QComboBox *hardwareGroupCombo;
    QComboBox *hardwareChannelCombo;
    QLabel *load1SensorLabel;
    QHBoxLayout *load1SensorLayout;
    QComboBox *load1SensorGroupCombo;
    QComboBox *load1SensorDeviceCombo;
    QLabel *load2SensorLabel;
    QHBoxLayout *load2SensorLayout;
    QComboBox *load2SensorGroupCombo;
    QComboBox *load2SensorDeviceCombo;
    QLabel *positionSensorLabel;
    QHBoxLayout *positionSensorLayout;
    QComboBox *positionSensorGroupCombo;
    QComboBox *positionSensorDeviceCombo;
    QLabel *controlActuatorLabel;
    QHBoxLayout *controlActuatorLayout;
    QComboBox *controlActuatorGroupCombo;
    QComboBox *controlActuatorDeviceCombo;
    QWidget *polarityTab;
    QFormLayout *polarityLayout;
    QLabel *servoControlPolarityLabel;
    QComboBox *servoControlPolarityCombo;
    QLabel *payload1PolarityLabel;
    QComboBox *payload1PolarityCombo;
    QLabel *payload2PolarityLabel;
    QComboBox *payload2PolarityCombo;
    QLabel *positionPolarityLabel;
    QComboBox *positionPolarityCombo;
    QWidget *notesTab;
    QVBoxLayout *notesLayout;
    QLabel *notesLabel;
    QTextEdit *notesEdit;
    QHBoxLayout *buttonLayout;
    QSpacerItem *buttonSpacer;
    QPushButton *okButton;
    QPushButton *cancelButton;

    void setupUi(QDialog *ControlChannelEditDialog)
    {
        if (ControlChannelEditDialog->objectName().isEmpty())
            ControlChannelEditDialog->setObjectName(QString::fromUtf8("ControlChannelEditDialog"));
        ControlChannelEditDialog->resize(600, 500);
        ControlChannelEditDialog->setModal(true);
        mainLayout = new QVBoxLayout(ControlChannelEditDialog);
        mainLayout->setObjectName(QString::fromUtf8("mainLayout"));
        tabWidget = new QTabWidget(ControlChannelEditDialog);
        tabWidget->setObjectName(QString::fromUtf8("tabWidget"));
        basicInfoTab = new QWidget();
        basicInfoTab->setObjectName(QString::fromUtf8("basicInfoTab"));
        basicInfoLayout = new QFormLayout(basicInfoTab);
        basicInfoLayout->setObjectName(QString::fromUtf8("basicInfoLayout"));
        channelNameLabel = new QLabel(basicInfoTab);
        channelNameLabel->setObjectName(QString::fromUtf8("channelNameLabel"));

        basicInfoLayout->setWidget(0, QFormLayout::LabelRole, channelNameLabel);

        channelNameEdit = new QLineEdit(basicInfoTab);
        channelNameEdit->setObjectName(QString::fromUtf8("channelNameEdit"));

        basicInfoLayout->setWidget(0, QFormLayout::FieldRole, channelNameEdit);

        lcIdLabel = new QLabel(basicInfoTab);
        lcIdLabel->setObjectName(QString::fromUtf8("lcIdLabel"));

        basicInfoLayout->setWidget(1, QFormLayout::LabelRole, lcIdLabel);

        lcIdSpinBox = new QSpinBox(basicInfoTab);
        lcIdSpinBox->setObjectName(QString::fromUtf8("lcIdSpinBox"));
        lcIdSpinBox->setMinimum(1);
        lcIdSpinBox->setMaximum(999);
        lcIdSpinBox->setValue(1);

        basicInfoLayout->setWidget(1, QFormLayout::FieldRole, lcIdSpinBox);

        stationIdLabel = new QLabel(basicInfoTab);
        stationIdLabel->setObjectName(QString::fromUtf8("stationIdLabel"));

        basicInfoLayout->setWidget(2, QFormLayout::LabelRole, stationIdLabel);

        stationIdSpinBox = new QSpinBox(basicInfoTab);
        stationIdSpinBox->setObjectName(QString::fromUtf8("stationIdSpinBox"));
        stationIdSpinBox->setMinimum(1);
        stationIdSpinBox->setMaximum(999);
        stationIdSpinBox->setValue(1);

        basicInfoLayout->setWidget(2, QFormLayout::FieldRole, stationIdSpinBox);

        enableLabel = new QLabel(basicInfoTab);
        enableLabel->setObjectName(QString::fromUtf8("enableLabel"));

        basicInfoLayout->setWidget(3, QFormLayout::LabelRole, enableLabel);

        enableCheckBox = new QCheckBox(basicInfoTab);
        enableCheckBox->setObjectName(QString::fromUtf8("enableCheckBox"));
        enableCheckBox->setChecked(true);

        basicInfoLayout->setWidget(3, QFormLayout::FieldRole, enableCheckBox);

        controlModeLabel = new QLabel(basicInfoTab);
        controlModeLabel->setObjectName(QString::fromUtf8("controlModeLabel"));

        basicInfoLayout->setWidget(4, QFormLayout::LabelRole, controlModeLabel);

        controlModeCombo = new QComboBox(basicInfoTab);
        controlModeCombo->addItem(QString());
        controlModeCombo->addItem(QString());
        controlModeCombo->addItem(QString());
        controlModeCombo->setObjectName(QString::fromUtf8("controlModeCombo"));

        basicInfoLayout->setWidget(4, QFormLayout::FieldRole, controlModeCombo);

        tabWidget->addTab(basicInfoTab, QString());
        associationTab = new QWidget();
        associationTab->setObjectName(QString::fromUtf8("associationTab"));
        associationLayout = new QFormLayout(associationTab);
        associationLayout->setObjectName(QString::fromUtf8("associationLayout"));
        hardwareLabel = new QLabel(associationTab);
        hardwareLabel->setObjectName(QString::fromUtf8("hardwareLabel"));

        associationLayout->setWidget(0, QFormLayout::LabelRole, hardwareLabel);

        hardwareLayout = new QHBoxLayout();
        hardwareLayout->setObjectName(QString::fromUtf8("hardwareLayout"));
        hardwareGroupCombo = new QComboBox(associationTab);
        hardwareGroupCombo->setObjectName(QString::fromUtf8("hardwareGroupCombo"));

        hardwareLayout->addWidget(hardwareGroupCombo);

        hardwareChannelCombo = new QComboBox(associationTab);
        hardwareChannelCombo->setObjectName(QString::fromUtf8("hardwareChannelCombo"));

        hardwareLayout->addWidget(hardwareChannelCombo);


        associationLayout->setLayout(0, QFormLayout::FieldRole, hardwareLayout);

        load1SensorLabel = new QLabel(associationTab);
        load1SensorLabel->setObjectName(QString::fromUtf8("load1SensorLabel"));

        associationLayout->setWidget(1, QFormLayout::LabelRole, load1SensorLabel);

        load1SensorLayout = new QHBoxLayout();
        load1SensorLayout->setObjectName(QString::fromUtf8("load1SensorLayout"));
        load1SensorGroupCombo = new QComboBox(associationTab);
        load1SensorGroupCombo->setObjectName(QString::fromUtf8("load1SensorGroupCombo"));

        load1SensorLayout->addWidget(load1SensorGroupCombo);

        load1SensorDeviceCombo = new QComboBox(associationTab);
        load1SensorDeviceCombo->setObjectName(QString::fromUtf8("load1SensorDeviceCombo"));

        load1SensorLayout->addWidget(load1SensorDeviceCombo);


        associationLayout->setLayout(1, QFormLayout::FieldRole, load1SensorLayout);

        load2SensorLabel = new QLabel(associationTab);
        load2SensorLabel->setObjectName(QString::fromUtf8("load2SensorLabel"));

        associationLayout->setWidget(2, QFormLayout::LabelRole, load2SensorLabel);

        load2SensorLayout = new QHBoxLayout();
        load2SensorLayout->setObjectName(QString::fromUtf8("load2SensorLayout"));
        load2SensorGroupCombo = new QComboBox(associationTab);
        load2SensorGroupCombo->setObjectName(QString::fromUtf8("load2SensorGroupCombo"));

        load2SensorLayout->addWidget(load2SensorGroupCombo);

        load2SensorDeviceCombo = new QComboBox(associationTab);
        load2SensorDeviceCombo->setObjectName(QString::fromUtf8("load2SensorDeviceCombo"));

        load2SensorLayout->addWidget(load2SensorDeviceCombo);


        associationLayout->setLayout(2, QFormLayout::FieldRole, load2SensorLayout);

        positionSensorLabel = new QLabel(associationTab);
        positionSensorLabel->setObjectName(QString::fromUtf8("positionSensorLabel"));

        associationLayout->setWidget(3, QFormLayout::LabelRole, positionSensorLabel);

        positionSensorLayout = new QHBoxLayout();
        positionSensorLayout->setObjectName(QString::fromUtf8("positionSensorLayout"));
        positionSensorGroupCombo = new QComboBox(associationTab);
        positionSensorGroupCombo->setObjectName(QString::fromUtf8("positionSensorGroupCombo"));

        positionSensorLayout->addWidget(positionSensorGroupCombo);

        positionSensorDeviceCombo = new QComboBox(associationTab);
        positionSensorDeviceCombo->setObjectName(QString::fromUtf8("positionSensorDeviceCombo"));

        positionSensorLayout->addWidget(positionSensorDeviceCombo);


        associationLayout->setLayout(3, QFormLayout::FieldRole, positionSensorLayout);

        controlActuatorLabel = new QLabel(associationTab);
        controlActuatorLabel->setObjectName(QString::fromUtf8("controlActuatorLabel"));

        associationLayout->setWidget(4, QFormLayout::LabelRole, controlActuatorLabel);

        controlActuatorLayout = new QHBoxLayout();
        controlActuatorLayout->setObjectName(QString::fromUtf8("controlActuatorLayout"));
        controlActuatorGroupCombo = new QComboBox(associationTab);
        controlActuatorGroupCombo->setObjectName(QString::fromUtf8("controlActuatorGroupCombo"));

        controlActuatorLayout->addWidget(controlActuatorGroupCombo);

        controlActuatorDeviceCombo = new QComboBox(associationTab);
        controlActuatorDeviceCombo->setObjectName(QString::fromUtf8("controlActuatorDeviceCombo"));

        controlActuatorLayout->addWidget(controlActuatorDeviceCombo);


        associationLayout->setLayout(4, QFormLayout::FieldRole, controlActuatorLayout);

        tabWidget->addTab(associationTab, QString());
        polarityTab = new QWidget();
        polarityTab->setObjectName(QString::fromUtf8("polarityTab"));
        polarityLayout = new QFormLayout(polarityTab);
        polarityLayout->setObjectName(QString::fromUtf8("polarityLayout"));
        servoControlPolarityLabel = new QLabel(polarityTab);
        servoControlPolarityLabel->setObjectName(QString::fromUtf8("servoControlPolarityLabel"));

        polarityLayout->setWidget(0, QFormLayout::LabelRole, servoControlPolarityLabel);

        servoControlPolarityCombo = new QComboBox(polarityTab);
        servoControlPolarityCombo->addItem(QString());
        servoControlPolarityCombo->addItem(QString());
        servoControlPolarityCombo->addItem(QString());
        servoControlPolarityCombo->addItem(QString());
        servoControlPolarityCombo->setObjectName(QString::fromUtf8("servoControlPolarityCombo"));

        polarityLayout->setWidget(0, QFormLayout::FieldRole, servoControlPolarityCombo);

        payload1PolarityLabel = new QLabel(polarityTab);
        payload1PolarityLabel->setObjectName(QString::fromUtf8("payload1PolarityLabel"));

        polarityLayout->setWidget(1, QFormLayout::LabelRole, payload1PolarityLabel);

        payload1PolarityCombo = new QComboBox(polarityTab);
        payload1PolarityCombo->addItem(QString());
        payload1PolarityCombo->addItem(QString());
        payload1PolarityCombo->addItem(QString());
        payload1PolarityCombo->addItem(QString());
        payload1PolarityCombo->setObjectName(QString::fromUtf8("payload1PolarityCombo"));

        polarityLayout->setWidget(1, QFormLayout::FieldRole, payload1PolarityCombo);

        payload2PolarityLabel = new QLabel(polarityTab);
        payload2PolarityLabel->setObjectName(QString::fromUtf8("payload2PolarityLabel"));

        polarityLayout->setWidget(2, QFormLayout::LabelRole, payload2PolarityLabel);

        payload2PolarityCombo = new QComboBox(polarityTab);
        payload2PolarityCombo->addItem(QString());
        payload2PolarityCombo->addItem(QString());
        payload2PolarityCombo->addItem(QString());
        payload2PolarityCombo->addItem(QString());
        payload2PolarityCombo->setObjectName(QString::fromUtf8("payload2PolarityCombo"));

        polarityLayout->setWidget(2, QFormLayout::FieldRole, payload2PolarityCombo);

        positionPolarityLabel = new QLabel(polarityTab);
        positionPolarityLabel->setObjectName(QString::fromUtf8("positionPolarityLabel"));

        polarityLayout->setWidget(3, QFormLayout::LabelRole, positionPolarityLabel);

        positionPolarityCombo = new QComboBox(polarityTab);
        positionPolarityCombo->addItem(QString());
        positionPolarityCombo->addItem(QString());
        positionPolarityCombo->addItem(QString());
        positionPolarityCombo->addItem(QString());
        positionPolarityCombo->setObjectName(QString::fromUtf8("positionPolarityCombo"));

        polarityLayout->setWidget(3, QFormLayout::FieldRole, positionPolarityCombo);

        tabWidget->addTab(polarityTab, QString());
        notesTab = new QWidget();
        notesTab->setObjectName(QString::fromUtf8("notesTab"));
        notesLayout = new QVBoxLayout(notesTab);
        notesLayout->setObjectName(QString::fromUtf8("notesLayout"));
        notesLabel = new QLabel(notesTab);
        notesLabel->setObjectName(QString::fromUtf8("notesLabel"));

        notesLayout->addWidget(notesLabel);

        notesEdit = new QTextEdit(notesTab);
        notesEdit->setObjectName(QString::fromUtf8("notesEdit"));

        notesLayout->addWidget(notesEdit);

        tabWidget->addTab(notesTab, QString());

        mainLayout->addWidget(tabWidget);

        buttonLayout = new QHBoxLayout();
        buttonLayout->setObjectName(QString::fromUtf8("buttonLayout"));
        buttonSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        buttonLayout->addItem(buttonSpacer);

        okButton = new QPushButton(ControlChannelEditDialog);
        okButton->setObjectName(QString::fromUtf8("okButton"));

        buttonLayout->addWidget(okButton);

        cancelButton = new QPushButton(ControlChannelEditDialog);
        cancelButton->setObjectName(QString::fromUtf8("cancelButton"));

        buttonLayout->addWidget(cancelButton);


        mainLayout->addLayout(buttonLayout);


        retranslateUi(ControlChannelEditDialog);
        QObject::connect(cancelButton, SIGNAL(clicked()), ControlChannelEditDialog, SLOT(reject()));

        tabWidget->setCurrentIndex(0);
        okButton->setDefault(true);


        QMetaObject::connectSlotsByName(ControlChannelEditDialog);
    } // setupUi

    void retranslateUi(QDialog *ControlChannelEditDialog)
    {
        ControlChannelEditDialog->setWindowTitle(QCoreApplication::translate("ControlChannelEditDialog", "\347\274\226\350\276\221\346\216\247\345\210\266\351\200\232\351\201\223\351\205\215\347\275\256", nullptr));
        channelNameLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "\351\200\232\351\201\223\345\220\215\347\247\260:", nullptr));
        channelNameEdit->setPlaceholderText(QCoreApplication::translate("ControlChannelEditDialog", "\350\257\267\350\276\223\345\205\245\351\200\232\351\201\223\345\220\215\347\247\260", nullptr));
        lcIdLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "\344\270\213\344\275\215\346\234\272ID:", nullptr));
        stationIdLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "\347\253\231\347\202\271ID:", nullptr));
        enableLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "\344\275\277\350\203\275\347\212\266\346\200\201:", nullptr));
        enableCheckBox->setText(QCoreApplication::translate("ControlChannelEditDialog", "\345\220\257\347\224\250\351\200\232\351\201\223", nullptr));
        controlModeLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "\346\216\247\345\210\266\346\250\241\345\274\217:", nullptr));
        controlModeCombo->setItemText(0, QCoreApplication::translate("ControlChannelEditDialog", "\344\275\215\347\275\256\346\216\247\345\210\266", nullptr));
        controlModeCombo->setItemText(1, QCoreApplication::translate("ControlChannelEditDialog", "\345\212\233\346\216\247\345\210\266", nullptr));
        controlModeCombo->setItemText(2, QCoreApplication::translate("ControlChannelEditDialog", "\346\267\267\345\220\210\346\216\247\345\210\266", nullptr));

        tabWidget->setTabText(tabWidget->indexOf(basicInfoTab), QCoreApplication::translate("ControlChannelEditDialog", "\345\237\272\346\234\254\344\277\241\346\201\257", nullptr));
        hardwareLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "\347\241\254\344\273\266\345\205\263\350\201\224:", nullptr));
#if QT_CONFIG(tooltip)
        hardwareGroupCombo->setToolTip(QCoreApplication::translate("ControlChannelEditDialog", "\351\200\211\346\213\251\347\241\254\344\273\266\350\212\202\347\202\271\347\273\204", nullptr));
#endif // QT_CONFIG(tooltip)
#if QT_CONFIG(tooltip)
        hardwareChannelCombo->setToolTip(QCoreApplication::translate("ControlChannelEditDialog", "\351\200\211\346\213\251\347\241\254\344\273\266\351\200\232\351\201\223", nullptr));
#endif // QT_CONFIG(tooltip)
        load1SensorLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "\350\275\275\350\215\2671\344\274\240\346\204\237\345\231\250:", nullptr));
#if QT_CONFIG(tooltip)
        load1SensorGroupCombo->setToolTip(QCoreApplication::translate("ControlChannelEditDialog", "\351\200\211\346\213\251\344\274\240\346\204\237\345\231\250\347\273\204", nullptr));
#endif // QT_CONFIG(tooltip)
#if QT_CONFIG(tooltip)
        load1SensorDeviceCombo->setToolTip(QCoreApplication::translate("ControlChannelEditDialog", "\351\200\211\346\213\251\344\274\240\346\204\237\345\231\250\350\256\276\345\244\207", nullptr));
#endif // QT_CONFIG(tooltip)
        load2SensorLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "\350\275\275\350\215\2672\344\274\240\346\204\237\345\231\250:", nullptr));
#if QT_CONFIG(tooltip)
        load2SensorGroupCombo->setToolTip(QCoreApplication::translate("ControlChannelEditDialog", "\351\200\211\346\213\251\344\274\240\346\204\237\345\231\250\347\273\204", nullptr));
#endif // QT_CONFIG(tooltip)
#if QT_CONFIG(tooltip)
        load2SensorDeviceCombo->setToolTip(QCoreApplication::translate("ControlChannelEditDialog", "\351\200\211\346\213\251\344\274\240\346\204\237\345\231\250\350\256\276\345\244\207", nullptr));
#endif // QT_CONFIG(tooltip)
        positionSensorLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "\344\275\215\347\275\256\344\274\240\346\204\237\345\231\250:", nullptr));
#if QT_CONFIG(tooltip)
        positionSensorGroupCombo->setToolTip(QCoreApplication::translate("ControlChannelEditDialog", "\351\200\211\346\213\251\344\274\240\346\204\237\345\231\250\347\273\204", nullptr));
#endif // QT_CONFIG(tooltip)
#if QT_CONFIG(tooltip)
        positionSensorDeviceCombo->setToolTip(QCoreApplication::translate("ControlChannelEditDialog", "\351\200\211\346\213\251\344\274\240\346\204\237\345\231\250\350\256\276\345\244\207", nullptr));
#endif // QT_CONFIG(tooltip)
        controlActuatorLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "\346\216\247\345\210\266\344\275\234\345\212\250\345\231\250:", nullptr));
#if QT_CONFIG(tooltip)
        controlActuatorGroupCombo->setToolTip(QCoreApplication::translate("ControlChannelEditDialog", "\351\200\211\346\213\251\344\275\234\345\212\250\345\231\250\347\273\204", nullptr));
#endif // QT_CONFIG(tooltip)
#if QT_CONFIG(tooltip)
        controlActuatorDeviceCombo->setToolTip(QCoreApplication::translate("ControlChannelEditDialog", "\351\200\211\346\213\251\344\275\234\345\212\250\345\231\250\350\256\276\345\244\207", nullptr));
#endif // QT_CONFIG(tooltip)
        tabWidget->setTabText(tabWidget->indexOf(associationTab), QCoreApplication::translate("ControlChannelEditDialog", "\345\205\263\350\201\224\351\205\215\347\275\256", nullptr));
        servoControlPolarityLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "\346\216\247\345\210\266\344\275\234\345\212\250\345\231\250\346\236\201\346\200\247:", nullptr));
        servoControlPolarityCombo->setItemText(0, QCoreApplication::translate("ControlChannelEditDialog", "\346\255\243\346\236\201\346\200\247 (+)", nullptr));
        servoControlPolarityCombo->setItemText(1, QCoreApplication::translate("ControlChannelEditDialog", "\350\264\237\346\236\201\346\200\247 (-)", nullptr));
        servoControlPolarityCombo->setItemText(2, QCoreApplication::translate("ControlChannelEditDialog", "\345\217\214\346\236\201\346\200\247 (\302\261)", nullptr));
        servoControlPolarityCombo->setItemText(3, QCoreApplication::translate("ControlChannelEditDialog", "\346\227\240\346\236\201\346\200\247", nullptr));

        payload1PolarityLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "\350\275\275\350\215\2671\344\274\240\346\204\237\345\231\250\346\236\201\346\200\247:", nullptr));
        payload1PolarityCombo->setItemText(0, QCoreApplication::translate("ControlChannelEditDialog", "\346\255\243\346\236\201\346\200\247 (+)", nullptr));
        payload1PolarityCombo->setItemText(1, QCoreApplication::translate("ControlChannelEditDialog", "\350\264\237\346\236\201\346\200\247 (-)", nullptr));
        payload1PolarityCombo->setItemText(2, QCoreApplication::translate("ControlChannelEditDialog", "\345\217\214\346\236\201\346\200\247 (\302\261)", nullptr));
        payload1PolarityCombo->setItemText(3, QCoreApplication::translate("ControlChannelEditDialog", "\346\227\240\346\236\201\346\200\247", nullptr));

        payload2PolarityLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "\350\275\275\350\215\2672\344\274\240\346\204\237\345\231\250\346\236\201\346\200\247:", nullptr));
        payload2PolarityCombo->setItemText(0, QCoreApplication::translate("ControlChannelEditDialog", "\346\255\243\346\236\201\346\200\247 (+)", nullptr));
        payload2PolarityCombo->setItemText(1, QCoreApplication::translate("ControlChannelEditDialog", "\350\264\237\346\236\201\346\200\247 (-)", nullptr));
        payload2PolarityCombo->setItemText(2, QCoreApplication::translate("ControlChannelEditDialog", "\345\217\214\346\236\201\346\200\247 (\302\261)", nullptr));
        payload2PolarityCombo->setItemText(3, QCoreApplication::translate("ControlChannelEditDialog", "\346\227\240\346\236\201\346\200\247", nullptr));

        positionPolarityLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "\344\275\215\347\275\256\344\274\240\346\204\237\345\231\250\346\236\201\346\200\247:", nullptr));
        positionPolarityCombo->setItemText(0, QCoreApplication::translate("ControlChannelEditDialog", "\346\255\243\346\236\201\346\200\247 (+)", nullptr));
        positionPolarityCombo->setItemText(1, QCoreApplication::translate("ControlChannelEditDialog", "\350\264\237\346\236\201\346\200\247 (-)", nullptr));
        positionPolarityCombo->setItemText(2, QCoreApplication::translate("ControlChannelEditDialog", "\345\217\214\346\236\201\346\200\247 (\302\261)", nullptr));
        positionPolarityCombo->setItemText(3, QCoreApplication::translate("ControlChannelEditDialog", "\346\227\240\346\236\201\346\200\247", nullptr));

        tabWidget->setTabText(tabWidget->indexOf(polarityTab), QCoreApplication::translate("ControlChannelEditDialog", "\346\236\201\346\200\247\351\205\215\347\275\256", nullptr));
        notesLabel->setText(QCoreApplication::translate("ControlChannelEditDialog", "\345\244\207\346\263\250\344\277\241\346\201\257:", nullptr));
        notesEdit->setPlaceholderText(QCoreApplication::translate("ControlChannelEditDialog", "\350\257\267\350\276\223\345\205\245\345\244\207\346\263\250\344\277\241\346\201\257...", nullptr));
        tabWidget->setTabText(tabWidget->indexOf(notesTab), QCoreApplication::translate("ControlChannelEditDialog", "\345\244\207\346\263\250\344\277\241\346\201\257", nullptr));
        okButton->setText(QCoreApplication::translate("ControlChannelEditDialog", "\347\241\256\345\256\232", nullptr));
        cancelButton->setText(QCoreApplication::translate("ControlChannelEditDialog", "\345\217\226\346\266\210", nullptr));
    } // retranslateUi

};

namespace Ui {
    class ControlChannelEditDialog: public Ui_ControlChannelEditDialog {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_CONTROLCHANNELEDITDIALOG_H
