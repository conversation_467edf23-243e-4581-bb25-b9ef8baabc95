/**
 * @file Utils_Fixed.cpp
 * @brief Utility functions implementation (Fixed Encoding)
 * @details Implementation of utility functions declared in Common_Fixed.h
 * <AUTHOR> Assistant
 * @date 2025-08-05
 * @version 1.0.0
 */

#include "Common_Fixed.h"
#include <sstream>
#include <iomanip>
#include <ctime>

#ifdef _WIN32
#include <windows.h>
#endif

namespace Utils {

/**
 * @brief Format timestamp
 * @param timestamp Timestamp (milliseconds)
 * @return Formatted time string (YYYY-MM-DD HH:MM:SS.mmm)
 */
StringType FormatTimestamp(int64_t timestamp) {
    // Convert to seconds and milliseconds
    time_t seconds = static_cast<time_t>(timestamp / 1000);
    int milliseconds = static_cast<int>(timestamp % 1000);
    
    // Convert to local time
    struct tm timeInfo;
#ifdef _WIN32
    localtime_s(&timeInfo, &seconds);
#else
    localtime_r(&seconds, &timeInfo);
#endif
    
    // Format time string
    std::ostringstream oss;
    oss << std::put_time(&timeInfo, "%Y-%m-%d %H:%M:%S");
    oss << "." << std::setfill('0') << std::setw(3) << milliseconds;
    
    return oss.str();
}

#ifdef _WIN32
/**
 * @brief Convert UTF-8 string to wide string (Windows implementation)
 * @param utf8Str UTF-8 string
 * @return Wide string
 */
WStringType Utf8ToWide(const StringType& utf8Str) {
    if (utf8Str.empty()) {
        return WStringType();
    }
    
    // Calculate required wide character buffer size
    int wideSize = MultiByteToWideChar(CP_UTF8, 0, utf8Str.c_str(), -1, nullptr, 0);
    if (wideSize <= 0) {
        return WStringType();
    }
    
    // Allocate buffer and convert
    std::vector<wchar_t> wideBuffer(wideSize);
    MultiByteToWideChar(CP_UTF8, 0, utf8Str.c_str(), -1, wideBuffer.data(), wideSize);
    
    return WStringType(wideBuffer.data());
}

/**
 * @brief Convert wide string to UTF-8 string (Windows implementation)
 * @param wideStr Wide string
 * @return UTF-8 string
 */
StringType WideToUtf8(const WStringType& wideStr) {
    if (wideStr.empty()) {
        return StringType();
    }
    
    // Calculate required UTF-8 buffer size
    int utf8Size = WideCharToMultiByte(CP_UTF8, 0, wideStr.c_str(), -1, nullptr, 0, nullptr, nullptr);
    if (utf8Size <= 0) {
        return StringType();
    }
    
    // Allocate buffer and convert
    std::vector<char> utf8Buffer(utf8Size);
    WideCharToMultiByte(CP_UTF8, 0, wideStr.c_str(), -1, utf8Buffer.data(), utf8Size, nullptr, nullptr);
    
    return StringType(utf8Buffer.data());
}

#else
/**
 * @brief Convert UTF-8 string to wide string (Non-Windows implementation)
 * @param utf8Str UTF-8 string
 * @return Wide string
 */
WStringType Utf8ToWide(const StringType& utf8Str) {
    // Simplified implementation, should use more complete conversion method in actual project
    std::wstring_convert<std::codecvt_utf8<wchar_t>> converter;
    return converter.from_bytes(utf8Str);
}

/**
 * @brief Convert wide string to UTF-8 string (Non-Windows implementation)
 * @param wideStr Wide string
 * @return UTF-8 string
 */
StringType WideToUtf8(const WStringType& wideStr) {
    // Simplified implementation, should use more complete conversion method in actual project
    std::wstring_convert<std::codecvt_utf8<wchar_t>> converter;
    return converter.to_bytes(wideStr);
}
#endif

} // namespace Utils
