@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🔧 Debug模式树形控件提示信息测试
echo ========================================
echo.

cd /d "%~dp0"

echo 📁 当前目录: %CD%
echo.

echo 🔍 检查源文件...
if exist "SiteResConfig\src\MainWindow_Qt_Simple.cpp" (
    echo ✅ 主窗口源文件存在
) else (
    echo ❌ 主窗口源文件不存在
    goto :end
)

if exist "SiteResConfig\include\MainWindow_Qt_Simple.h" (
    echo ✅ 主窗口头文件存在
) else (
    echo ❌ 主窗口头文件不存在
    goto :end
)

echo.
echo 🔧 Debug模式功能说明:
echo.
echo 📋 实现的功能:
echo ├─ 自动检测Debug/Release模式
echo ├─ Debug模式显示详细ID信息
echo ├─ Release模式显示用户友好信息
echo └─ 支持所有树形控件节点类型
echo.

echo 🎯 支持的节点类型:
echo ├─ 作动器组 (显示组ID、作动器ID列表)
echo ├─ 作动器设备 (显示设备ID、序列号、所属组)
echo ├─ 传感器组 (显示组ID、传感器ID列表)
echo ├─ 传感器设备 (显示设备ID、序列号、所属组)
echo ├─ 硬件节点 (显示节点ID、IP、端口、通道列表)
echo └─ 硬件通道 (显示通道ID、类型、状态)
echo.

echo 🔄 触发时机:
echo ├─ 拖拽操作完成后
echo ├─ 添加作动器组后
echo ├─ 添加传感器组后
echo ├─ 添加硬件节点后
echo └─ 打开工程文件后
echo.

echo 💡 使用方法:
echo.
echo 1. Debug版本编译:
echo    - 自动定义_DEBUG宏
echo    - 树形控件tooltip显示详细ID信息
echo.
echo 2. Release版本编译:
echo    - 不定义_DEBUG宏
echo    - 树形控件tooltip显示简洁用户信息
echo.

echo 🧪 测试步骤:
echo.
echo 1. 编译Debug版本
echo 2. 启动程序
echo 3. 创建作动器组和设备
echo 4. 鼠标悬停在树形控件节点上
echo 5. 观察tooltip是否显示详细的ID信息
echo.
echo 预期Debug模式tooltip格式:
echo ┌─────────────────────────────────────┐
echo │ ═══ 作动器组1 详细信息 ═══           │
echo │ 节点名称: 作动器组1                 │
echo │ 节点类型: 作动器组                  │
echo │ ...                                 │
echo │                                     │
echo │ 🔧 DEBUG信息 🔧                     │
echo │ ═══════════════════                 │
echo │ 节点类型: 作动器组                  │
echo │ 组ID: 1                            │
echo │ 组内作动器数量: 2个                 │
echo │ 作动器ID列表: [1, 2]                │
echo │ 树形位置: 第2层                     │
echo │ 子节点数: 2个                       │
echo │ 父节点: 作动器                      │
echo └─────────────────────────────────────┘
echo.

echo 📖 详细说明文档: DEBUG_TOOLTIP_功能说明.md
echo.

:end
echo 按任意键退出...
pause >nul
