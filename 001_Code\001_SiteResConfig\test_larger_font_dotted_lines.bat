@echo off
chcp 65001 > nul
echo ========================================
echo 大字体+虚线穿过加号图标测试
echo ========================================
echo.

echo 🔧 正在编译大字体和改进虚线的代码...
cd SiteResConfig
qmake SiteResConfig_Simple.pro
mingw32-make clean
mingw32-make

if %ERRORLEVEL% NEQ 0 (
    echo ❌ 编译失败！
    pause
    exit /b 1
)

echo ✅ 编译成功！
echo.

echo 🚀 启动应用程序进行大字体和虚线测试...
echo.
echo 📋 **大字体+虚线穿过加号图标改进**：
echo.
echo 🎯 **改进内容**：
echo 1. 整体软件字体从9pt增加到11pt
echo 2. 分组框标题字体增加到12pt
echo 3. 树形控件行高从18px增加到22px
echo 4. 加号/减号图标从11x11增加到13x13像素
echo 5. 图标使用透明背景，让虚线可以穿过
echo.
echo 🎨 **字体改进特征**：
echo.
echo 1️⃣ **全局字体加大**
echo    - 基础字体：从9pt → 11pt
echo    - 分组框标题：12pt（更突出）
echo    - 按钮字体：11pt
echo    - 标签和输入框：11pt
echo    - 树形控件：11pt
echo.
echo 2️⃣ **控件尺寸调整**
echo    - 树形控件行高：22px（适应大字体）
echo    - 按钮内边距：增加到10px 18px
echo    - 按钮最小尺寸：90x28px
echo    - 输入框内边距：8px 12px
echo.
echo 3️⃣ **加号图标改进**
echo    - 图标尺寸：13x13像素
echo    - 透明背景：让虚线可以穿过
echo    - 按钮区域：中心9x9像素白色方块
echo    - 符号位置：居中显示
echo.
echo 4️⃣ **虚线连接优化**
echo    - 分支区域宽度：16px
echo    - 分支区域高度：22px（匹配行高）
echo    - 虚线样式：dotted #808080
echo    - 虚线穿过图标中心
echo.
echo 5️⃣ **视觉层次改进**
echo    - 更清晰的文字显示
echo    - 更好的可读性
echo    - 更大的点击区域
echo    - 更专业的外观
echo.

start "" "debug\SiteResConfig.exe"

echo 📊 应用程序已启动！
echo.
echo 🔍 **详细验证清单**：
echo.
echo ☐ 1. **字体大小验证**
echo      - 整体界面文字比之前更大更清晰
echo      - 树形控件文字易于阅读
echo      - 按钮文字大小适中
echo      - 分组框标题突出显示
echo.
echo ☐ 2. **树形控件改进验证**
echo      - 行高增加，文字不拥挤
echo      - 加号/减号图标更大更清晰
echo      - 图标与文字对齐良好
echo      - 整体视觉效果更舒适
echo.
echo ☐ 3. **虚线连接验证**
echo      - 虚线从父节点延伸到子节点
echo      - 虚线穿过加号/减号图标的中心
echo      - 连接线连续不断开
echo      - 层次结构清晰可见
echo.
echo ☐ 4. **加号图标验证**
echo      - 图标尺寸为13x13像素
echo      - 中心有9x9像素的白色按钮
echo      - 加号/减号符号居中显示
echo      - 图标边缘透明，不遮挡虚线
echo.
echo ☐ 5. **整体协调性验证**
echo      - 所有控件字体大小协调
echo      - 按钮和输入框尺寸适中
echo      - 界面布局不会因字体变大而混乱
echo      - 整体视觉效果专业美观
echo.
echo ☐ 6. **可用性验证**
echo      - 文字更易于阅读
echo      - 按钮更容易点击
echo      - 树形控件操作更精确
echo      - 整体用户体验提升
echo.
echo 💡 **改进效果对比**：
echo.
echo 📏 **字体大小对比**：
echo - 之前：9pt → 现在：11pt（增加22%）
echo - 分组框：9pt → 现在：12pt（增加33%）
echo.
echo 📐 **尺寸对比**：
echo - 树形控件行高：18px → 22px（增加22%）
echo - 加号图标：11x11 → 13x13（增加18%）
echo - 按钮最小尺寸：80x24 → 90x28（增加12.5%）
echo.
echo 🎉 **成功标志**：
echo - ✅ 整体界面文字更大更清晰
echo - ✅ 树形控件行高适中，不拥挤
echo - ✅ 加号/减号图标更大更清晰
echo - ✅ 虚线正确穿过图标中心
echo - ✅ 所有控件尺寸协调统一
echo - ✅ 用户体验显著提升
echo.
echo 🎉 **设计优势**：
echo - 更好的可读性和可用性
echo - 更专业的视觉外观
echo - 更符合现代界面标准
echo - 更适合长时间使用
echo.
pause

echo.
echo 📁 检查生成的图标文件...
if exist "debug\temp\plus.png" (
    echo ✅ 加号图标: debug\temp\plus.png
    for %%A in (debug\temp\plus.png) do echo    大小: %%~zA 字节
) else (
    echo ❌ 加号图标文件不存在
)

if exist "debug\temp\minus.png" (
    echo ✅ 减号图标: debug\temp\minus.png
    for %%A in (debug\temp\minus.png) do echo    大小: %%~zA 字节
) else (
    echo ❌ 减号图标文件不存在
)
echo.
pause
