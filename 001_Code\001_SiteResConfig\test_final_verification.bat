@echo off
echo ========================================
echo  最终验证：传感器和作动器详细配置
echo ========================================

REM 设置Qt环境变量
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo 请检查上面的错误信息。
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！功能验证完成
    echo ========================================
    
    echo.
    echo ✅ 传感器详细配置（34列）:
    echo 第1列: 组序号（从1开始递增）
    echo 第2列: 传感器组名称
    echo 第3列: 传感器序号
    echo 第4列: 传感器序列号
    echo 第5列: 传感器类型
    echo ...
    echo 第34列: 编码器分辨率
    echo.
    echo ❌ 已去掉: 传感器名称列
    echo.
    echo ✅ 作动器详细配置（17列）:
    echo 第1列: 组序号（从1开始递增）
    echo 第2列: 作动器组名称
    echo 第3列: 作动器序号
    echo 第4列: 作动器序列号
    echo ...
    echo 第17列: 备注
    echo.
    echo 🎯 结构关系:
    echo - 一个传感器组名称包括多个传感器信息
    echo - 一个作动器组名称包括多个作动器信息
    echo - 组序号从1开始递增：1, 2, 3, 4...
    echo - 只有包含设备的组才分配序号
    echo.
    echo 📊 Excel导出示例:
    echo.
    echo 传感器详细配置:
    echo 组序号 ^| 传感器组名称 ^| 传感器序号 ^| 传感器序列号 ^| ...
    echo ------|------------|----------|------------|----
    echo 1     ^| 载荷_传感器组 ^| 1        ^| SEN001     ^| ...
    echo 1     ^|            ^| 2        ^| SEN002     ^| ...
    echo 2     ^| 位置_传感器组 ^| 1        ^| SEN003     ^| ...
    echo.
    echo 作动器详细配置:
    echo 组序号 ^| 作动器组名称 ^| 作动器序号 ^| 作动器序列号 ^| ...
    echo ------|------------|----------|------------|----
    echo 1     ^| 主作动器组   ^| 1        ^| ACT001     ^| ...
    echo 1     ^|            ^| 2        ^| ACT002     ^| ...
    echo 2     ^| 辅助作动器组 ^| 1        ^| ACT003     ^| ...
    echo.
    
    if exist "SiteResConfig.exe" (
        echo 启动程序进行最终验证...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 启动程序进行最终验证...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 启动程序进行最终验证...
        start release\SiteResConfig.exe
    )
)

echo.
echo 📖 最终验证步骤:
echo.
echo 🎮 传感器详细配置验证:
echo 1. 新建项目
echo 2. 创建多个传感器组（载荷、位置、压力等）
echo 3. 在每个组中添加传感器
echo 4. 配置传感器详细参数
echo 5. 导出传感器详细信息到Excel
echo 6. 验证Excel文件：
echo    - 第1列：组序号（1, 2, 3...）
echo    - 第2列：传感器组名称
echo    - 第3列：传感器序号
echo    - 第4列：传感器序列号
echo    - 总共34列
echo    - 没有"传感器名称"列
echo.
echo 🎮 作动器详细配置验证:
echo 1. 创建多个作动器组
echo 2. 在每个组中添加作动器
echo 3. 导出作动器详细信息到Excel
echo 4. 验证Excel文件：
echo    - 第1列：组序号（1, 2, 3...）
echo    - 第2列：作动器组名称
echo    - 总共17列
echo.
echo 🎮 组序号连续性验证:
echo 1. 创建5个组，只在第1、3、5个组中添加设备
echo 2. 导出Excel文件
echo 3. 验证组序号为：1, 2, 3（连续，无跳跃）
echo.
echo ✅ 预期结果:
echo - 传感器详细配置：34列，组序号从1递增
echo - 作动器详细配置：17列，组序号从1递增
echo - 没有"传感器名称"列
echo - 组序号连续，无跳跃
echo - Excel格式专业，数据完整
echo.
pause
