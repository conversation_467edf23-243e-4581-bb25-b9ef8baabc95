@echo off
echo ========================================
echo  增强拖拽功能和新建工程流程测试
echo ========================================

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o
if exist "ui_*.h" del ui_*.h

echo.
echo 生成UI头文件...
uic ui\MainWindow.ui -o ui_MainWindow.h
if errorlevel 1 (
    echo UI文件生成失败！
    pause
    exit /b 1
) else (
    echo ✅ UI头文件生成成功: ui_MainWindow.h
)

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo.
    echo 请检查以下可能的问题：
    echo 1. CustomTreeWidgets.cpp中的拖拽视觉反馈实现
    echo 2. 颜色保存和恢复逻辑
    echo 3. 新建工程流程修改
    echo 4. 头文件包含问题
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！增强拖拽功能已实现
    echo ========================================
    
    if exist "SiteResConfig.exe" (
        echo 可执行文件: SiteResConfig.exe
        echo.
        echo ✅ 增强拖拽功能和新建工程流程已实现！
        echo.
        echo 🗂️ 新建工程流程优化:
        echo ├─ 先清空数据: 立即清空当前界面数据
        echo ├─ 再添加数据: 重新初始化为新工程状态
        echo ├─ 流程顺序: 清空 → 用户选择 → 创建工程 → 初始化
        echo └─ 状态管理: 确保界面状态正确切换
        echo.
        echo 🎨 拖拽视觉反馈功能:
        echo ├─ 拖拽源节点: 被拖拽时颜色变化
        echo │  ├─ 背景颜色: 浅蓝色 (173, 216, 230)
        echo │  ├─ 文字颜色: 深蓝色 (0, 0, 139)
        echo │  └─ 恢复时机: 拖拽完成后自动恢复
        echo ├─ 拖拽目标节点: 可接收时高亮显示
        echo │  ├─ 背景颜色: 浅绿色 (144, 238, 144)
        echo │  ├─ 文字颜色: 深绿色 (0, 100, 0)
        echo │  └─ 恢复时机: 拖拽离开或完成后恢复
        echo └─ 颜色管理: 自动保存和恢复原始颜色
        echo.
        echo 🔧 技术实现细节:
        echo.
        echo 📝 新建工程流程修改:
        echo ├─ 第一步: 立即清空界面数据
        echo │  ├─ 调用: ClearInterfaceData()
        echo │  ├─ 时机: 用户确认新建工程后立即执行
        echo │  └─ 目的: 确保界面状态干净
        echo ├─ 第二步: 用户选择和工程创建
        echo │  ├─ 选择保存路径
        echo │  ├─ 创建TestProject对象
        echo │  └─ 设置工程基本信息
        echo └─ 第三步: 重新初始化界面
        echo    ├─ 调用: SetDefaultEmptyInterface()
        echo    ├─ 目的: 为新工程设置默认状态
        echo    └─ 结果: 界面显示新工程的初始状态
        echo.
        echo 🎨 拖拽视觉反馈实现:
        echo ├─ CustomHardwareTreeWidget::startDrag():
        echo │  ├─ 保存原始颜色: m_originalBackgroundColor, m_originalTextColor
        echo │  ├─ 设置拖拽颜色: 浅蓝色背景 + 深蓝色文字
        echo │  ├─ 执行拖拽: QTreeWidget::startDrag()
        echo │  └─ 恢复颜色: 拖拽完成后自动恢复
        echo ├─ CustomTestConfigTreeWidget::dragMoveEvent():
        echo │  ├─ 恢复上次高亮: 清除之前的高亮项目
        echo │  ├─ 验证目标: 检查是否可以接收拖拽
        echo │  ├─ 设置高亮: 浅绿色背景 + 深绿色文字
        echo │  └─ 记录状态: 保存当前高亮项目
        echo ├─ CustomTestConfigTreeWidget::dragLeaveEvent():
        echo │  ├─ 恢复颜色: 拖拽离开时恢复原始颜色
        echo │  └─ 清除状态: 重置高亮项目指针
        echo └─ CustomTestConfigTreeWidget::dropEvent():
        echo    ├─ 恢复颜色: 拖拽完成时恢复原始颜色
        echo    ├─ 处理关联: 执行拖拽关联逻辑
        echo    └─ 清除状态: 重置高亮项目指针
        echo.
        echo 📋 测试步骤:
        echo.
        echo 🎯 新建工程流程测试:
        echo 1. 启动程序，创建一些测试数据
        echo    - 右键"作动器" → 新建 → 作动器组
        echo    - 右键"传感器" → 新建 → 传感器组
        echo 2. 点击"文件" → "新建工程"
        echo 3. 观察界面立即清空（第一步）
        echo 4. 选择保存路径，完成工程创建
        echo 5. 验证界面重新初始化为新工程状态
        echo.
        echo 🎯 拖拽视觉反馈测试:
        echo 1. 创建硬件设备:
        echo    - 右键"作动器" → 新建 → 作动器组 → 选择"50kN_作动器组"
        echo    - 右键"传感器" → 新建 → 传感器组 → 选择"载荷"
        echo 2. 测试拖拽源节点颜色变化:
        echo    - 拖拽作动器设备，观察节点变为浅蓝色
        echo    - 拖拽完成后，观察节点恢复原始颜色
        echo 3. 测试拖拽目标节点高亮:
        echo    - 拖拽作动器到"控制"节点上方，观察"控制"节点变为浅绿色
        echo    - 移动到其他节点，观察"控制"节点恢复原色
        echo    - 拖拽到不可接收的节点，观察无高亮效果
        echo 4. 测试拖拽完成:
        echo    - 在"控制"节点上释放，观察关联成功且颜色恢复
        echo.
        echo 🔍 验证要点:
        echo ├─ ✅ 新建工程时先清空再添加数据
        echo ├─ ✅ 拖拽源节点颜色正确变化
        echo ├─ ✅ 拖拽目标节点正确高亮
        echo ├─ ✅ 颜色在拖拽完成后正确恢复
        echo ├─ ✅ 不可接收的目标不会高亮
        echo └─ ✅ 所有拖拽约束正常工作
        echo.
        echo 启动程序测试增强拖拽功能...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
        echo 启动程序...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
        echo 启动程序...
        start release\SiteResConfig.exe
    ) else (
        echo 警告: 找不到可执行文件！
    )
)

echo.
echo 📖 增强拖拽功能详细测试指南:
echo.
echo 🎯 新建工程流程验证:
echo 1. 准备测试数据:
echo    - 创建一些作动器组和传感器组
echo    - 进行一些拖拽关联操作
echo    - 确保界面有数据内容
echo.
echo 2. 执行新建工程:
echo    - 点击"文件" → "新建工程"
echo    - 观察界面是否立即清空
echo    - 选择保存路径并完成创建
echo    - 验证界面是否重新初始化
echo.
echo 🎯 拖拽视觉反馈验证:
echo 1. 拖拽源节点颜色变化:
echo    - 开始拖拽时: 节点变为浅蓝色背景 + 深蓝色文字
echo    - 拖拽过程中: 颜色保持不变
echo    - 拖拽完成后: 自动恢复原始颜色
echo.
echo 2. 拖拽目标节点高亮:
echo    - 拖拽到可接收节点: 节点变为浅绿色背景 + 深绿色文字
echo    - 移动到其他节点: 之前的高亮节点恢复原色
echo    - 拖拽到不可接收节点: 无高亮效果
echo    - 拖拽离开控件: 所有高亮节点恢复原色
echo.
echo 3. 颜色恢复验证:
echo    - 拖拽取消: 所有颜色正确恢复
echo    - 拖拽完成: 所有颜色正确恢复
echo    - 拖拽离开: 目标高亮正确清除
echo.
echo 🔍 关键验证点:
echo ✓ 新建工程流程：先清空，再添加
echo ✓ 拖拽源颜色：浅蓝色背景 + 深蓝色文字
echo ✓ 拖拽目标高亮：浅绿色背景 + 深绿色文字
echo ✓ 颜色自动恢复：拖拽完成后恢复原色
echo ✓ 高亮逻辑正确：只有可接收目标才高亮
echo ✓ 状态管理完善：无颜色残留或异常
echo.
pause
