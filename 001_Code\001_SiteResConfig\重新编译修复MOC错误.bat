@echo off
chcp 65001 >nul
echo 修复TreeInteractionHandler MOC编译错误
echo =====================================

cd /d "D:\Work\Project\004_SiteResConfig_ProjectAll\001_Code\001_SiteResConfig\SiteResConfig"

echo 步骤1: 清理所有编译文件...
if exist debug\*.o del /q debug\*.o 2>nul
if exist debug\*.moc del /q debug\*.moc 2>nul
if exist release\*.o del /q release\*.o 2>nul
if exist release\*.moc del /q release\*.moc 2>nul

echo 步骤2: 删除构建缓存...
if exist .qmake.stash del /q .qmake.stash 2>nul
if exist Makefile del /q Makefile 2>nul
if exist Makefile.Debug del /q Makefile.Debug 2>nul
if exist Makefile.Release del /q Makefile.Release 2>nul

echo 步骤3: 手动检查TreeInteractionHandler文件...
if exist "include\TreeInteractionHandler.h" (
    echo   ✓ 找到头文件: include\TreeInteractionHandler.h
) else (
    echo   ✗ 未找到头文件: include\TreeInteractionHandler.h
)

if exist "src\TreeInteractionHandler.cpp" (
    echo   ✓ 找到源文件: src\TreeInteractionHandler.cpp
) else (
    echo   ✗ 未找到源文件: src\TreeInteractionHandler.cpp
)

echo.
echo 步骤4: 检查MOC包含...
findstr /c:"#include \"TreeInteractionHandler.moc\"" "src\TreeInteractionHandler.cpp" >nul
if %errorlevel%==0 (
    echo   ✓ MOC包含正确
) else (
    echo   ✗ MOC包含缺失
)

echo.
echo =====================================
echo 修复建议:
echo 1. 在Qt Creator中打开 SiteResConfig_Simple.pro
echo 2. 右键项目 → 清理
echo 3. 右键项目 → 运行qmake
echo 4. 右键项目 → 重新构建
echo.
echo 或者手动执行以下步骤:
echo 1. 确保Qt环境变量已设置
echo 2. 运行: qmake SiteResConfig_Simple.pro
echo 3. 运行: nmake (Visual Studio) 或 mingw32-make (MinGW)
echo =====================================
pause 