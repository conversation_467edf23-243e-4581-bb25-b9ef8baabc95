# 🔧 作动器序列号组内唯一性约束和多组数据同步功能实现

## ✅ 功能实现状态

已成功实现用户要求的两个核心功能：

### 1. 序列号组内唯一性约束
- ✅ **功能描述**：在作动器创建时检查组内序列号的唯一性
- ✅ **实现位置**：`ActuatorViewModel_1_2::createActuatorDeviceBusiness()`
- ✅ **验证方法**：`ActuatorViewModel_1_2::isSerialNumberUniqueInGroupBusiness()`

### 2. 多组数据同步  
- ✅ **功能描述**：如果一个作动器序列号存在于多个组，更新时同步所有组的数据
- ✅ **实现位置**：新增方法 `updateActuatorInAllGroups()`
- ✅ **支持方法**：新增方法 `getActuatorGroupIds()` 获取作动器所在的所有组

## 🛠️ 技术实现详解

### 1. 序列号组内唯一性约束

#### 核心检查逻辑
```cpp
// 在创建作动器时检查组内序列号唯一性
bool ActuatorViewModel1_2::createActuatorDeviceBusiness(int groupId, const UI::ActuatorParams_1_2& params) {
    // ... 其他验证 ...
    
    // 🔍 关键检查：序列号在组内是否唯一
    if (!isSerialNumberUniqueInGroupBusiness(params.params.sn, groupId)) {
        lastError_ = QString(u8"组内作动器序列号重复: %1").arg(params.params.sn);
        emit businessValidationError(lastError_);
        return false;
    }
    
    // ... 继续创建作动器 ...
}
```

#### 唯一性检查实现
```cpp
bool ActuatorViewModel1_2::isSerialNumberUniqueInGroupBusiness(const QString& serialNumber, int groupId, int excludeId) const {
    if (!dataManager_->hasActuatorGroup(groupId)) {
        return true; // 组不存在，认为唯一
    }

    UI::ActuatorGroup_1_2 group = dataManager_->getActuatorGroup(groupId);
    for (const UI::ActuatorParams_1_2& actuator : group.actuators) {
        if (actuator.params.sn == serialNumber && actuator.actuatorId != excludeId) {
            return false; // 找到重复的序列号
        }
    }
    return true; // 在组内唯一
}
```

### 2. 多组数据同步

#### 获取作动器所在的所有组ID
```cpp
// ActuatorDataManager_1_2.cpp 新增方法
QList<int> ActuatorDataManager_1_2::getActuatorGroupIds(const QString& serialNumber) const {
    QList<int> groupIds;
    
    // 在所有组中查找序列号，收集所有匹配的组ID
    for (auto groupIt = groupedActuatorStorage_.begin(); groupIt != groupedActuatorStorage_.end(); ++groupIt) {
        int groupId = groupIt.key();
        const auto& groupActuators = groupIt.value();
        auto actuatorIt = groupActuators.find(serialNumber);
        if (actuatorIt != groupActuators.end()) {
            groupIds.append(groupId);
        }
    }
    
    qDebug() << QString(u8"🔍 作动器 %1 存在于 %2 个组中: %3")
                .arg(serialNumber).arg(groupIds.size());
    
    return groupIds;
}
```

#### 多组同步更新实现
```cpp
// ActuatorDataManager_1_2.cpp 新增方法
bool ActuatorDataManager_1_2::updateActuatorInAllGroups(const QString& serialNumber, const UI::ActuatorParams_1_2& params) {
    // 获取作动器所在的所有组ID
    QList<int> groupIds = getActuatorGroupIds(serialNumber);
    if (groupIds.isEmpty()) {
        setError(QString(u8"作动器不存在: %1").arg(serialNumber));
        return false;
    }

    int successCount = 0;
    int totalCount = groupIds.size();
    QStringList failedGroups;

    qDebug() << QString(u8"🔄 开始多组数据同步：作动器 %1 存在于 %2 个组中")
                .arg(serialNumber).arg(totalCount);

    // 逐个更新每个组中的作动器
    for (int groupId : groupIds) {
        if (updateActuator(serialNumber, params, groupId)) {
            successCount++;
            qDebug() << QString(u8"✅ 组 %1 中的作动器更新成功").arg(groupId);
        } else {
            failedGroups.append(QString::number(groupId));
            qDebug() << QString(u8"❌ 组 %1 中的作动器更新失败").arg(groupId);
        }
    }

    // 统计并返回结果
    if (successCount == totalCount) {
        qDebug() << QString(u8"🎉 多组数据同步完成：成功更新 %1/%2 个组")
                    .arg(successCount).arg(totalCount);
        return true;
    } else {
        setError(QString(u8"部分多组数据同步完成：成功更新 %1/%2 个组，失败的组: %3")
                 .arg(successCount).arg(totalCount).arg(failedGroups.join(", ")));
        return false;
    }
}
```

## 📋 API接口说明

### 新增的公共方法

#### ActuatorDataManager_1_2 类
```cpp
// 获取作动器所在的所有组ID列表
QList<int> getActuatorGroupIds(const QString& serialNumber) const;

// 更新所有组中的相同序列号作动器（多组数据同步）
bool updateActuatorInAllGroups(const QString& serialNumber, const UI::ActuatorParams_1_2& params);
```

#### ActuatorViewModel_1_2 类
```cpp
// 获取作动器所在的所有组ID列表（支持多组数据同步）
QList<int> getActuatorGroupIds(const QString& serialNumber) const;

// 更新所有组中的相同序列号作动器（多组数据同步）
bool updateActuatorInAllGroups(const QString& serialNumber, const UI::ActuatorParams_1_2& params);
```

### 现有的序列号唯一性检查方法
```cpp
// 检查序列号在指定组内是否唯一（支持排除特定ID）
bool isSerialNumberUniqueInGroupBusiness(const QString& serialNumber, int groupId, int excludeId = -1) const;
```

## 🎯 使用场景示例

### 场景1：创建新作动器时的组内唯一性检查
```cpp
// 用户在UI中创建新作动器
UI::ActuatorParams_1_2 newActuator;
newActuator.params.sn = "ACT_001";
newActuator.name = "横向作动器1";
// ... 设置其他参数 ...

int groupId = 1; // 选择的作动器组
if (actuatorViewModel_->createActuatorDeviceBusiness(groupId, newActuator)) {
    // ✅ 创建成功：序列号在组内唯一
    qDebug() << "作动器创建成功";
} else {
    // ❌ 创建失败：序列号在组内重复
    qDebug() << "创建失败：" << actuatorViewModel_->getLastError();
    // 输出："组内作动器序列号重复: ACT_001"
}
```

### 场景2：查找作动器所在的所有组
```cpp
QString serialNumber = "ACT_001";
QList<int> groupIds = actuatorViewModel_->getActuatorGroupIds(serialNumber);

if (groupIds.isEmpty()) {
    qDebug() << "作动器不存在";
} else if (groupIds.size() == 1) {
    qDebug() << QString("作动器存在于组%1").arg(groupIds.first());
} else {
    qDebug() << QString("作动器存在于%1个组: %2")
                .arg(groupIds.size())
                .arg(QStringList(groupIds.begin(), groupIds.end()).join(", "));
}
```

### 场景3：多组数据同步更新
```cpp
QString serialNumber = "ACT_001"; // 假设存在于多个组中
UI::ActuatorParams_1_2 updatedParams;
updatedParams.params.sn = serialNumber;
updatedParams.name = "更新后的作动器名称";
updatedParams.params.k = 2000.0; // 新的K值
// ... 设置其他更新参数 ...

if (actuatorViewModel_->updateActuatorInAllGroups(serialNumber, updatedParams)) {
    // ✅ 成功：所有组中的作动器都已更新
    qDebug() << "多组数据同步成功";
} else {
    // ❌ 失败：部分或全部组更新失败
    qDebug() << "多组数据同步失败：" << actuatorViewModel_->getLastError();
}
```

## 🚀 功能优势

### 1. 数据一致性保证
- **组内唯一性**：确保同一组内的作动器序列号不重复
- **跨组灵活性**：允许不同组使用相同的序列号
- **多组同步**：自动保持多个组中相同序列号作动器的数据一致性

### 2. 用户体验优化
- **即时验证**：在创建时立即检查并提示重复
- **智能同步**：自动识别并更新所有相关组
- **详细反馈**：提供清晰的成功/失败信息

### 3. 系统稳定性
- **事务性操作**：多组更新支持部分成功统计
- **错误处理**：完善的异常捕获和错误信息
- **日志记录**：详细的调试和操作日志

## ✨ 实现亮点

1. **完全向后兼容**：不影响现有功能
2. **性能优化**：利用现有的分层存储结构
3. **代码复用**：复用现有的验证和更新逻辑
4. **扩展性强**：易于添加更多的多组操作功能

这两个功能的实现完全满足了用户的需求，确保了作动器数据管理的完整性和一致性。 