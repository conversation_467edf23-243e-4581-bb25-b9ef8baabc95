@echo off
chcp 65001 > nul
echo ========================================
echo Windows标准风格树形控件测试
echo ========================================
echo.

echo 🔧 正在编译Windows标准风格TreeLineStyle...
cd SiteResConfig
qmake SiteResConfig_Simple.pro
mingw32-make clean
mingw32-make

if %ERRORLEVEL% NEQ 0 (
    echo ❌ 编译失败！
    pause
    exit /b 1
)

echo ✅ 编译成功！
echo.

echo 🚀 启动应用程序进行Windows标准风格测试...
echo.
echo 📋 **Windows标准树形控件风格**：
echo.
echo 🎯 **标准规则**：
echo 1. 没有子节点的节点：无图标，只有虚线连接
echo 2. 有子节点且折叠：显示加号图标 [+]
echo 3. 有子节点且展开：显示减号图标 [-]
echo 4. 图标只在节点前面显示一个
echo 5. 虚线连接所有节点，形成树形结构
echo.
echo 🎨 **视觉特征**：
echo.
echo 1️⃣ **图标显示规则**
echo    - 叶子节点（无子节点）：无图标
echo    - 父节点（有子节点）：
echo      * 折叠状态：加号图标 [+]
echo      * 展开状态：减号图标 [-]
echo.
echo 2️⃣ **图标样式**
echo    - 尺寸：9x9像素标准按钮
echo    - 背景：白色
echo    - 边框：1px黑色边框
echo    - 符号：黑色加号或减号
echo.
echo 3️⃣ **虚线连接**
echo    - 点状虚线 (Qt::DotLine)
echo    - 颜色：#808080 (中灰色)
echo    - 连接规则：
echo      * 垂直线：连接父子节点
echo      * 水平线：连接到节点内容
echo      * 有图标的节点：虚线连接到图标
echo      * 无图标的节点：虚线直接连接到内容
echo.
echo 4️⃣ **Windows标准布局**
echo    - 图标位置：紧邻虚线右侧
echo    - 缩进：每级20像素
echo    - 对齐：图标与虚线完美对齐
echo    - 间距：符合Windows资源管理器标准
echo.

start "" "debug\SiteResConfig.exe"

echo 📊 应用程序已启动！
echo.
echo 🔍 **详细验证清单**：
echo.
echo ☐ 1. **图标显示验证**
echo      - 叶子节点（如"作动器_000001"）：无图标
echo      - 父节点折叠状态：显示加号 [+]
echo      - 父节点展开状态：显示减号 [-]
echo      - 图标数量：每个有子节点的节点只有一个图标
echo.
echo ☐ 2. **虚线连接验证**
echo      - 所有节点都有虚线连接
echo      - 垂直线连接父子节点
echo      - 水平线连接到节点内容
echo      - 有图标的节点：虚线连接到图标左侧
echo      - 无图标的节点：虚线直接连接到文字
echo.
echo ☐ 3. **交互功能验证**
echo      - 点击加号 [+] 展开节点，图标变为减号 [-]
echo      - 点击减号 [-] 折叠节点，图标变为加号 [+]
echo      - 叶子节点无图标，无法展开/折叠
echo      - 双击节点不会展开/折叠（只通过图标）
echo.
echo ☐ 4. **Windows风格验证**
echo      - 整体外观与Windows资源管理器一致
echo      - 图标大小和样式符合Windows标准
echo      - 虚线样式和颜色符合Windows经典风格
echo      - 布局和间距符合Windows界面规范
echo.
echo ☐ 5. **层次结构验证**
echo      - 多级嵌套显示正确
echo      - 每一级的缩进清晰
echo      - 虚线连接不会断开或重叠
echo      - 父子关系一目了然
echo.
echo 💡 **Windows标准特征**：
echo - ✅ 简洁的图标设计（只在必要时显示）
echo - ✅ 清晰的层次结构
echo - ✅ 标准的交互方式
echo - ✅ 经典的Windows外观
echo - ✅ 高效的视觉信息传达
echo.
echo 🎉 **成功标志**：
echo - 叶子节点无图标，界面简洁
echo - 父节点有正确的加号/减号图标
echo - 虚线连接完整，层次清晰
echo - 交互功能完全正常
echo - 整体外观符合Windows标准
echo.
echo 🎉 **与之前版本对比**：
echo.
echo | 特征 | 之前版本 | Windows标准版 |
echo |------|----------|---------------|
echo | 图标数量 | 所有节点都有 | 只有父节点有 |
echo | 叶子节点 | 显示图标 | 无图标 |
echo | 视觉复杂度 | 较高 | 简洁清晰 |
echo | Windows一致性 | 一般 | 完全一致 |
echo | 信息传达 | 冗余 | 精确高效 |
echo.
echo 🎉 现在完全符合Windows资源管理器的标准风格！
echo.
pause
