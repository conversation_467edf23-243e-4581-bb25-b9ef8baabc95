/****************************************************************************
** Meta object code from reading C++ file 'ActuatorViewModel_1_2.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.14.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../001_SiteResConfig_OldStruct/SiteResConfig/include/ActuatorViewModel_1_2.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ActuatorViewModel_1_2.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.14.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_ActuatorViewModel1_2_t {
    QByteArrayData data[27];
    char stringdata0[428];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_ActuatorViewModel1_2_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_ActuatorViewModel1_2_t qt_meta_stringdata_ActuatorViewModel1_2 = {
    {
QT_MOC_LITERAL(0, 0, 20), // "ActuatorViewModel1_2"
QT_MOC_LITERAL(1, 21, 19), // "actuatorDataChanged"
QT_MOC_LITERAL(2, 41, 0), // ""
QT_MOC_LITERAL(3, 42, 12), // "serialNumber"
QT_MOC_LITERAL(4, 55, 9), // "operation"
QT_MOC_LITERAL(5, 65, 24), // "actuatorGroupDataChanged"
QT_MOC_LITERAL(6, 90, 7), // "groupId"
QT_MOC_LITERAL(7, 98, 17), // "statisticsChanged"
QT_MOC_LITERAL(8, 116, 13), // "errorOccurred"
QT_MOC_LITERAL(9, 130, 5), // "error"
QT_MOC_LITERAL(10, 136, 18), // "operationCompleted"
QT_MOC_LITERAL(11, 155, 7), // "success"
QT_MOC_LITERAL(12, 163, 22), // "batchOperationProgress"
QT_MOC_LITERAL(13, 186, 7), // "current"
QT_MOC_LITERAL(14, 194, 5), // "total"
QT_MOC_LITERAL(15, 200, 23), // "batchOperationCompleted"
QT_MOC_LITERAL(16, 224, 7), // "summary"
QT_MOC_LITERAL(17, 232, 12), // "syncProgress"
QT_MOC_LITERAL(18, 245, 5), // "stage"
QT_MOC_LITERAL(19, 251, 10), // "percentage"
QT_MOC_LITERAL(20, 262, 13), // "syncCompleted"
QT_MOC_LITERAL(21, 276, 28), // "actuatorGroupCreatedBusiness"
QT_MOC_LITERAL(22, 305, 9), // "groupName"
QT_MOC_LITERAL(23, 315, 29), // "actuatorDeviceCreatedBusiness"
QT_MOC_LITERAL(24, 345, 28), // "actuatorDeviceEditedBusiness"
QT_MOC_LITERAL(25, 374, 29), // "actuatorDeviceDeletedBusiness"
QT_MOC_LITERAL(26, 404, 23) // "businessValidationError"

    },
    "ActuatorViewModel1_2\0actuatorDataChanged\0"
    "\0serialNumber\0operation\0"
    "actuatorGroupDataChanged\0groupId\0"
    "statisticsChanged\0errorOccurred\0error\0"
    "operationCompleted\0success\0"
    "batchOperationProgress\0current\0total\0"
    "batchOperationCompleted\0summary\0"
    "syncProgress\0stage\0percentage\0"
    "syncCompleted\0actuatorGroupCreatedBusiness\0"
    "groupName\0actuatorDeviceCreatedBusiness\0"
    "actuatorDeviceEditedBusiness\0"
    "actuatorDeviceDeletedBusiness\0"
    "businessValidationError"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_ActuatorViewModel1_2[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      14,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
      14,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    2,   84,    2, 0x06 /* Public */,
       5,    2,   89,    2, 0x06 /* Public */,
       7,    0,   94,    2, 0x06 /* Public */,
       8,    1,   95,    2, 0x06 /* Public */,
      10,    2,   98,    2, 0x06 /* Public */,
      12,    2,  103,    2, 0x06 /* Public */,
      15,    2,  108,    2, 0x06 /* Public */,
      17,    2,  113,    2, 0x06 /* Public */,
      20,    1,  118,    2, 0x06 /* Public */,
      21,    2,  121,    2, 0x06 /* Public */,
      23,    2,  126,    2, 0x06 /* Public */,
      24,    2,  131,    2, 0x06 /* Public */,
      25,    1,  136,    2, 0x06 /* Public */,
      26,    1,  139,    2, 0x06 /* Public */,

 // signals: parameters
    QMetaType::Void, QMetaType::QString, QMetaType::QString,    3,    4,
    QMetaType::Void, QMetaType::Int, QMetaType::QString,    6,    4,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,    9,
    QMetaType::Void, QMetaType::QString, QMetaType::Bool,    4,   11,
    QMetaType::Void, QMetaType::Int, QMetaType::Int,   13,   14,
    QMetaType::Void, QMetaType::Bool, QMetaType::QString,   11,   16,
    QMetaType::Void, QMetaType::QString, QMetaType::Int,   18,   19,
    QMetaType::Void, QMetaType::Bool,   11,
    QMetaType::Void, QMetaType::QString, QMetaType::Int,   22,    6,
    QMetaType::Void, QMetaType::QString, QMetaType::Int,    3,    6,
    QMetaType::Void, QMetaType::QString, QMetaType::Int,    3,    6,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    9,

       0        // eod
};

void ActuatorViewModel1_2::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ActuatorViewModel1_2 *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->actuatorDataChanged((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 1: _t->actuatorGroupDataChanged((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 2: _t->statisticsChanged(); break;
        case 3: _t->errorOccurred((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 4: _t->operationCompleted((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< bool(*)>(_a[2]))); break;
        case 5: _t->batchOperationProgress((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 6: _t->batchOperationCompleted((*reinterpret_cast< bool(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 7: _t->syncProgress((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 8: _t->syncCompleted((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 9: _t->actuatorGroupCreatedBusiness((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 10: _t->actuatorDeviceCreatedBusiness((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 11: _t->actuatorDeviceEditedBusiness((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 12: _t->actuatorDeviceDeletedBusiness((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 13: _t->businessValidationError((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (ActuatorViewModel1_2::*)(const QString & , const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ActuatorViewModel1_2::actuatorDataChanged)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (ActuatorViewModel1_2::*)(int , const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ActuatorViewModel1_2::actuatorGroupDataChanged)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (ActuatorViewModel1_2::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ActuatorViewModel1_2::statisticsChanged)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (ActuatorViewModel1_2::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ActuatorViewModel1_2::errorOccurred)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (ActuatorViewModel1_2::*)(const QString & , bool );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ActuatorViewModel1_2::operationCompleted)) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (ActuatorViewModel1_2::*)(int , int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ActuatorViewModel1_2::batchOperationProgress)) {
                *result = 5;
                return;
            }
        }
        {
            using _t = void (ActuatorViewModel1_2::*)(bool , const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ActuatorViewModel1_2::batchOperationCompleted)) {
                *result = 6;
                return;
            }
        }
        {
            using _t = void (ActuatorViewModel1_2::*)(const QString & , int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ActuatorViewModel1_2::syncProgress)) {
                *result = 7;
                return;
            }
        }
        {
            using _t = void (ActuatorViewModel1_2::*)(bool );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ActuatorViewModel1_2::syncCompleted)) {
                *result = 8;
                return;
            }
        }
        {
            using _t = void (ActuatorViewModel1_2::*)(const QString & , int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ActuatorViewModel1_2::actuatorGroupCreatedBusiness)) {
                *result = 9;
                return;
            }
        }
        {
            using _t = void (ActuatorViewModel1_2::*)(const QString & , int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ActuatorViewModel1_2::actuatorDeviceCreatedBusiness)) {
                *result = 10;
                return;
            }
        }
        {
            using _t = void (ActuatorViewModel1_2::*)(const QString & , int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ActuatorViewModel1_2::actuatorDeviceEditedBusiness)) {
                *result = 11;
                return;
            }
        }
        {
            using _t = void (ActuatorViewModel1_2::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ActuatorViewModel1_2::actuatorDeviceDeletedBusiness)) {
                *result = 12;
                return;
            }
        }
        {
            using _t = void (ActuatorViewModel1_2::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ActuatorViewModel1_2::businessValidationError)) {
                *result = 13;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject ActuatorViewModel1_2::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_ActuatorViewModel1_2.data,
    qt_meta_data_ActuatorViewModel1_2,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *ActuatorViewModel1_2::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ActuatorViewModel1_2::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_ActuatorViewModel1_2.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int ActuatorViewModel1_2::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 14)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 14;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 14)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 14;
    }
    return _id;
}

// SIGNAL 0
void ActuatorViewModel1_2::actuatorDataChanged(const QString & _t1, const QString & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void ActuatorViewModel1_2::actuatorGroupDataChanged(int _t1, const QString & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void ActuatorViewModel1_2::statisticsChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void ActuatorViewModel1_2::errorOccurred(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void ActuatorViewModel1_2::operationCompleted(const QString & _t1, bool _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}

// SIGNAL 5
void ActuatorViewModel1_2::batchOperationProgress(int _t1, int _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 5, _a);
}

// SIGNAL 6
void ActuatorViewModel1_2::batchOperationCompleted(bool _t1, const QString & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 6, _a);
}

// SIGNAL 7
void ActuatorViewModel1_2::syncProgress(const QString & _t1, int _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 7, _a);
}

// SIGNAL 8
void ActuatorViewModel1_2::syncCompleted(bool _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 8, _a);
}

// SIGNAL 9
void ActuatorViewModel1_2::actuatorGroupCreatedBusiness(const QString & _t1, int _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 9, _a);
}

// SIGNAL 10
void ActuatorViewModel1_2::actuatorDeviceCreatedBusiness(const QString & _t1, int _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 10, _a);
}

// SIGNAL 11
void ActuatorViewModel1_2::actuatorDeviceEditedBusiness(const QString & _t1, int _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 11, _a);
}

// SIGNAL 12
void ActuatorViewModel1_2::actuatorDeviceDeletedBusiness(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 12, _a);
}

// SIGNAL 13
void ActuatorViewModel1_2::businessValidationError(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 13, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
