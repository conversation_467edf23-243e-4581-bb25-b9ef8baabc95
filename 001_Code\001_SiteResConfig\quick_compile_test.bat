@echo off
echo ========================================
echo  快速编译测试
echo ========================================

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo 清理构建文件...
if exist "Makefile" del Makefile
if exist "*.o" del *.o

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译测试...
mingw32-make -j4
if errorlevel 1 (
    echo 编译失败！
    echo 请检查上面的错误信息。
) else (
    echo 编译成功！
    echo 可执行文件已生成。
)

pause
