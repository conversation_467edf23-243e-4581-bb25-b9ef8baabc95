# 🚀 SiteResConfig 快速启动指南

## ⚡ **最简单的编译方法**

### **步骤1: 使用Qt Creator（推荐）**
1. 打开Qt Creator
2. 点击"打开项目"
3. 选择文件：`SiteResConfig/SiteResConfig_Simple.pro`
4. 选择构建套件：
   - `Desktop Qt 5.14.2 MSVC2017 64bit` (如果使用MSVC)
   - `Desktop Qt 5.14.2 MinGW 32-bit` (如果使用MinGW)
5. 点击左下角的"运行"按钮（绿色三角形）

### **步骤2: 或使用命令行**
```batch
# MinGW编译（推荐）
compile_qt_mingw.bat

# 或MSVC编译
build_simple.bat
```

## 🛠️ **如果遇到编译错误**

### **错误1: 'CustomTreeItem' does not name a type**
✅ **已修复** - 使用`MainWindow_Qt_Simple.cpp`简化实现

### **错误2: /utf-8 No such file or directory**
✅ **已修复** - 使用`SiteResConfig_Simple.pro`项目文件

### **错误3: 找不到Qt**
```batch
# MinGW版本 - 修改compile_qt_mingw.bat中的Qt路径
set QTDIR=您的Qt安装路径\5.14.2\mingw73_32

# MSVC版本 - 修改build_simple.bat中的Qt路径
set QTDIR=您的Qt安装路径\5.14.2\msvc2017_64
```

### **错误4: 找不到编译器**
- **MinGW**: 确保安装了Qt MinGW版本和MinGW编译器
- **MSVC**: 使用"VS2017 x64 Native Tools Command Prompt"运行编译脚本
- 或在Qt Creator中选择正确的构建套件

## 🎯 **编译成功后**

程序将显示现代化的Qt图形界面：
- ✅ **左侧面板**: 硬件资源和试验配置管理
- ✅ **右侧工作区**: 系统概览、实时数据、系统日志
- ✅ **菜单栏**: 文件、硬件、帮助菜单
- ✅ **状态栏**: 连接状态和系统信息

## 📁 **项目文件说明**

- `SiteResConfig_Simple.pro` - **简化版项目文件**（推荐使用）
- `SiteResConfig.pro` - 完整版项目文件
- `build_simple.bat` - 简化编译脚本
- `build_qt.bat` - 完整编译脚本

## 🎨 **界面预览**

启动后您将看到：
```
┌─────────────────────────────────────────────────────────┐
│ 文件(F)  硬件(H)  帮助(H)                                │
├─────────────┬───────────────────────────────────────────┤
│ 硬件资源    │ 系统概览                                    │
│ ├─硬件节点  │ ┌─系统状态─────────────────────────────────┐ │
│ ├─作动器    │ │ 连接状态: 未连接                        │ │
│ └─传感器    │ │ 系统状态: 空闲                          │ │
│             │ │ 活动通道: 0/0                           │ │
│ 试验配置    │ └─────────────────────────────────────────┘ │
│ ├─加载通道  │ ┌─快速操作─────────────────────────────────┐ │
│ └─载荷谱    │ │ [连接硬件] [断开连接] [紧急停止]        │ │
│             │ │ [开始试验] [暂停试验] [停止试验]        │ │
│             │ └─────────────────────────────────────────┘ │
├─────────────┴───────────────────────────────────────────┤
│ 就绪                                        未连接      │
└─────────────────────────────────────────────────────────┘
```

## 🎉 **开始使用**

1. **连接硬件**: 点击"连接硬件"按钮
2. **配置试验**: 在左侧面板添加硬件和通道
3. **开始试验**: 点击"开始试验"按钮
4. **查看数据**: 切换到"实时数据"标签页
5. **查看日志**: 切换到"系统日志"标签页

现在就开始使用Qt Creator编译运行吧！🚀
