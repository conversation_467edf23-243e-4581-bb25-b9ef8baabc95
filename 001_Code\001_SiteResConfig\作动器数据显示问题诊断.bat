@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🔍 作动器数据显示问题诊断
echo ========================================
echo.

echo 📋 问题描述：
echo   - 打开工程：桌面20250819171946_实验工程.xls
echo   - 自定义_作动器组 有两个作动器信息
echo   - 界面只显示了一个作动器
echo.

echo 🔍 可能的原因分析：
echo.
echo 1. 📊 Excel数据问题：
echo    - Excel文件中数据格式不正确
echo    - 组序号或作动器序列号重复
echo    - 数据行存在空值或格式错误
echo.
echo 2. 🔄 数据导入问题：
echo    - 导入过程中跳过了某些行
echo    - 序号重映射逻辑有问题
echo    - 数据验证过程中过滤了数据
echo.
echo 3. 💾 数据管理器问题：
echo    - 保存到数据管理器时丢失数据
echo    - 数据管理器内部存储有问题
echo    - 获取数据时逻辑错误
echo.
echo 4. 🖥️ 界面显示问题：
echo    - 树控件刷新逻辑有问题
echo    - 数据绑定不完整
echo    - 界面更新时机不对
echo.

echo 🔧 诊断步骤：
echo.
echo 步骤1: 检查Excel文件内容
echo   - 打开桌面的"20250819171946_实验工程.xls"
echo   - 查看"作动器详细配置"工作表
echo   - 确认"自定义_作动器组"的数据行数
echo   - 检查组序号、作动器序列号是否正确
echo.

echo 步骤2: 启动应用程序并观察日志
echo   - 编译并启动应用程序
echo   - 打开工程文件
echo   - 观察控制台日志输出
echo   - 查找以下关键信息：
echo.

echo 🔍 关键日志信息：
echo.
echo A. 数据导入阶段：
echo   "=== 作动器工作表前3行内容 ==="
echo   "🔢 序号映射：Excel组序号 X -> 实际组序号 Y"
echo   "✅ 创建新作动器组：ID=X, 名称='自定义_作动器组'"
echo   "📊 组X (自定义_作动器组)：Y个作动器"
echo.

echo B. 数据验证阶段：
echo   "=== 作动器数据序号验证报告 ==="
echo   "✅ 组序号连续性检查通过：1-N"
echo   "📈 总计：X个组，Y个作动器"
echo.

echo C. 界面显示阶段：
echo   "已填充作动器数据: X个组"
echo   "从数据管理器获取作动器组: X个组"
echo.

echo 步骤3: 手动验证数据管理器
echo   - 在导入完成后，检查数据管理器状态
echo   - 验证getAllActuatorGroups()返回的数据
echo   - 确认每个组中的作动器数量
echo.

echo 步骤4: 检查界面显示逻辑
echo   - 确认RefreshHardwareTreeFromDataManagers()被调用
echo   - 验证树控件的节点创建逻辑
echo   - 检查是否所有作动器都被添加到界面
echo.

echo 🎯 预期的正确日志输出：
echo.
echo 如果"自定义_作动器组"有2个作动器，应该看到：
echo   "🔢 序号映射：Excel组序号 1 -> 实际组序号 1 (组名称: 自定义_作动器组)"
echo   "✅ 创建新作动器组：ID=1, 名称='自定义_作动器组'"
echo   "📊 组1 (自定义_作动器组)：2个作动器"
echo   "📈 总计：1个组，2个作动器"
echo   "已填充作动器数据: 1个组"
echo.

echo 🚨 异常情况检查：
echo.
echo 1. 如果看到"跳过无效行"：
echo    - 检查Excel数据格式
echo    - 确认组序号和组名称不为空
echo.
echo 2. 如果看到"序列号重复"：
echo    - 检查作动器序列号是否唯一
echo    - 查看自动生成的新序列号
echo.
echo 3. 如果组中作动器数量不对：
echo    - 检查数据导入逻辑
echo    - 验证数据管理器保存逻辑
echo.
echo 4. 如果界面显示不完整：
echo    - 检查界面刷新逻辑
echo    - 验证树控件节点创建
echo.

echo ========================================
echo 🚀 开始诊断
echo ========================================
echo.
echo 请按以下步骤进行诊断：
echo.
echo 1. 首先检查Excel文件内容
echo 2. 编译并启动应用程序
echo 3. 打开工程文件并观察日志
echo 4. 记录所有相关的日志信息
echo 5. 根据日志信息定位问题
echo.
echo 诊断完成后，请提供：
echo   - Excel文件中"自定义_作动器组"的具体数据
echo   - 控制台日志的完整输出
echo   - 界面显示的实际情况
echo.
pause
