@echo off
echo ========================================
echo  SiteResConfig Simple Build Script
echo ========================================

REM 设置Qt环境变量（请根据您的Qt安装路径修改）
set QTDIR=C:\Qt\5.14.2\msvc2017_64
set PATH=%QTDIR%\bin;%PATH%

REM 检查Qt是否可用
echo 检查Qt环境...
qmake --version
if errorlevel 1 (
    echo 错误: 找不到Qt环境！
    echo 请确保Qt 5.14.2已正确安装
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

REM 清理之前的构建
if exist "Makefile" del Makefile
if exist "Makefile.Debug" del Makefile.Debug
if exist "Makefile.Release" del Makefile.Release

echo.
echo 使用简化项目文件生成Makefile...
qmake SiteResConfig_Simple.pro
if errorlevel 1 (
    echo 错误: qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目...
nmake
if errorlevel 1 (
    echo 错误: 编译失败！
    pause
    exit /b 1
)

echo.
echo ========================================
echo  编译成功！
echo ========================================

REM 查找并运行可执行文件
if exist "SiteResConfig.exe" (
    echo 运行程序: SiteResConfig.exe
    SiteResConfig.exe
) else if exist "debug\SiteResConfig.exe" (
    echo 运行程序: debug\SiteResConfig.exe
    debug\SiteResConfig.exe
) else if exist "release\SiteResConfig.exe" (
    echo 运行程序: release\SiteResConfig.exe
    release\SiteResConfig.exe
) else (
    echo 警告: 找不到可执行文件！
    dir *.exe /s
)

pause
