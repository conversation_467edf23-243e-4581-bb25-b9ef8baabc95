#pragma once

/**
 * @file Common_Fixed.h
 * @brief SiteResConfig - Common Definitions (Fixed Encoding)
 * @details Common definitions, constants and utility functions
 * <AUTHOR> Assistant
 * @date 2025-08-05
 * @version 1.0.0
 */

#include <memory>
#include <string>
#include <vector>
#include <map>
#include <functional>
#include <thread>
#include <mutex>
#include <atomic>
#include <chrono>

// Windows compatibility support
#ifdef _WIN32
    #define WIN32_LEAN_AND_MEAN
    #include <windows.h>
    #include <winsock2.h>
    #include <ws2tcpip.h>
    #pragma comment(lib, "ws2_32.lib")
#endif

// Project version information
#define SITERESCONFIG_VERSION_MAJOR 1
#define SITERESCONFIG_VERSION_MINOR 0
#define SITERESCONFIG_VERSION_PATCH 0
#define SITERESCONFIG_VERSION_STRING "1.0.0"

// Software information
#define SOFTWARE_NAME "SiteResConfig"
#define SOFTWARE_COMPANY "Company"
#define SOFTWARE_COPYRIGHT "Copyright_2025"

// Common type definitions
using StringType = std::string;
using WStringType = std::wstring;

// Smart pointer type aliases
template<typename T>
using UniquePtr = std::unique_ptr<T>;

template<typename T>
using SharedPtr = std::shared_ptr<T>;

template<typename T>
using WeakPtr = std::weak_ptr<T>;

// Common constants definition
namespace Constants {
    // System configuration
    constexpr int MAX_CHANNELS = 64;           // Maximum number of channels
    constexpr int MAX_ACTUATORS = 32;          // Maximum number of actuators
    constexpr int MAX_SENSORS = 128;           // Maximum number of sensors
    
    // Control parameters
    constexpr double DEFAULT_SAMPLE_RATE = 1000.0;  // Default sample rate (Hz)
    constexpr double MIN_CONTROL_PERIOD = 1.0;      // Minimum control period (ms)
    constexpr double MAX_CONTROL_PERIOD = 100.0;    // Maximum control period (ms)
    
    // Safety parameters
    constexpr int SAFETY_LEVELS = 3;           // Number of safety threshold levels
    constexpr double FAULT_DATA_BEFORE = 20.0; // Fault data save time before (s)
    constexpr double FAULT_DATA_AFTER = 5.0;   // Fault data save time after (s)
    
    // File paths
    constexpr const char* CONFIG_DIR = "config";
    constexpr const char* DATA_DIR = "data";
    constexpr const char* LOG_DIR = "logs";
    constexpr const char* TEMP_DIR = "temp";
}

// Enumeration definitions
namespace Enums {
    // Device type
    enum class DeviceType {
        Unknown = 0,
        Actuator,       // Actuator
        Sensor,         // Sensor
        Controller      // Controller
    };
    
    // Sensor type
    enum class SensorType {
        Unknown = 0,
        Force,          // Force sensor
        Position,       // Position sensor
        Pressure,       // Pressure sensor
        Temperature     // Temperature sensor
    };
    
    // Actuator type
    enum class ActuatorType {
        Unknown = 0,
        SingleRod,      // Single rod
        DoubleRod       // Double rod
    };
    
    // Safety threshold level
    enum class SafetyLevel {
        Level1 = 1,     // Interface warning
        Level2 = 2,     // Load reduction/pause
        Level3 = 3      // Disable and unload
    };
    
    // System state
    enum class SystemState {
        Idle = 0,       // Idle
        Ready,          // Ready
        Running,        // Running
        Paused,         // Paused
        Error,          // Error
        Emergency       // Emergency stop
    };
}

// Utility functions namespace
namespace Utils {
    /**
     * @brief Get current timestamp (milliseconds)
     * @return Timestamp
     */
    inline int64_t GetCurrentTimestamp() {
        auto now = std::chrono::system_clock::now();
        auto duration = now.time_since_epoch();
        return std::chrono::duration_cast<std::chrono::milliseconds>(duration).count();
    }
    
    /**
     * @brief Format timestamp string
     * @param timestamp Timestamp (milliseconds)
     * @return Formatted time string
     */
    StringType FormatTimestamp(int64_t timestamp);
    
    /**
     * @brief Convert UTF-8 string to wide string
     * @param utf8Str UTF-8 string
     * @return Wide string
     */
    WStringType Utf8ToWide(const StringType& utf8Str);
    
    /**
     * @brief Convert wide string to UTF-8 string
     * @param wideStr Wide string
     * @return UTF-8 string
     */
    StringType WideToUtf8(const WStringType& wideStr);
}

// Log macros definition
#define LOG_INFO(msg)    // TODO: Implement logging system
#define LOG_WARNING(msg) // TODO: Implement logging system
#define LOG_ERROR(msg)   // TODO: Implement logging system
#define LOG_DEBUG(msg)   // TODO: Implement logging system
