/**
 * @file test_actuator_id_assignment.cpp
 * @brief 测试作动器ID自动分配功能
 * @details 验证作动器ID是否正确分配和管理
 * <AUTHOR> Assistant
 * @date 2025-08-14
 * @version 1.0.0
 */

#include <iostream>
#include <cassert>
#include "ActuatorDataManager.h"
#include "DataModels_Fixed.h"

using namespace DataModels;

/**
 * @brief 测试作动器ID自动分配
 */
void testActuatorIdAssignment() {
    std::cout << "=== 测试作动器ID自动分配 ===" << std::endl;
    
    // 创建测试项目和DataManager
    TestProject project;
    ActuatorDataManager manager(&project);
    
    // 创建第一个作动器（ID为0，应该自动分配为1）
    UI::ActuatorParams actuator1;
    actuator1.serialNumber = "ACT001";
    actuator1.actuatorName = "测试作动器1";
    actuator1.actuatorType = "Hydraulic";
    actuator1.actuatorId = 0; // 默认值，应该被自动分配
    
    bool result1 = manager.addActuator(actuator1);
    assert(result1 && "添加第一个作动器应该成功");
    
    // 获取添加后的作动器，检查ID
    UI::ActuatorParams retrieved1 = manager.getActuator("ACT001");
    assert(retrieved1.actuatorId == 1 && "第一个作动器ID应该为1");
    std::cout << "✓ 第一个作动器ID自动分配为: " << retrieved1.actuatorId << std::endl;
    
    // 创建第二个作动器（ID为0，应该自动分配为2）
    UI::ActuatorParams actuator2;
    actuator2.serialNumber = "ACT002";
    actuator2.actuatorName = "测试作动器2";
    actuator2.actuatorType = "Electric";
    actuator2.actuatorId = 0; // 默认值，应该被自动分配
    
    bool result2 = manager.addActuator(actuator2);
    assert(result2 && "添加第二个作动器应该成功");
    
    // 获取添加后的作动器，检查ID
    UI::ActuatorParams retrieved2 = manager.getActuator("ACT002");
    assert(retrieved2.actuatorId == 2 && "第二个作动器ID应该为2");
    std::cout << "✓ 第二个作动器ID自动分配为: " << retrieved2.actuatorId << std::endl;
    
    // 创建第三个作动器（指定ID为10）
    UI::ActuatorParams actuator3;
    actuator3.serialNumber = "ACT003";
    actuator3.actuatorName = "测试作动器3";
    actuator3.actuatorType = "Pneumatic";
    actuator3.actuatorId = 10; // 指定ID
    
    bool result3 = manager.addActuator(actuator3);
    assert(result3 && "添加第三个作动器应该成功");
    
    // 获取添加后的作动器，检查ID
    UI::ActuatorParams retrieved3 = manager.getActuator("ACT003");
    assert(retrieved3.actuatorId == 10 && "第三个作动器ID应该为10");
    std::cout << "✓ 第三个作动器ID保持指定值: " << retrieved3.actuatorId << std::endl;
    
    // 创建第四个作动器（ID为0，应该自动分配为11，因为10已被使用）
    UI::ActuatorParams actuator4;
    actuator4.serialNumber = "ACT004";
    actuator4.actuatorName = "测试作动器4";
    actuator4.actuatorType = "Hydraulic";
    actuator4.actuatorId = 0; // 默认值，应该被自动分配
    
    bool result4 = manager.addActuator(actuator4);
    assert(result4 && "添加第四个作动器应该成功");
    
    // 获取添加后的作动器，检查ID
    UI::ActuatorParams retrieved4 = manager.getActuator("ACT004");
    assert(retrieved4.actuatorId == 11 && "第四个作动器ID应该为11");
    std::cout << "✓ 第四个作动器ID自动分配为: " << retrieved4.actuatorId << std::endl;
    
    std::cout << "=== 作动器ID自动分配测试完成 ===" << std::endl << std::endl;
}

/**
 * @brief 测试作动器组ID唯一性验证
 */
void testActuatorGroupIdValidation() {
    std::cout << "=== 测试作动器组ID唯一性验证 ===" << std::endl;
    
    // 创建测试项目和DataManager
    TestProject project;
    ActuatorDataManager manager(&project);
    
    // 添加测试作动器
    UI::ActuatorParams actuator1;
    actuator1.serialNumber = "ACT001";
    actuator1.actuatorName = "测试作动器1";
    actuator1.actuatorId = 1;
    manager.addActuator(actuator1);
    
    UI::ActuatorParams actuator2;
    actuator2.serialNumber = "ACT002";
    actuator2.actuatorName = "测试作动器2";
    actuator2.actuatorId = 2;
    manager.addActuator(actuator2);
    
    UI::ActuatorParams actuator3;
    actuator3.serialNumber = "ACT003";
    actuator3.actuatorName = "测试作动器3";
    actuator3.actuatorId = 3;
    manager.addActuator(actuator3);
    
    // 创建作动器组，包含不同ID的作动器（应该成功）
    UI::ActuatorGroup validGroup;
    validGroup.groupId = 1;
    validGroup.groupName = "有效组";
    validGroup.actuators.append(actuator1);
    validGroup.actuators.append(actuator2);
    validGroup.actuators.append(actuator3);
    
    bool validResult = manager.saveActuatorGroup(validGroup);
    assert(validResult && "有效的作动器组应该保存成功");
    std::cout << "✓ 有效作动器组保存成功" << std::endl;
    
    // 创建作动器组，包含重复ID的作动器（应该失败）
    UI::ActuatorGroup invalidGroup;
    invalidGroup.groupId = 2;
    invalidGroup.groupName = "无效组";
    
    // 创建两个ID相同的作动器
    UI::ActuatorParams duplicateActuator1 = actuator1;
    UI::ActuatorParams duplicateActuator2 = actuator2;
    duplicateActuator2.actuatorId = actuator1.actuatorId; // 设置相同的ID
    
    invalidGroup.actuators.append(duplicateActuator1);
    invalidGroup.actuators.append(duplicateActuator2);
    
    bool invalidResult = manager.saveActuatorGroup(invalidGroup);
    assert(!invalidResult && "包含重复ID的作动器组应该保存失败");
    std::cout << "✓ 包含重复ID的作动器组正确被拒绝" << std::endl;
    std::cout << "✓ 错误信息: " << manager.getLastError().toStdString() << std::endl;
    
    std::cout << "=== 作动器组ID唯一性验证测试完成 ===" << std::endl << std::endl;
}

/**
 * @brief 测试作动器更新时ID保持
 */
void testActuatorUpdateIdPersistence() {
    std::cout << "=== 测试作动器更新时ID保持 ===" << std::endl;
    
    // 创建测试项目和DataManager
    TestProject project;
    ActuatorDataManager manager(&project);
    
    // 添加作动器
    UI::ActuatorParams original;
    original.serialNumber = "ACT001";
    original.actuatorName = "原始作动器";
    original.actuatorId = 0; // 将被自动分配
    
    bool addResult = manager.addActuator(original);
    assert(addResult && "添加作动器应该成功");
    
    // 获取分配的ID
    UI::ActuatorParams retrieved = manager.getActuator("ACT001");
    int assignedId = retrieved.actuatorId;
    std::cout << "✓ 作动器分配的ID: " << assignedId << std::endl;
    
    // 更新作动器信息
    UI::ActuatorParams updated = retrieved;
    updated.actuatorName = "更新后的作动器";
    updated.actuatorType = "Updated Type";
    updated.actuatorId = 0; // 尝试重置ID
    
    bool updateResult = manager.updateActuator("ACT001", updated);
    assert(updateResult && "更新作动器应该成功");
    
    // 验证ID是否保持不变
    UI::ActuatorParams afterUpdate = manager.getActuator("ACT001");
    assert(afterUpdate.actuatorId == assignedId && "更新后ID应该保持不变");
    assert(afterUpdate.actuatorName == "更新后的作动器" && "其他信息应该被更新");
    std::cout << "✓ 更新后ID保持不变: " << afterUpdate.actuatorId << std::endl;
    std::cout << "✓ 其他信息正确更新: " << afterUpdate.actuatorName.toStdString() << std::endl;
    
    std::cout << "=== 作动器更新时ID保持测试完成 ===" << std::endl << std::endl;
}

/**
 * @brief 主测试函数
 */
int main() {
    std::cout << "开始作动器ID分配功能测试..." << std::endl << std::endl;
    
    try {
        testActuatorIdAssignment();
        testActuatorGroupIdValidation();
        testActuatorUpdateIdPersistence();
        
        std::cout << "🎉 所有测试通过！作动器ID分配功能正常工作！" << std::endl;
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "❌ 测试失败: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "❌ 测试失败: 未知错误" << std::endl;
        return 1;
    }
}
