# JSON导出功能验证脚本
Write-Host "========================================" -ForegroundColor Green
Write-Host " JSON导出功能验证" -ForegroundColor Green  
Write-Host "========================================" -ForegroundColor Green

# 设置工作目录
$workDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $workDir

Write-Host "工作目录: $workDir" -ForegroundColor Yellow

# 检查可执行文件
$exePath = "build-SiteResConfig_Simple-Desktop_Qt_5_14_2_MinGW_32_bit-Debug\debug\SiteResConfig.exe"

if (Test-Path $exePath) {
    Write-Host "找到可执行文件: $exePath" -ForegroundColor Green
    
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host " JSON导出功能测试说明" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "1. 应用程序启动后，创建一些测试数据" -ForegroundColor White
    Write-Host "2. 使用菜单中的导出功能" -ForegroundColor White
    Write-Host "3. 选择JSON格式进行导出" -ForegroundColor White
    Write-Host "4. 验证先保存CSV再转换JSON的流程" -ForegroundColor White
    Write-Host "5. 检查生成的JSON文件格式" -ForegroundColor White
    Write-Host ""
    
    # 检查示例文件
    $sampleJson = "SiteResConfig\sample_configs\hardware_config.json"
    $sampleCsv = "SiteResConfig\sample_configs\hardware_config.csv"
    
    if (Test-Path $sampleJson) {
        Write-Host "示例JSON文件: $sampleJson" -ForegroundColor Green
    }
    
    if (Test-Path $sampleCsv) {
        Write-Host "示例CSV文件: $sampleCsv" -ForegroundColor Green
    }
    
    Write-Host ""
    Write-Host "启动应用程序..." -ForegroundColor Yellow
    
    # 启动应用程序
    Start-Process -FilePath $exePath -WorkingDirectory (Split-Path $exePath)
    
    Write-Host "应用程序已启动！" -ForegroundColor Green
    Write-Host "请在应用程序中测试JSON导出功能" -ForegroundColor Yellow
    
} else {
    Write-Host "错误: 找不到可执行文件 $exePath" -ForegroundColor Red
    Write-Host "请先编译项目" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
