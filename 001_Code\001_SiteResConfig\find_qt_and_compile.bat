@echo off
echo Finding Qt installation and compiling...
echo.

REM Try to find Qt installation in common locations
set QT_FOUND=0

REM Check common Qt installation paths
if exist "C:\Qt\5.14.2\mingw73_32\bin\qmake.exe" (
    set QTDIR=C:\Qt\5.14.2\mingw73_32
    set MINGW_PATH=C:\Qt\Tools\mingw730_32\bin
    set QT_FOUND=1
    echo Found Qt 5.14.2 at C:\Qt\5.14.2\mingw73_32
)

if %QT_FOUND%==0 (
    if exist "C:\Qt\Qt5.14.2\5.14.2\mingw73_32\bin\qmake.exe" (
        set QTDIR=C:\Qt\Qt5.14.2\5.14.2\mingw73_32
        set MINGW_PATH=C:\Qt\Qt5.14.2\Tools\mingw730_32\bin
        set QT_FOUND=1
        echo Found Qt 5.14.2 at C:\Qt\Qt5.14.2\5.14.2\mingw73_32
    )
)

if %QT_FOUND%==0 (
    if exist "D:\Qt\5.14.2\mingw73_32\bin\qmake.exe" (
        set QTDIR=D:\Qt\5.14.2\mingw73_32
        set MINGW_PATH=D:\Qt\Tools\mingw730_32\bin
        set QT_FOUND=1
        echo Found Qt 5.14.2 at D:\Qt\5.14.2\mingw73_32
    )
)

if %QT_FOUND%==0 (
    echo ERROR: Qt 5.14.2 not found in common locations!
    echo Please check if Qt is installed and update the paths in this script.
    echo.
    echo Common Qt installation locations:
    echo   C:\Qt\5.14.2\mingw73_32
    echo   C:\Qt\Qt5.14.2\5.14.2\mingw73_32
    echo   D:\Qt\5.14.2\mingw73_32
    echo.
    echo You can also set QTDIR manually:
    echo   set QTDIR=your_qt_path
    echo   set PATH=%%QTDIR%%\bin;your_mingw_path\bin;%%PATH%%
    pause
    exit /b 1
)

REM Set environment variables
set PATH=%QTDIR%\bin;%MINGW_PATH%;%PATH%
echo Qt environment set successfully
echo QTDIR: %QTDIR%
echo MINGW: %MINGW_PATH%
echo.

REM Test if tools are available
qmake -v > nul 2>&1
if errorlevel 1 (
    echo ERROR: qmake not found or not working!
    echo PATH: %PATH%
    pause
    exit /b 1
)

mingw32-make --version > nul 2>&1
if errorlevel 1 (
    echo ERROR: mingw32-make not found or not working!
    echo PATH: %PATH%
    pause
    exit /b 1
)

echo Tools verified successfully
echo.

REM Enter project directory
cd /d "%~dp0\SiteResConfig"
if errorlevel 1 (
    echo ERROR: Cannot enter SiteResConfig directory!
    pause
    exit /b 1
)

echo Current directory: %CD%
echo.

REM Clean and compile
echo Cleaning previous build...
if exist "Makefile" del Makefile
mingw32-make clean > nul 2>&1

echo Generating Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo ERROR: qmake failed!
    pause
    exit /b 1
)

echo Compiling project...
mingw32-make -j4
if errorlevel 1 (
    echo ERROR: Compilation failed!
    echo Please check the error messages above.
    pause
    exit /b 1
)

echo.
echo SUCCESS: Compilation completed!
echo.
echo Starting application...
if exist "debug\SiteResConfig.exe" (
    start "" "debug\SiteResConfig.exe"
    echo Application started successfully
) else (
    echo ERROR: Executable not found at debug\SiteResConfig.exe
)

echo.
echo Test the import functionality with:
echo   File: C:\Users\<USER>\Desktop\20250818152156_shiyangongcheng.xlsx
echo.
pause
