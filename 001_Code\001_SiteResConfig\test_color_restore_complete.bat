@echo off
echo ========================================
echo  拖拽颜色完全恢复功能测试
echo ========================================

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o
if exist "ui_*.h" del ui_*.h

echo.
echo 生成UI头文件...
uic ui\MainWindow.ui -o ui_MainWindow.h
if errorlevel 1 (
    echo UI文件生成失败！
    pause
    exit /b 1
) else (
    echo ✅ UI头文件生成成功: ui_MainWindow.h
)

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo.
    echo 请检查以下可能的问题：
    echo 1. CustomTreeWidgets颜色恢复机制
    echo 2. 析构函数实现
    echo 3. 辅助方法定义
    echo 4. 头文件声明
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！拖拽颜色完全恢复功能已实现
    echo ========================================
    
    if exist "SiteResConfig.exe" (
        echo 可执行文件: SiteResConfig.exe
        echo.
        echo ✅ 拖拽颜色完全恢复功能已实现！
        echo.
        echo 🎨 颜色恢复增强功能:
        echo ├─ 完善的恢复机制: 确保所有情况下颜色都能正确恢复
        echo ├─ 辅助方法封装: 统一的颜色恢复逻辑
        echo ├─ 析构函数保护: 控件销毁时自动恢复颜色
        echo ├─ 公共强制恢复: 提供外部调用的恢复接口
        echo └─ 状态管理完善: 避免颜色状态残留
        echo.
        echo 🔧 增强的恢复机制:
        echo.
        echo 📝 拖拽源颜色恢复 (CustomHardwareTreeWidget):
        echo ├─ restoreDraggedItemColor(): 统一的恢复方法
        echo │  ├─ 检查拖拽项目: 验证m_draggedItem是否存在
        echo │  ├─ 恢复背景色: 使用保存的m_originalBackgroundColor
        echo │  ├─ 恢复文字色: 使用保存的m_originalTextColor
        echo │  └─ 清除状态: 重置m_draggedItem为nullptr
        echo ├─ startDrag()增强: 拖拽前后都调用恢复方法
        echo │  ├─ 拖拽前: 先恢复之前可能的颜色变化
        echo │  ├─ 拖拽中: 设置蓝色高亮显示
        echo │  └─ 拖拽后: 立即恢复原始颜色
        echo ├─ 析构函数保护: ~CustomHardwareTreeWidget()
        echo │  └─ 自动调用: restoreDraggedItemColor()
        echo └─ 公共强制恢复: forceRestoreAllColors()
        echo    └─ 外部调用: 强制恢复所有颜色变化
        echo.
        echo 📝 拖拽目标颜色恢复 (CustomTestConfigTreeWidget):
        echo ├─ restoreTargetItemColor(): 统一的恢复方法
        echo │  ├─ 检查高亮项目: 验证m_lastHighlightedItem是否存在
        echo │  ├─ 恢复背景色: 使用保存的m_originalTargetBackgroundColor
        echo │  ├─ 恢复文字色: 使用保存的m_originalTargetTextColor
        echo │  └─ 清除状态: 重置m_lastHighlightedItem为nullptr
        echo ├─ 事件处理增强: 所有拖拽事件都调用恢复方法
        echo │  ├─ dragMoveEvent(): 移动时恢复上次高亮
        echo │  ├─ dragLeaveEvent(): 离开时恢复当前高亮
        echo │  └─ dropEvent(): 完成时恢复当前高亮
        echo ├─ 析构函数保护: ~CustomTestConfigTreeWidget()
        echo │  └─ 自动调用: restoreTargetItemColor()
        echo └─ 公共强制恢复: forceRestoreAllColors()
        echo    └─ 外部调用: 强制恢复所有颜色变化
        echo.
        echo 🛡️ 多重保护机制:
        echo ├─ 正常流程保护: 拖拽完成时自动恢复
        echo ├─ 异常流程保护: 拖拽取消时自动恢复
        echo ├─ 控件销毁保护: 析构函数中强制恢复
        echo ├─ 外部调用保护: 提供公共恢复接口
        echo └─ 状态清理保护: 避免指针悬挂和状态残留
        echo.
        echo 📋 测试步骤:
        echo.
        echo 🎯 基本拖拽颜色恢复测试:
        echo 1. 创建测试数据:
        echo    - 右键"作动器" → 新建 → 作动器组 → 选择"50kN_作动器组"
        echo    - 右键"传感器" → 新建 → 传感器组 → 选择"载荷"
        echo 2. 测试正常拖拽流程:
        echo    - 拖拽作动器设备到"控制"节点
        echo    - 观察拖拽源变为蓝色，目标变为绿色
        echo    - 完成拖拽，观察所有颜色立即恢复
        echo 3. 验证颜色恢复完整性:
        echo    - 拖拽源节点恢复原始颜色
        echo    - 目标节点恢复原始颜色
        echo    - 无任何颜色残留
        echo.
        echo 🎯 拖拽取消颜色恢复测试:
        echo 1. 开始拖拽操作:
        echo    - 拖拽作动器设备，观察变为蓝色
        echo    - 移动到"控制"节点，观察变为绿色
        echo 2. 取消拖拽操作:
        echo    - 拖拽到不可接收的位置释放
        echo    - 或者按ESC键取消拖拽
        echo 3. 验证颜色恢复:
        echo    - 拖拽源节点应该恢复原色
        echo    - 目标节点应该恢复原色
        echo    - 无任何颜色残留
        echo.
        echo 🎯 拖拽离开颜色恢复测试:
        echo 1. 开始拖拽并移动:
        echo    - 拖拽作动器设备到"控制"节点（绿色高亮）
        echo    - 移动到"载荷1"节点（"控制"恢复，"载荷1"无高亮）
        echo    - 移动到其他位置
        echo 2. 验证动态恢复:
        echo    - 离开的节点立即恢复原色
        echo    - 可接收的节点正确高亮
        echo    - 不可接收的节点无高亮
        echo.
        echo 🎯 多次拖拽颜色恢复测试:
        echo 1. 连续进行多次拖拽:
        echo    - 拖拽作动器到"控制"节点
        echo    - 拖拽传感器到"载荷1"节点
        echo    - 拖拽传感器到"载荷2"节点
        echo 2. 验证每次恢复:
        echo    - 每次拖拽完成后颜色正确恢复
        echo    - 不会有累积的颜色变化
        echo    - 所有节点保持正确的原始颜色
        echo.
        echo 🔍 验证要点:
        echo ├─ ✅ 拖拽完成后所有节点颜色正确恢复
        echo ├─ ✅ 拖拽取消时所有节点颜色正确恢复
        echo ├─ ✅ 拖拽离开时目标节点颜色正确恢复
        echo ├─ ✅ 多次拖拽不会有颜色累积问题
        echo ├─ ✅ 异常情况下颜色也能正确恢复
        echo └─ ✅ 无任何颜色残留或状态异常
        echo.
        echo 启动程序测试拖拽颜色完全恢复功能...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
        echo 启动程序...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
        echo 启动程序...
        start release\SiteResConfig.exe
    ) else (
        echo 警告: 找不到可执行文件！
    )
)

echo.
echo 📖 拖拽颜色完全恢复功能详细测试指南:
echo.
echo 🎯 正常拖拽流程验证:
echo 1. 拖拽开始阶段:
echo    - 选中源节点，开始拖拽
echo    - 源节点应该变为浅蓝色背景+深蓝色文字
echo    - 验证颜色变化立即生效
echo.
echo 2. 拖拽移动阶段:
echo    - 移动到可接收目标节点
echo    - 目标节点应该变为浅绿色背景+深绿色文字
echo    - 移动到其他节点时，之前的高亮应该立即消失
echo.
echo 3. 拖拽完成阶段:
echo    - 在可接收目标上释放鼠标
echo    - 源节点应该立即恢复原始颜色
echo    - 目标节点应该立即恢复原始颜色
echo    - 关联信息正确显示在目标节点第二列
echo.
echo 🎯 异常情况验证:
echo 1. 拖拽取消测试:
echo    - 拖拽到不可接收位置释放
echo    - 按ESC键取消拖拽
echo    - 拖拽到控件外部释放
echo    - 验证所有情况下颜色都正确恢复
echo.
echo 2. 快速连续拖拽测试:
echo    - 快速进行多次拖拽操作
echo    - 验证不会有颜色状态混乱
echo    - 验证每次都能正确恢复
echo.
echo 🎯 边界情况验证:
echo 1. 控件状态变化:
echo    - 在拖拽过程中最小化/恢复窗口
echo    - 在拖拽过程中切换到其他应用
echo    - 验证颜色状态不会异常
echo.
echo 2. 内存管理验证:
echo    - 长时间使用拖拽功能
echo    - 验证没有内存泄漏
echo    - 验证颜色状态正确清理
echo.
echo 🔍 技术验证点:
echo ✓ restoreDraggedItemColor()正确工作
echo ✓ restoreTargetItemColor()正确工作
echo ✓ 析构函数正确清理颜色状态
echo ✓ 公共恢复接口正常工作
echo ✓ 多重保护机制有效
echo ✓ 状态管理完善无泄漏
echo.
pause
