@echo off
echo.
echo ========================================
echo Actuator1_1 UI Fix Test
echo ========================================
echo.

echo UI fixes applied:
echo.
echo 1. Changed typeSpinBox to typeCombo in UI file
echo 2. Cleared preset options from all ComboBoxes
echo 3. Added proper tooltips for all controls
echo 4. Code now dynamically populates ComboBox options
echo.

echo Fixed controls:
echo - typeCombo: Will show "单出杆" and "双出杆"
echo - polarityCombo: Will show "Positive", "Negative", "Both", "Unknown"
echo - measUnitCombo: Will show "m", "mm", "cm", "inch"
echo - outputSignalUnitCombo: Will show "m", "mm", "cm", "inch"
echo.

if exist "SiteResConfig\SiteResConfig_Simple.pro" (
    echo Found project file, testing compilation...
    echo.
    
    cd SiteResConfig
    
    echo Cleaning old files...
    if exist "Makefile" del /Q "Makefile" 2>nul
    if exist "debug" rmdir /S /Q "debug" 2>nul
    if exist "release" rmdir /S /Q "release" 2>nul
    if exist "ui_*.h" del /Q "ui_*.h" 2>nul
    
    echo.
    echo Running qmake...
    qmake SiteResConfig_Simple.pro -spec win32-g++ "CONFIG+=debug" 2>&1
    
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo qmake successful!
        echo.
        echo Starting compilation and linking...
        mingw32-make debug 2>&1
        
        if %ERRORLEVEL% EQU 0 (
            echo.
            echo *** COMPILATION SUCCESSFUL! ***
            echo.
            echo UI fix completed successfully!
            echo All ComboBox controls are now properly configured.
            echo.
            
            if exist "debug\SiteResConfig.exe" (
                echo Executable created successfully!
                echo.
                echo Fixed UI features:
                echo 1. typeCombo now exists and works properly
                echo 2. All ComboBoxes dynamically populated by code
                echo 3. Proper enum values used for data storage
                echo 4. Serial number validation with visual feedback
                echo 5. Preview shows human-readable text
                echo.
                
                set /p choice="Launch program to test fixed UI? (y/n): "
                if /i "%choice%"=="y" (
                    echo Launching program...
                    start "" "debug\SiteResConfig.exe"
                    echo.
                    echo Test the fixed UI:
                    echo [ ] Create actuator -^> Check all dropdowns work
                    echo [ ] Type dropdown shows 2 options
                    echo [ ] Polarity dropdown shows 4 options  
                    echo [ ] Unit dropdowns show 4 options each
                    echo [ ] Serial number validation works
                    echo [ ] Preview shows text not numbers
                    echo [ ] Save and edit work correctly
                )
            ) else (
                echo ERROR: Executable not found
            )
        ) else (
            echo.
            echo *** COMPILATION FAILED ***
            echo Please check the error messages above.
        )
    ) else (
        echo.
        echo *** QMAKE FAILED ***
        echo Please check Qt environment configuration.
    )
    
    cd ..
) else (
    echo ERROR: Project file not found
)

echo.
echo ========================================
echo UI Fix Test Completed
echo ========================================
echo.
pause
