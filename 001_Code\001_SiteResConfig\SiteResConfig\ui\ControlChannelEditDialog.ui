<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ControlChannelEditDialog</class>
 <widget class="QDialog" name="ControlChannelEditDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>600</width>
    <height>500</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>编辑控制通道配置</string>
  </property>
  <property name="modal">
   <bool>true</bool>
  </property>
  <layout class="QVBoxLayout" name="mainLayout">
   <item>
    <widget class="QTabWidget" name="tabWidget">
     <property name="currentIndex">
      <number>0</number>
     </property>
     
     <!-- 基本信息标签页 -->
     <widget class="QWidget" name="basicInfoTab">
      <attribute name="title">
       <string>基本信息</string>
      </attribute>
      <layout class="QFormLayout" name="basicInfoLayout">
       <item row="0" column="0">
        <widget class="QLabel" name="channelNameLabel">
         <property name="text">
          <string>通道名称:</string>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QLineEdit" name="channelNameEdit">
         <property name="placeholderText">
          <string>请输入通道名称</string>
         </property>
        </widget>
       </item>
       
       <item row="1" column="0">
        <widget class="QLabel" name="lcIdLabel">
         <property name="text">
          <string>下位机ID:</string>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="QSpinBox" name="lcIdSpinBox">
         <property name="minimum">
          <number>1</number>
         </property>
         <property name="maximum">
          <number>999</number>
         </property>
         <property name="value">
          <number>1</number>
         </property>
        </widget>
       </item>
       
       <item row="2" column="0">
        <widget class="QLabel" name="stationIdLabel">
         <property name="text">
          <string>站点ID:</string>
         </property>
        </widget>
       </item>
       <item row="2" column="1">
        <widget class="QSpinBox" name="stationIdSpinBox">
         <property name="minimum">
          <number>1</number>
         </property>
         <property name="maximum">
          <number>999</number>
         </property>
         <property name="value">
          <number>1</number>
         </property>
        </widget>
       </item>
       
       <item row="3" column="0">
        <widget class="QLabel" name="enableLabel">
         <property name="text">
          <string>使能状态:</string>
         </property>
        </widget>
       </item>
       <item row="3" column="1">
        <widget class="QCheckBox" name="enableCheckBox">
         <property name="text">
          <string>启用通道</string>
         </property>
         <property name="checked">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       
       <item row="4" column="0">
        <widget class="QLabel" name="controlModeLabel">
         <property name="text">
          <string>控制模式:</string>
         </property>
        </widget>
       </item>
       <item row="4" column="1">
        <widget class="QComboBox" name="controlModeCombo">
         <item>
          <property name="text">
           <string>位置控制</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>力控制</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>混合控制</string>
          </property>
         </item>
        </widget>
       </item>
      </layout>
     </widget>
     
     <!-- 关联配置标签页 -->
     <widget class="QWidget" name="associationTab">
      <attribute name="title">
       <string>关联配置</string>
      </attribute>
      <layout class="QFormLayout" name="associationLayout">
       <!-- 硬件关联 -->
       <item row="0" column="0">
        <widget class="QLabel" name="hardwareLabel">
         <property name="text">
          <string>硬件关联:</string>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <layout class="QHBoxLayout" name="hardwareLayout">
         <item>
          <widget class="QComboBox" name="hardwareGroupCombo">
           <property name="toolTip">
            <string>选择硬件节点组</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="hardwareChannelCombo">
           <property name="toolTip">
            <string>选择硬件通道</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       
       <!-- 载荷1传感器 -->
       <item row="1" column="0">
        <widget class="QLabel" name="load1SensorLabel">
         <property name="text">
          <string>载荷1传感器:</string>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <layout class="QHBoxLayout" name="load1SensorLayout">
         <item>
          <widget class="QComboBox" name="load1SensorGroupCombo">
           <property name="toolTip">
            <string>选择传感器组</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="load1SensorDeviceCombo">
           <property name="toolTip">
            <string>选择传感器设备</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       
       <!-- 载荷2传感器 -->
       <item row="2" column="0">
        <widget class="QLabel" name="load2SensorLabel">
         <property name="text">
          <string>载荷2传感器:</string>
         </property>
        </widget>
       </item>
       <item row="2" column="1">
        <layout class="QHBoxLayout" name="load2SensorLayout">
         <item>
          <widget class="QComboBox" name="load2SensorGroupCombo">
           <property name="toolTip">
            <string>选择传感器组</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="load2SensorDeviceCombo">
           <property name="toolTip">
            <string>选择传感器设备</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       
       <!-- 位置传感器 -->
       <item row="3" column="0">
        <widget class="QLabel" name="positionSensorLabel">
         <property name="text">
          <string>位置传感器:</string>
         </property>
        </widget>
       </item>
       <item row="3" column="1">
        <layout class="QHBoxLayout" name="positionSensorLayout">
         <item>
          <widget class="QComboBox" name="positionSensorGroupCombo">
           <property name="toolTip">
            <string>选择传感器组</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="positionSensorDeviceCombo">
           <property name="toolTip">
            <string>选择传感器设备</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       
       <!-- 控制作动器 -->
       <item row="4" column="0">
        <widget class="QLabel" name="controlActuatorLabel">
         <property name="text">
          <string>控制作动器:</string>
         </property>
        </widget>
       </item>
       <item row="4" column="1">
        <layout class="QHBoxLayout" name="controlActuatorLayout">
         <item>
          <widget class="QComboBox" name="controlActuatorGroupCombo">
           <property name="toolTip">
            <string>选择作动器组</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="controlActuatorDeviceCombo">
           <property name="toolTip">
            <string>选择作动器设备</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
     
     <!-- 极性配置标签页 -->
     <widget class="QWidget" name="polarityTab">
      <attribute name="title">
       <string>极性配置</string>
      </attribute>
      <layout class="QFormLayout" name="polarityLayout">
       <item row="0" column="0">
        <widget class="QLabel" name="servoControlPolarityLabel">
         <property name="text">
          <string>控制作动器极性:</string>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QComboBox" name="servoControlPolarityCombo">
         <item>
          <property name="text">
           <string>正极性 (+)</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>负极性 (-)</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>双极性 (±)</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>无极性</string>
          </property>
         </item>
        </widget>
       </item>
       
       <item row="1" column="0">
        <widget class="QLabel" name="payload1PolarityLabel">
         <property name="text">
          <string>载荷1传感器极性:</string>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="QComboBox" name="payload1PolarityCombo">
         <item>
          <property name="text">
           <string>正极性 (+)</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>负极性 (-)</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>双极性 (±)</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>无极性</string>
          </property>
         </item>
        </widget>
       </item>
       
       <item row="2" column="0">
        <widget class="QLabel" name="payload2PolarityLabel">
         <property name="text">
          <string>载荷2传感器极性:</string>
         </property>
        </widget>
       </item>
       <item row="2" column="1">
        <widget class="QComboBox" name="payload2PolarityCombo">
         <item>
          <property name="text">
           <string>正极性 (+)</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>负极性 (-)</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>双极性 (±)</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>无极性</string>
          </property>
         </item>
        </widget>
       </item>
       
       <item row="3" column="0">
        <widget class="QLabel" name="positionPolarityLabel">
         <property name="text">
          <string>位置传感器极性:</string>
         </property>
        </widget>
       </item>
       <item row="3" column="1">
        <widget class="QComboBox" name="positionPolarityCombo">
         <item>
          <property name="text">
           <string>正极性 (+)</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>负极性 (-)</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>双极性 (±)</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>无极性</string>
          </property>
         </item>
        </widget>
       </item>
      </layout>
     </widget>
     
     <!-- 备注信息标签页 -->
     <widget class="QWidget" name="notesTab">
      <attribute name="title">
       <string>备注信息</string>
      </attribute>
      <layout class="QVBoxLayout" name="notesLayout">
       <item>
        <widget class="QLabel" name="notesLabel">
         <property name="text">
          <string>备注信息:</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QTextEdit" name="notesEdit">
         <property name="placeholderText">
          <string>请输入备注信息...</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
   
   <!-- 按钮布局 -->
   <item>
    <layout class="QHBoxLayout" name="buttonLayout">
     <item>
      <spacer name="buttonSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="okButton">
       <property name="text">
        <string>确定</string>
       </property>
       <property name="default">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="cancelButton">
       <property name="text">
        <string>取消</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>cancelButton</sender>
   <signal>clicked()</signal>
   <receiver>ControlChannelEditDialog</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>369</x>
     <y>253</y>
    </hint>
    <hint type="destinationlabel">
     <x>179</x>
     <y>254</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui> 