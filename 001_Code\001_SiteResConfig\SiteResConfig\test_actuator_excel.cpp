/**
 * @file test_actuator_excel.cpp
 * @brief 作动器Excel导入导出测试程序
 * @details 创建示例作动器数据并测试Excel导入导出功能
 * <AUTHOR> Assistant
 * @date 2025-08-14
 * @version 1.0.0
 */

#include <QtCore/QCoreApplication>
#include <QtCore/QDebug>
#include <QtCore/QDir>
#include <QtCore/QDateTime>
#include <QtWidgets/QApplication>
#include <QtWidgets/QTreeWidget>

#include "XLSDataExporter.h"
#include "ActuatorDialog.h"

/**
 * @brief 创建示例作动器数据
 * @return 作动器组列表
 */
QList<UI::ActuatorGroup> createSampleActuatorData() {
    QList<UI::ActuatorGroup> actuatorGroups;
    
    // 创建第一个作动器组：主作动器组
    UI::ActuatorGroup group1;
    group1.groupId = 1;
    group1.groupName = u8"主作动器组";
    group1.groupType = u8"液压";
    group1.createTime = QDateTime::currentDateTime().toString("yyyy-MM-dd");
    group1.groupNotes = u8"主要控制作动器组";
    
    // 添加第一个作动器
    UI::ActuatorParams actuator1;
    actuator1.actuatorId = 1;
    actuator1.serialNumber = "ACT001";
    actuator1.type = u8"单出杆";
    actuator1.unitType = "m";
    actuator1.unitName = u8"米";
    actuator1.stroke = 0.15;
    actuator1.displacement = 0.05;
    actuator1.tensionArea = 0.0314;
    actuator1.compressionArea = 0.0254;
    actuator1.polarity = "Positive";
    actuator1.dither = 5.0;
    actuator1.frequency = 50.0;
    actuator1.outputMultiplier = 1.0;
    actuator1.balance = 2.5;
    actuator1.cylinderDiameter = 0.2;
    actuator1.rodDiameter = 0.1;
    actuator1.notes = u8"主控制作动器";
    
    // 添加第二个作动器
    UI::ActuatorParams actuator2;
    actuator2.actuatorId = 2;
    actuator2.serialNumber = "ACT002";
    actuator2.type = u8"单出杆";
    actuator2.unitType = "m";
    actuator2.unitName = u8"米";
    actuator2.stroke = 0.15;
    actuator2.displacement = 0.05;
    actuator2.tensionArea = 0.0314;
    actuator2.compressionArea = 0.0254;
    actuator2.polarity = "Positive";
    actuator2.dither = 5.0;
    actuator2.frequency = 50.0;
    actuator2.outputMultiplier = 1.0;
    actuator2.balance = 2.5;
    actuator2.cylinderDiameter = 0.2;
    actuator2.rodDiameter = 0.1;
    actuator2.notes = u8"备用作动器";
    
    group1.actuators.append(actuator1);
    group1.actuators.append(actuator2);
    
    // 创建第二个作动器组：辅助作动器组
    UI::ActuatorGroup group2;
    group2.groupId = 2;
    group2.groupName = u8"辅助作动器组";
    group2.groupType = u8"电动";
    group2.createTime = QDateTime::currentDateTime().toString("yyyy-MM-dd");
    group2.groupNotes = u8"辅助控制作动器组";
    
    // 添加第三个作动器
    UI::ActuatorParams actuator3;
    actuator3.actuatorId = 1;
    actuator3.serialNumber = "ACT003";
    actuator3.type = u8"双出杆";
    actuator3.unitType = "mm";
    actuator3.unitName = u8"毫米";
    actuator3.stroke = 100.0;
    actuator3.displacement = 30.0;
    actuator3.tensionArea = 0.0201;
    actuator3.compressionArea = 0.0154;
    actuator3.polarity = "Negative";
    actuator3.dither = 3.5;
    actuator3.frequency = 25.0;
    actuator3.outputMultiplier = 0.8;
    actuator3.balance = 1.8;
    actuator3.cylinderDiameter = 0.16;
    actuator3.rodDiameter = 0.08;
    actuator3.notes = u8"辅助控制";
    
    // 添加第四个作动器
    UI::ActuatorParams actuator4;
    actuator4.actuatorId = 2;
    actuator4.serialNumber = "ACT004";
    actuator4.type = u8"单出杆";
    actuator4.unitType = "cm";
    actuator4.unitName = u8"厘米";
    actuator4.stroke = 20.0;
    actuator4.displacement = 8.0;
    actuator4.tensionArea = 0.0283;
    actuator4.compressionArea = 0.0226;
    actuator4.polarity = "Positive";
    actuator4.dither = 4.2;
    actuator4.frequency = 40.0;
    actuator4.outputMultiplier = 1.2;
    actuator4.balance = 2.1;
    actuator4.cylinderDiameter = 0.19;
    actuator4.rodDiameter = 0.09;
    actuator4.notes = u8"精密控制";
    
    group2.actuators.append(actuator3);
    group2.actuators.append(actuator4);
    
    actuatorGroups.append(group1);
    actuatorGroups.append(group2);
    
    return actuatorGroups;
}

/**
 * @brief 测试作动器Excel导出功能
 * @param actuatorGroups 作动器组列表
 * @return 测试是否成功
 */
bool testActuatorExport(const QList<UI::ActuatorGroup>& actuatorGroups) {
    qDebug() << u8"🔧 测试作动器Excel导出功能...";
    
    XLSDataExporter exporter;
    QString filePath = "test_actuator_data.xlsx";
    
    // 测试1: 单独创建作动器工作表
    bool success = exporter.addActuatorWorksheetToExcel(filePath, actuatorGroups);
    if (!success) {
        qDebug() << u8"❌ 作动器工作表创建失败:" << exporter.getLastError();
        return false;
    }
    
    qDebug() << u8"✅ 作动器工作表创建成功:" << filePath;
    return true;
}

/**
 * @brief 测试作动器Excel导入功能
 * @param filePath Excel文件路径
 * @return 测试是否成功
 */
bool testActuatorImport(const QString& filePath) {
    qDebug() << u8"🔍 测试作动器Excel导入功能...";
    
    XLSDataExporter exporter;
    
    // 从Excel文件读取作动器数据
    QList<UI::ActuatorGroup> importedGroups = exporter.readActuatorGroupsFromExcel(filePath);
    
    if (importedGroups.isEmpty()) {
        qDebug() << u8"❌ 作动器数据导入失败:" << exporter.getLastError();
        return false;
    }
    
    qDebug() << u8"✅ 作动器数据导入成功，共" << importedGroups.size() << u8"个作动器组";
    
    // 验证导入的数据
    for (const auto& group : importedGroups) {
        qDebug() << u8"  组" << group.groupId << u8":" << group.groupName 
                 << u8"，包含" << group.actuators.size() << u8"个作动器";
        
        for (const auto& actuator : group.actuators) {
            qDebug() << u8"    - 序列号:" << actuator.serialNumber 
                     << u8"，类型:" << actuator.type
                     << u8"，Unit:" << actuator.unitType << "(" << actuator.unitName << ")";
        }
    }
    
    return true;
}

/**
 * @brief 测试完整项目导出（硬件树 + 作动器）
 * @param actuatorGroups 作动器组列表
 * @return 测试是否成功
 */
bool testCompleteProjectExport(const QList<UI::ActuatorGroup>& actuatorGroups) {
    qDebug() << u8"📊 测试完整项目导出功能...";
    
    // 创建一个简单的硬件树
    QTreeWidget treeWidget;
    QTreeWidgetItem* rootItem = new QTreeWidgetItem(&treeWidget);
    rootItem->setText(0, u8"测试项目");
    rootItem->setText(1, u8"项目");
    
    QTreeWidgetItem* nodeItem = new QTreeWidgetItem(rootItem);
    nodeItem->setText(0, u8"节点1");
    nodeItem->setText(1, u8"节点");
    
    XLSDataExporter exporter;
    QString filePath = "complete_project_with_actuators.xlsx";
    
    bool success = exporter.exportCompleteProjectWithActuators(&treeWidget, actuatorGroups, filePath);
    if (!success) {
        qDebug() << u8"❌ 完整项目导出失败:" << exporter.getLastError();
        return false;
    }
    
    qDebug() << u8"✅ 完整项目导出成功:" << filePath;
    return true;
}

int main(int argc, char *argv[]) {
    QApplication app(argc, argv);
    
    qDebug() << u8"========================================";
    qDebug() << u8"🚀 作动器Excel功能测试程序";
    qDebug() << u8"========================================";
    
    // 1. 创建示例作动器数据
    qDebug() << u8"📋 创建示例作动器数据...";
    QList<UI::ActuatorGroup> actuatorGroups = createSampleActuatorData();
    qDebug() << u8"✅ 创建了" << actuatorGroups.size() << u8"个作动器组";
    
    // 2. 测试作动器导出功能
    if (!testActuatorExport(actuatorGroups)) {
        qDebug() << u8"❌ 作动器导出测试失败";
        return 1;
    }
    
    // 3. 测试作动器导入功能
    if (!testActuatorImport("test_actuator_data.xlsx")) {
        qDebug() << u8"❌ 作动器导入测试失败";
        return 1;
    }
    
    // 4. 测试完整项目导出功能
    if (!testCompleteProjectExport(actuatorGroups)) {
        qDebug() << u8"❌ 完整项目导出测试失败";
        return 1;
    }
    
    qDebug() << u8"========================================";
    qDebug() << u8"🎉 所有测试通过！";
    qDebug() << u8"========================================";
    
    qDebug() << u8"📁 生成的文件:";
    qDebug() << u8"  - test_actuator_data.xlsx (作动器工作表)";
    qDebug() << u8"  - complete_project_with_actuators.xlsx (完整项目)";
    
    return 0;
}
