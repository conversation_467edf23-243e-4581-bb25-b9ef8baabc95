@echo off
chcp 65001 >nul
echo ========================================
echo  传感器界面英文翻译验证
echo ========================================
echo.

echo 🔍 检查英文翻译修改...
echo.

REM 检查传感器类型翻译
echo [1/4] 检查传感器类型翻译...
findstr /C:"称重传感器" SiteResConfig\src\SensorDialog.cpp >nul
if errorlevel 1 (
    echo ❌ 未找到"称重传感器"翻译
) else (
    echo ✅ Load Cell → 称重传感器 翻译完成
)

findstr /C:"位移传感器" SiteResConfig\src\SensorDialog.cpp >nul
if errorlevel 1 (
    echo ❌ 未找到"位移传感器"翻译
) else (
    echo ✅ Displacement Transducer → 位移传感器 翻译完成
)

findstr /C:"热电偶" SiteResConfig\src\SensorDialog.cpp >nul
if errorlevel 1 (
    echo ❌ 未找到"热电偶"翻译
) else (
    echo ✅ Thermocouple → 热电偶 翻译完成
)

echo.

REM 检查单位类型翻译
echo [2/4] 检查单位类型翻译...
findstr /C:"tr(\"力\")" SiteResConfig\src\SensorDialog.cpp >nul
if errorlevel 1 (
    echo ❌ 未找到"力"单位翻译
) else (
    echo ✅ Force → 力 翻译完成
)

findstr /C:"tr(\"位移\")" SiteResConfig\src\SensorDialog.cpp >nul
if errorlevel 1 (
    echo ❌ 未找到"位移"单位翻译
) else (
    echo ✅ Displacement → 位移 翻译完成
)

findstr /C:"tr(\"压力\")" SiteResConfig\src\SensorDialog.cpp >nul
if errorlevel 1 (
    echo ❌ 未找到"压力"单位翻译
) else (
    echo ✅ Pressure → 压力 翻译完成
)

echo.

REM 检查智能配置翻译
echo [3/4] 检查智能配置翻译...
findstr /C:"setCurrentText(tr(\"称重传感器\"))" SiteResConfig\src\SensorDialog.cpp >nul
if errorlevel 1 (
    echo ❌ 智能配置中未找到称重传感器设置
) else (
    echo ✅ 智能配置翻译更新完成
)

echo.

REM 检查测试程序翻译
echo [4/4] 检查测试程序翻译...
if exist "test_sensor_dialog_ui.cpp" (
    findstr /C:"称重传感器" test_sensor_dialog_ui.cpp >nul
    if errorlevel 1 (
        echo ❌ 测试程序中未找到翻译
    ) else (
        echo ✅ 测试程序翻译更新完成
    )
) else (
    echo ⚠️  测试程序文件未找到
)

echo.
echo ========================================
echo  ✅ 英文翻译验证完成！
echo ========================================
echo.
echo 📋 翻译摘要:
echo   • 传感器类型: Load Cell → 称重传感器
echo   • 传感器类型: Displacement Transducer → 位移传感器  
echo   • 传感器类型: Pressure Cell → 压力传感器
echo   • 传感器类型: Thermocouple → 热电偶
echo   • 单位类型: Force → 力
echo   • 单位类型: Displacement → 位移
echo   • 单位类型: Pressure → 压力
echo   • 单位类型: Temperature → 温度
echo.
echo 🎯 翻译特点:
echo   • 使用标准的中文专业术语
echo   • 保持技术准确性和专业性
echo   • 提高界面本土化程度
echo   • 改善中文用户体验
echo.
echo 🚀 测试建议:
echo   1. 编译项目查看翻译效果
echo   2. 测试传感器类型选择功能
echo   3. 验证智能配置是否正常工作
echo   4. 检查单位类型切换功能
echo.
echo 📖 详细信息: 传感器界面英文翻译完成报告.md
echo.
pause
