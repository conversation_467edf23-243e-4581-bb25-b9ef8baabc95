@echo off
echo ========================================
echo  SiteResConfig Qt Build Script
echo ========================================

REM 设置Qt环境变量（请根据您的Qt安装路径修改）
set QTDIR=C:\Qt\5.14.2\msvc2017_64
set PATH=%QTDIR%\bin;%PATH%

REM 检查Qt是否可用
echo 检查Qt环境...
qmake --version
if errorlevel 1 (
    echo 错误: 找不到Qt环境！
    echo 请确保：
    echo 1. Qt 5.14.2 已正确安装
    echo 2. 修改此脚本中的QTDIR路径
    echo 3. 或者设置系统环境变量QTDIR
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

REM 创建构建目录
if not exist "build" mkdir build
cd build

echo.
echo 生成Makefile...
qmake ..\SiteResConfig.pro -spec win32-msvc
if errorlevel 1 (
    echo 错误: qmake失败！
    cd ..
    pause
    exit /b 1
)

echo.
echo 编译项目...
nmake
if errorlevel 1 (
    echo 错误: 编译失败！
    cd ..
    pause
    exit /b 1
)

echo.
echo ========================================
echo  编译成功！
echo ========================================
echo 可执行文件位置: build\release\SiteResConfig.exe
echo.

REM 运行程序
echo 启动程序...
if exist "release\SiteResConfig.exe" (
    release\SiteResConfig.exe
) else if exist "debug\SiteResConfig.exe" (
    debug\SiteResConfig.exe
) else (
    echo 警告: 找不到可执行文件！
)

cd ..
pause
