#include "MainWindowHelper.h"
#include "MainWindow_Qt_Simple.h"
#include "LogManager.h"
#include "DeviceManager.h"
#include "ui_MainWindow.h"

#include <QMessageBox>
#include <QFileDialog>
#include <QProgressDialog>
#include <QApplication>
#include <QStandardPaths>
#include <QTreeWidget>
#include <QStatusBar>

MainWindowHelper::MainWindowHelper(CMyMainWindow* mainWindow, QObject* parent)
    : QObject(parent)
    , mainWindow_(mainWindow)
    , logManager_(nullptr)
    , deviceManager_(nullptr)
{
    if (mainWindow_) {
        // 获取管理器引用 - 这些是公共方法
        logManager_ = mainWindow_->getLogManager();
        deviceManager_ = mainWindow_->getDeviceManager();
        
        connectManagerSignals();
        setupValidationRules();
    }
}

MainWindowHelper::~MainWindowHelper() {
    // 清理资源
}

void MainWindowHelper::updateProjectTitle(const QString& projectName) {
    if (!mainWindow_) return;
    
    QString title = "SiteResConfig";
    if (!projectName.isEmpty()) {
        title += QString(" - %1").arg(projectName);
    }
    
    mainWindow_->setWindowTitle(title);
    logOperation("更新项目标题", true, projectName);
}

void MainWindowHelper::updateTreeWidgetTooltips() {
    if (!mainWindow_) return;
    
    auto updateTreeTooltips = [](QTreeWidget* tree) {
        if (!tree) return;
        
        for (int i = 0; i < tree->topLevelItemCount(); ++i) {
            QTreeWidgetItem* item = tree->topLevelItem(i);
            if (item) {
                QString tooltip = QString("节点: %1").arg(item->text(0));
                item->setToolTip(0, tooltip);
                
                // 递归更新子节点
                std::function<void(QTreeWidgetItem*)> updateChildren = [&](QTreeWidgetItem* parent) {
                    for (int j = 0; j < parent->childCount(); ++j) {
                        QTreeWidgetItem* child = parent->child(j);
                        if (child) {
                            QString childTooltip = QString("子节点: %1").arg(child->text(0));
                            child->setToolTip(0, childTooltip);
                            updateChildren(child);
                        }
                    }
                };
                updateChildren(item);
            }
        }
    };
    
    // 通过公共方法获取树形控件，而不是直接访问私有ui成员
    // 需要在MainWindow中添加公共的getter方法
    // updateTreeTooltips(mainWindow_->getHardwareTreeWidget());
    // updateTreeTooltips(mainWindow_->getTestConfigTreeWidget());
    
    // 临时注释掉这些调用，直到添加公共getter方法
    // updateTreeTooltips(mainWindow_->ui->hardwareTreeWidget);
    // updateTreeTooltips(mainWindow_->ui->testConfigTreeWidget);
    
    logOperation("更新树形控件提示", true);
}

void MainWindowHelper::updateStatusDisplay(const QString& message, int timeout) {
    if (!mainWindow_) return;
    
    // 更新状态栏
    if (mainWindow_->statusBar()) {
        mainWindow_->statusBar()->showMessage(message, timeout);
    }
    
    // 记录到日志
    if (logManager_) {
        logManager_->info(message);
    }
}

bool MainWindowHelper::validateProjectData() {
    validationErrors_.clear();
    
    if (!mainWindow_) {
        validationErrors_ << "主窗口未初始化";
        return false;
    }
    
    // 验证数据管理器
    if (!validateManagerStates()) {
        return false;
    }
    
    // 验证树形控件数据
    if (!validateTreeData()) {
        return false;
    }
    
    emit validationCompleted(validationErrors_.isEmpty(), validationErrors_);
    return validationErrors_.isEmpty();
}

bool MainWindowHelper::validateTreeData() {
    if (!mainWindow_) return false;
    
    bool valid = true;
    
    // 检查硬件树
    QTreeWidget* hardwareTree = mainWindow_->getHardwareTreeWidget();
    if (!hardwareTree) {
        validationErrors_ << "硬件树控件未初始化";
        valid = false;
    } else if (hardwareTree->topLevelItemCount() == 0) {
        validationErrors_ << "硬件树数据为空";
        valid = false;
    }
    
    // 检查测试配置树
    CustomTestConfigTreeWidget* testConfigTree = mainWindow_->getTestConfigTreeWidget();
    if (!testConfigTree) {
        validationErrors_ << "测试配置树控件未初始化";
        valid = false;
    } else if (testConfigTree->topLevelItemCount() == 0) {
        validationErrors_ << "测试配置树数据为空";
        valid = false;
    }
    
    return valid;
}

bool MainWindowHelper::validateManagerStates() {
    bool valid = true;
    
    if (!logManager_) {
        validationErrors_ << "日志管理器未初始化";
        valid = false;
    }
    
    if (!deviceManager_) {
        validationErrors_ << "设备管理器未初始化";
        valid = false;
    }
    
    // 检查主窗口的数据管理器
    if (mainWindow_) {
        if (!mainWindow_->getSensorDataManager()) {
            validationErrors_ << "传感器数据管理器未初始化";
            valid = false;
        }
        
        if (!mainWindow_->getActuatorViewModel()) {
            validationErrors_ << "作动器视图模型未初始化";
            valid = false;
        }
    }
    
    return valid;
}

QStringList MainWindowHelper::getValidationErrors() const {
    return validationErrors_;
}

QString MainWindowHelper::selectProjectFile(bool forSave) {
    if (!mainWindow_) return QString();
    
    QString filter = tr("项目文件 (*.json);;所有文件 (*.*)");
    QString defaultDir = QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation);
    
    QString filePath;
    if (forSave) {
        filePath = QFileDialog::getSaveFileName(
            mainWindow_, 
            tr("保存项目文件"), 
            defaultDir, 
            filter
        );
    } else {
        filePath = QFileDialog::getOpenFileName(
            mainWindow_, 
            tr("打开项目文件"), 
            defaultDir, 
            filter
        );
    }
    
    return filePath;
}

QString MainWindowHelper::selectExportFile(const QString& defaultName, const QString& filter) {
    if (!mainWindow_) return QString();
    
    QString defaultDir = QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation);
    QString defaultPath = defaultDir + "/" + defaultName;
    
    return QFileDialog::getSaveFileName(
        mainWindow_, 
        tr("导出文件"), 
        defaultPath, 
        filter
    );
}

bool MainWindowHelper::confirmDataLoss() {
    if (!mainWindow_) return false;
    
    int ret = QMessageBox::question(
        mainWindow_,
        tr("确认操作"),
        tr("当前项目有未保存的更改，是否继续？\n未保存的更改将丢失。"),
        QMessageBox::Yes | QMessageBox::No,
        QMessageBox::No
    );
    
    return ret == QMessageBox::Yes;
}

void MainWindowHelper::expandAllTreeNodes() {
    if (!mainWindow_) return;
    
    QTreeWidget* hardwareTree = mainWindow_->getHardwareTreeWidget();
    if (hardwareTree) {
        hardwareTree->expandAll();
    }
    
    CustomTestConfigTreeWidget* testConfigTree = mainWindow_->getTestConfigTreeWidget();
    if (testConfigTree) {
        testConfigTree->expandAll();
    }
    
    logOperation("展开所有树节点", true);
}

void MainWindowHelper::collapseAllTreeNodes() {
    if (!mainWindow_) return;
    
    QTreeWidget* hardwareTree = mainWindow_->getHardwareTreeWidget();
    if (hardwareTree) {
        hardwareTree->collapseAll();
    }
    
    CustomTestConfigTreeWidget* testConfigTree = mainWindow_->getTestConfigTreeWidget();
    if (testConfigTree) {
        testConfigTree->collapseAll();
    }
    
    logOperation("折叠所有树节点", true);
}

void MainWindowHelper::refreshTreeFromManagers() {
    if (!mainWindow_) return;
    
    // 调用主窗口的刷新方法
    mainWindow_->RefreshHardwareTreeFromDataManagers();
    mainWindow_->RefreshTestConfigTreeFromDataManagers();
    
    logOperation("从数据管理器刷新树形控件", true);
}

QTreeWidgetItem* MainWindowHelper::findTreeItem(const QString& itemName, const QString& treeType) {
    if (!mainWindow_) return nullptr;
    
    QTreeWidget* tree = nullptr;
    if (treeType == "hardware") {
        tree = mainWindow_->getHardwareTreeWidget();
    } else if (treeType == "testConfig") {
        tree = mainWindow_->getTestConfigTreeWidget();
    }
    
    if (!tree) return nullptr;
    
    // 递归查找项目
    std::function<QTreeWidgetItem*(QTreeWidgetItem*)> findInItem = 
        [&](QTreeWidgetItem* parent) -> QTreeWidgetItem* {
        if (parent && parent->text(0) == itemName) {
            return parent;
        }
        
        if (parent) {
            for (int i = 0; i < parent->childCount(); ++i) {
                QTreeWidgetItem* found = findInItem(parent->child(i));
                if (found) return found;
            }
        }
        
        return nullptr;
    };
    
    // 搜索顶级项目
    for (int i = 0; i < tree->topLevelItemCount(); ++i) {
        QTreeWidgetItem* found = findInItem(tree->topLevelItem(i));
        if (found) return found;
    }
    
    return nullptr;
}

void MainWindowHelper::logOperation(const QString& operation, bool success, const QString& details) {
    if (!logManager_) return;
    
    QString message = QString("%1 %2").arg(success ? "✅" : "❌").arg(operation);
    if (!details.isEmpty()) {
        message += QString(" - %1").arg(details);
    }
    
    if (success) {
        logManager_->info(message);
    } else {
        logManager_->error(message);
    }
}

void MainWindowHelper::showErrorDialog(const QString& title, const QString& message) {
    if (!mainWindow_) return;
    
    QMessageBox::critical(mainWindow_, title, message);
    logOperation(title, false, message);
}

void MainWindowHelper::showSuccessMessage(const QString& message) {
    if (!mainWindow_) return;
    
    updateStatusDisplay(message, 3000);
    logOperation("成功消息", true, message);
}

void MainWindowHelper::updateOperationState(bool hasProject) {
    if (!mainWindow_) return;
    
    // 更新主窗口的操作状态
    mainWindow_->updateOperationAreaState(hasProject);
    
    QString state = hasProject ? "启用" : "禁用";
    logOperation("更新操作区域状态", true, state);
}

void MainWindowHelper::saveCurrentState() {
    // 保存当前状态到配置文件或临时存储
    logOperation("保存当前状态", true);
}

void MainWindowHelper::restoreState() {
    // 从配置文件或临时存储恢复状态
    logOperation("恢复状态", true);
}

void MainWindowHelper::onManagerError(const QString& error) {
    validationErrors_ << error;
    showErrorDialog("管理器错误", error);
}

void MainWindowHelper::onValidationRequested() {
    validateProjectData();
}

void MainWindowHelper::connectManagerSignals() {
    // 连接管理器信号
    if (logManager_) {
        connect(logManager_, &LogManager::logAdded, 
                this, [this](const QString& level, const QString& message) {
            emit statusChanged(message);
        });
    }
    
    if (deviceManager_) {
        connect(deviceManager_, &DeviceManager::deviceError,
                this, &MainWindowHelper::onManagerError);
        connect(deviceManager_, &DeviceManager::deviceStatusChanged,
                this, [this](const QString& status) {
            if (mainWindow_) {
                mainWindow_->AddLogEntry("INFO", status);
            }
        });
        connect(deviceManager_, &DeviceManager::deviceGroupEdited,
                this, [this](const QString& type, const QString& groupName) {
            if (mainWindow_) {
                mainWindow_->AddLogEntry("SUCCESS", 
                    QString("✅ %1组编辑成功，详细信息界面将自动更新（编辑操作）").arg(type));
            }
        });
    }
}

void MainWindowHelper::setupValidationRules() {
    // 设置验证规则
    validationErrors_.clear();
}

QString MainWindowHelper::formatErrorMessage(const QString& error) {
    return QString("❌ %1").arg(error);
}

void MainWindowHelper::updateUIState() {
    // 更新UI状态
    updateTreeWidgetTooltips();
} 