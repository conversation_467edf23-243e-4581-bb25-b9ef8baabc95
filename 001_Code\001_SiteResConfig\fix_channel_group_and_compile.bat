@echo off
echo ========================================
echo Control Channel Group Save Fix
echo ========================================
echo.

echo [INFO] Applied fixes:
echo   1. Fixed deadlock in CtrlChanDataManager::clearAllData()
echo   2. Fixed Qt 5.14 compatibility issues
echo   3. Fixed control channel group validation:
echo      - Added missing channelId for each channel
echo      - Added missing groupType for channel group
echo      - Enhanced debug logging
echo.

REM Find Qt installation
set QT_FOUND=0

if exist "C:\Qt\5.14.2\mingw73_32\bin\qmake.exe" (
    set QTDIR=C:\Qt\5.14.2\mingw73_32
    set MINGW_PATH=C:\Qt\Tools\mingw730_32\bin
    set QT_FOUND=1
    echo Found Qt at C:\Qt\5.14.2\mingw73_32
)

if %QT_FOUND%==0 (
    if exist "C:\Qt\Qt5.14.2\5.14.2\mingw73_32\bin\qmake.exe" (
        set QTDIR=C:\Qt\Qt5.14.2\5.14.2\mingw73_32
        set MINGW_PATH=C:\Qt\Qt5.14.2\Tools\mingw730_32\bin
        set QT_FOUND=1
        echo Found Qt at C:\Qt\Qt5.14.2\5.14.2\mingw73_32
    )
)

if %QT_FOUND%==0 (
    echo ERROR: Qt not found! Please set paths manually.
    pause
    exit /b 1
)

set PATH=%QTDIR%\bin;%MINGW_PATH%;%PATH%
echo Qt environment set: %QTDIR%
echo.

cd /d "%~dp0\SiteResConfig"

echo Cleaning and compiling...
mingw32-make clean > nul 2>&1
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo ERROR: qmake failed!
    pause
    exit /b 1
)

mingw32-make -j4
if errorlevel 1 (
    echo ERROR: Compilation failed!
    pause
    exit /b 1
)

echo.
echo SUCCESS: Control channel group fix compiled successfully!
echo.
echo [VALIDATION FIXES]
echo   - channelId: Auto-generated as CH_001, CH_002, etc.
echo   - groupType: Set to "控制通道"
echo   - groupNotes: Added descriptive notes
echo   - Enhanced debug output for troubleshooting
echo.
echo Starting application...
if exist "debug\SiteResConfig.exe" (
    start "" "debug\SiteResConfig.exe"
    echo Application started - control channel group save should work now
) else (
    echo ERROR: Executable not found
)

echo.
echo [TEST INSTRUCTIONS]
echo 1. Import: C:\Users\<USER>\Desktop\20250818152156_shiyangongcheng.xlsx
echo 2. Check console output for detailed channel group creation logs
echo 3. Verify import completes without "保存控制通道组失败" error
echo 4. Check import completion dialog shows correct channel count
echo.
pause
