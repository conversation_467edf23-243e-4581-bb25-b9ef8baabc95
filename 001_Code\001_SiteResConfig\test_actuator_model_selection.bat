@echo off
echo ========================================
echo  作动器型号选择功能测试
echo ========================================

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o
if exist "ui_*.h" del ui_*.h

echo.
echo 生成UI头文件...
uic ui\MainWindow.ui -o ui_MainWindow.h
if errorlevel 1 (
    echo UI文件生成失败！
    pause
    exit /b 1
) else (
    echo ✅ UI头文件生成成功: ui_MainWindow.h
)

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo.
    echo 新增功能：
    echo ✅ 作动器型号选择下拉框
    echo ✅ 型号信息集成到工具提示
    echo ✅ 型号信息记录到日志
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  🎉 编译成功！作动器型号选择功能已实现
    echo ========================================
    
    if exist "SiteResConfig.exe" (
        echo 可执行文件: SiteResConfig.exe
        echo.
        echo ✅ 作动器型号选择功能已实现！
        echo.
        echo 📋 新增的型号选择功能:
        echo.
        echo 🎯 型号下拉框选项:
        echo ├─ 494.16 25VD Output
        echo ├─ 494.16 35VD Output
        echo ├─ 494.20 25VD Output
        echo ├─ 494.20 35VD Output
        echo ├─ 494.25 25VD Output
        echo ├─ 494.25 35VD Output
        echo ├─ 494.30 25VD Output
        echo ├─ 494.30 35VD Output
        echo └─ 自定义型号
        echo.
        echo 🎨 更新后的参数输入界面:
        echo ┌─────────────────────────────────────────────────────┐
        echo │                新建作动器                            │
        echo ├─────────────────────────────────────────────────────┤
        echo │ 作动器组（选择要添加作动器的作动器组名称）：          │
        echo │           100kN_作动器\作动器_000001                │
        echo ├─────────────────────────────────────────────────────┤
        echo │ 序列号: [作动器_000001                    ]         │
        echo │ 类型:   [单出杆 ▼]                                 │
        echo │ 型号:   [494.16 25VD Output ▼]                     │
        echo │ 缸径:   [0.10] m                                   │
        echo │ 杆径:   [0.05] m                                   │
        echo │ 行程:   [0.20] m                                   │
        echo │                                                     │
        echo │                    [确定] [取消]                    │
        echo └─────────────────────────────────────────────────────┘
        echo.
        echo 🔧 型号选择功能特色:
        echo.
        echo ✅ 预定义型号:
        echo - 494.16系列: 25VD和35VD输出版本
        echo - 494.20系列: 25VD和35VD输出版本
        echo - 494.25系列: 25VD和35VD输出版本
        echo - 494.30系列: 25VD和35VD输出版本
        echo.
        echo ✅ 自定义支持:
        echo - "自定义型号"选项
        echo - 用户可以输入特殊型号
        echo - 支持非标准型号规格
        echo.
        echo ✅ 信息集成:
        echo - 型号信息显示在工具提示中
        echo - 型号信息记录到系统日志
        echo - 完整的设备参数追踪
        echo.
        echo 💡 工具提示信息更新:
        echo 鼠标悬停在作动器节点上显示完整参数:
        echo - 序列号: 作动器_000001
        echo - 类型: 单出杆
        echo - 型号: 494.16 25VD Output
        echo - 缸径: 0.10 m
        echo - 杆径: 0.05 m
        echo - 行程: 0.20 m
        echo.
        echo 📝 日志记录更新:
        echo 系统自动记录完整的作动器创建信息:
        echo "创建作动器设备: 作动器_000001, 类型: 单出杆, 型号: 494.16 25VD Output, 缸径: 0.10m, 杆径: 0.05m, 行程: 0.20m"
        echo.
        echo 🎯 型号系列说明:
        echo.
        echo 494.16系列:
        echo - 25VD Output: 25V直流输出版本
        echo - 35VD Output: 35V直流输出版本
        echo.
        echo 494.20系列:
        echo - 25VD Output: 25V直流输出版本
        echo - 35VD Output: 35V直流输出版本
        echo.
        echo 494.25系列:
        echo - 25VD Output: 25V直流输出版本
        echo - 35VD Output: 35V直流输出版本
        echo.
        echo 494.30系列:
        echo - 25VD Output: 25V直流输出版本
        echo - 35VD Output: 35V直流输出版本
        echo.
        echo 🔧 技术实现:
        echo - QComboBox型号选择控件
        echo - 预定义型号列表
        echo - 型号信息传递到CreateActuatorDevice方法
        echo - 工具提示和日志信息更新
        echo - 对话框尺寸调整为520x420像素
        echo.
        echo 启动程序测试作动器型号选择功能...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
        echo 启动程序...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
        echo 启动程序...
        start release\SiteResConfig.exe
    ) else (
        echo 警告: 找不到可执行文件！
    )
)

echo.
echo 📖 作动器型号选择功能测试指南:
echo.
echo 🎯 完整测试流程:
echo.
echo 1️⃣ 创建作动器组:
echo   - 右键"作动器"节点
echo   - 选择"新建" → "作动器组"
echo   - 选择"100kN_作动器"
echo   - 验证组创建成功
echo.
echo 2️⃣ 创建作动器设备:
echo   - 右键"100kN_作动器"组
echo   - 选择"新建" → "作动器"
echo   - 验证显示信息正确
echo   - 验证型号下拉框显示所有选项
echo.
echo 3️⃣ 测试型号选择:
echo   - 选择"494.16 25VD Output"
echo   - 填写其他参数
echo   - 点击确定创建
echo   - 验证作动器创建成功
echo.
echo 4️⃣ 验证信息显示:
echo   - 鼠标悬停在创建的作动器上
echo   - 验证工具提示显示型号信息
echo   - 检查系统日志记录
echo   - 验证型号信息正确记录
echo.
echo 5️⃣ 测试不同型号:
echo   - 创建多个作动器
echo   - 选择不同的型号
echo   - 验证每个型号都正确显示和记录
echo.
echo 🔍 验证要点:
echo - 型号下拉框正确显示所有选项
echo - 选择的型号正确传递到创建方法
echo - 工具提示包含完整的型号信息
echo - 日志记录包含型号信息
echo - 对话框尺寸适当，布局美观
echo - 所有参数输入功能正常
echo.
pause
