@echo off
echo ========================================
echo  验证校准日期格式修复
echo ========================================
echo.

echo 检查校准日期格式是否已修复为 yyyy/MM/dd HH:mm:ss.z...
echo.

echo 1. 检查 SensorDialog.cpp 中的日期格式...
findstr /n "toString.*yyyy/MM/dd" "SiteResConfig\src\SensorDialog.cpp"
if errorlevel 1 (
    echo ❌ 错误: 未找到日期格式设置
    goto :error
) else (
    echo ✅ 找到日期格式设置
)
echo.

echo 2. 检查是否使用24小时制 (HH)...
findstr /n "HH:mm:ss" "SiteResConfig\src\SensorDialog.cpp"
if errorlevel 1 (
    echo ❌ 错误: 未使用24小时制格式 (HH)
    goto :error
) else (
    echo ✅ 成功: 已使用24小时制格式 (HH)
)
echo.

echo 3. 检查是否使用时区格式 (.z)...
findstr /n "\.z" "SiteResConfig\src\SensorDialog.cpp"
if errorlevel 1 (
    echo ❌ 错误: 未使用时区格式 (.z)
    goto :error
) else (
    echo ✅ 成功: 已使用时区格式 (.z)
)
echo.

echo 4. 检查是否移除了旧的12小时制格式 (hh)...
findstr /n "hh:mm:ss" "SiteResConfig\src\SensorDialog.cpp"
if not errorlevel 1 (
    echo ❌ 警告: 仍存在12小时制格式 (hh)
    echo 请检查是否有遗漏的地方
) else (
    echo ✅ 成功: 已移除12小时制格式 (hh)
)
echo.

echo 5. 检查是否移除了旧的毫秒格式 (.fff)...
findstr /n "\.fff" "SiteResConfig\src\SensorDialog.cpp"
if not errorlevel 1 (
    echo ❌ 警告: 仍存在毫秒格式 (.fff)
    echo 请检查是否有遗漏的地方
) else (
    echo ✅ 成功: 已移除毫秒格式 (.fff)
)
echo.

echo 6. 显示完整的日期格式字符串...
echo 当前格式设置:
findstr /n "toString.*yyyy" "SiteResConfig\src\SensorDialog.cpp"
echo.

echo 7. 验证完整格式是否正确...
findstr /n "yyyy/MM/dd HH:mm:ss\.z" "SiteResConfig\src\SensorDialog.cpp"
if errorlevel 1 (
    echo ❌ 错误: 完整格式不正确
    echo 期望格式: yyyy/MM/dd HH:mm:ss.z
    goto :error
) else (
    echo ✅ 成功: 完整格式正确 - yyyy/MM/dd HH:mm:ss.z
)
echo.

echo ========================================
echo  🎉 校准日期格式修复验证完成！
echo ========================================
echo.
echo 修复总结:
echo ✅ 日期格式已修正为: yyyy/MM/dd HH:mm:ss.z
echo ✅ 使用24小时制 (HH) 替代12小时制 (hh)
echo ✅ 保留时区格式 (.z) 确保时间准确性
echo ✅ 符合用户要求的精确格式规范
echo.
echo 示例输出格式: 2025/01/15 14:30:25.+08:00
echo.
goto :end

:error
echo.
echo ========================================
echo  ❌ 验证失败！请检查修复是否正确
echo ========================================
echo.
echo 期望的日期格式: yyyy/MM/dd HH:mm:ss.z
echo 示例: 2025/01/15 14:30:25.+08:00
echo.
pause
exit /b 1

:end
echo 校准日期格式现在完全符合用户要求！
echo.
pause
