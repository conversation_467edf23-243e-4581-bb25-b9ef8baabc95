# TreeLineStyle 自定义树形控件样式

## 🎯 概述

`TreeLineStyle` 是一个基于 `QProxyStyle` 的自定义样式类，专门用于实现Windows经典风格的树形控件。它提供了完全的C++控制，不依赖CSS样式表，确保了更好的性能和跨平台兼容性。

## 🎨 主要特征

### 1. Windows经典虚线连接
- **点状虚线** (Qt::DotLine)
- **中灰色** (#808080)
- **1像素粗细**
- **完整的连接系统**：垂直线、水平线、L型连接

### 2. 自定义展开/折叠指示器
- **13x13像素图标**
- **9x9像素中心白色按钮**
- **透明背景**：让虚线可以穿过
- **加号/减号符号**：清晰的视觉指示

### 3. 虚线穿过图标效果
- **连续连接**：虚线从父节点穿过图标到子节点
- **自然外观**：符合Windows资源管理器风格
- **层次清晰**：完美的树形结构展示

## 🔧 技术实现

### 文件结构
```
include/treelinestyle.h     # 头文件声明
src/treelinestyle.cpp       # 实现文件
```

### 核心类设计
```cpp
class TreeLineStyle : public QProxyStyle
{
    Q_OBJECT
public:
    explicit TreeLineStyle(QStyle *base = nullptr);
    
    // 重写绘制方法
    void drawPrimitive(PrimitiveElement elem,
                       const QStyleOption *opt,
                       QPainter *p,
                       const QWidget *w) const override;

private:
    // 绘制树形连接线
    void drawTreeLines(QPainter *p, const QRect &rect, 
                      const QStyleOptionViewItem *vopt, 
                      const QTreeView *treeView) const;

    // 绘制展开/折叠指示器
    void drawExpandCollapseIndicator(QPainter *p, const QRect &rect, bool expanded) const;
};
```

### 关键实现方法

#### 1. 虚线连接绘制
```cpp
void TreeLineStyle::drawTreeLines(QPainter *p, const QRect &rect, 
                                 const QStyleOptionViewItem *vopt, 
                                 const QTreeView *treeView) const
{
    // 设置点状虚线画笔
    QPen dottedPen(QColor(0x80, 0x80, 0x80), 1, Qt::DotLine);
    p->setPen(dottedPen);
    
    // 绘制垂直线（连接到父节点）
    // 绘制水平线（连接到节点内容）
    // 绘制向下的垂直线（如果不是最后一个兄弟节点）
}
```

#### 2. 加号/减号图标绘制
```cpp
void TreeLineStyle::drawExpandCollapseIndicator(QPainter *p, const QRect &rect, bool expanded) const
{
    const int iconSize = 13; // 图标尺寸
    const int buttonSize = 9; // 按钮区域大小
    
    // 绘制白色背景按钮
    // 绘制按钮边框
    // 绘制加号或减号符号
}
```

## 🚀 使用方法

### 1. 在主窗口中应用样式

```cpp
#include "treelinestyle.h"

void CMyMainWindow::setupCustomTreeStyle() {
    // 创建自定义样式
    TreeLineStyle *customStyle = new TreeLineStyle();
    
    // 应用到树形控件
    if (ui->hardwareTreeWidget) {
        ui->hardwareTreeWidget->setStyle(customStyle);
        ui->hardwareTreeWidget->setRootIsDecorated(true);
        ui->hardwareTreeWidget->setIndentation(20);
        ui->hardwareTreeWidget->setItemsExpandable(true);
        ui->hardwareTreeWidget->setExpandsOnDoubleClick(false);
    }
}
```

### 2. 在样式表加载时调用

```cpp
void CMyMainWindow::loadStyleSheetFromFile() {
    // 加载CSS样式表
    // ...
    
    // 应用自定义树形控件样式
    setupCustomTreeStyle();
}
```

### 3. 项目文件配置

确保在 `.pro` 文件中包含相关文件：

```pro
SOURCES += \
    src/treelinestyle.cpp

HEADERS += \
    include/treelinestyle.h
```

## 🎯 优势对比

### TreeLineStyle vs CSS样式表

| 特征 | TreeLineStyle (C++) | CSS样式表 |
|------|---------------------|-----------|
| **控制精度** | 像素级精确控制 | 依赖Qt CSS支持 |
| **性能** | 直接绘制，高性能 | 需要解析CSS |
| **兼容性** | 跨Qt版本兼容 | Qt版本敏感 |
| **调试** | 易于调试和修改 | 调试困难 |
| **稳定性** | 更稳定可靠 | 可能有兼容性问题 |
| **自定义** | 完全自定义 | 受CSS限制 |

## 🔍 验证要点

### 1. 编译验证
- ✅ TreeLineStyle类编译成功
- ✅ 项目文件正确包含源文件
- ✅ 无编译错误或警告

### 2. 功能验证
- ✅ 虚线连接显示正确
- ✅ 加号/减号图标清晰
- ✅ 虚线穿过图标中心
- ✅ 交互功能正常

### 3. 视觉验证
- ✅ 符合Windows经典风格
- ✅ 层次结构清晰
- ✅ 整体外观协调

## 📝 总结

`TreeLineStyle` 提供了一个专业、可靠的树形控件样式解决方案：

- **✅ 专业实现**：基于QProxyStyle的标准Qt实现
- **✅ 完全控制**：C++代码直接控制绘制过程
- **✅ 高性能**：无CSS解析开销，直接绘制
- **✅ 跨平台**：良好的Qt版本兼容性
- **✅ 易维护**：清晰的代码结构，易于调试和修改
- **✅ Windows风格**：完美复现Windows资源管理器外观

这是实现自定义树形控件样式的最佳实践方案。
