/**
 * @file test_sensor_dialog_ui.cpp
 * @brief 测试传感器对话框UI修改
 * @details 验证新添加的Sensor组合框是否正确集成
 */

#include <QtWidgets/QApplication>
#include <QtWidgets/QDialog>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QFrame>
#include <QtWidgets/QMessageBox>
#include <QtCore/QDebug>

class TestSensorDialog : public QDialog {
    Q_OBJECT

public:
    explicit TestSensorDialog(QWidget* parent = nullptr) : QDialog(parent) {
        setupUI();
        connectSignals();
        resize(520, 400);
        setWindowTitle("传感器对话框UI测试");
    }

private slots:
    void onSensorChanged() {
        QString sensorName = sensorCombo->currentText();
        qDebug() << "选择的传感器:" << sensorName;
        
        // 根据传感器选择更新其他字段
        if (sensorName.contains("力传感器")) {
            typeCombo->setCurrentText("称重传感器");
        } else if (sensorName.contains("位移传感器")) {
            typeCombo->setCurrentText("位移传感器");
        } else if (sensorName.contains("压力传感器")) {
            typeCombo->setCurrentText("压力传感器");
        } else if (sensorName.contains("温度传感器")) {
            typeCombo->setCurrentText("热电偶");
        }
    }

    void onTestButtonClicked() {
        QString result = QString("测试结果:\n")
                        + "Sensor: " + sensorCombo->currentText() + "\n"
                        + "传感器类型: " + typeCombo->currentText() + "\n"
                        + "序列号: " + serialEdit->text();
        
        QMessageBox::information(this, "测试结果", result);
    }

private:
    QComboBox* sensorCombo;
    QComboBox* typeCombo;
    QLineEdit* serialEdit;
    QPushButton* testButton;
    QPushButton* closeButton;

    void setupUI() {
        QVBoxLayout* mainLayout = new QVBoxLayout(this);

        // 信息标签
        QLabel* infoLabel = new QLabel("传感器组\\传感器_000001");
        infoLabel->setAlignment(Qt::AlignCenter);
        infoLabel->setStyleSheet("font-weight: bold; font-size: 12pt; padding: 10px;");
        mainLayout->addWidget(infoLabel);

        // 分隔线
        QFrame* line1 = new QFrame();
        line1->setFrameShape(QFrame::HLine);
        mainLayout->addWidget(line1);

        // Sensor区域 - 使用GroupBox突出显示
        QGroupBox* sensorGroupBox = new QGroupBox();
        sensorGroupBox->setStyleSheet(
            "QGroupBox {"
            "  border: 2px solid #2E86AB;"
            "  border-radius: 8px;"
            "  margin-top: 10px;"
            "  padding-top: 15px;"
            "  background-color: #F8F9FA;"
            "}"
        );

        QVBoxLayout* sensorGroupLayout = new QVBoxLayout(sensorGroupBox);
        sensorGroupLayout->setContentsMargins(15, 15, 15, 15);
        sensorGroupLayout->setSpacing(8);

        QHBoxLayout* sensorLayout = new QHBoxLayout();
        QLabel* sensorLabel = new QLabel("Sensor:");
        sensorLabel->setStyleSheet(
            "QLabel {"
            "  font-weight: bold;"
            "  color: #2E86AB;"
            "  font-size: 12pt;"
            "  background-color: transparent;"
            "  padding: 5px;"
            "}"
        );
        sensorLabel->setMinimumWidth(80);

        sensorCombo = new QComboBox();
        sensorCombo->setMinimumSize(250, 35);
        sensorCombo->setStyleSheet(
            "QComboBox {"
            "  border: 2px solid #2E86AB;"
            "  border-radius: 6px;"
            "  padding: 8px;"
            "  background-color: white;"
            "  font-size: 10pt;"
            "}"
            "QComboBox:hover {"
            "  border-color: #1976D2;"
            "  background-color: #F5F5F5;"
            "}"
            "QComboBox::drop-down {"
            "  border: none;"
            "  width: 30px;"
            "}"
        );

        // 初始化Sensor选项（改进版）
        sensorCombo->addItem("-- 请选择传感器 --");
        sensorCombo->addItem("主传感器");
        sensorCombo->addItem("辅助传感器");
        sensorCombo->addItem("备用传感器");
        sensorCombo->insertSeparator(sensorCombo->count());
        sensorCombo->addItem("力传感器 - 主要");
        sensorCombo->addItem("力传感器 - 辅助");
        sensorCombo->addItem("位移传感器 - X轴");
        sensorCombo->addItem("位移传感器 - Y轴");
        sensorCombo->insertSeparator(sensorCombo->count());
        sensorCombo->addItem("压力传感器 - 液压");
        sensorCombo->addItem("温度传感器 - 环境");
        sensorCombo->insertSeparator(sensorCombo->count());
        sensorCombo->addItem("自定义传感器...");
        sensorCombo->setEditable(true);

        sensorLayout->addWidget(sensorLabel);
        sensorLayout->addWidget(sensorCombo);
        sensorLayout->addStretch();
        sensorGroupLayout->addLayout(sensorLayout);

        mainLayout->addWidget(sensorGroupBox);

        // 添加间距
        mainLayout->addSpacing(15);

        // 分隔线
        QFrame* line2 = new QFrame();
        line2->setFrameShape(QFrame::HLine);
        line2->setStyleSheet(
            "QFrame {"
            "  color: #2E86AB;"
            "  background-color: #2E86AB;"
            "  border: 1px solid #2E86AB;"
            "}"
        );
        mainLayout->addWidget(line2);

        // 添加间距
        mainLayout->addSpacing(10);

        // 配置区域标题
        QLabel* configLabel = new QLabel("传感器配置参数");
        configLabel->setStyleSheet(
            "QLabel {"
            "  font-weight: bold;"
            "  color: #424242;"
            "  font-size: 10pt;"
            "  padding: 5px;"
            "}"
        );
        configLabel->setAlignment(Qt::AlignLeft);
        mainLayout->addWidget(configLabel);

        // 传感器类型
        QHBoxLayout* typeLayout = new QHBoxLayout();
        QLabel* typeLabel = new QLabel("传感器类型:");
        typeLabel->setMinimumWidth(80);
        typeCombo = new QComboBox();
        typeCombo->addItem("称重传感器");
        typeCombo->addItem("位移传感器");
        typeCombo->addItem("压力传感器");
        typeCombo->addItem("热电偶");
        typeCombo->addItem("应变片");
        typeCombo->setMinimumHeight(30);
        typeLayout->addWidget(typeLabel);
        typeLayout->addWidget(typeCombo);
        mainLayout->addLayout(typeLayout);

        // 序列号
        QHBoxLayout* serialLayout = new QHBoxLayout();
        QLabel* serialLabel = new QLabel("序列号:");
        serialLabel->setMinimumWidth(80);
        serialEdit = new QLineEdit("传感器_000001");
        serialEdit->setMinimumHeight(30);
        serialLayout->addWidget(serialLabel);
        serialLayout->addWidget(serialEdit);
        mainLayout->addLayout(serialLayout);

        mainLayout->addStretch();

        // 按钮
        QHBoxLayout* buttonLayout = new QHBoxLayout();
        buttonLayout->addStretch();
        testButton = new QPushButton("测试");
        closeButton = new QPushButton("关闭");
        buttonLayout->addWidget(testButton);
        buttonLayout->addWidget(closeButton);
        mainLayout->addLayout(buttonLayout);
    }

    void connectSignals() {
        connect(sensorCombo, QOverload<const QString&>::of(&QComboBox::currentTextChanged),
                this, &TestSensorDialog::onSensorChanged);
        connect(testButton, &QPushButton::clicked, this, &TestSensorDialog::onTestButtonClicked);
        connect(closeButton, &QPushButton::clicked, this, &QDialog::accept);
    }
};

int main(int argc, char *argv[]) {
    QApplication app(argc, argv);

    TestSensorDialog dialog;
    dialog.show();

    return app.exec();
}

#include "test_sensor_dialog_ui.moc"
