@echo off
chcp 65001 >nul
echo MOC编译错误正确修复方案
echo =============================

cd /d "D:\Work\Project\004_SiteResConfig_ProjectAll\001_Code\001_SiteResConfig\SiteResConfig"

echo 步骤1: 完全清理编译环境...
if exist debug rmdir /s /q debug 2>nul
if exist release rmdir /s /q release 2>nul
if exist Makefile* del /q Makefile* 2>nul
if exist .qmake.stash del /q .qmake.stash 2>nul
if exist object_script*.* del /q object_script*.* 2>nul

echo 步骤2: 重新创建编译目录...
mkdir debug 2>nul
mkdir release 2>nul

echo 步骤3: 检查Qt环境...
qmake -v >nul 2>&1
if %errorlevel% neq 0 (
    echo   ✗ Qt环境未配置！请设置Qt环境变量
    echo   建议: 添加Qt的bin目录到PATH
    goto :error
) else (
    echo   ✓ Qt环境正常
    qmake -v
)

echo.
echo 步骤4: 重新生成Makefile (让qmake自动处理MOC)...
qmake SiteResConfig_Simple.pro
if %errorlevel% neq 0 (
    echo   ✗ qmake执行失败
    goto :error
) else (
    echo   ✓ Makefile生成成功，MOC规则已自动创建
)

echo.
echo 步骤5: 检查生成的Makefile...
if exist Makefile (
    echo   ✓ Makefile存在
    findstr /c:"TreeInteractionHandler" Makefile >nul 2>&1
    if not errorlevel 1 (
        echo   ✓ Makefile包含TreeInteractionHandler规则
    )
) else (
    echo   ✗ Makefile不存在
    goto :error
)

echo.
echo 步骤6: 开始编译...
echo 检测可用的编译工具...

where nmake >nul 2>&1
if %errorlevel% equ 0 (
    echo   使用 nmake 编译...
    nmake
    set COMPILE_RESULT=%errorlevel%
) else (
    where mingw32-make >nul 2>&1
    if %errorlevel% equ 0 (
        echo   使用 mingw32-make 编译...
        mingw32-make
        set COMPILE_RESULT=%errorlevel%
    ) else (
        where make >nul 2>&1
        if %errorlevel% equ 0 (
            echo   使用 make 编译...
            make
            set COMPILE_RESULT=%errorlevel%
        ) else (
            echo   ✗ 未找到编译工具 (nmake/mingw32-make/make)
            goto :error
        )
    )
)

if %COMPILE_RESULT% equ 0 (
    echo   ✓ 编译成功！
    goto :success
) else (
    echo   ✗ 编译失败
    goto :error
)

:success
echo.
echo =============================
echo ✓ MOC错误修复成功！
echo.
echo 生成的文件：
if exist debug\SiteResConfig.exe (
    echo   ✓ 可执行文件: debug\SiteResConfig.exe
) else if exist release\SiteResConfig.exe (
    echo   ✓ 可执行文件: release\SiteResConfig.exe
) else (
    echo   ? 未找到可执行文件，但编译成功
)
echo =============================
pause
exit /b 0

:error
echo.
echo =============================
echo ✗ 修复失败，建议手动操作：
echo.
echo 方法1 - 使用Qt Creator：
echo 1. 在Qt Creator中打开 SiteResConfig_Simple.pro
echo 2. 构建 → 清理项目
echo 3. 构建 → 运行qmake  
echo 4. 构建 → 重新构建项目
echo.
echo 方法2 - 命令行操作：
echo 1. 确保Qt环境变量正确设置
echo 2. 运行: qmake SiteResConfig_Simple.pro
echo 3. 运行: nmake (或 mingw32-make/make)
echo.
echo 常见问题检查：
echo - Qt bin目录是否在PATH中
echo - 编译工具链是否安装并可用
echo - 项目文件是否有读写权限
echo - 是否有防病毒软件阻止编译
echo =============================
pause
exit /b 1 