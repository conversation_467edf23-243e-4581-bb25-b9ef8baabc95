/**
 * @file DataModels_Simple.cpp
 * @brief Core Data Models Implementation (Simplified)
 * @details Simplified implementation without JSON dependency
 * <AUTHOR> Assistant
 * @date 2025-08-05
 * @version 1.0.0
 */

#include "DataModels_Fixed.h"
#include "SensorDialog_1_2.h"  // 🆕 新增：包含传感器参数结构体
#include "SensorDataManager_1_2.h"  // 🆕 新增：包含传感器数据管理器
#include "ActuatorDataManager_1_2.h"  // 🆕 新增：包含作动器数据管理器
#include <regex>
#include <sstream>
#include <fstream>
#include <iostream>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QJsonValue>
#include <QString>
#include <QFile>
#include <QTextStream>

namespace DataModels {

// ============================================================================
// HardwareNode Implementation
// ============================================================================

//json HardwareNode::ToJson() const {
//    std::ostringstream oss;
//    oss << "{"
//        << "\"nodeId\":" << nodeId << ","
//        << "\"nodeName\":\"" << nodeName << "\","
//        << "\"nodeType\":\"" << nodeType << "\","
//        << "\"ipAddress\":\"" << ipAddress << "\","
//        << "\"port\":" << port << ","
//        << "\"isConnected\":" << (isConnected ? "true" : "false") << ","
//        << "\"firmwareVersion\":\"" << firmwareVersion << "\","
//        << "\"channelCount\":" << channelCount << ","
//        << "\"maxSampleRate\":" << maxSampleRate
//        << "}";
//    return oss.str();
//}

//bool HardwareNode::FromJson(const json& jsonData) {
//    // Simplified implementation - just set default values for now
//    // TODO: Implement proper JSON parsing when JSON library is available
//    nodeId = 0;
//    nodeName = "Default Node";
//    nodeType = "DefaultType";
//    ipAddress = "*************";
//    port = 8080;
//    isConnected = false;
//    firmwareVersion = "v1.0.0";
//    channelCount = 4;
//    maxSampleRate = 10000.0;
//    return true;
//}

//bool HardwareNode::IsValid() const {
//    // Validate node ID validity
//    if (nodeId < 0) return false;
    
//    // Validate node name is not empty
//    if (nodeName.empty()) return false;
    
//    // Validate IP address format (simple validation)
//    if (!ipAddress.empty()) {
//        std::regex ipRegex(R"(^(\d{1,3}\.){3}\d{1,3}$)");
//        if (!std::regex_match(ipAddress, ipRegex)) return false;
//    }
    
//    // Validate port range
//    if (port < 0 || port > 65535) return false;
    
//    return true;
//}

// ============================================================================
// SensorInfo Implementation
// ============================================================================

//json SensorInfo::ToJson() const {
//    std::ostringstream oss;
//    oss << "{"
//        << "\"sensorId\":\"" << sensorId << "\","
//        << "\"sensorName\":\"" << sensorName << "\","
//        << "\"sensorType\":" << static_cast<int>(sensorType) << ","
//        << "\"fullScale\":" << fullScale << ","
//        << "\"unit\":\"" << unit << "\""
//        << "}";
//    return oss.str();
//}

//bool SensorInfo::FromJson(const json& jsonData) {
//    // Simplified implementation
//    sensorId = "SENSOR_001";
//    sensorName = "Default Sensor";
//    sensorType = Enums::SensorType::Force;
//    fullScale = 1000.0;
//    unit = "N";
//    isPositive = true;
//    return true;
//}

//bool SensorInfo::IsValid() const {
//    // Validate sensor ID is not empty
//    if (sensorId.empty()) return false;
    
//    // Validate sensor name is not empty
//    if (sensorName.empty()) return false;
    
//    // Validate sensor type
//    if (sensorType == Enums::SensorType::Unknown) return false;
    
//    // Validate full scale is positive
//    if (fullScale <= 0.0) return false;
    
//    // Validate unit is not empty
//    if (unit.empty()) return false;
    
//    return true;
//}

// ============================================================================
// ActuatorInfo Implementation
// ============================================================================

//json ActuatorInfo::ToJson() const {
//    std::ostringstream oss;
//    oss << "{"
//        << "\"actuatorId\":\"" << actuatorId << "\","
//        << "\"actuatorName\":\"" << actuatorName << "\","
//        << "\"actuatorType\":" << static_cast<int>(actuatorType) << ","
//        << "\"cylinderDiameter\":" << cylinderDiameter << ","
//        << "\"stroke\":" << stroke
//        << "}";
//    return oss.str();
//}

//bool ActuatorInfo::FromJson(const json& jsonData) {
//    // Simplified implementation
//    actuatorId = "ACT_001";
//    actuatorName = "Default Actuator";
//    actuatorType = Enums::ActuatorType::Hydraulic;
//    cylinderDiameter = 125.0;
//    rodDiameter = 80.0;
//    stroke = 200.0;
//    maxForce = 100000.0;
//    return true;
//}

//bool ActuatorInfo::IsValid() const {
//    // Validate actuator ID is not empty
//    if (actuatorId.empty()) return false;
    
//    // Validate actuator name is not empty
//    if (actuatorName.empty()) return false;
    
//    // Validate actuator type
//    if (actuatorType == Enums::ActuatorType::Unknown) return false;
    
//    // Validate physical parameters reasonableness
//    if (cylinderDiameter <= 0.0 || rodDiameter <= 0.0 || stroke <= 0.0) return false;
//    if (rodDiameter >= cylinderDiameter) return false;
    
//    return true;
//}

// ============================================================================
// LoadControlChannel Implementation
// ============================================================================

//json LoadControlChannel::ToJson() const {
//    std::ostringstream oss;
//    oss << "{"
//        << "\"channelId\":\"" << channelId << "\","
//        << "\"channelName\":\"" << channelName << "\","
//        << "\"channelIndex\":" << channelIndex << ","
//        << "\"designLoad\":" << designLoad
//        << "}";
//    return oss.str();
//}

//bool LoadControlChannel::FromJson(const json& jsonData) {
//    // Simplified implementation
//    channelId = "CH_001";
//    channelName = "Default Channel";
//    channelIndex = 0;
//    actuatorId = "ACT_001";
//    sensorIds.push_back("SENSOR_001");
//    designLoad = 50000.0;
//    controlMode = Enums::ControlMode::Force;
//    return true;
//}

//bool LoadControlChannel::IsValid() const {
//    // Validate channel ID is not empty
//    if (channelId.empty()) return false;
    
//    // Validate channel name is not empty
//    if (channelName.empty()) return false;
    
//    // Validate channel index validity
//    if (channelIndex < 0) return false;
    
//    // Validate PID parameters reasonableness
//    if (kp < 0.0) return false;
    
//    // Validate safety limits reasonableness
//    if (positionLimitLow >= positionLimitHigh) return false;
//    if (loadLimitLow >= loadLimitHigh) return false;
    
//    return true;
//}

// ============================================================================
// LoadSpectrum Implementation
// ============================================================================

//json LoadSpectrum::ToJson() const {
//    std::ostringstream oss;
//    oss << "{"
//        << "\"spectrumId\":\"" << spectrumId << "\","
//        << "\"spectrumName\":\"" << spectrumName << "\","
//        << "\"spectrumType\":\"" << spectrumType << "\","
//        << "\"duration\":" << duration << ","
//        << "\"amplitude\":" << amplitude << ","
//        << "\"frequency\":" << frequency
//        << "}";
//    return oss.str();
//}

//bool LoadSpectrum::FromJson(const json& jsonData) {
//    // Simplified implementation
//    spectrumId = "SPEC001";
//    spectrumName = "Default Spectrum";
//    spectrumType = "sine";
//    duration = 60.0;
//    amplitude = 1000.0;
//    frequency = 1.0;
//    return true;
//}

//bool LoadSpectrum::IsValid() const {
//    if (spectrumId.empty() || spectrumName.empty()) return false;
//    if (duration <= 0.0) return false;
//    if (amplitude < 0.0) return false;
//    if (frequency <= 0.0) return false;
//    return true;
//}

// ============================================================================
// TestProject Implementation
// ============================================================================

//json TestProject::ToJson() const {
//    std::ostringstream oss;
//    oss << "{"
//        << "\"projectName\":\"" << projectName << "\","
//        << "\"description\":\"" << description << "\","
//        << "\"createdDate\":\"" << createdDate << "\","
//        << "\"version\":\"" << version << "\","
//        << "\"sampleRate\":" << sampleRate << ","
//        << "\"testDuration\":" << testDuration
//        << "}";
//    return oss.str();
//}

//bool TestProject::FromJson(const json& jsonData) {
//    // Simplified implementation
//    projectName = "Default Project";
//    description = "Default test project";
//    createdDate = "2025-08-05";
//    version = "1.0.0";
//    sampleRate = 1000.0;
//    testDuration = 0.0;
//    return true;
//}

//bool TestProject::IsValid() const {
//    if (projectName.empty()) return false;
//    if (sampleRate <= 0.0) return false;
//    return true;
//}

//bool TestProject::SaveToFile(const StringType& filePath) const {
//    try {
//        // 使用QJsonDocument生成JSON
//        QJsonObject projectJson;

//        // 基本项目信息
//        projectJson["projectName"] = QString::fromStdString(projectName);
//        projectJson["description"] = QString::fromStdString(description);
//        projectJson["createdDate"] = QString::fromStdString(createdDate);
//        projectJson["modifiedDate"] = QString::fromStdString(modifiedDate);
//        projectJson["version"] = QString::fromStdString(version);
//        projectJson["sampleRate"] = sampleRate;
//        projectJson["testDuration"] = testDuration;

//        // 注释掉硬件节点信息 - HardwareNode已弃用
//        // QJsonArray hardwareNodesArray;
//        // for (const auto& node : hardwareNodes) {
//        //     QJsonObject nodeObj;
//        //     nodeObj["nodeId"] = node.nodeId;
//        //     nodeObj["nodeName"] = QString::fromStdString(node.nodeName);
//        //     nodeObj["nodeType"] = QString::fromStdString(node.nodeType);
//        //     nodeObj["ipAddress"] = QString::fromStdString(node.ipAddress);
//        //     nodeObj["port"] = node.port;
//        //     nodeObj["channelCount"] = node.channelCount;
//        //     nodeObj["maxSampleRate"] = node.maxSampleRate;
//        //     nodeObj["firmwareVersion"] = QString::fromStdString(node.firmwareVersion);
//        //     hardwareNodesArray.append(nodeObj);
//        // }
//        // projectJson["hardwareNodes"] = hardwareNodesArray;

//        // 作动器信息
//        QJsonArray actuatorsArray;
//        for (const auto& actuator : actuators) {
//            QJsonObject actuatorObj;
//            actuatorObj["actuatorId"] = QString::fromStdString(actuator.actuatorId);
//            actuatorObj["actuatorName"] = QString::fromStdString(actuator.actuatorName);

//            // 转换枚举类型为字符串
//            QString actuatorTypeStr;
//            switch (actuator.actuatorType) {
//                case DataModels::Enums::ActuatorType::Hydraulic:
//                    actuatorTypeStr = "Hydraulic";
//                    break;
//                case DataModels::Enums::ActuatorType::Electric:
//                    actuatorTypeStr = "Electric";
//                    break;
//                case DataModels::Enums::ActuatorType::Pneumatic:
//                    actuatorTypeStr = "Pneumatic";
//                    break;
//                default:
//                    actuatorTypeStr = "Unknown";
//                    break;
//            }
//            actuatorObj["actuatorType"] = actuatorTypeStr;

//            actuatorObj["maxForce"] = actuator.maxForce;
//            actuatorObj["stroke"] = actuator.stroke;
//            actuatorObj["maxVelocity"] = actuator.maxVelocity;
//            actuatorObj["boundNodeId"] = actuator.boundNodeId;
//            actuatorObj["boundControlChannel"] = actuator.boundControlChannel;
//            actuatorsArray.append(actuatorObj);
//        }
//        projectJson["actuators"] = actuatorsArray;

//        // 传感器信息
//        QJsonArray sensorsArray;
//        for (const auto& sensor : sensors) {
//            QJsonObject sensorObj;
//            sensorObj["sensorId"] = QString::fromStdString(sensor.sensorId);
//            sensorObj["sensorName"] = QString::fromStdString(sensor.sensorName);

//            // 转换枚举类型为字符串
//            QString sensorTypeStr;
//            switch (sensor.sensorType) {
//                case DataModels::Enums::SensorType::Force:
//                    sensorTypeStr = "Force";
//                    break;
//                case DataModels::Enums::SensorType::Displacement:
//                    sensorTypeStr = "Displacement";
//                    break;
//                case DataModels::Enums::SensorType::Pressure:
//                    sensorTypeStr = "Pressure";
//                    break;
//                case DataModels::Enums::SensorType::Temperature:
//                    sensorTypeStr = "Temperature";
//                    break;
//                case DataModels::Enums::SensorType::Acceleration:
//                    sensorTypeStr = "Acceleration";
//                    break;
//                case DataModels::Enums::SensorType::Strain:
//                    sensorTypeStr = "Strain";
//                    break;
//                default:
//                    sensorTypeStr = "Unknown";
//                    break;
//            }
//            sensorObj["sensorType"] = sensorTypeStr;

//            sensorObj["fullScale"] = sensor.fullScale;
//            sensorObj["unit"] = QString::fromStdString(sensor.unit);
//            sensorObj["boundNodeId"] = sensor.boundNodeId;
//            sensorObj["boundChannel"] = sensor.boundChannel;
//            sensorsArray.append(sensorObj);
//        }
//        projectJson["sensors"] = sensorsArray;

//        // 加载通道信息
//        QJsonArray loadChannelsArray;
//        for (const auto& channel : loadChannels) {
//            QJsonObject channelObj;
//            channelObj["channelId"] = QString::fromStdString(channel.channelId);
//            channelObj["channelName"] = QString::fromStdString(channel.channelName);
//            channelObj["maxForce"] = channel.maxForce;
//            channelObj["maxVelocity"] = channel.maxVelocity;

//            // 转换ControlMode枚举为字符串
//            QString controlModeStr;
//            switch (channel.controlMode) {
//                case DataModels::Enums::ControlMode::Force:
//                    controlModeStr = "Force";
//                    break;
//                case DataModels::Enums::ControlMode::Position:
//                    controlModeStr = "Position";
//                    break;
//                case DataModels::Enums::ControlMode::Velocity:
//                    controlModeStr = "Velocity";
//                    break;
//                case DataModels::Enums::ControlMode::Hybrid:
//                    controlModeStr = "Hybrid";
//                    break;
//                default:
//                    controlModeStr = "Unknown";
//                    break;
//            }
//            channelObj["controlMode"] = controlModeStr;

//            // PID参数
//            channelObj["kp"] = channel.kp;
//            channelObj["ki"] = channel.ki;
//            channelObj["kd"] = channel.kd;
//            channelObj["safetyEnabled"] = channel.safetyEnabled;
//            channelObj["positionLimitLow"] = channel.positionLimitLow;
//            channelObj["positionLimitHigh"] = channel.positionLimitHigh;
//            channelObj["loadLimitLow"] = channel.loadLimitLow;
//            channelObj["loadLimitHigh"] = channel.loadLimitHigh;
//            loadChannelsArray.append(channelObj);
//        }
//        projectJson["loadChannels"] = loadChannelsArray;

//        // 载荷谱信息
//        QJsonArray loadSpectrumsArray;
//        for (const auto& spectrum : loadSpectrums) {
//            QJsonObject spectrumObj;
//            spectrumObj["spectrumId"] = QString::fromStdString(spectrum.spectrumId);
//            spectrumObj["spectrumName"] = QString::fromStdString(spectrum.spectrumName);
//            spectrumObj["spectrumType"] = QString::fromStdString(spectrum.spectrumType);
//            spectrumObj["duration"] = spectrum.duration;
//            spectrumObj["amplitude"] = spectrum.amplitude;
//            spectrumObj["frequency"] = spectrum.frequency;
//            loadSpectrumsArray.append(spectrumObj);
//        }
//        projectJson["loadSpectrums"] = loadSpectrumsArray;

//        // 创建JSON文档并写入文件
//        QJsonDocument jsonDoc(projectJson);

//        // 使用Qt文件操作来正确处理中文文件名
//        QString qFilePath = QString::fromStdString(filePath);
//        QFile file(qFilePath);
//        if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
//            return false;
//        }

//        // 使用QJsonDocument生成格式化的JSON字符串
//        QByteArray jsonData = jsonDoc.toJson(QJsonDocument::Indented);
//        file.write(jsonData);
//        file.close();

//        return true;

//    } catch (const std::exception&) {
//        return false;
//    }
//}

//bool TestProject::LoadFromFile(const StringType& filePath) {
//    try {
//        // 使用Qt文件操作来正确处理中文文件名
//        QString qFilePath = QString::fromStdString(filePath);
//        QFile file(qFilePath);
//        if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
//            return false;
//        }

//        QByteArray content = file.readAll();
//        file.close();

//        // 使用QJsonDocument解析JSON
//        QJsonParseError parseError;
//        QJsonDocument jsonDoc = QJsonDocument::fromJson(content, &parseError);

//        if (parseError.error != QJsonParseError::NoError) {
//            return false; // JSON解析错误
//        }

//        if (!jsonDoc.isObject()) {
//            return false; // 不是JSON对象
//        }

//        QJsonObject projectJson = jsonDoc.object();

//        // 解析基本项目信息
//        if (projectJson.contains("projectName")) {
//            projectName = projectJson["projectName"].toString().toStdString();
//        }
//        if (projectJson.contains("description")) {
//            description = projectJson["description"].toString().toStdString();
//        }
//        if (projectJson.contains("createdDate")) {
//            createdDate = projectJson["createdDate"].toString().toStdString();
//        }
//        if (projectJson.contains("modifiedDate")) {
//            modifiedDate = projectJson["modifiedDate"].toString().toStdString();
//        }
//        if (projectJson.contains("version")) {
//            version = projectJson["version"].toString().toStdString();
//        }
//        if (projectJson.contains("sampleRate")) {
//            sampleRate = projectJson["sampleRate"].toDouble();
//        }
//        if (projectJson.contains("testDuration")) {
//            testDuration = projectJson["testDuration"].toDouble();
//        }

//        // 注释掉硬件节点数组解析 - HardwareNode已弃用
//        // if (projectJson.contains("hardwareNodes") && projectJson["hardwareNodes"].isArray()) {
//        //     hardwareNodes.clear();
//        //     QJsonArray nodesArray = projectJson["hardwareNodes"].toArray();
//        //     for (const QJsonValue& nodeValue : nodesArray) {
//        //         if (nodeValue.isObject()) {
//        //             QJsonObject nodeObj = nodeValue.toObject();
//        //             HardwareNode node;

//        //             if (nodeObj.contains("nodeId")) {
//        //                 node.nodeId = nodeObj["nodeId"].toInt();
//        //             }
//        //             if (nodeObj.contains("nodeName")) {
//        //                 node.nodeName = nodeObj["nodeName"].toString().toStdString();
//        //             }
//        //             if (nodeObj.contains("nodeType")) {
//        //                 node.nodeType = nodeObj["nodeType"].toString().toStdString();
//        //             }
//        //             if (nodeObj.contains("ipAddress")) {
//        //                 node.ipAddress = nodeObj["ipAddress"].toString().toStdString();
//        //             }
//        //             if (nodeObj.contains("port")) {
//        //                 node.port = nodeObj["port"].toInt();
//        //             }
//        //             if (nodeObj.contains("channelCount")) {
//        //                 node.channelCount = nodeObj["channelCount"].toInt();
//        //             }
//        //             if (nodeObj.contains("maxSampleRate")) {
//        //                 node.maxSampleRate = nodeObj["maxSampleRate"].toDouble();
//        //             }
//        //             if (nodeObj.contains("firmwareVersion")) {
//        //                 node.firmwareVersion = nodeObj["firmwareVersion"].toString().toStdString();
//        //             }

//        //             hardwareNodes.push_back(node);
//        //         }
//        //     }
//        // }

//        // 注意：这里只实现了基本字段和硬件节点的解析
//        // 作动器、传感器、加载通道、载荷谱的解析可以类似实现
//        // 为了保持代码简洁，暂时只实现基本功能

//        return true;

//    } catch (const std::exception&) {
//        return false;
//    }
//}

// ============================================================================
// 🔄 修改：传感器详细参数管理方法实现（委托给SensorDataManager）
// ============================================================================

//bool TestProject::addSensorDetailedParams(const StringType& serialNumber, const UI::SensorParams_1_2& params) {
//    if (sensorDataManager_) {
//        return sensorDataManager_->addSensor(params);
//    }
//    return false;
//}

//UI::SensorParams_1_2 TestProject::getSensorDetailedParams(const StringType& serialNumber) const {
//    if (sensorDataManager_) {
//        return sensorDataManager_->getSensor(QString::fromStdString(serialNumber));
//    }
//    return UI::SensorParams_1_2(); // 返回默认构造的空参数
//}

//bool TestProject::hasSensorDetailedParams(const StringType& serialNumber) const {
//    if (sensorDataManager_) {
//        return sensorDataManager_->hasSensor(QString::fromStdString(serialNumber));
//    }
//    return false;
//}

//bool TestProject::updateSensorDetailedParams(const StringType& serialNumber, const UI::SensorParams_1_2& params) {
//    if (sensorDataManager_) {
//        return sensorDataManager_->updateSensor(QString::fromStdString(serialNumber), params);
//    }
//    return false;
//}

//bool TestProject::removeSensorDetailedParams(const StringType& serialNumber) {
//    if (sensorDataManager_) {
//        return sensorDataManager_->removeSensor(QString::fromStdString(serialNumber));
//    }
//    return false;
//}

//std::vector<StringType> TestProject::getAllSensorSerialNumbers() const {
//    std::vector<StringType> serialNumbers;
//    if (sensorDataManager_) {
//        QStringList qSerialNumbers = sensorDataManager_->getAllSensorSerialNumbers();
//        for (const QString& sn : qSerialNumbers) {
//            serialNumbers.push_back(sn.toStdString());
//        }
//    }
//    return serialNumbers;
//}

//int TestProject::getSensorCount() const {
//    if (sensorDataManager_) {
//        return sensorDataManager_->getSensorCount();
//    }
//    return 0;
//}

//void TestProject::clearAllSensors() {
//    if (sensorDataManager_) {
//        sensorDataManager_->clearAllSensors();
//    }
//}

// ============================================================================
// 🔄 修改：作动器详细参数管理方法实现（委托给ActuatorDataManager）
// ============================================================================

//bool TestProject::addActuatorDetailedParams(const StringType& serialNumber, const UI::ActuatorParams_1_2& params) {
//    if (actuatorDataManager_) {
//        return actuatorDataManager_->addActuator(params);
//    }
//    return false;
//}

//UI::ActuatorParams_1_2 TestProject::getActuatorDetailedParams(const StringType& serialNumber) const {
//    if (actuatorDataManager_) {
//        return actuatorDataManager_->getActuator(QString::fromStdString(serialNumber));
//    }
//    return UI::ActuatorParams_1_2(); // 返回默认构造的空参数
//}

//bool TestProject::hasActuatorDetailedParams(const StringType& serialNumber) const {
//    if (actuatorDataManager_) {
//        return actuatorDataManager_->hasActuator(QString::fromStdString(serialNumber));
//    }
//    return false;
//}

//bool TestProject::updateActuatorDetailedParams(const StringType& serialNumber, const UI::ActuatorParams_1_2& params) {
//    if (actuatorDataManager_) {
//        return actuatorDataManager_->updateActuator(QString::fromStdString(serialNumber), params);
//    }
//    return false;
//}

//bool TestProject::removeActuatorDetailedParams(const StringType& serialNumber) {
//    if (actuatorDataManager_) {
//        return actuatorDataManager_->removeActuator(QString::fromStdString(serialNumber));
//    }
//    return false;
//}

// 注意：此方法在头文件中已移除，但保留实现以防需要
// std::map<StringType, UI::ActuatorParams_1_2> TestProject::getAllActuatorDetailedParams() const {
//     std::map<StringType, UI::ActuatorParams_1_2> result;
//     if (actuatorDataManager_) {
//         QList<UI::ActuatorParams_1_2> actuators = actuatorDataManager_->getAllActuatorDetailedParams();
//         for (const auto& actuator : actuators) {
//             result[actuator.serialNumber.toStdString()] = actuator;
//         }
//     }
//     return result;
// }

//std::vector<StringType> TestProject::getAllActuatorSerialNumbers() const {
//    std::vector<StringType> serialNumbers;
//    if (actuatorDataManager_) {
//        QStringList qSerialNumbers = actuatorDataManager_->getAllActuatorSerialNumbers();
//        for (const QString& sn : qSerialNumbers) {
//            serialNumbers.push_back(sn.toStdString());
//        }
//    }
//    return serialNumbers;
//}

//int TestProject::getActuatorCount() const {
//    if (actuatorDataManager_) {
//        return actuatorDataManager_->getActuatorCount();
//    }
//    return 0;
//}

// ============================================================================
// 🔄 修改：作动器组管理方法实现（委托给ActuatorDataManager）
// ============================================================================

//bool TestProject::addActuatorGroup(int groupId, const UI::ActuatorGroup_1_2& group) {
//    if (actuatorDataManager_) {
//        return actuatorDataManager_->saveActuatorGroup(group);
//    }
//    return false;
//}

//UI::ActuatorGroup_1_2 TestProject::getActuatorGroup(int groupId) const {
//    if (actuatorDataManager_) {
//        return actuatorDataManager_->getActuatorGroup(groupId);
//    }
//    return UI::ActuatorGroup_1_2(); // 返回默认构造的空组
//}

//bool TestProject::hasActuatorGroup(int groupId) const {
//    if (actuatorDataManager_) {
//        return actuatorDataManager_->hasActuatorGroup(groupId);
//    }
//    return false;
//}

//bool TestProject::updateActuatorGroup(int groupId, const UI::ActuatorGroup_1_2& group) {
//    if (actuatorDataManager_) {
//        return actuatorDataManager_->updateActuatorGroup(groupId, group);
//    }
//    return false;
//}

//bool TestProject::removeActuatorGroup(int groupId) {
//    if (actuatorDataManager_) {
//        return actuatorDataManager_->removeActuatorGroup(groupId);
//    }
//    return false;
//}

//std::vector<UI::ActuatorGroup_1_2> TestProject::getAllActuatorGroups() const {
//    std::vector<UI::ActuatorGroup_1_2> result;
//    if (actuatorDataManager_) {
//        QList<UI::ActuatorGroup_1_2> groups = actuatorDataManager_->getAllActuatorGroups();
//        for (const auto& group : groups) {
//            result.push_back(group);
//        }
//    }
//    return result;
//}

//int TestProject::getActuatorGroupCount() const {
//    if (actuatorDataManager_) {
//        return actuatorDataManager_->getActuatorGroupCount();
//    }
//    return 0;
//}

//void TestProject::clearAllActuators() {
//    if (actuatorDataManager_) {
//        actuatorDataManager_->clearAll();
//    }
//}

//void TestProject::clearAllActuatorGroups() {
//    if (actuatorDataManager_) {
//        actuatorDataManager_->clearAllActuatorGroups();
//    }
//}

// ============================================================================
// 🔄 修改：DataManager管理方法实现
// ============================================================================

//// 设置传感器数据管理器
//void TestProject::setSensorDataManager(SensorDataManager_1_2* manager) {
//    sensorDataManager_ = manager;
////    if (sensorDataManager_) {
////        sensorDataManager_->setProject(this);
////    }
//}

//// 设置作动器数据管理器
//void TestProject::setActuatorDataManager(ActuatorDataManager_1_2* manager) {
//    actuatorDataManager_ = manager;
////    if (actuatorDataManager_) {
////        actuatorDataManager_->setProject(this);
////    }
//}

//SensorDataManager_1_2* TestProject::getSensorDataManager() const {
//    return sensorDataManager_;
//}

//ActuatorDataManager_1_2* TestProject::getActuatorDataManager() const {
//    return actuatorDataManager_;
//}

//// 初始化数据管理器
//void TestProject::initializeDataManagers() {
//    if (!sensorDataManager_) {
//        sensorDataManager_ = new SensorDataManager_1_2();
//        ownDataManagers_ = true;
//    }
//    if (!actuatorDataManager_) {
//        actuatorDataManager_ = new ActuatorDataManager_1_2();
//        ownDataManagers_ = true;
//    }
//}

//// 清理数据管理器
//void TestProject::cleanupDataManagers() {
//    if (ownDataManagers_) {
//        delete sensorDataManager_;
//        delete actuatorDataManager_;
//        sensorDataManager_ = nullptr;
//        actuatorDataManager_ = nullptr;
//        ownDataManagers_ = false;
//    }
//}

} // namespace DataModels
