@echo off
chcp 65001 >nul
echo.
echo ========================================
echo ✅ 修复传感器组名称显示问题
echo ========================================
echo.

echo 🎯 问题定位:
echo 发现有两个不同的传感器导出方法：
echo.
echo 1. ✅ OnExportSensorDetailsToExcel()
echo    - 使用: xlsDataExporter_-^>exportSensorGroupDetails()
echo    - 支持: 组名称只在第一行显示
echo    - 状态: 正常工作
echo.
echo 2. ❌ onExportSensorDetailsActionTriggered()
echo    - 使用: dataExportManager_-^>exportSensorDetails()
echo    - 问题: 单个传感器导出，无组名称显示功能
echo    - 状态: 已修复
echo.

echo 🔧 修复内容:
echo.
echo 修改 onExportSensorDetailsActionTriggered() 方法:
echo.
echo 修复前:
echo   - 使用 dataExportManager_-^>exportSensorDetails()
echo   - 获取 QList^<UI::SensorParams^> 单个传感器列表
echo   - 调用旧的单个传感器导出方法
echo   - 结果: 无组名称显示功能
echo.
echo 修复后:
echo   - 使用 xlsDataExporter_-^>exportSensorGroupDetails()
echo   - 获取 QList^<UI::SensorGroup^> 传感器组列表
echo   - 调用新的传感器组导出方法
echo   - 结果: 支持组名称只在第一行显示
echo.

echo 📊 修复后的统一行为:
echo.
echo 所有传感器导出路径现在都使用相同的逻辑:
echo 1. OnExportSensorDetailsToExcel() ✅
echo 2. onExportSensorDetailsActionTriggered() ✅
echo 3. exportCompleteProject() 中的传感器部分 ✅
echo.

echo 🚀 测试步骤:
echo.
echo 1. 重新编译应用程序
echo.
echo 2. 创建测试数据:
echo    - 创建多个传感器组
echo    - 每个组添加多个传感器
echo.
echo 3. 测试所有导出路径:
echo    a) 菜单: 数据导出 -^> 导出传感器详细信息到Excel
echo    b) 其他可能的传感器导出入口
echo    c) 完整项目导出中的传感器部分
echo.
echo 4. 验证Excel结果:
echo    - 检查"传感器组名称"列（第2列）
echo    - 确认组名称只在每组第一行显示
echo    - 同组其他行的组名称列为空白
echo.

echo ✅ 预期结果:
echo.
echo 1. 组名称显示规则:
echo    组序号 ^| 传感器组名称    ^| 传感器序列号 ^| ...
echo    ------|---------------|------------|----
echo    1     ^| 载荷_传感器组   ^| SEN001     ^| ...
echo    1     ^|               ^| SEN002     ^| ...  ← 空白
echo    1     ^|               ^| SEN003     ^| ...  ← 空白
echo    2     ^| 位置_传感器组   ^| SEN004     ^| ...
echo    2     ^|               ^| SEN005     ^| ...  ← 空白
echo.
echo 2. 调试输出:
echo    控制台应显示:
echo    "组ID: 1, 传感器索引: 0, 组名称: 载荷_传感器组, 显示组名称: 是"
echo    "组ID: 1, 传感器索引: 1, 组名称: 载荷_传感器组, 显示组名称: 否"
echo    "组ID: 1, 传感器索引: 2, 组名称: 载荷_传感器组, 显示组名称: 否"
echo.
echo 3. 格式一致性:
echo    - 第一行: 浅蓝色背景 + 粗体
echo    - 其他行: 普通边框格式
echo    - 所有行: 完整的33列数据
echo.

echo 🔍 关键修复点:
echo.
echo 1. 数据源统一:
echo    - 所有导出都从 sensorDataManager_-^>getAllSensorGroups() 获取
echo    - 不再使用单个传感器列表
echo.
echo 2. 导出方法统一:
echo    - 所有导出都使用 exportSensorGroupDetails()
echo    - 不再使用旧的 exportSensorDetails()
echo.
echo 3. 显示逻辑统一:
echo    - 所有导出都通过 addSensorGroupDetailToExcel() 处理
echo    - 确保组名称显示逻辑一致
echo.

echo 💡 技术要点:
echo.
echo 1. 方法调用链:
echo    用户操作 -^> OnExportSensorDetailsToExcel()
echo             -^> xlsDataExporter_-^>exportSensorGroupDetails()
echo             -^> addSensorGroupDetailToExcel()
echo             -^> 组名称显示逻辑
echo.
echo 2. 数据流:
echo    SensorDataManager -^> QList^<UI::SensorGroup^>
echo                      -^> 每个组包含多个传感器
echo                      -^> 组名称只在第一行显示
echo.
echo 3. 格式控制:
echo    if (i == 0) {
echo        worksheet-^>write(row, 2, group.groupName, currentFormat);
echo    } else {
echo        worksheet-^>write(row, 2, "", currentFormat);
echo    }
echo.

echo 🎉 修复完成！
echo.
echo 现在所有传感器导出路径都支持：
echo ✅ 组序号从1开始递增
echo ✅ 传感器组名称只在第一行显示
echo ✅ 同组其他行组名称列为空
echo ✅ 完整的33列传感器参数
echo ✅ 一致的格式和样式
echo.

pause
