/**
 * @file ProjectManager.cpp
 * @brief 项目管理模块实现 - 基于实际需求
 * @details 提供项目生命周期管理接口
 * <AUTHOR> Assistant
 * @date 2025-01-27
 * @version 3.4.0
 */

#include "ProjectManager.h"
#include "MainWindow_Qt_Simple.h"
#include <QFileInfo>
#include <QDir>
#include <QMessageBox>
#include <QDateTime>
#include <QDebug>
#include <QFileDialog>
#include <QStandardPaths>
#include <QWidget>

ProjectManager::ProjectManager(QObject* parent)
    : QObject(parent), hasUnsavedChanges_(false), mainWindow_(nullptr) {
}

ProjectManager::~ProjectManager() {
}

// v3.4架构：设置主窗口引用
void ProjectManager::setMainWindow(CMyMainWindow* mainWindow) {
    mainWindow_ = mainWindow;
}

// 项目生命周期管理
bool ProjectManager::createNewProject() {
    // 无参数版本：使用默认项目名称
    QString timestamp = QDateTime::currentDateTime().toString("yyyyMMddhhmmss");
    QString defaultProjectName = QString("%1_实验工程").arg(timestamp);
    return createNewProject(defaultProjectName);
}

bool ProjectManager::createNewProject(const QString& projectName) {
    if (!mainWindow_) {
        emit projectError("ProjectManager: 主窗口引用未设置");
        return false;
    }
    
    if (hasUnsavedChanges_) {
        if (!promptSaveIfNeeded()) {
            return false; // 用户取消了操作
        }
    }
    
    // 清空界面数据
    clearInterfaceData();
    
    // 选择保存文件路径（包含文件名） - 默认使用XLS格式
    QString defaultFileName = projectName + ".xls";
    QString defaultPath = QDir(QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation)).filePath(defaultFileName);

    QString projectFilePath = QFileDialog::getSaveFileName(
        qobject_cast<QWidget*>(mainWindow_),
        tr("选择实验工程保存位置"),
        defaultPath,
        tr("Excel文件 (*.xls);;Excel 2007+ (*.xlsx);;所有文件 (*.*)"));

    if (projectFilePath.isEmpty()) {
        // 用户取消操作，保持界面为空白状态
        emit projectError(tr("用户取消选择保存路径，界面保持空白状态"));
        return false;
    }

    // 创建新的工程对象
    if (mainWindow_->getCurrentProject()) {
        delete mainWindow_->getCurrentProject();
        mainWindow_->setCurrentProject(nullptr);
    }
    
    DataModels::TestProject* newProject = new DataModels::TestProject();
    newProject->projectName = projectName.toStdString();
    newProject->description = "灵动加载试验工程";
    newProject->createdDate = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss").toStdString();
    newProject->version = "1.0.0";
    newProject->projectPath = projectFilePath.toLocal8Bit().constData();
    mainWindow_->setCurrentProject(newProject);
    
    // 设置项目信息
    currentProjectName_ = projectName;
    currentProjectPath_ = projectFilePath;
    hasUnsavedChanges_ = true; // 新项目需要保存
    
    // 设置默认空界面
    setDefaultEmptyInterface();
    
    // 更新窗口标题
    updateWindowTitle();
    
    // 创建成功后立即保存文件
    bool saveSuccess = saveProjectToXLS(projectFilePath);
    
    if (saveSuccess) {
        emit projectMessage(tr("新建工程文件保存成功"));
        QMessageBox::information(qobject_cast<QWidget*>(mainWindow_), tr("创建成功"),
            QString("实验工程 '%1' 创建成功并已保存！\n保存路径: %2").arg(projectName).arg(projectFilePath));
    } else {
        emit projectError(tr("新建工程文件保存失败"));
        QMessageBox::warning(qobject_cast<QWidget*>(mainWindow_), tr("创建成功"),
            QString("实验工程 '%1' 创建成功，但保存文件时出现错误！\n保存路径: %2\n请手动保存工程。").arg(projectName).arg(projectFilePath));
    }
    
    emit projectOpened(currentProjectPath_, currentProjectName_);
    return true;
}

// 私有辅助方法实现
void ProjectManager::clearInterfaceData() {
    if (mainWindow_) {
        mainWindow_->ClearInterfaceData();
    }
}

void ProjectManager::setDefaultEmptyInterface() {
    if (mainWindow_) {
        mainWindow_->SetDefaultEmptyInterface();
    }
}

void ProjectManager::updateWindowTitle() {
    if (mainWindow_) {
        QString title = "SiteResConfig";
        if (!currentProjectName_.isEmpty()) {
            title += QString(" - %1").arg(currentProjectName_);
        }
        mainWindow_->setWindowTitle(title);
    }
}

bool ProjectManager::promptSaveIfNeeded() {
    if (mainWindow_) {
        return mainWindow_->PromptSaveIfNeeded();
    }
    return true;
}

bool ProjectManager::loadProjectFromXLS(const QString& filePath) {
    if (mainWindow_) {
        return mainWindow_->LoadProjectFromXLS(filePath);
    }
    return false;
}

bool ProjectManager::saveProjectToXLS(const QString& filePath) {
    if (mainWindow_) {
        return mainWindow_->SaveProjectToXLS(filePath);
    }
    return false;
}

bool ProjectManager::openProject(const QString& filePath) {
    if (!mainWindow_) {
        emit projectError("ProjectManager: 主窗口引用未设置");
        return false;
    }
    
    if (hasUnsavedChanges_) {
        if (!promptSaveIfNeeded()) {
            return false; // 用户取消了操作
        }
    }
    
    QFileInfo fileInfo(filePath);
    if (!fileInfo.exists()) {
        emit projectError(QString("项目文件不存在: %1").arg(filePath));
        return false;
    }
    
    // 检查文件格式
    QString extension = fileInfo.suffix().toLower();
    if (extension != "xlsx" && extension != "xls") {
        emit projectError("不支持的文件格式！请选择Excel格式的工程文件（.xlsx 或 .xls）。");
        return false;
    }
    
    // 加载项目
    if (!loadProjectFromXLS(filePath)) {
        emit projectError(QString("加载项目失败: %1").arg(filePath));
        return false;
    }
    
    currentProjectPath_ = filePath;
    currentProjectName_ = fileInfo.baseName();
    hasUnsavedChanges_ = false;
    
    // 更新窗口标题
    updateWindowTitle();
    
    emit projectOpened(currentProjectPath_, currentProjectName_);
    return true;
}

// v3.4架构：带对话框的打开项目
bool ProjectManager::openProjectWithDialog() {
    if (!mainWindow_) {
        emit projectError("ProjectManager: 主窗口引用未设置");
        return false;
    }
    
    QString fileName = QFileDialog::getOpenFileName(
        qobject_cast<QWidget*>(mainWindow_),
        tr("打开实验工程"),
        QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation),
        tr("Excel工程文件 (*.xls *.xlsx);;所有文件 (*.*)"));

    if (fileName.isEmpty()) {
        return false; // 用户取消
    }

    return openProject(fileName);
}

// v3.4架构：带对话框的保存项目
bool ProjectManager::saveProjectWithDialog() {
    if (!mainWindow_) {
        emit projectError("ProjectManager: 主窗口引用未设置");
        return false;
    }
    
    if (currentProjectPath_.isEmpty()) {
        // 如果没有保存路径，使用另存为
        return saveAsProjectWithDialog();
    }
    
    return saveProject();
}

// v3.4架构：带对话框的另存为项目
bool ProjectManager::saveAsProjectWithDialog() {
    if (!mainWindow_) {
        emit projectError("ProjectManager: 主窗口引用未设置");
        return false;
    }
    
    QString defaultFileName = currentProjectName_.isEmpty() ? "untitled.xls" : currentProjectName_ + ".xls";
    QString defaultPath = QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation);
    
    QString fileName = QFileDialog::getSaveFileName(
        qobject_cast<QWidget*>(mainWindow_),
        tr("另存为实验工程"),
        QDir(defaultPath).filePath(defaultFileName),
        tr("Excel文件 (*.xls);;Excel 2007+ (*.xlsx);;所有文件 (*.*)"));

    if (fileName.isEmpty()) {
        return false; // 用户取消
    }

    return saveAsProject(fileName);
}

bool ProjectManager::saveProject() {
    if (!mainWindow_) {
        emit projectError("ProjectManager: 主窗口引用未设置");
        return false;
    }
    
    if (currentProjectPath_.isEmpty()) {
        // 如果没有保存路径，调用另存为
        return saveAsProjectWithDialog();
    }
    
    QFileInfo fileInfo(currentProjectPath_);
    QString extension = fileInfo.suffix().toLower();
    
    bool success = false;
    if (extension == "json") {
        success = saveProjectToJSON(currentProjectPath_);
    } else {
        success = saveProjectToXLS(currentProjectPath_);
    }
    
    if (success) {
        hasUnsavedChanges_ = false;
        emit projectSaved();
        
        QMessageBox::information(nullptr, "保存成功",
            QString("实验工程已保存到:\n%1").arg(currentProjectPath_));
    } else {
        emit projectError(QString("保存项目失败: %1").arg(currentProjectPath_));
    }
    
    return success;
}

bool ProjectManager::saveAsProject(const QString& filePath) {
    if (!mainWindow_) {
        emit projectError("ProjectManager: 主窗口引用未设置");
        return false;
    }
    
    QFileInfo fileInfo(filePath);
    QString extension = fileInfo.suffix().toLower();
    
    bool success = false;
    if (extension == "json") {
        success = saveProjectToJSON(filePath);
    } else {
        success = saveProjectToXLS(filePath);
    }
    
    if (success) {
        currentProjectPath_ = filePath;
        currentProjectName_ = fileInfo.baseName();
        hasUnsavedChanges_ = false;
        
        // 更新窗口标题
        updateWindowTitle();
        
        emit projectSaved();
        
        if (extension != "json") { // JSON导出在函数内部已经显示了消息
            QMessageBox::information(nullptr, "保存成功",
                QString("实验工程已保存到:\n%1").arg(filePath));
        }
    } else {
        emit projectError(QString("保存项目失败: %1").arg(filePath));
    }
    
    return success;
}

void ProjectManager::closeProject() {
    if (hasActiveProject()) {
        currentProjectPath_.clear();
        currentProjectName_.clear();
        hasUnsavedChanges_ = false;
        
        // 清空界面数据
        clearInterfaceData();
        
        // 重置窗口标题
        updateWindowTitle();
        
        emit projectClosed();
    }
}

// 项目状态管理
bool ProjectManager::hasActiveProject() const {
    return !currentProjectName_.isEmpty();
}

QString ProjectManager::getCurrentProjectPath() const {
    return currentProjectPath_;
}

QString ProjectManager::getCurrentProjectName() const {
    return currentProjectName_;
}

bool ProjectManager::hasUnsavedChanges() const {
    return hasUnsavedChanges_;
}

void ProjectManager::markAsChanged() {
    hasUnsavedChanges_ = true;
}

bool ProjectManager::saveProjectToJSON(const QString& filePath) {
    if (mainWindow_) {
        return mainWindow_->SaveProjectToJSON(filePath);
    }
    return false;
} 