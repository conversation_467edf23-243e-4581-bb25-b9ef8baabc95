# 🔧 作动器组序号连续递增修改报告

## 📋 **修改目标**

将作动器详细配置的组序号显示逻辑修改为与传感器详细配置保持一致，使用简单的连续递增方式（1, 2, 3, 4...），而不是依赖复杂的组名称映射逻辑。

## 🔍 **修改前后对比**

### **修改前（复杂的组名称映射）**
```cpp
// 在数据创建时通过extractGroupIdFromName计算组ID
int groupId = extractGroupIdFromName(groupName);

// 导出时直接使用数据管理器中存储的组ID
worksheet->write(row, 1, group.groupId, currentFormat);
```

**问题**：
- 依赖复杂的组名称解析逻辑
- 可能出现组序号跳跃（如：1, 2, 50, 100）
- 需要维护预定义的组名称映射表

### **修改后（简单的连续递增）**
```cpp
// 在导出时通过数组索引+1生成连续的组序号
for (int groupIndex = 0; groupIndex < actuatorGroups.size(); ++groupIndex) {
    const UI::ActuatorGroup& group = actuatorGroups[groupIndex];
    int displayGroupId = groupIndex + 1; // 显示序号从1开始连续递增
    currentRow = addActuatorGroupDetailToExcel(worksheet, group, currentRow, displayGroupId);
}

// 在方法内部使用显示序号
int actualGroupId = (displayGroupId == -1) ? group.groupId : displayGroupId;
worksheet->write(row, 1, actualGroupId, currentFormat);
```

**优势**：
- 逻辑简单直接，易于理解
- 组序号总是连续递增（1, 2, 3, 4...）
- 与传感器详细配置保持一致
- 不依赖组名称解析

## 🔧 **具体修改内容**

### **1. 修改头文件** (`XLSDataExporter.h`)

#### **方法签名扩展**
```cpp
// 修改前
int addActuatorGroupDetailToExcel(QXlsx::Worksheet* worksheet, const UI::ActuatorGroup& group, int row);

// 修改后
int addActuatorGroupDetailToExcel(QXlsx::Worksheet* worksheet, const UI::ActuatorGroup& group, int row, int displayGroupId = -1);
```

### **2. 修改实现文件** (`XLSDataExporter.cpp`)

#### **方法实现修改**
```cpp
int XLSDataExporter::addActuatorGroupDetailToExcel(QXlsx::Worksheet* worksheet, const UI::ActuatorGroup& group, int row, int displayGroupId) {
    if (!worksheet) return row;

    // 如果没有提供显示组ID，使用原来的组ID
    int actualGroupId = (displayGroupId == -1) ? group.groupId : displayGroupId;

    // ... 其他代码保持不变 ...

    // 使用显示序号而不是原组ID
    worksheet->write(row, 1, actualGroupId, currentFormat);
}
```

#### **导出逻辑修改**
在两个地方修改了调用逻辑：

**A. exportCompleteProject方法**
```cpp
// 修改前
for (const UI::ActuatorGroup& group : actuatorGroups) {
    actuatorRow = addActuatorGroupDetailToExcel(actuatorWorksheet, group, actuatorRow);
}

// 修改后
for (int groupIndex = 0; groupIndex < actuatorGroups.size(); ++groupIndex) {
    const UI::ActuatorGroup& group = actuatorGroups[groupIndex];
    int displayGroupId = groupIndex + 1; // 显示序号从1开始连续递增
    actuatorRow = addActuatorGroupDetailToExcel(actuatorWorksheet, group, actuatorRow, displayGroupId);
}
```

**B. exportActuatorDetails方法**
```cpp
// 修改前
for (const UI::ActuatorGroup& group : actuatorGroups) {
    currentRow = addActuatorGroupDetailToExcel(worksheet, group, currentRow);
}

// 修改后
for (int groupIndex = 0; groupIndex < actuatorGroups.size(); ++groupIndex) {
    const UI::ActuatorGroup& group = actuatorGroups[groupIndex];
    int displayGroupId = groupIndex + 1; // 显示序号从1开始连续递增
    currentRow = addActuatorGroupDetailToExcel(worksheet, group, currentRow, displayGroupId);
}
```

## 📊 **修改效果**

### **修改前的Excel显示**
| 组序号 | 作动器组名称 | 作动器序列号 |
|--------|--------------|--------------|
| 1 | 50kN_作动器组 | 作动器_000001 |
| 1 |               | 作动器_000002 |
| 2 | 100kN_作动器组 | 作动器_000001 |
| 2 |                | 作动器_000002 |
| 3 | 是的方法 | 作动器_000122 |
| 3 |          | 作动器_000233 |

### **修改后的Excel显示**
| 组序号 | 作动器组名称 | 作动器序列号 |
|--------|--------------|--------------|
| 1 | 第一个组 | 作动器_000001 |
| 1 |          | 作动器_000002 |
| 2 | 第二个组 | 作动器_000001 |
| 2 |          | 作动器_000002 |
| 3 | 第三个组 | 作动器_000122 |
| 3 |          | 作动器_000233 |

**关键改进**：
- ✅ 组序号现在总是连续递增（1, 2, 3, 4...）
- ✅ 不再依赖组名称解析逻辑
- ✅ 与传感器详细配置保持一致
- ✅ 逻辑简单，易于维护

## ✅ **修改状态**

- ✅ 头文件修改完成
- ✅ 实现文件修改完成
- ✅ 导出逻辑修改完成
- ✅ 编译检查通过
- ✅ 无编译错误

## 📝 **总结**

通过这次修改，作动器详细配置的组序号显示逻辑现在与传感器详细配置完全一致：

1. **简化逻辑**：不再需要复杂的组名称映射
2. **连续递增**：组序号总是1, 2, 3, 4...连续
3. **一致性**：与传感器配置保持相同的实现方式
4. **可维护性**：代码更简单，更容易理解和维护

**修改文件**：
- `001_Code/001_SiteResConfig/SiteResConfig/include/XLSDataExporter.h`
- `001_Code/001_SiteResConfig/SiteResConfig/src/XLSDataExporter.cpp`

**修改状态**：✅ 完成并验证
