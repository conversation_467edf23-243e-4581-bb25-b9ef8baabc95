@echo off
echo ========================================
echo  SiteResConfig 数据制作和手动控制版本
echo ========================================

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    echo 请确保Qt 5.14.2 MinGW版本已正确安装
    pause
    exit /b 1
)

g++ --version
if errorlevel 1 (
    echo 错误: MinGW编译器未找到！
    pause
    exit /b 1
)

REM 进入项目目录
cd /d "%~dp0\SiteResConfig"

echo.
echo 清理构建文件...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o

echo.
echo 生成Makefile...
qmake SiteResConfig_Simple.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译项目...
mingw32-make clean
mingw32-make -j4
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    echo 请检查上面的错误信息。
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  编译成功！数据制作和手动控制版本已就绪
    echo ========================================
    
    if exist "SiteResConfig.exe" (
        echo 可执行文件: SiteResConfig.exe
        echo.
        echo 🎯 核心功能:
        echo ✅ 数据制作 - 6种数据类型生成
        echo ✅ 手动控制 - 设备手动操作
        echo ✅ 数据模板 - 模板保存和管理
        echo ✅ 配置管理 - 多格式配置文件支持
        echo.
        echo 🎮 使用流程:
        echo 1. 加载配置 - 导入配置文件或手动配置
        echo 2. 制作数据 - 选择数据类型和参数生成数据
        echo 3. 手动控制 - 连接设备进行手动操作
        echo 4. 导出数据 - 保存生成的数据为CSV文件
        echo.
        echo 启动程序...
        start SiteResConfig.exe
    ) else if exist "debug\SiteResConfig.exe" (
        echo 可执行文件: debug\SiteResConfig.exe
        echo 启动程序...
        start debug\SiteResConfig.exe
    ) else if exist "release\SiteResConfig.exe" (
        echo 可执行文件: release\SiteResConfig.exe
        echo 启动程序...
        start release\SiteResConfig.exe
    ) else (
        echo 警告: 找不到可执行文件！
    )
)

echo.
echo 📖 使用说明:
echo.
echo 🔧 数据制作功能:
echo - 点击"制作数据"按钮
echo - 设置数据点数、时间间隔、数据类型
echo - 支持正弦波、方波、三角波、随机数据等
echo - 生成多通道数据：位移、载荷、应变、压力、温度
echo.
echo 🎮 手动控制功能:
echo - 切换到"手动控制"标签页
echo - 连接设备后可进行位置、力值、速度控制
echo - 实时监控设备状态和参数
echo - 支持紧急停止和设备复位
echo.
echo 📁 数据管理:
echo - 支持数据模板保存和加载
echo - CSV格式数据导出
echo - 配置文件管理（JSON/XML/CSV格式）
echo.
pause
