# 传感器新需求软件流程修改设计方案

## 📋 需求概述

### 新增传感器参数结构
```json
{
    "zero_offset": 0.0,
    "enable": true,
    "params": {
        "model": "AKD-8A",
        "sn": "2223",
        "k": 20.0,
        "b": 0.0,
        "precision": 0.1,
        "polarity": -1,
        "meas_unit": 1,
        "meas_range_min": -100.0,
        "meas_range_max": 100.0,
        "output_signal_unit": 1,
        "output_signal_range_min": -100.0,
        "output_signal_range_max": 100.0
    }
}
```

### 完整工作流程
1. **打开工程（导入Excel）** - 从Excel文件导入传感器配置
2. **新建工程（新建Excel）** - 创建新的传感器配置模板
3. **保存工程（导出Excel）** - 将当前配置保存为Excel文件
4. **导出工程（导出JSON）** - 将配置导出为标准JSON格式

## 🏗️ 架构设计

### 1. 数据结构扩展 ✅ 已完成

#### SensorParams_1_2结构体扩展
已在 `SensorDialog_1_2.h` 中完成扩展，包含所有新需求字段：

```cpp
struct SensorParams_1_2 {
    // 基本信息
    int sensorId;
    QString serialNumber;
    QString sensorType;
    
    // 🆕 核心控制参数
    double zero_offset;       // 零点偏移
    bool enable;              // 启用状态
    
    // 🆕 物理参数 (params)
    QString params_model;     // 参数模型
    QString params_sn;        // 参数序列号
    double params_k;          // 线性系数k
    double params_b;          // 线性系数b
    double params_precision;  // 精度
    int params_polarity;      // 极性
    
    // 🆕 测量范围参数
    int meas_unit;            // 测量单位类型
    double meas_range_min;    // 测量范围最小值
    double meas_range_max;    // 测量范围最大值
    
    // 🆕 输出信号范围参数
    int output_signal_unit;          // 输出信号单位类型
    double output_signal_range_min;  // 输出信号范围最小值
    double output_signal_range_max;  // 输出信号范围最大值
};
```

### 2. Excel功能架构

#### 优化后的Excel结构设计
```
📊 传感器配置.xlsx
└── 传感器详细配置        (Sheet1: 包含组信息和完整传感器参数)
```

#### 传感器详细配置工作表字段设计

**传感器详细配置工作表（完整结构）：**
| 列号 | 字段名 | 说明 | 分类 |
|------|--------|------|------|
| A | 组序号 | 传感器组的显示序号 | 🏷️ 组信息 |
| B | 传感器组名称 | 传感器组名称 | 🏷️ 组信息 |
| C | 传感器ID | 传感器内部ID | 🆔 基本信息 |
| D | 传感器序列号 | 传感器序列号 | 🆔 基本信息 |
| E | 传感器类型 | 传感器类型 | 🆔 基本信息 |
| F | 零点偏移 | zero_offset | ⚙️ 控制参数 |
| G | 启用状态 | enable (是/否) | ⚙️ 控制参数 |
| H | 参数模型 | params.model | 🔧 物理参数 |
| I | 参数序列号 | params.sn | 🔧 物理参数 |
| J | 线性系数K | params.k | 🔧 物理参数 |
| K | 线性系数B | params.b | 🔧 物理参数 |
| L | 精度 | params.precision | 🔧 物理参数 |
| M | 极性 | params.polarity | 🔧 物理参数 |
| N | 测量单位类型 | meas_unit | 📏 测量范围 |
| O | 测量范围最小值 | meas_range_min | 📏 测量范围 |
| P | 测量范围最大值 | meas_range_max | 📏 测量范围 |
| Q | 输出信号单位类型 | output_signal_unit | 📶 输出信号 |
| R | 输出信号范围最小值 | output_signal_range_min | 📶 输出信号 |
| S | 输出信号范围最大值 | output_signal_range_max | 📶 输出信号 |

#### Excel表头设计
```
行1: 传感器详细配置
行2: [表头行]
组序号 | 传感器组名称 | 传感器ID | 传感器序列号 | 传感器类型 | 零点偏移 | 启用状态 | 参数模型 | 参数序列号 | 线性系数K | 线性系数B | 精度 | 极性 | 测量单位类型 | 测量范围最小值 | 测量范围最大值 | 输出信号单位类型 | 输出信号范围最小值 | 输出信号范围最大值
行3+: [数据行]
```

#### 数据组织方式
```
组序号 | 传感器组名称    | 传感器数据...
   1   | 压力传感器组1   | [传感器1数据]
   1   | 压力传感器组1   | [传感器2数据] 
   2   | 温度传感器组1   | [传感器3数据]
   2   | 温度传感器组1   | [传感器4数据]
```

## 📝 实现计划

### 阶段1：Excel扩展功能实现 ✅ 已完成

#### 1.1 创建SensorExcelExtensions_1_2.cpp ✅ 已完成
实现的核心方法：
- ✅ `exportEnhancedSensorDetails()` - 导出包含组信息的传感器详细配置
- ✅ `importEnhancedSensorDetails()` - 导入包含组信息的传感器详细配置
- ✅ `createSensorDetailWorksheet()` - 创建传感器详细配置工作表
- ✅ `readSensorDetailWorksheet()` - 读取传感器详细配置工作表
- ✅ `createEmptyTemplate()` - 创建空的传感器配置模板

**Excel结构特点：**
- 单一工作表："传感器详细配置"
- 19列完整数据结构
- 包含组序号和传感器组名称
- 支持多组传感器的层次结构显示
- 完整的19个参数字段覆盖

#### 1.2 更新现有Excel导出器
修改 `XLSDataExporter_1_2.cpp` 中的传感器导出部分，调用扩展功能：

```cpp
bool XLSDataExporter_1_2::exportCompleteProject(const QString& filePath) {
    // ... 现有代码 ...
    
    // 使用扩展功能导出传感器
    if (sensorDataManager_) {
        QList<UI::SensorGroup_1_2> sensorGroups = sensorDataManager_->getAllSensorGroups();
        if (!sensorGroups.isEmpty()) {
            // 调用扩展导出功能
            SensorExcelExtensions_1_2::exportEnhancedSensorDetails(sensorGroups, filePath);
        }
    }
}
```

### 阶段2：JSON导出功能升级 ✅ 已完成

#### 2.1 更新JSONDataExporter_1_2 ✅ 已完成
修改JSON导出器以支持新的传感器参数结构：

**核心修改点：**
- ✅ 更新 `createSensorGroupsJson()` 方法
- ✅ 支持 `zero_offset` 和 `enable` 字段
- ✅ 创建嵌套的 `params` 对象结构
- ✅ 包含所有新增的传感器参数字段

**JSON结构特点：**
```json
{
  "sensorId": 1,
  "serialNumber": "SN001",
  "sensorType": "AKD-8A",
  "zero_offset": 0.0,
  "enable": true,
  "params": {
    "model": "AKD-8A",
    "sn": "2223",
    "k": 20.0,
    "b": 0.0,
    "precision": 0.1,
    "polarity": -1,
    "meas_unit": 1,
    "meas_range_min": -100.0,
    "meas_range_max": 100.0,
    "output_signal_unit": 1,
    "output_signal_range_min": -100.0,
    "output_signal_range_max": 100.0
  }
}
```

### 阶段3：主界面工作流程集成

#### 3.1 新建工程流程
```cpp
void CMyMainWindow::OnNewSensorProject() {
    // 1. 清空当前传感器数据
    if (sensorDataManager_) {
        sensorDataManager_->clearAll();
    }
    
    // 2. 创建新的Excel模板
    QString templatePath = QFileDialog::getSaveFileName(
        this, u8"新建传感器工程", 
        QString(u8"传感器配置_%1.xlsx").arg(QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss")),
        u8"Excel文件 (*.xlsx)");
    
    if (!templatePath.isEmpty()) {
        // 创建包含表头的空Excel文件
        SensorExcelExtensions_1_2::createEmptyTemplate(templatePath);
        AddLogEntry("INFO", QString(u8"新建传感器工程: %1").arg(templatePath));
    }
}
```

#### 3.2 打开工程流程
```cpp
void CMyMainWindow::OnOpenSensorProject() {
    QString filePath = QFileDialog::getOpenFileName(
        this, u8"打开传感器工程", QString(), 
        u8"Excel文件 (*.xlsx);;所有文件 (*.*)");
    
    if (!filePath.isEmpty()) {
        // 导入Excel文件
        QList<UI::SensorGroup_1_2> importedGroups = 
            SensorExcelExtensions_1_2::importEnhancedSensorDetails(filePath);
        
        // 清空现有数据并加载新数据
        if (sensorDataManager_) {
            sensorDataManager_->clearAll();
            for (const auto& group : importedGroups) {
                sensorDataManager_->saveSensorGroup(group);
            }
        }
        
        // 刷新界面显示
        refreshSensorDisplay();
        AddLogEntry("INFO", QString(u8"导入传感器工程: %1, 共%2个组")
                   .arg(filePath).arg(importedGroups.size()));
    }
}
```

#### 3.3 保存工程流程
```cpp
void CMyMainWindow::OnSaveSensorProject() {
    if (!sensorDataManager_) {
        QMessageBox::warning(this, u8"警告", u8"传感器数据管理器未初始化");
        return;
    }
    
    QList<UI::SensorGroup_1_2> allGroups = sensorDataManager_->getAllSensorGroups();
    if (allGroups.isEmpty()) {
        QMessageBox::information(this, u8"提示", u8"当前没有传感器数据可保存");
        return;
    }
    
    QString filePath = QFileDialog::getSaveFileName(
        this, u8"保存传感器工程", 
        QString(u8"传感器配置_%1.xlsx").arg(QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss")),
        u8"Excel文件 (*.xlsx)");
    
    if (!filePath.isEmpty()) {
        bool success = SensorExcelExtensions_1_2::exportEnhancedSensorDetails(allGroups, filePath);
        if (success) {
            AddLogEntry("INFO", QString(u8"保存传感器工程成功: %1").arg(filePath));
            QMessageBox::information(this, u8"成功", u8"传感器工程保存成功！");
        } else {
            QString error = SensorExcelExtensions_1_2::getLastError();
            AddLogEntry("ERROR", QString(u8"保存传感器工程失败: %1").arg(error));
            QMessageBox::critical(this, u8"错误", QString(u8"保存失败：%1").arg(error));
        }
    }
}
```

#### 3.4 导出JSON流程
```cpp
void CMyMainWindow::OnExportSensorToJSON() {
    if (!sensorDataManager_) {
        QMessageBox::warning(this, u8"警告", u8"传感器数据管理器未初始化");
        return;
    }
    
    QList<UI::SensorGroup_1_2> allGroups = sensorDataManager_->getAllSensorGroups();
    if (allGroups.isEmpty()) {
        QMessageBox::information(this, u8"提示", u8"当前没有传感器数据可导出");
        return;
    }
    
    QString filePath = QFileDialog::getSaveFileName(
        this, u8"导出传感器JSON", 
        QString(u8"传感器配置_%1.json").arg(QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss")),
        u8"JSON文件 (*.json)");
    
    if (!filePath.isEmpty()) {
        // 确保JSON导出器已初始化
        if (!jsonDataExporter_) {
            initializeJSONExporter();
        }
        
        bool success = jsonDataExporter_->exportCompleteProject(filePath);
        if (success) {
            AddLogEntry("INFO", QString(u8"导出传感器JSON成功: %1").arg(filePath));
            QMessageBox::information(this, u8"成功", u8"传感器配置导出JSON成功！");
        } else {
            QString error = jsonDataExporter_->getLastError();
            AddLogEntry("ERROR", QString(u8"导出传感器JSON失败: %1").arg(error));
            QMessageBox::critical(this, u8"错误", QString(u8"导出失败：%1").arg(error));
        }
    }
}
```

## 🎛️ 用户界面集成

### 菜单结构扩展
```
主菜单栏 → 传感器(S) → [传感器工程管理]
├── 新建传感器工程(N)     - Ctrl+Shift+N
├── 打开传感器工程(O)     - Ctrl+Shift+O  
├── 保存传感器工程(S)     - Ctrl+Shift+S
├── ──────────────────
├── 导出为JSON(J)        - Ctrl+Shift+J
├── 导出为Excel(E)       - Ctrl+Shift+E
└── 批量导出所有格式(A)   - Ctrl+Shift+A
```

### 工具栏快捷按钮
- 🆕 新建传感器工程
- 📁 打开传感器工程
- 💾 保存传感器工程
- 📤 导出JSON配置

## 🔧 技术实现细节

### 1. 错误处理机制
```cpp
class SensorWorkflowManager {
private:
    static QString lastError_;
    
public:
    static void setError(const QString& error) {
        lastError_ = error;
        qDebug() << "SensorWorkflow Error:" << error;
    }
    
    static void clearError() {
        lastError_.clear();
    }
    
    static QString getLastError() {
        return lastError_;
    }
};
```

### 2. 数据验证机制
```cpp
bool validateSensorData(const UI::SensorParams_1_2& sensor) {
    // 必填字段检查
    if (sensor.serialNumber.isEmpty()) {
        setError(u8"传感器序列号不能为空");
        return false;
    }
    
    // 数值范围检查
    if (sensor.params_precision <= 0.0) {
        setError(u8"传感器精度必须大于0");
        return false;
    }
    
    // 极性值检查
    if (sensor.params_polarity != -1 && sensor.params_polarity != 1) {
        setError(u8"传感器极性必须为-1或1");
        return false;
    }
    
    return true;
}
```

### 3. 进度提示机制
```cpp
void showProgress(const QString& operation, int current, int total) {
    if (progressDialog_) {
        progressDialog_->setLabelText(QString(u8"%1 (%2/%3)").arg(operation).arg(current).arg(total));
        progressDialog_->setValue((current * 100) / total);
        QApplication::processEvents();
    }
}
```

## 📋 测试验证计划

### 1. 单元测试
- Excel导入导出功能测试
- JSON导出功能测试
- 数据结构转换测试
- 错误处理测试

### 2. 集成测试
- 完整工作流程测试
- 多工作表Excel兼容性测试
- 大数据量性能测试
- 异常情况恢复测试

### 3. 用户接受测试
- 界面操作流畅性测试
- 文件格式兼容性测试
- 数据完整性验证测试

## 🚀 实施时间表

### Week 1: 核心功能实现
- Day 1-2: 完成SensorExcelExtensions_1_2.cpp实现
- Day 3-4: 更新JSON导出功能
- Day 5: 单元测试和调试

### Week 2: 界面集成和完善
- Day 1-2: 主界面工作流程集成
- Day 3-4: 菜单和工具栏添加
- Day 5: 集成测试和优化

### Week 3: 测试和发布
- Day 1-3: 全面测试和bug修复
- Day 4-5: 文档完善和发布准备

## 📚 相关文件清单

### 需要修改的文件
1. `SensorExcelExtensions_1_2.cpp` - 🆕 待创建
2. `XLSDataExporter_1_2.cpp` - 集成扩展功能调用
3. `JSONDataExporter_1_2.cpp` - 更新JSON格式支持
4. `MainWindow_Qt_Simple.h/.cpp` - 添加工作流程方法
5. `MainWindow.ui` - 添加菜单和工具栏项目

### 新增的依赖
- QXlsx库的多工作表操作
- JSON序列化的嵌套对象支持
- 文件操作的进度提示支持

## 🎯 预期成果

### 功能完整性
- ✅ 支持新传感器参数结构的完整工作流程
- ✅ Excel多工作表导入导出
- ✅ 标准JSON格式导出
- ✅ 完善的错误处理和用户提示

### 用户体验
- ✅ 直观的菜单导航
- ✅ 快捷键支持
- ✅ 进度提示和状态反馈
- ✅ 数据验证和错误提示

### 技术架构
- ✅ 模块化设计，易于维护
- ✅ 数据结构版本兼容性
- ✅ 性能优化和错误恢复
- ✅ 完整的测试覆盖

这个设计方案提供了传感器新需求的完整实现路径，确保了功能的完整性和用户体验的优化。