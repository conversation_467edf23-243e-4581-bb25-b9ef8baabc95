/**
 * @file HardwareConfigDialog.cpp
 * @brief 硬件配置对话框类实现
 * @details 使用Qt Designer设计的硬件配置参数输入对话框实现
 * <AUTHOR> Assistant
 * @date 2025-08-06
 * @version 1.0.0
 */

#include "HardwareConfigDialog.h"
#include "ui_HardwareConfigDialog.h"
#include <QtWidgets/QMessageBox>

namespace UI {

HardwareConfigDialog::HardwareConfigDialog(QWidget* parent)
    : QDialog(parent)
    , ui(new Ui::HardwareConfigDialog) {
    
    ui->setupUi(this);
    initializeUI();
    connectSignals();
}

HardwareConfigDialog::~HardwareConfigDialog() {
    delete ui;
}

void HardwareConfigDialog::initializeUI() {
    // 设置窗口大小
    resize(500, 400);
    
    // 设置默认值
    ui->nodeCountSpinBox->setValue(2);
    ui->channelCountSpinBox->setValue(4);
    ui->actuatorCountSpinBox->setValue(4);
    ui->maxForceSpinBox->setValue(100.0);
    ui->sensorCountSpinBox->setValue(8);
}

void HardwareConfigDialog::connectSignals() {
    // 重新连接确定按钮，添加验证
    disconnect(ui->okButton, &QPushButton::clicked, this, &QDialog::accept);
    connect(ui->okButton, &QPushButton::clicked, this, &HardwareConfigDialog::onAcceptClicked);
}

void HardwareConfigDialog::onAcceptClicked() {
    // 直接接受，不进行验证（根据项目要求）
    accept();
}

HardwareConfigParams HardwareConfigDialog::getHardwareConfigParams() const {
    HardwareConfigParams params;
    
    params.nodeCount = ui->nodeCountSpinBox->value();
    params.channelCount = ui->channelCountSpinBox->value();
    params.actuatorCount = ui->actuatorCountSpinBox->value();
    params.maxForce = ui->maxForceSpinBox->value();
    params.sensorCount = ui->sensorCountSpinBox->value();
    
    return params;
}

} // namespace UI
