@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🔧 DEBUG数据诊断测试
echo ========================================
echo.

echo 📋 修复内容:
echo.
echo ✅ 添加了数据诊断信息:
echo    ├─ 显示节点名称和类型
echo    ├─ 显示查找的序列号
echo    ├─ 显示数据管理器中的实际数据
echo    └─ 帮助识别数据匹配问题
echo.

echo 🎯 诊断信息格式:
echo.
echo 1. 节点识别:
echo    🔍 节点: ACT001, 类型: 作动器设备
echo.
echo 2. 数据未找到时:
echo    ❌ 作动器数据未找到: ACT001
echo    📋 数据管理器中的作动器:
echo      [1] ACTUATOR_001
echo      [2] ACTUATOR_002
echo      [3] ACTUATOR_003
echo.
echo 3. 数据管理器为空时:
echo    ❌ 作动器数据未找到: ACT001
echo    📋 数据管理器为空
echo.

echo 🔧 可能的问题原因:
echo.
echo 1. 节点名称与序列号不匹配
echo    - 节点显示名: ACT001
echo    - 实际序列号: ACTUATOR_001
echo.
echo 2. 数据管理器未初始化
echo    - 显示: ActuatorDataManager未初始化
echo.
echo 3. 数据管理器为空
echo    - 显示: 数据管理器为空
echo.
echo 4. 节点类型识别错误
echo    - UserRole未正确设置
echo    - 父节点关系错误
echo.

echo 💡 测试步骤:
echo.
echo 1. 编译Debug版本
echo 2. 启动程序
echo 3. 创建作动器组和设备
echo 4. 鼠标悬停查看DEBUG信息
echo 5. 检查节点名称和数据管理器内容
echo.

echo 🔄 编译测试:
echo.

if exist "SiteResConfig\SiteResConfig_Simple.pro" (
    echo 找到项目文件，开始编译...
    echo.
    
    cd SiteResConfig
    
    echo 清理旧文件...
    if exist "Makefile" del /Q "Makefile" 2>nul
    if exist "debug" rmdir /S /Q "debug" 2>nul
    if exist "release" rmdir /S /Q "release" 2>nul
    
    echo.
    echo 生成Makefile...
    qmake SiteResConfig_Simple.pro -spec win32-g++ "CONFIG+=debug"
    
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo 开始编译...
        mingw32-make debug
        
        if %ERRORLEVEL% EQU 0 (
            echo.
            echo ✅ 编译成功！
            echo.
            if exist "debug\SiteResConfig.exe" (
                echo 🚀 启动程序进行DEBUG测试...
                echo.
                start "" "debug\SiteResConfig.exe"
            ) else (
                echo ❌ 可执行文件未找到
            )
        ) else (
            echo ❌ 编译失败
        )
    ) else (
        echo ❌ qmake失败
    )
    
    cd ..
) else (
    echo ❌ 项目文件未找到
    echo 请确保在正确的目录中运行此脚本
)

echo.
echo ========================================
echo 🔧 DEBUG数据诊断功能已添加
echo ========================================
echo.
echo 现在可以通过DEBUG信息诊断数据匹配问题！
echo.
pause
