@echo off
echo Building SiteResConfig with color restore fix...

REM Set Qt environment
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

REM Change to project directory
cd /d "%~dp0\SiteResConfig"

echo Cleaning build files...
if exist "Makefile*" del Makefile*
if exist "debug" rmdir /s /q debug
if exist "release" rmdir /s /q release
if exist "*.o" del *.o

echo Generating UI headers...
uic ui\MainWindow.ui -o ui_MainWindow.h

echo Running qmake...
qmake SiteResConfig_Simple.pro

echo Building project...
mingw32-make

if exist "SiteResConfig.exe" (
    echo Build successful! Executable: SiteResConfig.exe
) else (
    echo Build failed!
)

pause
