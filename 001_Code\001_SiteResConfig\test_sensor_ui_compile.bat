@echo off
echo ========================================
echo  传感器UI测试程序编译
echo ========================================

REM 设置Qt环境变量（MinGW版本）
set QTDIR=C:\Qt\5.14.2\mingw73_32
set PATH=%QTDIR%\bin;C:\Qt\Tools\mingw730_32\bin;%PATH%

echo 检查编译环境...
qmake --version
if errorlevel 1 (
    echo 错误: Qt环境未找到！
    echo 请确保Qt 5.14.2 MinGW版本已安装
    pause
    exit /b 1
)

echo.
echo 清理之前的构建文件...
if exist "test_sensor_ui.exe" del test_sensor_ui.exe
if exist "Makefile" del Makefile
if exist "*.o" del *.o

echo.
echo 生成Makefile...
qmake test_sensor_ui.pro -spec win32-g++
if errorlevel 1 (
    echo qmake失败！
    pause
    exit /b 1
)

echo.
echo 编译测试程序...
mingw32-make clean
mingw32-make
if errorlevel 1 (
    echo.
    echo ========================================
    echo  编译失败！
    echo ========================================
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo  编译成功！
    echo ========================================
    
    if exist "test_sensor_ui.exe" (
        echo.
        echo ✅ 传感器UI测试程序编译成功！
        echo ✅ 新的Sensor组合框已集成
        echo ✅ 界面布局已优化
        echo.
        echo 启动测试程序...
        start test_sensor_ui.exe
    ) else (
        echo 警告: 找不到可执行文件！
    )
)

echo.
echo 📖 测试说明:
echo.
echo 🎯 测试重点:
echo - 检查Sensor组合框是否正确显示
echo - 验证组合框样式是否应用
echo - 测试选择不同传感器时的联动效果
echo - 确认界面布局是否清晰分离
echo.
echo 🔧 如果测试成功，说明UI修改正确
echo 🔧 可以将相同的修改应用到主项目中
echo.
pause
