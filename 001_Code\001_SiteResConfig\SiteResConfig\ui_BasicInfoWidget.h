/********************************************************************************
** Form generated from reading UI file 'BasicInfoWidget.ui'
**
** Created by: Qt User Interface Compiler version 5.14.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_BASICINFOWIDGET_H
#define UI_BASICINFOWIDGET_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_BasicInfoWidget
{
public:
    QVBoxLayout *verticalLayout;
    QHBoxLayout *horizontalLayout;
    QLabel *lblSummaryInfo;
    QGroupBox *basicInfoGroup;
    QVBoxLayout *basicInfoLayout;
    QTableWidget *basicInfoTable;

    void setupUi(QWidget *BasicInfoWidget)
    {
        if (BasicInfoWidget->objectName().isEmpty())
            BasicInfoWidget->setObjectName(QString::fromUtf8("BasicInfoWidget"));
        BasicInfoWidget->resize(800, 600);
        verticalLayout = new QVBoxLayout(BasicInfoWidget);
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        horizontalLayout = new QHBoxLayout();
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        horizontalLayout->setContentsMargins(-1, 0, -1, -1);
        lblSummaryInfo = new QLabel(BasicInfoWidget);
        lblSummaryInfo->setObjectName(QString::fromUtf8("lblSummaryInfo"));

        horizontalLayout->addWidget(lblSummaryInfo);


        verticalLayout->addLayout(horizontalLayout);

        basicInfoGroup = new QGroupBox(BasicInfoWidget);
        basicInfoGroup->setObjectName(QString::fromUtf8("basicInfoGroup"));
        basicInfoLayout = new QVBoxLayout(basicInfoGroup);
        basicInfoLayout->setObjectName(QString::fromUtf8("basicInfoLayout"));
        basicInfoTable = new QTableWidget(basicInfoGroup);
        basicInfoTable->setObjectName(QString::fromUtf8("basicInfoTable"));
        basicInfoTable->setEditTriggers(QAbstractItemView::NoEditTriggers);
        basicInfoTable->setAlternatingRowColors(true);
        basicInfoTable->setSelectionBehavior(QAbstractItemView::SelectRows);
        basicInfoTable->setShowGrid(true);
        basicInfoTable->setGridStyle(Qt::SolidLine);

        basicInfoLayout->addWidget(basicInfoTable);


        verticalLayout->addWidget(basicInfoGroup);


        retranslateUi(BasicInfoWidget);

        QMetaObject::connectSlotsByName(BasicInfoWidget);
    } // setupUi

    void retranslateUi(QWidget *BasicInfoWidget)
    {
        BasicInfoWidget->setWindowTitle(QCoreApplication::translate("BasicInfoWidget", "\345\237\272\346\234\254\344\277\241\346\201\257\346\216\247\344\273\266", nullptr));
        lblSummaryInfo->setText(QCoreApplication::translate("BasicInfoWidget", "\345\255\220\351\200\232\351\201\223\346\225\260\351\207\217: 0\344\270\252", nullptr));
        basicInfoGroup->setTitle(QCoreApplication::translate("BasicInfoWidget", "\345\255\220\351\200\232\351\201\223\350\257\246\347\273\206\344\277\241\346\201\257", nullptr));
    } // retranslateUi

};

namespace Ui {
    class BasicInfoWidget: public Ui_BasicInfoWidget {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_BASICINFOWIDGET_H
