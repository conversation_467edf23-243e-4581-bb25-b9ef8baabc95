#include <QApplication>
#include <QWidget>
#include "include/BasicInfoWidget.h"
#include "include/DetailInfoPanel.h"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // 测试创建BasicInfoWidget
    QWidget parent;
    BasicInfoWidget* basicWidget = new BasicInfoWidget(&parent);
    
    // 测试创建DetailInfoPanel
    DetailInfoPanel* detailPanel = new DetailInfoPanel(&parent);
    
    qDebug() << "编译测试成功！";
    
    return 0;
} 